[{"pkg": "@capacitor-community/barcode-scanner", "classpath": "com.getcapacitor.community.barcodescanner.BarcodeScanner"}, {"pkg": "@capacitor-community/file-opener", "classpath": "com.ryltsov.alex.plugins.file.opener.FileOpenerPlugin"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/browser", "classpath": "com.capacitorjs.plugins.browser.BrowserPlugin"}, {"pkg": "@capacitor/camera", "classpath": "com.capacitorjs.plugins.camera.CameraPlugin"}, {"pkg": "@capacitor/device", "classpath": "com.capacitorjs.plugins.device.DevicePlugin"}, {"pkg": "@capacitor/filesystem", "classpath": "com.capacitorjs.plugins.filesystem.FilesystemPlugin"}, {"pkg": "@capacitor/geolocation", "classpath": "com.capacitorjs.plugins.geolocation.GeolocationPlugin"}, {"pkg": "@capacitor/local-notifications", "classpath": "com.capacitorjs.plugins.localnotifications.LocalNotificationsPlugin"}, {"pkg": "@capacitor/preferences", "classpath": "com.capacitorjs.plugins.preferences.PreferencesPlugin"}, {"pkg": "@capacitor/splash-screen", "classpath": "com.capacitorjs.plugins.splashscreen.SplashScreenPlugin"}]