System.register(["./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-645a3645.js"],(function(e,l){"use strict";var a,o,n,r,d,t,u,m,i,f;return{setters:[e=>{a=e._},e=>{o=e.Q,n=e.R,r=e.X,d=e.V,t=e.k,u=e.S,m=e.F,i=e.W},e=>{f=e.U}],execute:function(){const l={name:"DesignDispatchCommonTopNew",props:{formData:{type:Object,default:()=>({})},type:{},showContentDescription:{type:Boolean,default:!0},readonly:{type:Boolean,default:!0}}};e("D",a(l,[["render",function(e,l,a,u,m,i){const f=o("van-field"),p=o("van-cell-group");return n(),r(p,{border:!1},{default:d((()=>[t(f,{modelValue:a.formData.fileName,"onUpdate:modelValue":l[0]||(l[0]=e=>a.formData.fileName=e),label:"图册名称",readonly:a.readonly,required:""},null,8,["modelValue","readonly"]),t(f,{modelValue:a.formData.relavancePartName,"onUpdate:modelValue":l[1]||(l[1]=e=>a.formData.relavancePartName=e),label:"工程部位",readonly:a.readonly,required:""},null,8,["modelValue","readonly"]),t(f,{modelValue:a.formData.profession,"onUpdate:modelValue":l[2]||(l[2]=e=>a.formData.profession=e),label:"专业",readonly:a.readonly,required:""},null,8,["modelValue","readonly"]),t(f,{modelValue:a.formData.leadDesignerFullname,"onUpdate:modelValue":l[3]||(l[3]=e=>a.formData.leadDesignerFullname=e),label:"主设人",readonly:a.readonly,required:""},null,8,["modelValue","readonly"]),t(f,{modelValue:a.formData.subProjectName,"onUpdate:modelValue":l[4]||(l[4]=e=>a.formData.subProjectName=e),label:"子工程",readonly:a.readonly,required:""},null,8,["modelValue","readonly"])])),_:1})}]])),e("a",a({name:"DesignDispatchDrawingTable",components:{UploadFiles:f},props:{formDetailTable:{type:Array,default:()=>[]},formData:{type:Object,default:()=>({})},type:{}}},[["render",function(e,l,a,f,p,s){const y=o("van-field"),c=o("UploadFiles"),V=o("van-cell-group");return n(),r(V,{border:!1},{default:d((()=>[(n(!0),u(m,null,i(a.formDetailTable,(e=>(n(),u(m,null,[t(y,{modelValue:e.fileName,"onUpdate:modelValue":l=>e.fileName=l,label:"图纸名称",readonly:""},null,8,["modelValue","onUpdate:modelValue"]),t(y,{modelValue:e.fileCode,"onUpdate:modelValue":l=>e.fileCode=l,label:"图纸编号",readonly:""},null,8,["modelValue","onUpdate:modelValue"]),t(y,{label:"图纸附件","label-align":"top","input-align":"left"},{input:d((()=>[t(c,{ref_for:!0,ref:"afterFiles",g9s:e.attachment,"onUpdate:g9s":l=>e.attachment=l,readonly:!0},null,8,["g9s","onUpdate:g9s"])])),_:2},1024)],64)))),256))])),_:1})}]]))}}}));
