import{F as P}from"./FormItemPicker-d3f69283.js";import{_ as M}from"./FormItemSection-e3118d02.js";import{F as z}from"./FormItemDate-ba00d9d5.js";import{a as Q,b as Y,c as j,e as W}from"./api-58f40a5a.js";import{_ as A,D as C}from"./index-4829f8e2.js";import{a as B,g as x}from"./wbsUtil-3e809cfd.js";import{R}from"./constants-4e307505.js";import{Q as c,R as o,S as u,k as l,V as m,X as f,a2 as I,Z as v,U as r,Y as p,B as _,F as D,y as H,W as N}from"./verder-361ae6c7.js";import{F as G}from"./FormItemCalendar-905fde75.js";import{a as L}from"./vant-91101745.js";import"./array-15ef8611.js";function J(e){const t=e,i=[];return e.forEach(y=>{y.children&&y.children.forEach(a=>{i.push({label:a.content?a.content.name:"",value:a.content?a.content.id:""})})}),{departmentList:t,departmentOptions:i}}const K={name:"RectifyAndReformListItem",components:{},props:{item:{type:Object,default:()=>({})},wbsList:{type:Object,default:()=>({})},navbarType:String},emits:["detailItem"],setup(e,{attrs:t,slots:i,emit:y}){},data(){return{statusMap:R}},watch:{},created(){},computed:{statusTypeMap(){var e;return((e=this.statusMap)==null?void 0:e.TYPE_MAP)||{}},statusLabelMap(){var e;return((e=this.statusMap)==null?void 0:e.LABEL_MAP)||{}},toStartReviewStatus(){var e;return(e=this.statusMap)==null?void 0:e.PENDING_REVIEW}},mounted(){},methods:{getWbsLabel:B,hanelDel(){this.$emit("detailItem")}}},X={class:"task-item"},Z={class:"body"},$={class:"item-info header"},ee={class:"value"},te={class:"item-info"},ae={class:"value"},ne={class:"item-info"},se={class:"value"},ie={class:"item-info"},oe={class:"value"},re={class:"item-info"},le={class:"value"},ce={class:"right"};function me(e,t,i,y,a,s){const g=c("van-tag"),b=c("van-button"),d=c("van-swipe-cell");return o(),u("div",X,[l(d,{disabled:!["add","update"].includes(i.navbarType)},{right:m(()=>[["add","update"].includes(i.navbarType)?(o(),f(b,{key:0,square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:i.item.rectificationStatus!==s.toStartReviewStatus,onClick:I(s.hanelDel,["stop","prevent"])},null,8,["disabled","onClick"])):v("",!0)]),default:m(()=>[r("div",Z,[r("div",$,[t[0]||(t[0]=r("span",{class:"key"},"整改单号",-1)),r("span",ee,p(i.item.correctionNumber),1)]),r("div",te,[t[1]||(t[1]=r("span",{class:"key"},"所属标段",-1)),r("span",ae,p(e.$formatLabel(i.item.sectionId,e.$DICT_CODE.project_section)),1)]),r("div",ne,[t[2]||(t[2]=r("span",{class:"key"},"整改部位",-1)),r("span",se,p(s.getWbsLabel(i.wbsList,i.item.projectPosition)),1)]),r("div",ie,[t[3]||(t[3]=r("span",{class:"key"},"整改期限",-1)),r("span",oe,p(e.$dayjs(i.item.inspectionDate).format("YYYY-MM-DD")),1)]),r("div",re,[t[4]||(t[4]=r("span",{class:"key"},"整改内容",-1)),r("span",le,p(i.item.description),1)]),r("div",ce,[l(g,{class:"tag",color:s.statusTypeMap[i.item.rectificationStatus],plain:"",size:"medium"},{default:m(()=>[_(p(s.statusLabelMap[i.item.rectificationStatus]),1)]),_:1},8,["color"])])])]),_:1},8,["disabled"])])}const ue=A(K,[["render",me],["__scopeId","data-v-d58fca95"]]);const pe={name:"SafetyCheckDetail",components:{FormItemSection:M,FormItemCalendar:G,ListItem:ue,FormItemDate:z,FormItemPicker:P},props:{},emits:[],setup(e,{attrs:t,slots:i,emit:y}){},data(){return{wbsList:[],loading:!1,error:"",formData:{content:"",evidence:"",corrections:[],id:null,inspectionArea:[],inspectionDate:"",inspectionNumber:"",inspectionResult:"",inspectionType:"",inspectionUnit:"",inspectionUnitName:"",inspector:"",inspectorFullname:"",reporter:"",reporterName:"",sectionId:""},SafeInspectionHazardList:[],departmentOptions:[],statusMap:R}},mounted(){var e;this.getWbsList(),Object.keys(this.$store.QUALITY_INSPECTION).length?this.formData=this.$store.QUALITY_INSPECTION:this.navbarType==="add"&&!Object.keys(this.$store.QUALITY_INSPECTION).length?(this.formData.inspectionUnit=(e=this.portal)==null?void 0:e.id,this.formData.inspector=this.user.userName||"",this.formData.inspectorFullname=this.user.userFullname||"",this.formData.reporter=this.user.userName||"",this.formData.reporterName=this.user.userFullname||"",this.formData.sectionId=this.$getPortalId()):["update","detail"].includes(this.navbarType)&&this.getFormData(),this.getData(),console.log("safeInspectionAreaList",this.safeInspectionAreaList)},computed:{safeInspectionAreaList(){return this.$store.ENUM_DICT[C.safe_inspection_area]},qualityInspectionResult(){return this.$store.ENUM_DICT[C.quality_inspection_result]},safeInspectionTypeList(){return this.$store.ENUM_DICT[C.safe_inspection_type]},portalId(){var e;return((e=this.$store.PORTAL)==null?void 0:e.type)===1?this.$store.PORTAL.id:""},portal(){return this.$store.PORTAL},navbarTitle(){return this.$route.query.title||"安全检查详情"},navbarType(){return this.$route.query.type},user(){return this.$store.USER_INFO},inspectionList(){return this.departmentOptions.length?this.departmentOptions:this.$store.INSPECTION_LIST},inspectionUnitLabel(){const e=this.departmentOptions.find(t=>t.value===this.formData.inspectionUnit);return e?e.label:""},inspectionAreaStr(){return this.formData.inspectionArea&&this.formData.inspectionArea.length?this.formData.inspectionArea.map(e=>this.$formatLabel(e,$DICT_CODE.safe_inspection_area)).join("、"):""},toStartReviewStatus(){var e;return(e=this.statusMap)==null?void 0:e.PENDING_REVIEW}},methods:{async getWbsList(){this.wbsList=await x(null,!0,this.$store.PORTAL)},chengeInspectionUnitt(e){console.log("node",e,this.formData.inspectionUnit,this.portalId),this.formData.inspectionUnitName=e.label},chengeSection(e){this.formData.corrections&&this.formData.corrections.length>0&&this.formData.corrections.forEach(t=>{t.sectionId=this.formData.sectionId,t.projectPosition=null})},getFormData(){Q({id:this.$route.query.id}).then(e=>{if(console.log("res==============>",e),e){const t=Array.isArray(e.corrections)?e.corrections:[];this.formData={...e,inspectionArea:e.inspectionArea?e.inspectionArea.split(","):[],corrections:t}}})},getData(){Y(this.portalId).then(e=>{if(e){const{departmentOptions:t}=J(e);this.departmentOptions=t,this.$store.INSPECTION_LIST=t}})},handleClose(){this.$store.QUALITY_INSPECTION={},this.$router.go(-1)},handleAddHiddenTrouble(){if(!this.formData.sectionId){this.$showToast({position:"top",message:"请先选择所属标段"});return}this.$store.QUALITY_INSPECTION=this.formData,this.$store.rectify_AND_REFORM={},this.$router.push({name:"RectifyAndReform",query:{type:"add",title:"添加整改",sectionId:this.formData.sectionId}})},hanelItem(e,t){["add","update"].includes(this.navbarType)&&e.rectificationStatus===this.toStartReviewStatus?(this.$store.QUALITY_INSPECTION=this.formData,this.$store.rectify_AND_REFORM=JSON.parse(JSON.stringify({...e,index:t})),this.$router.push({name:"RectifyAndReform",query:{type:"update",title:"编辑整改"}})):this.$router.push({path:"FormCenter/QualityInspectionCorrection",query:{type:"view",taskKey:e.taskKey,bizId:e.id,taskId:e.taskId}})},async handleAddOrCreate(){try{if(await this.$refs.formDateRef.validate(),this.formData.inspectionResult==="1"&&(!this.formData.corrections||this.formData.corrections.length===0)){L({message:"请至少添加一条整改记录",position:"top"});return}let e;if(this.formData.inspectionResult==="1"&&this.formData.corrections&&this.formData.corrections.length>0){for(let i=0;i<this.formData.corrections.length;i++)if(!this.formData.corrections[i].projectPosition){e="第"+(i+1)+"条质量问题，请选择整改部位";break}}if(e){L({message:e,position:"top"});return}this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t={...this.formData,inspectionArea:this.formData.inspectionArea.join(","),inspectionDate:this.formData.inspectionDate||null,corrections:this.formData.corrections.map(i=>({...i,rectificationStatus:i.rectificationStatus||"1",acceptanceTime:null,acceptanceResult:null,corrections:this.formData.inspectionResult==="1"?i.corrections:[]}))};this.$route.query.type==="add"?await j(t).then(()=>{this.$router.push({path:"/QualityInspection"})}).finally(()=>{this.$closeToast()}):this.$route.query.type==="update"&&await W(t).then(()=>{this.$router.push({path:"/QualityInspection"})}).finally(()=>{this.$closeToast()})}catch(e){console.log(e)}},async handleDelete(e){try{await this.$confirm({title:"提示",message:"确认删除".concat(this.formData.corrections[e].correctionNumber,"?")}),this.formData.corrections.splice(e,1)}catch(t){console.log(t)}}}},de={key:0},fe={key:0},he={key:0},ye={key:2},_e={key:1,class:"btn-group"};function De(e,t,i,y,a,s){const g=c("Navbar"),b=c("van-empty"),d=c("van-field"),k=c("van-radio"),S=c("van-radio-group"),O=c("FormItemPicker"),w=c("FormItemSection"),U=c("FormItemDate"),E=c("van-cell-group"),V=c("van-form"),T=c("van-button"),F=c("ListItem");return o(),u(D,null,[l(g,{back:"",title:s.navbarTitle},null,8,["title"]),a.loading?(o(),u(D,{key:0},[],64)):a.error?(o(),f(b,{key:1,image:"error",description:a.error},null,8,["description"])):(o(),u("div",{key:2,class:H(["view-height",{"btn-bom":["add","update"].includes(s.navbarType)}])},[l(V,{ref:"formDateRef",readonly:s.navbarType==="detail","label-width":"7em","input-align":"right","error-message-align":"right",class:"form-bottom"},{default:m(()=>[l(E,{border:!1},{default:m(()=>[l(d,{modelValue:a.formData.inspectionNumber,"onUpdate:modelValue":t[0]||(t[0]=n=>a.formData.inspectionNumber=n),name:"inspectionNumber",label:"检查编号",readonly:"",placeholder:"自动生成"},null,8,["modelValue"]),l(d,{name:"inspectionType",label:"检查类型",placeholder:"检查类型",labelWidth:"5rem",required:"",rules:[{required:!0,message:"请选择检查类型"}]},{input:m(()=>[s.navbarType==="detail"?(o(),u("span",de,p(e.$formatLabel(a.formData.inspectionType,e.$DICT_CODE.safe_inspection_type)),1)):(o(),f(S,{key:1,modelValue:a.formData.inspectionType,"onUpdate:modelValue":t[1]||(t[1]=n=>a.formData.inspectionType=n),direction:"horizontal"},{default:m(()=>[(o(!0),u(D,null,N(s.safeInspectionTypeList,(n,h)=>(o(),f(k,{key:h,name:n.code},{default:m(()=>[_(p(n["zh-CN"]),1)]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"]))]),_:1}),l(O,{label:"检查单位",readonly:s.navbarType==="detail",name:"inspectionUnit","input-align":"right",value:a.formData.inspectionUnit,"onUpdate:value":t[2]||(t[2]=n=>a.formData.inspectionUnit=n),columns:[...s.inspectionList],"columns-field-names":{text:"label",value:"value",children:"none"},title:"检查单位",required:"",rules:[{required:!0,message:"请选择检查单位"}],onChange:s.chengeInspectionUnitt},null,8,["readonly","value","columns","onChange"]),l(w,{label:"所属标段",placeholder:"请选择",modelValue:a.formData.sectionId,"onUpdate:modelValue":t[3]||(t[3]=n=>a.formData.sectionId=n),readonly:s.navbarType==="detail"||a.formData.corrections.some(n=>n.rectificationStatus!==s.toStartReviewStatus),required:"",rules:[{required:!0,message:"请选择所属标段"}],onSelect:s.chengeSection},null,8,["modelValue","readonly","onSelect"]),l(d,{label:"详细区域",name:"inspectionArea",modelValue:a.formData.inspectionArea,"onUpdate:modelValue":t[4]||(t[4]=n=>a.formData.inspectionArea=n),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"]),l(U,{label:"检查日期",value:a.formData.inspectionDate,"onUpdate:value":t[5]||(t[5]=n=>a.formData.inspectionDate=n),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择检查日期"}],readonly:s.navbarType==="detail"},null,8,["value","readonly"]),l(d,{name:"inspectionResult",label:"检查结果",placeholder:"检查结果",required:"",rules:[{required:!0,message:"请选择检查结果"}]},{input:m(()=>[s.navbarType==="detail"?(o(),u("span",fe,p(e.$formatLabel(a.formData.inspectionResult,e.$DICT_CODE.quality_inspection_result)),1)):(o(),f(S,{key:1,modelValue:a.formData.inspectionResult,"onUpdate:modelValue":t[6]||(t[6]=n=>a.formData.inspectionResult=n),direction:"horizontal",disabled:a.formData.corrections.some(n=>n.rectificationStatus!==s.toStartReviewStatus)},{default:m(()=>[(o(!0),u(D,null,N(s.qualityInspectionResult,(n,h)=>(o(),f(k,{key:h,name:n.code},{default:m(()=>[_(p(n["zh-CN"]),1)]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue","disabled"]))]),_:1}),l(d,{label:"检查内容",name:"content",modelValue:a.formData.content,"onUpdate:modelValue":t[7]||(t[7]=n=>a.formData.content=n),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入检查内容"}],"input-align":"left"},null,8,["modelValue"]),l(d,{label:"检查依据",name:"evidence",modelValue:a.formData.evidence,"onUpdate:modelValue":t[8]||(t[8]=n=>a.formData.evidence=n),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"])]),_:1})]),_:1},8,["readonly"]),a.formData.inspectionResult==="1"?(o(),u("div",he,[t[11]||(t[11]=r("div",{class:"potential-safety-hazard-title"}," 质量问题整改列表 ",-1)),["add","update"].includes(s.navbarType)?(o(),f(T,{key:0,type:"primary",size:"normal",class:"addBtn",onClick:t[9]||(t[9]=I(n=>s.handleAddHiddenTrouble(),["stop","prevent"]))},{default:m(()=>t[10]||(t[10]=[_("新增质量问题")])),_:1,__:[10]})):v("",!0),a.formData.corrections&&a.formData.corrections.length?(o(!0),u(D,{key:1},N(a.formData.corrections||[],(n,h)=>(o(),f(F,{key:h,item:n,style:{"margin-top":"10px"},onClick:I(q=>s.hanelItem(n,h),["stop","prevent"]),navbarType:s.navbarType,wbsList:a.wbsList,onDetailItem:q=>s.handleDelete(h)},null,8,["item","onClick","navbarType","wbsList","onDetailItem"]))),128)):(o(),u("div",ye,[l(b,{description:"暂无数据",style:{padding:"0"}})]))])):v("",!0),["add","update"].includes(s.navbarType)?(o(),u("div",_e,[l(T,{round:"",type:"danger",plain:"",onClick:I(s.handleClose,["stop","prevent"])},{default:m(()=>t[12]||(t[12]=[_("取消")])),_:1,__:[12]},8,["onClick"]),l(T,{round:"",type:"primary",plain:"",onClick:I(s.handleAddOrCreate,["stop","prevent"])},{default:m(()=>t[13]||(t[13]=[_("保存")])),_:1,__:[13]},8,["onClick"])])):v("",!0)],2))],64)}const Re=A(pe,[["render",De],["__scopeId","data-v-fdc8353f"]]);export{Re as default};
