System.register(["./api-legacy-bb5b1e44.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./vant-legacy-b51a9379.js"],(function(e,t){"use strict";var i,a,s,o,n,r,l,d,c,h,g,v;return{setters:[e=>{i=e.a,a=e.b},e=>{s=e._},e=>{o=e.Q,n=e.R,r=e.S,l=e.k,d=e.U,c=e.V,h=e.X,g=e.Y,v=e.F},null],execute:function(){var t=document.createElement("style");t.textContent=".view-height[data-v-88923ba6]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat));padding:2.66667vw 3.2vw;overflow-x:hidden;overflow-y:scroll;background-color:#fff;box-sizing:border-box}.message-detail .title[data-v-88923ba6]{font-size:4.26667vw;font-weight:500;color:#111;line-height:1.4}.message-detail .aside[data-v-88923ba6]{font-size:3.2vw;color:#9a9a9a;display:flex;flex-direction:row;align-items:center;justify-content:space-between}.message-detail .content[data-v-88923ba6]{font-size:3.73333vw;padding:2.66667vw 0}.message-detail .content[data-v-88923ba6] img{display:block;max-width:100%;height:auto;margin:0 auto;border:none}\n",document.head.appendChild(t);const u={class:"view-height"},f={key:1,class:"message-detail"},m={class:"content"},p=["innerHTML"],y={class:"aside"};e("default",s({name:"Detail",components:{},props:{},emits:[],setup(e,{attrs:t,slots:i,emit:a}){},data(){var e;return{id:null===(e=this.$route.query)||void 0===e?void 0:e.id,refreshing:!1,loading:!1,errorMsg:"",detail:{}}},computed:{},watch:{},created(){},mounted(){this.onRefresh()},methods:{onRefresh(){this.getDetail()},async getDetail(){if(this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),!this.id)return this.errorMsg="未找到相关信息",setTimeout((()=>{this.loading=!1,this.$closeToast()}),100),void(this.refreshing=!1);try{this.loading=!0;const e={receiveLogId:this.id},t=await i(e);this.detail=t||{},this.errorMsg="",this.updateStatus()}catch(e){this.errorMsg="出错了, 请稍后重试!",console.log(e)}finally{this.loading=!1,this.refreshing=!1,setTimeout((()=>{this.loading=!1,this.$closeToast()}),100)}},async updateStatus(){var e,t;if(null!=this&&null!==(e=this.detail)&&void 0!==e&&e.receiveLogId&&"-1"==(null==this||null===(t=this.detail)||void 0===t?void 0:t.status))try{await a({receiveLogIdList:this.detail.receiveLogId,status:0})}catch(i){console.log(i)}}}},[["render",function(e,t,i,a,s,b){const w=o("Navbar"),x=o("van-empty"),M=o("van-pull-refresh");return n(),r(v,null,[l(w,{back:""}),d("div",u,[l(M,{modelValue:s.refreshing,"onUpdate:modelValue":t[0]||(t[0]=e=>s.refreshing=e),onRefresh:b.onRefresh},{default:c((()=>[s.errorMsg?(n(),h(x,{key:0,image:"error",description:s.errorMsg},null,8,["description"])):(n(),r("div",f,[d("div",m,[d("div",{innerHTML:s.detail.msgBody},null,8,p)]),d("div",y,[d("span",null,g(s.detail.createDate),1)])]))])),_:1},8,["modelValue","onRefresh"])])],64)}],["__scopeId","data-v-88923ba6"]]))}}}));
