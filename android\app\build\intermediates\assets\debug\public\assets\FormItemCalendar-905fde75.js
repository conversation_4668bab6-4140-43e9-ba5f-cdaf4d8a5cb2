import{_ as m}from"./index-4829f8e2.js";import{Q as i,R as c,S as f,k as o,F as h}from"./verder-361ae6c7.js";const g={name:"FormItemCalendar",components:{},emits:["update:value","update:text"],props:{value:[String,Number],text:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:<PERSON><PERSON><PERSON>,readonly:<PERSON><PERSON>an,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},type:{type:String,default:()=>"single"},minDate:{type:Date,default:()=>new Date},maxDate:{type:Date,default:()=>{const n=new Date().getFullYear();return new Date(n+1,0,1)}},showConfirm:{type:<PERSON>olean,default:!0}},setup(n,{attrs:t,slots:e,emit:u}){},data(){return{showPicker:!1,currentDate:void 0}},computed:{showValue(){return this.value?this.$dayjs(this.value).format("YYYY-MM-DD"):""}},watch:{value:{immediate:!0,handler(n){if(this.currentDate==null){let t=new Date(n);t instanceof Date&&!isNaN(t.getTime())||(t=new Date),this.currentDate=t}}}},created(){},mounted(){},methods:{onShowPicker(){this.readonly||(this.showPicker=!0)},onClosePicker(){this.showPicker=!1},onSelectConfirm(n){const t=this.$dayjs(n).format("YYYY-MM-DD 23:59:59");this.$emit("update:value",t),this.onClosePicker()}}};function w(n,t,e,u,r,a){const d=i("van-field"),s=i("van-calendar");return c(),f(h,null,[o(d,{name:e.name,"model-value":a.showValue,label:e.label,required:e.required,rules:e.rules,"input-align":e.inputAlign,"error-message-align":e.errorMessageAlign,"label-width":e.labelWidth,readonly:"","is-link":!e.readonly,placeholder:"请选择",onClick:t[0]||(t[0]=l=>a.onShowPicker())},null,8,["name","model-value","label","required","rules","input-align","error-message-align","label-width","is-link"]),o(s,{teleport:"#app",show:r.showPicker,"onUpdate:show":t[1]||(t[1]=l=>r.showPicker=l),"show-confirm":e.showConfirm,readonly:e.readonly,title:e.title,"default-date":r.currentDate,"min-date":e.minDate,"max-date":e.maxDate,onConfirm:a.onSelectConfirm,onCancel:a.onClosePicker},null,8,["show","show-confirm","readonly","title","default-date","min-date","max-date","onConfirm","onCancel"])],64)}const k=m(g,[["render",w]]);export{k as F};
