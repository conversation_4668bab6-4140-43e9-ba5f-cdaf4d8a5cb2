System.register(["./index-legacy-09188690.js","./verder-legacy-e6127216.js"],(function(e,t){"use strict";var a,n,r,l,i,o;return{setters:[e=>{a=e._},e=>{n=e.Q,r=e.R,l=e.S,i=e.k,o=e.F}],execute:function(){const t={name:"FormItemCalendar",components:{},emits:["update:value","update:text"],props:{value:[String,Number],text:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:Boolean,readonly:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},type:{type:String,default:()=>"single"},minDate:{type:Date,default:()=>new Date},maxDate:{type:Date,default:()=>{const e=(new Date).getFullYear();return new Date(e+1,0,1)}},showConfirm:{type:Boolean,default:!0}},setup(e,{attrs:t,slots:a,emit:n}){},data:()=>({showPicker:!1,currentDate:void 0}),computed:{showValue(){return this.value?this.$dayjs(this.value).format("YYYY-MM-DD"):""}},watch:{value:{immediate:!0,handler(e){if(null==this.currentDate){let t=new Date(e);t instanceof Date&&!isNaN(t.getTime())||(t=new Date),this.currentDate=t}}}},created(){},mounted(){},methods:{onShowPicker(){this.readonly||(this.showPicker=!0)},onClosePicker(){this.showPicker=!1},onSelectConfirm(e){const t=this.$dayjs(e).format("YYYY-MM-DD 23:59:59");this.$emit("update:value",t),this.onClosePicker()}}};e("F",a(t,[["render",function(e,t,a,s,d,u){const m=n("van-field"),c=n("van-calendar");return r(),l(o,null,[i(m,{name:a.name,"model-value":u.showValue,label:a.label,required:a.required,rules:a.rules,"input-align":a.inputAlign,"error-message-align":a.errorMessageAlign,"label-width":a.labelWidth,readonly:"","is-link":!a.readonly,placeholder:"请选择",onClick:t[0]||(t[0]=e=>u.onShowPicker())},null,8,["name","model-value","label","required","rules","input-align","error-message-align","label-width","is-link"]),i(c,{teleport:"#app",show:d.showPicker,"onUpdate:show":t[1]||(t[1]=e=>d.showPicker=e),"show-confirm":a.showConfirm,readonly:a.readonly,title:a.title,"default-date":d.currentDate,"min-date":a.minDate,"max-date":a.maxDate,onConfirm:u.onSelectConfirm,onCancel:u.onClosePicker},null,8,["show","show-confirm","readonly","title","default-date","min-date","max-date","onConfirm","onCancel"])],64)}]]))}}}));
