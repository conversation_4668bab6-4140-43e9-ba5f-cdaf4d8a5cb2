import{Q as c,R as a,S as k,k as r,V as m,X as f,U as u,a2 as h,Y as S,F as _,W as F}from"./verder-361ae6c7.js";import{_ as w}from"./index-4829f8e2.js";const P={name:"FormItemPicker",components:{},emits:["update:value","update:text","change"],props:{value:[String,Number,Array,Boolean],text:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},placeholder:{type:String,default:"请选择"},required:Boolean,readonly:Boolean,multiple:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},columns:{type:Array,default:()=>[]},columnsFieldNames:{type:Object,default:()=>({text:"text",value:"value",children:"children"})},dictName:String},setup(i,{attrs:e,slots:t,emit:s}){},data(){return{showPicker:!1,selectedValues:void 0,selectedText:void 0,multipleChecked:[],checked:[],checkboxRefs:[],multipleSelectedText:[]}},computed:{computedColumns(){return this.dictName?this.$store.ENUM_DICT[this.dictName]:this.columns},computedColumnsFieldNames(){return this.dictName?{text:"zh-CN",value:"code",children:"none"}:this.columnsFieldNames}},watch:{value:{immediate:!0,handler(i){if(this.multiple){let e=[];i!=null&&i!=""&&(e=String(i).split(",")||[]),this.multipleChecked=e||[],this.checked=e;const t=[];e.forEach(s=>{const l=this.computedColumns.find(n=>n[this.computedColumnsFieldNames.value]==s);l&&t.push(l==null?void 0:l[this.computedColumnsFieldNames.text])}),this.multipleSelectedText=t,this.$emit("update:text",t.join(","))}else{this.selectedValues=i!=null&&i!==""?[i]:[];let e="";if(this.selectedValues&&this.selectedValues.length){const t=this.computedColumns.find(s=>s[this.computedColumnsFieldNames.value]==this.value);e=(t==null?void 0:t[this.computedColumnsFieldNames.text])||""}this.selectedText=e,this.$emit("update:text",e)}}}},created(){},mounted(){},methods:{onShowPicker(){this.readonly||(this.showPicker=!0)},onClosePicker(){this.showPicker=!1},onSelectConfirm({selectedValues:i,selectedOptions:e}){var l;const t=i[0],s=(l=e[0])==null?void 0:l[this.computedColumnsFieldNames.text];this.$emit("update:value",t),this.$emit("update:text",s),this.$emit("change",e[0]),this.selectedText=s,this.onClosePicker()},handleChecked(i,e){this.checkboxRefs[i].toggle()},onCloseMultiplePicker(){this.onClosePicker(),this.checked=this.multipleChecked||[]},onSelectMultipleConfirm(){const i=this.checked.map(s=>this.computedColumns.find(n=>n[this.computedColumnsFieldNames.value]==s)).filter(s=>s!=null),e=i.map(s=>s[this.computedColumnsFieldNames.text]),t=Array.isArray(this.value)?[...this.checked]:this.checked.join(",");this.$emit("update:value",t),this.$emit("update:text",e.join(",")),this.$emit("change",i||[]),this.multipleSelectedText=e,this.onClosePicker()}}},V={key:1,class:"van-picker"},T={class:"van-picker__toolbar"},A={class:"van-picker__title van-ellipsis"},B={class:"van-picker__columns picker-content"},M={class:"van-picker-column picker-list"};function j(i,e,t,s,l,n){const v=c("van-field"),C=c("van-picker"),g=c("van-checkbox"),x=c("van-cell"),b=c("van-cell-group"),y=c("van-checkbox-group"),N=c("van-popup");return a(),k(_,null,[r(v,{name:t.name,"model-value":t.multiple?l.multipleSelectedText.join(","):l.selectedText,type:t.multiple?"textarea":"text",rows:t.multiple?1:void 0,autosize:t.multiple,label:t.label,required:t.required,rules:t.rules,"input-align":t.inputAlign,"error-message-align":t.errorMessageAlign,"label-width":t.labelWidth,readonly:"","is-link":!t.readonly,placeholder:t.placeholder,onClick:e[0]||(e[0]=o=>n.onShowPicker())},null,8,["name","model-value","type","rows","autosize","label","required","rules","input-align","error-message-align","label-width","is-link","placeholder"]),r(N,{show:l.showPicker,"onUpdate:show":e[5]||(e[5]=o=>l.showPicker=o),position:"bottom",teleport:"#app"},{default:m(()=>[t.multiple?(a(),k("div",V,[u("div",T,[u("button",{type:"button",class:"van-picker__cancel van-haptics-feedback",onClick:e[2]||(e[2]=h(o=>n.onCloseMultiplePicker(),["stop","prevent"]))}," 取消 "),u("div",A,S(t.title),1),u("button",{type:"button",class:"van-picker__confirm van-haptics-feedback",onClick:e[3]||(e[3]=h(o=>n.onSelectMultipleConfirm(),["stop","prevent"]))}," 确认 ")]),u("div",B,[u("div",M,[r(y,{modelValue:l.checked,"onUpdate:modelValue":e[4]||(e[4]=o=>l.checked=o)},{default:m(()=>[r(b,null,{default:m(()=>[(a(!0),k(_,null,F([...n.computedColumns],(o,d)=>(a(),f(x,{key:d,clickable:"",title:o[n.computedColumnsFieldNames.text],onClick:h(p=>n.handleChecked(d,o),["stop","prevent"])},{"right-icon":m(()=>[r(g,{onClick:h(p=>n.handleChecked(d,o),["stop","prevent"]),ref_for:!0,ref:p=>l.checkboxRefs[d]=p,name:o[n.computedColumnsFieldNames.value]},null,8,["onClick","name"])]),_:2},1032,["title","onClick"]))),128))]),_:1})]),_:1},8,["modelValue"])])])])):(a(),f(C,{key:0,ref:"picker",modelValue:l.selectedValues,"onUpdate:modelValue":e[1]||(e[1]=o=>l.selectedValues=o),title:t.title,columns:[...n.computedColumns],columnsFieldNames:n.computedColumnsFieldNames,onConfirm:n.onSelectConfirm,onCancel:n.onClosePicker},null,8,["modelValue","title","columns","columnsFieldNames","onConfirm","onCancel"]))]),_:1},8,["show"])],64)}const q=w(P,[["render",j],["__scopeId","data-v-c66453ea"]]);export{q as F};
