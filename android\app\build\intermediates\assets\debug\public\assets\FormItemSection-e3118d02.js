import{u as k}from"./index-4829f8e2.js";import{c as s,r as m,f as M,w as g,Q as v,R as b,S as P,k as p,q as C,V as R,F as q}from"./verder-361ae6c7.js";const F={__name:"FormItemSection",props:{modelValue:{type:String,default:""},textKey:{type:String,default:"ext2"},valueKey:{type:String,default:"id"},filterMethod:{type:Function,default:null},readonly:{type:Boolean,default:!1},name:String,label:{type:String,default:"所属标段"},placeholder:{type:String,default:"请选择标段"},required:{type:Boolean,default:!1},rules:{type:Array,default:()=>[]},inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},labelWidth:{type:String,default:void 0},title:{type:String,default:"请选择标段"}},emits:["select","update:modelValue"],setup(r,{emit:S}){const e=r,y=S,h=s(()=>({name:e.name,label:e.label,placeholder:e.placeholder,required:e.required,rules:e.rules,inputAlign:e.inputAlign,errorMessageAlign:e.errorMessageAlign,labelWidth:e.labelWidth})),d=k(),u=m(!1),o=m([""]),n=m(null),i=s(()=>{if(e.filterMethod)return e.filterMethod(d.PORTALS);const l=d.PORTAL;return l?l.parentId==="0"?d.PORTALS.filter(t=>t.parentId!=="0"):d.PORTALS.filter(t=>t.id===l.id):[]}),V=s(()=>{if(n.value)return n.value[e.textKey];if(e.modelValue){const l=f(e.modelValue);return l?l[e.textKey]:"".concat(e.modelValue)}return e.placeholder}),f=l=>l?i.value.find(t=>t[e.valueKey]===l||t[e.textKey]===l):null;s(()=>!e.modelValue||e.modelValue.trim()===""),M(()=>{if(e.modelValue&&i.value.length>0){const l=f(e.modelValue);l&&(n.value=l,o.value=[l[e.valueKey]])}}),g(()=>e.modelValue,l=>{if(l&&i.value.length>0){const t=f(l);t&&(n.value=t,o.value=[t[e.valueKey]])}},{immediate:!0}),g(i,l=>{if(l.length>0){if(n.value){const t=l.find(a=>a[e.valueKey]===n.value[e.valueKey]);if(t){n.value=t,o.value=[t[e.valueKey]];return}}if(e.modelValue){const t=l.find(a=>a[e.textKey]===e.modelValue||a[e.valueKey]===e.modelValue);t&&(n.value=t,o.value=[t[e.valueKey]])}}else n.value=null,e.modelValue?o.value=[e.modelValue]:o.value=[""]},{deep:!0});const K=({selectedOptions:l,selectedValues:t})=>{if(!l||l.length===0){u.value=!1;return}const a=l[0];if(!a){u.value=!1;return}n.value=a,y("update:modelValue",a[e.valueKey]),y("select",a),u.value=!1};return(l,t)=>{const a=v("van-field"),A=v("van-picker"),x=v("van-popup");return b(),P(q,null,[p(a,C({"model-value":V.value},{...h.value,...l.$attrs},{type:"text",readonly:"","is-link":!r.readonly,onClick:t[0]||(t[0]=c=>!r.readonly&&(u.value=!0))}),null,16,["model-value","is-link"]),p(x,{show:u.value,"onUpdate:show":t[2]||(t[2]=c=>u.value=c),teleport:"#app","destroy-on-close":"",position:"bottom",round:!0},{default:R(()=>[p(A,{columns:i.value,"model-value":o.value,"columns-field-names":{text:r.textKey,value:r.valueKey},title:r.title,onConfirm:K,onCancel:t[1]||(t[1]=c=>u.value=!1)},null,8,["columns","model-value","columns-field-names","title"])]),_:1},8,["show"])],64)}}};export{F as _};
