import{F as P}from"./FormItemDate-ba00d9d5.js";import{_ as U}from"./FormItemSection-e3118d02.js";import{F as w}from"./FormItemCalendar-905fde75.js";import{U as O}from"./index-fc22947f.js";import{F as T}from"./FormItemCoord-9e82e1bf.js";import{F as V}from"./FormItemCascader-c665b251.js";import{F as q}from"./FormItemPicker-d3f69283.js";import{g as E}from"./wbsUtil-3e809cfd.js";import{F as S}from"./FormItemPerson-bd0e3e57.js";import{R as x}from"./constants-94a272fa.js";import{Q as l,R as u,S as d,k as r,U as R,V as i,B as p,a2 as f,Z as k,F as z}from"./verder-361ae6c7.js";import{_ as A}from"./index-4829f8e2.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./validate-2249584f.js";import"./array-15ef8611.js";const L={name:"HiddenTroubleInfo",components:{FormItemSection:U,FormItemDate:P,FormItemPerson:S,FormItemPicker:q,FormItemCascader:V,FormItemCoord:T,UploadFiles:O,FormItemCalendar:w},props:{title:{type:String,default:""}},emits:[],setup(a,{attrs:e,slots:c,emit:h}){},data(){var a;return{taskKey:((a=this.$route.query)==null?void 0:a.taskKey)||"",formData:{},wbsList:[],statusMap:x}},computed:{navbarTitle(){return this.$route.query.title||"新增隐患"},navbarType(){return this.$route.query.type},portal(){return this.$store.PORTAL},user(){return this.$store.USER_INFO},toStartReviewStatus(){var a;return(a=this.statusMap)==null?void 0:a.PENDING_REVIEW}},mounted(){this.initData()},methods:{handleClose(){this.$store.HIDDEN_TROUBLE_INFO={},this.$router.back()},async handleAddOrCreate(){try{await this.$refs.form.validate(),this.formData.constructionPost=!this.formData.lng||!this.formData.lat?"":[this.formData.lng,this.formData.lat].join(","),this.formData.hasOwnProperty("index")&&this.$store.SAFE_INSPECTION_FORM.hasOwnProperty("hazards")?this.$store.SAFE_INSPECTION_FORM.hazards[this.formData.index]=this.formData:!this.formData.hasOwnProperty("index")&&this.$store.SAFE_INSPECTION_FORM.hasOwnProperty("hazards")&&this.$store.SAFE_INSPECTION_FORM.hazards.push(this.formData),this.$store.HIDDEN_TROUBLE_INFO={},this.$router.back()}catch(a){console.log(a)}},async getWbsList(){this.wbsList=await E(this.formData.sectionId,!0,this.portal),this.$nextTick(()=>{this.$refs.formItemCascaderRef.chengeLabel()})},handlePersonChange(){},async initData(){if(console.log("portal",this.portal),this.$store.HIDDEN_TROUBLE_INFO.hasOwnProperty("name")){if(this.formData.constructionPost){let a=this.formData.constructionPost.split(",");this.formData.lng=a[0],this.formData.lat=a[1]}this.formData=this.$store.HIDDEN_TROUBLE_INFO}else this.formData={sectionId:this.$route.query.sectionId,name:"",hazardNumber:"",projectPosition:"",constructionArea:"",source:"",level:"",deadline:"",dangerSource:"",description:"",requirement:"",fileUpload:"",type:"",typeChild:"",measure:"",measureDate:"",initiator:"",processName:"",processCode:"",measureFileUpload:"",isPass:"",needSupervision:"",positionInfo:null,lng:null,lat:null,height:null,rectificationStatus:this.toStartReviewStatus};this.getWbsList()}}},B={class:"view-height btn-bom"},H={key:0,class:"btn-group"};function M(a,e,c,h,t,n){const g=l("Navbar"),s=l("van-field"),D=l("FormItemSection"),I=l("FormItemCascader"),_=l("FormItemPicker"),F=l("FormItemDate"),b=l("UploadFiles"),v=l("FormItemCoord"),N=l("FormItemPerson"),C=l("van-cell-group"),y=l("van-form"),m=l("van-button");return u(),d(z,null,[r(g,{back:"",title:n.navbarTitle},null,8,["title"]),R("div",B,[r(y,{ref:"form","label-width":"9em","input-align":"right","error-message-align":"right"},{default:i(()=>[r(C,{border:!1},{default:i(()=>[r(s,{modelValue:t.formData.hazardNumber,"onUpdate:modelValue":e[0]||(e[0]=o=>t.formData.hazardNumber=o),label:"隐患编号",placeholder:"自动生成",readonly:""},null,8,["modelValue"]),r(D,{label:"所属标段",placeholder:"请选择",modelValue:t.formData.sectionId,"onUpdate:modelValue":e[1]||(e[1]=o=>t.formData.sectionId=o),readonly:"",required:"",rules:[{required:!0,message:"请选择所属标段"}]},null,8,["modelValue"]),r(I,{ref:"formItemCascaderRef",label:"工程部位",value:t.formData.projectPosition,"onUpdate:value":e[2]||(e[2]=o=>t.formData.projectPosition=o),columns:[...t.wbsList],"columns-field-names":{text:"nodeName",value:"id",children:"children"},"trigger-change-mode":!0,placeholder:"请选择"},null,8,["value","columns"]),r(_,{label:"隐患级别",value:t.formData.level,"onUpdate:value":e[3]||(e[3]=o=>t.formData.level=o),"dict-name":a.$DICT_CODE.safe_hazard_level,placeholder:"请选择",required:"",rules:[{required:!0,message:"请选择隐患级别"}]},null,8,["value","dict-name"]),r(F,{label:"整改期限",value:t.formData.deadline,"onUpdate:value":e[4]||(e[4]=o=>t.formData.deadline=o),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择整改期限"}]},null,8,["value"]),r(s,{label:"详细区域",modelValue:t.formData.constructionArea,"onUpdate:modelValue":e[5]||(e[5]=o=>t.formData.constructionArea=o),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"]),r(s,{label:"隐患名称",modelValue:t.formData.name,"onUpdate:modelValue":e[6]||(e[6]=o=>t.formData.name=o),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入隐患名称"}],"input-align":"left"},null,8,["modelValue"]),r(s,{label:"整改内容",modelValue:t.formData.description,"onUpdate:modelValue":e[7]||(e[7]=o=>t.formData.description=o),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改内容"}],"input-align":"left"},null,8,["modelValue"]),r(s,{label:"整改要求及处理意见",modelValue:t.formData.requirement,"onUpdate:modelValue":e[8]||(e[8]=o=>t.formData.requirement=o),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改要求及处理意见"}],"input-align":"left"},null,8,["modelValue"]),r(s,{label:"资料附件上传","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传资料附件"}]},{input:i(()=>[r(b,{ref:"fileUpload",g9s:t.formData.fileUpload,"onUpdate:g9s":e[9]||(e[9]=o=>t.formData.fileUpload=o),accept:"*",multiple:!0},null,8,["g9s"])]),_:1}),r(v,{label:"经纬度",longitude:t.formData.lng,"onUpdate:longitude":e[10]||(e[10]=o=>t.formData.lng=o),latitude:t.formData.lat,"onUpdate:latitude":e[11]||(e[11]=o=>t.formData.lat=o),title:"选择定位"},null,8,["longitude","latitude"]),r(N,{required:!0,label:"隐患分发人",title:"隐患分发人",userFullname:t.formData.acceptancePersonName,"onUpdate:userFullname":e[12]||(e[12]=o=>t.formData.acceptancePersonName=o),userName:t.formData.acceptancePerson,"onUpdate:userName":e[13]||(e[13]=o=>t.formData.acceptancePerson=o),onChange:n.handlePersonChange,rules:[{required:!0,message:"请选择隐患分发人"}]},null,8,["userFullname","userName","onChange"])]),_:1})]),_:1},512),["add","update"].includes(n.navbarType)?(u(),d("div",H,[r(m,{round:"",type:"danger",plain:"",onClick:f(n.handleClose,["stop","prevent"])},{default:i(()=>e[14]||(e[14]=[p("取消")])),_:1,__:[14]},8,["onClick"]),r(m,{round:"",type:"primary",plain:"",onClick:f(n.handleAddOrCreate,["stop","prevent"])},{default:i(()=>e[15]||(e[15]=[p("保存")])),_:1,__:[15]},8,["onClick"])])):k("",!0)])],64)}const me=A(L,[["render",M],["__scopeId","data-v-a44a7d60"]]);export{me as default};
