import{u as A,s as O,b as B,c as H,_ as T,f as U}from"./index-4829f8e2.js";import{r as k,f as N,Q as r,R as a,S as _,U as m,Y as R,u as b,k as h,V as y,F as S,g as Q,O as z,c as Y,X as g,W as $,y as C,Z as I,a2 as V}from"./verder-361ae6c7.js";import{u as j}from"./api-77f046f1.js";import{a as F}from"./vant-91101745.js";import{L as G,g as J}from"./api-dc8c064b.js";import{i as W}from"./validate-2249584f.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";const Z={class:"mr-[1em]"},q={__name:"index",setup(t){const e=A(),l=O(),n=k(!1),i=k([e.PORTAL_ID]),c=({selectedOptions:u})=>{const s=u[0],d=(s==null?void 0:s.type)===1?"":s.id;e.$patch({PORTAL:s,PORTAL_ID:d}),e.GET_BUTTONS(),e.GET_ROLES(),e.GET_NODES_TREE(),e.CLEAR_AUTOFILL(),l.CLEAR_BIZ(),n.value=!1};return N(()=>{console.log("selectedValues",i.value)}),(u,s)=>{var v;const d=r("van-icon"),o=r("van-picker"),p=r("van-popup");return a(),_(S,null,[m("div",{class:"flex flex-row items-center text-[var(--nav-bar-text-color)]",onClick:s[0]||(s[0]=f=>n.value=!0)},[m("div",Z,R((v=b(e).PORTAL)==null?void 0:v.ext2),1),h(d,{name:"arrow-down"})]),h(p,{teleport:"#app","close-on-popstate":"","safe-area-inset-top":"",show:n.value,"onUpdate:show":s[3]||(s[3]=f=>n.value=f),round:"",position:"top"},{default:y(()=>[h(o,{modelValue:i.value,"onUpdate:modelValue":s[1]||(s[1]=f=>i.value=f),title:"请选择门户",columns:b(e).PORTALS,"columns-field-names":{text:"ext2",value:"id"},onCancel:s[2]||(s[2]=f=>n.value=!1),onConfirm:c},null,8,["modelValue","columns"])]),_:1},8,["show"])],64)}}},X="/app/assets/icon-svg-hydrology-1-36e46af0.svg",K="/app/assets/icon-svg-hydrology-2-10711c81.svg",ee=B("Browser",{web:()=>H(()=>import("./web-dc97dd9b.js"),["assets/web-dc97dd9b.js","assets/index-4829f8e2.js","assets/verder-361ae6c7.js","assets/vant-91101745.js","assets/index-a9b3f457.css"]).then(t=>new t.BrowserWeb)}),te=async t=>{try{await ee.open({url:t,toolbarColor:"#1989fa",presentationStyle:"fullscreen"})}catch(e){window.location.href=t}};const se=["onClick"],oe={key:1,class:"item-icon"},ne={class:"item-name"},ae={__name:"Menu",setup(t){const{proxy:e}=Q(),l=z();let n=k();N(()=>{i()});const i=async()=>{const o=await j({page:1,size:1});n.value=o.total},c=o=>({"hydrology-1":X,"hydrology-2":K})[o],u=[{name:"待办任务",icon:"todo-list-o",path:"/Tasks"},{name:"视频监控",icon:"tv-o",path:"/Monitor/list"},{name:"质量检查",icon:"shield-o",path:"/QualityInspection"},{name:"质量整改",icon:"completed-o",path:"/QualityInspectionCorrection"},{name:"安全检查",icon:"records-o",path:"/SafetyCheck"},{name:"隐患管理",icon:"bulb-o",path:"/SafeInspectionHazard"},{name:"姚家平水文",svgIcon:"hydrology-1",path:"/YPJHydrology"},{name:"恩施水文",svgIcon:"hydrology-2",path:"/ESHydrology"},{name:"进度管理",icon:"underway-o",path:"/ProcessOverview"},{name:"人员管理",icon:"friends-o",path:"/PersonnelManagement",disabled:!0},{name:"环境监测",icon:"flower-o",path:"/CircumstancesDetection",disabled:!0},{name:"安全监测",icon:"aim",path:"/SafetyMonitoring",disabled:!0}],s=Y(()=>{const o=[];for(let p=0;p<u.length;p+=12){const v=u.slice(p,p+12);o.push(v)}return o}),d=o=>{if(o.disabled){F({message:"功能开发中"});return}const p={"/YPJHydrology":{url:"https://www.esswj.cn/rq/yjpsk.aspx",title:"姚家平水文"},"/ESHydrology":{url:"https://www.esswj.cn/RQ/default.aspx",title:"恩施水文"}};if(p[o.path]){te(p[o.path].url);return}o.path==="/Tasks"&&e.$bizStore.saveData({tasksTabName:"MyTodo"}),o.path==="/DesignDispatch"&&e.$bizStore.saveData({designDispatchTabName:"00_01_01"}),l.push(o.path)};return(o,p)=>{const v=r("van-badge"),f=r("van-image"),x=r("van-icon"),P=r("van-swipe-item"),D=r("van-swipe");return a(),g(D,{class:"menu-swiper",autoplay:0,loop:!1},{default:y(()=>[(a(!0),_(S,null,$(s.value||[],(E,L)=>(a(),g(P,{key:L,class:"swiper-item"},{default:y(()=>[(a(!0),_(S,null,$(E||[],(w,M)=>(a(),_("div",{key:"".concat(L,"_").concat(M),class:C(["menu-item",{disabled:w.disabled}]),onClick:we=>d(w)},[w.name==="待办任务"&&b(n)!=0?(a(),g(v,{key:0,class:"brage",content:b(n)},null,8,["content"])):I("",!0),w.svgIcon?(a(),_("div",oe,[h(f,{src:c(w.svgIcon),class:"svg-icon"},null,8,["src"])])):(a(),g(x,{key:2,class:"item-icon",name:w.icon||"apps-o"},null,8,["name"])),m("view",ne,R(w.name),1)],10,se))),128))]),_:2},1024))),128))]),_:1})}}},re=T(ae,[["__scopeId","data-v-15169a54"]]);const le={name:"News",components:{NewsListItem:G},props:{hasNotice:Boolean},emits:["news-scroll"],setup(t,{attrs:e,slots:l,emit:n}){},data(){return{loading:!1,list:[]}},computed:{portals(){return this.$store.PORTALS}},watch:{},created(){this.onRefresh()},mounted(){this.$refs.scrollContent&&(this.handleScroll=()=>{const t=this.$refs.scrollContent.scrollTop||0;this.$emit("news-scroll",t)},this.$refs.scrollContent.addEventListener("scroll",this.handleScroll),setTimeout(()=>{this.$emit("news-scroll",this.$refs.scrollContent.scrollTop||0)},300))},beforeUnmount(){this.$refs.scrollContent&&this.handleScroll&&this.$refs.scrollContent.removeEventListener("scroll",this.handleScroll)},methods:{onRefresh(){this.getList()},async getList(){try{this.loading=!0;const t={page:1,size:10,name:"",portals:this.portals.map(l=>l.id).join(",")},e=await J(t);this.list=(e==null?void 0:e.records)||[]}catch(t){console.log(t)}finally{this.loading=!1}},goNewsList(){this.$router.push({name:"NewsList"})}}},ie={class:"home-news-block",ref:"newsBlock"},ce={class:"block-header"},pe={key:0,class:"empty"};function ue(t,e,l,n,i,c){const u=r("van-icon"),s=r("NewsListItem");return a(),_("div",ie,[m("div",ce,[e[2]||(e[2]=m("div",{class:"title"},"新闻资讯",-1)),m("div",{class:"more",onClick:e[0]||(e[0]=V(d=>c.goNewsList(),["stop","prevent"]))},[e[1]||(e[1]=m("span",null,"查看更多",-1)),h(u,{name:"arrow",color:"#9a9a9a"})])]),m("div",{class:C(["block-content",{"has-notice":l.hasNotice}]),ref:"scrollContent"},[i.list&&i.list.length?(a(!0),_(S,{key:0},$([...i.list],(d,o)=>(a(),g(s,{key:o,item:d,simple:""},null,8,["item"]))),128)):(a(),_(S,{key:1},[i.loading?I("",!0):(a(),_("div",pe,"暂无新闻资讯"))],64))],2)],512)}const de=T(le,[["render",ue],["__scopeId","data-v-b3007328"]]),me={name:"QrScanner",components:{},emits:[],props:{},setup(t,{attrs:e,slots:l,emit:n}){},data(){return{}},computed:{},watch:{},created(){},mounted(){},methods:{goQrScanPage(){this.$router.push({name:"ScanPage"})}}};function he(t,e,l,n,i,c){const u=r("van-icon");return a(),g(u,{size:"26",name:"scan",onClick:e[0]||(e[0]=V(s=>c.goQrScanPage(),["stop","prevent"]))})}const _e=T(me,[["render",he]]);const fe={name:"Home",components:{PortalSelect:q,Menu:re,News:de,QrScanner:_e},emits:[],props:{},setup(t,{attrs:e,slots:l,emit:n}){},data(){return{refreshing:!1,refreshDisabled:!1,newsScrollTop:0,touchStartY:0,touchInMenuArea:!1}},computed:{protocol(){return window.location.protocol},appVersion(){return this.$store.VERSION_INFO},showQrScanner(){if(this.appVersion.platform=="web")return!0;if(!this.appVersion.version)return!1;const t=U("1.2.0",this.appVersion.version);return t==-1||t==0}},watch:{newsScrollTop(t){this.updateRefreshDisabled()}},created(){},mounted(){document.addEventListener("touchstart",this.onTouchStart)},beforeUnmount(){document.removeEventListener("touchstart",this.onTouchStart)},methods:{onRefresh(){var t;(t=this.$refs)==null||t.news.onRefresh(),setTimeout(()=>{this.refreshing=!1,this.updateRefreshDisabled()},300)},downloadApp(t){t.preventDefault();const{downloadUrl:e}=this.appVersion||{};e?W(e)?window.location.href=e+"?t="+Date.now():this.$showToast({message:"非HTTPS环境不支持安全下载"}):this.$showToast({message:"未获取到下载地址"})},onNewsScroll(t){this.newsScrollTop=t},onTouchStart(t){var l;this.touchStartY=t.touches[0].clientY;const e=(l=this.$refs.menu)==null?void 0:l.$el;if(e){const n=e.getBoundingClientRect();this.touchInMenuArea=this.touchStartY<=n.bottom}this.updateRefreshDisabled()},updateRefreshDisabled(){if(this.touchInMenuArea){this.refreshDisabled=!1;return}this.refreshDisabled=this.newsScrollTop>0}}};function ve(t,e,l,n,i,c){const u=r("PortalSelect"),s=r("Navbar"),d=r("van-notice-bar"),o=r("Menu"),p=r("News"),v=r("van-pull-refresh");return a(),_(S,null,[h(s,null,{left:y(()=>[h(u)]),title:y(()=>e[1]||(e[1]=[m("div",null,null,-1)])),_:1}),c.appVersion.update?(a(),g(d,{key:0,"left-icon":"volume-o",text:c.appVersion.updateText||"当前App有新版本, 请重新下载更新",onClick:c.downloadApp},null,8,["text","onClick"])):I("",!0),m("div",{class:C(["content",{"has-notice":c.appVersion.update}])},[h(v,{modelValue:i.refreshing,"onUpdate:modelValue":e[0]||(e[0]=f=>i.refreshing=f),onRefresh:c.onRefresh,disabled:i.refreshDisabled},{default:y(()=>[h(o,{ref:"menu"},null,512),h(p,{ref:"news","has-notice":c.appVersion.update,onNewsScroll:c.onNewsScroll},null,8,["has-notice","onNewsScroll"])]),_:1},8,["modelValue","onRefresh","disabled"])],2)],64)}const Re=T(fe,[["render",ve],["__scopeId","data-v-03f6acf9"]]);export{Re as default};
