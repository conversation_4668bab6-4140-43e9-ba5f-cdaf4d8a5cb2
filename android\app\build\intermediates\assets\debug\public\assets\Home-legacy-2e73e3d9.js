System.register(["./index-legacy-09188690.js","./verder-legacy-e6127216.js","./api-legacy-347ab5d7.js","./vant-legacy-b51a9379.js","./api-legacy-6bb462eb.js","./validate-legacy-4e8f0db9.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js"],(function(e,t){"use strict";var a,o,n,s,i,r,l,c,d,h,p,v,u,m,w,f,g,b,y,S,k,x,T,_,C,R,L,$,I,N;return{setters:[e=>{a=e.u,o=e.s,n=e.b,s=e.c,i=e._,r=e.f},e=>{l=e.r,c=e.f,d=e.Q,h=e.R,p=e.S,v=e.U,u=e.Y,m=e.u,w=e.k,f=e.V,g=e.F,b=e.g,y=e.O,S=e.c,k=e.X,x=e.W,T=e.y,_=e.Z,C=e.a2},e=>{R=e.u},e=>{L=e.a},e=>{$=e.L,I=e.g},e=>{N=e.i},null,null,null,null,null],execute:function(){var j=document.createElement("style");j.textContent='@charset "UTF-8";.menu-swiper[data-v-15169a54]{width:100%;height:69.33333vw}.menu-swiper .swiper-item[data-v-15169a54]{display:flex;width:100%;height:100%;flex-wrap:wrap;align-content:flex-start}.menu-swiper .swiper-item .menu-item[data-v-15169a54]{position:relative;width:25%;display:flex;flex-direction:column;align-items:center;padding:2.66667vw 0}.menu-swiper .swiper-item .menu-item .item-icon[data-v-15169a54]{display:flex;flex-direction:row;align-items:center;justify-content:center;width:10.66667vw;height:10.66667vw;font-size:9.6vw;color:var(--van-primary-color)}.menu-swiper .swiper-item .menu-item .item-icon .svg-icon[data-v-15169a54]{color:var(--van-primary-color)}.menu-swiper .swiper-item .menu-item .svg-icon-wrapper[data-v-15169a54]{display:flex;align-items:center;justify-content:center}.menu-swiper .swiper-item .menu-item .svg-icon[data-v-15169a54]{width:8.53333vw;height:8.53333vw;fill:var(--van-primary-color)}.menu-swiper .swiper-item .menu-item .item-name[data-v-15169a54]{font-size:3.73333vw;white-space:nowrap;width:100%;text-align:center;box-sizing:border-box;overflow:hidden;text-overflow:ellipsis}.menu-swiper .swiper-item .menu-item.disabled[data-v-15169a54]{opacity:.35}.menu-swiper[data-v-15169a54] .van-swipe__indicators .van-swipe__indicator{width:2.66667vw;height:.8vw;border-radius:1px}.brage[data-v-15169a54]{position:absolute;top:6.13333vw;right:5.33333vw}.home-news-block[data-v-b3007328]{padding:5.33333vw 3.2vw 2.66667vw;border-top:1px solid #f7f7f7}.block-header[data-v-b3007328]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;line-height:1;margin-bottom:2.66667vw}.block-header .title[data-v-b3007328]{font-size:3.73333vw;font-weight:700;color:#111;position:relative;padding-left:2em}.block-header .title[data-v-b3007328]:before{width:4.26667vw;height:4.26667vw;position:absolute;left:0;top:-.53333vw;content:"";border-radius:.53333vw;background-color:rgba(56,149,255,.4)}.block-header .title[data-v-b3007328]:after{width:4.26667vw;height:4.26667vw;position:absolute;left:1.06667vw;top:.53333vw;content:"";border-radius:.53333vw;background-color:#3895ff}.block-header .more[data-v-b3007328]{font-size:3.2vw;color:#9a9a9a;display:inline-flex;flex-direction:row;align-items:center;cursor:pointer}.block-header .more[data-v-b3007328] .van-icon{font-size:inherit;margin-left:.53333vw}.block-content[data-v-b3007328]{padding:2.66667vw 0;overflow-y:auto;box-sizing:border-box;height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height) - var(--sab) - 77.33333vw)}.block-content.has-notice[data-v-b3007328]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height) - var(--sab) - var(--van-notice-bar-height) - 56vw)}.block-content .empty[data-v-b3007328]{font-size:3.2vw;color:#9e9e9e;text-align:center;padding-top:3em}.content[data-v-03f6acf9]{height:calc(100vh - var(--van-nav-bar-height) - var(--van-tabbar-height) - var(--sat) - var(--sab));background-color:#fff;overflow-x:hidden;overflow-y:hidden}.content.has-notice[data-v-03f6acf9]{height:calc(100vh - var(--van-nav-bar-height) - var(--van-tabbar-height) - var(--sat) - var(--sab) - var(--van-notice-bar-height))}\n',document.head.appendChild(j);const D={class:"mr-[1em]"},V={__name:"index",setup(e){const t=a(),n=o(),s=l(!1),i=l([t.PORTAL_ID]),r=({selectedOptions:e})=>{const a=e[0],o=1===(null==a?void 0:a.type)?"":a.id;t.$patch({PORTAL:a,PORTAL_ID:o}),t.GET_BUTTONS(),t.GET_ROLES(),t.GET_NODES_TREE(),t.CLEAR_AUTOFILL(),n.CLEAR_BIZ(),s.value=!1};return c((()=>{console.log("selectedValues",i.value)})),(e,a)=>{var o;const n=d("van-icon"),l=d("van-picker"),c=d("van-popup");return h(),p(g,null,[v("div",{class:"flex flex-row items-center text-[var(--nav-bar-text-color)]",onClick:a[0]||(a[0]=e=>s.value=!0)},[v("div",D,u(null===(o=m(t).PORTAL)||void 0===o?void 0:o.ext2),1),w(n,{name:"arrow-down"})]),w(c,{teleport:"#app","close-on-popstate":"","safe-area-inset-top":"",show:s.value,"onUpdate:show":a[3]||(a[3]=e=>s.value=e),round:"",position:"top"},{default:f((()=>[w(l,{modelValue:i.value,"onUpdate:modelValue":a[1]||(a[1]=e=>i.value=e),title:"请选择门户",columns:m(t).PORTALS,"columns-field-names":{text:"ext2",value:"id"},onCancel:a[2]||(a[2]=e=>s.value=!1),onConfirm:r},null,8,["modelValue","columns"])])),_:1},8,["show"])],64)}}},E=n("Browser",{web:()=>s((()=>t.import("./web-legacy-96d28474.js")),void 0).then((e=>new e.BrowserWeb))}),P=["onClick"],A={key:1,class:"item-icon"},O={class:"item-name"},z=i({__name:"Menu",setup(e){const{proxy:t}=b(),a=y();let o=l();c((()=>{n()}));const n=async()=>{const e=await R({page:1,size:1});o.value=e.total},s=[{name:"待办任务",icon:"todo-list-o",path:"/Tasks"},{name:"视频监控",icon:"tv-o",path:"/Monitor/list"},{name:"质量检查",icon:"shield-o",path:"/QualityInspection"},{name:"质量整改",icon:"completed-o",path:"/QualityInspectionCorrection"},{name:"安全检查",icon:"records-o",path:"/SafetyCheck"},{name:"隐患管理",icon:"bulb-o",path:"/SafeInspectionHazard"},{name:"姚家平水文",svgIcon:"hydrology-1",path:"/YPJHydrology"},{name:"恩施水文",svgIcon:"hydrology-2",path:"/ESHydrology"},{name:"进度管理",icon:"underway-o",path:"/ProcessOverview"},{name:"人员管理",icon:"friends-o",path:"/PersonnelManagement",disabled:!0},{name:"环境监测",icon:"flower-o",path:"/CircumstancesDetection",disabled:!0},{name:"安全监测",icon:"aim",path:"/SafetyMonitoring",disabled:!0}],i=S((()=>{const e=[];for(let t=0;t<s.length;t+=12){const a=s.slice(t,t+12);e.push(a)}return e})),r=e=>{if(e.disabled)return void L({message:"功能开发中"});const o={"/YPJHydrology":{url:"https://www.esswj.cn/rq/yjpsk.aspx",title:"姚家平水文"},"/ESHydrology":{url:"https://www.esswj.cn/RQ/default.aspx",title:"恩施水文"}};o[e.path]?(async e=>{try{await E.open({url:e,toolbarColor:"#1989fa",presentationStyle:"fullscreen"})}catch(t){window.location.href=e}})(o[e.path].url):("/Tasks"===e.path&&t.$bizStore.saveData({tasksTabName:"MyTodo"}),"/DesignDispatch"===e.path&&t.$bizStore.saveData({designDispatchTabName:"00_01_01"}),a.push(e.path))};return(e,t)=>{const a=d("van-badge"),n=d("van-image"),s=d("van-icon"),l=d("van-swipe-item"),c=d("van-swipe");return h(),k(c,{class:"menu-swiper",autoplay:0,loop:!1},{default:f((()=>[(h(!0),p(g,null,x(i.value||[],((e,t)=>(h(),k(l,{key:t,class:"swiper-item"},{default:f((()=>[(h(!0),p(g,null,x(e||[],((e,i)=>{return h(),p("div",{key:`${t}_${i}`,class:T(["menu-item",{disabled:e.disabled}]),onClick:t=>r(e)},["待办任务"===e.name&&0!=m(o)?(h(),k(a,{key:0,class:"brage",content:m(o)},null,8,["content"])):_("",!0),e.svgIcon?(h(),p("div",A,[w(n,{src:(l=e.svgIcon,{"hydrology-1":"/app/assets/icon-svg-hydrology-1-36e46af0.svg","hydrology-2":"/app/assets/icon-svg-hydrology-2-10711c81.svg"}[l]),class:"svg-icon"},null,8,["src"])])):(h(),k(s,{key:2,class:"item-icon",name:e.icon||"apps-o"},null,8,["name"])),v("view",O,u(e.name),1)],10,P);var l})),128))])),_:2},1024)))),128))])),_:1})}}},[["__scopeId","data-v-15169a54"]]),M={name:"News",components:{NewsListItem:$},props:{hasNotice:Boolean},emits:["news-scroll"],setup(e,{attrs:t,slots:a,emit:o}){},data:()=>({loading:!1,list:[]}),computed:{portals(){return this.$store.PORTALS}},watch:{},created(){this.onRefresh()},mounted(){this.$refs.scrollContent&&(this.handleScroll=()=>{const e=this.$refs.scrollContent.scrollTop||0;this.$emit("news-scroll",e)},this.$refs.scrollContent.addEventListener("scroll",this.handleScroll),setTimeout((()=>{this.$emit("news-scroll",this.$refs.scrollContent.scrollTop||0)}),300))},beforeUnmount(){this.$refs.scrollContent&&this.handleScroll&&this.$refs.scrollContent.removeEventListener("scroll",this.handleScroll)},methods:{onRefresh(){this.getList()},async getList(){try{this.loading=!0;const e={page:1,size:10,name:"",portals:this.portals.map((e=>e.id)).join(",")},t=await I(e);this.list=(null==t?void 0:t.records)||[]}catch(e){console.log(e)}finally{this.loading=!1}},goNewsList(){this.$router.push({name:"NewsList"})}}},U={class:"home-news-block",ref:"newsBlock"},Q={class:"block-header"},B={key:0,class:"empty"};e("default",i({name:"Home",components:{PortalSelect:V,Menu:z,News:i(M,[["render",function(e,t,a,o,n,s){const i=d("van-icon"),r=d("NewsListItem");return h(),p("div",U,[v("div",Q,[t[2]||(t[2]=v("div",{class:"title"},"新闻资讯",-1)),v("div",{class:"more",onClick:t[0]||(t[0]=C((e=>s.goNewsList()),["stop","prevent"]))},[t[1]||(t[1]=v("span",null,"查看更多",-1)),w(i,{name:"arrow",color:"#9a9a9a"})])]),v("div",{class:T(["block-content",{"has-notice":a.hasNotice}]),ref:"scrollContent"},[n.list&&n.list.length?(h(!0),p(g,{key:0},x([...n.list],((e,t)=>(h(),k(r,{key:t,item:e,simple:""},null,8,["item"])))),128)):(h(),p(g,{key:1},[n.loading?_("",!0):(h(),p("div",B,"暂无新闻资讯"))],64))],2)],512)}],["__scopeId","data-v-b3007328"]]),QrScanner:i({name:"QrScanner",components:{},emits:[],props:{},setup(e,{attrs:t,slots:a,emit:o}){},data:()=>({}),computed:{},watch:{},created(){},mounted(){},methods:{goQrScanPage(){this.$router.push({name:"ScanPage"})}}},[["render",function(e,t,a,o,n,s){const i=d("van-icon");return h(),k(i,{size:"26",name:"scan",onClick:t[0]||(t[0]=C((e=>s.goQrScanPage()),["stop","prevent"]))})}]])},emits:[],props:{},setup(e,{attrs:t,slots:a,emit:o}){},data:()=>({refreshing:!1,refreshDisabled:!1,newsScrollTop:0,touchStartY:0,touchInMenuArea:!1}),computed:{protocol:()=>window.location.protocol,appVersion(){return this.$store.VERSION_INFO},showQrScanner(){if("web"==this.appVersion.platform)return!0;if(!this.appVersion.version)return!1;const e=r("1.2.0",this.appVersion.version);return-1==e||0==e}},watch:{newsScrollTop(e){this.updateRefreshDisabled()}},created(){},mounted(){document.addEventListener("touchstart",this.onTouchStart)},beforeUnmount(){document.removeEventListener("touchstart",this.onTouchStart)},methods:{onRefresh(){var e;null===(e=this.$refs)||void 0===e||e.news.onRefresh(),setTimeout((()=>{this.refreshing=!1,this.updateRefreshDisabled()}),300)},downloadApp(e){e.preventDefault();const{downloadUrl:t}=this.appVersion||{};t?N(t)?window.location.href=t+"?t="+Date.now():this.$showToast({message:"非HTTPS环境不支持安全下载"}):this.$showToast({message:"未获取到下载地址"})},onNewsScroll(e){this.newsScrollTop=e},onTouchStart(e){var t;this.touchStartY=e.touches[0].clientY;const a=null===(t=this.$refs.menu)||void 0===t?void 0:t.$el;if(a){const e=a.getBoundingClientRect();this.touchInMenuArea=this.touchStartY<=e.bottom}this.updateRefreshDisabled()},updateRefreshDisabled(){this.touchInMenuArea?this.refreshDisabled=!1:this.refreshDisabled=this.newsScrollTop>0}}},[["render",function(e,t,a,o,n,s){const i=d("PortalSelect"),r=d("Navbar"),l=d("van-notice-bar"),c=d("Menu"),u=d("News"),m=d("van-pull-refresh");return h(),p(g,null,[w(r,null,{left:f((()=>[w(i)])),title:f((()=>t[1]||(t[1]=[v("div",null,null,-1)]))),_:1}),s.appVersion.update?(h(),k(l,{key:0,"left-icon":"volume-o",text:s.appVersion.updateText||"当前App有新版本, 请重新下载更新",onClick:s.downloadApp},null,8,["text","onClick"])):_("",!0),v("div",{class:T(["content",{"has-notice":s.appVersion.update}])},[w(m,{modelValue:n.refreshing,"onUpdate:modelValue":t[0]||(t[0]=e=>n.refreshing=e),onRefresh:s.onRefresh,disabled:n.refreshDisabled},{default:f((()=>[w(c,{ref:"menu"},null,512),w(u,{ref:"news","has-notice":s.appVersion.update,onNewsScroll:s.onNewsScroll},null,8,["has-notice","onNewsScroll"])])),_:1},8,["modelValue","onRefresh","disabled"])],2)],64)}],["__scopeId","data-v-03f6acf9"]]))}}}));
