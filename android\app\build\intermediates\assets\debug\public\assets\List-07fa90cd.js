import{L as v,g as y}from"./api-dc8c064b.js";import{_ as k}from"./index-4829f8e2.js";import{Q as o,R as a,S as l,k as i,U as x,V as d,F as m,W as P,Z as V,X as b}from"./verder-361ae6c7.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";const C={name:"NewsList",components:{ListItem:v},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(s,{attrs:t,slots:h,emit:c}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:8,name:"",portals:""}}},computed:{portals(){return this.$store.PORTALS}},watch:{"searchParams.name"(s){s===""&&this.onSearch(s)}},created(){},mounted(){this.onLoadList()},methods:{onSearch(s){this.$nextTick(()=>{this.onRefresh()})},onCancel(){},onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const s={...this.searchParams,...this.search,portals:this.portals.map(c=>c.id).join(",")};s.page===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await y(s),h=this.searchParams.page<=1?[]:this.list||[];this.list=[...h,...t.records],this.searchParams.page++,this.list.length>=t.total&&(this.finished=!0)}catch(s){console.log(s),this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}}}},w={class:"view-height"},R={key:0,class:"p-[10px]"};function S(s,t,h,c,e,r){const p=o("Navbar"),f=o("van-search"),_=o("ListItem"),u=o("van-empty"),g=o("van-list"),L=o("van-pull-refresh");return a(),l(m,null,[i(p,{back:""}),i(f,{modelValue:e.searchParams.name,"onUpdate:modelValue":t[0]||(t[0]=n=>e.searchParams.name=n),placeholder:"搜索标题","show-action":!1,onSearch:r.onSearch,onCancel:r.onCancel},null,8,["modelValue","onSearch","onCancel"]),x("div",w,[i(L,{modelValue:e.refreshing,"onUpdate:modelValue":t[2]||(t[2]=n=>e.refreshing=n),onRefresh:r.onRefresh},{default:d(()=>[i(g,{loading:e.loading,"onUpdate:loading":t[1]||(t[1]=n=>e.loading=n),finished:e.finished,"finished-text":e.list&&e.list.length?"没有更多了":"",onLoad:r.onLoadList,"immediate-check":!1},{default:d(()=>[e.list&&e.list.length?(a(!0),l(m,{key:0},P(e.list||[],(n,N)=>(a(),b(_,{key:n.id,item:n},null,8,["item"]))),128)):(a(),l(m,{key:1},[e.loading?V("",!0):(a(),l("div",R,[i(u,{description:"暂无更多新闻"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])])],64)}const E=k(C,[["render",S],["__scopeId","data-v-56b394b4"]]);export{E as default};
