System.register(["./api-legacy-39a4c48a.js","./verder-legacy-e6127216.js","./index-legacy-09188690.js"],(function(e,t){"use strict";var i,s,a,n,o,l,c,r,d,m,p;return{setters:[e=>{i=e.d},e=>{s=e.Q,a=e.R,n=e.S,o=e.k,l=e.V,c=e.U,r=e.Y,d=e.B,m=e.a2},e=>{p=e._}],execute:function(){var t=document.createElement("style");t.textContent=".task-item[data-v-f11ec8ee]{background-color:#fff;border-radius:1.33333vw;padding:2.66667vw;margin-bottom:2.66667vw}.task-item .body[data-v-f11ec8ee]{padding:2.66667vw 1.33333vw 1.33333vw;position:relative}.task-item .body .item-info[data-v-f11ec8ee]{display:flex;flex-direction:row;align-items:center;font-size:3.73333vw;color:#9a9a9a;line-height:1;padding:1.33333vw 0}.task-item .body .item-info>.key[data-v-f11ec8ee]{min-width:5em}.task-item .body .item-info>.value[data-v-f11ec8ee]{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#333}.task-item .body .right[data-v-f11ec8ee]{position:absolute;top:1.33333vw;right:1.06667vw}.task-item .body .right .tag[data-v-f11ec8ee]{padding:1.6vw 3.2vw;text-align:center}.van-button--disabled[data-v-f11ec8ee]{background-color:#1d2129;border:none}\n",document.head.appendChild(t);const u={class:"body"},v={class:"item-info"},f={class:"value"},h={class:"item-info"},y={class:"value"},g={class:"item-info"},b={class:"value"},k={class:"item-info"},w={class:"value"},C={class:"item-info"},_={class:"value"},x={class:"item-info"},D={class:"value"},$={class:"right"};e("L",p({name:"ListItem",components:{},props:{item:{type:Object,default:()=>({})}},emits:[],setup(e,{attrs:t,slots:i,emit:s}){},data:()=>({}),watch:{},created(){},computed:{taskStateText(){return"0"===String(this.item.inspectionResult)?"正常":"有问题"},tagColor(){var e;return"0"===String(null===(e=this.item)||void 0===e?void 0:e.inspectionResult)?"#07c160":"#ff4d4f"},user(){return this.$store.USER_INFO}},mounted(){},methods:{handelDel(){this.$confirm({title:"提示",message:`确认删除${this.item.inspectionNumber}?`}).then((()=>{i(this.item).then((()=>{this.$emit("delSuccess")}))})).catch((()=>{}))},toFormCenter(){this.$store.QUALITY_INSPECTION={},this.$router.push({path:"/QualityInspectionDetail",query:{id:this.item.id,type:this.user.userName===this.item.createBy?"update":"detail",title:this.user.userName===this.item.createBy?"编辑质量检查":"质量检查详情"}})}}},[["render",function(e,t,i,p,I,N){const S=s("van-tag"),T=s("van-button"),j=s("van-swipe-cell");return a(),n("div",{class:"task-item",onClick:t[0]||(t[0]=m((e=>N.toFormCenter()),["stop","prevent"]))},[o(j,null,{right:l((()=>[o(T,{square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:N.user.userName!==i.item.createBy||!i.item.showDel,onClick:N.handelDel},null,8,["disabled","onClick"])])),default:l((()=>[c("div",u,[c("div",v,[t[1]||(t[1]=c("span",{class:"key"},"检查编号",-1)),c("span",f,r(i.item.inspectionNumber),1)]),c("div",h,[t[2]||(t[2]=c("span",{class:"key"},"检查类型",-1)),c("span",y,r(e.$formatLabel(i.item.inspectionType,e.$DICT_CODE.safe_inspection_type)),1)]),c("div",g,[t[3]||(t[3]=c("span",{class:"key"},"检查单位",-1)),c("span",b,r(i.item.inspectionUnitName),1)]),c("div",k,[t[4]||(t[4]=c("span",{class:"key"},"所属标段",-1)),c("span",w,r(e.$formatLabel(i.item.sectionId,e.$DICT_CODE.project_section)),1)]),c("div",C,[t[5]||(t[5]=c("span",{class:"key"},"详细区域",-1)),c("span",_,r(i.item.inspectionArea),1)]),c("div",x,[t[6]||(t[6]=c("span",{class:"key"},"检查日期",-1)),c("span",D,r(e.$dayjs(i.item.inspectionDate).format("YYYY-MM-DD")),1)]),c("div",$,[o(S,{class:"tag",color:N.tagColor,plain:"",size:"medium"},{default:l((()=>[d(r(N.taskStateText),1)])),_:1},8,["color"])])])])),_:1})])}],["__scopeId","data-v-f11ec8ee"]]))}}}));
