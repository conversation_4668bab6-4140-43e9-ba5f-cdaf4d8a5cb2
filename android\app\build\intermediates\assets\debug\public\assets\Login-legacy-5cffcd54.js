System.register(["./index-legacy-09188690.js","./verder-legacy-e6127216.js","./vant-legacy-b51a9379.js"],(function(e,a){"use strict";var t,o,n,i,r,l,c,s,d,p,m,u,g,h,v,f,b,w,x,y,_,k,F,S;return{setters:[e=>{t=e._,o=e.a,n=e.u,i=e.e,r=e.l,l=e.d,c=e.g},e=>{s=e.h,d=e.u,p=e.r,m=e.R,u=e.S,g=e.U,h=e.Y,v=e.Q,f=e.F,b=e.W,w=e.y,x=e.X,y=e.V,_=e.k,k=e.B,F=e.Z},e=>{S=e.s}],execute:function(){var a=document.createElement("style");a.textContent='@charset "UTF-8";.logo[data-v-340eaf45]{background-size:contain}.policy[data-v-806cbc49]{margin-top:1.2em;font-size:3.2vw;text-align:center}.policy>a[data-v-806cbc49]{color:var(--van-primary-color)}.isColor[data-v-806cbc49]{color:#4e5969;width:calc(50% - 40pt);text-align:center;border-bottom:2pt solid #C8E6FF}.Color[data-v-806cbc49]{color:var(--van-primary-color);border-bottom:2pt solid var(--van-button-primary-border-color);padding:0 2.66667vw;width:calc(50% - 40pt);text-align:center}[data-v-806cbc49] .van-field__left-icon{color:#fff}[data-v-806cbc49] .van-field{align-items:center;display:flex}.login-form-container[data-v-806cbc49]{width:100%;padding:4.26667vw;border:none}.login-form-container .remember-container[data-v-806cbc49]{padding:2.13333vw 4.26667vw;margin-top:1.33333vw}.login-form-container .button-container[data-v-806cbc49]{margin-top:60pt}.login-form-container .van-cell-group[data-v-806cbc49]{background-color:transparent;margin:0}.login-form-container[data-v-806cbc49] .van-form{margin-top:30pt}.login-form-container[data-v-806cbc49] .van-cell{background-color:transparent;padding-left:4.26667vw;padding-right:4.26667vw}.login-form-container[data-v-806cbc49] .van-field__control,.login-form-container[data-v-806cbc49] .van-checkbox__label{color:#333}.login-form-container[data-v-806cbc49] .van-field__label--top{color:#363636;font-weight:600}.page[data-v-2b2a843d]{background:url(/app/assets/bg_login-5c44b729.png) no-repeat center top,linear-gradient(to bottom,transparent 122.93333vw,#FFFFFF 122.93333vw);background-size:100% 122.93333vw,100% 100%;display:flex;flex-direction:column;align-items:center;justify-content:end;min-height:100vh;height:100%;--van-cell-group-background: transparent;--van-cell-background: transparent;--van-field-label-color: #333;--van-field-input-text-color: #333;--van-field-placeholder-text-color: #999}.login-container[data-v-2b2a843d]{display:flex;flex-direction:column;align-items:center;max-width:100vw;height:153.6vw;width:100%;padding:6.93333vw 0;box-sizing:border-box;border-radius:4.26667vw 4.26667vw 0 0;background:linear-gradient(180deg,rgba(255,255,255,.7) 2%,#FFFFFF 20%,#FFFFFF 100%);box-shadow:0 -1.6vw 2.13333vw rgba(9,86,91,.11)}@media (min-width: 768px) and (max-width: 1024px){.login-container[data-v-2b2a843d]{height:auto;min-height:500px;max-height:90vh;padding:30px 20px}}@media (width: 820px) and (height: 1180px){.login-container[data-v-2b2a843d]{height:auto;min-height:450px;max-height:650px}}@media (min-width: 1025px){.login-container[data-v-2b2a843d]{height:auto;min-height:500px;max-height:800px;padding:30px}}\n',document.head.appendChild(a),"undefined"!=typeof WorkerGlobalScope&&(globalThis,WorkerGlobalScope);const C=()=>{};function D(...e){let a,t,o,n,i,r,l=0,c=!0,p=C;s(e[0])||"object"!=typeof e[0]?[o,n=!0,i=!0,r=!1]=e:({delay:o,trailing:n=!0,leading:i=!0,rejectOnCancel:r=!1}=e[0]);const m=()=>{a&&(clearTimeout(a),a=void 0,p(),p=C)};return e=>{const s="function"==typeof(u=o)?u():d(u);var u;const g=Date.now()-l,h=()=>t=e();return m(),s<=0?(l=Date.now(),h()):(g>s&&(i||!c)?(l=Date.now(),h()):n&&(t=new Promise(((e,t)=>{p=r?t:e,a=setTimeout((()=>{l=Date.now(),c=!0,e(h()),m()}),Math.max(0,s-g))}))),i||a||(a=setTimeout((()=>c=!0),s)),c=!1,t)}}function I(e,a=200,t=!1,o=!0,n=!1){return function(e,a){return function(...t){return new Promise(((o,n)=>{Promise.resolve(e((()=>a.apply(this,t)),{fn:a,thisArg:this,args:t})).then(o).catch(n)}))}}(D(a,t,o,n),e)}const V={class:"flex flex-col items-center justify-center"},L={class:"font-600 font-[20px] text-[#1D2129]"},T=t({__name:"LoginLogo",setup(e){const a=o(),t=p(0),n=p(!1),i=I((()=>{t.value++,t.value>=5&&(t.value=0,n.value=!0)}),1e3);return(e,t)=>(m(),u("div",V,[g("img",{class:"logo mb-[18px] w-[48px] h-[48px]",onClick:t[0]||(t[0]=(...e)=>d(i)&&d(i)(...e)),src:"/app/assets/logo-3b0c2377.svg",alt:"Logo"}),g("div",L,"欢迎登录 ~ "+h(d(a).TITLE),1)]))}},[["__scopeId","data-v-340eaf45"]]),P={class:"w-[335px] rounded-[5px] px-[10px] py-[20px] pt-[40px] login-form-container"},j={class:"flex justify-around mt-[10px] h-[25px] text-[12px]"},A=["loading","onClick"],E={class:"button-container"},U={key:0,class:"word"},N={key:1,class:"word"},z={class:"button-container",style:{"margin-top":"20pt"}},q=t({name:"PassForm",components:{},props:{},emits:[],setup:(e,{attrs:a,slots:t,emit:o})=>({store:n()}),data:()=>({captcha_key:"",countDown:-2,smsLoading:!1,defaultIndex:0,form:{username:"",password:"",remember:!1,phone:"",captcha_code:""},loading:!1,showPassword:!1,loginList:["帐号登录","短信验证"]}),computed:{},watch:{},created(){},mounted(){this.loadAccount()},methods:{async onSubmit(e){try{let a;this.loading=!0,a=0==this.defaultIndex?{scope:"all",grant_type:"password",username:e.username,password:i(e.password)}:{scope:"all",grant_type:"sms_captcha",username:this.form.phone,captcha_code:this.form.captcha_code,captcha_key:this.captcha_key};const t=await r(a);this.store.$patch({TOKEN:t.access_token,USER_NAME:t.userName}),await this.store.INIT_DATA(),this.saveAccount(),window.ENV_FEISHU?this.$router.replace({name:"Tasks"}):this.$router.replace({name:"Home"})}catch(a){console.log(a)}finally{this.loading=!1}},saveAccount(){this.form.remember?localStorage.setItem("remember_info",JSON.stringify({...this.form,password:i(this.form.password)})):localStorage.removeItem("remember_info")},loadAccount(){try{const e=JSON.parse(localStorage.getItem("remember_info")),{username:a,password:t,remember:o}=e||{};o&&(this.form.remember=!0,this.form.username=a,this.form.password=l(t))}catch(e){console.log(e)}},changeVissible(){this.showPassword=!this.showPassword},cutLogin(e){this.defaultIndex=e},async getSMSCaptcha(){if(""!==this.form.phone){this.smsLoading=!0;try{const e=await c({phone:this.form.phone});e&&(this.captcha_key=e,this.updataCountDown())}catch(e){S("手机号错误或不存在!"),this.smsLoading=!1}}else S("请输入手机号码!")},updataCountDown(){this.countDown=60;const e=setInterval((()=>{0==this.countDown?(clearInterval(e),this.smsLoading=!1,this.countDown=-1):this.countDown--}),1e3)}}},[["render",function(e,a,t,o,n,i){const r=v("van-field"),l=v("van-cell-group"),c=v("van-button"),s=v("van-form");return m(),u("div",P,[g("div",j,[(m(!0),u(f,null,b(n.loginList,((e,a)=>(m(),u("div",{key:a,class:w(n.defaultIndex===a?"Color":"isColor"),loading:n.loading,"native-type":"submit",onClick:e=>i.cutLogin(a)},h(e),11,A)))),128))]),0===n.defaultIndex?(m(),x(s,{key:0,onSubmit:i.onSubmit,"label-align":"top"},{default:y((()=>[_(l,{inset:""},{default:y((()=>[_(r,{modelValue:n.form.username,"onUpdate:modelValue":a[0]||(a[0]=e=>n.form.username=e),name:"username",label:"用户名",placeholder:"请输入您的用户名",rules:[{required:!0,message:"请输入用户名"}]},null,8,["modelValue"]),_(r,{modelValue:n.form.password,"onUpdate:modelValue":a[1]||(a[1]=e=>n.form.password=e),type:n.showPassword?"text":"password",name:"password",label:"密码",placeholder:"请输入您的密码","right-icon":n.showPassword?"eye-o":"closed-eye",onClickRightIcon:i.changeVissible,rules:[{required:!0,message:"请输入密码"}]},null,8,["modelValue","type","right-icon","onClickRightIcon"]),_(r,{style:{height:"0",padding:"0 !important"}})])),_:1}),g("div",E,[_(c,{block:"",type:"primary",loading:n.loading,"native-type":"submit"},{default:y((()=>a[4]||(a[4]=[k(" 登 录 ")]))),_:1,__:[4]},8,["loading"])])])),_:1},8,["onSubmit"])):(m(),x(s,{key:1,onSubmit:i.onSubmit,"label-align":"top"},{default:y((()=>[_(l,{inset:""},{default:y((()=>[_(r,{modelValue:n.form.phone,"onUpdate:modelValue":a[2]||(a[2]=e=>n.form.phone=e),label:"手机号",placeholder:"请输入您的手机号",rules:[{required:!0,message:"请输入您的手机号"}]},null,8,["modelValue"]),_(r,{modelValue:n.form.captcha_code,"onUpdate:modelValue":a[3]||(a[3]=e=>n.form.captcha_code=e),label:"验证码",placeholder:"请输入您的验证码",rules:[{required:!0,message:"请输入您的验证码"}]},{button:y((()=>[_(c,{size:"mini",disabled:n.smsLoading,type:"primary",onClick:i.getSMSCaptcha},{default:y((()=>[-1!=n.countDown&&-2!=n.countDown?(m(),u("span",U,h(n.countDown+"s"),1)):F("",!0),-1!=n.countDown||-2!=n.countDown?(m(),u("span",N,h(-2==n.countDown?"获取验证码":"重新获取"),1)):F("",!0)])),_:1},8,["disabled","onClick"])])),_:1},8,["modelValue"]),_(r,{readonly:""})])),_:1}),g("div",z,[_(c,{block:"",type:"primary",loading:n.loading,"native-type":"submit"},{default:y((()=>a[5]||(a[5]=[k(" 登 录 ")]))),_:1,__:[5]},8,["loading"])])])),_:1},8,["onSubmit"])),a[6]||(a[6]=g("div",{class:"policy"},[g("a",{href:"/app/privacy-policy.html"},"《姚家平隐私协议》")],-1))])}],["__scopeId","data-v-806cbc49"]]),M={class:"page min-h-screen"},O={class:"login-container"};e("default",t({__name:"Login",setup:e=>(e,a)=>(m(),u("div",M,[g("div",O,[_(T),_(q)])]))},[["__scopeId","data-v-2b2a843d"]]))}}}));
