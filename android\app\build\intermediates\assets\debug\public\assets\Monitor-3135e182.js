import{Q as t,R as o,S as m,k as i,V as a,F as _,W as f,a2 as C,X as c,U as w,_ as M}from"./verder-361ae6c7.js";import{_ as N}from"./index-4829f8e2.js";import"./vant-91101745.js";const g={name:"Monitor",components:{},props:{},emits:[],setup(r,{attrs:s,slots:p,emit:u}){},data(){return{tabName:"MonitorList",tabs:[{title:"监控列表",name:"MonitorList"},{title:"我的收藏",name:"MonitorFavorites"}],key:Date.now()}},computed:{},watch:{},created(){this.tabName=this.$route.name},mounted(){},methods:{onClickTab(r){this.$router.replace({name:r.name})},onClickSearch(){this.$router.push({name:"MonitorSearch"})}}},x={class:"view-height"};function y(r,s,p,u,n,l){const v=t("van-tab"),d=t("van-tabs"),b=t("van-icon"),k=t("Navbar"),h=t("router-view");return o(),m(_,null,[i(k,{back:""},{title:a(()=>[i(d,{active:n.tabName,"onUpdate:active":s[0]||(s[0]=e=>n.tabName=e),"line-width":"4em",background:"none",color:"#fff","title-inactive-color":"rgba(255,255,255, .7)","title-active-color":"rgba(255,255,255,1)",onClickTab:l.onClickTab},{default:a(()=>[(o(!0),m(_,null,f(n.tabs,e=>(o(),c(v,{class:"px-[20px]",key:e.name,name:e.name,title:e.title},null,8,["name","title"]))),128))]),_:1},8,["active","onClickTab"])]),right:a(()=>[i(b,{name:"search",onClick:C(l.onClickSearch,["stop","prevent"]),size:"24"},null,8,["onClick"])]),_:1}),(o(),c(h,{key:n.key},{default:a(({Component:e})=>[w("div",x,[(o(),c(M(e)))])]),_:1}))],64)}const F=N(g,[["render",y],["__scopeId","data-v-47af711d"]]);export{F as default};
