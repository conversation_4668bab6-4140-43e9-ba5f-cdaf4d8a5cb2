System.register(["./verder-legacy-e6127216.js","./index-legacy-09188690.js","./vant-legacy-b51a9379.js"],(function(e,t){"use strict";var a,n,o,i,r,l,c,s,m,v,d,u;return{setters:[e=>{a=e.Q,n=e.R,o=e.S,i=e.k,r=e.V,l=e.F,c=e.W,s=e.a2,m=e.X,v=e.U,d=e._},e=>{u=e._},null],execute:function(){var t=document.createElement("style");t.textContent=".view-height[data-v-47af711d]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat));overflow-x:hidden;overflow-y:scroll}\n",document.head.appendChild(t);const h={class:"view-height"};e("default",u({name:"Monitor",components:{},props:{},emits:[],setup(e,{attrs:t,slots:a,emit:n}){},data:()=>({tabName:"MonitorList",tabs:[{title:"监控列表",name:"MonitorList"},{title:"我的收藏",name:"MonitorFavorites"}],key:Date.now()}),computed:{},watch:{},created(){this.tabName=this.$route.name},mounted(){},methods:{onClickTab(e){this.$router.replace({name:e.name})},onClickSearch(){this.$router.push({name:"MonitorSearch"})}}},[["render",function(e,t,u,b,p,k){const f=a("van-tab"),g=a("van-tabs"),C=a("van-icon"),y=a("Navbar"),w=a("router-view");return n(),o(l,null,[i(y,{back:""},{title:r((()=>[i(g,{active:p.tabName,"onUpdate:active":t[0]||(t[0]=e=>p.tabName=e),"line-width":"4em",background:"none",color:"#fff","title-inactive-color":"rgba(255,255,255, .7)","title-active-color":"rgba(255,255,255,1)",onClickTab:k.onClickTab},{default:r((()=>[(n(!0),o(l,null,c(p.tabs,(e=>(n(),m(f,{class:"px-[20px]",key:e.name,name:e.name,title:e.title},null,8,["name","title"])))),128))])),_:1},8,["active","onClickTab"])])),right:r((()=>[i(C,{name:"search",onClick:s(k.onClickSearch,["stop","prevent"]),size:"24"},null,8,["onClick"])])),_:1}),(n(),m(w,{key:p.key},{default:r((({Component:e})=>[v("div",h,[(n(),m(d(e)))])])),_:1}))],64)}],["__scopeId","data-v-47af711d"]]))}}}));
