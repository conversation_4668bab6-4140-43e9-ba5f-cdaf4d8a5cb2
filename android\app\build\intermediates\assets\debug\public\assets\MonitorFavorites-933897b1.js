import{g as V}from"./api-c1f17e6c.js";import{M as F}from"./MonitorItem-2ff5bad9.js";import{_ as b,u as w}from"./index-4829f8e2.js";import{Q as n,R as o,X as m,V as a,k as c,S as l,W as v,F as d,Z as L,U as y,Y as S}from"./verder-361ae6c7.js";import"./file-2bef16be.js";import"./vant-91101745.js";const B=w(),A={name:"MonitorFavorites",components:{MonitorItem:F},props:{},emits:[],setup(i,{attrs:t,slots:p,emit:h}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1}},computed:{portalId(){return this.$store.PORTAL_ID}},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1,this.list=[];const i={collectStatus:1,isFilterHidden:1};let t=B.PORTALS.filter(e=>e.type!=1);console.log("this.portals ",t);const p=await V(i);let h=[];for(let e=0;e<t.length;e++){let s=t[e];s.index=e,s.devices=p.records.filter(f=>f.portalId==s.id)}this.list=[...t],this.finished=!0}catch(i){console.log(i),this.finished=!0,this.list=[]}finally{this.loading=!1}},afterCollect(i){this.onLoadList()}}},D={class:"group-item"},N={key:0,class:"p-[10px]"};function U(i,t,p,h,e,s){const f=n("van-icon"),u=n("van-col"),x=n("MonitorItem"),k=n("van-row"),C=n("van-empty"),I=n("van-list"),M=n("van-pull-refresh");return o(),m(M,{modelValue:e.refreshing,"onUpdate:modelValue":t[1]||(t[1]=r=>e.refreshing=r),onRefresh:s.onRefresh},{default:a(()=>[c(I,{loading:e.loading,"onUpdate:loading":t[0]||(t[0]=r=>e.loading=r),finished:e.finished,"finished-text":e.list&&e.list.length?"没有更多了":"",onLoad:s.onLoadList,"immediate-check":!1},{default:a(()=>[e.list&&e.list.length?(o(),m(k,{key:0,class:"p-[10px]"},{default:a(()=>[(o(!0),l(d,null,v(e.list||[],({id:r,ext2:R,devices:_},O)=>(o(),l(d,{key:r},[_&&_.length?(o(),l(d,{key:0},[c(u,{span:"24"},{default:a(()=>[y("div",D,[c(f,{class:"icon",name:"label"}),y("span",null,S(R),1)])]),_:2},1024),(o(!0),l(d,null,v(_||[],g=>(o(),m(u,{class:"col-item",key:g.id,span:"24"},{default:a(()=>[c(x,{item:g,class:"!h-[180px]",onCollect:s.afterCollect},null,8,["item","onCollect"])]),_:2},1024))),128))],64)):L("",!0)],64))),128))]),_:1})):(o(),l(d,{key:1},[e.loading?L("",!0):(o(),l("div",N,[c(C,{description:"暂无收藏设备"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])}const X=b(A,[["render",U],["__scopeId","data-v-57debfb7"]]);export{X as default};
