import{d as h,e as f}from"./api-c1f17e6c.js";import{g as p,d as v}from"./file-2bef16be.js";import{Q as _,R as l,S as c,U as o,a2 as r,k as d,Y as g}from"./verder-361ae6c7.js";import{_ as y}from"./index-4829f8e2.js";import{a as U}from"./vant-91101745.js";const k={name:"MonitorItem",components:{},props:{item:{type:Object,default:()=>({})}},emits:["collect"],setup(e,{attrs:t,slots:s,emit:i}){},data(){return{loading:!1,defaultUrl:"https://statics.ys7.com/device/assets/imgs/public/homeDevice.jpeg",iconUrl:"https://statics.ys7.com/device/assets/imgs/public/homeDevice.jpeg"}},computed:{collectStatus(){return this.item.collectStatus},async cover(){}},watch:{"item.icon":{handler(e){e?(this.iconUrl=this.defaultUrl,p({g9s:[e]}).then(t=>{t.length>0&&v({fileToken:t[0].fileToken,width:127,height:86}).then(s=>{if(s.status===200){const i=URL.createObjectURL(new Blob([s.data]));this.iconUrl=i}})})):this.iconUrl=this.defaultUrl},immediate:!0,deep:!0}},created(){},mounted(){},methods:{async toggleFav(){try{if(this.loading===!0)return;this.loading=!0,this.collectStatus=="0"?(await h({deviceId:this.item.id}),this.item.collectStatus="1"):(await f({deviceId:this.item.id}),this.item.collectStatus="0"),U({message:this.collectStatus=="0"?"已收藏":"已取消收藏",forbidClick:!0,onOpened:()=>{},onClose:()=>{this.$emit("collect",this.collectStatus)}})}catch(e){console.log(e)}finally{this.loading=!1}},goDetail(){this.$router.push({name:"MonitorDetail",query:{id:this.item.id}})}}},S={class:"monitor-item"},b={class:"content"},w={key:0,class:"offline"},C=["src"],I={class:"name-bar"};function D(e,t,s,i,m,a){const n=_("van-icon");return l(),c("div",S,[o("div",b,[s.item.status=="0"?(l(),c("div",w,t[2]||(t[2]=[o("div",{class:"text"},"离线",-1)]))):(l(),c("div",{key:1,class:"online",onClick:t[0]||(t[0]=r(u=>a.goDetail(),["stop","prevent"]))},[o("img",{class:"cover",src:m.iconUrl,alt:"图片"},null,8,C),d(n,{class:"icon",name:"play-circle"})]))]),o("div",I,g(s.item.name),1),o("div",{class:"fav-tag",onClick:t[1]||(t[1]=r(u=>a.toggleFav(),["stop","prevent"]))},[d(n,{class:"icon",name:"star",color:a.collectStatus=="0"?"#f5c278":"#efefef"},null,8,["color"])])])}const F=y(k,[["render",D],["__scopeId","data-v-8a10495f"]]);export{F as M};
