import{g as b,t as M,a as C}from"./api-c1f17e6c.js";import{M as S}from"./MonitorItem-2ff5bad9.js";import{_ as I,u as V}from"./index-4829f8e2.js";import{Q as l,R as n,X as u,V as g,S as r,F as v,W as P,k as x,Z as k,U as _,y as z,Y as y}from"./verder-361ae6c7.js";import"./file-2bef16be.js";import"./vant-91101745.js";const D={name:"MonitorListList",components:{MonitorItem:S},props:{parentParams:{type:Object,default:()=>({})}},emits:[],setup(e,{attrs:s,slots:a,emit:p}){},data(){return{loading:!1,finished:!1,list:[],searchParams:{page:1,size:20}}},computed:{},watch:{parentParams:{deep:!0,handler(e){this.reload()}}},created(){},mounted(){this.reload()},methods:{async onLoadList(){try{this.loading=!0;const e={...this.searchParams,...this.parentParams,isFilterHidden:1},s=await b(e),a=s.records;this.list=[...this.list,...a],this.searchParams.currentPage++,this.list.length>=s.total&&(this.finished=!0)}catch(e){console.log(e),this.finished=!0,this.list=[],this.searchParams.currentPage=1}finally{this.loading=!1}},reload(){this.finished=!1,this.searchParams={currentPage:1,pageSize:20},this.list=[],this.onLoadList()}}},R={key:0,class:"p-[10px]"};function B(e,s,a,p,t,c){const d=l("MonitorItem"),m=l("van-col"),h=l("van-row"),f=l("van-empty"),o=l("van-list");return n(),u(o,{loading:t.loading,"onUpdate:loading":s[0]||(s[0]=i=>t.loading=i),finished:t.finished,"finished-text":t.list&&t.list.length?"没有更多了":"",onLoad:c.onLoadList,"immediate-check":!1},{default:g(()=>[t.list&&t.list.length?(n(),u(h,{key:0,class:"p-[10px] pr-[5px]"},{default:g(()=>[(n(!0),r(v,null,P([...t.list],(i,w)=>(n(),u(m,{class:"col-item",key:w,span:"12"},{default:g(()=>[x(d,{item:i},null,8,["item"])]),_:2},1024))),128))]),_:1})):(n(),r(v,{key:1},[t.loading?k("",!0):(n(),r("div",R,[x(f,{image:"search",description:"暂无监控设备"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])}const F=I(D,[["render",B],["__scopeId","data-v-816f1d34"]]);const L=V(),T={name:"MonitorList",components:{MonitorListList:F},props:{},emits:[],setup(e,{attrs:s,slots:a,emit:p}){},data(){return{loading:!1,refreshing:!1,portalId:"",portals:[],sideIndex:-1}},computed:{},watch:{},created(){},mounted(){this.getSideList()},beforeRouteLeave(e,s,a){(e.name==="MonitorDetail"||e.name==="MonitorFavorites")&&this.$bizStore.saveData({monitorPageParams:{portalId:this.portalId,sideIndex:this.sideIndex}}),a()},methods:{onRefresh(){this.refreshing=!1,this.getSideList()},async getSideList(){var e,s,a,p;try{if(this.loading)return;this.loading=!0;let t=L.PORTALS.filter(o=>o.type!=1);console.log("this.portals ",this.portals);const c=await M(),d=await C();if(d)for(let o=0;o<t.length;o++){let i=t[o];i.onlineTotal=d[i.id],i.total=d[i.id]}if(c)for(let o=0;o<t.length;o++){let i=t[o];i.total=c[i.id]}console.log("resall",c,"reslive",d,t,L.PORTALS),this.portals=t;const m=((s=(e=this.$bizStore)==null?void 0:e.tempData)==null?void 0:s.monitorPageParams)||{},{portalId:h,sideIndex:f}=m;if(h){const o=this.portals.find(i=>i.id===h);this.handlePortalIdChange(o.id,f)}else this.handlePortalIdChange((p=(a=this.portals)==null?void 0:a[0])==null?void 0:p.id)}catch(t){console.log(t)}finally{this.loading=!1}},handlePortalIdChange(e,s){console.log(s),this.portals.find(a=>a.id==e),this.portalId=e,this.sideIndex=s},onSidebarChange(e){const s=this.portals[e];this.portalId=s.id}}},A={key:1,class:"new-content"},N={key:0,class:"flex flex-row h-[100%]"},O={class:"!w-[85px] flex-shrink-0"},U={class:"w-[85px] new-left-bar bg-[#fff] overflow-auto"},j={class:"flex flex-col items-center justify-center text-[12px] text-center"},E={class:"leading-[1.3]"},H={class:"flex-1"},Q={key:1,class:"p-[10px]"};function W(e,s,a,p,t,c){const d=l("van-sidebar-item"),m=l("van-sidebar"),h=l("MonitorListList"),f=l("van-empty");return n(),r("div",{class:z(["new-list-layout",{"no-dorp-item":!!t.portalId}])},[t.loading?(n(),r(v,{key:0},[],64)):(n(),r("div",A,[t.portals&&t.portals.length?(n(),r("div",N,[_("div",O,[_("div",U,[x(m,{class:"w-full",modelValue:t.sideIndex,"onUpdate:modelValue":s[0]||(s[0]=o=>t.sideIndex=o),onChange:c.onSidebarChange},{default:g(()=>[(n(!0),r(v,null,P([...t.portals],(o,i)=>(n(),u(d,{key:i},{title:g(()=>[_("div",j,[_("div",E,y(o.ext2),1),_("div",null,"("+y(o.onlineTotal||0)+"/"+y(o.total||0)+")",1)])]),_:2},1024))),128))]),_:1},8,["modelValue","onChange"])])]),_("div",H,[t.portalId?(n(),u(h,{key:0,"parent-params":{portalId:t.portalId}},null,8,["parent-params"])):k("",!0)])])):(n(),r("div",Q,[x(f,{image:"search",description:"暂无监控设备"})]))]))],2)}const K=I(T,[["render",W],["__scopeId","data-v-884b30ec"]]);export{K as default};
