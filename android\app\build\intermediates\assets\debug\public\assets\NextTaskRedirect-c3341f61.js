import{_ as a}from"./index-4829f8e2.js";import{S as o,R as c}from"./verder-361ae6c7.js";import"./vant-91101745.js";const p={name:"NextTaskRedirect",components:{},emits:[],props:{},setup(t,{attrs:e,slots:r,emit:s}){},data(){return{}},computed:{},watch:{},created(){},mounted(){var e,r;const t=(r=(e=this.$bizStore)==null?void 0:e.tempData)==null?void 0:r.redirectParams;!t||!t.path?this.$router.replace({path:"/Tasks"}):this.$router.replace({...t})},methods:{}};function i(t,e,r,s,m,n){return c(),o("div")}const f=a(p,[["render",i]]);export{f as default};
