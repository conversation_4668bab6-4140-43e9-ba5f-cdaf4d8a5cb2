System.register(["./index-legacy-09188690.js","./verder-legacy-e6127216.js","./vant-legacy-b51a9379.js"],(function(e,l){"use strict";var a,t,n,r,i,u,s,v,o,c,d,p,m,x,f,y,_;return{setters:[e=>{a=e.u,t=e.s,n=e.i},e=>{r=e.O,i=e.c,u=e.Q,s=e.R,v=e.S,o=e.k,c=e.U,d=e.F,p=e.Y,m=e.V,x=e.B,f=e.X,y=e.Z},e=>{_=e.e}],execute:function(){const l={class:"flex flex-row items-center pt-[10px] px-[20px] pb-[30px] bg-[var(--nav-bar-bg-color)] text-[var(--nav-bar-text-color)]"},g={class:"flex-shrink-0 w-[60px] h-[60px] flex items-center justify-center bg-violet-400 rounded-[50%] mr-[20px]"},b={class:"text-[14px]"},k={class:"mb-[8px]"},L={key:1};e("default",{__name:"Personal",setup(e){const A=r(),C=a(),R=t(),h=i((()=>C.PORTAL)),I=i((()=>C.USER_INFO)),N=i((()=>{var e,l;return(null===(e=I.value)||void 0===e||null===(e=e.orgList)||void 0===e?void 0:e.find((e=>{var l;return e.portalId==(null===(l=h.value)||void 0===l?void 0:l.id)})))||(null===(l=I.value)||void 0===l?void 0:l.orgList[0])||{}}));null==N||N.name,null==N||N.orgNo;const O=i((()=>C.VERSION_INFO)),j=()=>{n().then((()=>{C.CLEAR_DATA(),R.CLEAR_BIZ(),A.replace({name:"Login"})}))},E=()=>{_({title:"提示",message:"是否确认退出登录？",beforeClose:async e=>{try{return"confirm"===e?(j(),Promise.resolve(!0)):Promise.resolve(!0)}catch(l){}}})};return(e,a)=>{const t=u("van-nav-bar"),n=u("van-icon"),r=u("van-tag"),i=u("van-cell"),_=u("van-cell-group"),A=u("van-button"),C=u("van-space");return s(),v(d,null,[o(t,{"safe-area-inset-top":"",border:!1,title:"个人信息","left-arrow":"",onClickLeft:a[0]||(a[0]=()=>e.$router.replace({name:"Home"}))}),c("div",l,[c("div",g,[o(n,{name:"contact",size:"2.4em"})]),c("div",b,[I.value&&I.value.id?(s(),v(d,{key:0},[c("div",k,p(I.value.userFullname),1),c("div",null,[o(r,{type:"success",size:"medium"},{default:m((()=>[x(p(N.value.name||"无部门"),1)])),_:1})])],64)):(s(),v("div",L,"未登录"))])]),I.value&&I.value.id?(s(),v(d,{key:0},[o(_,{class:"mt-[10px]"},{default:m((()=>[o(i,{title:"我的消息","is-link":"",to:"/message/list"})])),_:1}),o(_,{class:"mt-[10px]"},{default:m((()=>[o(i,{title:"用户名",value:I.value.userName},null,8,["value"]),o(i,{title:"手机号",value:I.value.phone},null,8,["value"]),o(i,{title:"邮箱",value:I.value.email},null,8,["value"]),O.value.version?(s(),f(i,{key:0,title:"App版本",value:O.value.version},null,8,["value"])):y("",!0)])),_:1}),o(C,{direction:"vertical",fill:"",class:"mt-[40px] px-[12px]"},{default:m((()=>[o(A,{block:"",type:"primary",onClick:E},{default:m((()=>a[1]||(a[1]=[x("退出登录")]))),_:1,__:[1]})])),_:1})],64)):(s(),f(C,{key:1,direction:"vertical",fill:"",class:"mt-[40px] px-[12px]"},{default:m((()=>[o(A,{block:"",type:"primary",onClick:j},{default:m((()=>a[2]||(a[2]=[x("登录")]))),_:1,__:[2]})])),_:1}))],64)}}})}}}));
