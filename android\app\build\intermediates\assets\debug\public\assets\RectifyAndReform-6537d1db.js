import{F as C}from"./FormItemDate-ba00d9d5.js";import{_ as U}from"./FormItemSection-e3118d02.js";import{F as P}from"./FormItemCalendar-905fde75.js";import{U as w}from"./index-fc22947f.js";import{F as R}from"./FormItemCoord-9e82e1bf.js";import{F as T}from"./FormItemCascader-c665b251.js";import{F as q}from"./FormItemPicker-d3f69283.js";import{g as V}from"./wbsUtil-3e809cfd.js";import{F as A}from"./FormItemPerson-bd0e3e57.js";import{R as O}from"./constants-94a272fa.js";import{Q as s,R as u,S as d,k as o,U as x,V as i,B as p,a2 as f,Z as S,F as k}from"./verder-361ae6c7.js";import{_ as E}from"./index-4829f8e2.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./validate-2249584f.js";import"./array-15ef8611.js";const L={name:"HiddenTroubleInfo",components:{FormItemDate:C,FormItemSection:U,FormItemPerson:A,FormItemPicker:q,FormItemCascader:T,FormItemCoord:R,UploadFiles:w,FormItemCalendar:P},props:{title:{type:String,default:""}},emits:[],setup(a,{attrs:e,slots:c,emit:h}){},data(){var a;return{taskKey:((a=this.$route.query)==null?void 0:a.taskKey)||"",formData:{},wbsList:[],statusMap:O}},computed:{navbarTitle(){return this.$route.query.title||"新增整改"},navbarType(){return this.$route.query.type},portal(){return this.$store.PORTAL},user(){return this.$store.USER_INFO},toStartReviewStatus(){var a;return(a=this.statusMap)==null?void 0:a.PENDING_REVIEW}},methods:{handleClose(){this.$store.rectify_AND_REFORM={},this.$router.back()},async handleAddOrCreate(){try{await this.$refs.form.validate(),this.formData.constructionPost=!this.formData.lng||!this.formData.lat?"":[this.formData.lng,this.formData.lat].join(","),this.formData.hasOwnProperty("index")&&this.$store.QUALITY_INSPECTION.hasOwnProperty("corrections")?this.$store.QUALITY_INSPECTION.corrections[this.formData.index]=this.formData:!this.formData.hasOwnProperty("index")&&this.$store.QUALITY_INSPECTION.hasOwnProperty("corrections")&&this.$store.QUALITY_INSPECTION.corrections.push(this.formData),this.$store.rectify_AND_REFORM={},this.$router.back()}catch(a){}},async getWbsList(){this.wbsList=await V(this.formData.sectionId,!0,this.portal),this.$nextTick(()=>{this.$refs.formItemCascaderRef.chengeLabel()})},handlePersonChange(a){}},mounted(){if(Object.keys(this.$store.rectify_AND_REFORM).length){if(this.formData.constructionPost){let a=this.formData.constructionPost.split(",");this.formData.lng=a[0],this.formData.lat=a[1]}this.formData=this.$store.rectify_AND_REFORM}else this.formData={sectionId:this.$route.query.sectionId,correctionNumber:"",projectPosition:"",constructionArea:"",deadline:"",description:"",requirement:"",fileUpload:"",measure:"",situation:"",measureDate:"",initiator:"",processName:"",processCode:"",measureFileUpload:"",isPass:"",positionInfo:null,lng:null,lat:null,height:null,rectificationStatus:this.toStartReviewStatus};this.getWbsList()}},M={class:"view-height btn-bom"},j={key:0,class:"btn-group"};function Q(a,e,c,h,t,l){const g=s("Navbar"),n=s("van-field"),D=s("FormItemSection"),_=s("FormItemCascader"),I=s("FormItemDate"),b=s("UploadFiles"),F=s("FormItemCoord"),y=s("FormItemPerson"),v=s("van-cell-group"),N=s("van-form"),m=s("van-button");return u(),d(k,null,[o(g,{back:"",title:l.navbarTitle},null,8,["title"]),x("div",M,[o(N,{ref:"form","label-width":"9em","input-align":"right","error-message-align":"right"},{default:i(()=>[o(v,{border:!1},{default:i(()=>[o(n,{modelValue:t.formData.correctionNumber,"onUpdate:modelValue":e[0]||(e[0]=r=>t.formData.correctionNumber=r),label:"整改单号",placeholder:"自动生成",readonly:""},null,8,["modelValue"]),o(D,{label:"所属标段",placeholder:"请选择",modelValue:t.formData.sectionId,"onUpdate:modelValue":e[1]||(e[1]=r=>t.formData.sectionId=r),readonly:"",required:"",rules:[{required:!0,message:"请选择所属标段"}]},null,8,["modelValue"]),o(_,{ref:"formItemCascaderRef",label:"整改部位",value:t.formData.projectPosition,"onUpdate:value":e[2]||(e[2]=r=>t.formData.projectPosition=r),columns:[...t.wbsList],"columns-field-names":{text:"nodeName",value:"id",children:"children"},"trigger-change-mode":!0,placeholder:"请选择",required:"",rules:[{required:!0,message:"请选择整改部位"}]},null,8,["value","columns"]),o(I,{label:"整改期限",value:t.formData.deadline,"onUpdate:value":e[3]||(e[3]=r=>t.formData.deadline=r),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择整改期限"}]},null,8,["value"]),o(n,{label:"详细区域",modelValue:t.formData.constructionArea,"onUpdate:modelValue":e[4]||(e[4]=r=>t.formData.constructionArea=r),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"]),o(n,{label:"整改内容",modelValue:t.formData.description,"onUpdate:modelValue":e[5]||(e[5]=r=>t.formData.description=r),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改内容"}],"input-align":"left"},null,8,["modelValue"]),o(n,{label:"整改要求及建议",modelValue:t.formData.requirement,"onUpdate:modelValue":e[6]||(e[6]=r=>t.formData.requirement=r),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改要求及建议"}],"input-align":"left"},null,8,["modelValue"]),o(n,{label:"附件上传","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传资料附件"}]},{input:i(()=>[o(b,{ref:"fileUpload",g9s:t.formData.fileUpload,"onUpdate:g9s":e[7]||(e[7]=r=>t.formData.fileUpload=r),accept:"*",multiple:!0},null,8,["g9s"])]),_:1}),o(F,{label:"经纬度",longitude:t.formData.lng,"onUpdate:longitude":e[8]||(e[8]=r=>t.formData.lng=r),latitude:t.formData.lat,"onUpdate:latitude":e[9]||(e[9]=r=>t.formData.lat=r),title:"选择定位"},null,8,["longitude","latitude"]),o(y,{required:"",label:"问题分发人",title:"问题分发人",userFullname:t.formData.acceptancePersonName,"onUpdate:userFullname":e[10]||(e[10]=r=>t.formData.acceptancePersonName=r),userName:t.formData.acceptancePerson,"onUpdate:userName":e[11]||(e[11]=r=>t.formData.acceptancePerson=r),onChange:l.handlePersonChange,rules:[{required:!0,message:"请选择问题分发人"}]},null,8,["userFullname","userName","onChange"])]),_:1})]),_:1},512),["add","update"].includes(l.navbarType)?(u(),d("div",j,[o(m,{round:"",type:"danger",plain:"",onClick:f(l.handleClose,["stop","prevent"])},{default:i(()=>e[12]||(e[12]=[p("取消")])),_:1,__:[12]},8,["onClick"]),o(m,{round:"",type:"primary",plain:"",onClick:f(l.handleAddOrCreate,["stop","prevent"])},{default:i(()=>e[13]||(e[13]=[p("保存")])),_:1,__:[13]},8,["onClick"])])):S("",!0)])],64)}const ie=E(L,[["render",Q],["__scopeId","data-v-297ec037"]]);export{ie as default};
