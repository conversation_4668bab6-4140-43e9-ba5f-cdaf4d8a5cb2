System.register(["./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./verder-legacy-e6127216.js","./index-legacy-09188690.js","./vant-legacy-b51a9379.js"],(function(e,t){"use strict";var a,s,i,n,o,d,l,r,h,c,m,f,p,g,v,u,y,b,k,w,T,L;return{setters:[e=>{a=e.u,s=e.c,i=e.r,n=e.a},e=>{o=e.f},e=>{d=e.Q,l=e.R,r=e.S,h=e.U,c=e.Y,m=e.k,f=e.V,p=e.B,g=e.Z,v=e.X,u=e.a2,y=e.F,b=e.W,k=e._,w=e.a3,T=e.y},e=>{L=e._},null],execute:function(){var t=document.createElement("style");t.textContent=".task-item[data-v-d21f52b9]{background-color:#fff;border-radius:1.33333vw;padding:2.66667vw;margin-bottom:2.66667vw}.task-item .header[data-v-d21f52b9]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;padding:1.33333vw 1.33333vw 2.66667vw;border-bottom:1px solid #f7f7f7}.task-item .header .left[data-v-d21f52b9]{flex:1;padding-right:5.33333vw;overflow:hidden}.task-item .header .right .tag[data-v-d21f52b9]{padding:1.6vw 3.2vw;text-align:center}.task-item .header .title[data-v-d21f52b9]{font-size:4.26667vw;color:#333;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;max-width:100%;word-break:break-all}.task-item .header .datetime[data-v-d21f52b9]{color:#aaa;font-size:3.2vw}.task-item .body[data-v-d21f52b9]{padding:2.66667vw 1.33333vw 1.33333vw}.task-item .body .item-info[data-v-d21f52b9]{display:flex;flex-direction:row;align-items:center;font-size:3.73333vw;color:#9a9a9a;line-height:1;padding:1.33333vw 0}.task-item .body .item-info>.key[data-v-d21f52b9]{min-width:5em}.task-item .body .item-info>.value[data-v-d21f52b9]{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#333}.task-item .footer[data-v-d21f52b9]{padding:1.33333vw}.task-item .footer .no-tag[data-v-d21f52b9]{padding:.53333vw 2.13333vw;text-align:center}.page[data-v-6400cf0b],.page[data-v-a9a5d373],.page[data-v-116f36ee],.page[data-v-0839a53d]{height:100%}.view-height[data-v-9e8d8a56]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height) - var(--sab) - 26.66667vw);padding-top:2.66667vw;overflow-x:hidden;overflow-y:scroll}.view-height.no-tabbar[data-v-9e8d8a56]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--sab) - 26.66667vw)}.tabs-wp[data-v-9e8d8a56]{border-bottom:1px solid #f8f8f8}\n",document.head.appendChild(t);const x={class:"header"},_={class:"left"},C={class:"title"},I={class:"datetime"},N={class:"right"},S={class:"body"},R={class:"item-info"},P={class:"value"},V={key:0,class:"item-info"},j={class:"value"},$={class:"item-info"},M={class:"value"},z={key:1,class:"item-info"},U={class:"value"},F={class:"item-info"},D={class:"value"},K={class:"footer"},E=L({name:"TaskItem",components:{},props:{item:{type:Object,default:()=>({})},tabName:String},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({}),computed:{taskStateText(){switch(String(this.item.taskState)){case"0":return"流转中";case"1":return"已完成";case"2":return"已废弃";case"3":return"暂存中";default:return"其他"}},tagColor(){var e;switch(String(null===(e=this.item)||void 0===e?void 0:e.taskState)){case"0":return"#1989fa";case"1":return"#07c160";case"2":return"#ff4d4f";case"3":return"#faab0c";default:return"#c8c9cc"}},hasView(){var e;const t=null===(e=this.item)||void 0===e?void 0:e.formKey;return o.includes(t)}},watch:{},created(){},mounted(){},methods:{toFormCenter(){if(!this.hasView)return void this.$showToast({message:"该流程暂不支持移动端查看"});const{formBizId:e,formKey:t,taskId:a,taskKey:s,processInstanceId:i,formName:n}=this.item;this.$router.push({path:`/FormCenter/${this.item.formKey}`,query:{type:"任务待办"===this.tabName?"execute":"view",taskKey:s,formKey:t,bizId:e,taskId:a,processInstanceId:i,title:n}})}}},[["render",function(e,t,a,s,i,n){const o=d("van-tag");return l(),r("div",{class:"task-item",onClick:t[0]||(t[0]=u((e=>n.toFormCenter()),["stop","prevent"]))},[h("div",x,[h("div",_,[h("div",C,c(a.item.formName||"流程"),1),h("div",I,c(e.$dayjs(a.item.createDate).format("YYYY-MM-DD HH:mm")),1)]),h("div",N,[m(o,{class:"tag",color:n.tagColor,plain:"",size:"medium"},{default:f((()=>[p(c(n.taskStateText),1)])),_:1},8,["color"])])]),h("div",S,[h("div",R,[t[1]||(t[1]=h("span",{class:"key"},"流程名称",-1)),h("span",P,c(a.item.taskSubject||"-"),1)]),"任务待办"===a.tabName?(l(),r("div",V,[t[2]||(t[2]=h("span",{class:"key"},"任务节点",-1)),h("span",j,c(a.item.taskName||"-"),1)])):g("",!0),h("div",$,[t[3]||(t[3]=h("span",{class:"key"},"开始时间",-1)),h("span",M,c(a.item.processCreateDate),1)]),"任务待办"!==a.tabName?(l(),r("div",z,[t[4]||(t[4]=h("span",{class:"key"},"完成时间",-1)),h("span",U,c(a.item.finishDate||"暂无"),1)])):g("",!0),h("div",F,[t[5]||(t[5]=h("span",{class:"key"},"发起人",-1)),h("span",D,c(a.item.taskCreatorName),1)])]),h("div",K,[n.hasView?g("",!0):(l(),v(o,{key:0,class:"no-tag",plain:"",type:"danger"},{default:f((()=>t[6]||(t[6]=[p(c("该流程暂不支持移动端查看"))]))),_:1,__:[6]}))])])}],["__scopeId","data-v-d21f52b9"]]),O={key:0,class:"p-[10px]"},Y={key:0,class:"p-[10px]"},H={key:0,class:"p-[10px]"},B={key:0,class:"p-[10px]"};e("default",L({name:"Tasks",components:{MyTodo:L({name:"MyTodo",components:{TaskItem:E},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}),computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams,...this.search};1===e.page&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await a(e),s=this.searchParams.page<=1?[]:this.list||[];this.list=[...s,...t.list],this.searchParams.page++,this.list.length>=t.total&&(this.finished=!0)}catch(e){console.log(e),this.finished=!0}finally{setTimeout((()=>{this.loading=!1,this.$closeToast()}),100)}}}},[["render",function(e,t,a,s,i,n){const o=d("TaskItem"),h=d("van-empty"),c=d("van-list"),p=d("van-pull-refresh");return l(),v(p,{modelValue:i.refreshing,"onUpdate:modelValue":t[1]||(t[1]=e=>i.refreshing=e),onRefresh:n.onRefresh},{default:f((()=>[m(c,{loading:i.loading,"onUpdate:loading":t[0]||(t[0]=e=>i.loading=e),finished:i.finished,"finished-text":i.list&&i.list.length?"没有更多了":"",onLoad:n.onLoadList,"immediate-check":!1},{default:f((()=>[i.list&&i.list.length?(l(!0),r(y,{key:0},b(i.list||[],((e,t)=>(l(),v(o,{key:e.id,item:e,tabName:"任务待办"},null,8,["item"])))),128)):(l(),r(y,{key:1},[i.loading?g("",!0):(l(),r("div",O,[m(h,{description:"暂无待办任务"})]))],64))])),_:1},8,["loading","finished","finished-text","onLoad"])])),_:1},8,["modelValue","onRefresh"])}],["__scopeId","data-v-6400cf0b"]]),MyCreate:L({name:"MyCreate",components:{TaskItem:E},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}),computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams,...this.search};1===e.page&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await s(e),a=this.searchParams.page<=1?[]:this.list||[];this.list=[...a,...t.list],this.searchParams.page++,this.list.length>=t.total&&(this.finished=!0)}catch(e){console.log(e),this.finished=!0}finally{setTimeout((()=>{this.loading=!1,this.$closeToast()}),100)}}}},[["render",function(e,t,a,s,i,n){const o=d("TaskItem"),h=d("van-empty"),c=d("van-list"),p=d("van-pull-refresh");return l(),v(p,{modelValue:i.refreshing,"onUpdate:modelValue":t[1]||(t[1]=e=>i.refreshing=e),onRefresh:n.onRefresh},{default:f((()=>[m(c,{loading:i.loading,"onUpdate:loading":t[0]||(t[0]=e=>i.loading=e),finished:i.finished,"finished-text":i.list&&i.list.length?"没有更多了":"",onLoad:n.onLoadList,"immediate-check":!1},{default:f((()=>[i.list&&i.list.length?(l(!0),r(y,{key:0},b(i.list||[],((e,t)=>(l(),v(o,{key:e.id,item:e,tabName:"我的发起"},null,8,["item"])))),128)):(l(),r(y,{key:1},[i.loading?g("",!0):(l(),r("div",Y,[m(h,{description:"暂无发起的任务"})]))],64))])),_:1},8,["loading","finished","finished-text","onLoad"])])),_:1},8,["modelValue","onRefresh"])}],["__scopeId","data-v-a9a5d373"]]),MyRelation:L({name:"MyRelation",components:{TaskItem:E},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}),computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams,...this.search};1===e.page&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await i(e),a=this.searchParams.page<=1?[]:this.list||[];this.list=[...a,...t.list],this.searchParams.page++,this.list.length>=t.total&&(this.finished=!0)}catch(e){console.log(e),this.finished=!0}finally{setTimeout((()=>{this.loading=!1,this.$closeToast()}),100)}}}},[["render",function(e,t,a,s,i,n){const o=d("TaskItem"),h=d("van-empty"),c=d("van-list"),p=d("van-pull-refresh");return l(),v(p,{modelValue:i.refreshing,"onUpdate:modelValue":t[1]||(t[1]=e=>i.refreshing=e),onRefresh:n.onRefresh},{default:f((()=>[m(c,{loading:i.loading,"onUpdate:loading":t[0]||(t[0]=e=>i.loading=e),finished:i.finished,"finished-text":i.list&&i.list.length?"没有更多了":"",onLoad:n.onLoadList,"immediate-check":!1},{default:f((()=>[i.list&&i.list.length?(l(!0),r(y,{key:0},b(i.list||[],((e,t)=>(l(),v(o,{key:e.id,item:e,tabName:"与我相关"},null,8,["item"])))),128)):(l(),r(y,{key:1},[i.loading?g("",!0):(l(),r("div",H,[m(h,{description:"暂无相关的任务"})]))],64))])),_:1},8,["loading","finished","finished-text","onLoad"])])),_:1},8,["modelValue","onRefresh"])}],["__scopeId","data-v-116f36ee"]]),MyCirculation:L({name:"MyCirculation",components:{TaskItem:E},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20,type:"receiver"}}),computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams,...this.search};1===e.page&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await n(e),a=this.searchParams.page<=1?[]:this.list||[];this.list=[...a,...t.list],this.searchParams.page++,this.list.length>=t.total&&(this.finished=!0)}catch(e){console.log(e),this.finished=!0}finally{setTimeout((()=>{this.loading=!1,this.$closeToast()}),100)}}}},[["render",function(e,t,a,s,i,n){const o=d("TaskItem"),h=d("van-empty"),c=d("van-list"),p=d("van-pull-refresh");return l(),v(p,{modelValue:i.refreshing,"onUpdate:modelValue":t[1]||(t[1]=e=>i.refreshing=e),onRefresh:n.onRefresh},{default:f((()=>[m(c,{loading:i.loading,"onUpdate:loading":t[0]||(t[0]=e=>i.loading=e),finished:i.finished,"finished-text":i.list&&i.list.length?"没有更多了":"",onLoad:n.onLoadList,"immediate-check":!1},{default:f((()=>[i.list&&i.list.length?(l(!0),r(y,{key:0},b(i.list||[],((e,t)=>(l(),v(o,{key:e.id,item:e,tabName:"抄送查阅"},null,8,["item"])))),128)):(l(),r(y,{key:1},[i.loading?g("",!0):(l(),r("div",B,[m(h,{description:"暂无更多数据"})]))],64))])),_:1},8,["loading","finished","finished-text","onLoad"])])),_:1},8,["modelValue","onRefresh"])}],["__scopeId","data-v-0839a53d"]])},props:{},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data(){var e;return{envFeishu:!!window.ENV_FEISHU,tabName:(null===(e=this.$bizStore)||void 0===e||null===(e=e.tempData)||void 0===e?void 0:e.tasksTabName)||"MyTodo",search:{taskSubject:""}}},computed:{tabs(){return this.envFeishu?[{title:"任务待办",name:"MyTodo"},{title:"与我相关",name:"MyRelation"}]:[{title:"任务待办",name:"MyTodo"},{title:"我的发起",name:"MyCreate"},{title:"与我相关",name:"MyRelation"}]}},watch:{tabName(){this.search.taskSubject=""},"search.taskSubject"(e){""===e&&this.onSearch(e)}},created(){},mounted(){},methods:{onClickTab(e){this.$bizStore.saveData({tasksTabName:e.name})},onSearch(e){this.$nextTick((()=>{this.$refs[this.tabName]&&this.$refs[this.tabName].onRefresh()}))},onCancel(){}}},[["render",function(e,t,a,s,i,n){const o=d("Navbar"),c=d("van-tab"),p=d("van-tabs"),g=d("van-search");return l(),r(y,null,[m(o,{back:!i.envFeishu,backEvent:()=>e.$router.replace({name:"Home"})},null,8,["back","backEvent"]),m(p,{class:"tabs-wp",active:i.tabName,"onUpdate:active":t[0]||(t[0]=e=>i.tabName=e),"line-width":"4em",onClickTab:n.onClickTab},{default:f((()=>[(l(!0),r(y,null,b(n.tabs,(e=>(l(),v(c,{class:"px-[20px]",key:e.name,name:e.name,title:e.title},null,8,["name","title"])))),128))])),_:1},8,["active","onClickTab"]),m(g,{modelValue:i.search.taskSubject,"onUpdate:modelValue":t[1]||(t[1]=e=>i.search.taskSubject=e),placeholder:"搜索流程名称","show-action":!1,onSearch:n.onSearch,onCancel:n.onCancel},null,8,["modelValue","onSearch","onCancel"]),h("div",{class:T(["view-height",{"no-tabbar":i.envFeishu}])},[(l(),v(w,{include:n.tabs.map((e=>e.name))},[(l(),v(k(i.tabName),{ref:i.tabName,search:i.search},null,8,["search"]))],1032,["include"]))],2)],64)}],["__scopeId","data-v-9e8d8a56"]]))}}}));
