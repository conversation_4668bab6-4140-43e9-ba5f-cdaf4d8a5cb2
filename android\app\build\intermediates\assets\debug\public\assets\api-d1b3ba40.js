import{h as e}from"./index-4829f8e2.js";const o="/microdamcodeservice/api";function a(t){return e({url:"".concat(o,"/monitorData/sysEnvCodeList"),method:"get",params:t})}function i(t){return e({url:"".concat(o,"/monitorData/code/warningData"),method:"get",params:t})}function d(t){return e({url:"".concat(o,"/gis/findCodeInfo?id=").concat(t),method:"get"})}function g(t,n){return e({url:"".concat(o,"/dam/").concat(t,"/findDefaultCategoryId"),method:"get",params:{userId:n}})}function u(t){return e({url:"".concat(o,"/dam/findFixNavigationTreeNew"),method:"get",params:t})}export{d as a,i as b,u as c,g as d,a as g};
