System.register(["./index-legacy-09188690.js"],(function(t,e){"use strict";var r;return{setters:[t=>{r=t.h}],execute:function(){t({a:function({id:t,projectDamId:o,projectInstrId:n},a){return r({url:`${e}/monitorData/code/initValueEntry`,method:"get",params:{projectId:t,damId:o,valueIsAuto:0,time:a,instrId:n}})},b:function(t){return r({url:`${e}/dams/${t}/code/projectInfo`,method:"get"})},c:function(t){return r({url:`${e}/monitorData/code/calcValueRecordList`,method:"post",data:{valueRecords:t}})},d:function({id:t,projectDamId:o,projectInstrId:n}){return r({url:`${e}/dam/${o}/findCodeInfoList`,method:"get",params:{params:encodeURI(JSON.stringify({codeStatus:null,isImportant:null,autoType:"",type:1})),projectId:t,instrId:n}})},e:function(t){return r({url:`${e}/dam/processLine`,method:"get",params:t})},f:function(t){return r({url:`${e}/monitorData/code/allValue`,method:"get",params:t})},g:function(t){return r({url:`${e}/dams/code/projects`,method:"get",params:t})},s:function(t,o){return r({url:`${e}/monitorData/code/entryCodeValues`,method:"post",params:{id:t},data:o})}});const e="/microdamcodeservice/api"}}}));
