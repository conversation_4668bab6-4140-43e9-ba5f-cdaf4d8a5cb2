System.register(["./index-legacy-645a3645.js","./file-legacy-a550097a.js","./file-legacy-e670f35e.js","./verder-legacy-e6127216.js","./index-legacy-09188690.js"],(function(e,t){"use strict";var i,a,s,l,o,n,r,d,c,m,h,v,f,w,p,u,g;return{setters:[e=>{i=e.U},e=>{a=e.g},e=>{s=e.i,l=e.a,o=e.b,n=e.c,r=e.d},e=>{d=e.Q,c=e.R,m=e.S,h=e.U,v=e.k,f=e.a2,w=e.Y},e=>{p=e._,u=e.h,g=e.A}],execute:function(){var t=document.createElement("style");t.textContent=".list-item[data-v-41e089b5]{display:flex;flex-direction:column;background:#fff;border-radius:1.06667vw;margin-bottom:4.26667vw}.list-item .item-container[data-v-41e089b5]{display:flex;width:100%}.list-item .container-left[data-v-41e089b5]{width:32vw;height:21.33333vw;margin-right:2.66667vw;flex-shrink:0;border-radius:1.06667vw;overflow:hidden}.list-item .container-left[data-v-41e089b5] .van-uploader__preview-image{width:32vw;height:21.33333vw;object-fit:cover}.list-item .container-right[data-v-41e089b5]{flex:1;display:flex;flex-direction:column;justify-content:space-between;overflow:hidden;min-width:0;width:100%}.list-item .title-wrapper[data-v-41e089b5]{width:100%;color:#363636;font-size:4.26667vw;font-weight:500;line-height:5.86667vw;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;word-break:break-all}.list-item .info-wrapper[data-v-41e089b5]{width:100%}.list-item .aside[data-v-41e089b5]{margin-bottom:1.06667vw;font-size:3.2vw;line-height:5.86667vw;color:#999}.list-item .aside .key[data-v-41e089b5]{margin-right:1.06667vw}\n",document.head.appendChild(t),e("g",(function(e){return u({url:`${g.VUE_APP_BASE_API_SERVICENAME}/${_}/page`,method:"get",params:e})}));const b={class:"list-item"},y={class:"item-container"},x={class:"container-left"},U={class:"title-wrapper"},k={class:"info-wrapper"},$={class:"aside"},T={class:"value"};e("L",p({name:"ListItem",components:{UploadFiles:i},props:{item:{type:Object,default:()=>({})}},emits:[],setup(e,{attrs:t,slots:i,emit:a}){},data:()=>({}),computed:{thumbnailUrl(){var e;return null!==(e=this.item)&&void 0!==e&&e.thumbnailFileUpload?`/api/sys-storage/download_image?f8s=${this.item.thumbnailFileUpload}`:null}},watch:{},created(){},mounted(){},methods:{async goDetail(){var e;if(null!==(e=this.item)&&void 0!==e&&e.attFileUpload)try{const e=await a({g9s:[this.item.attFileUpload]});if(e&&e.length>0){const t=e[0],i=t.fileName||"",a=s(i),d=l(i),c=o(i),m=n(i),h=r(i);if(a)return void this.$showToast("暂不支持预览，请上传正确的文件格式");if(d)return void this.$showToast("暂不支持预览，请上传正确的文件格式");this.$router.push({name:"NewsDetail",query:{fileData:JSON.stringify({fileToken:t.fileToken,name:t.fileName,isPdf:c,isDoc:m,isXls:h,isVideo:d})}})}else this.$showToast("文件信息获取失败")}catch(t){console.error("获取文件信息失败",t),this.$showToast("文件预览失败"),setTimeout((()=>{this.$closeToast()}),100)}else this.$showToast("暂无详细内容")}}},[["render",function(e,t,i,a,s,l){const o=d("UploadFiles");return c(),m("div",b,[h("div",y,[h("div",x,[v(o,{ref:"fileUpload",g9s:i.item.thumbnailFileUpload,"onUpdate:g9s":t[0]||(t[0]=e=>i.item.thumbnailFileUpload=e),readonly:""},null,8,["g9s"])]),h("div",{class:"container-right",onClick:t[1]||(t[1]=f((e=>l.goDetail()),["stop","prevent"]))},[h("div",U,w(i.item.name),1),h("div",k,[h("div",$,[h("span",T,w(e.$dayjs(i.item.newsTime).format("YYYY-MM-DD")),1)])])])])])}],["__scopeId","data-v-41e089b5"]]));const _="general-news"}}}));
