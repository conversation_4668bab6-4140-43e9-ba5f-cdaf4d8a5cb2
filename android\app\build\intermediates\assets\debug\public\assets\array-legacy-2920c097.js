System.register([],(function(t,e){"use strict";return{execute:function(){t({s:function t(e=[],n,r="id",i="children"){let u=[];for(let c=0;c<e.length;c++){const f=e[c];if(f[r]==n){u.push(f);break}if(f[i]&&0!==f[i].length){let e=t(f[i],n,r,i);if(0!==e.length){e.unshift(f),u=e;break}}}return u},t:function t(e,n=[]){return e.forEach((e=>{const{children:r,...i}=e;if(r&&r.length)return n.push(i),t(r,n);n.push(i)})),n}})}}}));
