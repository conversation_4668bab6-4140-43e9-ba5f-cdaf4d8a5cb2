var Zc=Object.freeze,Ev=Object.defineProperty;var ot=(t,e)=>Zc(Ev(t,"raw",{value:Zc(e||t.slice())}));import{k as hi,_ as Rf}from"./index-4829f8e2.js";import{l as Tf,aa as Ov,R as No,S as la,a1 as Hv,Q as Ef,X as Of,V as Iv,U as Mv}from"./verder-361ae6c7.js";import{h as Jc,d as eu}from"./dateFormat-1fb6392c.js";import{e as xv,f as _v}from"./api-e44c60fe.js";var no=function(t){return t&&t.Math===Math&&t},Ie=no(typeof globalThis=="object"&&globalThis)||no(typeof window=="object"&&window)||no(typeof self=="object"&&self)||no(typeof hi=="object"&&hi)||no(typeof hi=="object"&&hi)||function(){return this}()||Function("return this")(),ka={},De=function(t){try{return!!t()}catch(e){return!0}},Pv=De,Lt=!Pv(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!==7}),Av=De,Ss=!Av(function(){var t=(function(){}).bind();return typeof t!="function"||t.hasOwnProperty("prototype")}),Nv=Ss,di=Function.prototype.call,Ve=Nv?di.bind(di):function(){return di.apply(di,arguments)},Hf={},If={}.propertyIsEnumerable,Mf=Object.getOwnPropertyDescriptor,$v=Mf&&!If.call({1:2},1);Hf.f=$v?function(e){var r=Mf(this,e);return!!r&&r.enumerable}:If;var Rs=function(t,e){return{enumerable:!(t&1),configurable:!(t&2),writable:!(t&4),value:e}},xf=Ss,_f=Function.prototype,aa=_f.call,Lv=xf&&_f.bind.bind(aa,aa),be=xf?Lv:function(t){return function(){return aa.apply(t,arguments)}},Pf=be,Dv=Pf({}.toString),Vv=Pf("".slice),gn=function(t){return Vv(Dv(t),8,-1)},kv=be,Fv=De,Bv=gn,ks=Object,Wv=kv("".split),jv=Fv(function(){return!ks("z").propertyIsEnumerable(0)})?function(t){return Bv(t)==="String"?Wv(t,""):ks(t)}:ks,Fa=function(t){return t==null},zv=Fa,Uv=TypeError,Xo=function(t){if(zv(t))throw new Uv("Can't call method on "+t);return t},Gv=jv,Kv=Xo,qo=function(t){return Gv(Kv(t))},Fs=typeof document=="object"&&document.all,Me=typeof Fs>"u"&&Fs!==void 0?function(t){return typeof t=="function"||t===Fs}:function(t){return typeof t=="function"},Yv=Me,Dt=function(t){return typeof t=="object"?t!==null:Yv(t)},Bs=Ie,Xv=Me,qv=function(t){return Xv(t)?t:void 0},Gn=function(t,e){return arguments.length<2?qv(Bs[t]):Bs[t]&&Bs[t][e]},Qv=be,Qo=Qv({}.isPrototypeOf),Zv=Ie,tu=Zv.navigator,ru=tu&&tu.userAgent,Ts=ru?String(ru):"",Af=Ie,Ws=Ts,nu=Af.process,ou=Af.Deno,iu=nu&&nu.versions||ou&&ou.version,su=iu&&iu.v8,Zt,as;su&&(Zt=su.split("."),as=Zt[0]>0&&Zt[0]<4?1:+(Zt[0]+Zt[1]));!as&&Ws&&(Zt=Ws.match(/Edge\/(\d+)/),(!Zt||Zt[1]>=74)&&(Zt=Ws.match(/Chrome\/(\d+)/),Zt&&(as=+Zt[1])));var Jv=as,lu=Jv,ey=De,ty=Ie,ry=ty.String,Ba=!!Object.getOwnPropertySymbols&&!ey(function(){var t=Symbol("symbol detection");return!ry(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&lu&&lu<41}),ny=Ba,Nf=ny&&!Symbol.sham&&typeof Symbol.iterator=="symbol",oy=Gn,iy=Me,sy=Qo,ly=Nf,ay=Object,Wa=ly?function(t){return typeof t=="symbol"}:function(t){var e=oy("Symbol");return iy(e)&&sy(e.prototype,ay(t))},cy=String,Es=function(t){try{return cy(t)}catch(e){return"Object"}},uy=Me,hy=Es,dy=TypeError,bt=function(t){if(uy(t))return t;throw new dy(hy(t)+" is not a function")},fy=bt,gy=Fa,Zo=function(t,e){var r=t[e];return gy(r)?void 0:fy(r)},js=Ve,zs=Me,Us=Dt,my=TypeError,py=function(t,e){var r,n;if(e==="string"&&zs(r=t.toString)&&!Us(n=js(r,t))||zs(r=t.valueOf)&&!Us(n=js(r,t))||e!=="string"&&zs(r=t.toString)&&!Us(n=js(r,t)))return n;throw new my("Can't convert object to primitive value")},$f={exports:{}},au=Ie,wy=Object.defineProperty,ja=function(t,e){try{wy(au,t,{value:e,configurable:!0,writable:!0})}catch(r){au[t]=e}return e},vy=Ie,yy=ja,cu="__core-js_shared__",uu=$f.exports=vy[cu]||yy(cu,{});(uu.versions||(uu.versions=[])).push({version:"3.42.0",mode:"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.42.0/LICENSE",source:"https://github.com/zloirock/core-js"});var za=$f.exports,hu=za,Lf=function(t,e){return hu[t]||(hu[t]=e||{})},Cy=Xo,by=Object,Kn=function(t){return by(Cy(t))},Sy=be,Ry=Kn,Ty=Sy({}.hasOwnProperty),Vt=Object.hasOwn||function(e,r){return Ty(Ry(e),r)},Ey=be,Oy=0,Hy=Math.random(),Iy=Ey(1 .toString),Df=function(t){return"Symbol("+(t===void 0?"":t)+")_"+Iy(++Oy+Hy,36)},My=Ie,xy=Lf,du=Vt,_y=Df,Py=Ba,Ay=Nf,Dn=My.Symbol,Gs=xy("wks"),Ny=Ay?Dn.for||Dn:Dn&&Dn.withoutSetter||_y,er=function(t){return du(Gs,t)||(Gs[t]=Py&&du(Dn,t)?Dn[t]:Ny("Symbol."+t)),Gs[t]},$y=Ve,fu=Dt,gu=Wa,Ly=Zo,Dy=py,Vy=er,ky=TypeError,Fy=Vy("toPrimitive"),By=function(t,e){if(!fu(t)||gu(t))return t;var r=Ly(t,Fy),n;if(r){if(e===void 0&&(e="default"),n=$y(r,t,e),!fu(n)||gu(n))return n;throw new ky("Can't convert object to primitive value")}return e===void 0&&(e="number"),Dy(t,e)},Wy=By,jy=Wa,Vf=function(t){var e=Wy(t,"string");return jy(e)?e:e+""},zy=Ie,mu=Dt,ca=zy.document,Uy=mu(ca)&&mu(ca.createElement),Ua=function(t){return Uy?ca.createElement(t):{}},Gy=Lt,Ky=De,Yy=Ua,kf=!Gy&&!Ky(function(){return Object.defineProperty(Yy("div"),"a",{get:function(){return 7}}).a!==7}),Xy=Lt,qy=Ve,Qy=Hf,Zy=Rs,Jy=qo,eC=Vf,tC=Vt,rC=kf,pu=Object.getOwnPropertyDescriptor;ka.f=Xy?pu:function(e,r){if(e=Jy(e),r=eC(r),rC)try{return pu(e,r)}catch(n){}if(tC(e,r))return Zy(!qy(Qy.f,e,r),e[r])};var Ir={},nC=Lt,oC=De,Ff=nC&&oC(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!==42}),iC=Dt,sC=String,lC=TypeError,ke=function(t){if(iC(t))return t;throw new lC(sC(t)+" is not an object")},aC=Lt,cC=kf,uC=Ff,fi=ke,wu=Vf,hC=TypeError,Ks=Object.defineProperty,dC=Object.getOwnPropertyDescriptor,Ys="enumerable",Xs="configurable",qs="writable";Ir.f=aC?uC?function(e,r,n){if(fi(e),r=wu(r),fi(n),typeof e=="function"&&r==="prototype"&&"value"in n&&qs in n&&!n[qs]){var i=dC(e,r);i&&i[qs]&&(e[r]=n.value,n={configurable:Xs in n?n[Xs]:i[Xs],enumerable:Ys in n?n[Ys]:i[Ys],writable:!1})}return Ks(e,r,n)}:Ks:function(e,r,n){if(fi(e),r=wu(r),fi(n),cC)try{return Ks(e,r,n)}catch(i){}if("get"in n||"set"in n)throw new hC("Accessors not supported");return"value"in n&&(e[r]=n.value),e};var fC=Lt,gC=Ir,mC=Rs,Yn=fC?function(t,e,r){return gC.f(t,e,mC(1,r))}:function(t,e,r){return t[e]=r,t},Bf={exports:{}},ua=Lt,pC=Vt,Wf=Function.prototype,wC=ua&&Object.getOwnPropertyDescriptor,Ga=pC(Wf,"name"),vC=Ga&&(function(){}).name==="something",yC=Ga&&(!ua||ua&&wC(Wf,"name").configurable),CC={EXISTS:Ga,PROPER:vC,CONFIGURABLE:yC},bC=be,SC=Me,ha=za,RC=bC(Function.toString);SC(ha.inspectSource)||(ha.inspectSource=function(t){return RC(t)});var TC=ha.inspectSource,EC=Ie,OC=Me,vu=EC.WeakMap,HC=OC(vu)&&/native code/.test(String(vu)),IC=Lf,MC=Df,yu=IC("keys"),Ka=function(t){return yu[t]||(yu[t]=MC(t))},Ya={},xC=HC,jf=Ie,_C=Dt,PC=Yn,Qs=Vt,Zs=za,AC=Ka,NC=Ya,Cu="Object already initialized",da=jf.TypeError,$C=jf.WeakMap,cs,jo,us,LC=function(t){return us(t)?jo(t):cs(t,{})},DC=function(t){return function(e){var r;if(!_C(e)||(r=jo(e)).type!==t)throw new da("Incompatible receiver, "+t+" required");return r}};if(xC||Zs.state){var rr=Zs.state||(Zs.state=new $C);rr.get=rr.get,rr.has=rr.has,rr.set=rr.set,cs=function(t,e){if(rr.has(t))throw new da(Cu);return e.facade=t,rr.set(t,e),e},jo=function(t){return rr.get(t)||{}},us=function(t){return rr.has(t)}}else{var En=AC("state");NC[En]=!0,cs=function(t,e){if(Qs(t,En))throw new da(Cu);return e.facade=t,PC(t,En,e),e},jo=function(t){return Qs(t,En)?t[En]:{}},us=function(t){return Qs(t,En)}}var zf={set:cs,get:jo,has:us,enforce:LC,getterFor:DC},Xa=be,VC=De,kC=Me,gi=Vt,fa=Lt,FC=CC.CONFIGURABLE,BC=TC,Uf=zf,WC=Uf.enforce,jC=Uf.get,bu=String,Ji=Object.defineProperty,zC=Xa("".slice),UC=Xa("".replace),GC=Xa([].join),KC=fa&&!VC(function(){return Ji(function(){},"length",{value:8}).length!==8}),YC=String(String).split("String"),XC=Bf.exports=function(t,e,r){zC(bu(e),0,7)==="Symbol("&&(e="["+UC(bu(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!gi(t,"name")||FC&&t.name!==e)&&(fa?Ji(t,"name",{value:e,configurable:!0}):t.name=e),KC&&r&&gi(r,"arity")&&t.length!==r.arity&&Ji(t,"length",{value:r.arity});try{r&&gi(r,"constructor")&&r.constructor?fa&&Ji(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(i){}var n=WC(t);return gi(n,"source")||(n.source=GC(YC,typeof e=="string"?e:"")),t};Function.prototype.toString=XC(function(){return kC(this)&&jC(this).source||BC(this)},"toString");var Gf=Bf.exports,qC=Me,QC=Ir,ZC=Gf,JC=ja,qa=function(t,e,r,n){n||(n={});var i=n.enumerable,o=n.name!==void 0?n.name:e;if(qC(r)&&ZC(r,o,n),n.global)i?t[e]=r:JC(e,r);else{try{n.unsafe?t[e]&&(i=!0):delete t[e]}catch(s){}i?t[e]=r:QC.f(t,e,{value:r,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return t},Kf={},eb=Math.ceil,tb=Math.floor,rb=Math.trunc||function(e){var r=+e;return(r>0?tb:eb)(r)},nb=rb,Jo=function(t){var e=+t;return e!==e||e===0?0:nb(e)},ob=Jo,ib=Math.max,sb=Math.min,lb=function(t,e){var r=ob(t);return r<0?ib(r+e,0):sb(r,e)},ab=Jo,cb=Math.min,ub=function(t){var e=ab(t);return e>0?cb(e,9007199254740991):0},hb=ub,Xn=function(t){return hb(t.length)},db=qo,fb=lb,gb=Xn,Su=function(t){return function(e,r,n){var i=db(e),o=gb(i);if(o===0)return!t&&-1;var s=fb(n,o),l;if(t&&r!==r){for(;o>s;)if(l=i[s++],l!==l)return!0}else for(;o>s;s++)if((t||s in i)&&i[s]===r)return t||s||0;return!t&&-1}},mb={includes:Su(!0),indexOf:Su(!1)},pb=be,Js=Vt,wb=qo,vb=mb.indexOf,yb=Ya,Ru=pb([].push),Yf=function(t,e){var r=wb(t),n=0,i=[],o;for(o in r)!Js(yb,o)&&Js(r,o)&&Ru(i,o);for(;e.length>n;)Js(r,o=e[n++])&&(~vb(i,o)||Ru(i,o));return i},Qa=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Cb=Yf,bb=Qa,Sb=bb.concat("length","prototype");Kf.f=Object.getOwnPropertyNames||function(e){return Cb(e,Sb)};var Xf={};Xf.f=Object.getOwnPropertySymbols;var Rb=Gn,Tb=be,Eb=Kf,Ob=Xf,Hb=ke,Ib=Tb([].concat),Mb=Rb("Reflect","ownKeys")||function(e){var r=Eb.f(Hb(e)),n=Ob.f;return n?Ib(r,n(e)):r},Tu=Vt,xb=Mb,_b=ka,Pb=Ir,qf=function(t,e,r){for(var n=xb(e),i=Pb.f,o=_b.f,s=0;s<n.length;s++){var l=n[s];!Tu(t,l)&&!(r&&Tu(r,l))&&i(t,l,o(e,l))}},Ab=De,Nb=Me,$b=/#|\.prototype\./,ei=function(t,e){var r=Db[Lb(t)];return r===kb?!0:r===Vb?!1:Nb(e)?Ab(e):!!e},Lb=ei.normalize=function(t){return String(t).replace($b,".").toLowerCase()},Db=ei.data={},Vb=ei.NATIVE="N",kb=ei.POLYFILL="P",Fb=ei,mi=Ie,Bb=ka.f,Wb=Yn,jb=qa,zb=ja,Ub=qf,Gb=Fb,oe=function(t,e){var r=t.target,n=t.global,i=t.stat,o,s,l,a,c,d;if(n?s=mi:i?s=mi[r]||zb(r,{}):s=mi[r]&&mi[r].prototype,s)for(l in e){if(c=e[l],t.dontCallGetSet?(d=Bb(s,l),a=d&&d.value):a=s[l],o=Gb(n?l:r+(i?".":"#")+l,t.forced),!o&&a!==void 0){if(typeof c==typeof a)continue;Ub(c,a)}(t.sham||a&&a.sham)&&Wb(c,"sham",!0),jb(s,l,c,t)}},Kb=Ss,Qf=Function.prototype,Eu=Qf.apply,Ou=Qf.call,ti=typeof Reflect=="object"&&Reflect.apply||(Kb?Ou.bind(Eu):function(){return Ou.apply(Eu,arguments)}),Yb=be,Xb=bt,Zf=function(t,e,r){try{return Yb(Xb(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(n){}},qb=Dt,Qb=function(t){return qb(t)||t===null},Zb=Qb,Jb=String,eS=TypeError,tS=function(t){if(Zb(t))return t;throw new eS("Can't set "+Jb(t)+" as a prototype")},rS=Zf,nS=Dt,oS=Xo,iS=tS,Jf=Object.setPrototypeOf||("__proto__"in{}?function(){var t=!1,e={},r;try{r=rS(Object.prototype,"__proto__","set"),r(e,[]),t=e instanceof Array}catch(n){}return function(i,o){return oS(i),iS(o),nS(i)&&(t?r(i,o):i.__proto__=o),i}}():void 0),sS=Ir.f,lS=function(t,e,r){r in t||sS(t,r,{configurable:!0,get:function(){return e[r]},set:function(n){e[r]=n}})},aS=Me,cS=Dt,Hu=Jf,uS=function(t,e,r){var n,i;return Hu&&aS(n=e.constructor)&&n!==r&&cS(i=n.prototype)&&i!==r.prototype&&Hu(t,i),t},hS=er,dS=hS("toStringTag"),eg={};eg[dS]="z";var fS=String(eg)==="[object z]",gS=fS,mS=Me,es=gn,pS=er,wS=pS("toStringTag"),vS=Object,yS=es(function(){return arguments}())==="Arguments",CS=function(t,e){try{return t[e]}catch(r){}},tg=gS?es:function(t){var e,r,n;return t===void 0?"Undefined":t===null?"Null":typeof(r=CS(e=vS(t),wS))=="string"?r:yS?es(e):(n=es(e))==="Object"&&mS(e.callee)?"Arguments":n},bS=tg,SS=String,Os=function(t){if(bS(t)==="Symbol")throw new TypeError("Cannot convert a Symbol value to a string");return SS(t)},RS=Os,TS=function(t,e){return t===void 0?arguments.length<2?"":e:RS(t)},ES=Dt,OS=Yn,HS=function(t,e){ES(e)&&"cause"in e&&OS(t,"cause",e.cause)},IS=be,rg=Error,MS=IS("".replace),xS=function(t){return String(new rg(t).stack)}("zxcasd"),ng=/\n\s*at [^:]*:[^\n]*/,_S=ng.test(xS),PS=function(t,e){if(_S&&typeof t=="string"&&!rg.prepareStackTrace)for(;e--;)t=MS(t,ng,"");return t},AS=De,NS=Rs,$S=!AS(function(){var t=new Error("a");return"stack"in t?(Object.defineProperty(t,"stack",NS(1,7)),t.stack!==7):!0}),LS=Yn,DS=PS,VS=$S,Iu=Error.captureStackTrace,kS=function(t,e,r,n){VS&&(Iu?Iu(t,e):LS(t,"stack",DS(r,n)))},Mu=Gn,FS=Vt,xu=Yn,BS=Qo,_u=Jf,Pu=qf,Au=lS,WS=uS,jS=TS,zS=HS,US=kS,GS=Lt,KS=function(t,e,r,n){var i="stackTraceLimit",o=n?2:1,s=t.split("."),l=s[s.length-1],a=Mu.apply(null,s);if(a){var c=a.prototype;if(FS(c,"cause")&&delete c.cause,!r)return a;var d=Mu("Error"),f=e(function(m,p){var v=jS(n?p:m,void 0),R=n?new a(m):new a;return v!==void 0&&xu(R,"message",v),US(R,f,R.stack,2),this&&BS(c,this)&&WS(R,this,f),arguments.length>o&&zS(R,arguments[o]),R});f.prototype=c,l!=="Error"?_u?_u(f,d):Pu(f,d,{name:!0}):GS&&i in a&&(Au(f,a,i),Au(f,a,"prepareStackTrace")),Pu(f,a);try{c.name!==l&&xu(c,"name",l),c.constructor=f}catch(m){}return f}},og=oe,YS=Ie,fr=ti,ig=KS,ga="WebAssembly",Nu=YS[ga],hs=new Error("e",{cause:7}).cause!==7,mn=function(t,e){var r={};r[t]=ig(t,e,hs),og({global:!0,constructor:!0,arity:1,forced:hs},r)},Za=function(t,e){if(Nu&&Nu[t]){var r={};r[t]=ig(ga+"."+t,e,hs),og({target:ga,stat:!0,constructor:!0,arity:1,forced:hs},r)}};mn("Error",function(t){return function(r){return fr(t,this,arguments)}});mn("EvalError",function(t){return function(r){return fr(t,this,arguments)}});mn("RangeError",function(t){return function(r){return fr(t,this,arguments)}});mn("ReferenceError",function(t){return function(r){return fr(t,this,arguments)}});mn("SyntaxError",function(t){return function(r){return fr(t,this,arguments)}});mn("TypeError",function(t){return function(r){return fr(t,this,arguments)}});mn("URIError",function(t){return function(r){return fr(t,this,arguments)}});Za("CompileError",function(t){return function(r){return fr(t,this,arguments)}});Za("LinkError",function(t){return function(r){return fr(t,this,arguments)}});Za("RuntimeError",function(t){return function(r){return fr(t,this,arguments)}});var XS=gn,sg=Array.isArray||function(e){return XS(e)==="Array"},qS=Lt,QS=sg,ZS=TypeError,JS=Object.getOwnPropertyDescriptor,eR=qS&&!function(){if(this!==void 0)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}(),lg=eR?function(t,e){if(QS(t)&&!JS(t,"length").writable)throw new ZS("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e},tR=TypeError,rR=9007199254740991,ag=function(t){if(t>rR)throw tR("Maximum allowed index exceeded");return t},nR=oe,oR=Kn,iR=Xn,sR=lg,lR=ag,aR=De,cR=aR(function(){return[].push.call({length:4294967296},1)!==4294967297}),uR=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}},hR=cR||!uR();nR({target:"Array",proto:!0,arity:1,forced:hR},{push:function(e){var r=oR(this),n=iR(r),i=arguments.length;lR(n+i);for(var o=0;o<i;o++)r[n]=arguments[o],n++;return sR(r,n),n}});var dR=gn,fR=be,gR=function(t){if(dR(t)==="Function")return fR(t)},$u=gR,mR=bt,pR=Ss,wR=$u($u.bind),cg=function(t,e){return mR(t),e===void 0?t:pR?wR(t,e):function(){return t.apply(e,arguments)}},ug={},vR=er,yR=ug,CR=vR("iterator"),bR=Array.prototype,SR=function(t){return t!==void 0&&(yR.Array===t||bR[CR]===t)},RR=tg,Lu=Zo,TR=Fa,ER=ug,OR=er,HR=OR("iterator"),hg=function(t){if(!TR(t))return Lu(t,HR)||Lu(t,"@@iterator")||ER[RR(t)]},IR=Ve,MR=bt,xR=ke,_R=Es,PR=hg,AR=TypeError,NR=function(t,e){var r=arguments.length<2?PR(t):e;if(MR(r))return xR(IR(r,t));throw new AR(_R(t)+" is not iterable")},$R=Ve,Du=ke,LR=Zo,kt=function(t,e,r){var n,i;Du(t);try{if(n=LR(t,"return"),!n){if(e==="throw")throw r;return r}n=$R(n,t)}catch(o){i=!0,n=o}if(e==="throw")throw r;if(i)throw n;return Du(n),r},DR=cg,VR=Ve,kR=ke,FR=Es,BR=SR,WR=Xn,Vu=Qo,jR=NR,zR=hg,ku=kt,UR=TypeError,ts=function(t,e){this.stopped=t,this.result=e},Fu=ts.prototype,qn=function(t,e,r){var n=r&&r.that,i=!!(r&&r.AS_ENTRIES),o=!!(r&&r.IS_RECORD),s=!!(r&&r.IS_ITERATOR),l=!!(r&&r.INTERRUPTED),a=DR(e,n),c,d,f,m,p,v,R,H=function(b){return c&&ku(c,"normal",b),new ts(!0,b)},M=function(b){return i?(kR(b),l?a(b[0],b[1],H):a(b[0],b[1])):l?a(b,H):a(b)};if(o)c=t.iterator;else if(s)c=t;else{if(d=zR(t),!d)throw new UR(FR(t)+" is not iterable");if(BR(d)){for(f=0,m=WR(t);m>f;f++)if(p=M(t[f]),p&&Vu(Fu,p))return p;return new ts(!1)}c=jR(t,d)}for(v=o?t.next:c.next;!(R=VR(v,c)).done;){try{p=M(R.value)}catch(b){ku(c,"throw",b)}if(typeof p=="object"&&p&&Vu(Fu,p))return p}return new ts(!1)},GR=Lt,KR=Ir,YR=Rs,dg=function(t,e,r){GR?KR.f(t,e,YR(0,r)):t[e]=r},XR=oe,qR=qn,QR=dg;XR({target:"Object",stat:!0},{fromEntries:function(e){var r={};return qR(e,function(n,i){QR(r,n,i)},{AS_ENTRIES:!0}),r}});var el=be,pi=Set.prototype,gr={Set,add:el(pi.add),has:el(pi.has),remove:el(pi.delete),proto:pi},ZR=gr.has,pn=function(t){return ZR(t),t},JR=Ve,wn=function(t,e,r){for(var n=r?t:t.iterator,i=t.next,o,s;!(o=JR(i,n)).done;)if(s=e(o.value),s!==void 0)return s},fg=be,eT=wn,gg=gr,tT=gg.Set,mg=gg.proto,rT=fg(mg.forEach),pg=fg(mg.keys),nT=pg(new tT).next,ri=function(t,e,r){return r?eT({iterator:pg(t),next:nT},e):rT(t,e)},wg=gr,oT=ri,iT=wg.Set,sT=wg.add,Ja=function(t){var e=new iT;return oT(t,function(r){sT(e,r)}),e},lT=Zf,aT=gr,ni=lT(aT.proto,"size","get")||function(t){return t.size},jr=function(t){return{iterator:t,next:t.next,done:!1}},Bu=bt,vg=ke,Wu=Ve,cT=Jo,uT=jr,ju="Invalid size",hT=RangeError,dT=TypeError,fT=Math.max,yg=function(t,e){this.set=t,this.size=fT(e,0),this.has=Bu(t.has),this.keys=Bu(t.keys)};yg.prototype={getIterator:function(){return uT(vg(Wu(this.keys,this.set)))},includes:function(t){return Wu(this.has,this.set,t)}};var vn=function(t){vg(t);var e=+t.size;if(e!==e)throw new dT(ju);var r=cT(e);if(r<0)throw new hT(ju);return new yg(t,r)},gT=pn,Cg=gr,mT=Ja,pT=ni,wT=vn,vT=ri,yT=wn,CT=Cg.has,zu=Cg.remove,bT=function(e){var r=gT(this),n=wT(e),i=mT(r);return pT(r)<=n.size?vT(r,function(o){n.includes(o)&&zu(i,o)}):yT(n.getIterator(),function(o){CT(r,o)&&zu(i,o)}),i},ST=Gn,Uu=function(t){return{size:t,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}},Gu=function(t){return{size:t,has:function(){return!0},keys:function(){throw new Error("e")}}},yn=function(t,e){var r=ST("Set");try{new r()[t](Uu(0));try{return new r()[t](Uu(-1)),!1}catch(i){if(!e)return!0;try{return new r()[t](Gu(-1/0)),!1}catch(o){var n=new r;return n.add(1),n.add(2),e(n[t](Gu(1/0)))}}}catch(i){return!1}},RT=oe,TT=bT,ET=yn,OT=!ET("difference",function(t){return t.size===0});RT({target:"Set",proto:!0,real:!0,forced:OT},{difference:TT});var HT=pn,ec=gr,IT=ni,MT=vn,xT=ri,_T=wn,PT=ec.Set,Ku=ec.add,AT=ec.has,NT=function(e){var r=HT(this),n=MT(e),i=new PT;return IT(r)>n.size?_T(n.getIterator(),function(o){AT(r,o)&&Ku(i,o)}):xT(r,function(o){n.includes(o)&&Ku(i,o)}),i},$T=oe,LT=De,DT=NT,VT=yn,kT=!VT("intersection",function(t){return t.size===2&&t.has(1)&&t.has(2)})||LT(function(){return String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))!=="3,2"});$T({target:"Set",proto:!0,real:!0,forced:kT},{intersection:DT});var FT=pn,BT=gr.has,WT=ni,jT=vn,zT=ri,UT=wn,GT=kt,KT=function(e){var r=FT(this),n=jT(e);if(WT(r)<=n.size)return zT(r,function(o){if(n.includes(o))return!1},!0)!==!1;var i=n.getIterator();return UT(i,function(o){if(BT(r,o))return GT(i,"normal",!1)})!==!1},YT=oe,XT=KT,qT=yn,QT=!qT("isDisjointFrom",function(t){return!t});YT({target:"Set",proto:!0,real:!0,forced:QT},{isDisjointFrom:XT});var ZT=pn,JT=ni,eE=ri,tE=vn,rE=function(e){var r=ZT(this),n=tE(e);return JT(r)>n.size?!1:eE(r,function(i){if(!n.includes(i))return!1},!0)!==!1},nE=oe,oE=rE,iE=yn,sE=!iE("isSubsetOf",function(t){return t});nE({target:"Set",proto:!0,real:!0,forced:sE},{isSubsetOf:oE});var lE=pn,aE=gr.has,cE=ni,uE=vn,hE=wn,dE=kt,fE=function(e){var r=lE(this),n=uE(e);if(cE(r)<n.size)return!1;var i=n.getIterator();return hE(i,function(o){if(!aE(r,o))return dE(i,"normal",!1)})!==!1},gE=oe,mE=fE,pE=yn,wE=!pE("isSupersetOf",function(t){return!t});gE({target:"Set",proto:!0,real:!0,forced:wE},{isSupersetOf:mE});var vE=pn,tc=gr,yE=Ja,CE=vn,bE=wn,SE=tc.add,RE=tc.has,TE=tc.remove,EE=function(e){var r=vE(this),n=CE(e).getIterator(),i=yE(r);return bE(n,function(o){RE(r,o)?TE(i,o):SE(i,o)}),i},OE=oe,HE=EE,IE=yn;OE({target:"Set",proto:!0,real:!0,forced:!IE("symmetricDifference")},{symmetricDifference:HE});var ME=pn,xE=gr.add,_E=Ja,PE=vn,AE=wn,NE=function(e){var r=ME(this),n=PE(e).getIterator(),i=_E(r);return AE(n,function(o){xE(i,o)}),i},$E=oe,LE=NE,DE=yn;$E({target:"Set",proto:!0,real:!0,forced:!DE("union")},{union:LE});var VE=Qo,kE=TypeError,FE=function(t,e){if(VE(e,t))return t;throw new kE("Incorrect invocation")},BE=De,WE=!BE(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}),jE=Vt,zE=Me,UE=Kn,GE=Ka,KE=WE,Yu=GE("IE_PROTO"),ma=Object,YE=ma.prototype,bg=KE?ma.getPrototypeOf:function(t){var e=UE(t);if(jE(e,Yu))return e[Yu];var r=e.constructor;return zE(r)&&e instanceof r?r.prototype:e instanceof ma?YE:null},Xu=Gf,XE=Ir,qE=function(t,e,r){return r.get&&Xu(r.get,e,{getter:!0}),r.set&&Xu(r.set,e,{setter:!0}),XE.f(t,e,r)},Sg={},QE=Yf,ZE=Qa,JE=Object.keys||function(e){return QE(e,ZE)},eO=Lt,tO=Ff,rO=Ir,nO=ke,oO=qo,iO=JE;Sg.f=eO&&!tO?Object.defineProperties:function(e,r){nO(e);for(var n=oO(r),i=iO(r),o=i.length,s=0,l;o>s;)rO.f(e,l=i[s++],n[l]);return e};var sO=Gn,Rg=sO("document","documentElement"),lO=ke,aO=Sg,qu=Qa,cO=Ya,uO=Rg,hO=Ua,dO=Ka,Qu=">",Zu="<",pa="prototype",wa="script",Tg=dO("IE_PROTO"),tl=function(){},Eg=function(t){return Zu+wa+Qu+t+Zu+"/"+wa+Qu},Ju=function(t){t.write(Eg("")),t.close();var e=t.parentWindow.Object;return t=null,e},fO=function(){var t=hO("iframe"),e="java"+wa+":",r;return t.style.display="none",uO.appendChild(t),t.src=String(e),r=t.contentWindow.document,r.open(),r.write(Eg("document.F=Object")),r.close(),r.F},wi,rs=function(){try{wi=new ActiveXObject("htmlfile")}catch(e){}rs=typeof document<"u"?document.domain&&wi?Ju(wi):fO():Ju(wi);for(var t=qu.length;t--;)delete rs[pa][qu[t]];return rs()};cO[Tg]=!0;var Og=Object.create||function(e,r){var n;return e!==null?(tl[pa]=lO(e),n=new tl,tl[pa]=null,n[Tg]=e):n=rs(),r===void 0?n:aO.f(n,r)},gO=De,mO=Me,pO=Dt,eh=bg,wO=qa,vO=er,va=vO("iterator"),Hg=!1,an,rl,nl;[].keys&&(nl=[].keys(),"next"in nl?(rl=eh(eh(nl)),rl!==Object.prototype&&(an=rl)):Hg=!0);var yO=!pO(an)||gO(function(){var t={};return an[va].call(t)!==t});yO&&(an={});mO(an[va])||wO(an,va,function(){return this});var Ig={IteratorPrototype:an,BUGGY_SAFARI_ITERATORS:Hg},CO=oe,bO=Ie,SO=FE,RO=ke,TO=Me,EO=bg,OO=qE,HO=dg,IO=De,rc=Vt,MO=er,hr=Ig.IteratorPrototype,xO=Lt,ol="constructor",Mg="Iterator",th=MO("toStringTag"),xg=TypeError,il=bO[Mg],_g=!TO(il)||il.prototype!==hr||!IO(function(){il({})}),nc=function(){if(SO(this,hr),EO(this)===hr)throw new xg("Abstract class Iterator not directly constructable")},Pg=function(t,e){xO?OO(hr,t,{configurable:!0,get:function(){return e},set:function(r){if(RO(this),this===hr)throw new xg("You can't redefine this property");rc(this,t)?this[t]=r:HO(this,t,r)}}):hr[t]=e};rc(hr,th)||Pg(th,Mg);(_g||!rc(hr,ol)||hr[ol]===Object)&&Pg(ol,nc);nc.prototype=hr;CO({global:!0,constructor:!0,forced:_g},{Iterator:nc});var _O=qa,PO=function(t,e,r){for(var n in e)_O(t,n,e[n],r);return t},AO=function(t,e){return{value:t,done:e}},NO=Ve,$O=Og,LO=Yn,DO=PO,VO=er,Ag=zf,kO=Zo,FO=Ig.IteratorPrototype,vi=AO,sl=kt,BO=VO("toStringTag"),Ng="IteratorHelper",$g="WrapForValidIterator",WO=Ag.set,Lg=function(t){var e=Ag.getterFor(t?$g:Ng);return DO($O(FO),{next:function(){var n=e(this);if(t)return n.nextHandler();if(n.done)return vi(void 0,!0);try{var i=n.nextHandler();return n.returnHandlerResult?i:vi(i,n.done)}catch(o){throw n.done=!0,o}},return:function(){var r=e(this),n=r.iterator;if(r.done=!0,t){var i=kO(n,"return");return i?NO(i,n):vi(void 0,!0)}if(r.inner)try{sl(r.inner.iterator,"normal")}catch(o){return sl(n,"throw",o)}return n&&sl(n,"normal"),vi(void 0,!0)}})},jO=Lg(!0),Dg=Lg(!1);LO(Dg,BO,"Iterator Helper");var Vg=function(t,e,r){var n=function(o,s){s?(s.iterator=o.iterator,s.next=o.next):s=o,s.type=e?$g:Ng,s.returnHandlerResult=!!r,s.nextHandler=t,s.counter=0,s.done=!1,WO(this,s)};return n.prototype=e?jO:Dg,n},zO=ke,UO=kt,kg=function(t,e,r,n){try{return n?e(zO(r)[0],r[1]):e(r)}catch(i){UO(t,"throw",i)}},GO=Ie,Cn=function(t,e){var r=GO.Iterator,n=r&&r.prototype,i=n&&n[t],o=!1;if(i)try{i.call({next:function(){return{done:!0}},return:function(){o=!0}},-1)}catch(s){s instanceof e||(o=!1)}if(!o)return i},KO=oe,Fg=Ve,YO=bt,Bg=ke,XO=jr,qO=Vg,QO=kg,ZO=kt,JO=Cn,ll=JO("filter",TypeError),e0=qO(function(){for(var t=this.iterator,e=this.predicate,r=this.next,n,i,o;;){if(n=Bg(Fg(r,t)),i=this.done=!!n.done,i)return;if(o=n.value,QO(t,e,[o,this.counter++],!0))return o}});KO({target:"Iterator",proto:!0,real:!0,forced:ll},{filter:function(e){Bg(this);try{YO(e)}catch(r){ZO(this,"throw",r)}return ll?Fg(ll,this,e):new e0(XO(this),{predicate:e})}});var t0=oe,r0=Ve,n0=qn,o0=bt,i0=ke,s0=jr,l0=kt,a0=Cn,al=a0("forEach",TypeError);t0({target:"Iterator",proto:!0,real:!0,forced:al},{forEach:function(e){i0(this);try{o0(e)}catch(i){l0(this,"throw",i)}if(al)return r0(al,this,e);var r=s0(this),n=0;n0(r,function(i){e(i,n++)},{IS_RECORD:!0})}});var c0=oe,Wg=Ve,u0=bt,jg=ke,h0=jr,d0=Vg,f0=kg,g0=kt,m0=Cn,cl=m0("map",TypeError),p0=d0(function(){var t=this.iterator,e=jg(Wg(this.next,t)),r=this.done=!!e.done;if(!r)return f0(t,this.mapper,[e.value,this.counter++],!0)});c0({target:"Iterator",proto:!0,real:!0,forced:cl},{map:function(e){jg(this);try{u0(e)}catch(r){g0(this,"throw",r)}return cl?Wg(cl,this,e):new p0(h0(this),{mapper:e})}});var w0=be,oc=w0([].slice),v0=TypeError,zg=function(t,e){if(t<e)throw new v0("Not enough arguments");return t},y0=Ts,C0=/(?:ipad|iphone|ipod).*applewebkit/i.test(y0),oo=Ie,b0=Ts,S0=gn,yi=function(t){return b0.slice(0,t.length)===t},Ug=function(){return yi("Bun/")?"BUN":yi("Cloudflare-Workers")?"CLOUDFLARE":yi("Deno/")?"DENO":yi("Node.js/")?"NODE":oo.Bun&&typeof Bun.version=="string"?"BUN":oo.Deno&&typeof Deno.version=="object"?"DENO":S0(oo.process)==="process"?"NODE":oo.window&&oo.document?"BROWSER":"REST"}(),R0=Ug,T0=R0==="NODE",pt=Ie,E0=ti,O0=cg,rh=Me,H0=Vt,Gg=De,nh=Rg,I0=oc,oh=Ua,M0=zg,x0=C0,_0=T0,ya=pt.setImmediate,Ca=pt.clearImmediate,P0=pt.process,ul=pt.Dispatch,A0=pt.Function,ih=pt.MessageChannel,N0=pt.String,hl=0,$o={},sh="onreadystatechange",zo,Yr,dl,fl;Gg(function(){zo=pt.location});var ic=function(t){if(H0($o,t)){var e=$o[t];delete $o[t],e()}},gl=function(t){return function(){ic(t)}},lh=function(t){ic(t.data)},ah=function(t){pt.postMessage(N0(t),zo.protocol+"//"+zo.host)};(!ya||!Ca)&&(ya=function(e){M0(arguments.length,1);var r=rh(e)?e:A0(e),n=I0(arguments,1);return $o[++hl]=function(){E0(r,void 0,n)},Yr(hl),hl},Ca=function(e){delete $o[e]},_0?Yr=function(t){P0.nextTick(gl(t))}:ul&&ul.now?Yr=function(t){ul.now(gl(t))}:ih&&!x0?(dl=new ih,fl=dl.port2,dl.port1.onmessage=lh,Yr=O0(fl.postMessage,fl)):pt.addEventListener&&rh(pt.postMessage)&&!pt.importScripts&&zo&&zo.protocol!=="file:"&&!Gg(ah)?(Yr=ah,pt.addEventListener("message",lh,!1)):sh in oh("script")?Yr=function(t){nh.appendChild(oh("script"))[sh]=function(){nh.removeChild(this),ic(t)}}:Yr=function(t){setTimeout(gl(t),0)});var Kg={set:ya,clear:Ca},$0=oe,L0=Ie,ch=Kg.clear;$0({global:!0,bind:!0,enumerable:!0,forced:L0.clearImmediate!==ch},{clearImmediate:ch});var Yg=Ie,D0=ti,V0=Me,k0=Ug,F0=Ts,B0=oc,W0=zg,j0=Yg.Function,z0=/MSIE .\./.test(F0)||k0==="BUN"&&function(){var t=Yg.Bun.version.split(".");return t.length<3||t[0]==="0"&&(t[1]<3||t[1]==="3"&&t[2]==="0")}(),U0=function(t,e){var r=e?2:1;return z0?function(n,i){var o=W0(arguments.length,1)>r,s=V0(n)?n:j0(n),l=o?B0(arguments,r):[],a=o?function(){D0(s,this,l)}:s;return e?t(a,i):t(a)}:t},G0=oe,Xg=Ie,uh=Kg.set,K0=U0,hh=Xg.setImmediate?K0(uh,!1):uh;G0({global:!0,bind:!0,enumerable:!0,forced:Xg.setImmediate!==hh},{setImmediate:hh});/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:qg,setPrototypeOf:dh,isFrozen:Y0,getPrototypeOf:X0,getOwnPropertyDescriptor:q0}=Object;let{freeze:Qe,seal:$t,create:Qg}=Object,{apply:ba,construct:Sa}=typeof Reflect<"u"&&Reflect;Qe||(Qe=function(e){return e});$t||($t=function(e){return e});ba||(ba=function(e,r,n){return e.apply(r,n)});Sa||(Sa=function(e,r){return new e(...r)});const Ci=Ze(Array.prototype.forEach),Q0=Ze(Array.prototype.lastIndexOf),fh=Ze(Array.prototype.pop),io=Ze(Array.prototype.push),Z0=Ze(Array.prototype.splice),ns=Ze(String.prototype.toLowerCase),ml=Ze(String.prototype.toString),gh=Ze(String.prototype.match),so=Ze(String.prototype.replace),J0=Ze(String.prototype.indexOf),eH=Ze(String.prototype.trim),Xt=Ze(Object.prototype.hasOwnProperty),Xe=Ze(RegExp.prototype.test),lo=tH(TypeError);function Ze(t){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];return ba(t,e,n)}}function tH(t){return function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return Sa(t,r)}}function Y(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:ns;dh&&dh(t,null);let n=e.length;for(;n--;){let i=e[n];if(typeof i=="string"){const o=r(i);o!==i&&(Y0(e)||(e[n]=o),i=o)}t[i]=!0}return t}function rH(t){for(let e=0;e<t.length;e++)Xt(t,e)||(t[e]=null);return t}function Er(t){const e=Qg(null);for(const[r,n]of qg(t))Xt(t,r)&&(Array.isArray(n)?e[r]=rH(n):n&&typeof n=="object"&&n.constructor===Object?e[r]=Er(n):e[r]=n);return e}function ao(t,e){for(;t!==null;){const n=q0(t,e);if(n){if(n.get)return Ze(n.get);if(typeof n.value=="function")return Ze(n.value)}t=X0(t)}function r(){return null}return r}const mh=Qe(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),pl=Qe(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),wl=Qe(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),nH=Qe(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),vl=Qe(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),oH=Qe(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),ph=Qe(["#text"]),wh=Qe(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),yl=Qe(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),vh=Qe(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),bi=Qe(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),iH=$t(/\{\{[\w\W]*|[\w\W]*\}\}/gm),sH=$t(/<%[\w\W]*|[\w\W]*%>/gm),lH=$t(/\$\{[\w\W]*/gm),aH=$t(/^data-[\-\w.\u00B7-\uFFFF]+$/),cH=$t(/^aria-[\-\w]+$/),Zg=$t(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),uH=$t(/^(?:\w+script|data):/i),hH=$t(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Jg=$t(/^html$/i),dH=$t(/^[a-z][.\w]*(-[.\w]+)+$/i);var yh=Object.freeze({__proto__:null,ARIA_ATTR:cH,ATTR_WHITESPACE:hH,CUSTOM_ELEMENT:dH,DATA_ATTR:aH,DOCTYPE_NAME:Jg,ERB_EXPR:sH,IS_ALLOWED_URI:Zg,IS_SCRIPT_OR_DATA:uH,MUSTACHE_EXPR:iH,TMPLIT_EXPR:lH});const co={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},fH=function(){return typeof window>"u"?null:window},gH=function(e,r){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let n=null;const i="data-tt-policy-suffix";r&&r.hasAttribute(i)&&(n=r.getAttribute(i));const o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML(s){return s},createScriptURL(s){return s}})}catch(s){return console.warn("TrustedTypes policy "+o+" could not be created."),null}},Ch=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function em(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:fH();const e=N=>em(N);if(e.version="3.2.6",e.removed=[],!t||!t.document||t.document.nodeType!==co.document||!t.Element)return e.isSupported=!1,e;let{document:r}=t;const n=r,i=n.currentScript,{DocumentFragment:o,HTMLTemplateElement:s,Node:l,Element:a,NodeFilter:c,NamedNodeMap:d=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:f,DOMParser:m,trustedTypes:p}=t,v=a.prototype,R=ao(v,"cloneNode"),H=ao(v,"remove"),M=ao(v,"nextSibling"),b=ao(v,"childNodes"),k=ao(v,"parentNode");if(typeof s=="function"){const N=r.createElement("template");N.content&&N.content.ownerDocument&&(r=N.content.ownerDocument)}let $,W="";const{implementation:D,createNodeIterator:F,createDocumentFragment:ve,getElementsByTagName:V}=r,{importNode:St}=n;let te=Ch();e.isSupported=typeof qg=="function"&&typeof k=="function"&&D&&D.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:Rt,ERB_EXPR:Ye,TMPLIT_EXPR:Ft,DATA_ATTR:Bt,ARIA_ATTR:Wt,IS_SCRIPT_OR_DATA:ue,ATTR_WHITESPACE:ye,CUSTOM_ELEMENT:xe}=yh;let{IS_ALLOWED_URI:mr}=yh,J=null;const u=Y({},[...mh,...pl,...wl,...vl,...ph]);let h=null;const g=Y({},[...wh,...yl,...vh,...bi]);let w=Object.seal(Qg(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),C=null,S=null,E=!0,O=!0,A=!1,x=!0,T=!1,j=!0,K=!1,Q=!1,Re=!1,ge=!1,ie=!1,Je=!1,se=!0,_e=!1;const ft="user-content-";let jt=!0,Tt=!1,Be={},We=null;const Et=Y({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let xr=null;const _r=Y({},["audio","video","img","source","image","track"]);let Sn=null;const eo=Y({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Pr="http://www.w3.org/1998/Math/MathML",et="http://www.w3.org/2000/svg",tt="http://www.w3.org/1999/xhtml";let gt=tt,pr=!1,rt=null;const Ot=Y({},[Pr,et,tt],ml);let Gr=Y({},["mi","mo","mn","ms","mtext"]),Kr=Y({},["annotation-xml"]);const yv=Y({},["title","style","font","a","script"]);let to=null;const Cv=["application/xhtml+xml","text/html"],bv="text/html";let Ne=null,Rn=null;const Sv=r.createElement("form"),Fc=function(y){return y instanceof RegExp||y instanceof Function},Ls=function(){let y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(Rn&&Rn===y)){if((!y||typeof y!="object")&&(y={}),y=Er(y),to=Cv.indexOf(y.PARSER_MEDIA_TYPE)===-1?bv:y.PARSER_MEDIA_TYPE,Ne=to==="application/xhtml+xml"?ml:ns,J=Xt(y,"ALLOWED_TAGS")?Y({},y.ALLOWED_TAGS,Ne):u,h=Xt(y,"ALLOWED_ATTR")?Y({},y.ALLOWED_ATTR,Ne):g,rt=Xt(y,"ALLOWED_NAMESPACES")?Y({},y.ALLOWED_NAMESPACES,ml):Ot,Sn=Xt(y,"ADD_URI_SAFE_ATTR")?Y(Er(eo),y.ADD_URI_SAFE_ATTR,Ne):eo,xr=Xt(y,"ADD_DATA_URI_TAGS")?Y(Er(_r),y.ADD_DATA_URI_TAGS,Ne):_r,We=Xt(y,"FORBID_CONTENTS")?Y({},y.FORBID_CONTENTS,Ne):Et,C=Xt(y,"FORBID_TAGS")?Y({},y.FORBID_TAGS,Ne):Er({}),S=Xt(y,"FORBID_ATTR")?Y({},y.FORBID_ATTR,Ne):Er({}),Be=Xt(y,"USE_PROFILES")?y.USE_PROFILES:!1,E=y.ALLOW_ARIA_ATTR!==!1,O=y.ALLOW_DATA_ATTR!==!1,A=y.ALLOW_UNKNOWN_PROTOCOLS||!1,x=y.ALLOW_SELF_CLOSE_IN_ATTR!==!1,T=y.SAFE_FOR_TEMPLATES||!1,j=y.SAFE_FOR_XML!==!1,K=y.WHOLE_DOCUMENT||!1,ge=y.RETURN_DOM||!1,ie=y.RETURN_DOM_FRAGMENT||!1,Je=y.RETURN_TRUSTED_TYPE||!1,Re=y.FORCE_BODY||!1,se=y.SANITIZE_DOM!==!1,_e=y.SANITIZE_NAMED_PROPS||!1,jt=y.KEEP_CONTENT!==!1,Tt=y.IN_PLACE||!1,mr=y.ALLOWED_URI_REGEXP||Zg,gt=y.NAMESPACE||tt,Gr=y.MATHML_TEXT_INTEGRATION_POINTS||Gr,Kr=y.HTML_INTEGRATION_POINTS||Kr,w=y.CUSTOM_ELEMENT_HANDLING||{},y.CUSTOM_ELEMENT_HANDLING&&Fc(y.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(w.tagNameCheck=y.CUSTOM_ELEMENT_HANDLING.tagNameCheck),y.CUSTOM_ELEMENT_HANDLING&&Fc(y.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(w.attributeNameCheck=y.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),y.CUSTOM_ELEMENT_HANDLING&&typeof y.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(w.allowCustomizedBuiltInElements=y.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),T&&(O=!1),ie&&(ge=!0),Be&&(J=Y({},ph),h=[],Be.html===!0&&(Y(J,mh),Y(h,wh)),Be.svg===!0&&(Y(J,pl),Y(h,yl),Y(h,bi)),Be.svgFilters===!0&&(Y(J,wl),Y(h,yl),Y(h,bi)),Be.mathMl===!0&&(Y(J,vl),Y(h,vh),Y(h,bi))),y.ADD_TAGS&&(J===u&&(J=Er(J)),Y(J,y.ADD_TAGS,Ne)),y.ADD_ATTR&&(h===g&&(h=Er(h)),Y(h,y.ADD_ATTR,Ne)),y.ADD_URI_SAFE_ATTR&&Y(Sn,y.ADD_URI_SAFE_ATTR,Ne),y.FORBID_CONTENTS&&(We===Et&&(We=Er(We)),Y(We,y.FORBID_CONTENTS,Ne)),jt&&(J["#text"]=!0),K&&Y(J,["html","head","body"]),J.table&&(Y(J,["tbody"]),delete C.tbody),y.TRUSTED_TYPES_POLICY){if(typeof y.TRUSTED_TYPES_POLICY.createHTML!="function")throw lo('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof y.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw lo('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');$=y.TRUSTED_TYPES_POLICY,W=$.createHTML("")}else $===void 0&&($=gH(p,i)),$!==null&&typeof W=="string"&&(W=$.createHTML(""));Qe&&Qe(y),Rn=y}},Bc=Y({},[...pl,...wl,...nH]),Wc=Y({},[...vl,...oH]),Rv=function(y){let I=k(y);(!I||!I.tagName)&&(I={namespaceURI:gt,tagName:"template"});const P=ns(y.tagName),he=ns(I.tagName);return rt[y.namespaceURI]?y.namespaceURI===et?I.namespaceURI===tt?P==="svg":I.namespaceURI===Pr?P==="svg"&&(he==="annotation-xml"||Gr[he]):!!Bc[P]:y.namespaceURI===Pr?I.namespaceURI===tt?P==="math":I.namespaceURI===et?P==="math"&&Kr[he]:!!Wc[P]:y.namespaceURI===tt?I.namespaceURI===et&&!Kr[he]||I.namespaceURI===Pr&&!Gr[he]?!1:!Wc[P]&&(yv[P]||!Bc[P]):!!(to==="application/xhtml+xml"&&rt[y.namespaceURI]):!1},tr=function(y){io(e.removed,{element:y});try{k(y).removeChild(y)}catch(I){H(y)}},Tn=function(y,I){try{io(e.removed,{attribute:I.getAttributeNode(y),from:I})}catch(P){io(e.removed,{attribute:null,from:I})}if(I.removeAttribute(y),y==="is")if(ge||ie)try{tr(I)}catch(P){}else try{I.setAttribute(y,"")}catch(P){}},jc=function(y){let I=null,P=null;if(Re)y="<remove></remove>"+y;else{const Pe=gh(y,/^[\r\n\t ]+/);P=Pe&&Pe[0]}to==="application/xhtml+xml"&&gt===tt&&(y='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+y+"</body></html>");const he=$?$.createHTML(y):y;if(gt===tt)try{I=new m().parseFromString(he,to)}catch(Pe){}if(!I||!I.documentElement){I=D.createDocument(gt,"template",null);try{I.documentElement.innerHTML=pr?W:he}catch(Pe){}}const je=I.body||I.documentElement;return y&&P&&je.insertBefore(r.createTextNode(P),je.childNodes[0]||null),gt===tt?V.call(I,K?"html":"body")[0]:K?I.documentElement:je},zc=function(y){return F.call(y.ownerDocument||y,y,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT|c.SHOW_PROCESSING_INSTRUCTION|c.SHOW_CDATA_SECTION,null)},Ds=function(y){return y instanceof f&&(typeof y.nodeName!="string"||typeof y.textContent!="string"||typeof y.removeChild!="function"||!(y.attributes instanceof d)||typeof y.removeAttribute!="function"||typeof y.setAttribute!="function"||typeof y.namespaceURI!="string"||typeof y.insertBefore!="function"||typeof y.hasChildNodes!="function")},Uc=function(y){return typeof l=="function"&&y instanceof l};function wr(N,y,I){Ci(N,P=>{P.call(e,y,I,Rn)})}const Gc=function(y){let I=null;if(wr(te.beforeSanitizeElements,y,null),Ds(y))return tr(y),!0;const P=Ne(y.nodeName);if(wr(te.uponSanitizeElement,y,{tagName:P,allowedTags:J}),j&&y.hasChildNodes()&&!Uc(y.firstElementChild)&&Xe(/<[/\w!]/g,y.innerHTML)&&Xe(/<[/\w!]/g,y.textContent)||y.nodeType===co.progressingInstruction||j&&y.nodeType===co.comment&&Xe(/<[/\w]/g,y.data))return tr(y),!0;if(!J[P]||C[P]){if(!C[P]&&Yc(P)&&(w.tagNameCheck instanceof RegExp&&Xe(w.tagNameCheck,P)||w.tagNameCheck instanceof Function&&w.tagNameCheck(P)))return!1;if(jt&&!We[P]){const he=k(y)||y.parentNode,je=b(y)||y.childNodes;if(je&&he){const Pe=je.length;for(let nt=Pe-1;nt>=0;--nt){const vr=R(je[nt],!0);vr.__removalCount=(y.__removalCount||0)+1,he.insertBefore(vr,M(y))}}}return tr(y),!0}return y instanceof a&&!Rv(y)||(P==="noscript"||P==="noembed"||P==="noframes")&&Xe(/<\/no(script|embed|frames)/i,y.innerHTML)?(tr(y),!0):(T&&y.nodeType===co.text&&(I=y.textContent,Ci([Rt,Ye,Ft],he=>{I=so(I,he," ")}),y.textContent!==I&&(io(e.removed,{element:y.cloneNode()}),y.textContent=I)),wr(te.afterSanitizeElements,y,null),!1)},Kc=function(y,I,P){if(se&&(I==="id"||I==="name")&&(P in r||P in Sv))return!1;if(!(O&&!S[I]&&Xe(Bt,I))){if(!(E&&Xe(Wt,I))){if(!h[I]||S[I]){if(!(Yc(y)&&(w.tagNameCheck instanceof RegExp&&Xe(w.tagNameCheck,y)||w.tagNameCheck instanceof Function&&w.tagNameCheck(y))&&(w.attributeNameCheck instanceof RegExp&&Xe(w.attributeNameCheck,I)||w.attributeNameCheck instanceof Function&&w.attributeNameCheck(I))||I==="is"&&w.allowCustomizedBuiltInElements&&(w.tagNameCheck instanceof RegExp&&Xe(w.tagNameCheck,P)||w.tagNameCheck instanceof Function&&w.tagNameCheck(P))))return!1}else if(!Sn[I]){if(!Xe(mr,so(P,ye,""))){if(!((I==="src"||I==="xlink:href"||I==="href")&&y!=="script"&&J0(P,"data:")===0&&xr[y])){if(!(A&&!Xe(ue,so(P,ye,"")))){if(P)return!1}}}}}}return!0},Yc=function(y){return y!=="annotation-xml"&&gh(y,xe)},Xc=function(y){wr(te.beforeSanitizeAttributes,y,null);const{attributes:I}=y;if(!I||Ds(y))return;const P={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:h,forceKeepAttr:void 0};let he=I.length;for(;he--;){const je=I[he],{name:Pe,namespaceURI:nt,value:vr}=je,ro=Ne(Pe),Vs=vr;let ze=Pe==="value"?Vs:eH(Vs);if(P.attrName=ro,P.attrValue=ze,P.keepAttr=!0,P.forceKeepAttr=void 0,wr(te.uponSanitizeAttribute,y,P),ze=P.attrValue,_e&&(ro==="id"||ro==="name")&&(Tn(Pe,y),ze=ft+ze),j&&Xe(/((--!?|])>)|<\/(style|title)/i,ze)){Tn(Pe,y);continue}if(P.forceKeepAttr)continue;if(!P.keepAttr){Tn(Pe,y);continue}if(!x&&Xe(/\/>/i,ze)){Tn(Pe,y);continue}T&&Ci([Rt,Ye,Ft],Qc=>{ze=so(ze,Qc," ")});const qc=Ne(y.nodeName);if(!Kc(qc,ro,ze)){Tn(Pe,y);continue}if($&&typeof p=="object"&&typeof p.getAttributeType=="function"&&!nt)switch(p.getAttributeType(qc,ro)){case"TrustedHTML":{ze=$.createHTML(ze);break}case"TrustedScriptURL":{ze=$.createScriptURL(ze);break}}if(ze!==Vs)try{nt?y.setAttributeNS(nt,Pe,ze):y.setAttribute(Pe,ze),Ds(y)?tr(y):fh(e.removed)}catch(Qc){Tn(Pe,y)}}wr(te.afterSanitizeAttributes,y,null)},Tv=function N(y){let I=null;const P=zc(y);for(wr(te.beforeSanitizeShadowDOM,y,null);I=P.nextNode();)wr(te.uponSanitizeShadowNode,I,null),Gc(I),Xc(I),I.content instanceof o&&N(I.content);wr(te.afterSanitizeShadowDOM,y,null)};return e.sanitize=function(N){let y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},I=null,P=null,he=null,je=null;if(pr=!N,pr&&(N="<!-->"),typeof N!="string"&&!Uc(N))if(typeof N.toString=="function"){if(N=N.toString(),typeof N!="string")throw lo("dirty is not a string, aborting")}else throw lo("toString is not a function");if(!e.isSupported)return N;if(Q||Ls(y),e.removed=[],typeof N=="string"&&(Tt=!1),Tt){if(N.nodeName){const vr=Ne(N.nodeName);if(!J[vr]||C[vr])throw lo("root node is forbidden and cannot be sanitized in-place")}}else if(N instanceof l)I=jc("<!---->"),P=I.ownerDocument.importNode(N,!0),P.nodeType===co.element&&P.nodeName==="BODY"||P.nodeName==="HTML"?I=P:I.appendChild(P);else{if(!ge&&!T&&!K&&N.indexOf("<")===-1)return $&&Je?$.createHTML(N):N;if(I=jc(N),!I)return ge?null:Je?W:""}I&&Re&&tr(I.firstChild);const Pe=zc(Tt?N:I);for(;he=Pe.nextNode();)Gc(he),Xc(he),he.content instanceof o&&Tv(he.content);if(Tt)return N;if(ge){if(ie)for(je=ve.call(I.ownerDocument);I.firstChild;)je.appendChild(I.firstChild);else je=I;return(h.shadowroot||h.shadowrootmode)&&(je=St.call(n,je,!0)),je}let nt=K?I.outerHTML:I.innerHTML;return K&&J["!doctype"]&&I.ownerDocument&&I.ownerDocument.doctype&&I.ownerDocument.doctype.name&&Xe(Jg,I.ownerDocument.doctype.name)&&(nt="<!DOCTYPE "+I.ownerDocument.doctype.name+">\n"+nt),T&&Ci([Rt,Ye,Ft],vr=>{nt=so(nt,vr," ")}),$&&Je?$.createHTML(nt):nt},e.setConfig=function(){let N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Ls(N),Q=!0},e.clearConfig=function(){Rn=null,Q=!1},e.isValidAttribute=function(N,y,I){Rn||Ls({});const P=Ne(N),he=Ne(y);return Kc(P,he,I)},e.addHook=function(N,y){typeof y=="function"&&io(te[N],y)},e.removeHook=function(N,y){if(y!==void 0){const I=Q0(te[N],y);return I===-1?void 0:Z0(te[N],I,1)[0]}return fh(te[N])},e.removeHooks=function(N){te[N]=[]},e.removeAllHooks=function(){te=Ch()},e}var mH=em();function pH(t){const e=t.length;let r=0;for(;r<e;)t[r]=[t[r]],r+=1}function bh(t,e){const r=e.length;let n=0;for(;n<r;)t.push(e[n]),n+=1}function Cl(t){const e=[];if(!t||t.length===0||!t[0]||t[0].length===0)return e;const r=t.length,n=t[0].length;for(let i=0;i<r;i++)for(let o=0;o<n;o++)e[o]||(e[o]=[]),e[o][i]=t[i][o];return e}function Hs(t,e,r,n){let i=-1,o=t,s=r;Array.isArray(t)||(o=Array.from(t));const l=o.length;for(n&&l&&(i+=1,s=o[i]),i+=1;i<l;)s=e(s,o[i],i,o),i+=1;return s}function tm(t,e){let r=0,n=t;Array.isArray(t)||(n=Array.from(t));const i=n.length,o=[];let s=-1;for(;r<i;){const l=n[r];e(l,r,n)&&(s+=1,o[s]=l),r+=1}return o}function cn(t,e){let r=0,n=t;Array.isArray(t)||(n=Array.from(t));const i=n.length,o=[];let s=-1;for(;r<i;){const l=n[r];s+=1,o[s]=e(l,r,n),r+=1}return o}function U(t,e){let r=0,n=t;Array.isArray(t)||(n=Array.from(t));const i=n.length;for(;r<i&&e(n[r],r,n)!==!1;)r+=1;return t}function Sh(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const[n,...i]=[...e];let o=n;return U(i,s=>{o=o.filter(l=>!s.includes(l))}),o}function Rh(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:" ";return t.split(e)}function vt(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];return Hs(t,(o,s,l)=>{const a=s.replace(/\r?\n\s*/g,""),c=r[l]?r[l]:"";return o+a+c},"").trim()}function Lo(t){let e;switch(typeof t){case"string":case"number":e="".concat(t);break;case"object":e=t===null?"":t.toString();break;case"undefined":e="";break;default:e=t.toString();break}return e}function X(t){return typeof t<"u"}function ct(t){return typeof t>"u"}function jn(t){return t===null||t===""||ct(t)}function wH(t){return Object.prototype.toString.call(t)==="[object RegExp]"}const xo="length",Do=t=>parseInt(t,16),Th=t=>parseInt(t,10),ds=(t,e,r)=>t.substr(e,r),Vo=t=>t.codePointAt(0)-65,Ra=t=>"".concat(t).replace(/\-/g,""),vH=t=>Do(ds(Ra(t),Do("12"),Vo("F")))/(Do(ds(Ra(t),Vo("B"),~~![][xo]))||9),yH=()=>typeof location<"u"&&/^([a-z0-9\-]+\.)?\x68\x61\x6E\x64\x73\x6F\x6E\x74\x61\x62\x6C\x65\x2E\x63\x6F\x6D$/i.test(location.host);let Eh=!1;var uf,hf,df;const Oh={invalid:()=>vt(uf||(uf=ot(["\n    The license key for Handsontable is invalid. \n    If you need any help, contact <NAME_EMAIL>."],["\n    The license key for Handsontable is invalid.\\x20\n    If you need any help, contact <NAME_EMAIL>."]))),expired:t=>{let{keyValidityDate:e,hotVersion:r}=t;return vt(hf||(hf=ot(["\n    The license key for Handsontable expired on ",", and is not valid for the installed \n    version ",". Renew your license key at handsontable.com or downgrade to a version released prior \n    to ",". If you need any help, contact <NAME_EMAIL>."],["\n    The license key for Handsontable expired on ",", and is not valid for the installed\\x20\n    version ",". Renew your license key at handsontable.com or downgrade to a version released prior\\x20\n    to ",". If you need any help, contact <NAME_EMAIL>."])),e,r,e)},missing:()=>vt(df||(df=ot(["\n    The license key for Handsontable is missing. Use your purchased key to activate the product. \n    Alternatively, you can activate Handsontable to use for non-commercial purposes by \n    passing the key: 'non-commercial-and-evaluation'. If you need any help, contact \n    <NAME_EMAIL>."],["\n    The license key for Handsontable is missing. Use your purchased key to activate the product.\\x20\n    Alternatively, you can activate Handsontable to use for non-commercial purposes by\\x20\n    passing the key: 'non-commercial-and-evaluation'. If you need any help, contact\\x20\n    <NAME_EMAIL>."]))),non_commercial:()=>""};var ff,gf,mf;const Hh={invalid:()=>vt(ff||(ff=ot(['\n    The license key for Handsontable is invalid. \n    <a href="https://handsontable.com/docs/tutorial-license-key.html" target="_blank">Read more</a> on how to \n    install it properly or contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.'],['\n    The license key for Handsontable is invalid.\\x20\n    <a href="https://handsontable.com/docs/tutorial-license-key.html" target="_blank">Read more</a> on how to\\x20\n    install it properly or contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.']))),expired:t=>{let{keyValidityDate:e,hotVersion:r}=t;return vt(gf||(gf=ot(["\n    The license key for Handsontable expired on ",", and is not valid for the installed \n    version ",'. <a href="https://handsontable.com/pricing" target="_blank">Renew</a> your \n    license key or downgrade to a version released prior to ','. If you need any \n    help, contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.'],["\n    The license key for Handsontable expired on ",", and is not valid for the installed\\x20\n    version ",'. <a href="https://handsontable.com/pricing" target="_blank">Renew</a> your\\x20\n    license key or downgrade to a version released prior to ','. If you need any\\x20\n    help, contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.'])),e,r,e)},missing:()=>vt(mf||(mf=ot(['\n    The license key for Handsontable is missing. Use your purchased key to activate the product. \n    Alternatively, you can activate Handsontable to use for non-commercial purposes by \n    passing the key: \'non-commercial-and-evaluation\'. \n    <a href="https://handsontable.com/docs/tutorial-license-key.html" target="_blank">Read more</a> about it in \n    the documentation or contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.'],['\n    The license key for Handsontable is missing. Use your purchased key to activate the product.\\x20\n    Alternatively, you can activate Handsontable to use for non-commercial purposes by\\x20\n    passing the key: \'non-commercial-and-evaluation\'.\\x20\n    <a href="https://handsontable.com/docs/tutorial-license-key.html" target="_blank">Read more</a> about it in\\x20\n    the documentation or contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.']))),non_commercial:()=>""};function CH(t,e){const r=!jn(t),n=typeof t=="string"&&t.toLowerCase()==="non-commercial-and-evaluation",i="15.3.0";let o,s="invalid",l="invalid";t=Ra(t||"");const a=bH(t);if(r||n||a)if(a){const c=Jc("29/04/2025","DD/MM/YYYY"),d=Math.floor(c.toDate().getTime()/864e5),f=vH(t);o=Jc((f+1)*864e5,"x").format("MMMM DD, YYYY"),d>f?(s="expired",l="expired"):(s="valid",l="valid")}else n?(s="non_commercial",l="valid"):(s="invalid",l="invalid");else s="missing",l="missing";if(yH()&&(s="valid",l="valid"),!Eh&&s!=="valid"&&(Oh[s]({keyValidityDate:o,hotVersion:i})&&console[s==="non_commercial"?"info":"warn"](Oh[s]({keyValidityDate:o,hotVersion:i})),Eh=!0),l!=="valid"&&e.parentNode&&Hh[l]({keyValidityDate:o,hotVersion:i})){const d=document.createElement("div");d.className="handsontable hot-display-license-info",d.innerHTML=Hh[l]({keyValidityDate:o,hotVersion:i}),e.parentNode.insertBefore(d,e.nextSibling)}}function bH(t){let e=[][xo],r=e;if(t[xo]!==Vo("Z"))return!1;for(let n="",i="B<H4P+".split(""),o=Vo(i.shift());o;o=Vo(i.shift()||"A"))--o<""[xo]?r=r|(Th("".concat(Th(Do(n)+(Do(ds(t,Math.abs(o),2))+[]).padStart(2,"0"))))%97||2)>>1:n=ds(t,o,o?i[xo]===1?9:8:6);return r===e}function sc(t){return t[0].toUpperCase()+t.substr(1)}function rm(){function t(){return Math.floor((1+Math.random())*65536).toString(16).substring(1)}return t()+t()+t()+t()}function SH(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return"".concat(t).replace(/(?:\\)?\[([^[\]]+)]/g,(r,n)=>r.charAt(0)==="\\"?r.substr(1,r.length-1):e[n]===void 0?"":e[n])}function RH(t,e){return mH.sanitize(t,e)}const oi=t=>["tabindex",t],TH=()=>["role","treegrid"],Vr=()=>["role","presentation"],EH=()=>["role","gridcell"],OH=()=>["role","rowheader"],nm=()=>["role","rowgroup"],HH=()=>["role","columnheader"],Ta=()=>["role","row"],IH=()=>["scope","col"],MH=()=>["scope","row"],xH=t=>["aria-label",t],_H=()=>["aria-multiselectable","true"],om=t=>["aria-rowcount",t],Is=t=>["aria-colcount",t],im=t=>["aria-rowindex",t],lc=t=>["aria-colindex",t],Ih=()=>["aria-readonly","true"],Mh=()=>["aria-invalid","true"],sm=()=>["aria-selected","true"];function PH(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=null,i=t;for(;i!==null;){if(r===e){n=i;break}i.host&&i.nodeType===Node.DOCUMENT_FRAGMENT_NODE?i=i.host:(r+=1,i=i.parentNode)}return n}function AH(t,e){const r=t.closest(".handsontable");return!!r&&(r.parentNode===e||r===e)}function NH(t){return Object.getPrototypeOf(t.parent)&&t.frameElement}function fs(t){return NH(t)&&t.parent}function lm(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0;const{ELEMENT_NODE:n,DOCUMENT_FRAGMENT_NODE:i}=Node;let o=t;for(;o!=null&&o!==r;){const{nodeType:s,nodeName:l}=o;if(s===n&&(e.includes(l)||e.includes(o)))return o;const{host:a}=o;a&&s===i?o=a:o=o.parentNode}return null}function Si(t,e,r){const n=[];let i=t;for(;i&&(i=lm(i,e,r),!(!i||r&&!r.contains(i)));)n.push(i),i.host&&i.nodeType===Node.DOCUMENT_FRAGMENT_NODE?i=i.host:i=i.parentNode;const o=n.length;return o?n[o-1]:null}function $H(t,e){const r={element:void 0,classNames:[]};let n=t;for(;n!==null&&n!==t.ownerDocument.documentElement&&!r.element;){if(typeof e=="string"&&n.classList.contains(e))r.element=n,r.classNames.push(e);else if(e instanceof RegExp){const i=Array.from(n.classList).filter(o=>e.test(o));i.length&&(r.element=n,r.classNames.push(...i))}n=n.parentElement}return r}function Ea(t,e){let r=t.parentNode,n=[];for(typeof e=="string"?t.defaultView?n=Array.prototype.slice.call(t.querySelectorAll(e),0):n=Array.prototype.slice.call(t.ownerDocument.querySelectorAll(e),0):n.push(e);r!==null;){if(n.indexOf(r)>-1)return!0;r=r.parentNode}return!1}function LH(t){let e=0,r=t;if(r.previousSibling)for(;r=r.previousSibling;)e+=1;return e}function Xr(t,e,r){const n=r.parentElement.querySelector(".ht_clone_".concat(t));return n?n.contains(e):null}function am(t){return!t||!t.length?[]:t.filter(e=>!!e)}function cm(t,e){if(!t||!t.length)return e?{regexFree:[],regexes:[]}:[];const r=[],n=[];return n.push(...t.filter(i=>{const o=i instanceof RegExp;return o&&e&&r.push(i),!o})),e?{regexFree:n,regexes:r}:n}function ee(t,e){return t.classList===void 0||typeof e!="string"||e===""?!1:t.classList.contains(e)}function G(t,e){typeof e=="string"&&(e=e.split(" ")),e=am(e),e.length>0&&t.classList.add(...e)}function ce(t,e){typeof e=="string"?e=e.split(" "):e instanceof RegExp&&(e=[e]);let{regexFree:r,regexes:n}=cm(e,!0);r=am(r),r.length>0&&t.classList.remove(...r),n.forEach(i=>{t.classList.forEach(o=>{i.test(o)&&t.classList.remove(o)})})}function de(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0;Array.isArray(e)||(e=[[e,r]]),e.forEach(n=>{Array.isArray(n)&&n[0]!==""&&t.setAttribute(...n)})}function ii(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];typeof e=="string"?e=e.split(" "):e instanceof RegExp&&(e=[e]);const{regexFree:r,regexes:n}=cm(e,!0);r.forEach(i=>{i!==""&&t.removeAttribute(i)}),n.forEach(i=>{t.getAttributeNames().forEach(o=>{i.test(o)&&t.removeAttribute(o)})})}function um(t){if(t.nodeType===3)t.parentNode.removeChild(t);else if(["TABLE","THEAD","TBODY","TFOOT","TR"].indexOf(t.nodeName)>-1){const e=t.childNodes;for(let r=e.length-1;r>=0;r--)um(e[r])}}function Uo(t){let e;for(;e=t.lastChild;)t.removeChild(e)}const DH=/(<(.*)>|&(.*);)/;function VH(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;DH.test(e)?t.innerHTML=r?RH(e):e:si(t,e)}function si(t,e){const r=t.firstChild;r&&r.nodeType===3&&r.nextSibling===null?r.textContent=e:(Uo(t),t.appendChild(t.ownerDocument.createTextNode(e)))}function Go(t){const e=t.ownerDocument.documentElement,r=t.ownerDocument.defaultView;let n=t;for(;n!==e;){if(n===null)return!1;if(n.nodeType===Node.DOCUMENT_FRAGMENT_NODE)if(n.host){if(n.host.impl)return Go(n.host.impl);if(n.host)return Go(n.host);throw new Error("Lost in Web Components world")}else return!1;else if(r.getComputedStyle(n).display==="none")return!1;n=n.parentNode}return!0}function kH(t){const r=t.ownerDocument.defaultView;let n=t;for(;n.parentNode;){if(n.style.height==="0px"||n.style.height==="0")return r.getComputedStyle(n).overflow==="hidden";n=n.parentNode}return!1}function At(t){const e=t.ownerDocument,r=e.defaultView,n=e.documentElement;let i=t,o,s,l;for(o=i.offsetLeft,s=i.offsetTop,l=i;(i=i.offsetParent)&&!(i===e.body||!("offsetLeft"in i));)o+=i.offsetLeft,s+=i.offsetTop,l=i;return l&&l.style.position==="fixed"&&(o+=r.pageXOffset||n.scrollLeft,s+=r.pageYOffset||n.scrollTop),{left:o,top:s}}function hm(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:window).scrollY}function ac(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:window).scrollX}function cc(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:window;return t===e?hm(e):t.scrollTop}function dm(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:window;return t===e?ac(e):t.scrollLeft}function gs(t){let e=t.ownerDocument,r=e?e.defaultView:void 0;e||(e=t.document?t.document:t,r=e.defaultView);const n=["auto","scroll"];let i=t.parentNode;for(;i&&i.style&&e.body!==i;){let{overflow:o,overflowX:s,overflowY:l}=i.style;if([o,s,l].includes("scroll"))return i;if({overflow:o,overflowX:s,overflowY:l}=r.getComputedStyle(i),n.includes(o)||n.includes(s)||n.includes(l)||i.clientHeight<=i.scrollHeight+1&&(n.includes(l)||n.includes(o))||i.clientWidth<=i.scrollWidth+1&&(n.includes(s)||n.includes(o)))return i;i=i.parentNode}return r}function FH(t){return t.scrollHeight-t.clientHeight}function BH(t){return t.scrollWidth-t.clientWidth}function ms(t){const e=t.ownerDocument,r=e.defaultView;let n=t.parentNode;for(;n&&n.style&&e.body!==n;){if(n.style.overflow!=="visible"&&n.style.overflow!=="")return n;const i=r.getComputedStyle(n),o=["scroll","hidden","auto"],s=i.getPropertyValue("overflow"),l=i.getPropertyValue("overflow-y"),a=i.getPropertyValue("overflow-x");if(o.includes(s)||o.includes(l)||o.includes(a))return n;n=n.parentNode}return r}function xh(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:window;if(t){if(t===r)return e==="width"?"".concat(r.innerWidth,"px"):e==="height"?"".concat(r.innerHeight,"px"):void 0}else return;const n=t.style[e];if(n!==""&&n!==void 0)return n;const i=r.getComputedStyle(t);if(i[e]!==""&&i[e]!==void 0)return i[e]}function ut(t){return t.offsetWidth}function yt(t){return t.offsetHeight}function ko(t){return t.clientHeight||t.innerHeight}function fm(t){return t.clientWidth||t.innerWidth}function WH(t){return t.selectionStart?t.selectionStart:0}function _h(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window;t.getSelection&&(t.getSelection().empty?t.getSelection().empty():t.getSelection().removeAllRanges&&t.getSelection().removeAllRanges())}function gm(t,e,r){if(r===void 0&&(r=e),t.setSelectionRange){t.focus();try{t.setSelectionRange(e,r)}catch(n){const i=t.parentNode,o=i.style.display;i.style.display="block",t.setSelectionRange(e,r),i.style.display=o}}}let bl;function jH(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:document;const e=t.createElement("div");e.style.height="200px",e.style.width="100%";const r=t.createElement("div");r.style.boxSizing="content-box",r.style.height="150px",r.style.left="0px",r.style.overflow="hidden",r.style.position="absolute",r.style.top="0px",r.style.width="200px",r.style.visibility="hidden",r.appendChild(e),(t.body||t.documentElement).appendChild(r);const n=e.offsetWidth;r.style.overflow="scroll";let i=e.offsetWidth;return n===i&&(i=r.clientWidth),(t.body||t.documentElement).removeChild(r),n-i}function ht(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:document;return bl===void 0&&(bl=jH(t)),bl}function zH(t){return t instanceof Window?t.document.body.scrollHeight>t.innerHeight:t.offsetWidth!==t.clientWidth}function UH(t){return t instanceof Window?t.document.body.scrollWidth>t.innerWidth:t.offsetHeight!==t.clientHeight}function uc(t,e,r){t.style.transform="translate3d(".concat(e,",").concat(r,",0)")}function Ms(t){t.style.transform&&t.style.transform!==""&&(t.style.transform="")}function os(t){return t&&(["INPUT","SELECT","TEXTAREA"].indexOf(t.nodeName)>-1||t.contentEditable==="true")}function mm(t){return os(t)&&t.hasAttribute("data-hot-input")===!1}function GH(t,e){new IntersectionObserver((n,i)=>{n.forEach(o=>{o.isIntersecting&&t.offsetParent!==null&&(e(),i.unobserve(t))})},{root:t.ownerDocument.body}).observe(t)}function xs(t){var e;const r=t==null||(e=t.ownerDocument)===null||e===void 0?void 0:e.defaultView.Element;return!!(r&&r!==null&&t instanceof r)}function Z(t){return typeof t=="function"}function KH(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:200,r=null,n;function i(){for(var o=arguments.length,s=new Array(o),l=0;l<o;l++)s[l]=arguments[l];return r&&clearTimeout(r),r=setTimeout(()=>{n=t.apply(this,s)},e),n}return i}function YH(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];return function(){for(var o=arguments.length,s=new Array(o),l=0;l<o;l++)s[l]=arguments[l];return t.apply(this,r.concat(s))}}function Oa(t,e,r,n,i,o,s,l){return X(l)?t.call(e,r,n,i,o,s,l):X(s)?t.call(e,r,n,i,o,s):X(o)?t.call(e,r,n,i,o):X(i)?t.call(e,r,n,i):X(n)?t.call(e,r,n):X(r)?t.call(e,r):t.call(e)}var XH=be,Ph=sg,qH=Me,Ah=gn,QH=Os,Nh=XH([].push),ZH=function(t){if(qH(t))return t;if(Ph(t)){for(var e=t.length,r=[],n=0;n<e;n++){var i=t[n];typeof i=="string"?Nh(r,i):(typeof i=="number"||Ah(i)==="Number"||Ah(i)==="String")&&Nh(r,QH(i))}var o=r.length,s=!0;return function(l,a){if(s)return s=!1,a;if(Ph(this))return a;for(var c=0;c<o;c++)if(r[c]===l)return a}}},JH=oe,pm=Gn,wm=ti,eI=Ve,li=be,vm=De,$h=Me,Lh=Wa,ym=oc,tI=ZH,rI=Ba,nI=String,kr=pm("JSON","stringify"),Ri=li(/./.exec),Dh=li("".charAt),oI=li("".charCodeAt),iI=li("".replace),sI=li(1 .toString),lI=/[\uD800-\uDFFF]/g,Vh=/^[\uD800-\uDBFF]$/,kh=/^[\uDC00-\uDFFF]$/,Fh=!rI||vm(function(){var t=pm("Symbol")("stringify detection");return kr([t])!=="[null]"||kr({a:t})!=="{}"||kr(Object(t))!=="{}"}),Bh=vm(function(){return kr("\uDF06\uD834")!=='"\\udf06\\ud834"'||kr("\uDEAD")!=='"\\udead"'}),aI=function(t,e){var r=ym(arguments),n=tI(e);if(!(!$h(n)&&(t===void 0||Lh(t))))return r[1]=function(i,o){if($h(n)&&(o=eI(n,this,nI(i),o)),!Lh(o))return o},wm(kr,null,r)},cI=function(t,e,r){var n=Dh(r,e-1),i=Dh(r,e+1);return Ri(Vh,t)&&!Ri(kh,i)||Ri(kh,t)&&!Ri(Vh,n)?"\\u"+sI(oI(t,0),16):t};kr&&JH({target:"JSON",stat:!0,arity:3,forced:Fh||Bh},{stringify:function(e,r,n){var i=ym(arguments),o=wm(Fh?aI:kr,null,i);return Bh&&typeof o=="string"?iI(o,lI,cI):o}});function Ko(t){let e;return Array.isArray(t)?e=t.length?new Array(t.length).fill(null):[]:(e={},fe(t,(r,n)=>{n!=="__children"&&(r&&typeof r=="object"&&!Array.isArray(r)?e[n]=Ko(r):Array.isArray(r)?r.length&&typeof r[0]=="object"&&!Array.isArray(r[0])?e[n]=[Ko(r[0])]:e[n]=[]:e[n]=null)})),e}function uI(t,e){return e.prototype.constructor=e,t.prototype=new e,t.prototype.constructor=t,t}function zn(t,e,r){const n=Array.isArray(r);return fe(e,(i,o)=>{(n===!1||r.includes(o))&&(t[o]=i)}),t}function Cm(t,e){fe(e,(r,n)=>{e[n]&&typeof e[n]=="object"?(t[n]||(Array.isArray(e[n])?t[n]=[]:Object.prototype.toString.call(e[n])==="[object Date]"?t[n]=e[n]:t[n]={}),Cm(t[n],e[n])):t[n]=e[n]})}function Br(t){return typeof t=="object"?JSON.parse(JSON.stringify(t)):t}function Se(t){t.MIXINS||(t.MIXINS=[]);for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];return U(r,i=>{t.MIXINS.push(i.MIXIN_NAME),fe(i,(o,s)=>{if(t.prototype[s]!==void 0)throw new Error("Mixin conflict. Property '".concat(s,"' already exist and cannot be overwritten."));if(typeof o=="function")t.prototype[s]=o;else{const l=function(d,f){const m="_".concat(d),p=v=>{let R=v;return(Array.isArray(R)||dr(R))&&(R=Br(R)),R};return function(){return this[m]===void 0&&(this[m]=p(f)),this[m]}},a=function(d){const f="_".concat(d);return function(m){this[f]=m}};Object.defineProperty(t.prototype,s,{get:l(s,o),set:a(s),configurable:!0})}})}),t}function bm(t,e){return JSON.stringify(t)===JSON.stringify(e)}function dr(t){return Object.prototype.toString.call(t)==="[object Object]"}function zr(t,e,r,n){n.value=r,n.writable=n.writable!==!1,n.enumerable=n.enumerable!==!1,n.configurable=n.configurable!==!1,Object.defineProperty(t,e,n)}function fe(t,e){for(const r in t)if((!t.hasOwnProperty||t.hasOwnProperty&&Object.prototype.hasOwnProperty.call(t,r))&&e(t[r],r,t)===!1)break;return t}function hI(t,e){const r=e.split(".");let n=t;return fe(r,i=>{if(n=n[i],n===void 0)return n=void 0,!1}),n}function Sl(t,e,r){if(typeof e!="string")return;const n=e.split(".");let i=t;n.forEach((o,s)=>{o==="__proto__"||o==="constructor"||o==="prototype"||(s!==n.length-1?(un(i,o)||(i[o]={}),i=i[o]):i[o]=r)})}function hc(t){if(!dr(t))return 0;const e=function(r){let n=0;return dr(r)?fe(r,(i,o)=>{o!=="__children"&&(n+=e(i))}):n+=1,n};return e(t)}function wt(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"value";const r="_".concat(e),n={_touched:!1,[r]:t,isTouched(){return this._touched}};return Object.defineProperty(n,e,{get(){return this[r]},set(i){this._touched=!0,this[r]=i},enumerable:!0,configurable:!0}),n}function un(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function Wh(t){return window.requestAnimationFrame(t)}function dI(){return"ontouchstart"in window}function fI(){return typeof window<"u"}const Pt=t=>{const e={value:!1};return e.test=(r,n)=>{e.value=t(r,n)},e},bn={chrome:Pt((t,e)=>/Chrome/.test(t)&&/Google/.test(e)),chromeWebKit:Pt(t=>/CriOS/.test(t)),edge:Pt(t=>/Edge/.test(t)),edgeWebKit:Pt(t=>/EdgiOS/.test(t)),firefox:Pt(t=>/Firefox/.test(t)),firefoxWebKit:Pt(t=>/FxiOS/.test(t)),mobile:Pt(t=>/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t)),safari:Pt((t,e)=>/Safari/.test(t)&&/Apple Computer/.test(e))},_s={mac:Pt(t=>/^Mac/.test(t)),win:Pt(t=>/^Win/.test(t)),linux:Pt(t=>/^Linux/.test(t)),ios:Pt(t=>/iPhone|iPad|iPod/i.test(t))};function gI(){let{userAgent:t=navigator.userAgent,vendor:e=navigator.vendor}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};fe(bn,r=>{let{test:n}=r;return void n(t,e)})}function mI(){let{platform:t=navigator.platform}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};fe(_s,e=>{let{test:r}=e;return void r(t)})}fI()&&(gI(),mI());function pI(){return bn.chrome.value}function wI(){return bn.chromeWebKit.value}function Rl(){return bn.firefox.value}function vI(){return bn.firefoxWebKit.value}function yI(){return bn.edge.value}function Vn(){return bn.mobile.value}function Sm(){return _s.ios.value}function CI(){let{maxTouchPoints:t}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:navigator;return t>2&&_s.mac.value}function jh(){return _s.mac.value}const L={ALT:18,ARROW_DOWN:40,ARROW_LEFT:37,ARROW_RIGHT:39,ARROW_UP:38,AUDIO_DOWN:Rl()?182:174,AUDIO_MUTE:Rl()?181:173,AUDIO_UP:Rl()?183:175,BACKSPACE:8,CAPS_LOCK:20,COMMA:188,COMMAND_LEFT:91,COMMAND_RIGHT:93,COMMAND_FIREFOX:224,CONTROL:17,DELETE:46,END:35,ENTER:13,ESCAPE:27,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,F13:124,F14:125,F15:126,F16:127,F17:128,F18:129,F19:130,HOME:36,INSERT:45,MEDIA_NEXT:176,MEDIA_PLAY_PAUSE:179,MEDIA_PREV:177,MEDIA_STOP:178,NULL:0,NUM_LOCK:144,PAGE_DOWN:34,PAGE_UP:33,PAUSE:19,PERIOD:190,SCROLL_LOCK:145,SHIFT:16,SPACE:32,TAB:9,A:65,C:67,D:68,F:70,L:76,O:79,P:80,S:83,V:86,X:88,Y:89,Z:90},bI=[L.ALT,L.ARROW_DOWN,L.ARROW_LEFT,L.ARROW_RIGHT,L.ARROW_UP,L.AUDIO_DOWN,L.AUDIO_MUTE,L.AUDIO_UP,L.BACKSPACE,L.CAPS_LOCK,L.DELETE,L.END,L.ENTER,L.ESCAPE,L.F1,L.F2,L.F3,L.F4,L.F5,L.F6,L.F7,L.F8,L.F9,L.F10,L.F11,L.F12,L.F13,L.F14,L.F15,L.F16,L.F17,L.F18,L.F19,L.HOME,L.INSERT,L.MEDIA_NEXT,L.MEDIA_PLAY_PAUSE,L.MEDIA_PREV,L.MEDIA_STOP,L.NULL,L.NUM_LOCK,L.PAGE_DOWN,L.PAGE_UP,L.PAUSE,L.SCROLL_LOCK,L.SHIFT,L.TAB];function SI(t){return bI.includes(t)}function RI(t){return[L.CONTROL,L.COMMAND_LEFT,L.COMMAND_RIGHT,L.COMMAND_FIREFOX].includes(t)}function TI(t,e){const r=e.split("|");let n=!1;return U(r,i=>{if(t===L[i])return n=!0,!1}),n}function Ps(t){t.isImmediatePropagationEnabled=!1,t.cancelBubble=!0}function sn(t){return t.isImmediatePropagationEnabled===!1}function Ha(t){return t.button===2}function Rm(t){return t.button===0}function Wr(){X(console)&&console.warn(...arguments)}function EI(){X(console)&&console.error(...arguments)}const ln=["afterCellMetaReset","afterChange","afterContextMenuDefaultOptions","beforeContextMenuSetItems","afterDropdownMenuDefaultOptions","beforeDropdownMenuSetItems","afterContextMenuHide","beforeContextMenuShow","afterContextMenuShow","afterCopyLimit","beforeCreateCol","afterColumnSequenceChange","afterCreateCol","beforeCreateRow","afterCreateRow","afterDeselect","afterDestroy","afterDocumentKeyDown","afterDrawSelection","beforeRemoveCellClassNames","beforeCompositionStart","afterGetCellMeta","afterGetColHeader","afterGetRowHeader","afterInit","afterLoadData","afterUpdateData","afterMomentumScroll","afterOnCellCornerMouseDown","afterOnCellCornerDblClick","afterOnCellMouseDown","afterOnCellMouseUp","afterOnCellContextMenu","afterOnCellMouseOver","afterOnCellMouseOut","afterRemoveCol","afterRemoveRow","beforeRenderer","afterRenderer","afterRowSequenceChange","beforeViewportScrollVertically","beforeViewportScrollHorizontally","beforeViewportScroll","afterScrollHorizontally","afterScrollVertically","afterScroll","afterSelection","afterSelectionByProp","afterSelectionEnd","afterSelectionEndByProp","afterSelectionFocusSet","beforeSelectColumns","afterSelectColumns","beforeSelectRows","afterSelectRows","afterSetCellMeta","afterRemoveCellMeta","afterSetDataAtCell","afterSetDataAtRowProp","afterSetSourceDataAtCell","afterSetTheme","afterUpdateSettings","afterValidate","beforeLanguageChange","afterLanguageChange","beforeAutofill","afterAutofill","beforeCellAlignment","beforeChange","beforeChangeRender","beforeDrawBorders","beforeGetCellMeta","beforeRemoveCellMeta","beforeInit","beforeInitWalkontable","beforeLoadData","beforeUpdateData","beforeKeyDown","beforeOnCellMouseDown","beforeOnCellMouseUp","beforeOnCellContextMenu","beforeOnCellMouseOver","beforeOnCellMouseOut","beforeRemoveCol","beforeRemoveRow","beforeViewRender","afterViewRender","beforeRender","afterRender","beforeRowWrap","beforeColumnWrap","beforeSetCellMeta","beforeSelectionFocusSet","beforeSetRangeStartOnly","beforeSetRangeStart","beforeSetRangeEnd","beforeSelectionHighlightSet","beforeTouchScroll","beforeValidate","beforeValueRender","construct","init","modifyColHeader","modifyColWidth","modifyFiltersMultiSelectValue","modifyFocusedElement","modifyRowHeader","modifyRowHeight","modifyRowHeightByOverlayName","modifyData","modifySourceData","modifyRowData","modifyGetCellCoords","modifyGetCoordsElement","modifyFocusOnTabNavigation","beforeHighlightingRowHeader","beforeHighlightingColumnHeader","persistentStateLoad","persistentStateReset","persistentStateSave","beforeColumnSort","afterColumnSort","modifyAutofillRange","modifyCopyableRange","beforeCut","afterCut","beforeCopy","afterCopy","beforePaste","afterPaste","beforeColumnFreeze","afterColumnFreeze","beforeColumnMove","afterColumnMove","beforeColumnUnfreeze","afterColumnUnfreeze","beforeRowMove","afterRowMove","beforeColumnResize","afterColumnResize","beforeRowResize","afterRowResize","afterGetColumnHeaderRenderers","afterGetRowHeaderRenderers","beforeStretchingColumnWidth","beforeFilter","afterFilter","afterFormulasValuesUpdate","afterNamedExpressionAdded","afterNamedExpressionRemoved","afterSheetAdded","afterSheetRenamed","afterSheetRemoved","modifyColumnHeaderHeight","modifyColumnHeaderValue","beforeUndo","beforeUndoStackChange","afterUndo","afterUndoStackChange","beforeRedo","beforeRedoStackChange","afterRedo","afterRedoStackChange","modifyRowHeaderWidth","modifyTransformFocus","modifyTransformStart","modifyTransformEnd","afterModifyTransformFocus","afterModifyTransformStart","afterModifyTransformEnd","afterViewportRowCalculatorOverride","afterViewportColumnCalculatorOverride","afterPluginsInitialized","beforeHideRows","afterHideRows","beforeUnhideRows","afterUnhideRows","beforeHideColumns","afterHideColumns","beforeUnhideColumns","afterUnhideColumns","beforeTrimRow","afterTrimRow","beforeUntrimRow","afterUntrimRow","beforeDropdownMenuShow","afterDropdownMenuShow","afterDropdownMenuHide","beforeAddChild","afterAddChild","beforeDetachChild","afterDetachChild","beforeBeginEditing","afterBeginEditing","beforeMergeCells","afterMergeCells","beforeUnmergeCells","afterUnmergeCells","afterListen","afterUnlisten","afterRefreshDimensions","beforeRefreshDimensions","beforeColumnCollapse","afterColumnCollapse","beforeColumnExpand","afterColumnExpand","modifyAutoColumnSizeSeed"],Tl=new Map([["modifyRow","8.0.0"],["modifyCol","8.0.0"],["unmodifyRow","8.0.0"],["unmodifyCol","8.0.0"],["skipLengthCache","8.0.0"],["hiddenColumn","8.0.0"],["hiddenRow","8.0.0"]]),El=new Map([[]]);var OI=Xn,HI=function(t,e,r){for(var n=0,i=arguments.length>2?r:OI(e),o=new t(i);i>n;)o[n]=e[n++];return o},II=Ie,MI=function(t,e){var r=II[t],n=r&&r.prototype;return n&&n[e]},xI=er,_I=Og,PI=Ir.f,Ia=xI("unscopables"),Ma=Array.prototype;Ma[Ia]===void 0&&PI(Ma,Ia,{configurable:!0,value:_I(null)});var dc=function(t){Ma[Ia][t]=!0},AI=oe,NI=be,$I=bt,LI=qo,DI=HI,VI=MI,kI=dc,FI=Array,BI=NI(VI("Array","sort"));AI({target:"Array",proto:!0},{toSorted:function(e){e!==void 0&&$I(e);var r=LI(this),n=DI(FI,r);return BI(n,e)}});kI("toSorted");var WI=oe,jI=Ve,zI=qn,UI=bt,GI=ke,KI=jr,YI=kt,XI=Cn,Ol=XI("find",TypeError);WI({target:"Iterator",proto:!0,real:!0,forced:Ol},{find:function(e){GI(this);try{UI(e)}catch(i){YI(this,"throw",i)}if(Ol)return jI(Ol,this,e);var r=KI(this),n=0;return zI(r,function(i,o){if(e(i,n++))return o(i)},{IS_RECORD:!0,INTERRUPTED:!0}).result}});function qI(t,e){Tm(t,e),e.add(t)}function Hl(t,e,r){Tm(t,e),e.set(t,r)}function Tm(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function zh(t,e,r){return t.set(ps(t,e),r),r}function Fe(t,e){return t.get(ps(t,e))}function ps(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}const QI=100;var mt=new WeakMap,Ln=new WeakMap,Il=new WeakMap,Ml=new WeakSet;class Uh{constructor(){qI(this,Ml),Hl(this,mt,new Map),Hl(this,Ln,new Map),Hl(this,Il,new Set),ln.forEach(e=>ps(Ml,this,Gh).call(this,e))}getHooks(e){var r;return(r=Fe(mt,this).get(e))!==null&&r!==void 0?r:[]}add(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Fe(mt,this).has(e)||(ps(Ml,this,Gh).call(this,e),ln.push(e));const i=Fe(mt,this).get(e);if(i.find(c=>c.callback===r))return;const o=Number.isInteger(n.orderIndex)?n.orderIndex:0,s=!!n.runOnce,l=!!n.initialHook;let a=!1;if(l){const c=i.find(d=>d.initialHook);c&&(c.callback=r,a=!0)}if(!a){i.push({callback:r,orderIndex:o,runOnce:s,initialHook:l,skip:!1});let c=Fe(Il,this).has(e);!c&&o!==0&&(c=!0,Fe(Il,this).add(e)),c&&i.length>1&&Fe(mt,this).set(e,i.toSorted((d,f)=>d.orderIndex-f.orderIndex))}}has(e){return Fe(mt,this).has(e)&&Fe(mt,this).get(e).length>0}remove(e,r){if(!Fe(mt,this).has(e))return!1;const n=Fe(mt,this).get(e),i=n.find(o=>o.callback===r);if(i){let o=Fe(Ln,this).get(e);return i.skip=!0,o+=1,o>QI&&(Fe(mt,this).set(e,n.filter(s=>!s.skip)),o=0),Fe(Ln,this).set(e,o),!0}return!1}destroy(){Fe(mt,this).clear(),Fe(Ln,this).clear(),zh(mt,this,null),zh(Ln,this,null)}}function Gh(t){Fe(mt,this).set(t,[]),Fe(Ln,this).set(t,0)}function ZI(t,e,r){return(e=JI(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function JI(t){var e=eM(t,"string");return typeof e=="symbol"?e:e+""}function eM(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var pf;const tM=vt(pf||(pf=ot(['The plugin hook "[hookName]" was removed in Handsontable [removedInVersion]. \n  Please consult release notes https://github.com/handsontable/handsontable/releases/tag/[removedInVersion] to \n  learn about the migration path.'],['The plugin hook "[hookName]" was removed in Handsontable [removedInVersion].\\x20\n  Please consult release notes https://github.com/handsontable/handsontable/releases/tag/[removedInVersion] to\\x20\n  learn about the migration path.'])));class Le{constructor(){ZI(this,"globalBucket",new Uh)}static getSingleton(){return nM()}getBucket(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;return e?(e.pluginHookBucket||(e.pluginHookBucket=new Uh),e.pluginHookBucket):this.globalBucket}add(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,i=arguments.length>3?arguments[3]:void 0;return Array.isArray(r)?U(r,o=>this.add(e,o,n)):(Tl.has(e)&&Wr(SH(tM,{hookName:e,removedInVersion:Tl.get(e)})),El.has(e)&&Wr(El.get(e)),this.getBucket(n).add(e,r,{orderIndex:i,runOnce:!1})),this}once(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,i=arguments.length>3?arguments[3]:void 0;return Array.isArray(r)?U(r,o=>this.once(e,o,n)):this.getBucket(n).add(e,r,{orderIndex:i,runOnce:!0}),this}addAsFixed(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;return Array.isArray(r)?U(r,i=>this.addAsFixed(e,i,n)):this.getBucket(n).add(e,r,{initialHook:!0}),this}remove(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;return this.getBucket(n).remove(e,r)}has(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return this.getBucket(r).has(e)}run(e,r,n,i,o,s,l,a){{const c=this.getBucket().getHooks(r),d=c?c.length:0;let f=0;if(d)for(;f<d;){if(!c[f]||c[f].skip){f+=1;continue}const m=Oa(c[f].callback,e,n,i,o,s,l,a);m!==void 0&&(n=m),c[f]&&c[f].runOnce&&this.remove(r,c[f].callback),f+=1}}{const c=this.getBucket(e).getHooks(r),d=c?c.length:0;let f=0;if(d)for(;f<d;){if(!c[f]||c[f].skip){f+=1;continue}const m=Oa(c[f].callback,e,n,i,o,s,l,a);m!==void 0&&(n=m),c[f]&&c[f].runOnce&&this.remove(r,c[f].callback,e),f+=1}}return n}destroy(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;this.getBucket(e).destroy()}register(e){this.isRegistered(e)||ln.push(e)}deregister(e){this.isRegistered(e)&&ln.splice(ln.indexOf(e),1)}isDeprecated(e){return El.has(e)||Tl.has(e)}isRegistered(e){return ln.indexOf(e)>=0}getRegistered(){return ln}}const rM=new Le;function nM(){return rM}const xl=new Map;function Qn(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"common";xl.has(t)||xl.set(t,new Map);const e=xl.get(t);function r(l,a){e.set(l,a)}function n(l){return e.get(l)}function i(l){return e.has(l)}function o(){return[...e.keys()]}function s(){return[...e.values()]}return{register:r,getItem:n,hasItem:i,getNames:o,getValues:s}}const xa=new WeakMap,{register:oM,getItem:Em,hasItem:iM,getNames:xD,getValues:_D}=Qn("editors");function sM(t){const e={},r=t;this.getConstructor=function(){return t},this.getInstance=function(n){return n.guid in e||(e[n.guid]=new r(n)),e[n.guid]},Le.getSingleton().add("afterDestroy",function(){e[this.guid]=null})}function lM(t,e){let r;if(typeof t=="function")xa.get(t)||Om(null,t),r=xa.get(t);else if(typeof t=="string")r=Em(t);else throw Error('Only strings and functions can be passed as "editor" parameter');if(!r)throw Error('No editor registered under name "'.concat(t,'"'));return r.getInstance(e)}function Kh(t){if(typeof t=="function")return t;if(!iM(t))throw Error('No registered editor found under "'.concat(t,'" name'));return Em(t).getConstructor()}function Om(t,e){t&&typeof t!="string"&&(e=t,t=e.EDITOR_TYPE);const r=new sM(e);typeof t=="string"&&oM(t,r),xa.set(e,r)}function aM(t,e,r){return(e=cM(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function cM(t){var e=uM(t,"string");return typeof e=="symbol"?e:e+""}function uM(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class hM{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;aM(this,"context",void 0),this.context=e||this,this.context.eventListeners||(this.context.eventListeners=[])}addEventListener(e,r,n){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;function o(s){n.call(this,dM(s))}return this.context.eventListeners.push({element:e,event:r,callback:n,callbackProxy:o,options:i,eventManager:this}),e.addEventListener(r,o,i),()=>{this.removeEventListener(e,r,n)}}removeEventListener(e,r,n){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,o=this.context.eventListeners.length,s;for(;o;)if(o-=1,s=this.context.eventListeners[o],s.event===r&&s.element===e){if(n&&n!==s.callback||i&&s.eventManager!==this)continue;this.context.eventListeners.splice(o,1),s.element.removeEventListener(s.event,s.callbackProxy,s.options)}}clearEvents(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(!this.context)return;let r=this.context.eventListeners.length;for(;r;){r-=1;const n=this.context.eventListeners[r];e&&n.eventManager!==this||(this.context.eventListeners.splice(r,1),n.element.removeEventListener(n.event,n.callbackProxy,n.options))}}clear(){this.clearEvents()}destroy(){this.clearEvents(),this.context=null}destroyWithOwnEventsOnly(){this.clearEvents(!0),this.context=null}fireEvent(e,r){let n=e.document,i=e;n||(n=e.ownerDocument?e.ownerDocument:e,i=n.defaultView);const o={bubbles:!0,cancelable:r!=="mousemove",view:i,detail:0,screenX:0,screenY:0,clientX:1,clientY:1,ctrlKey:!1,altKey:!1,shiftKey:!1,metaKey:!1,button:0,relatedTarget:void 0};let s;n.createEvent?(s=n.createEvent("MouseEvents"),s.initMouseEvent(r,o.bubbles,o.cancelable,o.view,o.detail,o.screenX,o.screenY,o.clientX,o.clientY,o.ctrlKey,o.altKey,o.shiftKey,o.metaKey,o.button,n.body.parentNode)):s=n.createEventObject(),e.dispatchEvent?e.dispatchEvent(s):e.fireEvent("on".concat(r),s)}}function dM(t){const e=t.stopImmediatePropagation;return t.stopImmediatePropagation=function(){e.apply(this),Ps(this)},t}const Un=hM;function fM(t,e){gM(t,e),e.add(t)}function gM(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function qr(t,e,r){return(e=mM(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function mM(t){var e=pM(t,"string");return typeof e=="symbol"?e:e+""}function pM(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function _l(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var Ti=new WeakSet;class _a{constructor(e,r,n){fM(this,Ti),qr(this,"hot",void 0),qr(this,"tableMeta",void 0),qr(this,"selection",void 0),qr(this,"eventManager",void 0),qr(this,"destroyed",!1),qr(this,"activeEditor",void 0),qr(this,"cellProperties",void 0),this.hot=e,this.tableMeta=r,this.selection=n,this.eventManager=new Un(e),this.hot.addHook("afterDocumentKeyDown",i=>_l(Ti,this,Yh).call(this,i)),this.hot.addHook("beforeCompositionStart",i=>_l(Ti,this,Yh).call(this,i)),this.hot.view._wt.update("onCellDblClick",(i,o,s)=>_l(Ti,this,wM).call(this,i,o,s))}getActiveEditor(){return this.activeEditor}prepareEditor(){var e;if(this.activeEditor&&this.activeEditor.isWaiting()){this.closeEditor(!1,!1,c=>{c&&this.prepareEditor()});return}const r=(e=this.hot.getSelectedRangeLast())===null||e===void 0?void 0:e.highlight;if(!r||r.isHeader())return;const{row:n,col:i}=r,o=this.hot.runHooks("modifyGetCellCoords",n,i,!1,"meta");let s=n,l=i;if(Array.isArray(o)&&([s,l]=o),this.cellProperties=this.hot.getCellMeta(s,l),!this.isCellEditable()){this.clearActiveEditor();return}const a=this.hot.getCell(n,i,!0);if(a){const c=this.hot.getCellEditor(this.cellProperties),d=this.hot.colToProp(l),f=this.hot.getSourceDataAtCell(this.hot.toPhysicalRow(s),l);this.activeEditor=lM(c,this.hot),this.activeEditor.prepare(n,i,d,a,f,this.cellProperties)}}isEditorOpened(){return this.activeEditor&&this.activeEditor.isOpened()}openEditor(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(!this.isCellEditable()){this.clearActiveEditor();return}const i=this.hot.getSelectedRangeLast();let o=this.hot.runHooks("beforeBeginEditing",i.highlight.row,i.highlight.col,e,r,n);if(r instanceof MouseEvent&&typeof o!="boolean"&&(o=this.hot.selection.getLayerLevel()===0&&i.isSingle()),o===!1){this.clearActiveEditor();return}this.activeEditor||(this.hot.scrollToFocusedCell(),this.prepareEditor()),this.activeEditor&&(n&&this.activeEditor.enableFullEditMode(),this.activeEditor.beginEditing(e,r))}closeEditor(e,r,n){this.activeEditor?this.activeEditor.finishEditing(e,r,n):n&&n(!1)}closeEditorAndSaveChanges(e){this.closeEditor(!1,e)}closeEditorAndRestoreOriginalValue(e){this.closeEditor(!0,e)}clearActiveEditor(){this.activeEditor=void 0}isCellEditable(){const e=this.hot.getSelectedRangeLast();if(!e)return!1;const r=this.hot.getCellEditor(this.cellProperties),{row:n,col:i}=e.highlight,{rowIndexMapper:o,columnIndexMapper:s}=this.hot,l=o.isHidden(this.hot.toPhysicalRow(n))||s.isHidden(this.hot.toPhysicalColumn(i));return!(this.cellProperties.readOnly||!r||l)}moveSelectionAfterEnter(e){const r={...typeof this.tableMeta.enterMoves=="function"?this.tableMeta.enterMoves(e):this.tableMeta.enterMoves};e.shiftKey&&(r.row=-r.row,r.col=-r.col),this.hot.selection.isMultiple()?this.selection.transformFocus(r.row,r.col):this.selection.transformStart(r.row,r.col,!0)}destroy(){this.destroyed=!0,this.eventManager.destroy()}}function Yh(t){const e=this.hot.getSelectedRangeLast();if(!this.hot.isListening()||!e||e.highlight.isHeader()||sn(t))return;const{keyCode:r}=t,n=(t.ctrlKey||t.metaKey)&&!t.altKey;(!this.activeEditor||this.activeEditor&&!this.activeEditor.isWaiting())&&!SI(r)&&!RI(r)&&!n&&!this.isEditorOpened()&&this.openEditor("",t)}function wM(t,e){e.isCell()&&this.openEditor(null,t,!0)}const Xh=new WeakMap;_a.getInstance=function(t,e,r){let n=Xh.get(t);return n||(n=new _a(t,e,r),Xh.set(t,n)),n};const vM=_a;function yM(t,e){Hm(t,e),e.add(t)}function uo(t,e,r){Hm(t,e),e.set(t,r)}function Hm(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function re(t,e){return t.get(Or(t,e))}function ho(t,e,r){return t.set(Or(t,e),r),r}function Or(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}const Fn=Object.freeze({CELL:"cell",MIXED:"mixed"});var Oe=new WeakMap,Ei=new WeakMap,Oi=new WeakMap,Hi=new WeakMap,Ii=new WeakMap,Dr=new WeakSet;class CM{constructor(e){var r=this;yM(this,Dr),uo(this,Oe,void 0),uo(this,Ei,void 0),uo(this,Oi,1),uo(this,Hi,null),uo(this,Ii,new Map);const n=e.getSettings();ho(Oe,this,e),ho(Ei,this,n.imeFastEdit?Fn.MIXED:Fn.CELL),re(Oe,this).addHook("afterUpdateSettings",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return Or(Dr,r,SM).call(r,...o)}),re(Oe,this).addHook("afterSelection",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return Or(Dr,r,qh).call(r,...o)}),re(Oe,this).addHook("afterSelectionFocusSet",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return Or(Dr,r,qh).call(r,...o)}),re(Oe,this).addHook("afterSelectionEnd",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return Or(Dr,r,bM).call(r,...o)})}getFocusMode(){return re(Ei,this)}setFocusMode(e){Object.values(Fn).includes(e)?ho(Ei,this,e):Wr('"'.concat(e,'" is not a valid focus mode.'))}getRefocusDelay(){return re(Oi,this)}setRefocusDelay(e){ho(Oi,this,e)}setRefocusElementGetter(e){ho(Hi,this,e)}getRefocusElement(){var e;return typeof re(Hi,this)=="function"?re(Hi,this).call(this):(e=re(Oe,this).getActiveEditor())===null||e===void 0?void 0:e.TEXTAREA}focusOnHighlightedCell(e){const r=n=>{var i,o;const s=(i=re(Oe,this).getSelectedRangeLast())===null||i===void 0?void 0:i.highlight;if(!s)return;let l=re(Oe,this).runHooks("modifyFocusedElement",s.row,s.col,n);xs(l)||(l=n),l&&!((o=re(Oe,this).getActiveEditor())!==null&&o!==void 0&&o.isOpened())&&l.focus({preventScroll:!0})};e?r(e):Or(Dr,this,fc).call(this,n=>r(n))}refocusToEditorTextarea(){var e;let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:re(Oi,this);if(re(Oe,this).getSettings().imeFastEdit&&!((e=re(Oe,this).getActiveEditor())!==null&&e!==void 0&&e.isOpened())){var n,i;(n=re(Oe,this).getActiveEditor())===null||n===void 0||(i=n.refreshValue)===null||i===void 0||i.call(n),re(Ii,this).has(r)||re(Ii,this).set(r,KH(()=>{if(!re(Oe,this).isDestroyed){var o;(o=this.getRefocusElement())===null||o===void 0||o.select()}},r)),re(Ii,this).get(r)()}}}function fc(t){var e;const r=(e=re(Oe,this).getSelectedRangeLast())===null||e===void 0?void 0:e.highlight;if(!r||!re(Oe,this).selection.isCellVisible(r)){t(null);return}const n=re(Oe,this).getCell(r.row,r.col,!0);n===null?re(Oe,this).addHookOnce("afterScroll",()=>{t(re(Oe,this).getCell(r.row,r.col,!0))}):t(n)}function qh(){Or(Dr,this,fc).call(this,t=>{const{activeElement:e}=re(Oe,this).rootDocument;e&&mm(e)&&e.blur(),this.focusOnHighlightedCell(t)})}function bM(){Or(Dr,this,fc).call(this,t=>{this.getFocusMode()===Fn.MIXED&&(t==null?void 0:t.nodeName)==="TD"&&this.refocusToEditorTextarea()})}function SM(t){typeof t.imeFastEdit=="boolean"&&this.setFocusMode(t.imeFastEdit?Fn.MIXED:Fn.CELL)}var RM=Dt,TM=gn,EM=er,OM=EM("match"),HM=function(t){var e;return RM(t)&&((e=t[OM])!==void 0?!!e:TM(t)==="RegExp")},IM=ke,MM=function(){var t=IM(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e},xM=Ve,_M=Vt,PM=Qo,AM=MM,Qh=RegExp.prototype,NM=function(t){var e=t.flags;return e===void 0&&!("flags"in Qh)&&!_M(t,"flags")&&PM(Qh,t)?xM(AM,t):e},gc=be,$M=Kn,LM=Math.floor,Pl=gc("".charAt),DM=gc("".replace),Al=gc("".slice),VM=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,kM=/\$([$&'`]|\d{1,2})/g,FM=function(t,e,r,n,i,o){var s=r+t.length,l=n.length,a=kM;return i!==void 0&&(i=$M(i),a=VM),DM(o,a,function(c,d){var f;switch(Pl(d,0)){case"$":return"$";case"&":return t;case"`":return Al(e,0,r);case"'":return Al(e,s);case"<":f=i[Al(d,1,-1)];break;default:var m=+d;if(m===0)return c;if(m>l){var p=LM(m/10);return p===0?c:p<=l?n[p-1]===void 0?Pl(d,1):n[p-1]+Pl(d,1):c}f=n[m-1]}return f===void 0?"":f})},BM=oe,WM=Ve,mc=be,Zh=Xo,jM=Me,zM=Dt,UM=HM,fo=Os,GM=Zo,KM=NM,YM=FM,XM=er,qM=XM("replace"),QM=TypeError,Nl=mc("".indexOf);mc("".replace);var Jh=mc("".slice),ZM=Math.max;BM({target:"String",proto:!0},{replaceAll:function(e,r){var n=Zh(this),i,o,s,l,a,c,d,f,m,p,v=0,R="";if(zM(e)){if(i=UM(e),i&&(o=fo(Zh(KM(e))),!~Nl(o,"g")))throw new QM("`.replaceAll` does not allow non-global regexes");if(s=GM(e,qM),s)return WM(s,e,n,r)}for(l=fo(n),a=fo(e),c=jM(r),c||(r=fo(r)),d=a.length,f=ZM(1,d),m=Nl(l,a);m!==-1;)p=c?fo(r(a,m,l)):YM(a,l,m,[],void 0,r),R+=Jh(l,v,m)+p,v=m+d,m=m+f>l.length?-1:Nl(l,a,m+f);return v<l.length&&(R+=Jh(l,v)),R}});var JM=oe,ex=qn,tx=bt,rx=ke,nx=jr,ox=kt,ix=Cn,sx=ti,lx=De,Im=TypeError,Mm=lx(function(){[].keys().reduce(function(){},void 0)}),$l=!Mm&&ix("reduce",Im);JM({target:"Iterator",proto:!0,real:!0,forced:Mm||$l},{reduce:function(e){rx(this);try{tx(e)}catch(s){ox(this,"throw",s)}var r=arguments.length<2,n=r?void 0:arguments[1];if($l)return sx($l,this,r?[e]:[e,n]);var i=nx(this),o=0;if(ex(i,function(s){r?(r=!1,n=s):n=e(n,s,o),o++},{IS_RECORD:!0}),r)throw new Im("Reduce of empty iterator with no initial value");return n}});const ax={"&nbsp;":" ","&amp;":"&","&lt;":"<","&gt;":">"};new RegExp(Object.keys(ax).map(t=>"(".concat(t,")")).join("|"),"gi");function ed(t){const e=t.hasColHeaders(),r=t.hasRowHeaders(),n=[e?-1:0,r?-1:0,t.countRows()-1,t.countCols()-1],i=t.getData(...n),o=i.length,s=o>0?i[0].length:0,l=["<table>","</table>"],a=e?["<thead>","</thead>"]:[],c=["<tbody>","</tbody>"],d=r?1:0,f=e?1:0;for(let m=0;m<o;m+=1){const p=e&&m===0,v=[];for(let H=0;H<s;H+=1){const M=!p&&r&&H===0;let b="";if(p)b="<th>".concat(t.getColHeader(H-d),"</th>");else if(M)b="<th>".concat(t.getRowHeader(m-f),"</th>");else{const k=i[m][H],{hidden:$,rowspan:W,colspan:D}=t.getCellMeta(m-f,H-d);if(!$){const F=[];if(W&&F.push('rowspan="'.concat(W,'"')),D&&F.push('colspan="'.concat(D,'"')),jn(k))b="<td ".concat(F.join(" "),"></td>");else{const ve=k.toString().replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/(<br(\s*|\/)>(\r\n|\n)?|\r\n|\n)/g,"<br>\r\n").replace(/\x20/gi,"&nbsp;").replace(/\t/gi,"&#9;");b="<td ".concat(F.join(" "),">").concat(ve,"</td>")}}}v.push(b)}const R=["<tr>",...v,"</tr>"].join("");p?a.splice(1,0,R):c.splice(-1,0,R)}return l.splice(1,0,a.join(""),c.join("")),l.join("")}function xm(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];const r=typeof t;if(r==="number")return!isNaN(t)&&isFinite(t);if(r==="string"){if(t.length===0)return!1;if(t.length===1)return/\d/.test(t);const n=Array.from(new Set([".",...e])).map(i=>"\\".concat(i)).join("|");return new RegExp("^[+-]?(((".concat(n,")?\\d+((").concat(n,")\\d+)?(e[+-]?\\d+)?)|(0x[a-f\\d]+))$"),"i").test(t.trim())}else if(r==="object")return!!t&&typeof t.valueOf()=="number"&&!(t instanceof Date);return!1}function cx(t){return xm(t,[","])}function Nt(t,e,r){let n=-1;for(typeof e=="function"?(r=e,e=t):n=t-1;++n<=e&&r(n)!==!1;);}function td(t,e,r){let n=t+1;for(typeof e=="function"&&(r=e,e=0);--n>=e&&r(n)!==!1;);}function pe(t,e,r){return Math.min(t,e)===t?e:Math.max(t,r)===t?r:t}const Pa="asc",ux="desc",rd=new Map([[Pa,[-1,1]],[ux,[1,-1]]]),hx=t=>"The priority '".concat(t,"' is already declared in a map."),dx=t=>"The priority '".concat(t,"' is not a number.");function fx(){let{errorPriorityExists:t,errorPriorityNaN:e}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const r=new Map;t=Z(t)?t:hx,e=Z(e)?e:dx;function n(o,s){if(!xm(o))throw new Error(e(o));if(r.has(o))throw new Error(t(o));r.set(o,s)}function i(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Pa;const[s,l]=rd.get(o)||rd.get(Pa);return[...r].sort((a,c)=>a[0]<c[0]?s:l).map(a=>a[1])}return{addItem:n,getItems:i}}const gx=t=>"The id '".concat(t,"' is already declared in a map.");function As(){let{errorIdExists:t}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=new Map;t=Z(t)?t:gx;function r(c,d){if(a(c))throw new Error(t(c));e.set(c,d)}function n(c){return e.delete(c)}function i(){e.clear()}function o(c){const[d]=l().find(f=>{let[m,p]=f;return c===p?m:!1})||[null];return d}function s(c){return e.get(c)}function l(){return[...e]}function a(c){return e.has(c)}return{addItem:r,clear:i,getId:o,getItem:s,getItems:l,hasItem:a,removeItem:n}}const mx=t=>"'".concat(t,"' value is already declared in a unique set.");function px(){let{errorItemExists:t}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=new Set;t=Z(t)?t:mx;function r(o){if(e.has(o))throw new Error(t(o));e.add(o)}function n(){return[...e]}function i(){e.clear()}return{addItem:r,clear:i,getItems:n}}const _m=t=>'There is already registered "'.concat(t,'" plugin.'),wx=t=>'There is already registered plugin on priority "'.concat(t,'".'),vx=t=>'The priority "'.concat(t,'" is not a number.'),yx=fx({errorPriorityExists:wx,errorPriorityNaN:vx}),Cx=px({errorItemExists:_m}),bx=As({errorIdExists:_m});function Sx(){return[...yx.getItems(),...Cx.getItems()]}function Rx(t){const e=sc(t);return bx.getItem(e)}const{register:Tx,getItem:Ex,hasItem:Ox,getNames:PD,getValues:AD}=Qn("renderers");function nd(t){if(typeof t=="function")return t;if(!Ox(t))throw Error('No registered renderer found under "'.concat(t,'" name'));return Ex(t)}function Hx(t,e){typeof t!="string"&&(e=t,t=e.RENDERER_TYPE),Tx(t,e)}const{register:Ix,getItem:Mx,hasItem:xx,getNames:ND,getValues:$D}=Qn("validators");function _x(t){if(typeof t=="function")return t;if(!xx(t))throw Error('No registered validator found under "'.concat(t,'" name'));return Mx(t)}function Px(t,e){typeof t!="string"&&(e=t,t=e.VALIDATOR_TYPE),Ix(t,e)}var Ax=oe,Nx=Kn,$x=Xn,Lx=Jo,Dx=dc;Ax({target:"Array",proto:!0},{at:function(e){var r=Nx(this),n=$x(r),i=Lx(e),o=i>=0?i:n+i;return o<0||o>=n?void 0:r[o]}});Dx("at");var Vx=oe,kx=be,Fx=Xo,Bx=Jo,Wx=Os,jx=De,zx=kx("".charAt),Ux=jx(function(){return"𠮷".at(-2)!=="\uD842"});Vx({target:"String",proto:!0,forced:Ux},{at:function(e){var r=Wx(Fx(this)),n=r.length,i=Bx(e),o=i>=0?i:n+i;return o<0||o>=n?void 0:zx(r,o)}});function go(t,e,r){return(e=Gx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Gx(t){var e=Kx(t,"string");return typeof e=="symbol"?e:e+""}function Kx(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class Yx{constructor(){go(this,"count",0),go(this,"startColumn",null),go(this,"endColumn",null),go(this,"startPosition",null),go(this,"isVisibleInTrimmingContainer",!1)}initialize(){}process(e,r){const{totalCalculatedWidth:n,zeroBasedScrollOffset:i,viewportWidth:o,columnWidth:s}=r,l=i>0?o+1:o;n>=i&&n+s<=i+l&&((this.startColumn===null||this.startColumn===void 0)&&(this.startColumn=e),this.endColumn=e)}finalize(e){var r;const{scrollOffset:n,viewportWidth:i,inlineStartOffset:o,zeroBasedScrollOffset:s,totalColumns:l,needReverse:a,startPositions:c,columnWidth:d}=e;if(this.endColumn===l-1&&a)for(this.startColumn=this.endColumn;this.startColumn>0;){const v=c[this.endColumn]+d-c[this.startColumn-1];if(v<=i&&(this.startColumn-=1),v>=i)break}this.startPosition=(r=c[this.startColumn])!==null&&r!==void 0?r:null;const f=s>0?i+1:i,m=n+i-f,p=this.startColumn===null?0:e.getColumnWidth(this.startColumn);m<-1*o||n>c.at(-1)||-1*n-i>-1*p?this.isVisibleInTrimmingContainer=!1:this.isVisibleInTrimmingContainer=!0,l<this.endColumn&&(this.endColumn=l-1),this.startColumn!==null&&(this.count=this.endColumn-this.startColumn+1)}}function mo(t,e,r){return(e=Xx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Xx(t){var e=qx(t,"string");return typeof e=="symbol"?e:e+""}function qx(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class Qx{constructor(){mo(this,"count",0),mo(this,"startRow",null),mo(this,"endRow",null),mo(this,"startPosition",null),mo(this,"isVisibleInTrimmingContainer",!1)}initialize(){}process(e,r){const{totalCalculatedHeight:n,zeroBasedScrollOffset:i,innerViewportHeight:o,rowHeight:s}=r;n>=i&&n+s<=o&&(this.startRow===null&&(this.startRow=e),this.endRow=e)}finalize(e){var r;const{scrollOffset:n,viewportHeight:i,horizontalScrollbarHeight:o,totalRows:s,needReverse:l,startPositions:a,rowHeight:c}=e;if(this.endRow===s-1&&l)for(this.startRow=this.endRow;this.startRow>0;){const m=a[this.endRow]+c-a[this.startRow-1];if(m<=i-o&&(this.startRow-=1),m>=i-o)break}this.startPosition=(r=a[this.startRow])!==null&&r!==void 0?r:null;const d=n+i-o,f=this.startRow===null?0:e.getRowHeight(this.startRow);d<f||n>a.at(-1)?this.isVisibleInTrimmingContainer=!1:this.isVisibleInTrimmingContainer=!0,s<this.endRow&&(this.endRow=s-1),this.startRow!==null&&(this.count=this.endRow-this.startRow+1)}}function po(t,e,r){return(e=Zx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Zx(t){var e=Jx(t,"string");return typeof e=="symbol"?e:e+""}function Jx(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class Pm{constructor(){po(this,"count",0),po(this,"startColumn",null),po(this,"endColumn",null),po(this,"startPosition",null),po(this,"isVisibleInTrimmingContainer",!1)}initialize(){}process(e,r){const{totalCalculatedWidth:n,zeroBasedScrollOffset:i,viewportWidth:o}=r;n<=i&&(this.startColumn=e);const s=i>0?o+1:o;n>=i&&n<=i+s&&(this.startColumn===null||this.startColumn===void 0)&&(this.startColumn=e),this.endColumn=e}finalize(e){var r;const{scrollOffset:n,viewportWidth:i,inlineStartOffset:o,zeroBasedScrollOffset:s,totalColumns:l,needReverse:a,startPositions:c,columnWidth:d}=e;if(this.endColumn===l-1&&a)for(this.startColumn=this.endColumn;this.startColumn>0;){const p=c[this.endColumn]+d-c[this.startColumn-1];if(this.startColumn-=1,p>i)break}this.startPosition=(r=c[this.startColumn])!==null&&r!==void 0?r:null;const f=s>0?i+1:i;n+i-f<-1*o||n>c.at(-1)+d||-1*n-i>0?this.isVisibleInTrimmingContainer=!1:this.isVisibleInTrimmingContainer=!0,l<this.endColumn&&(this.endColumn=l-1),this.startColumn!==null&&(this.count=this.endColumn-this.startColumn+1)}}function wo(t,e,r){return(e=e_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e_(t){var e=t_(t,"string");return typeof e=="symbol"?e:e+""}function t_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class Am{constructor(){wo(this,"count",0),wo(this,"startRow",null),wo(this,"endRow",null),wo(this,"startPosition",null),wo(this,"isVisibleInTrimmingContainer",!1)}initialize(){}process(e,r){const{totalCalculatedHeight:n,zeroBasedScrollOffset:i,innerViewportHeight:o}=r;n<=i&&(this.startRow=e),n>=i&&n<=o&&this.startRow===null&&(this.startRow=e),this.endRow=e}finalize(e){var r;const{scrollOffset:n,viewportHeight:i,horizontalScrollbarHeight:o,totalRows:s,needReverse:l,startPositions:a,rowHeight:c}=e;if(this.endRow===s-1&&l)for(this.startRow=this.endRow;this.startRow>0;){const f=a[this.endRow]+c-a[this.startRow-1];if(this.startRow-=1,f>=i-o)break}this.startPosition=(r=a[this.startRow])!==null&&r!==void 0?r:null,n+i-o<0||n>a.at(-1)+c?this.isVisibleInTrimmingContainer=!1:this.isVisibleInTrimmingContainer=!0,s<this.endRow&&(this.endRow=s-1),this.startRow!==null&&(this.count=this.endRow-this.startRow+1)}}function vo(t,e,r){return(e=r_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function r_(t){var e=n_(t,"string");return typeof e=="symbol"?e:e+""}function n_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class o_{constructor(){vo(this,"count",0),vo(this,"startColumn",0),vo(this,"endColumn",0),vo(this,"startPosition",0),vo(this,"isVisibleInTrimmingContainer",!0)}initialize(e){let{totalColumns:r}=e;this.count=r,this.endColumn=this.count-1}process(){}finalize(){}}function yo(t,e,r){return(e=i_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function i_(t){var e=s_(t,"string");return typeof e=="symbol"?e:e+""}function s_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class l_{constructor(){yo(this,"count",0),yo(this,"startRow",0),yo(this,"endRow",0),yo(this,"startPosition",0),yo(this,"isVisibleInTrimmingContainer",!0)}initialize(e){let{totalRows:r}=e;this.count=r,this.endRow=this.count-1}process(){}finalize(){}}function od(t,e,r){return(e=a_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function a_(t){var e=c_(t,"string");return typeof e=="symbol"?e:e+""}function c_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class u_ extends Pm{constructor(){super(...arguments),od(this,"columnStartOffset",0),od(this,"columnEndOffset",0)}finalize(e){var r;super.finalize(e);const{overrideFn:n,totalColumns:i,startPositions:o}=e;if(this.startColumn!==null&&typeof n=="function"){const s=this.startColumn,l=this.endColumn;n(this),this.columnStartOffset=s-this.startColumn,this.columnEndOffset=this.endColumn-l}this.startColumn<0&&(this.startColumn=0),this.startPosition=(r=o[this.startColumn])!==null&&r!==void 0?r:null,i<this.endColumn&&(this.endColumn=i-1),this.startColumn!==null&&(this.count=this.endColumn-this.startColumn+1)}}function id(t,e,r){return(e=h_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h_(t){var e=d_(t,"string");return typeof e=="symbol"?e:e+""}function d_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class f_ extends Am{constructor(){super(...arguments),id(this,"rowStartOffset",0),id(this,"rowEndOffset",0)}finalize(e){var r;super.finalize(e);const{overrideFn:n,totalRows:i,startPositions:o}=e;if(this.startRow!==null&&typeof n=="function"){const s=this.startRow,l=this.endRow;n(this),this.rowStartOffset=s-this.startRow,this.rowEndOffset=this.endRow-l}this.startRow<0&&(this.startRow=0),this.startPosition=(r=o[this.startRow])!==null&&r!==void 0?r:null,i<this.endRow&&(this.endRow=i-1),this.startRow!==null&&(this.count=this.endRow-this.startRow+1)}}function sd(t,e,r){return(e=g_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g_(t){var e=m_(t,"string");return typeof e=="symbol"?e:e+""}function m_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class Nm{constructor(e){sd(this,"calculationTypes",[]),sd(this,"calculationResults",new Map),this.calculationTypes=e}_initialize(e){this.calculationTypes.forEach(r=>{let[n,i]=r;this.calculationResults.set(n,i),i.initialize(e)})}_process(e,r){this.calculationTypes.forEach(n=>{let[,i]=n;return i.process(e,r)})}_finalize(e){this.calculationTypes.forEach(r=>{let[,n]=r;return n.finalize(e)})}getResultsFor(e){return this.calculationResults.get(e)}}function zt(t,e,r){return(e=p_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p_(t){var e=w_(t,"string");return typeof e=="symbol"?e:e+""}function w_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}const $m=50;class v_ extends Nm{constructor(e){let{calculationTypes:r,viewportWidth:n,scrollOffset:i,totalColumns:o,columnWidthFn:s,overrideFn:l,inlineStartOffset:a}=e;super(r),zt(this,"viewportWidth",0),zt(this,"scrollOffset",0),zt(this,"zeroBasedScrollOffset",0),zt(this,"totalColumns",0),zt(this,"columnWidthFn",null),zt(this,"columnWidth",0),zt(this,"overrideFn",null),zt(this,"inlineStartOffset",0),zt(this,"totalCalculatedWidth",0),zt(this,"startPositions",[]),zt(this,"needReverse",!0),this.viewportWidth=n,this.scrollOffset=i,this.zeroBasedScrollOffset=Math.max(i,0),this.totalColumns=o,this.columnWidthFn=s,this.overrideFn=l,this.inlineStartOffset=a,this.calculate()}calculate(){this._initialize(this);for(let e=0;e<this.totalColumns;e++)if(this.columnWidth=this.getColumnWidth(e),this._process(e,this),this.startPositions.push(this.totalCalculatedWidth),this.totalCalculatedWidth+=this.columnWidth,this.totalCalculatedWidth>=this.zeroBasedScrollOffset+this.viewportWidth){this.needReverse=!1;break}this._finalize(this)}getColumnWidth(e){const r=this.columnWidthFn(e);return isNaN(r)?$m:r}}function Ht(t,e,r){return(e=y_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y_(t){var e=C_(t,"string");return typeof e=="symbol"?e:e+""}function C_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class b_ extends Nm{constructor(e){let{calculationTypes:r,viewportHeight:n,scrollOffset:i,totalRows:o,defaultRowHeight:s,rowHeightFn:l,overrideFn:a,horizontalScrollbarHeight:c}=e;super(r),Ht(this,"viewportHeight",0),Ht(this,"scrollOffset",0),Ht(this,"zeroBasedScrollOffset",0),Ht(this,"totalRows",0),Ht(this,"rowHeightFn",null),Ht(this,"rowHeight",0),Ht(this,"overrideFn",null),Ht(this,"horizontalScrollbarHeight",0),Ht(this,"innerViewportHeight",0),Ht(this,"totalCalculatedHeight",0),Ht(this,"startPositions",[]),Ht(this,"needReverse",!0),this.defaultHeight=s,this.viewportHeight=n,this.scrollOffset=i,this.zeroBasedScrollOffset=Math.max(i,0),this.totalRows=o,this.rowHeightFn=l,this.overrideFn=a,this.horizontalScrollbarHeight=c!=null?c:0,this.innerViewportHeight=this.zeroBasedScrollOffset+this.viewportHeight-this.horizontalScrollbarHeight,this.calculate()}calculate(){this._initialize(this);for(let e=0;e<this.totalRows;e++)if(this.rowHeight=this.getRowHeight(e),this._process(e,this),this.startPositions.push(this.totalCalculatedHeight),this.totalCalculatedHeight+=this.rowHeight,this.totalCalculatedHeight>=this.innerViewportHeight){this.needReverse=!1;break}this._finalize(this)}getRowHeight(e){const r=this.rowHeightFn(e);return isNaN(r)?this.defaultHeight:r}}function S_(t,e,r){R_(t,e),e.set(t,r)}function R_(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function ld(t,e,r){return(e=T_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function T_(t){var e=E_(t,"string");return typeof e=="symbol"?e:e+""}function E_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function On(t,e){return t.get(Lm(t,e))}function ad(t,e,r){return t.set(Lm(t,e),r),r}function Lm(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var yr=new WeakMap;class ws{constructor(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;ld(this,"row",null),ld(this,"col",null),S_(this,yr,!1),ad(yr,this,n),typeof e<"u"&&typeof r<"u"&&(this.row=e,this.col=r)}isValid(e){const{countRows:r,countCols:n,countRowHeaders:i,countColHeaders:o}={countRows:0,countCols:0,countRowHeaders:0,countColHeaders:0,...e};return!(!Number.isInteger(this.row)||!Number.isInteger(this.col)||this.row<-o||this.col<-i||this.row>=r||this.col>=n)}isEqual(e){return e===this?!0:this.row===e.row&&this.col===e.col}isHeader(){return!this.isCell()}isCell(){return this.row>=0&&this.col>=0}isRtl(){return On(yr,this)}isSouthEastOf(e){return this.row>=e.row&&(On(yr,this)?this.col<=e.col:this.col>=e.col)}isNorthWestOf(e){return this.row<=e.row&&(On(yr,this)?this.col>=e.col:this.col<=e.col)}isSouthWestOf(e){return this.row>=e.row&&(On(yr,this)?this.col>=e.col:this.col<=e.col)}isNorthEastOf(e){return this.row<=e.row&&(On(yr,this)?this.col<=e.col:this.col>=e.col)}normalize(){return this.row=this.row===null?this.row:Math.max(this.row,0),this.col=this.col===null?this.col:Math.max(this.col,0),this}assign(e){return Number.isInteger(e==null?void 0:e.row)&&(this.row=e.row),Number.isInteger(e==null?void 0:e.col)&&(this.col=e.col),e instanceof ws&&ad(yr,this,e.isRtl()),this}clone(){return new ws(this.row,this.col,On(yr,this))}toObject(){return{row:this.row,col:this.col}}}const vs=ws;function O_(t,e,r){H_(t,e),e.set(t,r)}function H_(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Ll(t,e,r){return(e=I_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function I_(t){var e=M_(t,"string");return typeof e=="symbol"?e:e+""}function M_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function It(t,e){return t.get(Dm(t,e))}function x_(t,e,r){return t.set(Dm(t,e),r),r}function Dm(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var it=new WeakMap;class pc{constructor(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;Ll(this,"highlight",null),Ll(this,"from",null),Ll(this,"to",null),O_(this,it,!1),this.highlight=e.clone(),this.from=r.clone(),this.to=n.clone(),x_(it,this,i)}setHighlight(e){return this.highlight=e.clone(),this}setFrom(e){return this.from=e.clone(),this}setTo(e){return this.to=e.clone(),this}normalize(){return this.highlight.normalize(),this.from.normalize(),this.to.normalize(),this}isValid(e){return this.from.isValid(e)&&this.to.isValid(e)}isSingle(){return this.isSingleCell()||this.isSingleHeader()}isSingleCell(){return this.from.row>=0&&this.from.row===this.to.row&&this.from.col>=0&&this.from.col===this.to.col}isSingleHeader(){return(this.from.row<0||this.from.col<0)&&this.from.row===this.to.row&&this.from.col===this.to.col}isHeader(){return this.from.isHeader()&&this.to.isHeader()?!0:this.from.col<0&&this.to.col<0||this.from.row<0&&this.to.row<0}containsHeaders(){return this.from.isHeader()||this.to.isHeader()}getOuterHeight(){return Math.max(this.from.row,this.to.row)-Math.min(this.from.row,this.to.row)+1}getOuterWidth(){return Math.max(this.from.col,this.to.col)-Math.min(this.from.col,this.to.col)+1}getHeight(){if(this.from.row<0&&this.to.row<0)return 0;const e=Math.max(this.from.row,0),r=Math.max(this.to.row,0);return Math.max(e,r)-Math.min(e,r)+1}getWidth(){if(this.from.col<0&&this.to.col<0)return 0;const e=Math.max(this.from.col,0),r=Math.max(this.to.col,0);return Math.max(e,r)-Math.min(e,r)+1}getCellsCount(){return this.getWidth()*this.getHeight()}includes(e){const{row:r,col:n}=e,i=this.getOuterTopStartCorner(),o=this.getOuterBottomEndCorner();return i.row<=r&&o.row>=r&&i.col<=n&&o.col>=n}includesRange(e){return this.includes(e.getOuterTopStartCorner())&&this.includes(e.getOuterBottomEndCorner())}isEqual(e){return Math.min(this.from.row,this.to.row)===Math.min(e.from.row,e.to.row)&&Math.max(this.from.row,this.to.row)===Math.max(e.from.row,e.to.row)&&Math.min(this.from.col,this.to.col)===Math.min(e.from.col,e.to.col)&&Math.max(this.from.col,this.to.col)===Math.max(e.from.col,e.to.col)}overlaps(e){return e.isSouthEastOf(this.getOuterTopLeftCorner())&&e.isNorthWestOf(this.getOuterBottomRightCorner())}isSouthEastOf(e){return this.getOuterTopLeftCorner().isSouthEastOf(e)||this.getOuterBottomRightCorner().isSouthEastOf(e)}isNorthWestOf(e){return this.getOuterTopLeftCorner().isNorthWestOf(e)||this.getOuterBottomRightCorner().isNorthWestOf(e)}isOverlappingHorizontally(e){return this.getOuterTopEndCorner().col>=e.getOuterTopStartCorner().col&&this.getOuterTopEndCorner().col<=e.getOuterTopEndCorner().col||this.getOuterTopStartCorner().col<=e.getOuterTopEndCorner().col&&this.getOuterTopStartCorner().col>=e.getOuterTopStartCorner().col}isOverlappingVertically(e){return this.getOuterBottomStartCorner().row>=e.getOuterTopRightCorner().row&&this.getOuterBottomStartCorner().row<=e.getOuterBottomStartCorner().row||this.getOuterTopEndCorner().row<=e.getOuterBottomStartCorner().row&&this.getOuterTopEndCorner().row>=e.getOuterTopRightCorner().row}expand(e){const r=this.getOuterTopStartCorner(),n=this.getOuterBottomEndCorner();return e.row<r.row||e.col<r.col||e.row>n.row||e.col>n.col?(this.from=this._createCellCoords(Math.min(r.row,e.row),Math.min(r.col,e.col)),this.to=this._createCellCoords(Math.max(n.row,e.row),Math.max(n.col,e.col)),!0):!1}expandByRange(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(this.includesRange(e)||!this.overlaps(e))return!1;const n=this.getOuterTopStartCorner(),i=this.getOuterBottomEndCorner(),o=this.getDirection(),s=e.getOuterTopStartCorner(),l=e.getOuterBottomEndCorner(),a=Math.min(n.row,s.row),c=Math.min(n.col,s.col),d=Math.max(i.row,l.row),f=Math.max(i.col,l.col),m=this._createCellCoords(a,c),p=this._createCellCoords(d,f);return this.from=m,this.to=p,this.setDirection(o),r&&(this.highlight.row===this.getOuterBottomRightCorner().row&&this.getVerticalDirection()==="N-S"&&this.flipDirectionVertically(),this.highlight.col===this.getOuterTopRightCorner().col&&this.getHorizontalDirection()==="W-E"&&this.flipDirectionHorizontally()),!0}getDirection(){if(this.from.isNorthWestOf(this.to))return"NW-SE";if(this.from.isNorthEastOf(this.to))return"NE-SW";if(this.from.isSouthEastOf(this.to))return"SE-NW";if(this.from.isSouthWestOf(this.to))return"SW-NE"}setDirection(e){switch(e){case"NW-SE":[this.from,this.to]=[this.getOuterTopLeftCorner(),this.getOuterBottomRightCorner()];break;case"NE-SW":[this.from,this.to]=[this.getOuterTopRightCorner(),this.getOuterBottomLeftCorner()];break;case"SE-NW":[this.from,this.to]=[this.getOuterBottomRightCorner(),this.getOuterTopLeftCorner()];break;case"SW-NE":[this.from,this.to]=[this.getOuterBottomLeftCorner(),this.getOuterTopRightCorner()];break}}getVerticalDirection(){return["NE-SW","NW-SE"].indexOf(this.getDirection())>-1?"N-S":"S-N"}getHorizontalDirection(){return["NW-SE","SW-NE"].indexOf(this.getDirection())>-1?"W-E":"E-W"}flipDirectionVertically(){switch(this.getDirection()){case"NW-SE":this.setDirection("SW-NE");break;case"NE-SW":this.setDirection("SE-NW");break;case"SE-NW":this.setDirection("NE-SW");break;case"SW-NE":this.setDirection("NW-SE");break}}flipDirectionHorizontally(){switch(this.getDirection()){case"NW-SE":this.setDirection("NE-SW");break;case"NE-SW":this.setDirection("NW-SE");break;case"SE-NW":this.setDirection("SW-NE");break;case"SW-NE":this.setDirection("SE-NW");break}}getTopStartCorner(){return this._createCellCoords(Math.min(this.from.row,this.to.row),Math.min(this.from.col,this.to.col)).normalize()}getTopLeftCorner(){return It(it,this)?this.getTopEndCorner():this.getTopStartCorner()}getBottomEndCorner(){return this._createCellCoords(Math.max(this.from.row,this.to.row),Math.max(this.from.col,this.to.col)).normalize()}getBottomRightCorner(){return It(it,this)?this.getBottomStartCorner():this.getBottomEndCorner()}getTopEndCorner(){return this._createCellCoords(Math.min(this.from.row,this.to.row),Math.max(this.from.col,this.to.col)).normalize()}getTopRightCorner(){return It(it,this)?this.getTopStartCorner():this.getTopEndCorner()}getBottomStartCorner(){return this._createCellCoords(Math.max(this.from.row,this.to.row),Math.min(this.from.col,this.to.col)).normalize()}getBottomLeftCorner(){return It(it,this)?this.getBottomEndCorner():this.getBottomStartCorner()}getOuterTopStartCorner(){return this._createCellCoords(Math.min(this.from.row,this.to.row),Math.min(this.from.col,this.to.col))}getOuterTopLeftCorner(){return It(it,this)?this.getOuterTopEndCorner():this.getOuterTopStartCorner()}getOuterBottomEndCorner(){return this._createCellCoords(Math.max(this.from.row,this.to.row),Math.max(this.from.col,this.to.col))}getOuterBottomRightCorner(){return It(it,this)?this.getOuterBottomStartCorner():this.getOuterBottomEndCorner()}getOuterTopEndCorner(){return this._createCellCoords(Math.min(this.from.row,this.to.row),Math.max(this.from.col,this.to.col))}getOuterTopRightCorner(){return It(it,this)?this.getOuterTopStartCorner():this.getOuterTopEndCorner()}getOuterBottomStartCorner(){return this._createCellCoords(Math.max(this.from.row,this.to.row),Math.min(this.from.col,this.to.col))}getOuterBottomLeftCorner(){return It(it,this)?this.getOuterBottomEndCorner():this.getOuterBottomStartCorner()}isCorner(e){return e.isEqual(this.getOuterTopLeftCorner())||e.isEqual(this.getOuterTopRightCorner())||e.isEqual(this.getOuterBottomLeftCorner())||e.isEqual(this.getOuterBottomRightCorner())}getOppositeCorner(e){if(!(e instanceof vs))return!1;if(e.isEqual(this.getOuterBottomEndCorner()))return this.getOuterTopStartCorner();if(e.isEqual(this.getOuterTopStartCorner()))return this.getOuterBottomEndCorner();if(e.isEqual(this.getOuterTopEndCorner()))return this.getOuterBottomStartCorner();if(e.isEqual(this.getOuterBottomStartCorner()))return this.getOuterTopEndCorner()}getBordersSharedWith(e){if(!this.includesRange(e))return[];const r={top:Math.min(this.from.row,this.to.row),bottom:Math.max(this.from.row,this.to.row),left:Math.min(this.from.col,this.to.col),right:Math.max(this.from.col,this.to.col)},n={top:Math.min(e.from.row,e.to.row),bottom:Math.max(e.from.row,e.to.row),left:Math.min(e.from.col,e.to.col),right:Math.max(e.from.col,e.to.col)},i=[];return r.top===n.top&&i.push("top"),r.right===n.right&&i.push(It(it,this)?"left":"right"),r.bottom===n.bottom&&i.push("bottom"),r.left===n.left&&i.push(It(it,this)?"right":"left"),i}getInner(){const e=this.getOuterTopStartCorner(),r=this.getOuterBottomEndCorner(),n=[];for(let i=e.row;i<=r.row;i++)for(let o=e.col;o<=r.col;o++)!(this.from.row===i&&this.from.col===o)&&!(this.to.row===i&&this.to.col===o)&&n.push(this._createCellCoords(i,o));return n}getAll(){const e=this.getOuterTopStartCorner(),r=this.getOuterBottomEndCorner(),n=[];for(let i=e.row;i<=r.row;i++)for(let o=e.col;o<=r.col;o++)e.row===i&&e.col===o?n.push(e):r.row===i&&r.col===o?n.push(r):n.push(this._createCellCoords(i,o));return n}forAll(e){const r=this.getOuterTopStartCorner(),n=this.getOuterBottomEndCorner();for(let i=r.row;i<=n.row;i++)for(let o=r.col;o<=n.col;o++)if(e(i,o)===!1)return}clone(){return new pc(this.highlight,this.from,this.to,It(it,this))}toObject(){return{from:this.from.toObject(),to:this.to.toObject()}}_createCellCoords(e,r){return new vs(e,r,It(it,this))}}const wc=pc;function nr(t,e,r){__(t,e),e.set(t,r)}function __(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _(t,e){return t.get(Vm(t,e))}function Ar(t,e,r){return t.set(Vm(t,e),r),r}function Vm(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var Mt=new WeakMap,Dl=new WeakMap,$e=new WeakMap,Hn=new WeakMap,Mi=new WeakMap,Ut=new WeakMap,Vl=new WeakMap,Co=new WeakMap,Qr=new WeakMap,or=new WeakMap;class P_{constructor(e,r,n,i,o,s){let l=arguments.length>6&&arguments[6]!==void 0?arguments[6]:null;nr(this,Mt,void 0),nr(this,Dl,void 0),nr(this,$e,void 0),nr(this,Hn,void 0),nr(this,Mi,void 0),nr(this,Ut,void 0),nr(this,Vl,void 0),nr(this,Co,void 0),nr(this,Qr,[null,null]),nr(this,or,[null,null]),Ar(Mt,this,n),Ar(Dl,this,r),Ar($e,this,o),Ar(Hn,this,s),Ar(Mi,this,l),Ar(Ut,this,i),Ar(Vl,this,e),this.registerEvents()}registerEvents(){_(Ut,this).addEventListener(_($e,this).holder,"contextmenu",n=>this.onContextMenu(n)),_(Ut,this).addEventListener(_($e,this).TABLE,"mouseover",n=>this.onMouseOver(n)),_(Ut,this).addEventListener(_($e,this).TABLE,"mouseout",n=>this.onMouseOut(n));const e=()=>{_(Ut,this).addEventListener(_($e,this).holder,"touchstart",n=>this.onTouchStart(n)),_(Ut,this).addEventListener(_($e,this).holder,"touchend",n=>this.onTouchEnd(n)),this.momentumScrolling||(this.momentumScrolling={}),_(Ut,this).addEventListener(_($e,this).holder,"scroll",()=>{clearTimeout(this.momentumScrolling._timeout),this.momentumScrolling.ongoing||_(Mt,this).getSetting("onBeforeTouchScroll"),this.momentumScrolling.ongoing=!0,this.momentumScrolling._timeout=setTimeout(()=>{this.touchApplied||(this.momentumScrolling.ongoing=!1,_(Mt,this).getSetting("onAfterMomentumScroll"))},200)})},r=()=>{_(Ut,this).addEventListener(_($e,this).holder,"mouseup",n=>this.onMouseUp(n)),_(Ut,this).addEventListener(_($e,this).holder,"mousedown",n=>this.onMouseDown(n))};Vn()?e():(dI()&&e(),r())}selectedCellWasTouched(e){const n=this.parentCell(e).coords;if(_(Co,this)&&n){const[i,o]=[n.row,_(Co,this).from.row],[s,l]=[n.col,_(Co,this).from.col];return i===o&&s===l}return!1}parentCell(e){const r={},n=_($e,this).TABLE,i=Si(e,["TD","TH"],n);return i?(r.coords=_($e,this).getCoords(i),r.TD=i):ee(e,"wtBorder")&&ee(e,"current")?(r.coords=_(Hn,this).getFocusSelection().cellRange.highlight,r.TD=_($e,this).getCell(r.coords)):ee(e,"wtBorder")&&ee(e,"area")&&_(Hn,this).getAreaSelection().cellRange&&(r.coords=_(Hn,this).getAreaSelection().cellRange.to,r.TD=_($e,this).getCell(r.coords)),r}onMouseDown(e){const r=_(Dl,this).rootDocument.activeElement,n=YH(PH,e.target),i=e.target;if(!["TD","TH"].includes(r.nodeName)&&(i===r||n(0)===r||n(1)===r))return;const o=this.parentCell(i);ee(i,"corner")?_(Mt,this).getSetting("onCellCornerMouseDown",e,i):o.TD&&_(Mt,this).has("onCellMouseDown")&&this.callListener("onCellMouseDown",e,o.coords,o.TD),(e.button===0||this.touchApplied)&&o.TD&&(_(or,this)[0]=o.TD,clearTimeout(_(Qr,this)[0]),_(Qr,this)[0]=setTimeout(()=>{_(or,this)[0]=null},1e3))}onContextMenu(e){if(_(Mt,this).has("onCellContextMenu")){const r=this.parentCell(e.target);r.TD&&this.callListener("onCellContextMenu",e,r.coords,r.TD)}}onMouseOver(e){if(!_(Mt,this).has("onCellMouseOver"))return;const r=_($e,this).TABLE,n=Si(e.target,["TD","TH"],r),i=_(Mi,this)||this;n&&n!==i.lastMouseOver&&Ea(n,r)&&(i.lastMouseOver=n,this.callListener("onCellMouseOver",e,_($e,this).getCoords(n),n))}onMouseOut(e){if(!_(Mt,this).has("onCellMouseOut"))return;const r=_($e,this).TABLE,n=Si(e.target,["TD","TH"],r),i=Si(e.relatedTarget,["TD","TH"],r),o=_(Mi,this)||this;n&&n!==i&&Ea(n,r)&&(this.callListener("onCellMouseOut",e,_($e,this).getCoords(n),n),i===null&&(o.lastMouseOver=null))}onMouseUp(e){const r=this.parentCell(e.target);r.TD&&_(Mt,this).has("onCellMouseUp")&&this.callListener("onCellMouseUp",e,r.coords,r.TD),!(e.button!==0&&!this.touchApplied)&&(r.TD===_(or,this)[0]&&r.TD===_(or,this)[1]?(ee(e.target,"corner")?this.callListener("onCellCornerDblClick",e,r.coords,r.TD):this.callListener("onCellDblClick",e,r.coords,r.TD),_(or,this)[0]=null,_(or,this)[1]=null):r.TD===_(or,this)[0]&&(_(or,this)[1]=r.TD,clearTimeout(_(Qr,this)[1]),_(Qr,this)[1]=setTimeout(()=>{_(or,this)[1]=null},500)))}onTouchStart(e){Ar(Co,this,_(Hn,this).getFocusSelection().cellRange),this.touchApplied=!0,this.onMouseDown(e)}onTouchEnd(e){var r;const n=e.target,i=(r=this.parentCell(n))===null||r===void 0?void 0:r.coords,o=X(i)&&i.row>=0&&i.col>=0;if(e.cancelable&&o&&_(Mt,this).getSetting("isDataViewInstance")){const l=["A","BUTTON","INPUT"];Sm()&&(wI()||vI())&&this.selectedCellWasTouched(n)&&!l.includes(n.tagName)?e.preventDefault():this.selectedCellWasTouched(n)||e.preventDefault()}this.onMouseUp(e),this.touchApplied=!1}callListener(e,r,n,i){const o=_(Mt,this).getSettingPure(e);o&&o(r,n,i,_(Vl,this).call(this))}destroy(){clearTimeout(_(Qr,this)[0]),clearTimeout(_(Qr,this)[1]),_(Ut,this).destroy()}}const km=P_;function kl(t,e,r){return(e=A_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function A_(t){var e=N_(t,"string");return typeof e=="symbol"?e:e+""}function N_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class $_{constructor(e,r,n){kl(this,"offset",void 0),kl(this,"total",void 0),kl(this,"countTH",void 0),this.offset=e,this.total=r,this.countTH=n}offsetted(e){return e+this.offset}unOffsetted(e){return e-this.offset}renderedToSource(e){return this.offsetted(e)}sourceToRendered(e){return this.unOffsetted(e)}offsettedTH(e){return e-this.countTH}unOffsettedTH(e){return e+this.countTH}visibleRowHeadedColumnToSourceColumn(e){return this.renderedToSource(this.offsettedTH(e))}sourceColumnToVisibleRowHeadedColumn(e){return this.unOffsettedTH(this.sourceToRendered(e))}}const L_=$_;function Fl(t,e,r){return(e=D_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function D_(t){var e=V_(t,"string");return typeof e=="symbol"?e:e+""}function V_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class k_{constructor(e,r,n){Fl(this,"offset",void 0),Fl(this,"total",void 0),Fl(this,"countTH",void 0),this.offset=e,this.total=r,this.countTH=n}offsetted(e){return e+this.offset}unOffsetted(e){return e-this.offset}renderedToSource(e){return this.offsetted(e)}sourceToRendered(e){return this.unOffsetted(e)}offsettedTH(e){return e-this.countTH}unOffsettedTH(e){return e+this.countTH}visibleColHeadedRowToSourceRow(e){return this.renderedToSource(this.offsettedTH(e))}sourceRowToVisibleColHeadedRow(e){return this.unOffsettedTH(this.sourceToRendered(e))}}const F_=k_;function xi(t,e,r){return(e=B_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function B_(t){var e=W_(t,"string");return typeof e=="symbol"?e:e+""}function W_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class j_{constructor(){xi(this,"currentSize",0),xi(this,"nextSize",0),xi(this,"currentOffset",0),xi(this,"nextOffset",0)}setSize(e){this.currentSize=this.nextSize,this.nextSize=e}setOffset(e){this.currentOffset=this.nextOffset,this.nextOffset=e}}const z_=0,cd=1,Aa=2;function Bl(t,e,r){return(e=U_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function U_(t){var e=G_(t,"string");return typeof e=="symbol"?e:e+""}function G_(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class K_{constructor(){Bl(this,"size",new j_),Bl(this,"workingSpace",z_),Bl(this,"sharedSize",null)}setSize(e){this.size.setSize(e)}setOffset(e){this.size.setOffset(e)}getViewSize(){return this.size}isShared(){return this.sharedSize!==null}isPlaceOn(e){return this.workingSpace===e}append(e){this.workingSpace=cd,e.workingSpace=Aa,this.sharedSize=e.getViewSize()}prepend(e){this.workingSpace=Aa,e.workingSpace=cd,this.sharedSize=e.getViewSize()}}var ud=Es,Y_=TypeError,X_=function(t,e){if(!delete t[e])throw new Y_("Cannot delete property "+ud(e)+" of "+ud(t))},q_=oe,Q_=Kn,Z_=Xn,J_=lg,eP=X_,tP=ag,rP=[].unshift(0)!==1,nP=function(){try{Object.defineProperty([],"length",{writable:!1}).unshift()}catch(t){return t instanceof TypeError}},oP=rP||!nP();q_({target:"Array",proto:!0,arity:1,forced:oP},{unshift:function(e){var r=Q_(this),n=Z_(r),i=arguments.length;if(i){tP(n+i);for(var o=n;o--;){var s=o+i;o in r?r[s]=r[o]:eP(r,s)}for(var l=0;l<i;l++)r[l]=arguments[l]}return J_(r,n+i)}});function iP(t,e,r){return(e=sP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sP(t){var e=lP(t,"string");return typeof e=="symbol"?e:e+""}function lP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class hd{constructor(e,r){iP(this,"order",[]),this.order=[...Array(r).keys()].map(n=>e+n)}get length(){return this.order.length}has(e){return this.order.indexOf(e)>-1}get(e){return e<this.order.length?this.order[e]:-1}remove(e){this.order.splice(this.order.indexOf(e),1)}prepend(e){return this.order.unshift(e),this.order.pop()}}function aP(t,e,r){return(e=cP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function cP(t){var e=uP(t,"string");return typeof e=="symbol"?e:e+""}function uP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class hP{constructor(e){aP(this,"sizeSet",void 0),this.sizeSet=e}diff(){const{sizeSet:e}=this,{currentSize:r,nextSize:n}=e.getViewSize();let i=Math.max(n,r);if(i===0)return[];const{currentOffset:o,nextOffset:s}=e.getViewSize(),l=new hd(o,r),a=new hd(s,n),c=[];for(let d=0;d<i;d++){const f=l.get(d),m=a.get(d);if(m===-1)c.push(["remove",f]);else if(f===-1)!e.isShared()||e.isShared()&&e.isPlaceOn(Aa)?c.push(["append",m]):c.push(["prepend",m]);else if(m>f)l.has(m)&&(l.remove(m),n<=l.length&&(i-=1)),c.push(["replace",m,f]);else if(m<f){const p=l.prepend(m);c.push(["insert_before",m,f,p])}else c.push(["none",m])}return c}}function In(t,e,r){return(e=dP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dP(t){var e=fP(t,"string");return typeof e=="symbol"?e:e+""}function fP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class Fm{constructor(e,r){In(this,"rootNode",void 0),In(this,"nodesPool",void 0),In(this,"sizeSet",new K_),In(this,"collectedNodes",[]),In(this,"viewDiffer",new hP(this.sizeSet)),In(this,"leads",[]),this.rootNode=e,this.nodesPool=r}setSize(e){return this.sizeSet.setSize(e),this}setOffset(e){return this.sizeSet.setOffset(e),this}isSharedViewSet(){return this.sizeSet.isShared()}getNode(e){return e<this.collectedNodes.length?this.collectedNodes[e]:null}getCurrentNode(){const e=this.collectedNodes.length;return e>0?this.collectedNodes[e-1]:null}applyCommand(e){const{rootNode:r}=this,[n,i,o,s]=e,l=this.nodesPool(i);switch(this.collectedNodes.push(l),n){case"prepend":r.insertBefore(l,r.firstChild);break;case"append":r.appendChild(l);break;case"insert_before":r.insertBefore(l,this.nodesPool(o)),r.removeChild(this.nodesPool(s));break;case"replace":r.replaceChild(l,this.nodesPool(o));break;case"remove":r.removeChild(l);break}}start(){this.collectedNodes.length=0,this.leads=this.viewDiffer.diff()}render(){this.leads.length>0&&this.applyCommand(this.leads.shift())}end(){for(;this.leads.length>0;)this.applyCommand(this.leads.shift())}}class Bm extends Fm{prependView(e){return this.sizeSet.prepend(e.sizeSet),e.sizeSet.append(this.sizeSet),this}appendView(e){return this.sizeSet.append(e.sizeSet),e.sizeSet.prepend(this.sizeSet),this}}function dd(t,e,r){return(e=gP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function gP(t){var e=mP(t,"string");return typeof e=="symbol"?e:e+""}function mP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class pP{constructor(e){dd(this,"nodeType",void 0),dd(this,"pool",new Map),this.nodeType=e.toUpperCase()}setRootDocument(e){this.rootDocument=e}obtain(e,r){const i=typeof r=="number"?"".concat(e,"x").concat(r):e.toString();if(this.pool.has(i))return this.pool.get(i);const o=this.rootDocument.createElement(this.nodeType);return this.pool.set(i,o),o}}function bo(t,e,r){return(e=wP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function wP(t){var e=vP(t,"string");return typeof e=="symbol"?e:e+""}function vP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class ai{constructor(e,r){bo(this,"nodesPool",null),bo(this,"nodeType",void 0),bo(this,"rootNode",void 0),bo(this,"table",null),bo(this,"renderedNodes",0),this.nodesPool=typeof e=="string"?new pP(e):null,this.nodeType=e,this.rootNode=r}setTable(e){this.nodesPool&&this.nodesPool.setRootDocument(e.rootDocument),this.table=e}adjust(){}render(){}}function fd(t,e,r){return(e=yP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yP(t){var e=CP(t,"string");return typeof e=="symbol"?e:e+""}function CP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class bP extends ai{constructor(){super("TH"),fd(this,"orderViews",new WeakMap),fd(this,"sourceRowIndex",0)}obtainOrderView(e){let r;return this.orderViews.has(e)?r=this.orderViews.get(e):(r=new Bm(e,n=>this.nodesPool.obtain(this.sourceRowIndex,n)),this.orderViews.set(e,r)),r}render(){const{rowsToRender:e,rowHeaderFunctions:r,rowHeadersCount:n,rows:i,cells:o}=this.table;for(let s=0;s<e;s++){const l=this.table.renderedRowToSource(s),a=i.getRenderedNode(s);this.sourceRowIndex=l;const c=this.obtainOrderView(a),d=o.obtainOrderView(a);c.appendView(d).setSize(n).setOffset(0).start();for(let f=n-1;f>=0;f--){c.render();const m=c.getCurrentNode();m.className="",m.removeAttribute("style"),ii(m,[new RegExp("aria-(.*)"),new RegExp("role")]),this.table.isAriaEnabled()&&de(m,[OH(),MH(),lc(f+1),oi(-1)]),r[f](l,m,f)}c.end()}}}class SP extends ai{constructor(e){super(null,e)}adjust(){const{columnHeadersCount:e,rowHeadersCount:r}=this.table;let n=this.rootNode.firstChild;if(e){const{columnsToRender:i}=this.table,o=i+r;for(let l=0,a=e;l<a;l++){for(n=this.rootNode.childNodes[l],n||(n=this.table.rootDocument.createElement("tr"),this.rootNode.appendChild(n)),this.renderedNodes=n.childNodes.length;this.renderedNodes<o;)n.appendChild(this.table.rootDocument.createElement("th")),this.renderedNodes+=1;for(;this.renderedNodes>o;)n.removeChild(n.lastChild),this.renderedNodes-=1}const s=this.rootNode.childNodes.length;if(s>e)for(let l=e;l<s;l++)this.rootNode.removeChild(this.rootNode.lastChild)}else n&&Uo(n)}render(){const{columnHeadersCount:e}=this.table;this.table.isAriaEnabled()&&de(this.rootNode,[nm()]);for(let r=0;r<e;r+=1){const{columnHeaderFunctions:n,columnsToRender:i,rowHeadersCount:o}=this.table,s=this.rootNode.childNodes[r];this.table.isAriaEnabled()&&de(s,[Ta(),im(r+1)]);for(let l=-1*o;l<i;l+=1){const a=this.table.renderedColumnToSource(l),c=s.childNodes[l+o];c.className="",c.removeAttribute("style"),ii(c,[new RegExp("aria-(.*)"),new RegExp("role")]),this.table.isAriaEnabled()&&de(c,[lc(l+1+this.table.rowHeadersCount),oi(-1),HH(),...l>=0?[IH()]:[Ta()]]),n[r](a,c,r)}}}}let gd=!1;var wf;class RP extends ai{constructor(e){super(null,e)}adjust(){const{columnsToRender:e,rowHeadersCount:r}=this.table,n=e+r;for(;this.renderedNodes<n;)this.rootNode.appendChild(this.table.rootDocument.createElement("col")),this.renderedNodes+=1;for(;this.renderedNodes>n;)this.rootNode.removeChild(this.rootNode.lastChild),this.renderedNodes-=1}render(){this.adjust();const{columnsToRender:e,rowHeadersCount:r}=this.table;!gd&&e>1e3&&(gd=!0,Wr(vt(wf||(wf=ot(['Performance tip: Handsontable rendered more than 1000 visible columns. \n        Consider limiting the number of rendered columns by specifying the table width and/or \n        turning off the "renderAllColumns" option.'],['Performance tip: Handsontable rendered more than 1000 visible columns.\\x20\n        Consider limiting the number of rendered columns by specifying the table width and/or\\x20\n        turning off the "renderAllColumns" option.'])))));for(let i=0;i<r;i++){const o=this.table.renderedColumnToSource(i),s=this.table.columnUtils.getHeaderWidth(o);this.rootNode.childNodes[i].style.width="".concat(s,"px")}for(let i=0;i<e;i++){const o=this.table.renderedColumnToSource(i),s=this.table.columnUtils.getWidth(o);this.rootNode.childNodes[i+r].style.width="".concat(s,"px")}const n=this.rootNode.firstChild;n&&G(n,"rowHeader")}}function TP(t,e,r){return(e=EP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function EP(t){var e=OP(t,"string");return typeof e=="symbol"?e:e+""}function OP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}const Mn={rowEven:"ht__row_even",rowOdd:"ht__row_odd"};let md=!1;var vf;class HP extends ai{constructor(e){super("TR",e),TP(this,"orderView",void 0),this.orderView=new Fm(e,r=>this.nodesPool.obtain(r))}getRenderedNode(e){return this.orderView.getNode(e)}hasStaleContent(e){return this.orderView.hasStaleContent(e)}render(){const{rowsToRender:e}=this.table;!md&&e>1e3&&(md=!0,Wr(vt(vf||(vf=ot(['Performance tip: Handsontable rendered more than 1000 visible rows. \n        Consider limiting the number of rendered rows by specifying the table height and/or \n        turning off the "renderAllRows" option.'],['Performance tip: Handsontable rendered more than 1000 visible rows.\\x20\n        Consider limiting the number of rendered rows by specifying the table height and/or\\x20\n        turning off the "renderAllRows" option.']))))),this.table.isAriaEnabled()&&de(this.rootNode,[nm()]),this.orderView.setSize(e).setOffset(this.table.renderedRowToSource(0)).start();for(let i=0;i<e;i++){this.orderView.render();const o=this.orderView.getCurrentNode(),s=this.table.renderedRowToSource(i);if(this.table.isAriaEnabled()){var r,n;de(o,[Ta(),im(s+((r=(n=this.table.rowUtils)===null||n===void 0||(n=n.dataAccessObject)===null||n===void 0?void 0:n.columnHeaders.length)!==null&&r!==void 0?r:0)+1)])}(s+1)%2===0?ee(o,Mn.rowEven)||(ce(o,Mn.rowOdd),G(o,Mn.rowEven)):ee(o,Mn.rowOdd)||(ce(o,Mn.rowEven),G(o,Mn.rowOdd))}this.orderView.end()}}function pd(t,e,r){return(e=IP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function IP(t){var e=MP(t,"string");return typeof e=="symbol"?e:e+""}function MP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class xP extends ai{constructor(){super("TD"),pd(this,"orderViews",new WeakMap),pd(this,"sourceRowIndex",0)}obtainOrderView(e){let r;return this.orderViews.has(e)?r=this.orderViews.get(e):(r=new Bm(e,n=>this.nodesPool.obtain(this.sourceRowIndex,n),this.nodeType),this.orderViews.set(e,r)),r}render(){const{rowsToRender:e,columnsToRender:r,rows:n,rowHeaders:i}=this.table;for(let l=0;l<e;l++){const a=this.table.renderedRowToSource(l),c=n.getRenderedNode(l);this.sourceRowIndex=a;const d=this.obtainOrderView(c),f=i.obtainOrderView(c);d.prependView(f).setSize(r).setOffset(0).start();for(let m=0;m<r;m++){d.render();const p=this.table.renderedColumnToSource(m),v=d.getCurrentNode();if(ee(v,"hide")||(v.className=""),v.removeAttribute("style"),v.removeAttribute("dir"),ii(v,[new RegExp("aria-(.*)"),new RegExp("role")]),this.table.cellRenderer(a,p,v),this.table.isAriaEnabled()){var o,s;de(v,[...v.hasAttribute("role")?[]:[EH()],oi(-1),lc(p+((o=(s=this.table.rowUtils)===null||s===void 0||(s=s.dataAccessObject)===null||s===void 0?void 0:s.rowHeaders.length)!==null&&o!==void 0?o:0)+1)])}}d.end()}}}function Te(t,e,r){return(e=_P(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _P(t){var e=PP(t,"string");return typeof e=="symbol"?e:e+""}function PP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class AP{constructor(e){let{cellRenderer:r,stylesHandler:n}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};Te(this,"rootNode",void 0),Te(this,"rootDocument",void 0),Te(this,"rowHeaders",null),Te(this,"columnHeaders",null),Te(this,"colGroup",null),Te(this,"rows",null),Te(this,"cells",null),Te(this,"rowFilter",null),Te(this,"columnFilter",null),Te(this,"rowUtils",null),Te(this,"columnUtils",null),Te(this,"rowsToRender",0),Te(this,"columnsToRender",0),Te(this,"rowHeaderFunctions",[]),Te(this,"rowHeadersCount",0),Te(this,"columnHeaderFunctions",[]),Te(this,"columnHeadersCount",0),Te(this,"cellRenderer",void 0),Te(this,"activeOverlayName",void 0),Te(this,"stylesHandler",void 0),this.rootNode=e,this.rootDocument=this.rootNode.ownerDocument,this.cellRenderer=r,this.stylesHandler=n}setActiveOverlayName(e){this.activeOverlayName=e}setAxisUtils(e,r){this.rowUtils=e,this.columnUtils=r}setViewportSize(e,r){this.rowsToRender=e,this.columnsToRender=r}setFilters(e,r){this.rowFilter=e,this.columnFilter=r}setHeaderContentRenderers(e,r){this.rowHeaderFunctions=e,this.rowHeadersCount=e.length,this.columnHeaderFunctions=r,this.columnHeadersCount=r.length}setRenderers(){let{rowHeaders:e,columnHeaders:r,colGroup:n,rows:i,cells:o}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};e.setTable(this),r.setTable(this),n.setTable(this),i.setTable(this),o.setTable(this),this.rowHeaders=e,this.columnHeaders=r,this.colGroup=n,this.rows=i,this.cells=o}renderedRowToSource(e){return this.rowFilter.renderedToSource(e)}renderedColumnToSource(e){return this.columnFilter.renderedToSource(e)}isAriaEnabled(){return this.rowUtils.wtSettings.getSetting("ariaTags")}render(){this.colGroup.adjust(),this.columnHeaders.adjust(),this.rows.adjust(),this.rowHeaders.adjust(),this.columnHeaders.render(),this.rows.render(),this.rowHeaders.render(),this.cells.render(),this.columnUtils.calculateWidths(),this.colGroup.render();const{rowsToRender:e,rows:r}=this;for(let n=0;n<e;n++){const i=r.getRenderedNode(n),o=this.rowUtils;if(i.firstChild){const s=this.renderedRowToSource(n),l=o.getHeightByOverlayName(s,this.activeOverlayName),c=this.stylesHandler.areCellsBorderBox()?0:1;l?i.firstChild.style.height="".concat(l-c,"px"):i.firstChild.style.height=""}}}}class NP{constructor(){let{TABLE:e,THEAD:r,COLGROUP:n,TBODY:i,rowUtils:o,columnUtils:s,cellRenderer:l,stylesHandler:a}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.renderer=new AP(e,{cellRenderer:l,stylesHandler:a}),this.renderer.setRenderers({rowHeaders:new bP,columnHeaders:new SP(r),colGroup:new RP(n),rows:new HP(i),cells:new xP}),this.renderer.setAxisUtils(o,s)}setActiveOverlayName(e){return this.renderer.setActiveOverlayName(e),this}setFilters(e,r){return this.renderer.setFilters(e,r),this}setViewportSize(e,r){return this.renderer.setViewportSize(e,r),this}setHeaderContentRenderers(e,r){return this.renderer.setHeaderContentRenderers(e,r),this}adjust(){this.renderer.adjust()}render(){this.renderer.render()}}function Wl(t,e,r){return(e=$P(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function $P(t){var e=LP(t,"string");return typeof e=="symbol"?e:e+""}function LP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class DP{constructor(e,r){Wl(this,"dataAccessObject",void 0),Wl(this,"wtSettings",void 0),Wl(this,"headerWidths",new Map),this.dataAccessObject=e,this.wtSettings=r}getWidth(e){return this.wtSettings.getSetting("columnWidth",e)||this.wtSettings.getSetting("defaultColumnWidth")}getHeaderHeight(e){let r=this.dataAccessObject.stylesHandler.getDefaultRowHeight();const n=this.dataAccessObject.wtViewport.oversizedColumnHeaders[e];return n!==void 0&&(r=r?Math.max(r,n):n),r}getHeaderWidth(e){return this.headerWidths.get(this.dataAccessObject.wtTable.columnFilter.sourceToRendered(e))}calculateWidths(){const{wtSettings:e}=this;let r=e.getSetting("rowHeaderWidth");if(r=e.getSetting("onModifyRowHeaderWidth",r),r!=null){const n=e.getSetting("rowHeaders").length,i=e.getSetting("defaultColumnWidth");for(let o=0;o<n;o++){let s=Array.isArray(r)?r[o]:r;s=s==null?i:s,this.headerWidths.set(o,s)}}}}function wd(t,e,r){return(e=VP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function VP(t){var e=kP(t,"string");return typeof e=="symbol"?e:e+""}function kP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class FP{constructor(e,r){wd(this,"dataAccessObject",void 0),wd(this,"wtSettings",void 0),this.dataAccessObject=e,this.wtSettings=r}getHeight(e){let r=this.wtSettings.getSetting("rowHeight",e);const n=this.dataAccessObject.wtViewport.oversizedRows[e];return n!==void 0&&(r=r===void 0?n:Math.max(r,n)),r}getHeightByOverlayName(e,r){let n=this.wtSettings.getSetting("rowHeightByOverlayName",e,r);const i=this.dataAccessObject.wtViewport.oversizedRows[e];return i!==void 0&&(n=n===void 0?i:Math.max(n,i)),n}}function ir(t,e,r){return(e=BP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function BP(t){var e=WP(t,"string");return typeof e=="symbol"?e:e+""}function WP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class jP{constructor(e,r,n,i,o){ir(this,"wtSettings",null),ir(this,"domBindings",void 0),ir(this,"TBODY",null),ir(this,"THEAD",null),ir(this,"COLGROUP",null),ir(this,"hasTableHeight",!0),ir(this,"hasTableWidth",!0),ir(this,"isTableVisible",!1),ir(this,"tableOffset",0),ir(this,"holderOffset",0),this.domBindings=n,this.isMaster=o==="master",this.name=o,this.dataAccessObject=e,this.facadeGetter=r,this.wtSettings=i,this.instance=this.dataAccessObject.wot,this.wot=this.dataAccessObject.wot,this.TABLE=n.rootTable,um(this.TABLE),this.spreader=this.createSpreader(this.TABLE),this.hider=this.createHider(this.spreader),this.holder=this.createHolder(this.hider),this.wtRootElement=this.holder.parentNode,this.isMaster&&this.alignOverlaysWithTrimmingContainer(),this.fixTableDomTree(),this.rowFilter=null,this.columnFilter=null,this.correctHeaderWidth=!1;const s=this.wtSettings.getSettingPure("rowHeaderWidth");this.wtSettings.update("rowHeaderWidth",()=>this._modifyRowHeaderWidth(s)),this.rowUtils=new FP(this.dataAccessObject,this.wtSettings),this.columnUtils=new DP(this.dataAccessObject,this.wtSettings),this.tableRenderer=new NP({TABLE:this.TABLE,THEAD:this.THEAD,COLGROUP:this.COLGROUP,TBODY:this.TBODY,rowUtils:this.rowUtils,columnUtils:this.columnUtils,cellRenderer:this.wtSettings.getSettingPure("cellRenderer"),stylesHandler:this.dataAccessObject.stylesHandler})}is(e){return this.name===e}fixTableDomTree(){const e=this.domBindings.rootDocument;this.TBODY=this.TABLE.querySelector("tbody"),this.TBODY||(this.TBODY=e.createElement("tbody"),this.TABLE.appendChild(this.TBODY)),this.THEAD=this.TABLE.querySelector("thead"),this.THEAD||(this.THEAD=e.createElement("thead"),this.TABLE.insertBefore(this.THEAD,this.TBODY)),this.COLGROUP=this.TABLE.querySelector("colgroup"),this.COLGROUP||(this.COLGROUP=e.createElement("colgroup"),this.TABLE.insertBefore(this.COLGROUP,this.THEAD))}createSpreader(e){const r=e.parentNode;let n;return(!r||r.nodeType!==Node.ELEMENT_NODE||!ee(r,"wtHolder"))&&(n=this.domBindings.rootDocument.createElement("div"),n.className="wtSpreader",r&&r.insertBefore(n,e),n.appendChild(e)),n.style.position="relative",this.wtSettings.getSetting("ariaTags")&&de(n,[Vr()]),n}createHider(e){const r=e.parentNode;let n;return(!r||r.nodeType!==Node.ELEMENT_NODE||!ee(r,"wtHolder"))&&(n=this.domBindings.rootDocument.createElement("div"),n.className="wtHider",r&&r.insertBefore(n,e),n.appendChild(e)),this.wtSettings.getSetting("ariaTags")&&de(n,[Vr()]),n}createHolder(e){const r=e.parentNode;let n;return(!r||r.nodeType!==Node.ELEMENT_NODE||!ee(r,"wtHolder"))&&(n=this.domBindings.rootDocument.createElement("div"),n.style.position="relative",n.className="wtHolder",de(n,[oi(-1)]),r&&r.insertBefore(n,e),this.isMaster&&(n.parentNode.className+="ht_master handsontable",n.parentNode.setAttribute("dir",this.wtSettings.getSettingPure("rtlMode")?"rtl":"ltr"),this.wtSettings.getSetting("ariaTags")&&de(n.parentNode,[Vr()])),n.appendChild(e)),this.wtSettings.getSetting("ariaTags")&&de(n,[Vr()]),n}draw(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const{wtSettings:r}=this,{wtOverlays:n,wtViewport:i}=this.dataAccessObject,o=r.getSetting("totalRows"),s=r.getSetting("totalColumns"),l=r.getSetting("rowHeaders"),a=l.length,c=r.getSetting("columnHeaders"),d=c.length;let f=e;if(this.isMaster&&(n.beforeDraw(),this.holderOffset=At(this.holder),f=i.createCalculators(f),a&&!r.getSetting("fixedColumnsStart"))){const p=n.inlineStartOverlay.getScrollPosition(),v=this.correctHeaderWidth;this.correctHeaderWidth=p!==0,v!==this.correctHeaderWidth&&(f=!1)}if(f)this.isMaster&&n.refresh(!0);else{this.isMaster?this.tableOffset=At(this.TABLE):this.tableOffset=this.dataAccessObject.parentTableOffset;const p=Math.max(this.getFirstRenderedRow(),0),v=Math.max(this.getFirstRenderedColumn(),0);this.rowFilter=new F_(p,o,d),this.columnFilter=new L_(v,s,a);let R=!0;if(this.isMaster){this.alignOverlaysWithTrimmingContainer();const H={};this.wtSettings.getSetting("beforeDraw",!0,H),R=H.skipRender!==!0}R&&(this.tableRenderer.setHeaderContentRenderers(l,c),(this.is(ur)||this.is(Fr))&&this.tableRenderer.setHeaderContentRenderers(l,[]),this.resetOversizedRows(),this.tableRenderer.setActiveOverlayName(this.name).setViewportSize(this.getRenderedRowsCount(),this.getRenderedColumnsCount()).setFilters(this.rowFilter,this.columnFilter).render(),this.isMaster&&this.markOversizedColumnHeaders(),this.adjustColumnHeaderHeights(),(this.isMaster||this.is(ur))&&this.markOversizedRows(),this.isMaster?(this.wtSettings.getSetting("externalRowCalculator")||i.createVisibleCalculators(),n.refresh(!1),n.applyToDOM(),this.wtSettings.getSetting("onDraw",!0)):this.is(ur)&&this.dataAccessObject.cloneSource.wtOverlays.adjustElementsSize())}let m=!1;return this.isMaster&&(m=n.topOverlay.resetFixedPosition(),n.bottomOverlay.clone&&(m=n.bottomOverlay.resetFixedPosition()||m),m=n.inlineStartOverlay.resetFixedPosition()||m,n.topInlineStartCornerOverlay&&n.topInlineStartCornerOverlay.resetFixedPosition(),n.bottomInlineStartCornerOverlay&&n.bottomInlineStartCornerOverlay.clone&&n.bottomInlineStartCornerOverlay.resetFixedPosition()),m?(n.refreshAll(),n.adjustElementsSize()):this.dataAccessObject.selectionManager.setActiveOverlay(this.facadeGetter()).render(f),this.isMaster&&n.afterDraw(),this.dataAccessObject.drawn=!0,this}markIfOversizedColumnHeader(e){const r=this.columnFilter.renderedToSource(e);let n=this.wtSettings.getSetting("columnHeaders").length;const i=this.dataAccessObject.stylesHandler.getDefaultRowHeight();let o,s,l;const a=this.wtSettings.getSetting("columnHeaderHeight")||[];for(;n;)n-=1,o=this.getColumnHeaderHeight(n),s=this.getColumnHeader(r,n),s&&(l=ko(s),(!o&&i<l||o<l)&&(this.dataAccessObject.wtViewport.oversizedColumnHeaders[n]=l),Array.isArray(a)?a[n]!==null&&a[n]!==void 0&&(this.dataAccessObject.wtViewport.oversizedColumnHeaders[n]=a[n]):isNaN(a)||(this.dataAccessObject.wtViewport.oversizedColumnHeaders[n]=a),this.dataAccessObject.wtViewport.oversizedColumnHeaders[n]<(a[n]||a)&&(this.dataAccessObject.wtViewport.oversizedColumnHeaders[n]=a[n]||a))}adjustColumnHeaderHeights(){const{wtSettings:e}=this,r=this.THEAD.childNodes,n=this.dataAccessObject.wtViewport.oversizedColumnHeaders,i=e.getSetting("columnHeaders");for(let o=0,s=i.length;o<s;o++)if(n[o]){if(!r[o]||r[o].childNodes.length===0)return;r[o].childNodes[0].style.height="".concat(n[o],"px")}}resetOversizedRows(){const{wtSettings:e}=this,{wtViewport:r}=this.dataAccessObject;if(!(!this.isMaster&&!this.is(ur))&&!e.getSetting("externalRowCalculator")){const n=this.getRenderedRowsCount();for(let i=0;i<n;i++){const o=this.rowFilter.renderedToSource(i);r.oversizedRows&&r.oversizedRows[o]&&(r.oversizedRows[o]=void 0)}}}getCell(e){let r=e.row,n=e.col;const i=this.wtSettings.getSetting("onModifyGetCellCoords",r,n,!this.isMaster,"render");if(i&&Array.isArray(i)&&([r,n]=i),this.isRowBeforeRenderedRows(r))return-1;if(this.isRowAfterRenderedRows(r))return-2;if(this.isColumnBeforeRenderedColumns(n))return-3;if(this.isColumnAfterRenderedColumns(n))return-4;const o=this.getRow(r);if(!o&&r>=0)throw new Error("TR was expected to be rendered but is not");const s=o.childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(n)];if(!s&&n>=0)throw new Error("TD or TH was expected to be rendered but is not");return s}getRow(e){let r=null,n=null;if(e<0){var i;r=(i=this.rowFilter)===null||i===void 0?void 0:i.sourceRowToVisibleColHeadedRow(e),n=this.THEAD}else{var o;r=(o=this.rowFilter)===null||o===void 0?void 0:o.sourceToRendered(e),n=this.TBODY}return r!==void 0&&n!==void 0?n.childNodes.length<r+1?!1:n.childNodes[r]:!1}getColumnHeader(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;const n=this.THEAD.childNodes[r];return n==null?void 0:n.childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(e)]}getColumnHeaders(e){const r=[],n=this.columnFilter.sourceColumnToVisibleRowHeadedColumn(e);return this.THEAD.childNodes.forEach(i=>{const o=i.childNodes[n];o&&r.push(o)}),r}getRowHeader(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;const n=this.wtSettings.getSetting("rowHeaders").length;if(r>=n)return;const i=this.rowFilter.sourceToRendered(e),o=i<0?this.rowFilter.sourceRowToVisibleColHeadedRow(e):i,l=(i<0?this.THEAD:this.TBODY).childNodes[o];return l==null?void 0:l.childNodes[r]}getRowHeaders(e){const r=[],n=this.wtSettings.getSetting("rowHeaders").length;for(let i=0;i<n;i++){const o=this.TBODY.childNodes[this.rowFilter.sourceToRendered(e)],s=o==null?void 0:o.childNodes[i];s&&r.push(s)}return r}getCoords(e){let r=e;if(r.nodeName!=="TD"&&r.nodeName!=="TH"&&(r=lm(r,["TD","TH"])),r===null)return null;const n=r.parentNode;if(!n)return null;const i=n.parentNode;let o=LH(n),s=r.cellIndex;Xr(fn,r,this.wtRootElement)||Xr(hn,r,this.wtRootElement)?i.nodeName==="THEAD"&&(o-=i.childNodes.length):Xr(Fr,r,this.wtRootElement)||Xr(ur,r,this.wtRootElement)?o=this.wtSettings.getSetting("totalRows")-i.childNodes.length+o:i===this.THEAD?o=this.rowFilter.visibleColHeadedRowToSourceRow(o):this.rowFilter&&(o=this.rowFilter.renderedToSource(o)),Xr(fn,r,this.wtRootElement)||Xr(dn,r,this.wtRootElement)||Xr(Fr,r,this.wtRootElement)?s=this.columnFilter.offsettedTH(s):this.columnFilter&&(s=this.columnFilter.visibleRowHeadedColumnToSourceColumn(s));const l=this.wtSettings.getSetting("onModifyGetCoordsElement",o,s);return l&&Array.isArray(l)&&([o,s]=l),this.wot.createCellCoords(o,s)}markOversizedRows(){if(this.wtSettings.getSetting("externalRowCalculator"))return;let e=this.TBODY.childNodes.length;const r=e*this.dataAccessObject.stylesHandler.getDefaultRowHeight(),n=ko(this.TBODY)-1,i=this.wot.stylesHandler.areCellsBorderBox(),o=i?yt:ko,s=i?0:1,l=i?1:0;let a,c,d,f,m;if(!(r===n&&!this.wtSettings.getSetting("fixedRowsBottom")))for(;e;){e-=1,d=this.rowFilter.renderedToSource(e),a=this.getRowHeight(d),f=this.getTrForRow(d),m=f.querySelector("th");const p=d===0?l:0;m?c=o(m):c=o(f)-s,(!a&&this.dataAccessObject.stylesHandler.getDefaultRowHeight()<c-p||a<c)&&(i||(c+=1),this.dataAccessObject.wtViewport.oversizedRows[d]=c)}}getTrForRow(e){return this.TBODY.childNodes[this.rowFilter.sourceToRendered(e)]}isColumnHeaderRendered(e){if(e>=0)return!1;const n=this.wtSettings.getSetting("rowHeaders").length;return Math.abs(e)<=n}isRowHeaderRendered(e){if(e>=0)return!1;const n=this.wtSettings.getSetting("columnHeaders").length;return Math.abs(e)<=n}isRowBeforeRenderedRows(e){const r=this.getFirstRenderedRow();return e<0&&r<=0?!this.isRowHeaderRendered(e):e<r}isRowAfterRenderedRows(e){return e>this.getLastRenderedRow()}isColumnBeforeRenderedColumns(e){const r=this.getFirstRenderedColumn();return e<0&&r<=0?!this.isColumnHeaderRendered(e):e<r}isColumnAfterRenderedColumns(e){return this.columnFilter&&e>this.getLastRenderedColumn()}isColumnAfterViewport(e){return this.columnFilter&&e>this.getLastVisibleColumn()}isRowAfterViewport(e){return this.rowFilter&&e>this.getLastVisibleRow()}isColumnBeforeViewport(e){return this.columnFilter&&this.columnFilter.sourceToRendered(e)<0&&e>=0}isLastRowFullyVisible(){return this.getLastVisibleRow()===this.getLastRenderedRow()}isLastColumnFullyVisible(){return this.getLastVisibleColumn()===this.getLastRenderedColumn()}allRowsInViewport(){return this.wtSettings.getSetting("totalRows")===this.getVisibleRowsCount()}allColumnsInViewport(){return this.wtSettings.getSetting("totalColumns")===this.getVisibleColumnsCount()}getRowHeight(e){return this.rowUtils.getHeight(e)}getColumnHeaderHeight(e){return this.columnUtils.getHeaderHeight(e)}getColumnWidth(e){return this.columnUtils.getWidth(e)}hasDefinedSize(){return this.hasTableHeight&&this.hasTableWidth}getWidth(){return ut(this.TABLE)}getHeight(){return yt(this.TABLE)}getTotalWidth(){const e=ut(this.hider);return e!==0?e:this.getWidth()}getTotalHeight(){const e=yt(this.hider);return e!==0?e:this.getHeight()}isVisible(){return Go(this.TABLE)}_modifyRowHeaderWidth(e){let r=Z(e)?e():null;return Array.isArray(r)?(r=[...r],r[r.length-1]=this._correctRowHeaderWidth(r[r.length-1])):r=this._correctRowHeaderWidth(r),r}_correctRowHeaderWidth(e){let r=e;return typeof e!="number"&&(r=this.wtSettings.getSetting("defaultColumnWidth")),this.correctHeaderWidth&&(r+=1),r}}const Zn=jP,zP="stickyRowsBottom",Wm={getFirstRenderedRow(){const t=this.getRenderedRowsCount();return t===0?-1:this.wtSettings.getSetting("totalRows")-t},getFirstVisibleRow(){return this.getFirstRenderedRow()},getFirstPartiallyVisibleRow(){return this.getFirstRenderedRow()},getLastRenderedRow(){return this.getRenderedRowsCount()===0?-1:this.wtSettings.getSetting("totalRows")-1},getLastVisibleRow(){return this.getLastRenderedRow()},getLastPartiallyVisibleRow(){return this.getLastRenderedRow()},getRenderedRowsCount(){return Math.min(this.wtSettings.getSetting("totalRows"),this.wtSettings.getSetting("fixedRowsBottom"))},getVisibleRowsCount(){return this.getRenderedRowsCount()},getColumnHeadersCount(){return 0}};zr(Wm,"MIXIN_NAME",zP,{writable:!1,enumerable:!1});const jm=Wm,UP="stickyColumnsStart",zm={getFirstRenderedColumn(){return this.getRenderedColumnsCount()===0?-1:0},getFirstVisibleColumn(){return this.getFirstRenderedColumn()},getFirstPartiallyVisibleColumn(){return this.getFirstRenderedColumn()},getLastRenderedColumn(){return this.getRenderedColumnsCount()-1},getLastVisibleColumn(){return this.getLastRenderedColumn()},getLastPartiallyVisibleColumn(){return this.getLastRenderedColumn()},getRenderedColumnsCount(){return Math.min(this.wtSettings.getSetting("totalColumns"),this.wtSettings.getSetting("fixedColumnsStart"))},getVisibleColumnsCount(){return this.getRenderedColumnsCount()},getRowHeadersCount(){return this.dataAccessObject.rowHeaders.length}};zr(zm,"MIXIN_NAME",UP,{writable:!1,enumerable:!1});const vc=zm;class yc extends Zn{constructor(e,r,n,i){super(e,r,n,i,Fr)}}Se(yc,jm);Se(yc,vc);const GP=yc,hn="top",ur="bottom",dn="inline_start",fn="top_inline_start_corner",Fr="bottom_inline_start_corner",Um=[hn,ur,dn,fn,Fr],KP=new Map([[hn,"ht_clone_".concat(hn)],[ur,"ht_clone_".concat(ur)],[dn,"ht_clone_".concat(dn," ht_clone_left")],[fn,"ht_clone_".concat(fn," ht_clone_top_left_corner")],[Fr,"ht_clone_".concat(Fr," ht_clone_bottom_left_corner")]]);function YP(t,e){XP(t,e),e.add(t)}function XP(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function qP(t,e,r){return(e=QP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function QP(t){var e=ZP(t,"string");return typeof e=="symbol"?e:e+""}function ZP(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function _i(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var So=new WeakSet;class JP{constructor(e){YP(this,So),qP(this,"dataAccessObject",void 0),this.dataAccessObject=e}scrollViewport(e,r,n){if(e.col<0||e.row<0)return!1;const i=this.scrollViewportHorizontally(e.col,r),o=this.scrollViewportVertically(e.row,n);return i||o}scrollViewportHorizontally(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"auto";const{drawn:n,totalColumns:i}=this.dataAccessObject;if(!n)return!1;const o=wt(r);if(e=this.dataAccessObject.wtSettings.getSetting("onBeforeViewportScrollHorizontally",e,o),!Number.isInteger(e)||e<0||e>i)return!1;r=o.value;const{fixedColumnsStart:s,inlineStartOverlay:l}=this.dataAccessObject,a=r==="auto";if(a&&e<s)return!1;const c=this.getFirstVisibleColumn(),d=this.getLastVisibleColumn();let f=!1;return(a&&(e<c||e>d)||!a)&&(f=l.scrollTo(e,a?e>=this.getLastPartiallyVisibleColumn():r==="end")),f}scrollViewportVertically(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"auto";const{drawn:n,totalRows:i}=this.dataAccessObject;if(!n)return!1;const o=wt(r);if(e=this.dataAccessObject.wtSettings.getSetting("onBeforeViewportScrollVertically",e,o),!Number.isInteger(e)||e<0||e>i)return!1;r=o.value;const{fixedRowsBottom:s,fixedRowsTop:l,topOverlay:a}=this.dataAccessObject,c=r==="auto";if(c&&(e<l||e>i-s-1))return!1;const d=this.getFirstVisibleRow(),f=this.getLastVisibleRow();let m=!1;return(c&&(e<d||e>f)||!c)&&(m=a.scrollTo(e,c?e>=this.getLastPartiallyVisibleRow():r==="bottom")),m}getFirstVisibleRow(){return this.dataAccessObject.wtTable.getFirstVisibleRow()}getLastVisibleRow(){return _i(So,this,yd).call(this,this.dataAccessObject.wtTable.getLastVisibleRow())}getFirstPartiallyVisibleRow(){return this.dataAccessObject.wtTable.getFirstPartiallyVisibleRow()}getLastPartiallyVisibleRow(){return _i(So,this,yd).call(this,this.dataAccessObject.wtTable.getLastPartiallyVisibleRow())}getFirstVisibleColumn(){return this.dataAccessObject.wtTable.getFirstVisibleColumn()}getLastVisibleColumn(){return _i(So,this,vd).call(this,this.dataAccessObject.wtTable.getLastVisibleColumn())}getFirstPartiallyVisibleColumn(){return this.dataAccessObject.wtTable.getFirstPartiallyVisibleColumn()}getLastPartiallyVisibleColumn(){return _i(So,this,vd).call(this,this.dataAccessObject.wtTable.getLastPartiallyVisibleColumn())}}function vd(t){const{wtSettings:e,inlineStartOverlay:r,wtTable:n,wtViewport:i,totalColumns:o,rootWindow:s}=this.dataAccessObject;if(r.mainTableScrollableElement===s){const l=e.getSetting("rtlMode");let a=null;if(l){const d=n.TABLE.getBoundingClientRect(),m=this.dataAccessObject.rootWindow.document.documentElement.offsetWidth;a=Math.abs(d.right-m)}else a=At(n.wtRootElement).left;const c=Math.abs(dm(s,s));if(a>c){const d=fm(s);let f=i.getRowHeaderWidth();for(let m=1;m<=o;m++)if(f+=r.sumCellSizes(m-1,m),a+f-c>=d){t=m-2;break}}}return t}function yd(t){const{topOverlay:e,wtTable:r,wtViewport:n,totalRows:i,rootWindow:o}=this.dataAccessObject;if(e.mainTableScrollableElement===o){const s=At(r.wtRootElement),l=cc(o,o);if(s.top>l){const a=ko(o);let c=n.getColumnHeaderHeight();for(let d=1;d<=i;d++)if(c+=e.sumCellSizes(d-1,d),s.top+c-l>=a){t=d-2;break}}}return t}const eA=JP;function xt(t,e,r){return(e=tA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tA(t){var e=rA(t,"string");return typeof e=="symbol"?e:e+""}function rA(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class Cc{get eventManager(){return new Un(this)}constructor(e,r){xt(this,"wtTable",void 0),xt(this,"wtScroll",void 0),xt(this,"wtViewport",void 0),xt(this,"wtOverlays",void 0),xt(this,"selectionManager",void 0),xt(this,"wtEvent",void 0),xt(this,"guid","wt_".concat(rm())),xt(this,"drawInterrupted",!1),xt(this,"drawn",!1),xt(this,"activeOverlayName","master"),xt(this,"domBindings",void 0),xt(this,"wtSettings",void 0),this.domBindings={rootTable:e,rootDocument:e.ownerDocument,rootWindow:e.ownerDocument.defaultView},this.wtSettings=r,this.wtScroll=new eA(this.createScrollDao())}findOriginalHeaders(){const e=[];if(this.wtTable.THEAD.childNodes.length&&this.wtTable.THEAD.childNodes[0].childNodes.length){for(let r=0,n=this.wtTable.THEAD.childNodes[0].childNodes.length;r<n;r++)e.push(this.wtTable.THEAD.childNodes[0].childNodes[r].innerHTML);this.wtSettings.getSetting("columnHeaders").length||this.wtSettings.update("columnHeaders",[function(r,n){si(n,e[r])}])}}createCellCoords(e,r){return new vs(e,r,this.wtSettings.getSetting("rtlMode"))}createCellRange(e,r,n){return new wc(e,r,n,this.wtSettings.getSetting("rtlMode"))}draw(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return this.drawInterrupted=!1,!this.wtTable.isVisible()||kH(this.wtTable.wtRootElement.parentNode)?this.drawInterrupted=!0:this.wtTable.draw(e),this}getCell(e){if(!(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1))return this.wtTable.getCell(e);const n=this.wtSettings.getSetting("totalRows"),i=this.wtSettings.getSetting("fixedRowsTop"),o=this.wtSettings.getSetting("fixedRowsBottom"),s=this.wtSettings.getSetting("fixedColumnsStart");if(e.row<i&&e.col<s)return this.wtOverlays.topInlineStartCornerOverlay.clone.wtTable.getCell(e);if(e.row<i)return this.wtOverlays.topOverlay.clone.wtTable.getCell(e);if(e.col<s&&e.row>=n-o){if(this.wtOverlays.bottomInlineStartCornerOverlay&&this.wtOverlays.bottomInlineStartCornerOverlay.clone)return this.wtOverlays.bottomInlineStartCornerOverlay.clone.wtTable.getCell(e)}else{if(e.col<s)return this.wtOverlays.inlineStartOverlay.clone.wtTable.getCell(e);if(e.row<n&&e.row>=n-o&&this.wtOverlays.bottomOverlay&&this.wtOverlays.bottomOverlay.clone)return this.wtOverlays.bottomOverlay.clone.wtTable.getCell(e)}return this.wtTable.getCell(e)}scrollViewport(e,r,n){return this.wtScroll.scrollViewport(e,r,n)}scrollViewportHorizontally(e,r){return this.wtScroll.scrollViewportHorizontally(e,r)}scrollViewportVertically(e,r){return this.wtScroll.scrollViewportVertically(e,r)}getViewport(){return[this.wtTable.getFirstVisibleRow(),this.wtTable.getFirstVisibleColumn(),this.wtTable.getLastVisibleRow(),this.wtTable.getLastVisibleColumn()]}destroy(){this.wtOverlays.destroy(),this.wtEvent.destroy()}createScrollDao(){const e=this;return{get drawn(){return e.drawn},get topOverlay(){return e.wtOverlays.topOverlay},get inlineStartOverlay(){return e.wtOverlays.inlineStartOverlay},get wtTable(){return e.wtTable},get wtViewport(){return e.wtViewport},get wtSettings(){return e.wtSettings},get rootWindow(){return e.domBindings.rootWindow},get totalRows(){return e.wtSettings.getSetting("totalRows")},get totalColumns(){return e.wtSettings.getSetting("totalColumns")},get fixedRowsTop(){return e.wtSettings.getSetting("fixedRowsTop")},get fixedRowsBottom(){return e.wtSettings.getSetting("fixedRowsBottom")},get fixedColumnsStart(){return e.wtSettings.getSetting("fixedColumnsStart")}}}getTableDao(){const e=this;return{get wot(){return e},get parentTableOffset(){return e.cloneSource.wtTable.tableOffset},get cloneSource(){return e.cloneSource},get workspaceWidth(){return e.wtViewport.getWorkspaceWidth()},get wtViewport(){return e.wtViewport},get wtOverlays(){return e.wtOverlays},get selectionManager(){return e.selectionManager},get stylesHandler(){return e.stylesHandler},get drawn(){return e.drawn},set drawn(r){e.drawn=r},get wtTable(){return e.wtTable},get startColumnRendered(){return e.wtViewport.columnsRenderCalculator.startColumn},get startColumnVisible(){return e.wtViewport.columnsVisibleCalculator.startColumn},get startColumnPartiallyVisible(){return e.wtViewport.columnsPartiallyVisibleCalculator.startColumn},get endColumnRendered(){return e.wtViewport.columnsRenderCalculator.endColumn},get endColumnVisible(){return e.wtViewport.columnsVisibleCalculator.endColumn},get endColumnPartiallyVisible(){return e.wtViewport.columnsPartiallyVisibleCalculator.endColumn},get countColumnsRendered(){return e.wtViewport.columnsRenderCalculator.count},get countColumnsVisible(){return e.wtViewport.columnsVisibleCalculator.count},get startRowRendered(){return e.wtViewport.rowsRenderCalculator.startRow},get startRowVisible(){return e.wtViewport.rowsVisibleCalculator.startRow},get startRowPartiallyVisible(){return e.wtViewport.rowsPartiallyVisibleCalculator.startRow},get endRowRendered(){return e.wtViewport.rowsRenderCalculator.endRow},get endRowVisible(){return e.wtViewport.rowsVisibleCalculator.endRow},get endRowPartiallyVisible(){return e.wtViewport.rowsPartiallyVisibleCalculator.endRow},get countRowsRendered(){return e.wtViewport.rowsRenderCalculator.count},get countRowsVisible(){return e.wtViewport.rowsVisibleCalculator.count},get columnHeaders(){return e.wtSettings.getSetting("columnHeaders")},get rowHeaders(){return e.wtSettings.getSetting("rowHeaders")}}}}function Cd(t,e,r){return(e=nA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nA(t){var e=oA(t,"string");return typeof e=="symbol"?e:e+""}function oA(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class iA extends Cc{constructor(e,r,n){super(e,r),Cd(this,"cloneSource",void 0),Cd(this,"cloneOverlay",void 0);const i=this.wtSettings.getSetting("facade",this);this.cloneSource=n.source,this.cloneOverlay=n.overlay,this.stylesHandler=n.stylesHandler,this.wtTable=this.cloneOverlay.createTable(this.getTableDao(),i,this.domBindings,this.wtSettings),this.wtViewport=n.viewport,this.selectionManager=n.selectionManager,this.wtEvent=new km(i,this.domBindings,this.wtSettings,this.eventManager,this.wtTable,this.selectionManager,n.event),this.findOriginalHeaders()}}function sA(t,e,r){return(e=lA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lA(t){var e=aA(t,"string");return typeof e=="symbol"?e:e+""}function aA(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class ci{constructor(e,r,n,i,o){sA(this,"wtSettings",null),zr(this,"wot",e,{writable:!1}),this.domBindings=o,this.facadeGetter=r,this.wtSettings=i;const{TABLE:s,hider:l,spreader:a,holder:c,wtRootElement:d}=this.wot.wtTable;this.instance=this.wot,this.type=n,this.mainTableScrollableElement=null,this.TABLE=s,this.hider=l,this.spreader=a,this.holder=c,this.wtRootElement=d,this.trimmingContainer=ms(this.hider.parentNode.parentNode),this.needFullRender=this.shouldBeRendered(),this.clone=this.makeClone()}hasRenderingStateChanged(){return this.needFullRender!==this.shouldBeRendered()}updateStateOfRendering(e){e==="before"&&this.shouldBeRendered()?this.needFullRender=!0:e==="after"&&!this.shouldBeRendered()&&(this.needFullRender=!1)}shouldBeRendered(){return!0}updateTrimmingContainer(){this.trimmingContainer=ms(this.hider.parentNode.parentNode)}updateMainScrollableElement(){const{wtTable:e}=this.wot,{rootWindow:r}=this.domBindings;r.getComputedStyle(e.wtRootElement.parentNode).getPropertyValue("overflow")==="hidden"?this.mainTableScrollableElement=this.wot.wtTable.holder:this.mainTableScrollableElement=gs(e.TABLE)}getRelativeCellPosition(e,r,n){if(this.clone.wtTable.holder.contains(e)===!1){Wr("The provided element is not a child of the ".concat(this.type," overlay"));return}const i=this.mainTableScrollableElement===this.domBindings.rootWindow,o=n<this.wtSettings.getSetting("fixedColumnsStart"),s=r<this.wtSettings.getSetting("fixedRowsTop"),l=r>=this.wtSettings.getSetting("totalRows")-this.wtSettings.getSetting("fixedRowsBottom"),a=this.clone.wtTable.spreader,c={start:this.getRelativeStartPosition(a),top:a.offsetTop},d={start:this.getRelativeStartPosition(e),top:e.offsetTop};let f=null;return i?f=this.getRelativeCellPositionWithinWindow(s,o,d,c):f=this.getRelativeCellPositionWithinHolder(s,l,o,d,c),f}getRelativeStartPosition(e){return this.isRtl()?e.offsetParent.offsetWidth-e.offsetLeft-e.offsetWidth:e.offsetLeft}getRelativeCellPositionWithinWindow(e,r,n,i){const o=this.wot.wtTable.wtRootElement.getBoundingClientRect();let s=0,l=0;if(!r)s=i.start;else{let a=o.left;this.isRtl()&&(a=this.domBindings.rootWindow.innerWidth-(o.left+o.width+ht())),s=a<=0?-1*a:0}return e?l=this.clone.wtTable.TABLE.getBoundingClientRect().top-o.top:l=i.top,{start:n.start+s,top:n.top+l}}getRelativeCellPositionWithinHolder(e,r,n,i,o){const s={horizontal:this.wot.wtOverlays.inlineStartOverlay.getScrollPosition(),vertical:this.wot.wtOverlays.topOverlay.getScrollPosition()};let l=0,a=0;if(n||(l=s.horizontal-o.start),r){const c=this.wot.wtTable.wtRootElement.getBoundingClientRect();a=this.clone.wtTable.TABLE.getBoundingClientRect().top*-1+c.top}else e||(a=s.vertical-o.top);return{start:i.start-l,top:i.top-a}}makeClone(){if(Um.indexOf(this.type)===-1)throw new Error('Clone type "'.concat(this.type,'" is not supported.'));const{wtTable:e,wtSettings:r}=this.wot,{rootDocument:n,rootWindow:i}=this.domBindings,o=n.createElement("div"),s=n.createElement("table"),l=e.wtRootElement.parentNode;o.className="".concat(KP.get(this.type)," handsontable"),o.setAttribute("dir",this.isRtl()?"rtl":"ltr"),o.style.position="absolute",o.style.top=0,o.style.overflow="visible",this.isRtl()?o.style.right=0:o.style.left=0,r.getSetting("ariaTags")&&de(o,[Vr()]),s.className=e.TABLE.className,e.TABLE.getAttribute("role")&&s.setAttribute("role",e.TABLE.getAttribute("role")),o.appendChild(s),l.appendChild(o);const c=this.wtSettings.getSetting("preventOverflow");return c===!0||c==="horizontal"&&this.type===hn||c==="vertical"&&this.type===dn?this.mainTableScrollableElement=i:i.getComputedStyle(l).getPropertyValue("overflow")==="hidden"?this.mainTableScrollableElement=e.holder:this.mainTableScrollableElement=gs(e.TABLE),new iA(s,this.wtSettings,{source:this.wot,overlay:this,viewport:this.wot.wtViewport,event:this.wot.wtEvent,selectionManager:this.wot.selectionManager,stylesHandler:this.wot.stylesHandler})}refresh(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(this.needFullRender){const r=this.clone.cloneSource;r.activeOverlayName=this.clone.wtTable.name,this.clone.draw(e),r.activeOverlayName="master"}}reset(){const e=this.clone.wtTable.holder,r=this.clone.wtTable.hider,n=e.style,i=r.style,o=e.parentNode.style;[n,i,o].forEach(s=>{s.width="",s.height=""})}isRtl(){return this.wtSettings.getSetting("rtlMode")}destroy(){this.clone.eventManager.destroy()}}class cA extends ci{constructor(e,r,n,i,o,s){super(e,r,Fr,n,i),this.bottomOverlay=o,this.inlineStartOverlay=s}createTable(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return new GP(...r)}shouldBeRendered(){return this.wtSettings.getSetting("shouldRenderBottomOverlay")&&this.wtSettings.getSetting("shouldRenderInlineStartOverlay")}resetFixedPosition(){const{wot:e}=this;if(this.updateTrimmingContainer(),!e.wtTable.holder.parentNode)return!1;const r=this.clone.wtTable.holder.parentNode;if(r.style.top="",this.trimmingContainer===this.domBindings.rootWindow){const o=this.inlineStartOverlay.getOverlayOffset(),s=this.bottomOverlay.getOverlayOffset();r.style[this.isRtl()?"right":"left"]="".concat(o,"px"),r.style.bottom="".concat(s,"px")}else Ms(r),this.repositionOverlay();let n=yt(this.clone.wtTable.TABLE);const i=ut(this.clone.wtTable.TABLE);return this.wot.wtTable.hasDefinedSize()||(n=0),r.style.height="".concat(n,"px"),r.style.width="".concat(i,"px"),!1}repositionOverlay(){const{wtTable:e,wtViewport:r}=this.wot,{rootDocument:n}=this.domBindings,i=this.clone.wtTable.holder.parentNode;let o=0;r.hasVerticalScroll()||(o+=r.getWorkspaceHeight()-e.getTotalHeight()),r.hasVerticalScroll()&&r.hasHorizontalScroll()&&(o+=ht(n)),i.style.bottom="".concat(o,"px")}}const uA="calculatedColumns",Gm={getFirstRenderedColumn(){const t=this.dataAccessObject.startColumnRendered;return t===null?-1:t},getFirstVisibleColumn(){const t=this.dataAccessObject.startColumnVisible;return t===null?-1:t},getFirstPartiallyVisibleColumn(){const t=this.dataAccessObject.startColumnPartiallyVisible;return t===null?-1:t},getLastRenderedColumn(){const t=this.dataAccessObject.endColumnRendered;return t===null?-1:t},getLastVisibleColumn(){const t=this.dataAccessObject.endColumnVisible;return t===null?-1:t},getLastPartiallyVisibleColumn(){const t=this.dataAccessObject.endColumnPartiallyVisible;return t===null?-1:t},getRenderedColumnsCount(){return this.dataAccessObject.countColumnsRendered},getVisibleColumnsCount(){return this.dataAccessObject.countColumnsVisible},getRowHeadersCount(){return this.dataAccessObject.rowHeaders.length}};zr(Gm,"MIXIN_NAME",uA,{writable:!1,enumerable:!1});const bc=Gm;class Sc extends Zn{constructor(e,r,n,i){super(e,r,n,i,ur)}}Se(Sc,jm);Se(Sc,bc);const hA=Sc;function dA(t,e,r){return(e=fA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fA(t){var e=gA(t,"string");return typeof e=="symbol"?e:e+""}function gA(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class mA extends ci{constructor(e,r,n,i){super(e,r,ur,n,i),dA(this,"cachedFixedRowsBottom",-1),this.cachedFixedRowsBottom=this.wtSettings.getSetting("fixedRowsBottom")}createTable(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return new hA(...r)}shouldBeRendered(){return this.wtSettings.getSetting("shouldRenderBottomOverlay")}resetFixedPosition(){if(!this.needFullRender||!this.shouldBeRendered()||!this.wot.wtTable.holder.parentNode)return!1;const{rootWindow:e}=this.domBindings,r=this.clone.wtTable.holder.parentNode;r.style.top="";let n=0;const i=this.wtSettings.getSetting("preventOverflow");this.trimmingContainer===e&&(!i||i!=="vertical")?(n=this.getOverlayOffset(),r.style.bottom="".concat(n,"px")):(n=this.getScrollPosition(),this.repositionOverlay());const o=this.adjustHeaderBordersPosition(n);return this.adjustElementsSize(),o}repositionOverlay(){const{wtTable:e,wtViewport:r}=this.wot,{rootDocument:n}=this.domBindings,i=this.clone.wtTable.holder.parentNode;let o=0;r.hasVerticalScroll()||(o+=r.getWorkspaceHeight()-e.getTotalHeight()),r.hasVerticalScroll()&&r.hasHorizontalScroll()&&(o+=ht(n)),i.style.bottom="".concat(o,"px")}setScrollPosition(e){const{rootWindow:r}=this.domBindings,n=this.mainTableScrollableElement;let i=!1;if(n===r&&e!==r.scrollY){const o=r.scrollY;r.scrollTo(ac(r),top),i=o!==r.scrollY}else if(e!==n.scrollTop){const o=n.scrollTop;n.scrollTop=e,i=o!==n.scrollTop}return i}onScroll(){this.wtSettings.getSetting("onScrollHorizontally")}sumCellSizes(e,r){const{wtTable:n,stylesHandler:i}=this.wot,o=i.getDefaultRowHeight();let s=e,l=0;for(;s<r;){const a=n.getRowHeight(s);l+=a===void 0?o:a,s+=1}return l}adjustElementsSize(){this.updateTrimmingContainer(),this.needFullRender&&(this.adjustRootElementSize(),this.adjustRootChildrenSize())}adjustRootElementSize(){const{wtTable:e,wtViewport:r}=this.wot,{rootDocument:n,rootWindow:i}=this.domBindings,s=this.clone.wtTable.holder.parentNode.style,l=this.wtSettings.getSetting("preventOverflow");if(this.trimmingContainer!==i||l==="horizontal"){let c=r.getWorkspaceWidth();r.hasVerticalScroll()&&(c-=ht(n)),c=Math.min(c,e.wtRootElement.scrollWidth),s.width="".concat(c,"px")}else s.width="";this.clone.wtTable.holder.style.width=s.width;let a=yt(this.clone.wtTable.TABLE);e.hasDefinedSize()||(a=0),s.height="".concat(a,"px")}adjustRootChildrenSize(){const{holder:e}=this.clone.wtTable;this.clone.wtTable.hider.style.width=this.hider.style.width,e.style.width=e.parentNode.style.width,e.style.height=e.parentNode.style.height}applyToDOM(){const e=this.wtSettings.getSetting("totalRows");if(typeof this.wot.wtViewport.rowsRenderCalculator.startPosition=="number")this.spreader.style.top="".concat(this.wot.wtViewport.rowsRenderCalculator.startPosition,"px");else if(e===0)this.spreader.style.top="0";else throw new Error("Incorrect value of the rowsRenderCalculator");this.spreader.style.bottom="",this.needFullRender&&this.syncOverlayOffset()}syncOverlayOffset(){const e=this.isRtl()?"right":"left",{spreader:r}=this.clone.wtTable;typeof this.wot.wtViewport.columnsRenderCalculator.startPosition=="number"?r.style[e]="".concat(this.wot.wtViewport.columnsRenderCalculator.startPosition,"px"):r.style[e]=""}scrollTo(e,r){let n=this.getTableParentOffset();const o=(this.wot.cloneSource?this.wot.cloneSource:this.wot).wtTable.holder;let s=0;r&&o.offsetHeight!==o.clientHeight&&(s=ht(this.domBindings.rootDocument)),r?(n+=this.sumCellSizes(0,e+1),n-=this.wot.wtViewport.getViewportHeight(),n+=1):n+=this.sumCellSizes(this.wtSettings.getSetting("fixedRowsBottom"),e),n+=s,this.setScrollPosition(n)}getTableParentOffset(){return this.mainTableScrollableElement===this.domBindings.rootWindow?this.wot.wtTable.holderOffset.top:0}getScrollPosition(){return cc(this.mainTableScrollableElement,this.domBindings.rootWindow)}getOverlayOffset(){const{rootWindow:e}=this.domBindings,r=this.wtSettings.getSetting("preventOverflow");let n=0;if(this.trimmingContainer===e&&(!r||r!=="vertical")){const i=this.wot.wtTable.getTotalHeight(),o=this.clone.wtTable.getTotalHeight(),s=i-o,l=this.domBindings.rootDocument.documentElement.clientHeight;n=Math.max(this.getTableParentOffset()-this.getScrollPosition()-l+i,0),n>s&&(n=0)}return n}adjustHeaderBordersPosition(e){const r=this.wtSettings.getSetting("fixedRowsBottom"),n=this.cachedFixedRowsBottom!==r,i=this.wtSettings.getSetting("columnHeaders");let o=!1;if((n||r===0)&&i.length>0){const s=this.wot.wtTable.holder.parentNode,l=ee(s,"innerBorderBottom");this.cachedFixedRowsBottom=this.wtSettings.getSetting("fixedRowsBottom"),e||this.wtSettings.getSetting("totalRows")===0?(G(s,"innerBorderBottom"),o=!l):(ce(s,"innerBorderBottom"),o=l)}return o}}const pA="calculatedRows",Km={getFirstRenderedRow(){const t=this.dataAccessObject.startRowRendered;return t===null?-1:t},getFirstVisibleRow(){const t=this.dataAccessObject.startRowVisible;return t===null?-1:t},getFirstPartiallyVisibleRow(){const t=this.dataAccessObject.startRowPartiallyVisible;return t===null?-1:t},getLastRenderedRow(){const t=this.dataAccessObject.endRowRendered;return t===null?-1:t},getLastVisibleRow(){const t=this.dataAccessObject.endRowVisible;return t===null?-1:t},getLastPartiallyVisibleRow(){const t=this.dataAccessObject.endRowPartiallyVisible;return t===null?-1:t},getRenderedRowsCount(){return this.dataAccessObject.countRowsRendered},getVisibleRowsCount(){return this.dataAccessObject.countRowsVisible},getColumnHeadersCount(){return this.dataAccessObject.columnHeaders.length}};zr(Km,"MIXIN_NAME",pA,{writable:!1,enumerable:!1});const Ym=Km;class Rc extends Zn{constructor(e,r,n,i){super(e,r,n,i,dn)}}Se(Rc,Ym);Se(Rc,vc);const wA=Rc,vA="localHooks",Xm={_localHooks:Object.create(null),addLocalHook(t,e){return this._localHooks[t]||(this._localHooks[t]=[]),this._localHooks[t].push(e),this},runLocalHooks(t,e,r,n,i,o,s){if(this._localHooks[t]){const l=this._localHooks[t].length;for(let a=0;a<l;a++)Oa(this._localHooks[t][a],this,e,r,n,i,o,s)}},clearLocalHooks(){return this._localHooks={},this}};zr(Xm,"MIXIN_NAME",vA,{writable:!1,enumerable:!1});const Ur=Xm;let qm=class{constructor(e,r){this.settings=e,this.cellRange=r||null}isEmpty(){return this.cellRange===null}add(e){return this.isEmpty()?this.cellRange=this.settings.createCellRange(e):this.cellRange.expand(e),this}replace(e,r){if(!this.isEmpty()){if(this.cellRange.from.isEqual(e))return this.cellRange.from=r,!0;if(this.cellRange.to.isEqual(e))return this.cellRange.to=r,!0}return!1}clear(){return this.cellRange=null,this}getCorners(){const e=this.cellRange.getOuterTopStartCorner(),r=this.cellRange.getOuterBottomEndCorner();return[e.row,e.col,r.row,r.col]}destroy(){this.runLocalHooks("destroy")}};Se(qm,Ur);const yA=qm,CA="active-header",Qm="header",Tc="area",Fo="focus",bA="fill",SA="row",RA="column",TA="custom-selection",Ec=t=>{const e=t.stylesHandler;if(e.isClassicTheme())return Object.freeze({width:6,height:6,borderWidth:1,borderStyle:"solid",borderColor:"#FFF"});const r=e.getCSSVariableValue("cell-autofill-size"),n=e.getCSSVariableValue("cell-autofill-border-width"),i=e.getCSSVariableValue("cell-autofill-border-color");return Object.freeze({width:r,height:r,borderWidth:n,borderStyle:"solid",borderColor:i})};var EA=dc;EA("flat");function OA(t,e){Zm(t,e),e.add(t)}function bd(t,e,r){Zm(t,e),e.set(t,r)}function Zm(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function me(t,e){return t.get(Bo(t,e))}function Sd(t,e,r){return t.set(Bo(t,e),r),r}function Bo(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var Yt=new WeakMap,Ge=new WeakMap,Pi=new WeakSet;class HA{constructor(){OA(this,Pi),bd(this,Yt,void 0),bd(this,Ge,void 0)}setActiveOverlay(e){return Sd(Ge,this,e),this}setActiveSelection(e){return Sd(Yt,this,e),this}scan(){const e=me(Yt,this).settings.selectionType,r=new Set;return e==="active-header"?(this.scanColumnsInHeadersRange(n=>r.add(n)),this.scanRowsInHeadersRange(n=>r.add(n))):e==="area"?this.scanCellsRange(n=>r.add(n)):e==="focus"?(this.scanColumnsInHeadersRange(n=>r.add(n)),this.scanRowsInHeadersRange(n=>r.add(n)),this.scanCellsRange(n=>r.add(n))):e==="fill"?this.scanCellsRange(n=>r.add(n)):e==="header"?(this.scanColumnsInHeadersRange(n=>r.add(n)),this.scanRowsInHeadersRange(n=>r.add(n))):e==="row"?(this.scanRowsInHeadersRange(n=>r.add(n)),this.scanRowsInCellsRange(n=>r.add(n))):e==="column"&&(this.scanColumnsInHeadersRange(n=>r.add(n)),this.scanColumnsInCellsRange(n=>r.add(n))),r}scanColumnsInHeadersRange(e){const[r,n,i,o]=me(Yt,this).getCorners(),{wtTable:s}=me(Ge,this),l=s.getRenderedColumnsCount(),a=s.getColumnHeadersCount();let c=0;for(let d=-s.getRowHeadersCount();d<l;d++){const f=s.columnFilter.renderedToSource(d);if(!(f<n||f>o)){for(let m=-a;m<0;m++){if(m<r||m>i)continue;const p=m+a;let v=s.getColumnHeader(f,p);const R=me(Ge,this).getSetting("onBeforeHighlightingColumnHeader",f,p,{selectionType:me(Yt,this).settings.selectionType,columnCursor:c,selectionWidth:o-n+1});R!==null&&(R!==f&&(v=s.getColumnHeader(R,p)),e(v))}c+=1}}}scanRowsInHeadersRange(e){const[r,n,i,o]=me(Yt,this).getCorners(),{wtTable:s}=me(Ge,this),l=s.getRenderedRowsCount(),a=s.getRowHeadersCount();let c=0;for(let d=-s.getColumnHeadersCount();d<l;d++){const f=s.rowFilter.renderedToSource(d);if(!(f<r||f>i)){for(let m=-a;m<0;m++){if(m<n||m>o)continue;const p=m+a;let v=s.getRowHeader(f,p);const R=me(Ge,this).getSetting("onBeforeHighlightingRowHeader",f,p,{selectionType:me(Yt,this).settings.selectionType,rowCursor:c,selectionHeight:i-r+1});R!==null&&(R!==f&&(v=s.getRowHeader(R,p)),e(v))}c+=1}}}scanCellsRange(e){const{wtTable:r}=me(Ge,this);Bo(Pi,this,IA).call(this,(n,i)=>{const o=r.getCell(me(Ge,this).createCellCoords(n,i)),s=me(Ge,this).getSetting("onAfterDrawSelection",n,i,me(Yt,this).settings.layerLevel);typeof s=="string"&&G(o,s),e(o)})}scanRowsInCellsRange(e){const[r,,n]=me(Yt,this).getCorners(),{wtTable:i}=me(Ge,this);Bo(Pi,this,Rd).call(this,(o,s)=>{if(o>=r&&o<=n){const l=i.getCell(me(Ge,this).createCellCoords(o,s));e(l)}})}scanColumnsInCellsRange(e){const[,r,,n]=me(Yt,this).getCorners(),{wtTable:i}=me(Ge,this);Bo(Pi,this,Rd).call(this,(o,s)=>{if(s>=r&&s<=n){const l=i.getCell(me(Ge,this).createCellCoords(o,s));e(l)}})}}function IA(t){let[e,r,n,i]=me(Yt,this).getCorners();if(e<0&&n<0||r<0&&i<0)return;const{wtTable:o}=me(Ge,this),s=e!==n||r!==i;if(r=Math.max(r,0),i=Math.max(i,0),e=Math.max(e,0),n=Math.max(n,0),s){if(r=Math.max(r,o.getFirstRenderedColumn()),i=Math.min(i,o.getLastRenderedColumn()),e=Math.max(e,o.getFirstRenderedRow()),n=Math.min(n,o.getLastRenderedRow()),i<r||n<e)return}else{const l=o.getCell(me(Ge,this).createCellCoords(e,r));if(!xs(l))return}for(let l=e;l<=n;l+=1)for(let a=r;a<=i;a+=1)t(l,a)}function Rd(t){const{wtTable:e}=me(Ge,this),r=e.getRenderedRowsCount(),n=e.getRenderedColumnsCount();for(let i=0;i<r;i+=1){const o=e.rowFilter.renderedToSource(i);for(let s=0;s<n;s+=1)t(o,e.columnFilter.renderedToSource(s))}}class MA{constructor(e,r){r&&(this.eventManager=e.eventManager,this.instance=e,this.wot=e,this.settings=r,this.mouseDown=!1,this.main=null,this.top=null,this.bottom=null,this.start=null,this.end=null,this.topStyle=null,this.bottomStyle=null,this.startStyle=null,this.endStyle=null,this.cornerDefaultStyle=Ec(this.instance),this.cornerCenterPointOffset=-Math.ceil(parseInt(this.cornerDefaultStyle.width,10)/2),this.corner=null,this.cornerStyle=null,this.createBorders(r),this.registerListeners())}registerListeners(){const e=this.wot.rootDocument.body;this.eventManager.addEventListener(e,"mousedown",()=>this.onMouseDown()),this.eventManager.addEventListener(e,"mouseup",()=>this.onMouseUp());for(let r=0,n=this.main.childNodes.length;r<n;r++){const i=this.main.childNodes[r];this.eventManager.addEventListener(i,"mouseenter",o=>this.onMouseEnter(o,this.main.childNodes[r]))}}onMouseDown(){this.mouseDown=!0}onMouseUp(){this.mouseDown=!1}onMouseEnter(e,r){if(!this.mouseDown||!this.wot.getSetting("hideBorderOnMouseDownOver"))return;e.preventDefault(),Ps(e);const n=this,i=this.wot.rootDocument.body,o=r.getBoundingClientRect();r.style.display="none";function s(a){if(a.clientY<Math.floor(o.top)||a.clientY>Math.ceil(o.top+o.height)||a.clientX<Math.floor(o.left)||a.clientX>Math.ceil(o.left+o.width))return!0}function l(a){s(a)&&(n.eventManager.removeEventListener(i,"mousemove",l),r.style.display="block")}this.eventManager.addEventListener(i,"mousemove",l)}createBorders(e){const{rootDocument:r}=this.wot;this.main=r.createElement("div");const n=["top","start","bottom","end","corner"];let i=this.main.style;i.position="absolute",i.top=0,i.left=0;for(let l=0;l<5;l++){const a=n[l],c=r.createElement("div");c.className="wtBorder ".concat(this.settings.className||""),this.settings[a]&&this.settings[a].hide&&(c.className+=" hidden"),i=c.style,i.backgroundColor=this.settings[a]&&this.settings[a].color?this.settings[a].color:e.border.color,i.height=this.settings[a]&&this.settings[a].width?"".concat(this.settings[a].width,"px"):"".concat(e.border.width,"px"),i.width=this.settings[a]&&this.settings[a].width?"".concat(this.settings[a].width,"px"):"".concat(e.border.width,"px"),this.main.appendChild(c)}this.top=this.main.childNodes[0],this.start=this.main.childNodes[1],this.bottom=this.main.childNodes[2],this.end=this.main.childNodes[3],this.topStyle=this.top.style,this.startStyle=this.start.style,this.bottomStyle=this.bottom.style,this.endStyle=this.end.style,this.corner=this.main.childNodes[4],this.corner.className+=" corner",this.cornerStyle=this.corner.style,this.cornerStyle.width="".concat(this.cornerDefaultStyle.width,"px"),this.cornerStyle.height="".concat(this.cornerDefaultStyle.height,"px"),this.cornerStyle.border=["".concat(this.cornerDefaultStyle.borderWidth,"px"),this.cornerDefaultStyle.borderStyle,this.cornerDefaultStyle.borderColor].join(" "),Vn()&&this.instance.getSetting("isDataViewInstance")&&this.createMultipleSelectorHandles(),this.disappear();const{wtTable:o}=this.wot;let s=o.bordersHolder;s||(s=r.createElement("div"),s.className="htBorders",o.bordersHolder=s,o.spreader.appendChild(s)),s.appendChild(this.main)}createMultipleSelectorHandles(){const{rootDocument:e,stylesHandler:r}=this.wot,n=r.getCSSVariableValue("cell-mobile-handle-size"),i=r.getCSSVariableValue("cell-mobile-handle-border-radius"),o=r.getCSSVariableValue("cell-mobile-handle-background-color"),s=r.getCSSVariableValue("cell-mobile-handle-border-width"),l=r.getCSSVariableValue("cell-mobile-handle-border-color");this.selectionHandles={top:e.createElement("DIV"),topHitArea:e.createElement("DIV"),bottom:e.createElement("DIV"),bottomHitArea:e.createElement("DIV")};const a=10,c=40;this.selectionHandles.top.className="topSelectionHandle topLeftSelectionHandle",this.selectionHandles.topHitArea.className="topSelectionHandle-HitArea topLeftSelectionHandle-HitArea",this.selectionHandles.bottom.className="bottomSelectionHandle bottomRightSelectionHandle",this.selectionHandles.bottomHitArea.className="bottomSelectionHandle-HitArea bottomRightSelectionHandle-HitArea",this.selectionHandles.styles={top:this.selectionHandles.top.style,topHitArea:this.selectionHandles.topHitArea.style,bottom:this.selectionHandles.bottom.style,bottomHitArea:this.selectionHandles.bottomHitArea.style};const d={position:"absolute",height:"".concat(c,"px"),width:"".concat(c,"px"),"border-radius":"".concat(parseInt(c/1.5,10),"px")};fe(d,(m,p)=>{this.selectionHandles.styles.bottomHitArea[p]=m,this.selectionHandles.styles.topHitArea[p]=m});const f=r.isClassicTheme()?{position:"absolute",height:"".concat(a,"px"),width:"".concat(a,"px"),"border-radius":"".concat(parseInt(a/1.5,10),"px"),background:"#F5F5FF",border:"1px solid #4285c8"}:{position:"absolute",height:"".concat(n,"px"),width:"".concat(n,"px"),"border-radius":"".concat(i,"px"),background:"".concat(o),border:"".concat(s,"px solid ").concat(l)};fe(f,(m,p)=>{this.selectionHandles.styles.bottom[p]=m,this.selectionHandles.styles.top[p]=m}),this.main.appendChild(this.selectionHandles.top),this.main.appendChild(this.selectionHandles.bottom),this.main.appendChild(this.selectionHandles.topHitArea),this.main.appendChild(this.selectionHandles.bottomHitArea)}isPartRange(e,r){const n=this.wot.selectionManager.getAreaSelection();return!!(n.cellRange&&(e!==n.cellRange.to.row||r!==n.cellRange.to.col))}updateMultipleSelectionHandlesPosition(e,r,n,i,o,s){const a=this.wot.wtSettings.getSetting("rtlMode")?"right":"left",{top:c,topHitArea:d,bottom:f,bottomHitArea:m}=this.selectionHandles.styles,p=parseInt(c.borderWidth,10),v=parseInt(c.width,10),R=parseInt(d.width,10),H=this.wot.wtTable.getWidth(),M=this.wot.wtTable.getHeight();c.top="".concat(parseInt(n-v-1,10),"px"),c[a]="".concat(parseInt(i-v-1,10),"px"),d.top="".concat(parseInt(n-R/4*3,10),"px"),d[a]="".concat(parseInt(i-R/4*3,10),"px");const b=Math.min(parseInt(i+o,10),H-v-p*2),k=Math.min(parseInt(i+o-R/4,10),H-R-p*2);f[a]="".concat(b,"px"),m[a]="".concat(k,"px");const $=Math.min(parseInt(n+s,10),M-v-p*2),W=Math.min(parseInt(n+s-R/4,10),M-R-p*2);f.top="".concat($,"px"),m.top="".concat(W,"px"),this.settings.border.cornerVisible&&this.settings.border.cornerVisible()?(c.display="block",d.display="block",this.isPartRange(e,r)?(f.display="none",m.display="none"):(f.display="block",m.display="block")):(c.display="none",f.display="none",d.display="none",m.display="none"),e===this.wot.wtSettings.getSetting("fixedRowsTop")||r===this.wot.wtSettings.getSetting("fixedColumnsStart")?(c.zIndex="9999",d.zIndex="9999"):(c.zIndex="",d.zIndex="")}appear(e){if(this.disabled)return;let[r,n,i,o]=e;if(r<0&&i<0||n<0&&o<0){this.disappear();return}const{wtTable:s,rootDocument:l,rootWindow:a}=this.wot,c=r!==i||n!==o,d=s.getFirstRenderedRow(),f=s.getLastRenderedRow(),m=s.getFirstRenderedColumn(),p=s.getLastRenderedColumn();if(m<0&&p<0||d<0&&f<0){this.disappear();return}let v;if(c){if(n=Math.max(n,m),o=Math.min(o,p),r=Math.max(r,d),i=Math.min(i,f),o<n||i<r){this.disappear();return}v=s.getCell(this.wot.createCellCoords(r,n))}else if(v=s.getCell(this.wot.createCellCoords(r,n)),!xs(v)){this.disappear();return}const R=c?s.getCell(this.wot.createCellCoords(i,o)):v,H=At(v),M=c?At(R):H,b=At(s.TABLE),k=H.top,$=H.left,W=this.wot.wtSettings.getSetting("rtlMode");let D=0,F=0;if(W){const ue=ut(s.TABLE),ye=ut(v),xe=a.innerWidth-b.left-ue;F=$+ye-M.left,D=a.innerWidth-$-ye-xe-1}else F=M.left+ut(R)-$,D=$-b.left-1;if(this.isEntireColumnSelected(r,i)){const ue=r,ye=this.getDimensionsFromHeader("columns",n,o,ue,b);let xe=null;ye&&([xe,D,F]=ye),xe&&(v=xe)}let ve=k-b.top-1,V=M.top+yt(R)-k;if(this.isEntireRowSelected(n,o)){const ue=n,ye=this.getDimensionsFromHeader("rows",r,i,ue,b);let xe=null;ye&&([xe,ve,V]=ye),xe&&(v=xe)}const St=a.getComputedStyle(v);parseInt(St.borderTopWidth,10)>0&&(ve+=1,V=V>0?V-1:0),parseInt(St[W?"borderRightWidth":"borderLeftWidth"],10)>0&&(D+=1,F=F>0?F-1:0);const te=W?"right":"left";this.topStyle.top="".concat(ve,"px"),this.topStyle[te]="".concat(D,"px"),this.topStyle.width="".concat(F,"px"),this.topStyle.display="block",this.startStyle.top="".concat(ve,"px"),this.startStyle[te]="".concat(D,"px"),this.startStyle.height="".concat(V,"px"),this.startStyle.display="block";const Rt=Math.floor(this.settings.border.width/2);this.bottomStyle.top="".concat(ve+V-Rt,"px"),this.bottomStyle[te]="".concat(D,"px"),this.bottomStyle.width="".concat(F,"px"),this.bottomStyle.display="block",this.endStyle.top="".concat(ve,"px"),this.endStyle[te]="".concat(D+F-Rt,"px"),this.endStyle.height="".concat(V+1,"px"),this.endStyle.display="block";let Ye=this.settings.border.cornerVisible;Ye=typeof Ye=="function"?Ye(this.settings.layerLevel):Ye;const Ft=this.wot.getSetting("onModifyGetCellCoords",i,o,!1,"render");let[Bt,Wt]=[i,o];if(Ft&&Array.isArray(Ft)&&([,,Bt,Wt]=Ft),Vn()||!Ye||this.isPartRange(Bt,Wt))this.cornerStyle.display="none";else{this.cornerStyle.top="".concat(ve+V+this.cornerCenterPointOffset-this.cornerDefaultStyle.borderWidth,"px"),this.cornerStyle[te]="".concat(D+F+this.cornerCenterPointOffset-this.cornerDefaultStyle.borderWidth,"px"),this.cornerStyle.borderRightWidth="".concat(this.cornerDefaultStyle.borderWidth,"px"),this.cornerStyle.borderLeftWidth="".concat(this.cornerDefaultStyle.borderWidth,"px"),this.cornerStyle.borderBottomWidth="".concat(this.cornerDefaultStyle.borderWidth,"px"),this.cornerStyle.width=this.cornerDefaultStyle.width,this.cornerStyle.display="none";let ue=ms(s.TABLE);const ye=ue===a;ye&&(ue=l.documentElement);const xe=parseInt(this.cornerDefaultStyle.borderWidth,10)-1,mr=Math.ceil(parseInt(this.cornerDefaultStyle.width,10)/2),J=Math.ceil(parseInt(this.cornerDefaultStyle.height,10)/2);if(o===this.wot.getSetting("totalColumns")-1){const u=ye?R.getBoundingClientRect().left:R.offsetLeft;let h=!1,g=0;W?(g=u-parseInt(this.cornerDefaultStyle.width,10)/2,h=g<0):(g=u+ut(R)+parseInt(this.cornerDefaultStyle.width,10)/2,h=g>=fm(ue)),h&&(this.cornerStyle[te]="".concat(Math.floor(D+F+this.cornerCenterPointOffset-mr-xe),"px"),this.cornerStyle[W?"borderLeftWidth":"borderRightWidth"]=0)}if(i===this.wot.getSetting("totalRows")-1){const g=(ye?R.getBoundingClientRect().top:R.offsetTop)+yt(R)+parseInt(this.cornerDefaultStyle.height,10)/2>=ko(ue),w=this.wot.stylesHandler.isClassicTheme();if(g){const C=Math.floor(ve+V+this.cornerCenterPointOffset-J-xe);w?(this.cornerStyle.top="".concat(C,"px"),this.cornerStyle.borderBottomWidth=0):this.cornerStyle.top="".concat(C-1,"px")}}this.cornerStyle.display="block"}Vn()&&this.instance.getSetting("isDataViewInstance")&&this.updateMultipleSelectionHandlesPosition(i,o,ve,D,F,V)}isEntireColumnSelected(e,r){return e===this.wot.wtTable.getFirstRenderedRow()&&r===this.wot.wtTable.getLastRenderedRow()}isEntireRowSelected(e,r){return e===this.wot.wtTable.getFirstRenderedColumn()&&r===this.wot.wtTable.getLastRenderedColumn()}getDimensionsFromHeader(e,r,n,i,o){const{wtTable:s}=this.wot,l=s.wtRootElement.parentNode;let a=null,c=null,d=null,f=null,m=null,p=null,v=null,R=null;switch(e){case"rows":a=function(){return s.getRowHeader(...arguments)},c=function(){return yt(...arguments)},d="ht__selection--rows",p="top";break;case"columns":a=function(){return s.getColumnHeader(...arguments)},c=function(){return ut(...arguments)},d="ht__selection--columns",p="left";break}if(l.classList.contains(d)){const H=this.wot.getSetting("columnHeaders").length;if(v=a(r,H-i),R=a(n,H-i),!v||!R)return!1;const M=At(v),b=At(R);return v&&R&&(f=M[p]-o[p]-1,m=b[p]+c(R)-M[p]),[v,f,m]}return!1}changeBorderStyle(e,r){const n=this[e].style,i=r[e];!i||i.hide?G(this[e],"hidden"):(ee(this[e],"hidden")&&ce(this[e],"hidden"),n.backgroundColor=i.color,(e==="top"||e==="bottom")&&(n.height="".concat(i.width,"px")),(e==="start"||e==="end")&&(n.width="".concat(i.width,"px")))}changeBorderToDefaultStyle(e){const r={width:1,color:"#000"},n=this[e].style;n.backgroundColor=r.color,n.width="".concat(r.width,"px"),n.height="".concat(r.width,"px")}toggleHiddenClass(e,r){this.changeBorderToDefaultStyle(e),r?G(this[e],"hidden"):ce(this[e],"hidden")}disappear(){this.topStyle.display="none",this.bottomStyle.display="none",this.startStyle.display="none",this.endStyle.display="none",this.cornerStyle.display="none",Vn()&&this.instance.getSetting("isDataViewInstance")&&(this.selectionHandles.styles.top.display="none",this.selectionHandles.styles.topHitArea.display="none",this.selectionHandles.styles.bottom.display="none",this.selectionHandles.styles.bottomHitArea.display="none")}destroy(){this.eventManager.destroyWithOwnEventsOnly(),this.main.parentNode.removeChild(this.main)}}const Td=MA;function xA(t,e){Jm(t,e),e.add(t)}function xn(t,e,r){Jm(t,e),e.set(t,r)}function Jm(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function z(t,e){return t.get(Oc(t,e))}function Ed(t,e,r){return t.set(Oc(t,e),r),r}function Oc(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var Ke=new WeakMap,at=new WeakMap,jl=new WeakMap,_o=new WeakMap,zl=new WeakMap,Zr=new WeakMap,Od=new WeakSet;class _A{constructor(e){xA(this,Od),xn(this,Ke,void 0),xn(this,at,void 0),xn(this,jl,new HA),xn(this,_o,new WeakMap),xn(this,zl,new WeakSet),xn(this,Zr,new Map),Ed(at,this,e)}setActiveOverlay(e){return Ed(Ke,this,e),z(jl,this).setActiveOverlay(z(Ke,this)),z(_o,this).has(z(Ke,this))||z(_o,this).set(z(Ke,this),new Set),this}getFocusSelection(){return z(at,this)!==null?z(at,this).getFocus():null}getAreaSelection(){return z(at,this)!==null?z(at,this).createLayeredArea():null}getBorderInstance(e){if(!e.settings.border)return null;if(z(Zr,this).has(e)){const n=z(Zr,this).get(e);if(n.has(z(Ke,this)))return n.get(z(Ke,this));const i=new Td(z(Ke,this),e.settings);return n.set(z(Ke,this),i),i}const r=new Td(z(Ke,this),e.settings);return z(Zr,this).set(e,new Map([[z(Ke,this),r]])),r}getBorderInstances(e){var r,n;return Array.from((r=(n=z(Zr,this).get(e))===null||n===void 0?void 0:n.values())!==null&&r!==void 0?r:[])}destroyBorders(e){z(Zr,this).get(e).forEach(r=>r.destroy()),z(Zr,this).delete(e)}render(e){if(z(at,this)===null)return;e&&Oc(Od,this,PA).call(this);const r=Array.from(z(at,this)),n=new Map,i=new Map;for(let o=0;o<r.length;o++){const s=r[o],{className:l,headerAttributes:a,createLayers:c,selectionType:d}=s.settings;z(zl,this).has(s)||(z(zl,this).add(s),s.addLocalHook("destroy",()=>this.destroyBorders(s)));const f=this.getBorderInstance(s);if(s.isEmpty()){f==null||f.disappear();continue}l&&z(jl,this).setActiveSelection(s).scan().forEach(v=>{if(n.has(v)){const R=n.get(v);R.has(l)&&c===!0?R.set(l,R.get(l)+1):R.set(l,1)}else n.set(v,new Map([[l,1]]));a&&(i.has(v)||i.set(v,[]),v.nodeName==="TH"&&i.get(v).push(...a))});const m=s.getCorners();z(Ke,this).getSetting("onBeforeDrawBorders",m,d),f==null||f.appear(m)}n.forEach((o,s)=>{var l;const a=Array.from(o).map(c=>{let[d,f]=c;return f===1?d:[d,...Array.from({length:f-1},(m,p)=>"".concat(d,"-").concat(p+1))]}).flat();a.forEach(c=>z(_o,this).get(z(Ke,this)).add(c)),G(s,a),s.nodeName==="TD"&&Array.isArray((l=z(at,this).options)===null||l===void 0?void 0:l.cellAttributes)&&de(s,z(at,this).options.cellAttributes)}),Array.from(i.keys()).forEach(o=>{de(o,[...i.get(o)])})}}function PA(){const t=z(_o,this).get(z(Ke,this)),e=z(Ke,this).wtSettings.getSetting("onBeforeRemoveCellClassNames");if(Array.isArray(e))for(let r=0;r<e.length;r++)t.add(e[r]);t.forEach(r=>{var n,i;const o=z(Ke,this).wtTable.TABLE.querySelectorAll(".".concat(r));let s=[];Array.isArray((n=z(at,this).options)===null||n===void 0?void 0:n.cellAttributes)&&(s=z(at,this).options.cellAttributes.map(l=>l[0])),Array.isArray((i=z(at,this).options)===null||i===void 0?void 0:i.headerAttributes)&&(s=[...s,...z(at,this).options.headerAttributes.map(l=>l[0])]);for(let l=0,a=o.length;l<a;l++)ce(o[l],r),ii(o[l],s)}),t.clear()}class AA extends ci{constructor(e,r,n,i){super(e,r,dn,n,i)}createTable(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return new wA(...r)}shouldBeRendered(){return this.wtSettings.getSetting("shouldRenderInlineStartOverlay")}resetFixedPosition(){const{wtTable:e}=this.wot;if(!this.needFullRender||!this.shouldBeRendered()||!e.holder.parentNode)return!1;const{rootWindow:r}=this.domBindings,n=this.clone.wtTable.holder.parentNode,i=this.wtSettings.getSetting("preventOverflow");let o=0;this.trimmingContainer===r&&(!i||i!=="horizontal")?(o=this.getOverlayOffset()*(this.isRtl()?-1:1),uc(n,"".concat(o,"px"),"0px")):(o=this.getScrollPosition(),Ms(n));const s=this.adjustHeaderBordersPosition(o);return this.adjustElementsSize(),s}setScrollPosition(e){const{rootWindow:r}=this.domBindings;let n=!1;this.isRtl()&&(e=-e);const i=this.mainTableScrollableElement;if(i===r&&e!==r.scrollX){const o=r.scrollX;r.scrollTo(e,hm(r)),n=o!==r.scrollX}else if(e!==i.scrollLeft){const o=i.scrollLeft;i.scrollLeft=e,n=o!==i.scrollLeft}return n}onScroll(){this.wtSettings.getSetting("onScrollVertically")}sumCellSizes(e,r){const n=this.wtSettings.getSetting("defaultColumnWidth");let i=e,o=0;for(;i<r;)o+=this.wot.wtTable.getColumnWidth(i)||n,i+=1;return o}adjustElementsSize(){this.updateTrimmingContainer(),this.needFullRender&&(this.adjustRootElementSize(),this.adjustRootChildrenSize())}adjustRootElementSize(){const{wtTable:e,wtViewport:r}=this.wot,{rootDocument:n,rootWindow:i}=this.domBindings,s=this.clone.wtTable.holder.parentNode.style,l=this.wtSettings.getSetting("preventOverflow");if(this.trimmingContainer!==i||l==="vertical"){let c=r.getWorkspaceHeight();r.hasHorizontalScroll()&&(c-=ht(n)),c=Math.min(c,e.wtRootElement.scrollHeight),s.height="".concat(c,"px")}else s.height="";this.clone.wtTable.holder.style.height=s.height;const a=ut(this.clone.wtTable.TABLE);s.width="".concat(a,"px")}adjustRootChildrenSize(){const{holder:e}=this.clone.wtTable,r=Ec(this.wot),n=this.wot.selectionManager.getFocusSelection()?parseInt(r.width,10)/2:0;this.clone.wtTable.hider.style.height=this.hider.style.height,e.style.height=e.parentNode.style.height,e.style.width="".concat(parseInt(e.parentNode.style.width,10)+n,"px")}applyToDOM(){const e=this.wtSettings.getSetting("totalColumns"),r=this.isRtl()?"right":"left";if(typeof this.wot.wtViewport.columnsRenderCalculator.startPosition=="number")this.spreader.style[r]="".concat(this.wot.wtViewport.columnsRenderCalculator.startPosition,"px");else if(e===0)this.spreader.style[r]="0";else throw new Error("Incorrect value of the columnsRenderCalculator");this.isRtl()?this.spreader.style.left="":this.spreader.style.right="",this.needFullRender&&this.syncOverlayOffset()}syncOverlayOffset(){typeof this.wot.wtViewport.rowsRenderCalculator.startPosition=="number"?this.clone.wtTable.spreader.style.top="".concat(this.wot.wtViewport.rowsRenderCalculator.startPosition,"px"):this.clone.wtTable.spreader.style.top=""}scrollTo(e,r){const{wtSettings:n}=this,i=n.getSetting("rowHeaders"),o=n.getSetting("fixedColumnsStart"),l=(this.wot.cloneSource?this.wot.cloneSource:this.wot).wtTable.holder,a=o===0&&i.length>0&&!ee(l.parentNode,"innerBorderInlineStart")?1:0;let c=this.getTableParentOffset(),d=0;if(r){const f=this.wot.wtTable.getColumnWidth(e),m=this.wot.wtViewport.getViewportWidth();f>m&&(r=!1)}return r&&l.offsetWidth!==l.clientWidth&&(d=ht(this.domBindings.rootDocument)),r?(c+=this.sumCellSizes(0,e+1),c-=this.wot.wtViewport.getViewportWidth(),c+=a):c+=this.sumCellSizes(this.wtSettings.getSetting("fixedColumnsStart"),e),c+=d,BH(this.mainTableScrollableElement)===c-a&&a>0&&this.wot.wtOverlays.expandHiderHorizontallyBy(a),this.setScrollPosition(c)}getTableParentOffset(){const e=this.wtSettings.getSetting("preventOverflow");let r=0;return!e&&this.trimmingContainer===this.domBindings.rootWindow&&(r=this.wot.wtTable.holderOffset.left),r}getScrollPosition(){return Math.abs(dm(this.mainTableScrollableElement,this.domBindings.rootWindow))}getOverlayOffset(){const{rootWindow:e}=this.domBindings,r=this.wtSettings.getSetting("preventOverflow");let n=0;if(this.trimmingContainer===e&&(!r||r!=="horizontal")){this.isRtl()?n=Math.abs(Math.min(this.getTableParentOffset()-this.getScrollPosition(),0)):n=Math.max(this.getScrollPosition()-this.getTableParentOffset(),0);const i=this.wot.wtTable.getTotalWidth(),o=this.clone.wtTable.getTotalWidth(),s=i-o;n>s&&(n=0)}return n}adjustHeaderBordersPosition(e){const{wtSettings:r}=this,n=this.wot.wtTable.holder.parentNode,i=r.getSetting("rowHeaders"),o=r.getSetting("fixedColumnsStart"),s=r.getSetting("totalRows"),l=r.getSetting("preventOverflow")==="vertical";s?ce(n,"emptyRows"):G(n,"emptyRows");let a=!1;if(!l){if(o&&!i.length)G(n,"innerBorderLeft innerBorderInlineStart");else if(!o&&i.length){const c=ee(n,"innerBorderInlineStart");e?(G(n,"innerBorderLeft innerBorderInlineStart"),a=!c):(ce(n,"innerBorderLeft innerBorderInlineStart"),a=c)}}return a}}const NA="stickyRowsTop",ep={getFirstRenderedRow(){return this.getRenderedRowsCount()===0?-1:0},getFirstVisibleRow(){return this.getFirstRenderedRow()},getFirstPartiallyVisibleRow(){return this.getFirstRenderedRow()},getLastRenderedRow(){return this.getRenderedRowsCount()-1},getLastVisibleRow(){return this.getLastRenderedRow()},getLastPartiallyVisibleRow(){return this.getLastRenderedRow()},getRenderedRowsCount(){return Math.min(this.wtSettings.getSetting("totalRows"),this.wtSettings.getSetting("fixedRowsTop"))},getVisibleRowsCount(){return this.getRenderedRowsCount()},getColumnHeadersCount(){return this.dataAccessObject.columnHeaders.length}};zr(ep,"MIXIN_NAME",NA,{writable:!1,enumerable:!1});const tp=ep;class Hc extends Zn{constructor(e,r,n,i){super(e,r,n,i,fn)}}Se(Hc,tp);Se(Hc,vc);const $A=Hc;function Hd(t,e,r){return(e=LA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function LA(t){var e=DA(t,"string");return typeof e=="symbol"?e:e+""}function DA(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class VA extends ci{constructor(e,r,n,i,o,s){super(e,r,fn,n,i),Hd(this,"topOverlay",void 0),Hd(this,"inlineStartOverlay",void 0),this.topOverlay=o,this.inlineStartOverlay=s}createTable(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return new $A(...r)}shouldBeRendered(){return this.wtSettings.getSetting("shouldRenderTopOverlay")&&this.wtSettings.getSetting("shouldRenderInlineStartOverlay")}resetFixedPosition(){if(this.updateTrimmingContainer(),!this.wot.wtTable.holder.parentNode)return!1;const e=this.clone.wtTable.holder.parentNode;if(this.trimmingContainer===this.domBindings.rootWindow){const i=this.inlineStartOverlay.getOverlayOffset()*(this.isRtl()?-1:1),o=this.topOverlay.getOverlayOffset();uc(e,"".concat(i,"px"),"".concat(o,"px"))}else Ms(e);let r=yt(this.clone.wtTable.TABLE);const n=ut(this.clone.wtTable.TABLE);return this.wot.wtTable.hasDefinedSize()||(r=0),e.style.height="".concat(r,"px"),e.style.width="".concat(n,"px"),!1}}class Ic extends Zn{constructor(e,r,n,i){super(e,r,n,i,hn)}}Se(Ic,tp);Se(Ic,bc);const kA=Ic;function FA(t,e,r){return(e=BA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function BA(t){var e=WA(t,"string");return typeof e=="symbol"?e:e+""}function WA(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class jA extends ci{constructor(e,r,n,i){super(e,r,hn,n,i),FA(this,"cachedFixedRowsTop",-1),this.cachedFixedRowsTop=this.wtSettings.getSetting("fixedRowsTop")}createTable(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return new kA(...r)}shouldBeRendered(){return this.wtSettings.getSetting("shouldRenderTopOverlay")}resetFixedPosition(){if(!this.needFullRender||!this.shouldBeRendered()||!this.wot.wtTable.holder.parentNode)return!1;const e=this.clone.wtTable.holder.parentNode,{rootWindow:r}=this.domBindings,n=this.wtSettings.getSetting("preventOverflow");let i=0,o=!1;if(this.trimmingContainer===r&&(!n||n!=="vertical")){const{wtTable:l}=this.wot,a=l.hider.getBoundingClientRect(),c=Math.ceil(a.bottom),d=e.offsetHeight;o=c===d,i=this.getOverlayOffset(),uc(e,"0px","".concat(i,"px"))}else i=this.getScrollPosition(),Ms(e);const s=this.adjustHeaderBordersPosition(i,o);return this.adjustElementsSize(),s}setScrollPosition(e){const{rootWindow:r}=this.domBindings,n=this.mainTableScrollableElement;let i=!1;if(n===r&&e!==r.scrollY){const o=r.scrollY;r.scrollTo(ac(r),e),i=o!==r.scrollY}else if(e!==n.scrollTop){const o=n.scrollTop;n.scrollTop=e,i=o!==n.scrollTop}return i}onScroll(){this.wtSettings.getSetting("onScrollHorizontally")}sumCellSizes(e,r){const n=this.wot.stylesHandler.getDefaultRowHeight();let i=e,o=0;for(;i<r;){const s=this.wot.wtTable.getRowHeight(i);o+=s===void 0?n:s,i+=1}return o}adjustElementsSize(){this.updateTrimmingContainer(),this.needFullRender&&(this.adjustRootElementSize(),this.adjustRootChildrenSize())}adjustRootElementSize(){const{wtTable:e,wtViewport:r}=this.wot,{rootDocument:n,rootWindow:i}=this.domBindings,s=this.clone.wtTable.holder.parentNode.style,l=this.wtSettings.getSetting("preventOverflow");if(this.trimmingContainer!==i||l==="horizontal"){let c=r.getWorkspaceWidth();r.hasVerticalScroll()&&(c-=ht(n)),c=Math.min(c,e.wtRootElement.scrollWidth),s.width="".concat(c,"px")}else s.width="";this.clone.wtTable.holder.style.width=s.width;let a=yt(this.clone.wtTable.TABLE);e.hasDefinedSize()||(a=0),s.height="".concat(a,"px")}adjustRootChildrenSize(){const{holder:e}=this.clone.wtTable,r=Ec(this.wot),n=this.wot.selectionManager.getFocusSelection()?parseInt(r.height,10)/2:0;this.clone.wtTable.hider.style.width=this.hider.style.width,e.style.width=e.parentNode.style.width,e.style.height="".concat(parseInt(e.parentNode.style.height,10)+n,"px")}applyToDOM(){const e=this.wtSettings.getSetting("totalRows");if(typeof this.wot.wtViewport.rowsRenderCalculator.startPosition=="number")this.spreader.style.top="".concat(this.wot.wtViewport.rowsRenderCalculator.startPosition,"px");else if(e===0)this.spreader.style.top="0";else throw new Error("Incorrect value of the rowsRenderCalculator");this.spreader.style.bottom="",this.needFullRender&&this.syncOverlayOffset()}syncOverlayOffset(){const e=this.isRtl()?"right":"left",{spreader:r}=this.clone.wtTable;typeof this.wot.wtViewport.columnsRenderCalculator.startPosition=="number"?r.style[e]="".concat(this.wot.wtViewport.columnsRenderCalculator.startPosition,"px"):r.style[e]=""}scrollTo(e,r){const{wot:n,wtSettings:i}=this,s=(n.cloneSource?n.cloneSource:n).wtTable.holder,l=i.getSetting("columnHeaders"),c=i.getSetting("fixedRowsTop")===0&&l.length>0&&!ee(s.parentNode,"innerBorderTop")?1:0;let d=this.getTableParentOffset(),f=0;if(r){const m=this.wot.wtTable.getRowHeight(e),p=this.wot.wtViewport.getViewportHeight();m>p&&(r=!1)}if(r&&s.offsetHeight!==s.clientHeight&&(f=ht(this.domBindings.rootDocument)),r){const m=i.getSetting("fixedRowsBottom"),p=i.getSetting("totalRows");d+=this.sumCellSizes(0,e+1),d-=n.wtViewport.getViewportHeight()-this.sumCellSizes(p-m,p),d+=1,d+=c}else d+=this.sumCellSizes(i.getSetting("fixedRowsTop"),e);return d+=f,FH(this.mainTableScrollableElement)===d-c&&c>0&&this.wot.wtOverlays.expandHiderVerticallyBy(c),this.setScrollPosition(d)}getTableParentOffset(){return this.mainTableScrollableElement===this.domBindings.rootWindow?this.wot.wtTable.holderOffset.top:0}getScrollPosition(){return cc(this.mainTableScrollableElement,this.domBindings.rootWindow)}getOverlayOffset(){const{rootWindow:e}=this.domBindings,r=this.wtSettings.getSetting("preventOverflow");let n=0;if(this.trimmingContainer===e&&(!r||r!=="vertical")){const i=this.wot.wtTable.getTotalHeight(),o=this.clone.wtTable.getTotalHeight(),s=i-o;n=Math.max(this.getScrollPosition()-this.getTableParentOffset(),0),n>s&&(n=0)}return n}adjustHeaderBordersPosition(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const{wtSettings:n}=this,i=this.wot.wtTable.holder.parentNode,o=n.getSetting("totalColumns"),s=n.getSetting("preventOverflow")==="horizontal";o?ce(i,"emptyColumns"):G(i,"emptyColumns");let l=!1;if(!r&&!s){const a=n.getSetting("fixedRowsTop"),c=this.cachedFixedRowsTop!==a,d=n.getSetting("columnHeaders");if((c||a===0)&&d.length>0){const f=ee(i,"innerBorderTop");this.cachedFixedRowsTop=n.getSetting("fixedRowsTop"),e||n.getSetting("totalRows")===0?(G(i,"innerBorderTop"),l=!f):(ce(i,"innerBorderTop"),l=f)}}return l}}function Ai(t,e,r){zA(t,e),e.set(t,r)}function zA(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Cr(t,e,r){return(e=UA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function UA(t){var e=GA(t,"string");return typeof e=="symbol"?e:e+""}function GA(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Jr(t,e,r){return t.set(rp(t,e),r),r}function br(t,e){return t.get(rp(t,e))}function rp(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var _n=new WeakMap,Ni=new WeakMap,Pn=new WeakMap,$i=new WeakMap;class KA{constructor(e,r,n,i,o,s){Cr(this,"wot",null),Ai(this,_n,[]),Cr(this,"topOverlay",null),Cr(this,"bottomOverlay",null),Cr(this,"inlineStartOverlay",null),Cr(this,"topInlineStartCornerOverlay",null),Cr(this,"bottomInlineStartCornerOverlay",null),Cr(this,"browserLineHeight",void 0),Cr(this,"wtSettings",null),Ai(this,Ni,!1),Ai(this,Pn,0),Ai(this,$i,null),Cr(this,"resizeObserver",new ResizeObserver(d=>{Wh(()=>{!Array.isArray(d)||!d.length||(Jr(Pn,this,br(Pn,this)+1),br(Pn,this)===100&&(Wr("The ResizeObserver callback was fired too many times in direct succession.\nThis may be due to an infinite loop caused by setting a dynamic height/width (for example, with the `dvh` units) to a Handsontable container's parent. \nThe observer will be disconnected."),this.resizeObserver.disconnect()),br($i,this)!==null&&clearTimeout(br($i,this)),Jr($i,this,setTimeout(()=>{Jr(Pn,this,0)},100)),this.wtSettings.getSetting("onContainerElementResize"))})})),this.wot=e,this.wtSettings=i,this.domBindings=n,this.facadeGetter=r,this.wtTable=s;const{rootDocument:l,rootWindow:a}=this.domBindings;this.instance=this.wot,this.eventManager=o,this.scrollbarSize=ht(l);const c=a.getComputedStyle(s.wtRootElement.parentNode).getPropertyValue("overflow")==="hidden";this.scrollableElement=c?s.holder:gs(s.TABLE),this.initOverlays(),this.destroyed=!1,this.keyPressed=!1,this.spreaderLastSize={width:null,height:null},this.verticalScrolling=!1,this.horizontalScrolling=!1,this.initBrowserLineHeight(),this.registerListeners(),this.lastScrollX=a.scrollX,this.lastScrollY=a.scrollY}getOverlays(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const r=[...br(_n,this)];return e&&r.push(this.wtTable),r}initBrowserLineHeight(){const{rootWindow:e,rootDocument:r}=this.domBindings,n=e.getComputedStyle(r.body),i=parseInt(n.lineHeight,10),o=parseInt(n.fontSize,10)*1.2;this.browserLineHeight=i||o}initOverlays(){const e=[this.wot,this.facadeGetter,this.wtSettings,this.domBindings];this.topOverlay=new jA(...e),this.bottomOverlay=new mA(...e),this.inlineStartOverlay=new AA(...e),this.topInlineStartCornerOverlay=new VA(...e,this.topOverlay,this.inlineStartOverlay),this.bottomInlineStartCornerOverlay=new cA(...e,this.bottomOverlay,this.inlineStartOverlay),Jr(_n,this,[this.topOverlay,this.bottomOverlay,this.inlineStartOverlay,this.topInlineStartCornerOverlay,this.bottomInlineStartCornerOverlay])}beforeDraw(){Jr(Ni,this,br(_n,this).reduce((e,r)=>r.hasRenderingStateChanged()||e,!1)),br(_n,this).forEach(e=>e.updateStateOfRendering("before"))}afterDraw(){this.syncScrollWithMaster(),br(_n,this).forEach(e=>{const r=e.hasRenderingStateChanged();e.updateStateOfRendering("after"),r&&!e.needFullRender&&e.reset()})}refreshAll(){if(this.wot.drawn){if(!this.wtTable.holder.parentNode){this.destroy();return}this.wot.draw(!0),this.verticalScrolling&&this.inlineStartOverlay.onScroll(),this.horizontalScrolling&&this.topOverlay.onScroll(),this.verticalScrolling=!1,this.horizontalScrolling=!1}}registerListeners(){const{rootDocument:e,rootWindow:r}=this.domBindings,{mainTableScrollableElement:n}=this.topOverlay,{mainTableScrollableElement:i}=this.inlineStartOverlay;this.eventManager.addEventListener(e.documentElement,"keydown",f=>this.onKeyDown(f)),this.eventManager.addEventListener(e.documentElement,"keyup",()=>this.onKeyUp()),this.eventManager.addEventListener(e,"visibilitychange",()=>this.onKeyUp()),this.eventManager.addEventListener(n,"scroll",f=>this.onTableScroll(f),{passive:!0}),n!==i&&this.eventManager.addEventListener(i,"scroll",f=>this.onTableScroll(f),{passive:!0});const o=r.devicePixelRatio&&r.devicePixelRatio>1,s=this.scrollableElement===r,l=this.wtSettings.getSetting("preventWheel"),a={passive:s};(l||o||!pI())&&this.eventManager.addEventListener(this.wtTable.wtRootElement,"wheel",f=>this.onCloneWheel(f,l),a),[this.topOverlay,this.bottomOverlay,this.inlineStartOverlay,this.topInlineStartCornerOverlay,this.bottomInlineStartCornerOverlay].forEach(f=>{this.eventManager.addEventListener(f.clone.wtTable.holder,"wheel",m=>this.onCloneWheel(m,l),a)});let d;this.eventManager.addEventListener(r,"resize",()=>{Wh(()=>{clearTimeout(d),this.wtSettings.getSetting("onWindowResize"),d=setTimeout(()=>{Jr(Pn,this,0)},200)})}),s||this.resizeObserver.observe(this.wtTable.wtRootElement.parentElement)}onTableScroll(e){const r=this.domBindings.rootWindow,n=this.inlineStartOverlay.mainTableScrollableElement,i=this.topOverlay.mainTableScrollableElement,o=e.target;this.keyPressed&&(i!==r&&o!==r&&!e.target.contains(i)||n!==r&&o!==r&&!e.target.contains(n))||this.syncScrollPositions(e)}onCloneWheel(e,r){const{rootWindow:n}=this.domBindings,i=this.inlineStartOverlay.mainTableScrollableElement,o=this.topOverlay.mainTableScrollableElement,s=e.target,l=o!==n&&s!==n&&!s.contains(o),a=i!==n&&s!==n&&!s.contains(i);if(this.keyPressed&&(l||a)||this.scrollableElement===n)return;const c=this.translateMouseWheelToScroll(e);(r||this.scrollableElement!==n&&c)&&e.preventDefault()}onKeyDown(e){this.keyPressed=TI(e.keyCode,"ARROW_UP|ARROW_RIGHT|ARROW_DOWN|ARROW_LEFT")}onKeyUp(){this.keyPressed=!1}translateMouseWheelToScroll(e){let r=isNaN(e.deltaY)?-1*e.wheelDeltaY:e.deltaY,n=isNaN(e.deltaX)?-1*e.wheelDeltaX:e.deltaX;e.deltaMode===1&&(n+=n*this.browserLineHeight,r+=r*this.browserLineHeight);const i=this.scrollVertically(r),o=this.scrollHorizontally(n);return i||o}scrollVertically(e){const r=this.scrollableElement.scrollTop;return this.scrollableElement.scrollTop+=e,r!==this.scrollableElement.scrollTop}scrollHorizontally(e){const r=this.scrollableElement.scrollLeft;return this.scrollableElement.scrollLeft+=e,r!==this.scrollableElement.scrollLeft}syncScrollPositions(){if(this.destroyed)return;const e=this.topOverlay.clone.wtTable.holder,r=this.inlineStartOverlay.clone.wtTable.holder;let n=this.scrollableElement.scrollLeft,i=this.scrollableElement.scrollTop;if(this.wot.wtViewport.isHorizontallyScrollableByWindow()&&(n=this.scrollableElement.scrollX),this.wot.wtViewport.isVerticallyScrollableByWindow()&&(i=this.scrollableElement.scrollY),this.horizontalScrolling=this.lastScrollX!==n,this.verticalScrolling=this.lastScrollY!==i,this.lastScrollX=n,this.lastScrollY=i,this.horizontalScrolling){e.scrollLeft=n;const o=this.bottomOverlay.needFullRender?this.bottomOverlay.clone.wtTable.holder:null;o&&(o.scrollLeft=n)}this.verticalScrolling&&(r.scrollTop=i),this.refreshAll()}syncScrollWithMaster(){if(!br(Ni,this))return;const e=this.topOverlay.mainTableScrollableElement,{scrollLeft:r,scrollTop:n}=e;this.topOverlay.needFullRender&&(this.topOverlay.clone.wtTable.holder.scrollLeft=r),this.bottomOverlay.needFullRender&&(this.bottomOverlay.clone.wtTable.holder.scrollLeft=r),this.inlineStartOverlay.needFullRender&&(this.inlineStartOverlay.clone.wtTable.holder.scrollTop=n),Jr(Ni,this,!1)}updateMainScrollableElements(){this.eventManager.clearEvents(!0),this.inlineStartOverlay.updateMainScrollableElement(),this.topOverlay.updateMainScrollableElement(),this.bottomOverlay.needFullRender&&this.bottomOverlay.updateMainScrollableElement();const{wtTable:e}=this,{rootWindow:r}=this.domBindings;r.getComputedStyle(e.wtRootElement.parentNode).getPropertyValue("overflow")==="hidden"?this.scrollableElement=e.holder:this.scrollableElement=gs(e.TABLE),this.registerListeners()}destroy(){this.resizeObserver.disconnect(),this.eventManager.destroy(),this.topOverlay.destroy(),this.bottomOverlay.clone&&this.bottomOverlay.destroy(),this.inlineStartOverlay.destroy(),this.topInlineStartCornerOverlay&&this.topInlineStartCornerOverlay.destroy(),this.bottomInlineStartCornerOverlay&&this.bottomInlineStartCornerOverlay.clone&&this.bottomInlineStartCornerOverlay.destroy(),this.destroyed=!0}refresh(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;this.updateLastSpreaderSize()&&this.adjustElementsSize(),this.bottomOverlay.clone&&this.bottomOverlay.refresh(e),this.inlineStartOverlay.refresh(e),this.topOverlay.refresh(e),this.topInlineStartCornerOverlay&&this.topInlineStartCornerOverlay.refresh(e),this.bottomInlineStartCornerOverlay&&this.bottomInlineStartCornerOverlay.clone&&this.bottomInlineStartCornerOverlay.refresh(e)}updateLastSpreaderSize(){const e=this.wtTable.spreader,r=e.clientWidth,n=e.clientHeight,i=r!==this.spreaderLastSize.width||n!==this.spreaderLastSize.height;return i&&(this.spreaderLastSize.width=r,this.spreaderLastSize.height=n),i}adjustElementsSize(){const{wtViewport:e}=this.wot,{wtTable:r}=this,{rootWindow:n}=this.domBindings,i=this.scrollableElement===n,o=this.wtSettings.getSetting("totalColumns"),s=this.wtSettings.getSetting("totalRows"),l=e.getRowHeaderWidth(),c=e.getColumnHeaderHeight()+this.topOverlay.sumCellSizes(0,s)+1,d=l+this.inlineStartOverlay.sumCellSizes(0,o),m=r.hider.style,p=()=>i?!1:this.scrollableElement.scrollTop>Math.max(0,c-r.holder.clientHeight),v=()=>i?!1:this.scrollableElement.scrollLeft>Math.max(0,d-r.holder.clientWidth),R=p()?1:0,H=v()?1:0;m.width="".concat(d+H,"px"),m.height="".concat(c+R,"px"),this.topOverlay.adjustElementsSize(),this.inlineStartOverlay.adjustElementsSize(),this.bottomOverlay.adjustElementsSize()}expandHiderVerticallyBy(e){const{wtTable:r}=this;r.hider.style.height="".concat(parseInt(r.hider.style.height,10)+e,"px")}expandHiderHorizontallyBy(e){const{wtTable:r}=this;r.hider.style.width="".concat(parseInt(r.hider.style.width,10)+e,"px")}applyToDOM(){this.wtTable.isVisible()&&(this.topOverlay.applyToDOM(),this.bottomOverlay.clone&&this.bottomOverlay.applyToDOM(),this.inlineStartOverlay.applyToDOM())}getParentOverlay(e){if(!e)return null;const r=[this.topOverlay,this.inlineStartOverlay,this.bottomOverlay,this.topInlineStartCornerOverlay,this.bottomInlineStartCornerOverlay];let n=null;return U(r,i=>{i&&i.clone&&i.clone.wtTable.TABLE.contains(e)&&(n=i.clone)}),n}syncOverlayTableClassNames(){const e=this.wtTable.TABLE,r=[this.topOverlay,this.inlineStartOverlay,this.bottomOverlay,this.topInlineStartCornerOverlay,this.bottomInlineStartCornerOverlay];U(r,n=>{n&&(n.clone.wtTable.TABLE.className=e.className)})}}const YA=KA;function Id(t,e,r){return(e=XA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function XA(t){var e=qA(t,"string");return typeof e=="symbol"?e:e+""}function qA(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class QA{constructor(e){Id(this,"settings",{}),Id(this,"defaults",Object.freeze(this.getDefaults())),fe(this.defaults,(r,n)=>{if(e[n]!==void 0)this.settings[n]=e[n];else{if(r===void 0)throw new Error('A required setting "'.concat(n,'" was not provided'));this.settings[n]=r}})}getDefaults(){return{facade:void 0,table:void 0,isDataViewInstance:!0,externalRowCalculator:!1,currentRowClassName:null,currentColumnClassName:null,preventOverflow(){return!1},preventWheel:!1,data:void 0,fixedColumnsStart:0,fixedRowsTop:0,fixedRowsBottom:0,shouldRenderInlineStartOverlay:()=>this.getSetting("fixedColumnsStart")>0||this.getSetting("rowHeaders").length>0,shouldRenderTopOverlay:()=>this.getSetting("fixedRowsTop")>0||this.getSetting("columnHeaders").length>0,shouldRenderBottomOverlay:()=>this.getSetting("fixedRowsBottom")>0,minSpareRows:0,rowHeaders(){return[]},columnHeaders(){return[]},totalRows:void 0,totalColumns:void 0,cellRenderer:(e,r,n)=>{const i=this.getSetting("data",e,r);si(n,i==null?"":i)},columnWidth(){},rowHeight(){},rowHeightByOverlayName(){},defaultColumnWidth:50,selections:null,hideBorderOnMouseDownOver:!1,viewportRowCalculatorOverride:null,viewportColumnCalculatorOverride:null,viewportRowRenderingThreshold:null,viewportColumnRenderingThreshold:null,onCellMouseDown:null,onCellContextMenu:null,onCellMouseOver:null,onCellMouseOut:null,onCellMouseUp:null,onCellDblClick:null,onCellCornerMouseDown:null,onCellCornerDblClick:null,beforeDraw:null,onDraw:null,onBeforeRemoveCellClassNames:null,onAfterDrawSelection:null,onBeforeDrawBorders:null,onBeforeViewportScrollHorizontally:e=>e,onBeforeViewportScrollVertically:e=>e,onScrollHorizontally:null,onScrollVertically:null,onBeforeTouchScroll:null,onAfterMomentumScroll:null,onModifyRowHeaderWidth:null,onModifyGetCellCoords:null,onModifyGetCoordsElement:null,onModifyGetCoords:null,onBeforeHighlightingRowHeader:e=>e,onBeforeHighlightingColumnHeader:e=>e,onWindowResize:null,onContainerElementResize:null,renderAllColumns:!1,renderAllRows:!1,groups:!1,rowHeaderWidth:null,columnHeaderHeight:null,headerClassName:null,rtlMode:!1,ariaTags:!0}}update(e,r){return r===void 0?fe(e,(n,i)=>{this.settings[i]=n}):this.settings[e]=r,this}getSetting(e,r,n,i,o){return typeof this.settings[e]=="function"?this.settings[e](r,n,i,o):r!==void 0&&Array.isArray(this.settings[e])?this.settings[e][r]:this.settings[e]}getSettingPure(e){return this.settings[e]}has(e){return!!this.settings[e]}}class Mc extends Zn{constructor(e,r,n,i){super(e,r,n,i,"master")}alignOverlaysWithTrimmingContainer(){const e=ms(this.wtRootElement),{rootWindow:r}=this.domBindings;if(e===r)this.wtSettings.getSetting("preventOverflow")||(this.holder.style.overflow="visible",this.wtRootElement.style.overflow="visible");else{const n=e.parentElement,i=xh(e,"height",r),o=xh(e,"overflow",r),s=this.holder.style,{scrollWidth:l,scrollHeight:a}=e;let c=e.offsetWidth,d=e.offsetHeight;if(n&&["auto","hidden","scroll"].includes(o)){const m=e.cloneNode(!1);m.style.overflow="auto",m.style.position="absolute",e.nextElementSibling?n.insertBefore(m,e.nextElementSibling):n.appendChild(m);const p=parseInt(r.getComputedStyle(m).height,10);n.removeChild(m),p===0&&(d=0)}d=Math.min(d,a),s.height=i==="auto"?"auto":"".concat(d,"px"),c=Math.min(c,l),s.width="".concat(c,"px"),s.overflow="",this.hasTableHeight=s.height==="auto"?!0:d>0,this.hasTableWidth=c>0}this.isTableVisible=Go(this.TABLE)}markOversizedColumnHeaders(){const{wtSettings:e}=this,{wtViewport:r}=this.dataAccessObject,n="master",o=e.getSetting("columnHeaders").length;if(o&&!r.hasOversizedColumnHeadersMarked[n]){const l=e.getSetting("rowHeaders").length,a=this.getRenderedColumnsCount();for(let c=0;c<o;c++)for(let d=-1*l;d<a;d++)this.markIfOversizedColumnHeader(d);r.hasOversizedColumnHeadersMarked[n]=!0}}}Se(Mc,Ym);Se(Mc,bc);const ZA=Mc;class JA{constructor(e,r,n,i,o){this.dataAccessObject=e,this.wot=e.wot,this.instance=this.wot,this.domBindings=r,this.wtSettings=n,this.wtTable=o,this.oversizedRows=[],this.oversizedColumnHeaders=[],this.hasOversizedColumnHeadersMarked={},this.clientHeight=0,this.rowHeaderWidth=NaN,this.rowsVisibleCalculator=null,this.columnsVisibleCalculator=null,this.rowsCalculatorTypes=new Map([["rendered",()=>this.wtSettings.getSetting("renderAllRows")?new l_:new f_],["fullyVisible",()=>new Qx],["partiallyVisible",()=>new Am]]),this.columnsCalculatorTypes=new Map([["rendered",()=>this.wtSettings.getSetting("renderAllColumns")?new o_:new u_],["fullyVisible",()=>new Yx],["partiallyVisible",()=>new Pm]]),this.eventManager=i,this.eventManager.addEventListener(this.domBindings.rootWindow,"resize",()=>{this.clientHeight=this.getWorkspaceHeight()})}getWorkspaceHeight(){const e=this.domBindings.rootDocument,r=this.dataAccessObject.topOverlayTrimmingContainer;let n=0;return r===this.domBindings.rootWindow?n=e.documentElement.clientHeight:n=yt(r)>0&&r.clientHeight>0?r.clientHeight:1/0,n}getViewportHeight(){let e=this.getWorkspaceHeight();if(e===1/0)return e;const r=this.getColumnHeaderHeight();return r>0&&(e-=r),e}getWorkspaceWidth(){const{rootDocument:e,rootWindow:r}=this.domBindings,n=this.dataAccessObject.inlineStartOverlayTrimmingContainer;let i;if(n===r){const o=this.wtSettings.getSetting("totalColumns");i=this.wtTable.holder.offsetWidth,this.getRowHeaderWidth()+this.sumColumnWidths(0,o)>i&&(i=e.documentElement.clientWidth)}else i=n.clientWidth;return i}getViewportWidth(){const e=this.getWorkspaceWidth();if(e===1/0)return e;const r=this.getRowHeaderWidth();return r>0?e-r:e}hasVerticalScroll(){if(this.isVerticallyScrollableByWindow()){const o=this.domBindings.rootDocument.documentElement;return o.scrollHeight>o.clientHeight}const{holder:e,hider:r}=this.wtTable,n=e.clientHeight,i=r.offsetHeight;return n<i?!0:i>this.getWorkspaceHeight()}hasHorizontalScroll(){if(this.isVerticallyScrollableByWindow()){const o=this.domBindings.rootDocument.documentElement;return o.scrollWidth>o.clientWidth}const{holder:e,hider:r}=this.wtTable,n=e.clientWidth,i=r.offsetWidth;return n<i?!0:i>this.getWorkspaceWidth()}isVerticallyScrollableByWindow(){return this.dataAccessObject.topOverlayTrimmingContainer===this.domBindings.rootWindow}isHorizontallyScrollableByWindow(){return this.dataAccessObject.inlineStartOverlayTrimmingContainer===this.domBindings.rootWindow}sumColumnWidths(e,r){let n=0,i=e;for(;i<r;)n+=this.wtTable.getColumnWidth(i),i+=1;return n}getWorkspaceOffset(){return At(this.wtTable.holder)}getColumnHeaderHeight(){return this.wtSettings.getSetting("columnHeaders").length?isNaN(this.columnHeaderHeight)&&(this.columnHeaderHeight=yt(this.wtTable.THEAD)):this.columnHeaderHeight=0,this.columnHeaderHeight}getRowHeaderWidth(){const e=this.wtSettings.getSetting("rowHeaderWidth"),r=this.wtSettings.getSetting("rowHeaders");if(e){this.rowHeaderWidth=0;for(let n=0,i=r.length;n<i;n++)this.rowHeaderWidth+=e[n]||e}if(isNaN(this.rowHeaderWidth))if(r.length){let n=this.wtTable.TABLE.querySelector("TH");this.rowHeaderWidth=0;for(let i=0,o=r.length;i<o;i++)n?(this.rowHeaderWidth+=ut(n),n=n.nextSibling):this.rowHeaderWidth+=50}else this.rowHeaderWidth=0;return this.rowHeaderWidth=this.wtSettings.getSetting("onModifyRowHeaderWidth",this.rowHeaderWidth)||this.rowHeaderWidth,this.rowHeaderWidth}createRowsCalculator(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["rendered","fullyVisible","partiallyVisible"];const{wtSettings:r,wtTable:n}=this;let i=this.getViewportHeight(),o,s;this.rowHeaderWidth=NaN;let l=this.dataAccessObject.topScrollPosition-this.dataAccessObject.topParentOffset;const a=r.getSetting("fixedRowsTop"),c=r.getSetting("fixedRowsBottom"),d=r.getSetting("totalRows");return a&&l>=0&&(s=this.dataAccessObject.topOverlay.sumCellSizes(0,a),l+=s,i-=s),c&&this.dataAccessObject.bottomOverlay.clone&&(s=this.dataAccessObject.bottomOverlay.sumCellSizes(d-c,d),i-=s),n.holder.clientHeight===n.holder.offsetHeight?o=0:o=ht(this.domBindings.rootDocument),new b_({calculationTypes:e.map(f=>[f,this.rowsCalculatorTypes.get(f)()]),viewportHeight:i,scrollOffset:l,totalRows:r.getSetting("totalRows"),defaultRowHeight:this.instance.stylesHandler.getDefaultRowHeight(),rowHeightFn:f=>n.getRowHeight(f),overrideFn:r.getSettingPure("viewportRowCalculatorOverride"),horizontalScrollbarHeight:o})}createColumnsCalculator(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["rendered","fullyVisible","partiallyVisible"];const{wtSettings:r,wtTable:n}=this;let i=this.getViewportWidth(),o=Math.abs(this.dataAccessObject.inlineStartScrollPosition)-this.dataAccessObject.inlineStartParentOffset;this.columnHeaderHeight=NaN;const s=r.getSetting("fixedColumnsStart");if(s&&o>=0){const l=this.dataAccessObject.inlineStartOverlay.sumCellSizes(0,s);o+=l,i-=l}return n.holder.clientWidth!==n.holder.offsetWidth&&(i-=ht(this.domBindings.rootDocument)),new v_({calculationTypes:e.map(l=>[l,this.columnsCalculatorTypes.get(l)()]),viewportWidth:i,scrollOffset:o,totalColumns:r.getSetting("totalColumns"),columnWidthFn:l=>n.getColumnWidth(l),overrideFn:r.getSettingPure("viewportColumnCalculatorOverride"),inlineStartOffset:this.dataAccessObject.inlineStartParentOffset})}createCalculators(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const{wtSettings:r}=this,n=this.createRowsCalculator(),i=this.createColumnsCalculator();if(e&&!r.getSetting("renderAllRows")){const o=n.getResultsFor("fullyVisible");e=this.areAllProposedVisibleRowsAlreadyRendered(o)}if(e&&!r.getSetting("renderAllColumns")){const o=i.getResultsFor("fullyVisible");e=this.areAllProposedVisibleColumnsAlreadyRendered(o)}return e||(this.rowsRenderCalculator=n.getResultsFor("rendered"),this.columnsRenderCalculator=i.getResultsFor("rendered")),this.rowsVisibleCalculator=n.getResultsFor("fullyVisible"),this.columnsVisibleCalculator=i.getResultsFor("fullyVisible"),this.rowsPartiallyVisibleCalculator=n.getResultsFor("partiallyVisible"),this.columnsPartiallyVisibleCalculator=i.getResultsFor("partiallyVisible"),e}createVisibleCalculators(){const e=this.createRowsCalculator(["fullyVisible","partiallyVisible"]),r=this.createColumnsCalculator(["fullyVisible","partiallyVisible"]);this.rowsVisibleCalculator=e.getResultsFor("fullyVisible"),this.columnsVisibleCalculator=r.getResultsFor("fullyVisible"),this.rowsPartiallyVisibleCalculator=e.getResultsFor("partiallyVisible"),this.columnsPartiallyVisibleCalculator=r.getResultsFor("partiallyVisible")}areAllProposedVisibleRowsAlreadyRendered(e){if(!this.rowsVisibleCalculator)return!1;let{startRow:r,endRow:n}=e;if(r===null&&n===null){if(!e.isVisibleInTrimmingContainer)return!0;r=this.rowsPartiallyVisibleCalculator.startRow,n=this.rowsPartiallyVisibleCalculator.endRow}const{startRow:i,endRow:o,rowStartOffset:s,rowEndOffset:l}=this.rowsRenderCalculator,a=this.wtSettings.getSetting("totalRows")-1,c=this.wtSettings.getSetting("viewportRowRenderingThreshold");return Number.isInteger(c)&&c>0?(r=Math.max(0,r-Math.min(s,c)),n=Math.min(a,n+Math.min(l,c))):c==="auto"&&(r=Math.max(0,r-Math.ceil(s/2)),n=Math.min(a,n+Math.ceil(l/2))),r<i||r===i&&r>0?!1:!(n>o||n===o&&n<a)}areAllProposedVisibleColumnsAlreadyRendered(e){if(!this.columnsVisibleCalculator)return!1;let{startColumn:r,endColumn:n}=e;if(r===null&&n===null){if(!e.isVisibleInTrimmingContainer)return!0;r=this.columnsPartiallyVisibleCalculator.startColumn,n=this.columnsPartiallyVisibleCalculator.endColumn}const{startColumn:i,endColumn:o,columnStartOffset:s,columnEndOffset:l}=this.columnsRenderCalculator,a=this.wtSettings.getSetting("totalColumns")-1,c=this.wtSettings.getSetting("viewportColumnRenderingThreshold");return Number.isInteger(c)&&c>0?(r=Math.max(0,r-Math.min(s,c)),n=Math.min(a,n+Math.min(l,c))):c==="auto"&&(r=Math.max(0,r-Math.ceil(s/2)),n=Math.min(a,n+Math.ceil(l/2))),r<i||r===i&&r>0?!1:!(n>o||n===o&&n<a)}resetHasOversizedColumnHeadersMarked(){fe(this.hasOversizedColumnHeadersMarked,(e,r,n)=>{n[r]=void 0})}}const eN=JA;function tN(t,e){np(t,e),e.add(t)}function en(t,e,r){np(t,e),e.set(t,r)}function np(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function ae(t,e){return t.get(Qt(t,e))}function qt(t,e,r){return t.set(Qt(t,e),r),r}function Qt(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}const Md=23;var ar=new WeakMap,Hr=new WeakMap,xc=new WeakMap,Na=new WeakMap,Lr=new WeakMap,Po=new WeakMap,Yo=new WeakMap,cr=new WeakSet;class rN{constructor(e){tN(this,cr),en(this,ar,void 0),en(this,Hr,void 0),en(this,xc,void 0),en(this,Na,void 0),en(this,Lr,!0),en(this,Po,{}),en(this,Yo,{}),qt(Hr,this,e.rootTable.parentElement.parentElement),qt(Na,this,e.rootDocument)}isClassicTheme(){return ae(Lr,this)}getCSSVariableValue(e){var r;if(ae(Lr,this))return null;if(ae(Po,this)["--ht-".concat(e)])return ae(Po,this)["--ht-".concat(e)];const n=(r=Qt(cr,this,sN).call(this,"--ht-".concat(e)))!==null&&r!==void 0?r:Qt(cr,this,op).call(this,"--ht-".concat(e));if(n!==null)return ae(Po,this)["--ht-".concat(e)]=n,n}getStyleForTD(e){var r;return(r=ae(Yo,this))===null||r===void 0?void 0:r.td[e]}getDefaultRowHeight(){if(ae(Lr,this))return Md;const e=Qt(cr,this,nN).call(this);return!e&&ee(ae(Hr,this),"ht-wrapper")?(Wr('The "'.concat(ae(ar,this),'" theme is enabled, but its stylesheets are missing or not imported correctly. Import the correct CSS files in order to use that theme.')),qt(Lr,this,!0),this.useTheme(),Md):e}areCellsBorderBox(){return this.getStyleForTD("box-sizing")==="border-box"}useTheme(e){if(!e){Qt(cr,this,xd).call(this),qt(Lr,this,!0),qt(ar,this,e||void 0);return}e&&e!==ae(ar,this)&&(ae(ar,this)&&Qt(cr,this,lN).call(this),qt(ar,this,e),qt(Lr,this,!1),Qt(cr,this,oN).call(this),Qt(cr,this,xd).call(this))}getThemeName(){return ae(ar,this)}removeClassNames(){ee(ae(Hr,this),ae(ar,this))&&ce(ae(Hr,this),ae(ar,this))}}function nN(){const t=this.getCSSVariableValue("line-height"),e=this.getCSSVariableValue("cell-vertical-padding"),r=Math.ceil(parseFloat(this.getStyleForTD("border-bottom-width")));return t===null||e===null||isNaN(r)?null:t+2*e+r}function oN(){ce(ae(Hr,this),/ht-theme-.*/g),G(ae(Hr,this),ae(ar,this))}function xd(){this.isClassicTheme()||qt(xc,this,getComputedStyle(ae(Hr,this)));const t=Qt(cr,this,iN).call(this,["box-sizing","border-bottom-width"]);ae(Yo,this).td={...ae(Yo,this).td,"box-sizing":t["box-sizing"],"border-bottom-width":t["border-bottom-width"]}}function iN(t){const e=ae(Na,this),r=ae(Hr,this),n=e.createElement("table"),i=e.createElement("tbody"),o=e.createElement("tr"),s=e.createElement("tr"),l=e.createElement("td");s.appendChild(l),i.appendChild(o),i.appendChild(s),n.appendChild(i),r.appendChild(n);const a=getComputedStyle(l),c={};return t.forEach(d=>{c[d]=a.getPropertyValue(d)}),r.removeChild(n),c}function sN(t){const e=Math.ceil(parseFloat(Qt(cr,this,op).call(this,t)));return Number.isNaN(e)?null:e}function op(t){const e=ae(xc,this).getPropertyValue(t);return e===""?null:e}function lN(){qt(Yo,this,{}),qt(Po,this,{}),qt(Lr,this,!0)}class aN extends Cc{constructor(e,r){super(e,new QA(r)),this.stylesHandler=new rN(this.domBindings);const n=this.wtSettings.getSetting("facade",this);this.wtTable=new ZA(this.getTableDao(),n,this.domBindings,this.wtSettings),this.wtViewport=new eN(this.getViewportDao(),this.domBindings,this.wtSettings,this.eventManager,this.wtTable),this.selectionManager=new _A(this.wtSettings.getSetting("selections")),this.wtEvent=new km(n,this.domBindings,this.wtSettings,this.eventManager,this.wtTable,this.selectionManager),this.wtOverlays=new YA(this,n,this.domBindings,this.wtSettings,this.eventManager,this.wtTable),this.exportSettingsAsClassNames(),this.findOriginalHeaders()}exportSettingsAsClassNames(){const e={rowHeaders:"htRowHeaders",columnHeaders:"htColumnHeaders"},r=[],n=[];fe(e,(i,o)=>{this.wtSettings.getSetting(o).length&&n.push(i),r.push(i)}),ce(this.wtTable.wtRootElement.parentNode,r),G(this.wtTable.wtRootElement.parentNode,n)}getOverlayByName(e){var r;if(!Um.includes(e))return null;const n=e.replace(/_([a-z])/g,i=>i[1].toUpperCase());return(r=this.wtOverlays["".concat(n,"Overlay")])!==null&&r!==void 0?r:null}getViewportDao(){const e=this;return{get wot(){return e},get topOverlayTrimmingContainer(){return e.wtOverlays.topOverlay.trimmingContainer},get inlineStartOverlayTrimmingContainer(){return e.wtOverlays.inlineStartOverlay.trimmingContainer},get topScrollPosition(){return e.wtOverlays.topOverlay.getScrollPosition()},get topParentOffset(){return e.wtOverlays.topOverlay.getTableParentOffset()},get inlineStartScrollPosition(){return e.wtOverlays.inlineStartOverlay.getScrollPosition()},get inlineStartParentOffset(){return e.wtOverlays.inlineStartOverlay.getTableParentOffset()},get topOverlay(){return e.wtOverlays.topOverlay},get inlineStartOverlay(){return e.wtOverlays.inlineStartOverlay},get bottomOverlay(){return e.wtOverlays.bottomOverlay}}}}class _c{constructor(e){e instanceof Cc?this._wot=e:this._initFromSettings(e)}_initFromSettings(e){e.facade=r=>{const n=new _c(r);return()=>n},this._wot=new aN(e.table,e)}get guid(){return this._wot.guid}get rootDocument(){return this._wot.domBindings.rootDocument}get rootWindow(){return this._wot.domBindings.rootWindow}get wtSettings(){return this._wot.wtSettings}get cloneSource(){return this._wot.cloneSource}get cloneOverlay(){return this._wot.cloneOverlay}get selectionManager(){return this._wot.selectionManager}get wtViewport(){return this._wot.wtViewport}get wtOverlays(){return this._wot.wtOverlays}get wtTable(){return this._wot.wtTable}get wtEvent(){return this._wot.wtEvent}get wtScroll(){return this._wot.wtScroll}get drawn(){return this._wot.drawn}set drawn(e){this._wot.drawn=e}get activeOverlayName(){return this._wot.activeOverlayName}get drawInterrupted(){return this._wot.drawInterrupted}set drawInterrupted(e){this._wot.drawInterrupted=e}get lastMouseOver(){return this._wot.lastMouseOver}set lastMouseOver(e){this._wot.lastMouseOver=e}get momentumScrolling(){return this._wot.momentumScrolling}set momentumScrolling(e){this._wot.momentumScrolling=e}get touchApplied(){return this._wot.touchApplied}set touchApplied(e){this._wot.touchApplied=e}get domBindings(){return this._wot.domBindings}get eventListeners(){return this._wot.eventListeners}set eventListeners(e){this._wot.eventListeners=e}get eventManager(){return this._wot.eventManager}get stylesHandler(){return this._wot.stylesHandler}createCellCoords(e,r){return this._wot.createCellCoords(e,r)}createCellRange(e,r,n){return this._wot.createCellRange(e,r,n)}draw(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return this._wot.draw(e),this}getCell(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return this._wot.getCell(e,r)}scrollViewport(e,r,n){return this._wot.scrollViewport(e,r,n)}scrollViewportHorizontally(e,r){return this._wot.scrollViewportHorizontally(e,r)}scrollViewportVertically(e,r){return this._wot.scrollViewportVertically(e,r)}getViewport(){return this._wot.getViewport()}getOverlayName(){return this._wot.cloneOverlay?this._wot.cloneOverlay.type:"master"}getOverlayByName(e){return this._wot.getOverlayByName(e)}exportSettingsAsClassNames(){return this._wot.exportSettingsAsClassNames()}update(e,r){return this._wot.wtSettings.update(e,r),this}getSetting(e,r,n,i,o){return this._wot.wtSettings.getSetting(e,r,n,i,o)}hasSetting(e){return this._wot.wtSettings.hasSetting(e)}destroy(){this._wot.destroy()}}function _d(t){let{isShiftKey:e,isLeftClick:r,isRightClick:n,coords:i,selection:o,controller:s,cellCoordsFactory:l}=t;const a=o.isSelected()?o.getSelectedRange().current():null,c=o.isSelectedByCorner(),d=o.isSelectedByRowHeader();if(o.markSource("mouse"),e&&a)i.row>=0&&i.col>=0&&!s.cell?o.setRangeEnd(i):(c||d)&&i.row>=0&&i.col>=0&&!s.cell?o.setRangeEnd(l(i.row,i.col)):c&&i.row<0&&!s.column?o.setRangeEnd(l(a.to.row,i.col)):d&&i.col<0&&!s.row?o.setRangeEnd(l(i.row,a.to.col)):(!c&&!d&&i.col<0||c&&i.col<0)&&!s.row?o.selectRows(Math.max(a.from.row,0),i.row,i.col):(!c&&!d&&i.row<0||d&&i.row<0)&&!s.column&&o.selectColumns(Math.max(a.from.col,0),i.col,i.row);else{const f=!o.inInSelection(i),m=r||n&&f;i.row<0&&i.col>=0&&!s.column?m&&o.selectColumns(i.col,i.col,i.row):i.col<0&&i.row>=0&&!s.row?m&&o.selectRows(i.row,i.row,i.col):i.col>=0&&i.row>=0&&!s.cell?m&&o.setRangeStart(i):i.col<0&&i.row<0&&o.selectAll(!0,!0,{disableHeadersHighlight:!0,focusPosition:{row:0,col:0}})}o.markEndSource()}function cN(t){let{isLeftClick:e,coords:r,selection:n,controller:i,cellCoordsFactory:o}=t;if(!e)return;const s=n.isSelectedByRowHeader(),l=n.isSelectedByColumnHeader(),a=n.tableProps.countCols(),c=n.tableProps.countRows();n.markSource("mouse"),l&&!i.column?n.setRangeEnd(o(c-1,r.col)):s&&!i.row?n.setRangeEnd(o(r.row,a-1)):i.cell||n.setRangeEnd(r),n.markEndSource()}const uN=new Map([["mousedown",_d],["mouseover",cN],["touchstart",_d]]);function Pd(t,e){let{coords:r,selection:n,controller:i,cellCoordsFactory:o}=e;uN.get(t.type)({coords:r,selection:n,controller:i,cellCoordsFactory:o,isShiftKey:t.shiftKey,isLeftClick:Rm(t)||t.type==="touchstart",isRightClick:Ha(t)})}const ip=new WeakMap,Pc=Symbol("rootInstance");function hN(t){ip.set(t,!0)}function dN(t){return t===Pc}function is(t){return ip.has(t)}function fN(t,e){sp(t,e),e.add(t)}function Nr(t,e,r){sp(t,e),e.set(t,r)}function sp(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function $r(t,e,r){return(e=gN(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function gN(t){var e=mN(t,"string");return typeof e=="symbol"?e:e+""}function mN(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ce(t,e){return t.get(Bn(t,e))}function st(t,e,r){return t.set(Bn(t,e),r),r}function Bn(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var Ul=new WeakMap,Li=new WeakMap,Di=new WeakMap,Sr=new WeakMap,Gt=new WeakMap,Gl=new WeakMap,Kl=new WeakMap,An=new WeakMap,Ao=new WeakSet;class pN{constructor(e){fN(this,Ao),$r(this,"hot",void 0),$r(this,"eventManager",void 0),$r(this,"settings",void 0),$r(this,"THEAD",void 0),$r(this,"TBODY",void 0),$r(this,"_wt",void 0),$r(this,"activeWt",void 0),Nr(this,Ul,0),Nr(this,Li,0),$r(this,"postponedAdjustElementsSize",!1),Nr(this,Di,!1),Nr(this,Sr,void 0),Nr(this,Gt,void 0),Nr(this,Gl,0),Nr(this,Kl,0),Nr(this,An,null),this.hot=e,this.eventManager=new Un(this.hot),this.settings=this.hot.getSettings(),this.createElements(),this.registerEvents(),this.initializeWalkontable()}render(){if(!this.hot.isRenderSuspended()){const e=this.hot.forceFullRender;this.hot.runHooks("beforeRender",e),this.postponedAdjustElementsSize&&(this.postponedAdjustElementsSize=!1,this.adjustElementsSize()),this._wt.draw(!e),Bn(Ao,this,vN).call(this),this.hot.runHooks("afterRender",e),this.hot.forceFullRender=!1}}adjustElementsSize(){this.hot.isRenderSuspended()?this.postponedAdjustElementsSize=!0:this._wt.wtOverlays.adjustElementsSize()}getCellAtCoords(e,r){const n=this._wt.getCell(e,r);return n<0?null:n}scrollViewport(e,r,n){return this._wt.scrollViewport(e,r,n)}scrollViewportHorizontally(e,r){return this._wt.scrollViewportHorizontally(e,r)}scrollViewportVertically(e,r){return this._wt.scrollViewportVertically(e,r)}createElements(){const{rootElement:e,rootDocument:r}=this.hot,n=e.getAttribute("style");n&&e.setAttribute("data-originalstyle",n),G(e,"handsontable"),st(Gt,this,r.createElement("TABLE")),G(Ce(Gt,this),"htCore"),this.hot.getSettings().tableClassName&&G(Ce(Gt,this),this.hot.getSettings().tableClassName),this.settings.ariaTags&&(de(Ce(Gt,this),[Vr()]),de(e,[TH(),om(-1),Is(this.hot.countCols()),_H()])),this.THEAD=r.createElement("THEAD"),Ce(Gt,this).appendChild(this.THEAD),this.TBODY=r.createElement("TBODY"),Ce(Gt,this).appendChild(this.TBODY),this.hot.table=Ce(Gt,this),this.hot.container.insertBefore(Ce(Gt,this),this.hot.container.firstChild)}registerEvents(){const{rootElement:e,rootDocument:r,selection:n,rootWindow:i}=this.hot,o=r.documentElement;this.eventManager.addEventListener(e,"mousedown",l=>{st(Di,this,!0),this.isTextSelectionAllowed(l.target)||(_h(i),l.preventDefault(),i.focus())}),this.eventManager.addEventListener(e,"mouseup",()=>{st(Di,this,!1)}),this.eventManager.addEventListener(e,"mousemove",l=>{Ce(Di,this)&&!this.isTextSelectionAllowed(l.target)&&(this.settings.fragmentSelection&&_h(i),l.preventDefault())}),this.eventManager.addEventListener(o,"keyup",l=>{n.isInProgress()&&!l.shiftKey&&n.finish()}),this.eventManager.addEventListener(o,"mouseup",l=>{n.isInProgress()&&Rm(l)&&n.finish(),st(Sr,this,!1);const a=mm(r.activeElement);os(r.activeElement)&&!a||(a||!n.isSelected()&&!n.isSelectedByAnyHeader()&&!e.contains(l.target)&&!Ha(l))&&this.hot.unlisten()}),this.eventManager.addEventListener(o,"contextmenu",l=>{n.isInProgress()&&Ha(l)&&(n.finish(),st(Sr,this,!1))}),this.eventManager.addEventListener(o,"touchend",()=>{n.isInProgress()&&n.finish(),st(Sr,this,!1)}),this.eventManager.addEventListener(o,"mousedown",l=>{const a=l.target,c=l.x||l.clientX,d=l.y||l.clientY;let f=l.target;if(Ce(Sr,this)||!e||!this.hot.view)return;const{holder:m}=this._wt.wtTable;if(f===m){const v=ht(r);if(r.elementFromPoint(c+v,d)!==m||r.elementFromPoint(c,d+v)!==m)return}else for(;f!==o;){if(f===null){if(l.isTargetWebComponent)break;return}if(f===e)return;f=f.parentNode}(typeof this.settings.outsideClickDeselects=="function"?this.settings.outsideClickDeselects(a):this.settings.outsideClickDeselects)?this.hot.deselectCell():this.hot.destroyEditor(!1,!1)});let s=fs(i);for(;s!==null;)this.eventManager.addEventListener(s.document.documentElement,"click",()=>{this.hot.unlisten()}),s=fs(s);this.eventManager.addEventListener(Ce(Gt,this),"selectstart",l=>{this.settings.fragmentSelection||os(l.target)||l.preventDefault()})}translateFromRenderableToVisualCoords(e){let{row:r,col:n}=e;return this.hot._createCellCoords(...this.translateFromRenderableToVisualIndex(r,n))}translateFromRenderableToVisualIndex(e,r){let n=e>=0?this.hot.rowIndexMapper.getVisualFromRenderableIndex(e):e,i=r>=0?this.hot.columnIndexMapper.getVisualFromRenderableIndex(r):r;return n===null&&(n=e),i===null&&(i=r),[n,i]}countRenderableIndexes(e,r){const n=Math.min(e.getNotTrimmedIndexesLength(),r),i=e.getNearestNotHiddenIndex(n-1,-1);return i===null?0:e.getRenderableFromVisualIndex(i)+1}countRenderableColumns(){return this.countRenderableIndexes(this.hot.columnIndexMapper,this.settings.maxCols)}countRenderableRows(){return this.countRenderableIndexes(this.hot.rowIndexMapper,this.settings.maxRows)}countNotHiddenRowIndexes(e,r){return this.countNotHiddenIndexes(e,r,this.hot.rowIndexMapper,this.countRenderableRows())}countNotHiddenColumnIndexes(e,r){return this.countNotHiddenIndexes(e,r,this.hot.columnIndexMapper,this.countRenderableColumns())}countNotHiddenIndexes(e,r,n,i){if(isNaN(e)||e<0)return 0;const o=n.getNearestNotHiddenIndex(e,r),s=n.getRenderableFromVisualIndex(o);if(!Number.isInteger(s))return 0;let l=0;return r<0?l=s+1:r>0&&(l=i-s),l}countNotHiddenFixedColumnsStart(){const e=this.hot.countCols(),r=Math.min(parseInt(this.settings.fixedColumnsStart,10),e)-1;return this.countNotHiddenColumnIndexes(r,-1)}countNotHiddenFixedRowsTop(){const e=this.hot.countRows(),r=Math.min(parseInt(this.settings.fixedRowsTop,10),e)-1;return this.countNotHiddenRowIndexes(r,-1)}countNotHiddenFixedRowsBottom(){const e=this.hot.countRows(),r=Math.max(e-parseInt(this.settings.fixedRowsBottom,10),0);return this.countNotHiddenRowIndexes(r,1)}countRenderableColumnsInRange(e,r){let n=0;for(let i=e;i<=r;i++)this.hot.columnIndexMapper.getRenderableFromVisualIndex(i)!==null&&(n+=1);return n}countRenderableRowsInRange(e,r){let n=0;for(let i=e;i<=r;i++)this.hot.rowIndexMapper.getRenderableFromVisualIndex(i)!==null&&(n+=1);return n}getStylesHandler(){return this._wt.stylesHandler}getDefaultRowHeight(){return this._wt.stylesHandler.getDefaultRowHeight()}addClassNameToLicenseElement(e){var r;const n=(r=this.hot.rootElement.parentNode)===null||r===void 0?void 0:r.querySelector(".hot-display-license-info");n&&G(n,e)}removeClassNameFromLicenseElement(e){var r;const n=(r=this.hot.rootElement.parentNode)===null||r===void 0?void 0:r.querySelector(".hot-display-license-info");n&&ce(n,e)}isMainTableNotFullyCoveredByOverlays(){const e=this.countNotHiddenFixedRowsTop()+this.countNotHiddenFixedRowsBottom(),r=this.countNotHiddenFixedColumnsStart();return this.hot.countRenderedRows()>e&&this.hot.countRenderedCols()>r}initializeWalkontable(){const e={ariaTags:this.settings.ariaTags,rtlMode:this.hot.isRtl(),externalRowCalculator:this.hot.getPlugin("autoRowSize")&&this.hot.getPlugin("autoRowSize").isEnabled(),table:Ce(Gt,this),isDataViewInstance:()=>is(this.hot),preventOverflow:()=>this.settings.preventOverflow,preventWheel:()=>this.settings.preventWheel,viewportColumnRenderingThreshold:()=>this.settings.viewportColumnRenderingThreshold,viewportRowRenderingThreshold:()=>this.settings.viewportRowRenderingThreshold,data:(o,s)=>this.hot.getDataAtCell(...this.translateFromRenderableToVisualIndex(o,s)),totalRows:()=>this.countRenderableRows(),totalColumns:()=>this.countRenderableColumns(),fixedColumnsStart:()=>this.countNotHiddenFixedColumnsStart(),fixedRowsTop:()=>this.countNotHiddenFixedRowsTop(),fixedRowsBottom:()=>this.countNotHiddenFixedRowsBottom(),shouldRenderInlineStartOverlay:()=>this.settings.fixedColumnsStart>0||e.rowHeaders().length>0,shouldRenderTopOverlay:()=>this.settings.fixedRowsTop>0||e.columnHeaders().length>0,shouldRenderBottomOverlay:()=>this.settings.fixedRowsBottom>0,minSpareRows:()=>this.settings.minSpareRows,renderAllRows:this.settings.renderAllRows,renderAllColumns:this.settings.renderAllColumns,rowHeaders:()=>{const o=[];return this.hot.hasRowHeaders()&&o.push((s,l)=>{const a=s>=0?this.hot.rowIndexMapper.getVisualFromRenderableIndex(s):s;this.appendRowHeader(a,l)}),this.hot.runHooks("afterGetRowHeaderRenderers",o),st(Li,this,o.length),this.hot.getSettings().ariaTags&&Bn(Ao,this,lp).call(this)===this.hot.countCols()&&Bn(Ao,this,wN).call(this,Ce(Li,this)),o},columnHeaders:()=>{const o=[];return this.hot.hasColHeaders()&&o.push((s,l)=>{const a=s>=0?this.hot.columnIndexMapper.getVisualFromRenderableIndex(s):s;this.appendColHeader(a,l)}),this.hot.runHooks("afterGetColumnHeaderRenderers",o),st(Ul,this,o.length),o},columnWidth:o=>{const s=this.hot.columnIndexMapper.getVisualFromRenderableIndex(o);return this.hot.getColWidth(s===null?o:s)},rowHeight:o=>{const s=this.hot.rowIndexMapper.getVisualFromRenderableIndex(o);return this.hot.getRowHeight(s===null?o:s)},rowHeightByOverlayName:(o,s)=>{const l=this.hot.rowIndexMapper.getVisualFromRenderableIndex(o),a=l===null?o:l;return this.hot.runHooks("modifyRowHeightByOverlayName",this.hot.getRowHeight(a),a,s)},cellRenderer:(o,s,l)=>{const[a,c]=this.translateFromRenderableToVisualIndex(o,s),d=this.hot.runHooks("modifyGetCellCoords",a,c,!1,"meta");let f=a,m=c;Array.isArray(d)&&([f,m]=d);const p=this.hot.getCellMeta(f,m),v=this.hot.colToProp(m);let R=this.hot.getDataAtRowProp(f,v);this.hot.hasHook("beforeValueRender")&&(R=this.hot.runHooks("beforeValueRender",R,p)),this.hot.runHooks("beforeRenderer",l,a,c,v,R,p),this.hot.getCellRenderer(p)(this.hot,l,a,c,v,R,p),this.hot.runHooks("afterRenderer",l,a,c,v,R,p)},selections:this.hot.selection.highlight,hideBorderOnMouseDownOver:()=>this.settings.fragmentSelection,onWindowResize:()=>{this.hot&&!this.hot.isDestroyed&&this.hot.refreshDimensions()},onContainerElementResize:()=>{this.hot&&!this.hot.isDestroyed&&Go(this.hot.rootElement)&&this.hot.refreshDimensions()},onCellMouseDown:(o,s,l,a)=>{const c=this.translateFromRenderableToVisualCoords(s),d={row:!1,column:!1,cell:!1};this.hot.listen(),this.activeWt=a,st(Sr,this,!0),st(An,this,{x:o.clientX,y:o.clientY}),this.hot.runHooks("beforeOnCellMouseDown",o,c,l,d),!sn(o)&&(Pd(o,{coords:c,selection:this.hot.selection,controller:d,cellCoordsFactory:(f,m)=>this.hot._createCellCoords(f,m)}),this.hot.runHooks("afterOnCellMouseDown",o,c,l),this.activeWt=this._wt)},onCellContextMenu:(o,s,l,a)=>{const c=this.translateFromRenderableToVisualCoords(s);this.activeWt=a,st(Sr,this,!1),this.hot.selection.isInProgress()&&this.hot.selection.finish(),this.hot.runHooks("beforeOnCellContextMenu",o,c,l),!sn(o)&&(this.hot.runHooks("afterOnCellContextMenu",o,c,l),this.activeWt=this._wt)},onCellMouseOut:(o,s,l,a)=>{const c=this.translateFromRenderableToVisualCoords(s);this.activeWt=a,this.hot.runHooks("beforeOnCellMouseOut",o,c,l),!sn(o)&&(this.hot.runHooks("afterOnCellMouseOut",o,c,l),this.activeWt=this._wt)},onCellMouseOver:(o,s,l,a)=>{const c=this.translateFromRenderableToVisualCoords(s),d={row:!1,column:!1,cell:!1};this.activeWt=a,this.hot.runHooks("beforeOnCellMouseOver",o,c,l,d),!sn(o)&&(Ce(Sr,this)&&(!Ce(An,this)||Ce(An,this).x!==o.clientX||Ce(An,this).y!==o.clientY)&&Pd(o,{coords:c,selection:this.hot.selection,controller:d,cellCoordsFactory:(f,m)=>this.hot._createCellCoords(f,m)}),this.hot.runHooks("afterOnCellMouseOver",o,c,l),this.activeWt=this._wt,st(An,this,null))},onCellMouseUp:(o,s,l,a)=>{const c=this.translateFromRenderableToVisualCoords(s);this.activeWt=a,this.hot.runHooks("beforeOnCellMouseUp",o,c,l),!(sn(o)||this.hot.isDestroyed)&&(this.hot.runHooks("afterOnCellMouseUp",o,c,l),this.activeWt=this._wt)},onCellCornerMouseDown:o=>{o.preventDefault(),this.hot.runHooks("afterOnCellCornerMouseDown",o)},onCellCornerDblClick:o=>{o.preventDefault(),this.hot.runHooks("afterOnCellCornerDblClick",o)},beforeDraw:(o,s)=>this.beforeRender(o,s),onDraw:o=>this.afterRender(o),onBeforeViewportScrollVertically:(o,s)=>{const l=this.hot.rowIndexMapper,a=o<0;let c=o;return!a&&(c=l.getVisualFromRenderableIndex(o),c===null)?o:(c=this.hot.runHooks("beforeViewportScrollVertically",c,s),this.hot.runHooks("beforeViewportScroll"),a?c:l.getRenderableFromVisualIndex(c))},onBeforeViewportScrollHorizontally:(o,s)=>{const l=this.hot.columnIndexMapper,a=o<0;let c=o;return!a&&(c=l.getVisualFromRenderableIndex(o),c===null)?o:(c=this.hot.runHooks("beforeViewportScrollHorizontally",c,s),this.hot.runHooks("beforeViewportScroll"),a?c:l.getRenderableFromVisualIndex(c))},onScrollVertically:()=>{this.hot.runHooks("afterScrollVertically"),this.hot.runHooks("afterScroll")},onScrollHorizontally:()=>{this.hot.runHooks("afterScrollHorizontally"),this.hot.runHooks("afterScroll")},onBeforeRemoveCellClassNames:()=>this.hot.runHooks("beforeRemoveCellClassNames"),onBeforeHighlightingRowHeader:(o,s,l)=>{const a=this.hot.rowIndexMapper,c=o<0;let d=o;c||(d=a.getVisualFromRenderableIndex(o));const f=this.hot.runHooks("beforeHighlightingRowHeader",d,s,l);return c?f:a.getRenderableFromVisualIndex(a.getNearestNotHiddenIndex(f,1))},onBeforeHighlightingColumnHeader:(o,s,l)=>{const a=this.hot.columnIndexMapper,c=o<0;let d=o;c||(d=a.getVisualFromRenderableIndex(o));const f=this.hot.runHooks("beforeHighlightingColumnHeader",d,s,l);return c?f:a.getRenderableFromVisualIndex(a.getNearestNotHiddenIndex(f,1))},onAfterDrawSelection:(o,s,l)=>{let a;const[c,d]=this.translateFromRenderableToVisualIndex(o,s),f=this.hot.selection.getSelectedRange();if(f.size()>0){const p=f.peekByIndex(l!=null?l:0);a=[p.from.row,p.from.col,p.to.row,p.to.col]}return this.hot.runHooks("afterDrawSelection",c,d,a,l)},onBeforeDrawBorders:(o,s)=>{const[l,a,c,d]=o,f=[this.hot.rowIndexMapper.getVisualFromRenderableIndex(l),this.hot.columnIndexMapper.getVisualFromRenderableIndex(a),this.hot.rowIndexMapper.getVisualFromRenderableIndex(c),this.hot.columnIndexMapper.getVisualFromRenderableIndex(d)];return this.hot.runHooks("beforeDrawBorders",f,s)},onBeforeTouchScroll:()=>this.hot.runHooks("beforeTouchScroll"),onAfterMomentumScroll:()=>this.hot.runHooks("afterMomentumScroll"),onModifyRowHeaderWidth:o=>this.hot.runHooks("modifyRowHeaderWidth",o),onModifyGetCellCoords:(o,s,l,a)=>{const c=this.hot.rowIndexMapper,d=this.hot.columnIndexMapper,f=s>=0?d.getVisualFromRenderableIndex(s):s,m=o>=0?c.getVisualFromRenderableIndex(o):o,p=this.hot.runHooks("modifyGetCellCoords",m,f,l,a);if(Array.isArray(p)){const[v,R,H,M]=p;return[v>=0?c.getRenderableFromVisualIndex(c.getNearestNotHiddenIndex(v,1)):v,R>=0?d.getRenderableFromVisualIndex(d.getNearestNotHiddenIndex(R,1)):R,H>=0?c.getRenderableFromVisualIndex(c.getNearestNotHiddenIndex(H,-1)):H,M>=0?d.getRenderableFromVisualIndex(d.getNearestNotHiddenIndex(M,-1)):M]}},onModifyGetCoordsElement:(o,s)=>{const l=this.hot.rowIndexMapper,a=this.hot.columnIndexMapper,c=s>=0?a.getVisualFromRenderableIndex(s):s,d=o>=0?l.getVisualFromRenderableIndex(o):o,f=this.hot.runHooks("modifyGetCoordsElement",d,c);if(Array.isArray(f)){const[m,p]=f;return[m>=0?l.getRenderableFromVisualIndex(l.getNearestNotHiddenIndex(m,1)):m,p>=0?a.getRenderableFromVisualIndex(a.getNearestNotHiddenIndex(p,1)):p]}},viewportRowCalculatorOverride:o=>{let s=this.settings.viewportRowRenderingOffset;if(s==="auto"&&this.settings.fixedRowsTop&&(s=10),s>0||s==="auto"){const l=this.countRenderableRows(),a=o.startRow,c=o.endRow;if(typeof s=="number")o.startRow=Math.max(a-s,0),o.endRow=Math.min(c+s,l-1);else if(s==="auto"){const d=Math.max(1,Math.ceil(c/l*12));o.startRow=Math.max(a-d,0),o.endRow=Math.min(c+d,l-1)}}this.hot.runHooks("afterViewportRowCalculatorOverride",o)},viewportColumnCalculatorOverride:o=>{let s=this.settings.viewportColumnRenderingOffset;if(s==="auto"&&this.settings.fixedColumnsStart&&(s=10),s>0||s==="auto"){const l=this.countRenderableColumns(),a=o.startColumn,c=o.endColumn;if(typeof s=="number"&&(o.startColumn=Math.max(a-s,0),o.endColumn=Math.min(c+s,l-1)),s==="auto"){const d=Math.max(1,Math.ceil(c/l*6));o.startColumn=Math.max(a-d,0),o.endColumn=Math.min(c+d,l-1)}}this.hot.runHooks("afterViewportColumnCalculatorOverride",o)},rowHeaderWidth:()=>this.settings.rowHeaderWidth,columnHeaderHeight:()=>{const o=this.hot.runHooks("modifyColumnHeaderHeight");return this.settings.columnHeaderHeight||o}};this.hot.runHooks("beforeInitWalkontable",e),this._wt=new _c(e),this.activeWt=this._wt;const r=this._wt.wtTable.spreader,{width:n,height:i}=this.hot.rootElement.getBoundingClientRect();this.setLastSize(n,i),this.eventManager.addEventListener(r,"mousedown",o=>{o.target===r&&o.which===3&&o.stopPropagation()}),this.eventManager.addEventListener(r,"contextmenu",o=>{o.target===r&&o.which===3&&o.stopPropagation()}),this.eventManager.addEventListener(this.hot.rootDocument.documentElement,"click",()=>{this.settings.observeDOMVisibility&&this._wt.drawInterrupted&&this.hot.render()})}isTextSelectionAllowed(e){if(os(e))return!0;const r=Ea(e,this._wt.wtTable.spreader);return!!(this.settings.fragmentSelection===!0&&r||this.settings.fragmentSelection==="cell"&&this.isSelectedOnlyCell()&&r||!this.settings.fragmentSelection&&this.isCellEdited()&&this.isSelectedOnlyCell())}isMouseDown(){return Ce(Sr,this)}isSelectedOnlyCell(){var e,r;return(e=(r=this.hot.getSelectedRangeLast())===null||r===void 0?void 0:r.isSingleCell())!==null&&e!==void 0?e:!1}isCellEdited(){const e=this.hot.getActiveEditor();return e&&e.isOpened()}beforeRender(e,r){e&&this.hot.runHooks("beforeViewRender",this.hot.forceFullRender,r)}afterRender(e){e&&this.hot.runHooks("afterViewRender",this.hot.forceFullRender)}appendRowHeader(e,r){if(r.firstChild){const n=r.firstChild;if(!ee(n,"relative")){Uo(r),this.appendRowHeader(e,r);return}this.updateCellHeader(n.querySelector(".rowHeader"),e,this.hot.getRowHeader)}else{const{rootDocument:n,getRowHeader:i}=this.hot,o=n.createElement("div"),s=n.createElement("span");o.className="relative",s.className="rowHeader",this.updateCellHeader(s,e,i),o.appendChild(s),r.appendChild(o)}this.hot.runHooks("afterGetRowHeader",e,r)}appendColHeader(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.hot.getColHeader,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;const o=()=>{const s=e>=0?this.hot.getColumnMeta(e).headerClassName:null;return s?s.split(" "):[]};if(r.firstChild){const s=r.firstChild;ee(s,"relative")?(this.updateCellHeader(s.querySelector(".colHeader"),e,n,i),s.className="",G(s,["relative",...o()])):(Uo(r),this.appendColHeader(e,r,n,i))}else{const{rootDocument:s}=this.hot,l=s.createElement("div"),a=s.createElement("span"),c=o();l.classList.add("relative",...c),a.className="colHeader",this.settings.ariaTags&&(de(l,...Vr()),de(a,...Vr())),this.updateCellHeader(a,e,n,i),l.appendChild(a),r.appendChild(l)}this.hot.runHooks("afterGetColHeader",e,r,i)}updateCellHeader(e,r,n){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,o=r;const s=this._wt.wtOverlays.getParentOverlay(e)||this._wt;e.parentNode&&(ee(e,"colHeader")?o=s.wtTable.columnFilter.sourceToRendered(r):ee(e,"rowHeader")&&(o=s.wtTable.rowFilter.sourceToRendered(r))),o>-1?VH(e,n(r,i)):(si(e,String.fromCharCode(160)),G(e,"cornerHeader"))}maximumVisibleElementWidth(e){const n=this._wt.wtViewport.getWorkspaceWidth()-e;return n>0?n:0}maximumVisibleElementHeight(e){const n=this._wt.wtViewport.getWorkspaceHeight()-e;return n>0?n:0}setLastSize(e,r){st(Gl,this,e),st(Kl,this,r)}getLastSize(){return{width:Ce(Gl,this),height:Ce(Kl,this)}}getFirstRenderedVisibleRow(){if(!this._wt.wtViewport.rowsRenderCalculator)return null;const e=this.hot.rowIndexMapper,r=e.getVisualFromRenderableIndex(this._wt.wtTable.getFirstRenderedRow());return e.getNearestNotHiddenIndex(r!=null?r:0,1)}getLastRenderedVisibleRow(){if(!this._wt.wtViewport.rowsRenderCalculator)return null;const e=this.hot.rowIndexMapper,r=e.getVisualFromRenderableIndex(this._wt.wtTable.getLastRenderedRow());return e.getNearestNotHiddenIndex(r!=null?r:this.hot.countRows()-1,-1)}getFirstRenderedVisibleColumn(){if(!this._wt.wtViewport.columnsRenderCalculator)return null;const e=this.hot.columnIndexMapper,r=e.getVisualFromRenderableIndex(this._wt.wtTable.getFirstRenderedColumn());return e.getNearestNotHiddenIndex(r!=null?r:0,1)}getLastRenderedVisibleColumn(){if(!this._wt.wtViewport.columnsRenderCalculator)return null;const e=this.hot.columnIndexMapper,r=e.getVisualFromRenderableIndex(this._wt.wtTable.getLastRenderedColumn());return e.getNearestNotHiddenIndex(r!=null?r:this.hot.countCols()-1,-1)}getFirstFullyVisibleRow(){return this.hot.rowIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getFirstVisibleRow())}getLastFullyVisibleRow(){return this.hot.rowIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getLastVisibleRow())}getFirstFullyVisibleColumn(){return this.hot.columnIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getFirstVisibleColumn())}getLastFullyVisibleColumn(){return this.hot.columnIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getLastVisibleColumn())}getFirstPartiallyVisibleRow(){return this.hot.rowIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getFirstPartiallyVisibleRow())}getLastPartiallyVisibleRow(){return this.hot.rowIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getLastPartiallyVisibleRow())}getFirstPartiallyVisibleColumn(){return this.hot.columnIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getFirstPartiallyVisibleColumn())}getLastPartiallyVisibleColumn(){return this.hot.columnIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getLastPartiallyVisibleColumn())}getColumnHeadersCount(){return Ce(Ul,this)}getRowHeadersCount(){return Ce(Li,this)}getViewportWidth(){return this._wt.wtViewport.getViewportWidth()}getWorkspaceWidth(){return this._wt.wtViewport.getWorkspaceWidth()}getViewportHeight(){return this._wt.wtViewport.getViewportHeight()}getWorkspaceHeight(){return this._wt.wtViewport.getWorkspaceHeight()}getElementOverlayName(e){var r;return((r=this._wt.wtOverlays.getParentOverlay(e))!==null&&r!==void 0?r:this._wt).wtTable.name}getOverlayByName(e){return this._wt.getOverlayByName(e)}getActiveOverlayName(){return this._wt.activeOverlayName}isVisible(){return this._wt.wtTable.isVisible()}hasVerticalScroll(){return this._wt.wtViewport.hasVerticalScroll()}hasHorizontalScroll(){return this._wt.wtViewport.hasHorizontalScroll()}getTableWidth(){return this._wt.wtTable.getWidth()}getTableHeight(){return this._wt.wtTable.getHeight()}getTableOffset(){return this._wt.wtViewport.getWorkspaceOffset()}getRowHeaderWidth(){return this._wt.wtViewport.getRowHeaderWidth()}getColumnHeaderHeight(){return this._wt.wtViewport.getColumnHeaderHeight()}isVerticallyScrollableByWindow(){return this._wt.wtViewport.isVerticallyScrollableByWindow()}isHorizontallyScrollableByWindow(){return this._wt.wtViewport.isHorizontallyScrollableByWindow()}destroy(){this._wt.destroy(),this.eventManager.destroy()}}function lp(){return parseInt(this.hot.rootElement.getAttribute(Is()[0]),10)}function wN(t){const e=Bn(Ao,this,lp).call(this)+t;de(this.hot.rootElement,...Is(e))}function vN(){const t=this.hot.rootElement;this.hasVerticalScroll()?G(t,"htHasScrollY"):ce(t,"htHasScrollY"),this.hasHorizontalScroll()?G(t,"htHasScrollX"):ce(t,"htHasScrollX")}const yN=pN;var CN=oe,bN=Ve,SN=qn,RN=bt,TN=ke,EN=jr,ON=kt,HN=Cn,Yl=HN("every",TypeError);CN({target:"Iterator",proto:!0,real:!0,forced:Yl},{every:function(e){TN(this);try{RN(e)}catch(i){ON(this,"throw",i)}if(Yl)return bN(Yl,this,e);var r=EN(this),n=0;return!SN(r,function(i,o){if(!e(i,n++))return o()},{IS_RECORD:!0,INTERRUPTED:!0}).stopped}});const IN="ABCDEFGHIJKLMNOPQRSTUVWXYZ",Ad=IN.length;function MN(t){let e=t+1,r="",n;for(;e>0;)n=(e-1)%Ad,r=String.fromCharCode(65+n)+r,e=parseInt((e-n)/Ad,10);return r}function ap(t){let e=0;return Array.isArray(t)&&(t[0]&&Array.isArray(t[0])?e=t[0].length:t[0]&&dr(t[0])&&(e=hc(t[0]))),e}function Ro(t,e,r){return(e=xN(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function xN(t){var e=_N(t,"string");return typeof e=="symbol"?e:e+""}function _N(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class PN{constructor(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];Ro(this,"hot",void 0),Ro(this,"data",void 0),Ro(this,"dataType","array"),Ro(this,"colToProp",()=>{}),Ro(this,"propToCol",()=>{}),this.hot=e,this.data=r}modifyRowData(e){let r;return this.hot.hasHook("modifyRowData")&&(r=this.hot.runHooks("modifyRowData",e)),r!==void 0&&!Number.isInteger(r)?r:this.data[e]}getData(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return!this.data||this.data.length===0?this.data:this.getByRange(null,null,e)}setData(e){this.data=e}getAtColumn(e){const r=[];return U(this.data,(n,i)=>{const o=this.getAtCell(i,e);r.push(o)}),r}getAtRow(e,r,n){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;const o=r===void 0&&n===void 0,{dataDotNotation:s}=this.hot.getSettings();let l=null,a=null;if(l=this.modifyRowData(e),Array.isArray(l))a=[],o?l.forEach((c,d)=>{a[d]=this.getAtPhysicalCell(e,d,l)}):Nt(r,n,c=>{a[c-r]=this.getAtPhysicalCell(e,c,l)});else if(dr(l)||Z(l))if(i?a=[]:a={},!o||i){const d=this.countFirstRowKeys()-1;Nt(0,d,f=>{const m=this.colToProp(f);if(f>=(r||0)&&f<=(n||d)&&!Number.isInteger(m)){const p=this.getAtPhysicalCell(e,m,l);i?a.push(p):s?Sl(a,m,p):a[m]=p}})}else fe(l,(c,d)=>{const f=this.getAtPhysicalCell(e,d,l);s?Sl(a,d,f):a[d]=f});return a}setAtCell(e,r,n){if(!(e>=this.countRows()||r>=this.countFirstRowKeys())){if(this.hot.hasHook("modifySourceData")){const i=wt(n);this.hot.runHooks("modifySourceData",e,r,i,"set"),i.isTouched()&&(n=i.value)}["__proto__","constructor","prototype"].includes(e)||(Number.isInteger(r)?this.data[e][r]=n:Sl(this.data[e],r,n))}}getAtPhysicalCell(e,r,n){let i=null;if(n)if(typeof r=="string"){const{dataDotNotation:o}=this.hot.getSettings();i=o?hI(n,r):n[r]}else typeof r=="function"?i=r(n):i=n[r];if(this.hot.hasHook("modifySourceData")){const o=wt(i);this.hot.runHooks("modifySourceData",e,r,o,"get"),o.isTouched()&&(i=o.value)}return i}getAtCell(e,r){const n=this.modifyRowData(e);return this.getAtPhysicalCell(e,this.colToProp(r),n)}getByRange(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=!1,o=null,s=null,l=null,a=null;e===null||r===null?(i=!0,o=0,l=this.countRows()-1):(o=Math.min(e.row,r.row),s=Math.min(e.col,r.col),l=Math.max(e.row,r.row),a=Math.max(e.col,r.col));const c=[];return Nt(o,l,d=>{c.push(i?this.getAtRow(d,void 0,void 0,n):this.getAtRow(d,s,a,n))}),c}countRows(){if(this.hot.hasHook("modifySourceLength")){const e=this.hot.runHooks("modifySourceLength");if(Number.isInteger(e))return e}return this.data.length}countFirstRowKeys(){return ap(this.data)}destroy(){this.data=null,this.hot=null}}const AN=PN;var NN=oe,$N=Ve,LN=qn,DN=bt,VN=ke,kN=jr,FN=kt,BN=Cn,Xl=BN("some",TypeError);NN({target:"Iterator",proto:!0,real:!0,forced:Xl},{some:function(e){VN(this);try{DN(e)}catch(i){FN(this,"throw",i)}if(Xl)return $N(Xl,this,e);var r=kN(this),n=0;return LN(r,function(i,o){if(e(i,n++))return o()},{IS_RECORD:!0,INTERRUPTED:!0}).stopped}});function Nd(t,e,r){return(e=WN(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function WN(t){var e=jN(t,"string");return typeof e=="symbol"?e:e+""}function jN(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class ui{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;Nd(this,"indexedValues",[]),Nd(this,"initValueOrFn",void 0),this.initValueOrFn=e}getValues(){return this.indexedValues}getValueAtIndex(e){const r=this.indexedValues;if(e<r.length)return r[e]}setValues(e){this.indexedValues=e.slice(),this.runLocalHooks("change")}setValueAtIndex(e,r){return e<this.indexedValues.length?(this.indexedValues[e]=r,this.runLocalHooks("change"),!0):!1}clear(){this.setDefaultValues()}getLength(){return this.getValues().length}setDefaultValues(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.indexedValues.length;this.indexedValues.length=0,Z(this.initValueOrFn)?Nt(e-1,r=>this.indexedValues.push(this.initValueOrFn(r))):Nt(e-1,()=>this.indexedValues.push(this.initValueOrFn)),this.runLocalHooks("change")}init(e){return this.setDefaultValues(e),this.runLocalHooks("init"),this}insert(){this.runLocalHooks("change")}remove(){this.runLocalHooks("change")}destroy(){this.clearLocalHooks(),this.indexedValues=null,this.initValueOrFn=null}}Se(ui,Ur);function cp(t,e,r,n){const i=r.length?r[0]:void 0;return[...t.slice(0,i),...r.map((o,s)=>Z(n)?n(o,s):n),...i===void 0?[]:t.slice(i)]}function up(t,e){return tm(t,(r,n)=>e.includes(n)===!1)}class Ac extends ui{insert(e,r){this.indexedValues=cp(this.indexedValues,e,r,this.initValueOrFn),super.insert(e,r)}remove(e){this.indexedValues=up(this.indexedValues,e),super.remove(e)}}class hp extends Ac{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;super(e)}getHiddenIndexes(){return Hs(this.getValues(),(e,r,n)=>(r&&e.push(n),e),[])}}function dp(t,e,r){return[...t.slice(0,e),...r,...t.slice(e)]}function ys(t,e){return tm(t,r=>e.includes(r)===!1)}function fp(t,e){return cn(t,r=>r-e.filter(n=>n<r).length)}function gp(t,e){const r=e[0],n=e.length;return cn(t,i=>i>=r?i+n:i)}function zN(t,e,r){return(e=UN(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function UN(t){var e=GN(t,"string");return typeof e=="symbol"?e:e+""}function GN(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class KN extends ui{constructor(){super(...arguments),zN(this,"orderOfIndexes",[])}getValues(){return this.orderOfIndexes.map(e=>this.indexedValues[e])}setValues(e){this.orderOfIndexes=[...Array(e.length).keys()],super.setValues(e)}setValueAtIndex(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.orderOfIndexes.length;return e<this.indexedValues.length?(this.indexedValues[e]=r,this.orderOfIndexes.includes(e)===!1&&this.orderOfIndexes.splice(n,0,e),this.runLocalHooks("change"),!0):!1}clearValue(e){this.orderOfIndexes=ys(this.orderOfIndexes,[e]),Z(this.initValueOrFn)?super.setValueAtIndex(e,this.initValueOrFn(e)):super.setValueAtIndex(e,this.initValueOrFn)}getLength(){return this.orderOfIndexes.length}setDefaultValues(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.indexedValues.length;this.orderOfIndexes.length=0,super.setDefaultValues(e)}insert(e,r){this.indexedValues=cp(this.indexedValues,e,r,this.initValueOrFn),this.orderOfIndexes=gp(this.orderOfIndexes,r),super.insert(e,r)}remove(e){this.indexedValues=up(this.indexedValues,e),this.orderOfIndexes=ys(this.orderOfIndexes,e),this.orderOfIndexes=fp(this.orderOfIndexes,e),super.remove(e)}getEntries(){return this.orderOfIndexes.map(e=>[e,this.getValueAtIndex(e)])}}class mp extends Ac{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;super(e)}getTrimmedIndexes(){return Hs(this.getValues(),(e,r,n)=>(r&&e.push(n),e),[])}}class YN extends ui{constructor(){super(e=>e)}insert(e,r){const n=gp(this.indexedValues,r);this.indexedValues=dp(n,e,r),super.insert(e,r)}remove(e){const r=ys(this.indexedValues,e);this.indexedValues=fp(r,e),super.remove(e)}}const $d=new Map([["hiding",hp],["index",ui],["linkedPhysicalIndexToValue",KN],["physicalIndexToValue",Ac],["trimming",mp]]);function XN(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(!$d.has(t))throw new Error('The provided map type ("'.concat(t,'") does not exist.'));return new($d.get(t))(e)}function qN(t,e,r){return(e=QN(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function QN(t){var e=ZN(t,"string");return typeof e=="symbol"?e:e+""}function ZN(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class Nc{constructor(){qN(this,"collection",new Map)}register(e,r){this.collection.has(e)===!1&&(this.collection.set(e,r),r.addLocalHook("change",()=>this.runLocalHooks("change",r)))}unregister(e){const r=this.collection.get(e);X(r)&&(r.destroy(),this.collection.delete(e),this.runLocalHooks("change",r))}unregisterAll(){this.collection.forEach((e,r)=>this.unregister(r)),this.collection.clear()}get(e){return ct(e)?Array.from(this.collection.values()):this.collection.get(e)}getLength(){return this.collection.size}removeFromEvery(e){this.collection.forEach(r=>{r.remove(e)})}insertToEvery(e,r){this.collection.forEach(n=>{n.insert(e,r)})}initEvery(e){this.collection.forEach(r=>{r.init(e)})}}Se(Nc,Ur);function ql(t,e,r){return(e=JN(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function JN(t){var e=e$(t,"string");return typeof e=="symbol"?e:e+""}function e$(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class Ld extends Nc{constructor(e,r){super(),ql(this,"mergedValuesCache",[]),ql(this,"aggregationFunction",void 0),ql(this,"fallbackValue",void 0),this.aggregationFunction=e,this.fallbackValue=r}getMergedValues(){if((arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0)===!0)return this.mergedValuesCache;if(this.getLength()===0)return[];const r=cn(this.get(),o=>o.getValues()),n=[],i=X(r[0])&&r[0].length||0;for(let o=0;o<i;o+=1){const s=[];for(let l=0;l<this.getLength();l+=1)s.push(r[l][o]);n.push(s)}return cn(n,this.aggregationFunction)}getMergedValueAtIndex(e,r){const n=this.getMergedValues(r)[e];return X(n)?n:this.fallbackValue}updateCache(){this.mergedValuesCache=this.getMergedValues(!1)}}function t$(t,e,r){r$(t,e),e.set(t,r)}function r$(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function n$(t,e,r){return t.set(pp(t,e),r),r}function o$(t,e){return t.get(pp(t,e))}function pp(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var Ql=new WeakMap;class wp{constructor(){t$(this,Ql,[])}subscribe(e){return this.addLocalHook("change",e),this._write(o$(Ql,this)),this}unsubscribe(){return this.runLocalHooks("unsubscribe"),this.clearLocalHooks(),this}_write(e){return e.length>0&&this.runLocalHooks("change",e),this}_writeInitialChanges(e){n$(Ql,this,e)}}Se(wp,Ur);function Dd(t,e){const r=[];let n=0,i=0;for(;n<t.length&&i<e.length;n++,i++)t[n]!==e[i]&&r.push({op:"replace",index:i,oldValue:t[n],newValue:e[i]});for(;n<e.length;n++)r.push({op:"insert",index:n,oldValue:void 0,newValue:e[n]});for(;i<t.length;i++)r.push({op:"remove",index:i,oldValue:t[i],newValue:void 0});return r}function To(t,e,r){i$(t,e),e.set(t,r)}function i$(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _t(t,e){return t.get(vp(t,e))}function Vi(t,e,r){return t.set(vp(t,e),r),r}function vp(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var ki=new WeakMap,Eo=new WeakMap,Fi=new WeakMap,Bi=new WeakMap,Wi=new WeakMap;class s${constructor(){let{initialIndexValue:e}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};To(this,ki,new Set),To(this,Eo,[]),To(this,Fi,[]),To(this,Bi,!1),To(this,Wi,!1),Vi(Wi,this,e!=null?e:!1)}createObserver(){const e=new wp;return _t(ki,this).add(e),e.addLocalHook("unsubscribe",()=>{_t(ki,this).delete(e)}),e._writeInitialChanges(Dd(_t(Eo,this),_t(Fi,this))),e}emit(e){let r=_t(Fi,this);(!_t(Bi,this)||_t(Eo,this).length!==e.length)&&(e.length===0?e=new Array(r.length).fill(_t(Wi,this)):Vi(Eo,this,new Array(e.length).fill(_t(Wi,this))),_t(Bi,this)||(Vi(Bi,this,!0),r=_t(Eo,this)));const n=Dd(r,e);_t(ki,this).forEach(i=>i._write(n)),Vi(Fi,this,e)}}function qe(t,e,r){return(e=l$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function l$(t){var e=a$(t,"string");return typeof e=="symbol"?e:e+""}function a$(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class $a{constructor(){qe(this,"indexesSequence",new YN),qe(this,"trimmingMapsCollection",new Ld(e=>e.some(r=>r===!0),!1)),qe(this,"hidingMapsCollection",new Ld(e=>e.some(r=>r===!0),!1)),qe(this,"variousMapsCollection",new Nc),qe(this,"hidingChangesObservable",new s$({initialIndexValue:!1})),qe(this,"notTrimmedIndexesCache",[]),qe(this,"notHiddenIndexesCache",[]),qe(this,"isBatched",!1),qe(this,"indexesSequenceChanged",!1),qe(this,"indexesChangeSource",void 0),qe(this,"trimmedIndexesChanged",!1),qe(this,"hiddenIndexesChanged",!1),qe(this,"renderablePhysicalIndexesCache",[]),qe(this,"fromPhysicalToVisualIndexesCache",new Map),qe(this,"fromVisualToRenderableIndexesCache",new Map),this.indexesSequence.addLocalHook("change",()=>{this.indexesSequenceChanged=!0,this.updateCache(),this.runLocalHooks("indexesSequenceChange",this.indexesChangeSource),this.runLocalHooks("change",this.indexesSequence,null)}),this.trimmingMapsCollection.addLocalHook("change",e=>{this.trimmedIndexesChanged=!0,this.updateCache(),this.runLocalHooks("change",e,this.trimmingMapsCollection)}),this.hidingMapsCollection.addLocalHook("change",e=>{this.hiddenIndexesChanged=!0,this.updateCache(),this.runLocalHooks("change",e,this.hidingMapsCollection)}),this.variousMapsCollection.addLocalHook("change",e=>{this.runLocalHooks("change",e,this.variousMapsCollection)})}suspendOperations(){this.isBatched=!0}resumeOperations(){this.isBatched=!1,this.updateCache()}createChangesObserver(e){if(e!=="hiding")throw new Error('Unsupported index map type "'.concat(e,'".'));return this.hidingChangesObservable.createObserver()}createAndRegisterIndexMap(e,r,n){return this.registerMap(e,XN(r,n))}registerMap(e,r){if(this.trimmingMapsCollection.get(e)||this.hidingMapsCollection.get(e)||this.variousMapsCollection.get(e))throw Error('Map with name "'.concat(e,'" has been already registered.'));r instanceof mp?this.trimmingMapsCollection.register(e,r):r instanceof hp?this.hidingMapsCollection.register(e,r):this.variousMapsCollection.register(e,r);const n=this.getNumberOfIndexes();return n>0&&r.init(n),r}unregisterMap(e){this.trimmingMapsCollection.unregister(e),this.hidingMapsCollection.unregister(e),this.variousMapsCollection.unregister(e)}unregisterAll(){this.trimmingMapsCollection.unregisterAll(),this.hidingMapsCollection.unregisterAll(),this.variousMapsCollection.unregisterAll()}getPhysicalFromVisualIndex(e){const r=this.notTrimmedIndexesCache[e];return X(r)?r:null}getPhysicalFromRenderableIndex(e){const r=this.renderablePhysicalIndexesCache[e];return X(r)?r:null}getVisualFromPhysicalIndex(e){const r=this.fromPhysicalToVisualIndexesCache.get(e);return X(r)?r:null}getVisualFromRenderableIndex(e){return this.getVisualFromPhysicalIndex(this.getPhysicalFromRenderableIndex(e))}getRenderableFromVisualIndex(e){const r=this.fromVisualToRenderableIndexesCache.get(e);return X(r)?r:null}getNearestNotHiddenIndex(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(this.getPhysicalFromVisualIndex(e)===null)return null;if(this.fromVisualToRenderableIndexesCache.has(e))return e;const o=Array.from(this.fromVisualToRenderableIndexesCache.keys());let s=-1;return r>0?s=o.findIndex(l=>l>e):s=o.reverse().findIndex(l=>l<e),s===-1?n?this.getNearestNotHiddenIndex(e,-r,!1):null:o[s]}initToLength(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getNumberOfIndexes();this.notTrimmedIndexesCache=[...new Array(e).keys()],this.notHiddenIndexesCache=[...new Array(e).keys()],this.suspendOperations(),this.indexesChangeSource="init",this.indexesSequence.init(e),this.indexesChangeSource=void 0,this.trimmingMapsCollection.initEvery(e),this.resumeOperations(),this.suspendOperations(),this.hidingMapsCollection.initEvery(e),this.variousMapsCollection.initEvery(e),this.resumeOperations(),this.runLocalHooks("init")}fitToLength(e){const r=this.getNumberOfIndexes();if(e<r){const n=[...Array(this.getNumberOfIndexes()-e).keys()].map(i=>i+e);this.removeIndexes(n)}else this.insertIndexes(r,e-r)}getIndexesSequence(){return this.indexesSequence.getValues()}setIndexesSequence(e){this.indexesChangeSource===void 0&&(this.indexesChangeSource="update"),this.indexesSequence.setValues(e),this.indexesChangeSource==="update"&&(this.indexesChangeSource=void 0)}getNotTrimmedIndexes(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0)===!0?this.notTrimmedIndexesCache:this.getIndexesSequence().filter(n=>this.isTrimmed(n)===!1)}getNotTrimmedIndexesLength(){return this.getNotTrimmedIndexes().length}getNotHiddenIndexes(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0)===!0?this.notHiddenIndexesCache:this.getIndexesSequence().filter(n=>this.isHidden(n)===!1)}getNotHiddenIndexesLength(){return this.getNotHiddenIndexes().length}getRenderableIndexes(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0)===!0?this.renderablePhysicalIndexesCache:this.getNotTrimmedIndexes().filter(n=>this.isHidden(n)===!1)}getRenderableIndexesLength(){return this.getRenderableIndexes().length}getNumberOfIndexes(){return this.getIndexesSequence().length}moveIndexes(e,r){typeof e=="number"&&(e=[e]);const n=cn(e,c=>this.getPhysicalFromVisualIndex(c)),i=this.getNotTrimmedIndexesLength(),o=e.length,s=ys(this.getIndexesSequence(),n),l=s.filter(c=>this.isTrimmed(c)===!1);let a=s.indexOf(l[l.length-1])+1;if(r+o<i){const c=l[r];a=s.indexOf(c)}this.indexesChangeSource="move",this.setIndexesSequence(dp(s,a,n)),this.indexesChangeSource=void 0}isTrimmed(e){return this.trimmingMapsCollection.getMergedValueAtIndex(e)}isHidden(e){return this.hidingMapsCollection.getMergedValueAtIndex(e)}insertIndexes(e,r){const n=this.getNotTrimmedIndexes()[e],i=X(n)?n:this.getNumberOfIndexes(),o=this.getIndexesSequence().includes(n)?this.getIndexesSequence().indexOf(n):this.getNumberOfIndexes(),s=cn(new Array(r).fill(i),(l,a)=>l+a);this.suspendOperations(),this.indexesChangeSource="insert",this.indexesSequence.insert(o,s),this.indexesChangeSource=void 0,this.trimmingMapsCollection.insertToEvery(o,s),this.hidingMapsCollection.insertToEvery(o,s),this.variousMapsCollection.insertToEvery(o,s),this.resumeOperations()}removeIndexes(e){this.suspendOperations(),this.indexesChangeSource="remove",this.indexesSequence.remove(e),this.indexesChangeSource=void 0,this.trimmingMapsCollection.removeFromEvery(e),this.hidingMapsCollection.removeFromEvery(e),this.variousMapsCollection.removeFromEvery(e),this.resumeOperations()}updateCache(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const r=this.indexesSequenceChanged||this.trimmedIndexesChanged||this.hiddenIndexesChanged;(e===!0||this.isBatched===!1&&r===!0)&&(this.trimmingMapsCollection.updateCache(),this.hidingMapsCollection.updateCache(),this.notTrimmedIndexesCache=this.getNotTrimmedIndexes(!1),this.notHiddenIndexesCache=this.getNotHiddenIndexes(!1),this.renderablePhysicalIndexesCache=this.getRenderableIndexes(!1),this.cacheFromPhysicalToVisualIndexes(),this.cacheFromVisualToRenderableIndexes(),this.hiddenIndexesChanged&&this.hidingChangesObservable.emit(this.hidingMapsCollection.getMergedValues()),this.runLocalHooks("cacheUpdated",{indexesSequenceChanged:this.indexesSequenceChanged,trimmedIndexesChanged:this.trimmedIndexesChanged,hiddenIndexesChanged:this.hiddenIndexesChanged}),this.indexesSequenceChanged=!1,this.trimmedIndexesChanged=!1,this.hiddenIndexesChanged=!1)}cacheFromPhysicalToVisualIndexes(){const e=this.getNotTrimmedIndexesLength();this.fromPhysicalToVisualIndexesCache.clear();for(let r=0;r<e;r+=1){const n=this.getPhysicalFromVisualIndex(r);this.fromPhysicalToVisualIndexesCache.set(n,r)}}cacheFromVisualToRenderableIndexes(){const e=this.getRenderableIndexesLength();this.fromVisualToRenderableIndexesCache.clear();for(let r=0;r<e;r+=1){const n=this.getPhysicalFromRenderableIndex(r),i=this.getVisualFromPhysicalIndex(n);this.fromVisualToRenderableIndexesCache.set(i,r)}}}Se($a,Ur);function c$(t,e){return fe(e,(r,n)=>{ct(t[n])&&(t[n]=r)}),t}function yp(t){const r=/^([a-zA-Z]{2})-([a-zA-Z]{2})$/.exec(t);return r?"".concat(r[1].toLowerCase(),"-").concat(r[2].toUpperCase()):t}var yf;function Cp(t){X(t)&&EI(vt(yf||(yf=ot(['Language with code "','" was not found. You should register particular language \n    before using it. Read more about this issue at: https://docs.handsontable.com/i18n/missing-language-code.'],['Language with code "','" was not found. You should register particular language\\x20\n    before using it. Read more about this issue at: https://docs.handsontable.com/i18n/missing-language-code.'])),t))}function u$(t,e){return Array.isArray(t)&&Number.isInteger(e)?t[e]:t}const{register:h$,getValues:d$}=Qn("phraseFormatters");function f$(t,e){h$(t,e)}function g$(){return d$()}f$("pluralize",u$);const bp="ContextMenu:items",B=bp,Sp="".concat(B,".noItems"),Rp="".concat(B,".insertRowAbove"),Tp="".concat(B,".insertRowBelow"),Ep="".concat(B,".insertColumnOnTheLeft"),Op="".concat(B,".insertColumnOnTheRight"),Hp="".concat(B,".removeRow"),Ip="".concat(B,".removeColumn"),Mp="".concat(B,".undo"),xp="".concat(B,".redo"),_p="".concat(B,".readOnly"),Pp="".concat(B,".clearColumn"),Ap="".concat(B,".copy"),Np="".concat(B,".copyWithHeaders"),$p="".concat(B,".copyWithGroupHeaders"),Lp="".concat(B,".copyHeadersOnly"),Dp="".concat(B,".cut"),Vp="".concat(B,".freezeColumn"),kp="".concat(B,".unfreezeColumn"),Fp="".concat(B,".mergeCells"),Bp="".concat(B,".unmergeCells"),Wp="".concat(B,".addComment"),jp="".concat(B,".editComment"),zp="".concat(B,".removeComment"),Up="".concat(B,".readOnlyComment"),Gp="".concat(B,".align"),Kp="".concat(B,".align.left"),Yp="".concat(B,".align.center"),Xp="".concat(B,".align.right"),qp="".concat(B,".align.justify"),Qp="".concat(B,".align.top"),Zp="".concat(B,".align.middle"),Jp="".concat(B,".align.bottom"),ew="".concat(B,".borders"),tw="".concat(B,".borders.top"),rw="".concat(B,".borders.right"),nw="".concat(B,".borders.bottom"),ow="".concat(B,".borders.left"),iw="".concat(B,".borders.remove"),sw="".concat(B,".nestedHeaders.insertChildRow"),lw="".concat(B,".nestedHeaders.detachFromParent"),aw="".concat(B,".hideColumn"),cw="".concat(B,".showColumn"),uw="".concat(B,".hideRow"),hw="".concat(B,".showRow"),dt="Filters:",we="".concat(dt,"conditions"),dw="".concat(we,".none"),fw="".concat(we,".isEmpty"),gw="".concat(we,".isNotEmpty"),mw="".concat(we,".isEqualTo"),pw="".concat(we,".isNotEqualTo"),ww="".concat(we,".beginsWith"),vw="".concat(we,".endsWith"),yw="".concat(we,".contains"),Cw="".concat(we,".doesNotContain"),m$="".concat(we,".byValue"),bw="".concat(we,".greaterThan"),Sw="".concat(we,".greaterThanOrEqualTo"),Rw="".concat(we,".lessThan"),Tw="".concat(we,".lessThanOrEqualTo"),Ew="".concat(we,".isBetween"),Ow="".concat(we,".isNotBetween"),Hw="".concat(we,".after"),Iw="".concat(we,".before"),Mw="".concat(we,".today"),xw="".concat(we,".tomorrow"),_w="".concat(we,".yesterday"),Pw="".concat(dt,"labels.filterByCondition"),Aw="".concat(dt,"labels.filterByValue"),Nw="".concat(dt,"labels.conjunction"),$w="".concat(dt,"labels.disjunction"),Lw="".concat(dt,"values.blankCells"),Dw="".concat(dt,"buttons.selectAll"),Vw="".concat(dt,"buttons.clear"),kw="".concat(dt,"buttons.ok"),Fw="".concat(dt,"buttons.cancel"),Bw="".concat(dt,"buttons.placeholder.search"),Ww="".concat(dt,"buttons.placeholder.value"),jw="".concat(dt,"buttons.placeholder.secondValue"),$c="CheckboxRenderer:",zw="".concat($c,"checked"),Uw="".concat($c,"unchecked"),p$=Object.freeze(Object.defineProperty({__proto__:null,CHECKBOX_CHECKED:zw,CHECKBOX_RENDERER_NAMESPACE:$c,CHECKBOX_UNCHECKED:Uw,CONTEXTMENU_ITEMS_ADD_COMMENT:Wp,CONTEXTMENU_ITEMS_ALIGNMENT:Gp,CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM:Jp,CONTEXTMENU_ITEMS_ALIGNMENT_CENTER:Yp,CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY:qp,CONTEXTMENU_ITEMS_ALIGNMENT_LEFT:Kp,CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE:Zp,CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT:Xp,CONTEXTMENU_ITEMS_ALIGNMENT_TOP:Qp,CONTEXTMENU_ITEMS_BORDERS:ew,CONTEXTMENU_ITEMS_BORDERS_BOTTOM:nw,CONTEXTMENU_ITEMS_BORDERS_LEFT:ow,CONTEXTMENU_ITEMS_BORDERS_RIGHT:rw,CONTEXTMENU_ITEMS_BORDERS_TOP:tw,CONTEXTMENU_ITEMS_CLEAR_COLUMN:Pp,CONTEXTMENU_ITEMS_COPY:Ap,CONTEXTMENU_ITEMS_COPY_COLUMN_HEADERS_ONLY:Lp,CONTEXTMENU_ITEMS_COPY_WITH_COLUMN_GROUP_HEADERS:$p,CONTEXTMENU_ITEMS_COPY_WITH_COLUMN_HEADERS:Np,CONTEXTMENU_ITEMS_CUT:Dp,CONTEXTMENU_ITEMS_EDIT_COMMENT:jp,CONTEXTMENU_ITEMS_FREEZE_COLUMN:Vp,CONTEXTMENU_ITEMS_HIDE_COLUMN:aw,CONTEXTMENU_ITEMS_HIDE_ROW:uw,CONTEXTMENU_ITEMS_INSERT_LEFT:Ep,CONTEXTMENU_ITEMS_INSERT_RIGHT:Op,CONTEXTMENU_ITEMS_MERGE_CELLS:Fp,CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD:lw,CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD:sw,CONTEXTMENU_ITEMS_NO_ITEMS:Sp,CONTEXTMENU_ITEMS_READ_ONLY:_p,CONTEXTMENU_ITEMS_READ_ONLY_COMMENT:Up,CONTEXTMENU_ITEMS_REDO:xp,CONTEXTMENU_ITEMS_REMOVE_BORDERS:iw,CONTEXTMENU_ITEMS_REMOVE_COLUMN:Ip,CONTEXTMENU_ITEMS_REMOVE_COMMENT:zp,CONTEXTMENU_ITEMS_REMOVE_ROW:Hp,CONTEXTMENU_ITEMS_ROW_ABOVE:Rp,CONTEXTMENU_ITEMS_ROW_BELOW:Tp,CONTEXTMENU_ITEMS_SHOW_COLUMN:cw,CONTEXTMENU_ITEMS_SHOW_ROW:hw,CONTEXTMENU_ITEMS_UNDO:Mp,CONTEXTMENU_ITEMS_UNFREEZE_COLUMN:kp,CONTEXTMENU_ITEMS_UNMERGE_CELLS:Bp,CONTEXT_MENU_ITEMS_NAMESPACE:bp,FILTERS_BUTTONS_CANCEL:Fw,FILTERS_BUTTONS_CLEAR:Vw,FILTERS_BUTTONS_OK:kw,FILTERS_BUTTONS_PLACEHOLDER_SEARCH:Bw,FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE:jw,FILTERS_BUTTONS_PLACEHOLDER_VALUE:Ww,FILTERS_BUTTONS_SELECT_ALL:Dw,FILTERS_CONDITIONS_AFTER:Hw,FILTERS_CONDITIONS_BEFORE:Iw,FILTERS_CONDITIONS_BEGINS_WITH:ww,FILTERS_CONDITIONS_BETWEEN:Ew,FILTERS_CONDITIONS_BY_VALUE:m$,FILTERS_CONDITIONS_CONTAINS:yw,FILTERS_CONDITIONS_EMPTY:fw,FILTERS_CONDITIONS_ENDS_WITH:vw,FILTERS_CONDITIONS_EQUAL:mw,FILTERS_CONDITIONS_GREATER_THAN:bw,FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL:Sw,FILTERS_CONDITIONS_LESS_THAN:Rw,FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL:Tw,FILTERS_CONDITIONS_NAMESPACE:we,FILTERS_CONDITIONS_NONE:dw,FILTERS_CONDITIONS_NOT_BETWEEN:Ow,FILTERS_CONDITIONS_NOT_CONTAIN:Cw,FILTERS_CONDITIONS_NOT_EMPTY:gw,FILTERS_CONDITIONS_NOT_EQUAL:pw,FILTERS_CONDITIONS_TODAY:Mw,FILTERS_CONDITIONS_TOMORROW:xw,FILTERS_CONDITIONS_YESTERDAY:_w,FILTERS_DIVS_FILTER_BY_CONDITION:Pw,FILTERS_DIVS_FILTER_BY_VALUE:Aw,FILTERS_LABELS_CONJUNCTION:Nw,FILTERS_LABELS_DISJUNCTION:$w,FILTERS_NAMESPACE:dt,FILTERS_VALUES_BLANK_CELLS:Lw},Symbol.toStringTag,{value:"Module"}));/**
 * @preserve
 * Authors: <AUTHORS>
 * Last updated: Nov 15, 2017
 *
 * Description: Definition file for English - United States language-country.
 */const w$={languageCode:"en-US",[Sp]:"No available options",[Rp]:"Insert row above",[Tp]:"Insert row below",[Ep]:"Insert column left",[Op]:"Insert column right",[Hp]:["Remove row","Remove rows"],[Ip]:["Remove column","Remove columns"],[Mp]:"Undo",[xp]:"Redo",[_p]:"Read only",[Pp]:"Clear column",[Gp]:"Alignment",[Kp]:"Left",[Yp]:"Center",[Xp]:"Right",[qp]:"Justify",[Qp]:"Top",[Zp]:"Middle",[Jp]:"Bottom",[Vp]:"Freeze column",[kp]:"Unfreeze column",[ew]:"Borders",[tw]:"Top",[rw]:"Right",[nw]:"Bottom",[ow]:"Left",[iw]:"Remove border(s)",[Wp]:"Add comment",[jp]:"Edit comment",[zp]:"Delete comment",[Up]:"Read-only comment",[Fp]:"Merge cells",[Bp]:"Unmerge cells",[Ap]:"Copy",[Np]:["Copy with header","Copy with headers"],[$p]:["Copy with group header","Copy with group headers"],[Lp]:["Copy header only","Copy headers only"],[Dp]:"Cut",[sw]:"Insert child row",[lw]:"Detach from parent",[aw]:["Hide column","Hide columns"],[cw]:["Show column","Show columns"],[uw]:["Hide row","Hide rows"],[hw]:["Show row","Show rows"],[dw]:"None",[fw]:"Is empty",[gw]:"Is not empty",[mw]:"Is equal to",[pw]:"Is not equal to",[ww]:"Begins with",[vw]:"Ends with",[yw]:"Contains",[Cw]:"Does not contain",[bw]:"Greater than",[Sw]:"Greater than or equal to",[Rw]:"Less than",[Tw]:"Less than or equal to",[Ew]:"Is between",[Ow]:"Is not between",[Hw]:"After",[Iw]:"Before",[Mw]:"Today",[xw]:"Tomorrow",[_w]:"Yesterday",[Lw]:"Blank cells",[Pw]:"Filter by condition",[Aw]:"Filter by value",[Nw]:"And",[$w]:"Or",[Dw]:"Select all",[Vw]:"Clear",[kw]:"OK",[Fw]:"Cancel",[Bw]:"Search",[Ww]:"Value",[jw]:"Second value",[zw]:"Checked",[Uw]:"Unchecked"},Gw=w$,v$=p$,La=Gw.languageCode,{register:y$,getItem:Kw,hasItem:C$,getValues:b$}=Qn("languagesDictionaries");Yw(Gw);function Yw(t,e){let r=t,n=e;return dr(t)&&(n=t,r=n.languageCode),S$(r,n),y$(r,Br(n)),Br(n)}function S$(t,e){t!==La&&c$(e,Kw(La))}function Xw(t){return Lc(t)?Br(Kw(t)):null}function Lc(t){return C$(t)}function R$(){return b$()}function qw(t,e,r){const n=Xw(t);if(n===null)return null;const i=n[e];if(ct(i))return null;const o=T$(i,r);return Array.isArray(o)?o[0]:o}function T$(t,e){let r=t;return U(g$(),n=>{r=n(t,e)}),r}function E$(t){let e=yp(t);return Lc(e)||(e=La,Cp(t)),e}function O$(t,e,r){return(e=H$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function H$(t){var e=I$(t,"string");return typeof e=="symbol"?e:e+""}function I$(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class M$ extends yA{constructor(e,r){super(e,null),O$(this,"visualCellRange",null),this.visualCellRange=r||null,this.commit()}add(e){return this.visualCellRange===null?this.visualCellRange=this.settings.createCellRange(e):this.visualCellRange.expand(e),this}clear(){return this.visualCellRange=null,super.clear()}trimToVisibleCellsRangeOnly(e){let{from:r,to:n}=e,i=this.getNearestNotHiddenCoords(r,1),o=this.getNearestNotHiddenCoords(n,-1);return i===null||o===null?null:((i.row>o.row||i.col>o.col)&&(i=r,o=n),this.settings.createCellRange(i,i,o))}getNearestNotHiddenCoords(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:r;const i=this.getNearestNotHiddenIndex(this.settings.rowIndexMapper,e.row,r);if(i===null)return null;const o=this.getNearestNotHiddenIndex(this.settings.columnIndexMapper,e.col,n);return o===null?null:this.settings.createCellCoords(i,o)}getNearestNotHiddenIndex(e,r,n){return r<0?r:e.getNearestNotHiddenIndex(r,n)}commit(){if(this.visualCellRange===null)return this;const e=this.trimToVisibleCellsRangeOnly(this.visualCellRange);return e===null?this.cellRange=null:this.cellRange=this.createRenderableCellRange(e.from,e.to),this}syncWith(e){const r=e.from.clone().normalize(),n=e.getVerticalDirection()==="N-S"?1:-1,i=e.getHorizontalDirection()==="W-E"?1:-1,o=this.settings.visualToRenderableCoords(this.visualCellRange.highlight);let s=null;if((o===null||o.col===null||o.row===null)&&(s=this.getNearestNotHiddenCoords(r,n,i)),s!==null&&e.overlaps(s)){const l=e.highlight.clone();if(l.row>=0&&(l.row=s.row),l.col>=0&&(l.col=s.col),this.cellRange===null){const a=this.settings.visualToRenderableCoords(l);this.cellRange=this.settings.createCellRange(a)}e.setHighlight(l)}return this.settings.selectionType==="focus"&&o!==null&&s===null&&e.setHighlight(this.visualCellRange.highlight),this}getCorners(){const{from:e,to:r}=this.cellRange;return[Math.min(e.row,r.row),Math.min(e.col,r.col),Math.max(e.row,r.row),Math.max(e.col,r.col)]}getVisualCorners(){const e=this.settings.renderableToVisualCoords(this.cellRange.getTopStartCorner()),r=this.settings.renderableToVisualCoords(this.cellRange.getBottomEndCorner());return[e.row,e.col,r.row,r.col]}createRenderableCellRange(e,r){const n=this.settings.visualToRenderableCoords(e),i=this.settings.visualToRenderableCoords(r);return n.row===null||n.col===null||i.row===null||i.col===null?null:this.settings.createCellRange(n,n,i)}}const Mr=M$;function Zl(t){let{activeHeaderClassName:e,...r}=t;return new Mr({className:e,...r,selectionType:CA})}function x$(t){let{areaCornerVisible:e,...r}=t;return new Mr({className:"area",createLayers:!0,border:{width:1,color:"#4b89ff",cornerVisible:e},...r,selectionType:Tc})}function _$(t){let{...e}=t;return new Mr({className:"highlight",...e,selectionType:Tc})}function P$(t){let{columnClassName:e,...r}=t;return new Mr({className:e,...r,selectionType:RA})}function A$(t){let{cellCornerVisible:e,...r}=t;return new Mr({className:"current",headerAttributes:[sm()],border:{width:2,color:"#4b89ff",cornerVisible:e},...r,selectionType:Fo})}function N$(t){let{border:e,visualCellRange:r,...n}=t;return new Mr({...e,...n,selectionType:TA},r)}function $$(t){let{...e}=t;return new Mr({className:"fill",border:{width:1,color:"#ff0000"},...e,selectionType:bA})}function Vd(t){let{headerClassName:e,...r}=t;return new Mr({className:e,...r,selectionType:Qm})}function L$(t){let{rowClassName:e,...r}=t;return new Mr({className:e,...r,selectionType:SA})}function D$(t,e){V$(t,e),e.add(t)}function V$(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function lt(t,e,r){return(e=k$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function k$(t){var e=F$(t,"string");return typeof e=="symbol"?e:e+""}function F$(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Rr(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var sr=new WeakSet;class B${constructor(e){D$(this,sr),lt(this,"options",void 0),lt(this,"layerLevel",0),lt(this,"focus",void 0),lt(this,"fill",void 0),lt(this,"layeredAreas",new Map),lt(this,"areas",new Map),lt(this,"rowHeaders",new Map),lt(this,"columnHeaders",new Map),lt(this,"activeRowHeaders",new Map),lt(this,"activeColumnHeaders",new Map),lt(this,"activeCornerHeaders",new Map),lt(this,"rowHighlights",new Map),lt(this,"columnHighlights",new Map),lt(this,"customSelections",[]),this.options=e,this.focus=A$(e),this.fill=$$(e)}isEnabledFor(e,r){let n=e;e===Fo&&(n="current");let i=this.options.disabledCellSelection(r.row,r.col);return typeof i=="string"&&(i=[i]),i===!1||Array.isArray(i)&&!i.includes(n)}useLayerLevel(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return this.layerLevel=e,this}getFocus(){return this.focus}getFill(){return this.fill}createLayeredArea(){return Rr(sr,this,Tr).call(this,this.layeredAreas,x$)}getLayeredAreas(){return[...this.layeredAreas.values()]}createArea(){return Rr(sr,this,Tr).call(this,this.areas,_$)}getAreas(){return[...this.areas.values()]}createRowHeader(){return Rr(sr,this,Tr).call(this,this.rowHeaders,Vd)}getRowHeaders(){return[...this.rowHeaders.values()]}createColumnHeader(){return Rr(sr,this,Tr).call(this,this.columnHeaders,Vd)}getColumnHeaders(){return[...this.columnHeaders.values()]}createActiveRowHeader(){return Rr(sr,this,Tr).call(this,this.activeRowHeaders,Zl)}getActiveRowHeaders(){return[...this.activeRowHeaders.values()]}createActiveColumnHeader(){return Rr(sr,this,Tr).call(this,this.activeColumnHeaders,Zl)}getActiveColumnHeaders(){return[...this.activeColumnHeaders.values()]}createActiveCornerHeader(){return Rr(sr,this,Tr).call(this,this.activeCornerHeaders,Zl)}getActiveCornerHeaders(){return[...this.activeCornerHeaders.values()]}createRowHighlight(){return Rr(sr,this,Tr).call(this,this.rowHighlights,L$)}getRowHighlights(){return[...this.rowHighlights.values()]}createColumnHighlight(){return Rr(sr,this,Tr).call(this,this.columnHighlights,P$)}getColumnHighlights(){return[...this.columnHighlights.values()]}getCustomSelections(){return[...this.customSelections.values()]}addCustomSelection(e){this.customSelections.push(N$({...this.options,...e}))}clear(){this.focus.clear(),this.fill.clear(),U(this.areas.values(),e=>void e.clear()),U(this.layeredAreas.values(),e=>void e.clear()),U(this.rowHeaders.values(),e=>void e.clear()),U(this.columnHeaders.values(),e=>void e.clear()),U(this.activeRowHeaders.values(),e=>void e.clear()),U(this.activeColumnHeaders.values(),e=>void e.clear()),U(this.activeCornerHeaders.values(),e=>void e.clear()),U(this.rowHighlights.values(),e=>void e.clear()),U(this.columnHighlights.values(),e=>void e.clear())}[Symbol.iterator](){return[this.focus,this.fill,...this.areas.values(),...this.layeredAreas.values(),...this.rowHeaders.values(),...this.columnHeaders.values(),...this.activeRowHeaders.values(),...this.activeColumnHeaders.values(),...this.activeCornerHeaders.values(),...this.rowHighlights.values(),...this.columnHighlights.values(),...this.customSelections][Symbol.iterator]()}}function Tr(t,e){const r=this.layerLevel;if(t.has(r))return t.get(r);const n=e({layerLevel:r,...this.options});return t.set(r,n),n}const W$=B$;function kd(t,e,r){return(e=j$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j$(t){var e=z$(t,"string");return typeof e=="symbol"?e:e+""}function z$(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class U${constructor(e){kd(this,"ranges",[]),kd(this,"createCellRange",void 0),this.createCellRange=e}isEmpty(){return this.size()===0}set(e){return this.clear(),this.ranges.push(this.createCellRange(e)),this}add(e){return this.ranges.push(this.createCellRange(e)),this}pop(){return this.ranges.pop(),this}current(){return this.peekByIndex(this.size()-1)}previous(){return this.peekByIndex(this.size()-2)}includes(e){return this.ranges.some(r=>r.includes(e))}clear(){return this.ranges.length=0,this}size(){return this.ranges.length}peekByIndex(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,r;return e>=0&&e<this.size()&&(r=this.ranges[e]),r}[Symbol.iterator](){return this.ranges[Symbol.iterator]()}}const G$=U$;function K$(t,e){Qw(t,e),e.add(t)}function Jl(t,e,r){Qw(t,e),e.set(t,r)}function Qw(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function q(t,e){return t.get(Ee(t,e))}function ji(t,e,r){return t.set(Ee(t,e),r),r}function Ee(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var zi=new WeakMap,ne=new WeakMap,Jt=new WeakMap,Ae=new WeakSet;class Zw{constructor(e,r){K$(this,Ae),Jl(this,zi,void 0),Jl(this,ne,void 0),Jl(this,Jt,{x:0,y:0}),ji(zi,this,e),ji(ne,this,r)}transformStart(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;const i=q(ne,this).createCellCoords(e,r);let o=q(zi,this).current().highlight;const s=q(ne,this).visualToRenderableCoords(o);let l=0,a=0;if(this.runLocalHooks("beforeTransformStart",i),s.row!==null&&s.col!==null){const{width:c,height:d}=Ee(Ae,this,Jw).call(this),{row:f,col:m}=Ee(Ae,this,Bd).call(this,o),p=q(ne,this).fixedRowsBottom(),v=q(ne,this).minSpareRows(),R=q(ne,this).minSpareCols(),H=q(ne,this).autoWrapRow(),M=q(ne,this).autoWrapCol(),b=q(ne,this).createCellCoords(f+i.row,m+i.col);if(b.row>=d){const W=wt(n&&v>0&&p===0),D=b.col+1,F=q(ne,this).createCellCoords(b.row-d,D>=c?D-c:D);this.runLocalHooks("beforeColumnWrap",W,Ee(Ae,this,Nn).call(this,F),D>=c),W.value?this.runLocalHooks("insertRowRequire",q(ne,this).countRenderableRows()):M&&b.assign(F)}else if(b.row<0){const W=wt(M),D=b.col-1,F=q(ne,this).createCellCoords(d+b.row,D<0?c+D:D);this.runLocalHooks("beforeColumnWrap",W,Ee(Ae,this,Nn).call(this,F),D<0),M&&b.assign(F)}if(b.col>=c){const W=wt(n&&R>0),D=b.row+1,F=q(ne,this).createCellCoords(D>=d?D-d:D,b.col-c);this.runLocalHooks("beforeRowWrap",W,Ee(Ae,this,Nn).call(this,F),D>=d),W.value?this.runLocalHooks("insertColRequire",q(ne,this).countRenderableColumns()):H&&b.assign(F)}else if(b.col<0){const W=wt(H),D=b.row-1,F=q(ne,this).createCellCoords(D<0?d+D:D,c+b.col);this.runLocalHooks("beforeRowWrap",W,Ee(Ae,this,Nn).call(this,F),D<0),H&&b.assign(F)}const{rowDir:k,colDir:$}=Ee(Ae,this,Fd).call(this,b);l=k,a=$,o=Ee(Ae,this,Nn).call(this,b)}return this.runLocalHooks("afterTransformStart",o,l,a),o}transformEnd(e,r){const n=q(ne,this).createCellCoords(e,r),i=q(zi,this).current(),o=q(ne,this).visualToRenderableCoords(i.highlight),s=Ee(Ae,this,ea).call(this,i.to.row,i.from.row),l=Ee(Ae,this,ta).call(this,i.to.col,i.from.col),a=i.to.clone();let c=0,d=0;if(this.runLocalHooks("beforeTransformEnd",n),o.row!==null&&o.col!==null&&s!==null&&l!==null){const{row:f,col:m}=Ee(Ae,this,Bd).call(this,i.highlight),p=q(ne,this).createCellCoords(s+n.row,l+n.col),v=i.getTopStartCorner(),R=i.getTopEndCorner(),H=i.getBottomEndCorner();if(n.col<0&&l>=m&&p.col<m){const $=p.col-m;p.col=Ee(Ae,this,ta).call(this,v.col,R.col)+$}else if(n.col>0&&l<=m&&p.col>m){const $=Ee(Ae,this,ta).call(this,R.col,v.col),W=Math.max(p.col-$,1);p.col=$+W}if(n.row<0&&s>=f&&p.row<f){const $=p.row-f;p.row=Ee(Ae,this,ea).call(this,v.row,H.row)+$}else if(n.row>0&&s<=f&&p.row>f){const $=Ee(Ae,this,ea).call(this,H.row,v.row),W=Math.max(p.row-$,1);p.row=$+W}const{rowDir:M,colDir:b}=Ee(Ae,this,Fd).call(this,p);c=M,d=b;const k=Ee(Ae,this,Nn).call(this,p);n.row===0&&n.col!==0?a.col=k.col:n.row!==0&&n.col===0?a.row=k.row:(a.row=k.row,a.col=k.col)}return this.runLocalHooks("afterTransformEnd",a,c,d),a}setOffsetSize(e){let{x:r,y:n}=e;ji(Jt,this,{x:r,y:n})}resetOffsetSize(){ji(Jt,this,{x:0,y:0})}}function Fd(t){const{width:e,height:r}=Ee(Ae,this,Jw).call(this);let n=0,i=0;return t.row<0?(n=-1,t.row=0):t.row>0&&t.row>=r&&(n=1,t.row=r-1),t.col<0?(i=-1,t.col=0):t.col>0&&t.col>=e&&(i=1,t.col=e-1),{rowDir:n,colDir:i}}function Jw(){return{width:q(Jt,this).x+q(ne,this).countRenderableColumns(),height:q(Jt,this).y+q(ne,this).countRenderableRows()}}function ea(t,e){const r=q(ne,this).findFirstNonHiddenRenderableRow(t,e);return r===null?null:q(Jt,this).y+r}function ta(t,e){const r=q(ne,this).findFirstNonHiddenRenderableColumn(t,e);return r===null?null:q(Jt,this).x+r}function Bd(t){const{row:e,col:r}=q(ne,this).visualToRenderableCoords(t);if(e===null||r===null)throw new Error("Renderable coords are not visible.");return q(ne,this).createCellCoords(q(Jt,this).y+e,q(Jt,this).x+r)}function Nn(t){const e=t.clone();return e.col=t.col-q(Jt,this).x,e.row=t.row-q(Jt,this).y,q(ne,this).renderableToVisualCoords(e)}Se(Zw,Ur);const Wd=Zw,ev=0,tv=1,rv=2,Dc=3,Y$=[Dc,rv],X$=[["number"],["number","string"],["number","undefined"],["number","string","undefined"]],ra=Symbol("root"),jd=Symbol("child");function nv(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ra;if(e!==ra&&e!==jd)throw new Error("The second argument is used internally only and cannot be overwritten.");const r=Array.isArray(t),n=e===ra;let i=ev;if(r){const o=t[0];t.length===0?i=tv:n&&o instanceof wc?i=Dc:n&&Array.isArray(o)?i=nv(o,jd):t.length>=2&&t.length<=4&&!t.some((l,a)=>!X$[a].includes(typeof l))&&(i=rv)}return i}function q$(t){let{createCellCoords:e,createCellRange:r,keepDirection:n=!1,propToCol:i}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!Y$.includes(t))throw new Error("Unsupported selection ranges schema type was provided.");return function(o){const s=t===Dc;let l=s?o.from.row:o[0],a=s?o.from.col:o[1],c=s?o.to.row:o[2],d=s?o.to.col:o[3];if(typeof i=="function"&&(typeof a=="string"&&(a=i(a)),typeof d=="string"&&(d=i(d))),ct(c)&&(c=l),ct(d)&&(d=a),!n){const p=l,v=a,R=c,H=d;l=Math.min(p,R),a=Math.min(v,H),c=Math.max(p,R),d=Math.max(v,H)}const f=e(l,a),m=e(c,d);return r(f,f,m)}}function $n(t,e,r){Q$(t,e),e.set(t,r)}function Q$(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function tn(t,e,r){return(e=Z$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Z$(t){var e=J$(t,"string");return typeof e=="symbol"?e:e+""}function J$(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function le(t,e){return t.get(ov(t,e))}function lr(t,e,r){return t.set(ov(t,e),r),r}function ov(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var Ue=new WeakMap,rn=new WeakMap,Ui=new WeakMap,Oo=new WeakMap,Gi=new WeakMap,Ho=new WeakMap,Cf;class iv{constructor(e,r){var n=this;tn(this,"settings",void 0),tn(this,"tableProps",void 0),tn(this,"inProgress",!1),tn(this,"selectedRange",new G$((i,o,s)=>this.tableProps.createCellRange(i,o,s))),tn(this,"highlight",void 0),$n(this,Ue,void 0),$n(this,rn,void 0),tn(this,"selectedByRowHeader",new Set),tn(this,"selectedByColumnHeader",new Set),$n(this,Ui,!1),$n(this,Oo,!1),$n(this,Gi,"unknown"),$n(this,Ho,-1),this.settings=e,this.tableProps=r,this.highlight=new W$({headerClassName:e.currentHeaderClassName,activeHeaderClassName:e.activeHeaderClassName,rowClassName:e.currentRowClassName,columnClassName:e.currentColClassName,cellAttributes:[sm()],rowIndexMapper:this.tableProps.rowIndexMapper,columnIndexMapper:this.tableProps.columnIndexMapper,disabledCellSelection:(i,o)=>this.tableProps.isDisabledCellSelection(i,o),cellCornerVisible:function(){return n.isCellCornerVisible(...arguments)},areaCornerVisible:function(){return n.isAreaCornerVisible(...arguments)},visualToRenderableCoords:i=>this.tableProps.visualToRenderableCoords(i),renderableToVisualCoords:i=>this.tableProps.renderableToVisualCoords(i),createCellCoords:(i,o)=>this.tableProps.createCellCoords(i,o),createCellRange:(i,o,s)=>this.tableProps.createCellRange(i,o,s)}),lr(Ue,this,new Wd(this.selectedRange,{rowIndexMapper:this.tableProps.rowIndexMapper,columnIndexMapper:this.tableProps.columnIndexMapper,countRenderableRows:()=>this.tableProps.countRenderableRows(),countRenderableColumns:()=>this.tableProps.countRenderableColumns(),visualToRenderableCoords:i=>this.tableProps.visualToRenderableCoords(i),renderableToVisualCoords:i=>this.tableProps.renderableToVisualCoords(i),findFirstNonHiddenRenderableRow:function(){return n.tableProps.findFirstNonHiddenRenderableRow(...arguments)},findFirstNonHiddenRenderableColumn:function(){return n.tableProps.findFirstNonHiddenRenderableColumn(...arguments)},createCellCoords:(i,o)=>this.tableProps.createCellCoords(i,o),fixedRowsBottom:()=>e.fixedRowsBottom,minSpareRows:()=>e.minSpareRows,minSpareCols:()=>e.minSpareCols,autoWrapRow:()=>e.autoWrapRow,autoWrapCol:()=>e.autoWrapCol})),lr(rn,this,new Wd(this.selectedRange,{rowIndexMapper:this.tableProps.rowIndexMapper,columnIndexMapper:this.tableProps.columnIndexMapper,countRenderableRows:()=>{const i=this.selectedRange.current();return this.tableProps.countRenderableRowsInRange(0,i.getOuterBottomEndCorner().row)},countRenderableColumns:()=>{const i=this.selectedRange.current();return this.tableProps.countRenderableColumnsInRange(0,i.getOuterBottomEndCorner().col)},visualToRenderableCoords:i=>this.tableProps.visualToRenderableCoords(i),renderableToVisualCoords:i=>this.tableProps.renderableToVisualCoords(i),findFirstNonHiddenRenderableRow:function(){return n.tableProps.findFirstNonHiddenRenderableRow(...arguments)},findFirstNonHiddenRenderableColumn:function(){return n.tableProps.findFirstNonHiddenRenderableColumn(...arguments)},createCellCoords:(i,o)=>this.tableProps.createCellCoords(i,o),fixedRowsBottom:()=>0,minSpareRows:()=>0,minSpareCols:()=>0,autoWrapRow:()=>!0,autoWrapCol:()=>!0})),le(Ue,this).addLocalHook("beforeTransformStart",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return n.runLocalHooks("beforeModifyTransformStart",...o)}),le(Ue,this).addLocalHook("afterTransformStart",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return n.runLocalHooks("afterModifyTransformStart",...o)}),le(Ue,this).addLocalHook("beforeTransformEnd",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return n.runLocalHooks("beforeModifyTransformEnd",...o)}),le(Ue,this).addLocalHook("afterTransformEnd",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return n.runLocalHooks("afterModifyTransformEnd",...o)}),le(Ue,this).addLocalHook("insertRowRequire",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return n.runLocalHooks("insertRowRequire",...o)}),le(Ue,this).addLocalHook("insertColRequire",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return n.runLocalHooks("insertColRequire",...o)}),le(Ue,this).addLocalHook("beforeRowWrap",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return n.runLocalHooks("beforeRowWrap",...o)}),le(Ue,this).addLocalHook("beforeColumnWrap",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return n.runLocalHooks("beforeColumnWrap",...o)}),le(rn,this).addLocalHook("beforeTransformStart",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return n.runLocalHooks("beforeModifyTransformFocus",...o)}),le(rn,this).addLocalHook("afterTransformStart",function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return n.runLocalHooks("afterModifyTransformFocus",...o)})}getSelectedRange(){return this.selectedRange}markSource(e){lr(Gi,this,e)}markEndSource(){lr(Gi,this,"unknown")}getSelectionSource(){return le(Gi,this)}setExpectedLayers(e){lr(Ho,this,e)}begin(){this.inProgress=!0}finish(){this.runLocalHooks("afterSelectionFinished",Array.from(this.selectedRange)),this.inProgress=!1,lr(Ho,this,-1)}isInProgress(){return this.inProgress}setRangeStart(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:e;const o=this.settings.selectionMode==="multiple",s=ct(r)?this.tableProps.getShortcutManager().isCtrlPressed():r,l=e.clone();lr(Ui,this,!1),this.runLocalHooks("beforeSetRangeStart".concat(n?"Only":""),l),(!o||o&&!s&&ct(r))&&this.selectedRange.clear(),this.selectedRange.add(l).current().setHighlight(i.clone()),this.getLayerLevel()===0&&(this.selectedByRowHeader.clear(),this.selectedByColumnHeader.clear()),n||this.setRangeEnd(e)}setRangeStartOnly(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e;this.setRangeStart(e,r,!0,n)}setRangeEnd(e){if(this.selectedRange.isEmpty())return;const r=e.clone(),n=this.tableProps.countRows(),i=this.tableProps.countCols(),o=this.selectedRange.current().clone().setTo(e).isSingleHeader();if((n>0||i>0)&&(n===0&&r.col<0&&!o||i===0&&r.row<0&&!o))return;this.runLocalHooks("beforeSetRangeEnd",r),this.begin();const s=this.selectedRange.current();if(this.settings.navigableHeaders||s.highlight.normalize(),this.settings.selectionMode==="single")s.setFrom(s.highlight),s.setTo(s.highlight);else{const a=s.getHorizontalDirection(),c=s.getVerticalDirection(),d=this.isMultiple();s.setTo(r),d&&(a!==s.getHorizontalDirection()||s.getWidth()===1&&!s.includes(s.highlight))&&s.from.assign({col:s.highlight.col}),d&&(c!==s.getVerticalDirection()||s.getHeight()===1&&!s.includes(s.highlight))&&s.from.assign({row:s.highlight.row})}n>0&&i>0&&(!this.settings.navigableHeaders||this.settings.navigableHeaders&&!s.isSingleHeader())&&s.to.normalize(),this.runLocalHooks("beforeHighlightSet"),this.setRangeFocus(this.selectedRange.current().highlight),this.applyAndCommit();const l=le(Ho,this)===-1||this.selectedRange.size()===le(Ho,this);this.runLocalHooks("afterSetRangeEnd",e,l)}applyAndCommit(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectedRange.current(),r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLayerLevel();const n=this.tableProps.countRows(),i=this.tableProps.countCols();r<this.highlight.layerLevel&&(U(this.highlight.getAreas(),v=>void v.clear()),U(this.highlight.getLayeredAreas(),v=>void v.clear()),U(this.highlight.getRowHeaders(),v=>void v.clear()),U(this.highlight.getColumnHeaders(),v=>void v.clear()),U(this.highlight.getActiveRowHeaders(),v=>void v.clear()),U(this.highlight.getActiveColumnHeaders(),v=>void v.clear()),U(this.highlight.getActiveCornerHeaders(),v=>void v.clear()),U(this.highlight.getRowHighlights(),v=>void v.clear()),U(this.highlight.getColumnHighlights(),v=>void v.clear())),this.highlight.useLayerLevel(r);const o=this.highlight.createArea(),s=this.highlight.createLayeredArea(),l=this.highlight.createRowHeader(),a=this.highlight.createColumnHeader(),c=this.highlight.createActiveRowHeader(),d=this.highlight.createActiveColumnHeader(),f=this.highlight.createActiveCornerHeader(),m=this.highlight.createRowHighlight(),p=this.highlight.createColumnHighlight();if(o.clear(),s.clear(),l.clear(),a.clear(),c.clear(),d.clear(),f.clear(),m.clear(),p.clear(),this.highlight.isEnabledFor(Tc,e.highlight)&&(this.isMultiple()||r>=1)&&(o.add(e.from).add(e.to).commit(),s.add(e.from).add(e.to).commit(),r===1)){const v=this.selectedRange.previous();this.highlight.useLayerLevel(r-1),this.highlight.createArea().add(v.from).commit().syncWith(v),this.highlight.createLayeredArea().add(v.from).commit().syncWith(v),this.highlight.useLayerLevel(r)}if(this.highlight.isEnabledFor(Qm,e.highlight)){if(!e.isSingleHeader()){const H=this.tableProps.createCellCoords(Math.max(e.from.row,0),-1),M=this.tableProps.createCellCoords(e.to.row,-1),b=this.tableProps.createCellCoords(-1,Math.max(e.from.col,0)),k=this.tableProps.createCellCoords(-1,e.to.col);this.settings.selectionMode==="single"?(l.add(H).commit(),a.add(b).commit(),m.add(H).commit(),p.add(b).commit()):(l.add(H).add(M).commit(),a.add(b).add(k).commit(),m.add(H).add(M).commit(),p.add(b).add(k).commit())}const v=!le(Oo,this)&&this.isEntireRowSelected()&&(i>0&&i===e.getWidth()||i===0&&this.isSelectedByRowHeader()),R=!le(Oo,this)&&this.isEntireColumnSelected()&&(n>0&&n===e.getHeight()||n===0&&this.isSelectedByColumnHeader());v&&c.add(this.tableProps.createCellCoords(Math.max(e.from.row,0),Math.min(-this.tableProps.countRowHeaders(),-1))).add(this.tableProps.createCellCoords(Math.max(e.to.row,0),-1)).commit(),R&&d.add(this.tableProps.createCellCoords(Math.min(-this.tableProps.countColHeaders(),-1),Math.max(e.from.col,0))).add(this.tableProps.createCellCoords(-1,Math.max(e.to.col,0))).commit(),v&&R&&f.add(this.tableProps.createCellCoords(-this.tableProps.countColHeaders(),-this.tableProps.countRowHeaders())).add(this.tableProps.createCellCoords(-1,-1)).commit()}}setRangeFocus(e){if(this.selectedRange.isEmpty())return;const r=this.selectedRange.current();this.inProgress||this.runLocalHooks("beforeSetFocus",e);const n=this.highlight.getFocus();n.clear(),r.setHighlight(e),this.inProgress||this.runLocalHooks("beforeHighlightSet"),this.highlight.isEnabledFor(Fo,r.highlight)&&n.add(r.highlight).commit().syncWith(r),this.inProgress||(lr(Ui,this,!0),this.runLocalHooks("afterSetFocus",r.highlight))}transformStart(e,r){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;this.settings.navigableHeaders?le(Ue,this).setOffsetSize({x:this.tableProps.countRowHeaders(),y:this.tableProps.countColHeaders()}):le(Ue,this).resetOffsetSize(),this.setRangeStart(le(Ue,this).transformStart(e,r,n))}transformEnd(e,r){this.settings.navigableHeaders?le(Ue,this).setOffsetSize({x:this.tableProps.countRowHeaders(),y:this.tableProps.countColHeaders()}):le(Ue,this).resetOffsetSize(),this.setRangeEnd(le(Ue,this).transformEnd(e,r))}transformFocus(e,r){const n=this.selectedRange.current(),{row:i,col:o}=n.getOuterTopStartCorner(),s=this.tableProps.countRenderableColumnsInRange(0,o-1),l=this.tableProps.countRenderableRowsInRange(0,i-1);n.highlight.isHeader()?le(rn,this).setOffsetSize({x:o<0?Math.abs(o):-s,y:i<0?Math.abs(i):-l}):le(rn,this).setOffsetSize({x:o<0?0:-s,y:i<0?0:-l});const a=le(rn,this).transformStart(e,r);this.setRangeFocus(a.normalize())}shiftRows(e,r){if(!this.isSelected())return;const n=this.selectedRange.current();if(this.isSelectedByCorner())this.selectAll(!0,!0,{disableHeadersHighlight:!0});else if(this.isSelectedByColumnHeader()||n.getOuterTopStartCorner().row>=e){const{from:i,to:o,highlight:s}=n,l=this.tableProps.countRows(),a=this.isSelectedByRowHeader(),c=this.isSelectedByColumnHeader(),d=c?-1:0,f=c?0:r;this.getSelectedRange().pop();const m=this.tableProps.createCellCoords(pe(i.row+f,d,l-1),i.col),p=this.tableProps.createCellCoords(pe(o.row+r,d,l-1),o.col);this.markSource("shift"),s.row>=e?this.setRangeStartOnly(m,!0,this.tableProps.createCellCoords(pe(s.row+r,0,l-1),s.col)):this.setRangeStartOnly(m,!0),a&&this.selectedByRowHeader.add(this.getLayerLevel()),c&&this.selectedByColumnHeader.add(this.getLayerLevel()),this.setRangeEnd(p),this.markEndSource()}}shiftColumns(e,r){if(!this.isSelected())return;const n=this.selectedRange.current();if(this.isSelectedByCorner())this.selectAll(!0,!0,{disableHeadersHighlight:!0});else if(this.isSelectedByRowHeader()||n.getOuterTopStartCorner().col>=e){const{from:i,to:o,highlight:s}=n,l=this.tableProps.countCols(),a=this.isSelectedByRowHeader(),c=this.isSelectedByColumnHeader(),d=a?-1:0,f=a?0:r;this.getSelectedRange().pop();const m=this.tableProps.createCellCoords(i.row,pe(i.col+f,d,l-1)),p=this.tableProps.createCellCoords(o.row,pe(o.col+r,d,l-1));this.markSource("shift"),s.col>=e?this.setRangeStartOnly(m,!0,this.tableProps.createCellCoords(s.row,pe(s.col+r,0,l-1))):this.setRangeStartOnly(m,!0),a&&this.selectedByRowHeader.add(this.getLayerLevel()),c&&this.selectedByColumnHeader.add(this.getLayerLevel()),this.setRangeEnd(p),this.markEndSource()}}getLayerLevel(){return this.selectedRange.size()-1}isSelected(){return!this.selectedRange.isEmpty()}isMultiple(){if(!this.isSelected())return!1;const e=wt(!this.selectedRange.current().isSingle());return this.runLocalHooks("afterIsMultipleSelection",e),e.value}isFocusSelectionChanged(){return this.isSelected()&&le(Ui,this)}isSelectedByRowHeader(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getLayerLevel();return!this.isSelectedByCorner(e)&&(e===-1?this.selectedByRowHeader.size>0:this.selectedByRowHeader.has(e))}isEntireRowSelected(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getLayerLevel();const r=i=>{const{col:o}=i.getOuterTopStartCorner(),s=this.tableProps.countRowHeaders(),l=this.tableProps.countCols();return(s>0&&o<0||s===0)&&i.getWidth()===l};if(e===-1)return Array.from(this.selectedRange).some(i=>r(i));const n=this.selectedRange.peekByIndex(e);return n?r(n):!1}isSelectedByColumnHeader(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getLayerLevel();return!this.isSelectedByCorner()&&(e===-1?this.selectedByColumnHeader.size>0:this.selectedByColumnHeader.has(e))}isEntireColumnSelected(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getLayerLevel();const r=i=>{const{row:o}=i.getOuterTopStartCorner(),s=this.tableProps.countColHeaders(),l=this.tableProps.countRows();return(s>0&&o<0||s===0)&&i.getHeight()===l};if(e===-1)return Array.from(this.selectedRange).some(i=>r(i));const n=this.selectedRange.peekByIndex(e);return n?r(n):!1}isSelectedByAnyHeader(){return this.isSelectedByRowHeader(-1)||this.isSelectedByColumnHeader(-1)||this.isSelectedByCorner()}isSelectedByCorner(){return this.selectedByColumnHeader.has(this.getLayerLevel())&&this.selectedByRowHeader.has(this.getLayerLevel())}inInSelection(e){return this.selectedRange.includes(e)}isCellCornerVisible(){return this.settings.fillHandle&&!this.tableProps.isEditorOpened()&&!this.isMultiple()}isCellVisible(e){const r=this.tableProps.visualToRenderableCoords(e);return r.row!==null&&r.col!==null}isAreaCornerVisible(e){return Number.isInteger(e)&&e!==this.getLayerLevel()?!1:this.settings.fillHandle&&!this.tableProps.isEditorOpened()&&this.isMultiple()}clear(){this.selectedRange.clear(),this.highlight.clear()}deselect(){this.isSelected()&&(this.inProgress=!1,this.clear(),this.runLocalHooks("afterDeselect"))}selectAll(){var e;let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{focusPosition:!1,disableHeadersHighlight:!1};const o=this.tableProps.countRows(),s=this.tableProps.countCols(),l=this.tableProps.countRowHeaders(),a=this.tableProps.countColHeaders(),c=n?-a:0,d=r?-l:0;if(c===0&&d===0&&(o===0||s===0))return;let f=(e=this.getSelectedRange().current())===null||e===void 0?void 0:e.highlight;const{focusPosition:m,disableHeadersHighlight:p}=i;lr(Oo,this,p),m&&Number.isInteger(m==null?void 0:m.row)&&Number.isInteger(m==null?void 0:m.col)&&(f=this.tableProps.createCellCoords(pe(m.row,c,o-1),pe(m.col,d,s-1)));const v=this.tableProps.createCellCoords(c,d),R=this.tableProps.createCellCoords(o-1,s-1);this.clear(),this.setRangeStartOnly(v,void 0,f),d<0&&this.selectedByRowHeader.add(this.getLayerLevel()),c<0&&this.selectedByColumnHeader.add(this.getLayerLevel()),this.setRangeEnd(R),this.finish(),lr(Oo,this,!1)}selectCells(e){var r=this;const n=nv(e);if(n===tv)return!1;if(n===ev)throw new Error(vt(Cf||(Cf=ot(["Unsupported format of the selection ranges was passed. To select cells pass \n        the coordinates as an array of arrays ([[rowStart, columnStart/columnPropStart, rowEnd, \n        columnEnd/columnPropEnd]]) or as an array of CellRange objects."],["Unsupported format of the selection ranges was passed. To select cells pass\\x20\n        the coordinates as an array of arrays ([[rowStart, columnStart/columnPropStart, rowEnd,\\x20\n        columnEnd/columnPropEnd]]) or as an array of CellRange objects."]))));const i=q$(n,{createCellCoords:function(){return r.tableProps.createCellCoords(...arguments)},createCellRange:function(){return r.tableProps.createCellRange(...arguments)},propToCol:a=>this.tableProps.propToCol(a),keepDirection:!0}),o=this.settings.navigableHeaders,s={countRows:this.tableProps.countRows(),countCols:this.tableProps.countCols(),countRowHeaders:o?this.tableProps.countRowHeaders():0,countColHeaders:o?this.tableProps.countColHeaders():0},l=!e.some(a=>{const c=i(a),d=c.isValid(s);return!(d&&!c.containsHeaders()||d&&c.containsHeaders()&&c.isSingleHeader())});return l&&(this.clear(),this.setExpectedLayers(e.length),U(e,a=>{const{from:c,to:d}=i(a);this.setRangeStartOnly(c.clone(),!1),this.setRangeEnd(d.clone())}),this.finish()),l}selectColumns(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;const i=typeof e=="string"?this.tableProps.propToCol(e):e,o=typeof r=="string"?this.tableProps.propToCol(r):r,s=this.tableProps.countRows(),l=this.tableProps.countCols(),a=this.tableProps.countColHeaders(),c=a===0?0:-a,d=this.tableProps.createCellCoords(c,i),f=this.tableProps.createCellCoords(s-1,o),m=this.tableProps.createCellRange(d,d,f).isValid({countRows:s,countCols:l,countRowHeaders:0,countColHeaders:a});if(m){let p=0,v=0;Number.isInteger(n==null?void 0:n.row)&&Number.isInteger(n==null?void 0:n.col)?(p=pe(n.row,c,s-1),v=pe(n.col,Math.min(i,o),Math.max(i,o))):(p=pe(n,c,s-1),v=i);const R=this.tableProps.createCellCoords(p,v),H=a===0?0:pe(R.row,c,-1),M=s-1,b=this.tableProps.createCellCoords(H,i),k=this.tableProps.createCellCoords(M,o);this.runLocalHooks("beforeSelectColumns",b,k,R),b.row=H,k.row=M,this.setRangeStartOnly(b,void 0,R),this.selectedByColumnHeader.add(this.getLayerLevel()),this.setRangeEnd(k),this.runLocalHooks("afterSelectColumns",b,k,R),this.finish()}return m}selectRows(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;const i=this.tableProps.countRows(),o=this.tableProps.countCols(),s=this.tableProps.countRowHeaders(),l=s===0?0:-s,a=this.tableProps.createCellCoords(e,l),c=this.tableProps.createCellCoords(r,o-1),d=this.tableProps.createCellRange(a,a,c).isValid({countRows:i,countCols:o,countRowHeaders:s,countColHeaders:0});if(d){let f=0,m=0;Number.isInteger(n==null?void 0:n.row)&&Number.isInteger(n==null?void 0:n.col)?(f=pe(n.row,Math.min(e,r),Math.max(e,r)),m=pe(n.col,l,o-1)):(f=e,m=pe(n,l,o-1));const p=this.tableProps.createCellCoords(f,m),v=s===0?0:pe(p.col,l,-1),R=o-1,H=this.tableProps.createCellCoords(e,v),M=this.tableProps.createCellCoords(r,R);this.runLocalHooks("beforeSelectRows",H,M,p),H.col=v,M.col=R,this.setRangeStartOnly(H,void 0,p),this.selectedByRowHeader.add(this.getLayerLevel()),this.setRangeEnd(M),this.runLocalHooks("afterSelectRows",H,M,p),this.finish()}return d}refresh(){if(!this.isSelected())return;const e=this.tableProps.countRows(),r=this.tableProps.countCols();if(e===0||r===0){this.deselect();return}const n=this.selectedRange.peekByIndex(this.selectedRange.size()-1),{from:i,to:o,highlight:s}=n;this.clear(),s.assign({row:pe(s.row,-1/0,e-1),col:pe(s.col,-1/0,r-1)}),i.assign({row:pe(i.row,-1/0,e-1),col:pe(i.col,-1/0,r-1)}),o.assign({row:pe(o.row,0,e-1),col:pe(o.col,0,r-1)}),this.selectedRange.ranges.push(n),this.highlight.isEnabledFor(Fo,this.selectedRange.current().highlight)&&this.highlight.getFocus().add(s).commit().syncWith(n),this.applyAndCommit(n)}commit(){if(this.highlight.getCustomSelections().forEach(i=>{i.commit()}),!this.isSelected())return;const r=this.getLayerLevel(),n=this.selectedRange.current();this.highlight.isEnabledFor(Fo,n.highlight)&&this.highlight.getFocus().commit().syncWith(n);for(let i=0;i<this.selectedRange.size();i+=1){this.highlight.useLayerLevel(i);const o=this.highlight.createArea(),s=this.highlight.createLayeredArea(),l=this.highlight.createRowHeader(),a=this.highlight.createColumnHeader(),c=this.highlight.createActiveRowHeader(),d=this.highlight.createActiveColumnHeader(),f=this.highlight.createActiveCornerHeader(),m=this.highlight.createRowHighlight(),p=this.highlight.createColumnHighlight();o.commit(),s.commit(),l.commit(),a.commit(),c.commit(),d.commit(),f.commit(),m.commit(),p.commit()}this.highlight.useLayerLevel(r)}}Se(iv,Ur);const e1=iv;function zd(t){let e,r,n,i,o="",s;for(e=0,r=t.length;e<r;e+=1){for(i=t[e].length,n=0;n<i;n+=1)n>0&&(o+="	"),s=t[e][n],typeof s=="string"?s.indexOf("\n")>-1?o+='"'.concat(s.replace(/"/g,'""'),'"'):o+=s:s==null?o+="":o+=s;e!==r-1&&(o+="\n")}return o}function nn(t,e,r){return(e=t1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t1(t){var e=r1(t,"string");return typeof e=="symbol"?e:e+""}function r1(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class kn{static get DESTINATION_RENDERER(){return 1}static get DESTINATION_CLIPBOARD_GENERATOR(){return 2}constructor(e,r,n){nn(this,"hot",void 0),nn(this,"metaManager",void 0),nn(this,"tableMeta",void 0),nn(this,"dataSource",void 0),nn(this,"duckSchema",void 0),nn(this,"colToPropCache",void 0),nn(this,"propToColCache",void 0),this.hot=e,this.metaManager=n,this.tableMeta=n.getTableMeta(),this.dataSource=r,this.refreshDuckSchema(),this.createMap()}createMap(){const e=this.getSchema();if(typeof e>"u")throw new Error("trying to create `columns` definition but you didn't provide `schema` nor `data`");const r=this.tableMeta.columns;let n;if(this.colToPropCache=[],this.propToColCache=new Map,r){let i=0,o=0,s=!1;if(typeof r=="function"){const l=hc(e);i=l>0?l:this.countFirstRowKeys(),s=!0}else{const l=this.tableMeta.maxCols;i=Math.min(l,r.length)}for(n=0;n<i;n++){const l=s?r(n):r[n];if(dr(l)){if(typeof l.data<"u"){const a=s?o:n;this.colToPropCache[a]=l.data,this.propToColCache.set(l.data,a)}o+=1}}}else this.recursiveDuckColumns(e)}countFirstRowKeys(){return ap(this.dataSource)}recursiveDuckColumns(e,r,n){let i=r,o=n,s;return typeof i>"u"&&(i=0,o=""),typeof e=="object"&&!Array.isArray(e)&&fe(e,(l,a)=>{l===null?(s=o+a,this.colToPropCache.push(s),this.propToColCache.set(s,i),i+=1):i=this.recursiveDuckColumns(l,i,"".concat(a,"."))}),i}colToProp(e){if(Number.isInteger(e)===!1)return e;const r=this.hot.toPhysicalColumn(e);return r===null?e:this.colToPropCache&&X(this.colToPropCache[r])?this.colToPropCache[r]:r}propToCol(e){const r=this.propToColCache.get(e);if(X(r))return this.hot.toVisualColumn(r);const n=this.hot.toVisualColumn(e);return n===null?e:n}getSchema(){const e=this.tableMeta.dataSchema;return e?typeof e=="function"?e():e:this.duckSchema}createDuckSchema(){return this.dataSource&&this.dataSource[0]?Ko(this.dataSource[0]):{}}refreshDuckSchema(){this.duckSchema=this.createDuckSchema()}createRow(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,{source:n,mode:i="above"}=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const o=this.hot.countSourceRows();let s=o,l=0,a=e;if((typeof a!="number"||a>=o)&&(a=o),a<this.hot.countRows()&&(s=this.hot.toPhysicalRow(a)),this.hot.runHooks("beforeCreateRow",a,r,n)===!1||s===null)return{delta:0};const d=this.tableMeta.maxRows,f=this.getSchema().length,m=[];for(;l<r&&o+l<d;){let v=null;this.hot.dataType==="array"?this.tableMeta.dataSchema?v=Br(this.getSchema()):(v=[],Nt(f-1,()=>v.push(null))):this.hot.dataType==="function"?v=this.tableMeta.dataSchema(a+l):(v={},Cm(v,this.getSchema())),m.push(v),l+=1}this.hot.rowIndexMapper.insertIndexes(a,l),i==="below"&&(s=Math.min(s+1,o)),this.spliceData(s,0,m);const p=this.hot.toVisualRow(s);return this.hot.countSourceRows()===m.length&&this.hot.columnIndexMapper.initToLength(this.hot.getInitialColumnCount()),l>0&&(e==null?this.metaManager.createRow(null,l):n!=="auto"&&this.metaManager.createRow(s,r)),this.hot.runHooks("afterCreateRow",p,l,n),{delta:l,startPhysicalIndex:s}}createCol(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,{source:n,mode:i="start"}=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!this.hot.isColumnModificationAllowed())throw new Error("Cannot create new column. When data source in an object, you can only have as much columns as defined in first data row, data schema or in the 'columns' setting.If you want to be able to add new columns, you have to use array datasource.");const o=this.dataSource,s=this.tableMeta.maxCols,l=this.hot.countSourceCols();let a=e;if((typeof a!="number"||a>=l)&&(a=l),this.hot.runHooks("beforeCreateCol",a,r,n)===!1)return{delta:0};let d=l;a<this.hot.countCols()&&(d=this.hot.toPhysicalColumn(a));const f=this.hot.countSourceRows();let m=this.hot.countCols(),p=0,v=d;i==="end"&&(v=Math.min(v+1,l));const R=v;for(;p<r&&m<s;){if(typeof a!="number"||a>=m)if(f>0)for(let M=0;M<f;M+=1)typeof o[M]>"u"&&(o[M]=[]),o[M].push(null);else o.push([null]);else for(let M=0;M<f;M++)o[M].splice(v,0,null);p+=1,v+=1,m+=1}this.hot.columnIndexMapper.insertIndexes(a,p),p>0&&(e==null?this.metaManager.createColumn(null,p):n!=="auto"&&this.metaManager.createColumn(R,r));const H=this.hot.toVisualColumn(R);return this.hot.runHooks("afterCreateCol",H,p,n),this.refreshDuckSchema(),{delta:p,startPhysicalIndex:R}}removeRow(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,n=arguments.length>2?arguments[2]:void 0,i=Number.isInteger(e)?e:-r;const o=this.visualRowsToPhysical(i,r),s=this.hot.countSourceRows();if(i=(s+i)%s,this.hot.runHooks("beforeRemoveRow",i,o.length,o,n)===!1)return!1;const a=o.length;if(this.filterData(i,a,o),i<this.hot.countRows()){this.hot.rowIndexMapper.removeIndexes(o);const d=X(this.tableMeta.columns)||X(this.tableMeta.dataSchema)||this.tableMeta.colHeaders;this.hot.rowIndexMapper.getNotTrimmedIndexesLength()===0&&!d&&this.hot.columnIndexMapper.setIndexesSequence([])}return o.slice(0).sort((d,f)=>f-d).forEach(d=>{this.metaManager.removeRow(d,1)}),this.hot.runHooks("afterRemoveRow",i,a,o,n),!0}removeCol(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,n=arguments.length>2?arguments[2]:void 0;if(this.hot.dataType==="object"||this.tableMeta.columns)throw new Error("cannot remove column with object data source or columns option specified");let i=typeof e!="number"?-r:e;i=(this.hot.countCols()+i)%this.hot.countCols();const o=this.visualColumnsToPhysical(i,r),s=o.slice(0).sort((f,m)=>m-f);if(this.hot.runHooks("beforeRemoveCol",i,r,o,n)===!1)return!1;let a=!0;const c=s.length,d=this.dataSource;for(let f=0;f<c;f++)a&&o[0]!==o[f]-f&&(a=!1);if(a)for(let f=0,m=this.hot.countSourceRows();f<m;f++)d[f].splice(o[0],r),f===0&&this.metaManager.removeColumn(o[0],r);else for(let f=0,m=this.hot.countSourceRows();f<m;f++)for(let p=0;p<c;p++)d[f].splice(s[p],1),f===0&&this.metaManager.removeColumn(s[p],1);return i<this.hot.countCols()&&(this.hot.columnIndexMapper.removeIndexes(o),!this.tableMeta.rowHeaders&&this.hot.columnIndexMapper.getNotTrimmedIndexesLength()===0&&this.hot.rowIndexMapper.setIndexesSequence([])),this.hot.runHooks("afterRemoveCol",i,r,o,n),this.refreshDuckSchema(),!0}spliceCol(e,r,n){const i=this.hot.getDataAtCol(e),o=i.slice(r,r+n),s=i.slice(r+n);for(var l=arguments.length,a=new Array(l>3?l-3:0),c=3;c<l;c++)a[c-3]=arguments[c];bh(a,s);let d=0;for(;d<n;)a.push(null),d+=1;return pH(a),this.hot.populateFromArray(r,e,a,null,null,"spliceCol"),o}spliceRow(e,r,n){const i=this.hot.getSourceDataAtRow(e),o=i.slice(r,r+n),s=i.slice(r+n);for(var l=arguments.length,a=new Array(l>3?l-3:0),c=3;c<l;c++)a[c-3]=arguments[c];bh(a,s);let d=0;for(;d<n;)a.push(null),d+=1;return this.hot.populateFromArray(e,r,[a],null,null,"spliceRow"),o}spliceData(e,r,n){if(this.hot.runHooks("beforeDataSplice",e,r,n)!==!1){const o=[...this.dataSource.slice(0,e),...n,...this.dataSource.slice(e)];this.dataSource.length=0,o.forEach(s=>this.dataSource.push(s))}}filterData(e,r,n){let i=this.hot.runHooks("filterData",e,r,n);Array.isArray(i)===!1&&(i=this.dataSource.filter((o,s)=>n.indexOf(s)===-1)),this.dataSource.length=0,Array.prototype.push.apply(this.dataSource,i)}get(e,r){const n=this.hot.toPhysicalRow(e);let i=this.dataSource[n];const o=this.hot.runHooks("modifyRowData",n);i=isNaN(o)?o:i;const{dataDotNotation:s}=this.hot.getSettings();let l=null;if(i&&i.hasOwnProperty&&un(i,r))l=i[r];else if(s&&typeof r=="string"&&r.indexOf(".")>-1){let a=i;if(!a)return null;const c=r.split(".");for(let d=0,f=c.length;d<f;d++)if(a=a[c[d]],typeof a>"u")return null;l=a}else typeof r=="function"&&(l=r(this.dataSource.slice(n,n+1)[0]));if(this.hot.hasHook("modifyData")){const a=wt(l);this.hot.runHooks("modifyData",n,this.propToCol(r),a,"get"),a.isTouched()&&(l=a.value)}return l}getCopyable(e,r){return this.hot.getCellMeta(e,this.propToCol(r)).copyable?this.get(e,r):""}set(e,r,n){const i=this.hot.toPhysicalRow(e);let o=n,s=this.dataSource[i];const l=this.hot.runHooks("modifyRowData",i);if(s=isNaN(l)?l:s,this.hot.hasHook("modifyData")){const c=wt(o);this.hot.runHooks("modifyData",i,this.propToCol(r),c,"set"),c.isTouched()&&(o=c.value)}const{dataDotNotation:a}=this.hot.getSettings();if(s&&s.hasOwnProperty&&un(s,r))s[r]=o;else if(a&&typeof r=="string"&&r.indexOf(".")>-1){let c=s,d=0,f;const m=r.split(".");for(d=0,f=m.length-1;d<f;d++){if(m[d]==="__proto__"||m[d]==="constructor"||m[d]==="prototype")return;typeof c[m[d]]>"u"&&(c[m[d]]={}),c=c[m[d]]}c[m[d]]=o}else if(typeof r=="function")r(this.dataSource.slice(i,i+1)[0],o);else{if(r==="__proto__"||r==="constructor"||r==="prototype")return;s[r]=o}}visualRowsToPhysical(e,r){const n=this.hot.countSourceRows(),i=[];let o=(n+e)%n,s=r,l;for(;o<n&&s;)l=this.hot.toPhysicalRow(o),i.push(l),s-=1,o+=1;return i}visualColumnsToPhysical(e,r){const n=this.hot.countCols(),i=[];let o=(n+e)%n,s=r;for(;o<n&&s;){const l=this.hot.toPhysicalColumn(o);i.push(l),s-=1,o+=1}return i}clear(){for(let e=0;e<this.hot.countSourceRows();e++)for(let r=0;r<this.hot.countCols();r++)this.set(e,this.colToProp(r),"")}getLength(){const e=this.tableMeta.maxRows;let r;e<0||e===0?r=0:r=e||1/0;const n=this.hot.rowIndexMapper.getNotTrimmedIndexesLength();return Math.min(n,r)}getAll(){const e={row:0,col:0},r={row:Math.max(this.hot.countRows()-1,0),col:Math.max(this.hot.countCols()-1,0)};return e.row-r.row===0&&!this.hot.countSourceRows()?[]:this.getRange(e,r,kn.DESTINATION_RENDERER)}countCachedColumns(){return this.colToPropCache.length}getRange(e,r,n){const i=[];let o,s,l;const a=this.tableMeta.maxRows,c=this.tableMeta.maxCols;if(a===0||c===0)return[];const d=n===kn.DESTINATION_CLIPBOARD_GENERATOR?this.getCopyable:this.get,f=Math.min(Math.max(a-1,0),Math.max(e.row,r.row)),m=Math.min(Math.max(c-1,0),Math.max(e.col,r.col));for(o=Math.min(e.row,r.row);o<=f;o++){l=[];const p=o>=0?this.hot.toPhysicalRow(o):o;for(s=Math.min(e.col,r.col);s<=m&&p!==null;s++)l.push(d.call(this,o,this.colToProp(s)));p!==null&&i.push(l)}return i}getText(e,r){return zd(this.getRange(e,r,kn.DESTINATION_RENDERER))}getCopyableText(e,r){return zd(this.getRange(e,r,kn.DESTINATION_CLIPBOARD_GENERATOR))}destroy(){this.hot=null,this.metaManager=null,this.dataSource=null,this.duckSchema=null,this.colToPropCache.length=0,this.propToColCache.clear(),this.propToColCache=void 0}}const n1=kn,{register:o1,getItem:i1,hasItem:s1,getNames:DD,getValues:VD}=Qn("cellTypes");function l1(t){if(!s1(t))throw Error('You declared cell type "'.concat(t,'" as a string that is not mapped to a known object.\n                 Cell type must be an object or a string mapped to an object registered by\n                 "Handsontable.cellTypes.registerCellType" method'));return i1(t)}function a1(t,e){typeof t!="string"&&(e=t,t=e.CELL_TYPE);const{editor:r,renderer:n,validator:i}=e;r&&Om(t,r),n&&Hx(t,n),i&&Px(t,i),o1(t,e)}function c1(t,e){var r;return t==="CELL_TYPE"?!1:((r=e._automaticallyAssignedMetaProps)===null||r===void 0?void 0:r.has(t))||!un(e,t)}function Ns(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t;const n=typeof e.type=="string"?l1(e.type):e.type;if(t._automaticallyAssignedMetaProps&&fe(e,(o,s)=>void t._automaticallyAssignedMetaProps.delete(s)),!dr(n))return;r===t&&!t._automaticallyAssignedMetaProps&&(t._automaticallyAssignedMetaProps=new Set);const i={};fe(n,(o,s)=>{if(c1(s,r)){var l;i[s]=o,(l=t._automaticallyAssignedMetaProps)===null||l===void 0||l.add(s)}}),zn(t,i)}function u1(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];function r(){}uI(r,t);for(let n=0;n<e.length;n++)r.prototype[e[n]]=void 0;return r}function ss(t){return Number.isInteger(t)&&t>=0}function ls(t,e){if(!t())throw new Error("Assertion failed: ".concat(e))}function Ki(t){return t==null}const sv=()=>({_automaticallyAssignedMetaProps:void 0,activeHeaderClassName:"ht__active_highlight",allowEmpty:!0,allowHtml:!1,allowInsertColumn:!0,allowInsertRow:!0,allowInvalid:!0,allowRemoveColumn:!0,allowRemoveRow:!0,ariaTags:!0,autoColumnSize:void 0,autoRowSize:void 0,autoWrapCol:!1,autoWrapRow:!1,bindRowsWithHeaders:void 0,cell:[],cells:void 0,checkedTemplate:void 0,className:void 0,colHeaders:null,collapsibleColumns:void 0,columnHeaderHeight:void 0,columns:void 0,columnSorting:void 0,columnSummary:void 0,colWidths:void 0,commentedCellClassName:"htCommentCell",comments:!1,contextMenu:void 0,copyable:!0,copyPaste:!0,correctFormat:!1,currentColClassName:void 0,currentHeaderClassName:"ht__highlight",currentRowClassName:void 0,customBorders:!1,data:void 0,dataDotNotation:!0,dataSchema:void 0,dateFormat:"DD/MM/YYYY",timeFormat:"h:mm:ss a",datePickerConfig:void 0,defaultDate:void 0,disableVisualSelection:!1,dragToScroll:!0,dropdownMenu:void 0,editor:void 0,enterBeginsEditing:!0,enterMoves:{col:0,row:1},fillHandle:{autoInsertRow:!1},filter:!0,filteringCaseSensitive:!1,filters:void 0,fixedColumnsLeft:0,fixedColumnsStart:0,fixedRowsBottom:0,fixedRowsTop:0,formulas:void 0,fragmentSelection:!1,headerClassName:void 0,height:void 0,hiddenColumns:void 0,hiddenRows:void 0,invalidCellClassName:"htInvalid",imeFastEdit:!1,isEmptyCol(t){let e,r,n;for(e=0,r=this.countRows();e<r;e++)if(n=this.getDataAtCell(e,t),jn(n)===!1)return!1;return!0},isEmptyRow(t){let e,r,n,i;for(e=0,r=this.countCols();e<r;e++)if(n=this.getDataAtCell(t,e),jn(n)===!1)return typeof n=="object"?(i=this.getCellMeta(t,e),bm(this.getSchema()[i.prop],n)):!1;return!0},label:void 0,language:"en-US",layoutDirection:"inherit",licenseKey:void 0,locale:"en-US",manualColumnFreeze:void 0,manualColumnMove:void 0,manualColumnResize:void 0,manualRowMove:void 0,manualRowResize:void 0,maxCols:1/0,maxRows:1/0,mergeCells:!1,minCols:0,minRows:0,minSpareCols:0,minSpareRows:0,multiColumnSorting:void 0,navigableHeaders:!1,tabNavigation:!0,nestedHeaders:void 0,nestedRows:void 0,noWordWrapClassName:"htNoWrap",numericFormat:void 0,observeDOMVisibility:!0,outsideClickDeselects:!0,persistentState:void 0,placeholder:void 0,placeholderCellClassName:"htPlaceholder",preventOverflow:!1,preventWheel:!1,readOnly:!1,readOnlyCellClassName:"htDimmed",renderAllRows:!1,renderAllColumns:!1,renderer:void 0,rowHeaders:void 0,rowHeaderWidth:void 0,rowHeights:void 0,search:!1,selectionMode:"multiple",selectOptions:void 0,skipColumnOnPaste:!1,skipRowOnPaste:!1,sortByRelevance:!0,source:void 0,startCols:5,startRows:5,stretchH:"none",strict:void 0,tableClassName:void 0,themeName:void 0,tabMoves:{row:0,col:1},title:void 0,trimDropdown:!0,trimRows:void 0,trimWhitespace:!0,type:"text",uncheckedTemplate:void 0,undo:!0,validator:void 0,viewportColumnRenderingOffset:"auto",viewportRowRenderingOffset:"auto",viewportColumnRenderingThreshold:0,viewportRowRenderingThreshold:0,visibleRows:10,width:void 0,wordWrap:!0});function Ud(t,e,r){return(e=h1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h1(t){var e=d1(t,"string");return typeof e=="symbol"?e:e+""}function d1(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function f1(){return class{}}class g1{constructor(e){Ud(this,"metaCtor",f1()),Ud(this,"meta",void 0),this.meta=this.metaCtor.prototype,zn(this.meta,sv()),this.meta.instance=e}getMetaConstructor(){return this.metaCtor}getMeta(){return this.meta}updateMeta(e){var r;zn(this.meta,e),Ns(this.meta,{...e,type:(r=e.type)!==null&&r!==void 0?r:this.meta.type},e)}}function m1(t,e,r){return(e=p1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p1(t){var e=w1(t,"string");return typeof e=="symbol"?e:e+""}function w1(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class v1{constructor(e){m1(this,"meta",void 0);const r=e.getMetaConstructor();this.meta=new r}getMeta(){return this.meta}updateMeta(e){zn(this.meta,e),Ns(this.meta,e,e)}}function Yi(t,e,r){return(e=y1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y1(t){var e=C1(t,"string");return typeof e=="symbol"?e:e+""}function C1(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class Cs{constructor(e){Yi(this,"valueFactory",void 0),Yi(this,"data",[]),Yi(this,"index",[]),Yi(this,"holes",new Set),this.valueFactory=e}obtain(e){ls(()=>ss(e),"Expecting an unsigned number.");const r=this._getStorageIndexByKey(e);let n;if(r>=0)n=this.data[r],n===void 0&&(n=this.valueFactory(e),this.data[r]=n);else if(n=this.valueFactory(e),this.holes.size>0){const i=this.holes.values().next().value;this.holes.delete(i),this.data[i]=n,this.index[e]=i}else this.data.push(n),this.index[e]=this.data.length-1;return n}insert(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;ls(()=>ss(e)||Ki(e),"Expecting an unsigned number or null/undefined argument.");const n=[],i=this.data.length;for(let s=0;s<r;s++)n.push(i+s),this.data.push(void 0);const o=Ki(e)?this.index.length:e;this.index=[...this.index.slice(0,o),...n,...this.index.slice(o)]}remove(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;ls(()=>ss(e)||Ki(e),"Expecting an unsigned number or null/undefined argument.");const n=this.index.splice(Ki(e)?this.index.length-r:e,r);for(let i=0;i<n.length;i++){const o=n[i];typeof o=="number"&&this.holes.add(o)}}size(){return this.data.length-this.holes.size}values(){return this.data.filter((e,r)=>e!==void 0&&!this.holes.has(r))[Symbol.iterator]()}entries(){const e=[];for(let n=0;n<this.data.length;n++){const i=this._getKeyByStorageIndex(n);i!==-1&&this.data[n]!==void 0&&e.push([i,this.data[n]])}let r=0;return{next:()=>{if(r<e.length){const n=e[r];return r+=1,{value:n,done:!1}}return{done:!0}}}}clear(){this.data=[],this.index=[],this.holes.clear()}_getStorageIndexByKey(e){return this.index.length>e?this.index[e]:-1}_getKeyByStorageIndex(e){return this.index.indexOf(e)}[Symbol.iterator](){return this.entries()}}function Gd(t,e,r){return(e=b1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b1(t){var e=S1(t,"string");return typeof e=="symbol"?e:e+""}function S1(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}const R1=["data","width"];class T1{constructor(e){Gd(this,"globalMeta",void 0),Gd(this,"metas",new Cs(()=>this._createMeta())),this.globalMeta=e,this.metas=new Cs(()=>this._createMeta())}updateMeta(e,r){const n=this.getMeta(e);zn(n,r),Ns(n,r)}createColumn(e,r){this.metas.insert(e,r)}removeColumn(e,r){this.metas.remove(e,r)}getMeta(e){return this.metas.obtain(e)}getMetaConstructor(e){return this.metas.obtain(e).constructor}clearCache(){this.metas.clear()}_createMeta(){return u1(this.globalMeta.getMetaConstructor(),R1).prototype}}function Kd(t,e,r){return(e=E1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function E1(t){var e=O1(t,"string");return typeof e=="symbol"?e:e+""}function O1(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class H1{constructor(e){Kd(this,"columnMeta",void 0),Kd(this,"metas",new Cs(()=>this._createRow())),this.columnMeta=e}updateMeta(e,r,n){const i=this.getMeta(e,r);zn(i,n),Ns(i,n)}createRow(e,r){this.metas.insert(e,r)}createColumn(e,r){for(let n=0;n<this.metas.size();n++)this.metas.obtain(n).insert(e,r)}removeRow(e,r){this.metas.remove(e,r)}removeColumn(e,r){for(let n=0;n<this.metas.size();n++)this.metas.obtain(n).remove(e,r)}getMeta(e,r,n){const i=this.metas.obtain(e).obtain(r);return n===void 0?i:i[n]}setMeta(e,r,n,i){var o;const s=this.metas.obtain(e).obtain(r);(o=s._automaticallyAssignedMetaProps)===null||o===void 0||o.delete(n),s[n]=i}removeMeta(e,r,n){const i=this.metas.obtain(e).obtain(r);delete i[n]}getMetas(){const e=[],r=Array.from(this.metas.values());for(let n=0;n<r.length;n++)X(r[n])&&e.push(...r[n].values());return e}getMetasAtRow(e){ls(()=>ss(e),"Expecting an unsigned number.");const r=new Map(this.metas);return r.has(e)?Array.from(r.get(e).values()):[]}clearCache(){this.metas.clear()}_createRow(){return new Cs(e=>this._createMeta(e))}_createMeta(e){const r=this.columnMeta.getMetaConstructor(e);return new r}}class lv{constructor(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];this.hot=e,this.globalMeta=new g1(e),this.tableMeta=new v1(this.globalMeta),this.columnMeta=new T1(this.globalMeta),this.cellMeta=new H1(this.columnMeta),n.forEach(i=>new i(this)),this.globalMeta.updateMeta(r)}getGlobalMeta(){return this.globalMeta.getMeta()}updateGlobalMeta(e){this.globalMeta.updateMeta(e)}getTableMeta(){return this.tableMeta.getMeta()}updateTableMeta(e){this.tableMeta.updateMeta(e)}getColumnMeta(e){return this.columnMeta.getMeta(e)}updateColumnMeta(e,r){this.columnMeta.updateMeta(e,r)}getCellMeta(e,r,n){const i=this.cellMeta.getMeta(e,r);return i.visualRow=n.visualRow,i.visualCol=n.visualColumn,i.row=e,i.col=r,n.skipMetaExtension||this.runLocalHooks("afterGetCellMeta",i),i}getCellMetaKeyValue(e,r,n){if(typeof n!="string")throw new Error("The passed cell meta object key is not a string");return this.cellMeta.getMeta(e,r,n)}setCellMeta(e,r,n,i){this.cellMeta.setMeta(e,r,n,i)}updateCellMeta(e,r,n){this.cellMeta.updateMeta(e,r,n)}removeCellMeta(e,r,n){this.cellMeta.removeMeta(e,r,n)}getCellsMeta(){return this.cellMeta.getMetas()}getCellsMetaAtRow(e){return this.cellMeta.getMetasAtRow(e)}createRow(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;this.cellMeta.createRow(e,r)}removeRow(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;this.cellMeta.removeRow(e,r)}createColumn(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;this.cellMeta.createColumn(e,r),this.columnMeta.createColumn(e,r)}removeColumn(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;this.cellMeta.removeColumn(e,r),this.columnMeta.removeColumn(e,r)}clearCellsCache(){this.cellMeta.clearCache()}clearCache(){this.cellMeta.clearCache(),this.columnMeta.clearCache()}}Se(lv,Ur);function Yd(t,e,r,n){const{hotInstance:i,dataMap:o,dataSource:s,internalSource:l,source:a,metaManager:c,firstRun:d}=n,f=sc(l),m=i.getSettings();Array.isArray(m.dataSchema)?i.dataType="array":Z(m.dataSchema)?i.dataType="function":i.dataType="object",o&&o.destroy(),t=i.runHooks("before".concat(f),t,d,a);const p=new n1(i,t,c);if(e(p),typeof t=="object"&&t!==null)t.push&&t.splice||(t=[t]);else if(t===null){const v=p.getSchema();t=[];let R,H=0,M=0;for(H=0,M=m.startRows;H<M;H++)if((i.dataType==="object"||i.dataType==="function")&&m.dataSchema)R=Br(v),t.push(R);else if(i.dataType==="array")R=Br(v[0]),t.push(R);else{R=[];for(let b=0,k=m.startCols;b<k;b++)R.push(null);t.push(R)}}else throw new Error("".concat(l," only accepts array of objects or array of arrays (").concat(typeof t," given)"));Array.isArray(t[0])&&(i.dataType="array"),m.data=t,p.dataSource=t,s.data=t,s.dataType=i.dataType,s.colToProp=p.colToProp.bind(p),s.propToCol=p.propToCol.bind(p),s.countCachedColumns=p.countCachedColumns.bind(p),r(p),i.runHooks("after".concat(f),t,d,a),d||(i.runHooks("afterChange",null,l),i.render()),i.getSettings().ariaTags&&de(i.rootElement,[om(-1),Is(i.countCols()+(i.view?i.countRowHeaders():0))])}function Xd(t,e,r){return(e=I1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function I1(t){var e=M1(t,"string");return typeof e=="symbol"?e:e+""}function M1(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}class x1{constructor(e){var r=this;Xd(this,"metaManager",void 0),Xd(this,"metaSyncMemo",new Map),this.metaManager=e,e.addLocalHook("afterGetCellMeta",function(){return r.extendCellMeta(...arguments)}),Le.getSingleton().add("beforeRender",n=>{n&&this.metaSyncMemo.clear()},this.metaManager.hot)}extendCellMeta(e){var r;const{row:n,col:i}=e;if((r=this.metaSyncMemo.get(n))!==null&&r!==void 0&&r.has(i))return;const{visualRow:o,visualCol:s}=e,l=this.metaManager.hot,a=l.colToProp(s);e.prop=a,l.runHooks("beforeGetCellMeta",o,s,e);const c=un(e,"type")?e.type:null;let d=Z(e.cells)?e.cells(n,i,a):null;if(c)if(d){var f;d.type=(f=d.type)!==null&&f!==void 0?f:c}else d={type:c};d&&this.metaManager.updateCellMeta(n,i,d),l.runHooks("afterGetCellMeta",o,s,e),this.metaSyncMemo.has(n)||this.metaSyncMemo.set(n,new Set),this.metaSyncMemo.get(n).add(i)}}function _1(t,e,r){P1(t,e),e.set(t,r)}function P1(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function na(t,e,r){return(e=A1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function A1(t){var e=N1(t,"string");return typeof e=="symbol"?e:e+""}function N1(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function $1(t,e){return t.get(L1(t,e))}function L1(t,e,r){if(typeof t=="function"?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var qd=new WeakMap;class D1{constructor(e){na(this,"metaManager",void 0),na(this,"usageTracker",new Set),na(this,"propDescriptors",new Map([["ariaTags",{initOnly:!0}],["fixedColumnsLeft",{target:"fixedColumnsStart",onChange(r){if(this.metaManager.hot.isRtl()&&r==="fixedColumnsLeft")throw new Error("The `fixedColumnsLeft` is not supported for RTL. Please use option `fixedColumnsStart`.");if(this.usageTracker.has("fixedColumnsLeft")&&this.usageTracker.has("fixedColumnsStart"))throw new Error("The `fixedColumnsLeft` and `fixedColumnsStart` should not be used together. Please use only the option `fixedColumnsStart`.")}}],["layoutDirection",{initOnly:!0}],["renderAllColumns",{initOnly:!0}],["renderAllRows",{initOnly:!0}]])),_1(this,qd,(r,n,i)=>{if(!i)throw new Error("The `".concat(r,"` option can not be updated after the Handsontable is initialized."))}),this.metaManager=e,this.extendMetaProps()}extendMetaProps(){this.propDescriptors.forEach((e,r)=>{const{initOnly:n,target:i,onChange:o}=e,s=typeof i=="string",l=s?i:r,a="_".concat(l);this.metaManager.globalMeta.meta[a]=this.metaManager.globalMeta.meta[l],o?(this.installPropWatcher(r,a,o),s&&this.installPropWatcher(i,a,o)):n&&(this.installPropWatcher(r,a,$1(qd,this)),this.metaManager.globalMeta.meta._initOnlySettings||(this.metaManager.globalMeta.meta._initOnlySettings=[]),this.metaManager.globalMeta.meta._initOnlySettings.push(r))})}installPropWatcher(e,r,n){const i=this;Object.defineProperty(this.metaManager.globalMeta.meta,e,{get(){return this[r]},set(o){const s=!i.usageTracker.has(e);i.usageTracker.add(e),n.call(i,e,o,s),this[r]=o},enumerable:!0,configurable:!0})}}const av="gridDefault",cv="editorManager.handlingEditor",V1={name:"editorCloseAndSave",callback(t){t._getEditorManager().closeEditorAndSaveChanges()}},k1="hooksRefRegisterer",uv={_hooksStorage:Object.create(null),addHook(t,e){return this._hooksStorage[t]||(this._hooksStorage[t]=[]),this.hot.addHook(t,e),this._hooksStorage[t].push(e),this},removeHooksByKey(t){U(this._hooksStorage[t]||[],e=>{this.hot.removeHook(t,e)})},clearHooks(){fe(this._hooksStorage,(t,e)=>this.removeHooksByKey(e)),this._hooksStorage={}}};zr(uv,"MIXIN_NAME",k1,{writable:!1,enumerable:!1});const F1=uv;function Kt(t,e,r){return(e=B1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function B1(t){var e=W1(t,"string");return typeof e=="symbol"?e:e+""}function W1(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}const j1="base",He=Object.freeze({VIRGIN:"STATE_VIRGIN",EDITING:"STATE_EDITING",WAITING:"STATE_WAITING",FINISHED:"STATE_FINISHED"});class Vc{static get EDITOR_TYPE(){return j1}constructor(e){Kt(this,"hot",void 0),Kt(this,"state",He.VIRGIN),Kt(this,"_opened",!1),Kt(this,"_fullEditMode",!1),Kt(this,"_closeCallback",null),Kt(this,"TD",null),Kt(this,"row",null),Kt(this,"col",null),Kt(this,"prop",null),Kt(this,"originalValue",null),Kt(this,"cellProperties",null),this.hot=e,this.init()}_fireCallbacks(e){this._closeCallback&&(this._closeCallback(e),this._closeCallback=null)}init(){}getValue(){throw Error("Editor getValue() method unimplemented")}setValue(){throw Error("Editor setValue() method unimplemented")}open(){throw Error("Editor open() method unimplemented")}close(){throw Error("Editor close() method unimplemented")}prepare(e,r,n,i,o,s){this.TD=i,this.row=e,this.col=r,this.prop=n,this.originalValue=o,this.cellProperties=s,this.state=this.isOpened()?this.state:He.VIRGIN}extend(){return class extends this.constructor{}}saveValue(e,r){let n,i,o,s;if(r){const a=this.hot.getSelectedLast();n=Math.max(Math.min(a[0],a[2]),0),i=Math.max(Math.min(a[1],a[3]),0),o=Math.max(a[0],a[2]),s=Math.max(a[1],a[3])}else[n,i,o,s]=[this.row,this.col,null,null];const l=this.hot.runHooks("modifyGetCellCoords",n,i,!1,"meta");Array.isArray(l)&&([n,i]=l),this.hot.populateFromArray(n,i,e,o,s,"edit")}beginEditing(e,r){if(this.state!==He.VIRGIN)return;const n=this.hot,i=n.rowIndexMapper.getRenderableFromVisualIndex(this.row),o=n.columnIndexMapper.getRenderableFromVisualIndex(this.col),s=()=>{if(this.state=He.EDITING,this.isInFullEditMode()){const a=typeof e=="string"?e:Lo(this.originalValue);this.setValue(a)}this.open(r),this._opened=!0,this.focus(),n.view.render(),n.runHooks("afterBeginEditing",this.row,this.col)};this.hot.addHookOnce("afterScroll",s),n.view.scrollViewport(n._createCellCoords(i,o))||(this.hot.removeHook("afterScroll",s),s())}finishEditing(e,r,n){let i;if(n){const o=this._closeCallback;this._closeCallback=s=>{o&&o(s),n(s),this.hot.view.render()}}if(!this.isWaiting()){if(this.state===He.VIRGIN){this.hot._registerTimeout(()=>{this._fireCallbacks(!0)});return}if(this.state===He.EDITING){if(e){this.cancelChanges(),this.hot.view.render();return}const o=this.getValue();this.cellProperties.trimWhitespace?i=[[typeof o=="string"?String.prototype.trim.call(o||""):o]]:i=[[o]],this.state=He.WAITING,this.saveValue(i,r),this.hot.getCellValidator(this.cellProperties)?this.hot.addHookOnce("postAfterValidate",s=>{this.state=He.FINISHED,this.discardEditor(s)}):(this.state=He.FINISHED,this.discardEditor(!0))}}}cancelChanges(){this.state=He.FINISHED,this.discardEditor()}discardEditor(e){this.state===He.FINISHED&&(e===!1&&this.cellProperties.allowInvalid!==!0?(this.hot.selectCell(this.row,this.col),this.focus(),this.state=He.EDITING,this._fireCallbacks(!1)):(this.close(),this._opened=!1,this._fullEditMode=!1,this.state=He.VIRGIN,this._fireCallbacks(!0),this.hot.getShortcutManager().setActiveContextName("grid")))}enableFullEditMode(){this._fullEditMode=!0}isInFullEditMode(){return this._fullEditMode}isOpened(){return this._opened}isWaiting(){return this.state===He.WAITING}getEditedCellRect(){var e;const r=this.getEditedCell();if(!r)return;const{wtOverlays:n,wtViewport:i}=this.hot.view._wt,o=this.hot.rootWindow,s=At(r),l=ut(r),a=At(this.hot.rootElement),c=ut(this.hot.rootElement),d=n.topOverlay.holder,f=n.inlineStartOverlay.holder,m=d!==o?d.scrollTop:0,p=f!==o?f.scrollLeft:0,v=o.innerWidth-a.left-c,{wtTable:R}=(e=n.getParentOverlay(r))!==null&&e!==void 0?e:this.hot.view._wt,H=R.name,M=["master","inline_start"].includes(H)?m:0,b=["master","top","bottom"].includes(H)?p:0,k=s.top===a.top?0:1;let $=s.top-a.top-k-M,W=0;this.hot.isRtl()?W=o.innerWidth-s.left-l-v-1+b:W=s.left-a.left-1-b,["top","top_inline_start_corner"].includes(H)&&($+=n.topOverlay.getOverlayOffset()),["inline_start","top_inline_start_corner"].includes(H)&&(W+=Math.abs(n.inlineStartOverlay.getOverlayOffset()));const D=this.hot.hasColHeaders(),F=this.hot.rowIndexMapper.getRenderableFromVisualIndex(this.row),ve=this.hot.columnIndexMapper.getRenderableFromVisualIndex(this.col),St=this.hot.rowIndexMapper.getRenderableIndexesLength()-this.hot.view._wt.getSetting("fixedRowsBottom");(D&&F<=0||F===St)&&($+=1),ve<=0&&(W+=1);const te=i.rowsRenderCalculator.startPosition,Rt=i.columnsRenderCalculator.startPosition,Ye=Math.abs(n.inlineStartOverlay.getScrollPosition()),Ft=n.topOverlay.getScrollPosition(),Bt=ht(this.hot.rootDocument);let Wt=r.offsetTop;if(["inline_start","master"].includes(H)&&(Wt+=te-Ft),["bottom","bottom_inline_start_corner"].includes(H)){const{wtViewport:E,wtTable:O}=n.bottomOverlay.clone;Wt+=E.getWorkspaceHeight()-O.getHeight()-Bt}let ue=r.offsetLeft;this.hot.isRtl()?(ue>=0?ue=R.getWidth()-r.offsetLeft:ue=Math.abs(ue),ue+=Rt-Ye-l):["top","master","bottom"].includes(H)&&(ue+=Rt-Ye);const ye=o.getComputedStyle(this.TD),xe=this.hot.isRtl()?"borderRightWidth":"borderLeftWidth",mr=parseInt(ye[xe],10)>0?0:1,J=parseInt(ye.borderTopWidth,10)>0?0:1,u=ut(r)+mr,h=yt(r)+J,g=zH(d)?Bt:0,w=UH(f)?Bt:0,C=this.hot.view.maximumVisibleElementWidth(ue)-g+mr,S=Math.max(this.hot.view.maximumVisibleElementHeight(Wt)-w+J,this.hot.view.getDefaultRowHeight());return{top:$,start:W,height:h,maxHeight:S,width:u,maxWidth:C}}getEditedCellsLayerClass(){switch(this.checkEditorSection()){case"inline-start":return"ht_clone_left ht_clone_inline_start";case"bottom":return"ht_clone_bottom";case"bottom-inline-start-corner":return"ht_clone_bottom_left_corner ht_clone_bottom_inline_start_corner";case"top":return"ht_clone_top";case"top-inline-start-corner":return"ht_clone_top_left_corner ht_clone_top_inline_start_corner";default:return"ht_clone_master"}}getEditedCell(){return this.hot.getCell(this.row,this.col,!0)}checkEditorSection(){const e=this.hot.countRows();let r="";return this.row<this.hot.getSettings().fixedRowsTop?this.col<this.hot.getSettings().fixedColumnsStart?r="top-inline-start-corner":r="top":this.hot.getSettings().fixedRowsBottom&&this.row>=e-this.hot.getSettings().fixedRowsBottom?this.col<this.hot.getSettings().fixedColumnsStart?r="bottom-inline-start-corner":r="bottom":this.col<this.hot.getSettings().fixedColumnsStart&&(r="inline-start"),r}}Se(Vc,F1);const z1={name:"editorCloseAndSaveByArrowKeys",callback(t,e,r){const n=t._getEditorManager(),i=n.getActiveEditor();i.isInFullEditMode()&&i.state===He.EDITING||(n.closeEditorAndSaveChanges(),t.getSelected()&&(r.includes("arrowdown")?t.selection.transformStart(1,0):r.includes("arrowup")?t.selection.transformStart(-1,0):r.includes("arrowleft")?t.selection.transformStart(0,-1*t.getDirectionFactor()):r.includes("arrowright")&&t.selection.transformStart(0,t.getDirectionFactor())),e.preventDefault())}},U1={name:"editorCloseAndSaveByEnter",callback(t,e){const r=t._getEditorManager();r.closeEditorAndSaveChanges(e.ctrlKey||e.metaKey),r.moveSelectionAfterEnter(e)}},G1={name:"editorCloseWithoutSaving",callback(t){const e=t._getEditorManager();e.closeEditorAndRestoreOriginalValue(t.getShortcutManager().isCtrlPressed()),e.activeEditor.focus()}},K1={name:"editorFastOpen",callback(t,e){const{highlight:r}=t.getSelectedRangeLast();r.isHeader()||t._getEditorManager().openEditor(null,e,!0)}},Y1={name:"editorOpen",callback(t,e,r){const n=t._getEditorManager(),i=t.getSelectedRangeLast(),{highlight:o}=i;if(t.selection.isMultiple()&&!i.isHeader()&&t.countRenderedCols()>0&&t.countRenderedRows()>0){const s=t.getSettings(),l=typeof s.enterMoves=="function"?s.enterMoves(e):s.enterMoves;r.includes("shift")?t.selection.transformFocus(-l.row,-l.col):t.selection.transformFocus(l.row,l.col);return}o.isHeader()||(t.getSettings().enterBeginsEditing?n.cellProperties.readOnly?n.moveSelectionAfterEnter(e):n.openEditor(null,e,!0):n.moveSelectionAfterEnter(e),Ps(e))}};function X1(){return[V1,z1,U1,G1,K1,Y1]}const q1={name:"extendCellsSelectionDown",callback(t){const{selection:e}=t,{highlight:r}=t.getSelectedRangeLast();!e.isSelectedByColumnHeader()&&!e.isSelectedByCorner()&&(r.isCell()||r.isHeader()&&e.isSelectedByRowHeader())&&(e.markSource("keyboard"),e.transformEnd(1,0),e.markEndSource())}},Q1={name:"extendCellsSelectionDownByViewportHeight",callback(t){const{selection:e,rowIndexMapper:r}=t,{to:n}=t.getSelectedRangeLast(),i=Math.min(n.row+t.countVisibleRows(),t.countRows()-1),o=r.getNearestNotHiddenIndex(i,-1);if(o!==null){const s=t._createCellCoords(o,n.col),l=n.row-t.getFirstFullyVisibleRow(),a=Math.min(s.row-l,t.countRows()-1);e.markSource("keyboard"),e.setRangeEnd(s),e.markEndSource(),t.scrollViewportTo({row:a,verticalSnap:"top",horizontalSnap:"start"})}}},Z1={name:"extendCellsSelectionLeft",callback(t){const{selection:e}=t,{highlight:r}=t.getSelectedRangeLast();!e.isSelectedByRowHeader()&&!e.isSelectedByCorner()&&(r.isCell()||r.isHeader()&&e.isSelectedByColumnHeader())&&(e.markSource("keyboard"),e.transformEnd(0,-1*t.getDirectionFactor()),e.markEndSource())}},J1={name:"extendCellsSelectionRight",callback(t){const{selection:e}=t,{highlight:r}=t.getSelectedRangeLast();!e.isSelectedByRowHeader()&&!e.isSelectedByCorner()&&(r.isCell()||r.isHeader()&&e.isSelectedByColumnHeader())&&(e.markSource("keyboard"),e.transformEnd(0,t.getDirectionFactor()),e.markEndSource())}},eL={name:"extendCellsSelectionToColumns",callback(t){const{selection:e}=t,{highlight:r,from:n,to:i}=t.getSelectedRangeLast();e.markSource("keyboard"),e.isSelectedByRowHeader()?e.selectAll(!0,!0):t.selectColumns(n.col,i.col,r),e.markEndSource()}},tL={name:"extendCellsSelectionToMostBottom",callback(t){const{selection:e,rowIndexMapper:r}=t,{highlight:n,from:i,to:o}=t.getSelectedRangeLast(),s=n.isHeader()&&e.isSelectedByRowHeader();if(n.isCell()||s){const l=r.getNearestNotHiddenIndex(t.countRows()-1,-1),a=i.clone();a.row=n.row,e.markSource("keyboard"),e.setRangeStart(a,void 0,!1,n.clone()),s&&e.selectedByRowHeader.add(e.getLayerLevel()),e.setRangeEnd(t._createCellCoords(l,o.col)),e.markEndSource()}}},rL={name:"extendCellsSelectionToMostInlineEnd",callback(t){const{selection:e,columnIndexMapper:r}=t,{highlight:n,from:i,to:o}=t.getSelectedRangeLast();if(!e.isSelectedByRowHeader()&&!e.isSelectedByCorner()&&n.isCell()){const s=r.getNearestNotHiddenIndex(t.countCols()-1,-1),l=i.clone();l.col=n.col,e.markSource("keyboard"),e.setRangeStart(l,void 0,!1,n.clone()),e.setRangeEnd(t._createCellCoords(o.row,s)),e.markEndSource()}}},nL={name:"extendCellsSelectionToMostInlineStart",callback(t){const{selection:e,columnIndexMapper:r}=t,{highlight:n,from:i,to:o}=t.getSelectedRangeLast();if(!e.isSelectedByRowHeader()&&!e.isSelectedByCorner()&&n.isCell()){const s=parseInt(t.getSettings().fixedColumnsStart,10),l=r.getNearestNotHiddenIndex(s,1),a=i.clone();a.col=n.col,e.markSource("keyboard"),e.setRangeStart(a,void 0,!1,n.clone()),e.setRangeEnd(t._createCellCoords(o.row,l)),e.markEndSource()}}},oL={name:"extendCellsSelectionToMostLeft",callback(t){const{selection:e,columnIndexMapper:r}=t,{highlight:n,from:i,to:o}=t.getSelectedRangeLast(),s=n.isHeader()&&e.isSelectedByColumnHeader();if(n.isCell()||s){const l=r.getNearestNotHiddenIndex(...t.isRtl()?[t.countCols()-1,-1]:[0,1]),a=i.clone();a.col=n.col,e.markSource("keyboard"),e.setRangeStart(a,void 0,!1,n.clone()),s&&e.selectedByColumnHeader.add(e.getLayerLevel()),e.setRangeEnd(t._createCellCoords(o.row,l)),e.markEndSource()}}},iL={name:"extendCellsSelectionToMostRight",callback(t){const{selection:e,columnIndexMapper:r}=t,{highlight:n,from:i,to:o}=t.getSelectedRangeLast(),s=n.isHeader()&&e.isSelectedByColumnHeader();if(n.isCell()||s){const l=r.getNearestNotHiddenIndex(...t.isRtl()?[0,1]:[t.countCols()-1,-1]),a=i.clone();a.col=n.col,e.markSource("keyboard"),e.setRangeStart(a,void 0,!1,n.clone()),s&&e.selectedByColumnHeader.add(e.getLayerLevel()),e.setRangeEnd(t._createCellCoords(o.row,l)),e.markEndSource()}}},sL={name:"extendCellsSelectionToMostTop",callback(t){const{selection:e,rowIndexMapper:r}=t,{highlight:n,from:i,to:o}=t.getSelectedRangeLast(),s=n.isHeader()&&e.isSelectedByRowHeader();if(n.isCell()||s){const l=r.getNearestNotHiddenIndex(0,1),a=i.clone();a.row=n.row,e.markSource("keyboard"),e.setRangeStart(a,void 0,!1,n.clone()),s&&e.selectedByRowHeader.add(e.getLayerLevel()),e.setRangeEnd(t._createCellCoords(l,o.col)),e.markEndSource()}}},lL={name:"extendCellsSelectionToRows",callback(t){const{selection:e}=t,{highlight:r,from:n,to:i}=t.getSelectedRangeLast();e.markSource("keyboard"),e.isSelectedByColumnHeader()?e.selectAll(!0,!0):t.selectRows(n.row,i.row,r),e.markEndSource()}},aL={name:"extendCellsSelectionUp",callback(t){const{selection:e}=t,{highlight:r}=t.getSelectedRangeLast();!e.isSelectedByColumnHeader()&&!e.isSelectedByCorner()&&(r.isCell()||r.isHeader()&&e.isSelectedByRowHeader())&&(e.markSource("keyboard"),e.transformEnd(-1,0),e.markEndSource())}},cL={name:"extendCellsSelectionUpByViewportHeight",callback(t){const{selection:e,rowIndexMapper:r}=t,{to:n}=t.getSelectedRangeLast(),i=Math.max(n.row-t.countVisibleRows(),0),o=r.getNearestNotHiddenIndex(i,1);if(o!==null){const s=t._createCellCoords(o,n.col),l=n.row-t.getFirstFullyVisibleRow(),a=Math.max(s.row-l,0);e.markSource("keyboard"),e.setRangeEnd(s),e.markEndSource(),t.scrollViewportTo({row:a,verticalSnap:"top",horizontalSnap:"start"})}}};function uL(){return[q1,Q1,Z1,J1,eL,tL,rL,nL,oL,iL,sL,lL,aL,cL]}const hL={name:"moveCellSelectionDown",callback(t){let{selection:e}=t;e.markSource("keyboard"),e.transformStart(1,0),e.markEndSource()}},dL={name:"moveCellSelectionDownByViewportHeight",callback(t){const{selection:e}=t,{navigableHeaders:r}=t.getSettings(),n=r?t.countColHeaders():0,{row:i}=t.getSelectedRangeLast().highlight;let o=t.countVisibleRows()+n;o=o===0?1:o,i===t.countRows()-1?o=1:i+o>t.countRows()&&(o=t.countRows()-i-1),e.markSource("keyboard"),e.transformStart(o,0),e.markEndSource(),t.getSelectedRangeLast().highlight.row<0&&t.scrollViewportTo({row:0})}},fL={name:"moveCellSelectionInlineEnd",callback(t,e){const{selection:r}=t,n=t.getSettings(),i=t.getSelectedRangeLast(),o=typeof n.tabMoves=="function"?n.tabMoves(e):n.tabMoves;r.markSource("keyboard"),r.isMultiple()&&!i.isHeader()&&t.countRenderedCols()>0&&t.countRenderedRows()>0?r.transformFocus(-o.row,-o.col):r.transformStart(-o.row,-o.col),r.markEndSource()}},gL={name:"moveCellSelectionInlineStart",callback(t,e){const{selection:r}=t,n=t.getSettings(),i=t.getSelectedRangeLast(),o=typeof n.tabMoves=="function"?n.tabMoves(e):n.tabMoves;r.markSource("keyboard"),r.isMultiple()&&!i.isHeader()&&t.countRenderedCols()>0&&t.countRenderedRows()>0?r.transformFocus(o.row,o.col):r.transformStart(o.row,o.col),r.markEndSource()}},mL={name:"moveCellSelectionLeft",callback(t){const{selection:e}=t;e.markSource("keyboard"),e.transformStart(0,-1*t.getDirectionFactor()),e.markEndSource()}},pL={name:"moveCellSelectionRight",callback(t){const{selection:e}=t;e.markSource("keyboard"),e.transformStart(0,t.getDirectionFactor()),e.markEndSource()}},wL={name:"moveCellSelectionToMostBottom",callback(t){const{selection:e}=t,{col:r}=t.getSelectedRangeLast().highlight;let n=t.rowIndexMapper.getNearestNotHiddenIndex(t.countRows()-1,-1);n===null&&(n=-1),e.setRangeStart(t._createCellCoords(n,r))}},vL={name:"moveCellSelectionToMostBottomInlineEnd",callback(t){const{selection:e,rowIndexMapper:r,columnIndexMapper:n}=t,i=parseInt(t.getSettings().fixedRowsBottom,10),o=r.getNearestNotHiddenIndex(t.countRows()-i-1,-1),s=n.getNearestNotHiddenIndex(t.countCols()-1,-1);e.markSource("keyboard"),e.setRangeStart(t._createCellCoords(o,s)),e.markEndSource()}},yL={name:"moveCellSelectionToMostInlineEnd",callback(t){const{selection:e,columnIndexMapper:r}=t;e.markSource("keyboard"),e.setRangeStart(t._createCellCoords(t.getSelectedRangeLast().highlight.row,r.getNearestNotHiddenIndex(t.countCols()-1,-1))),e.markEndSource()}},CL={name:"moveCellSelectionToMostInlineStart",callback(t){const{selection:e,columnIndexMapper:r}=t,n=parseInt(t.getSettings().fixedColumnsStart,10),i=t.getSelectedRangeLast().highlight.row,o=r.getNearestNotHiddenIndex(n,1);e.markSource("keyboard"),e.setRangeStart(t._createCellCoords(i,o)),e.markEndSource()}},bL={name:"moveCellSelectionToMostLeft",callback(t){const{selection:e,columnIndexMapper:r}=t,n=t.getSelectedRangeLast().highlight.row;let i=r.getNearestNotHiddenIndex(...t.isRtl()?[t.countCols()-1,-1]:[0,1]);i===null&&(i=t.isRtl()?-1:-t.countRowHeaders()),e.markSource("keyboard"),e.setRangeStart(t._createCellCoords(n,i)),e.markEndSource()}},SL={name:"moveCellSelectionToMostRight",callback(t){const{selection:e,columnIndexMapper:r}=t,{row:n}=t.getSelectedRangeLast().highlight;let i=r.getNearestNotHiddenIndex(...t.isRtl()?[0,1]:[t.countCols()-1,-1]);i===null&&(i=t.isRtl()?-t.countRowHeaders():-1),e.markSource("keyboard"),e.setRangeStart(t._createCellCoords(n,i)),e.markEndSource()}},RL={name:"moveCellSelectionToMostTop",callback(t){const{selection:e}=t,{col:r}=t.getSelectedRangeLast().highlight;let n=t.rowIndexMapper.getNearestNotHiddenIndex(0,1);n===null&&(n=-t.countColHeaders()),e.markSource("keyboard"),e.setRangeStart(t._createCellCoords(n,r)),e.markEndSource()}},TL={name:"moveCellSelectionToMostTopInlineStart",callback(t){const{selection:e,rowIndexMapper:r,columnIndexMapper:n}=t,i=parseInt(t.getSettings().fixedRowsTop,10),o=parseInt(t.getSettings().fixedColumnsStart,10),s=r.getNearestNotHiddenIndex(i,1),l=n.getNearestNotHiddenIndex(o,1);e.markSource("keyboard"),e.setRangeStart(t._createCellCoords(s,l)),e.markEndSource()}},EL={name:"moveCellSelectionUp",callback(t){let{selection:e}=t;e.markSource("keyboard"),e.transformStart(-1,0),e.markEndSource()}},OL={name:"moveCellSelectionUpByViewportHight",callback(t){const{selection:e}=t,{navigableHeaders:r}=t.getSettings(),n=r?t.countColHeaders():0,{row:i}=t.getSelectedRangeLast().highlight;let o=t.countVisibleRows()+n;o=o===0?-1:-o,i===-n?o=-1:i+o<n&&(o=-(i+n)),e.markSource("keyboard"),e.transformStart(o,0),e.markEndSource(),t.getSelectedRangeLast().highlight.row<0&&t.scrollViewportTo({row:0})}};function HL(){return[hL,dL,fL,gL,mL,pL,wL,vL,yL,CL,bL,SL,RL,TL,EL,OL]}const IL={name:"emptySelectedCells",callback(t){t.emptySelectedCells(),t._getEditorManager().prepareEditor()}},ML={name:"scrollToFocusedCell",callback(t){const{highlight:e}=t.getSelectedRangeLast(),r=t.getFirstFullyVisibleRow()-1,n=t.getFirstFullyVisibleColumn()-1,i=t.getLastFullyVisibleRow()+1,o=t.getLastFullyVisibleColumn()+1,s=t._createCellCoords(r,n),l=t._createCellCoords(i,o);if(!t._createCellRange(s,s,l).includes(e)&&(e.row>=0||e.col>=0)){const c={};if(e.col>=0){const d=Math.floor(t.countVisibleCols()/2);c.col=Math.max(e.col-d,0)}if(e.row>=0){const d=Math.floor(t.countVisibleRows()/2);c.row=Math.max(e.row-d,0)}t.scrollViewportTo({...c,verticalSnap:"top",horizontalSnap:"start"})}}},xL={name:"selectAllCells",callback(t){let{selection:e}=t;e.markSource("keyboard"),e.selectAll(!0,!0,{disableHeadersHighlight:!0}),e.markEndSource()}},_L={name:"selectAllCellsAndHeaders",callback(t){let{selection:e}=t;e.markSource("keyboard"),e.selectAll(!0,!0,{disableHeadersHighlight:!1}),e.markEndSource()}},PL={name:"populateSelectedCellsData",callback(t){const e=t.getSelectedRange(),{row:r,col:n}=e[e.length-1].highlight.normalize(),i=t.getDataAtCell(r,n),o=new Map;for(let s=0;s<e.length;s++)e[s].forAll((l,a)=>{if(l>=0&&a>=0&&(l!==r||a!==n)){const{readOnly:c}=t.getCellMeta(l,a);c||o.set("".concat(l,"x").concat(a),[l,a,i])}});t.setDataAtCell(Array.from(o.values()))}},AL=[...X1(),...uL(),...HL(),IL,ML,xL,_L,PL];function hv(t){const e={};return AL.forEach(r=>{let{name:n,callback:i}=r;e[n]=function(){for(var o=arguments.length,s=new Array(o),l=0;l<o;l++)s[l]=arguments[l];return i(t,...s)}}),e}function NL(t){const e=t.getShortcutManager().addContext("editor"),r=hv(t),n={group:cv};e.addShortcuts([{keys:[["Enter"],["Enter","Shift"]],callback:(i,o)=>r.editorCloseAndSaveByEnter(i,o)},{keys:[["Enter","Control/Meta"],["Enter","Control/Meta","Shift"]],captureCtrl:!0,callback:(i,o)=>r.editorCloseAndSaveByEnter(i,o)},{keys:[["Tab"],["Tab","Shift"],["PageDown"],["PageUp"]],forwardToContext:t.getShortcutManager().getContext("grid"),callback:(i,o)=>r.editorCloseAndSave(i,o)},{keys:[["ArrowDown"],["ArrowUp"],["ArrowLeft"],["ArrowRight"]],preventDefault:!1,callback:(i,o)=>r.editorCloseAndSaveByArrowKeys(i,o)},{keys:[["Escape"],["Escape","Control/Meta"]],callback:()=>r.editorCloseWithoutSaving()}],n)}function $L(t){const e=t.getShortcutManager().addContext("grid"),r=hv(t),n={runOnlyIf:()=>{const{navigableHeaders:i}=t.getSettings();return X(t.getSelected())&&(i||!i&&t.countRenderedRows()>0&&t.countRenderedCols()>0)},group:av};e.addShortcuts([{keys:[["F2"]],callback:i=>r.editorFastOpen(i)},{keys:[["Enter"],["Enter","Shift"]],callback:(i,o)=>r.editorOpen(i,o)},{keys:[["Backspace"],["Delete"]],callback:()=>r.emptySelectedCells()}],{group:cv,runOnlyIf:()=>X(t.getSelected())}),e.addShortcuts([{keys:[["Control/Meta","A"]],callback:()=>r.selectAllCells(),runOnlyIf:()=>{var i;return!((i=t.getSelectedRangeLast())!==null&&i!==void 0&&i.highlight.isHeader())}},{keys:[["Control/Meta","A"]],callback:()=>{},runOnlyIf:()=>{var i;return(i=t.getSelectedRangeLast())===null||i===void 0?void 0:i.highlight.isHeader()},preventDefault:!0},{keys:[["Control/Meta","Shift","Space"]],callback:()=>r.selectAllCellsAndHeaders()},{keys:[["Control/Meta","Enter"]],callback:()=>r.populateSelectedCellsData(),runOnlyIf:()=>{var i,o;return!((i=t.getSelectedRangeLast())!==null&&i!==void 0&&i.highlight.isHeader())&&((o=t.getSelectedRangeLast())===null||o===void 0?void 0:o.getCellsCount())>1}},{keys:[["Control","Space"]],captureCtrl:!0,callback:()=>r.extendCellsSelectionToColumns()},{keys:[["Shift","Space"]],stopPropagation:!0,callback:()=>r.extendCellsSelectionToRows()},{keys:[["ArrowUp"]],callback:()=>r.moveCellSelectionUp()},{keys:[["ArrowUp","Control/Meta"]],captureCtrl:!0,callback:()=>r.moveCellSelectionToMostTop()},{keys:[["ArrowUp","Shift"]],callback:()=>r.extendCellsSelectionUp()},{keys:[["ArrowUp","Shift","Control/Meta"]],captureCtrl:!0,callback:()=>r.extendCellsSelectionToMostTop(),runOnlyIf:()=>!(t.selection.isSelectedByCorner()||t.selection.isSelectedByColumnHeader())},{keys:[["ArrowDown"]],callback:()=>r.moveCellSelectionDown()},{keys:[["ArrowDown","Control/Meta"]],captureCtrl:!0,callback:()=>r.moveCellSelectionToMostBottom()},{keys:[["ArrowDown","Shift"]],callback:()=>r.extendCellsSelectionDown()},{keys:[["ArrowDown","Shift","Control/Meta"]],captureCtrl:!0,callback:()=>r.extendCellsSelectionToMostBottom(),runOnlyIf:()=>!(t.selection.isSelectedByCorner()||t.selection.isSelectedByColumnHeader())},{keys:[["ArrowLeft"]],callback:()=>r.moveCellSelectionLeft()},{keys:[["ArrowLeft","Control/Meta"]],captureCtrl:!0,callback:()=>r.moveCellSelectionToMostLeft()},{keys:[["ArrowLeft","Shift"]],callback:()=>r.extendCellsSelectionLeft()},{keys:[["ArrowLeft","Shift","Control/Meta"]],captureCtrl:!0,callback:()=>r.extendCellsSelectionToMostLeft(),runOnlyIf:()=>!(t.selection.isSelectedByCorner()||t.selection.isSelectedByRowHeader())},{keys:[["ArrowRight"]],callback:()=>r.moveCellSelectionRight()},{keys:[["ArrowRight","Control/Meta"]],captureCtrl:!0,callback:()=>r.moveCellSelectionToMostRight()},{keys:[["ArrowRight","Shift"]],callback:()=>r.extendCellsSelectionRight()},{keys:[["ArrowRight","Shift","Control/Meta"]],captureCtrl:!0,callback:()=>r.extendCellsSelectionToMostRight(),runOnlyIf:()=>!(t.selection.isSelectedByCorner()||t.selection.isSelectedByRowHeader())},{keys:[["Home"]],captureCtrl:!0,callback:()=>r.moveCellSelectionToMostInlineStart(),runOnlyIf:()=>t.view.isMainTableNotFullyCoveredByOverlays()},{keys:[["Home","Shift"]],callback:()=>r.extendCellsSelectionToMostInlineStart()},{keys:[["Home","Control/Meta"]],captureCtrl:!0,callback:()=>r.moveCellSelectionToMostTopInlineStart(),runOnlyIf:()=>t.view.isMainTableNotFullyCoveredByOverlays()},{keys:[["End"]],captureCtrl:!0,callback:()=>r.moveCellSelectionToMostInlineEnd(),runOnlyIf:()=>t.view.isMainTableNotFullyCoveredByOverlays()},{keys:[["End","Shift"]],callback:()=>r.extendCellsSelectionToMostInlineEnd()},{keys:[["End","Control/Meta"]],captureCtrl:!0,callback:()=>r.moveCellSelectionToMostBottomInlineEnd(),runOnlyIf:()=>t.view.isMainTableNotFullyCoveredByOverlays()},{keys:[["PageUp"]],callback:()=>r.moveCellSelectionUpByViewportHight()},{keys:[["PageUp","Shift"]],callback:()=>r.extendCellsSelectionUpByViewportHeight()},{keys:[["PageDown"]],callback:()=>r.moveCellSelectionDownByViewportHeight()},{keys:[["PageDown","Shift"]],callback:()=>r.extendCellsSelectionDownByViewportHeight()},{keys:[["Tab"]],preventDefault:!1,callback:i=>r.moveCellSelectionInlineStart(i)},{keys:[["Shift","Tab"]],preventDefault:!1,callback:i=>r.moveCellSelectionInlineEnd(i)},{keys:[["Control/Meta","Backspace"]],callback:()=>r.scrollToFocusedCell()}],n)}function LL(t){[$L,NL].forEach(e=>e(t))}function DL(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=t.rootElement,n=Qd(t),i=Qd(t);return n.addEventListener("focus",()=>e==null?void 0:e.onFocusFromTop()),i.addEventListener("focus",()=>e==null?void 0:e.onFocusFromBottom()),r.firstChild.before(n),r.lastChild.after(i),{activate(){t._registerTimeout(()=>{n.tabIndex=0,i.tabIndex=0},10)},deactivate(){t._registerTimeout(()=>{n.tabIndex=-1,i.tabIndex=-1},10)}}}function Qd(t){const r=t.rootDocument.createElement("input");return r.type="text",r.name="__htFocusCatcher",r.classList.add("htFocusCatcher"),t.getSettings().ariaTags&&de(r,[xH("Focus catcher")]),r}function VL(t){const e=kL(t);let r;const{activate:n,deactivate:i}=DL(t,{onFocusFromTop(){var f;const m=(f=e(r))!==null&&f!==void 0?f:Da(t);m&&(t.runHooks("modifyFocusOnTabNavigation","from_above",m),t.selectCell(m.row,m.col)),t.listen()},onFocusFromBottom(){var f;const m=(f=e(r))!==null&&f!==void 0?f:Va(t);m&&(t.runHooks("modifyFocusOnTabNavigation","from_below",m),t.selectCell(m.row,m.col)),t.listen()}}),o={wrapped:!1,flipped:!1};let s=!0,l=!1,a=!1;t.addHook("afterListen",()=>i()),t.addHook("afterUnlisten",()=>n()),t.addHook("afterSelection",(f,m,p,v,R)=>{if(l&&(o.wrapped&&o.flipped||a)&&(a=!1,R.value=!0),s){var H;r=(H=t.getSelectedRangeLast())===null||H===void 0?void 0:H.highlight}}),t.addHook("beforeRowWrap",(f,m,p)=>{o.wrapped=!0,o.flipped=p});function c(){o.wrapped=!1,o.flipped=!1,t.deselectCell(),t.unlisten()}const d={keys:[["Tab"],["Shift","Tab"]],preventDefault:!1,stopPropagation:!1,relativeToGroup:av,group:"focusCatcher"};t.getShortcutManager().getContext("grid").addShortcuts([{...d,callback:()=>{const{tabNavigation:f}=t.getSettings();l=!0,t.getSelectedRangeLast()&&!f&&(s=!1),f||(a=!0)},position:"before"},{...d,callback:f=>{const{tabNavigation:m,autoWrapRow:p}=t.getSettings();if(l=!1,s=!0,!m||!t.selection.isSelected()||p&&o.wrapped&&o.flipped||!p&&o.wrapped)return p&&o.wrapped&&o.flipped&&(r=f.shiftKey?Da(t):Va(t)),c(),!1;f.preventDefault()},position:"after"}])}function Da(t){const{rowIndexMapper:e,columnIndexMapper:r}=t,{navigableHeaders:n}=t.getSettings();let i=n&&t.countColHeaders()>0?-t.countColHeaders():0,o=n&&t.countRowHeaders()>0?-t.countRowHeaders():0;return i===0&&(i=e.getVisualFromRenderableIndex(i)),o===0&&(o=r.getVisualFromRenderableIndex(o)),i===null||o===null?null:t._createCellCoords(i,o)}function Va(t){var e,r;const{rowIndexMapper:n,columnIndexMapper:i}=t,{navigableHeaders:o}=t.getSettings();let s=n.getRenderableIndexesLength()-1,l=i.getRenderableIndexesLength()-1;if(s<0){if(!o||t.countColHeaders()===0)return null;s=-1}if(l<0){if(!o||t.countColHeaders()===0)return null;l=-1}return t._createCellCoords((e=n.getVisualFromRenderableIndex(s))!==null&&e!==void 0?e:s,(r=i.getVisualFromRenderableIndex(l))!==null&&r!==void 0?r:l)}function kL(t){return e=>{if(!e)return null;const r=Da(t),n=Va(t);return e.col<r.col&&(e.col=r.col),e.col>n.col&&(e.col=n.col),e.row<r.row&&(e.row=r.row),e.row>n.row&&(e.row=n.row),e}}function Jn(t){xs(t)&&t.scrollIntoView({block:"nearest",inline:"nearest"})}function $s(t){const{selection:e,view:r}=t,n=t.getSelectedRangeLast(),i=e.getSelectionSource(),o=r.getFirstFullyVisibleColumn(),s=r.getLastFullyVisibleColumn(),l=n.getTopStartCorner().col,a=n.getBottomEndCorner().col,c=l<=o,d=a>=s,f=r.getFirstFullyVisibleRow(),m=r.getLastFullyVisibleRow(),p=n.getTopStartCorner().row,v=n.getBottomEndCorner().row,R=p<=f,H=v>=m;return{getComputedColumnTarget(M){return i==="mouse"||i==="keyboard"?M.col:c&&d?n.highlight.col:c?l:d?a:M.col},getComputedRowTarget(M){return i==="mouse"||i==="keyboard"?M.row:R&&H?n.highlight.row:R?p:H?v:M.row}}}function FL(t){return e=>{const r=$s(t).getComputedColumnTarget(e);t.scrollViewportTo({col:r},()=>{const n=!!t.getSettings().colHeaders;Jn(t.getCell(n?-1:0,r,!0))})}}function BL(){return()=>{}}function WL(t){return e=>{t.scrollViewportTo(e.toObject(),()=>{const{row:r,col:n}=t.getSelectedRangeLast().highlight;Jn(t.getCell(r,n,!0))})}}function jL(t){return e=>{const r=$s(t),n={row:r.getComputedRowTarget(e),col:r.getComputedColumnTarget(e)};t.scrollViewportTo(n,()=>{const{row:i,col:o}=n;Jn(t.getCell(i,o,!0))})}}function zL(t){return e=>{const r=$s(t),n={row:r.getComputedRowTarget(e),col:r.getComputedColumnTarget(e)};t.scrollViewportTo(n,()=>{const{row:i,col:o}=n;Jn(t.getCell(i,o,!0))})}}function UL(t){return e=>{const r=$s(t).getComputedRowTarget(e);t.scrollViewportTo({row:r},()=>{const n=!!t.getSettings().rowHeaders;Jn(t.getCell(r,n?-1:0,!0))})}}function GL(t){return e=>{const r=t.selection.getSelectionSource(),{row:n,col:i}=e,o=()=>{Jn(t.getCell(n,i,!0))};if(n<0&&i>=0)t.scrollViewportTo({col:i},o);else if(i<0&&n>=0)t.scrollViewportTo({row:n},o);else{if(r==="mouse"&&(i===t.view.getLastPartiallyVisibleColumn()||n===t.view.getLastPartiallyVisibleRow()))return;t.scrollViewportTo({row:n,col:i},o)}}}function KL(t){const{selection:e}=t;let r=!1,n=!1;return{resume(){n=!1},suspend(){n=!0},skipNextScrollCycle(){r=!0},scrollTo(i){var o;if(r||n){r=!1;return}let s;e.isFocusSelectionChanged()?s=WL(t):e.isSelectedByCorner()?s=BL():e.isSelectedByRowHeader()?s=UL(t):e.isSelectedByColumnHeader()?s=FL(t):e.getSelectedRange().size()===1&&e.isMultiple()?s=jL(t):e.getSelectedRange().size()===1&&!e.isMultiple()?s=GL(t):e.getSelectedRange().size()>1&&(s=zL(t)),(o=s)===null||o===void 0||o(i)}}}const Zd=new Map([[" ","space"],["spacebar","space"],["scroll","scrolllock"],["del","delete"],["esc","escape"],["medianexttrack","mediatracknext"],["mediaprevioustrack","mediatrackprevious"],["volumeup","audiovolumeup"],["volumedown","audiovolumedown"],["volumemute","audiovolumemute"],["multiply","*"],["add","+"],["divide","/"],["subtract","-"],["left","arrowleft"],["right","arrowright"],["up","arrowup"],["down","arrowdown"]]),Xi=t=>t.map(e=>{const r=e.toLowerCase();return Zd.has(r)?Zd.get(r):r}).sort().join("+"),YL=t=>t.split("+"),Jd=new Map([[96,"numpad0"],[97,"numpad1"],[98,"numpad2"],[99,"numpad3"],[100,"numpad4"],[101,"numpad5"],[102,"numpad6"],[103,"numpad7"],[104,"numpad8"],[105,"numpad9"],[106,"multiply"],[107,"add"],[108,"decimal"],[109,"subtract"],[110,"decimal"],[111,"divide"],[112,"f1"],[113,"f2"],[114,"f3"],[115,"f4"],[116,"f5"],[117,"f6"],[118,"f7"],[119,"f8"],[120,"f9"],[121,"f10"],[122,"f11"],[123,"f12"],[186,"semicolon"],[187,"equal"],[188,"comma"],[189,"minus"],[190,"period"],[191,"slash"],[192,"backquote"],[219,"bracketleft"],[220,"backslash"],[221,"bracketright"],[222,"quote"]]),oa=t=>{let{which:e,key:r}=t;if(Jd.has(e))return Jd.get(e);const n=String.fromCharCode(e).toLowerCase();return/^[a-z0-9]$/.test(n)?n:r.toLowerCase()},dv=Symbol("shortcut-context");function fv(t){return dr(t)&&t.__kindOf===dv}var bf;const XL=t=>{const e=As({errorIdExists:a=>'The "'.concat(a,'" shortcut is already registered in the "').concat(t,'" context.')}),r=function(){let{keys:a,callback:c,group:d,runOnlyIf:f=()=>!0,captureCtrl:m=!1,preventDefault:p=!0,stopPropagation:v=!1,relativeToGroup:R,position:H,forwardToContext:M}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(ct(d))throw new Error("You need to define the shortcut's group.");if(Z(c)===!1)throw new Error("The shortcut's callback needs to be a function.");if(Array.isArray(a)===!1)throw new Error(vt(bf||(bf=ot(["Pass the shortcut's keys as an array of arrays, \n      using the KeyboardEvent.key properties: \n      https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key/Key_Values."],["Pass the shortcut\\'s keys as an array of arrays,\\x20\n      using the KeyboardEvent.key properties:\\x20\n      https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key/Key_Values."]))));const b={callback:c,group:d,runOnlyIf:f,captureCtrl:m,preventDefault:p,stopPropagation:v};X(R)&&(b.relativeToGroup=R,b.position=H),fv(M)&&(b.forwardToContext=M),a.forEach(k=>{const $=Xi(k);if(e.hasItem($)){const D=e.getItem($);let F=D.findIndex(ve=>ve.group===R);F!==-1?H==="before"?F-=1:F+=1:F=D.length,D.splice(F,0,b)}else e.addItem($,[b])})},n=function(a){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};a.forEach(d=>{fe(c,(f,m)=>{Object.prototype.hasOwnProperty.call(d,m)===!1&&(d[m]=c[m])}),r(d)})},i=a=>{const c=Xi(a);e.removeItem(c)};return{__kindOf:dv,addShortcut:r,addShortcuts:n,getShortcuts:a=>{const c=Xi(a),d=e.getItem(c);return X(d)?d.slice():[]},hasShortcut:a=>{const c=Xi(a);return e.hasItem(c)},removeShortcutsByKeys:i,removeShortcutsByGroup:a=>{e.getItems().forEach(d=>{let[f,m]=d;const p=m.filter(v=>v.group!==a);p.length===0?i(YL(f)):(m.length=0,m.push(...p))})}}};function qL(){const t=new Set;return{press(e){t.add(e)},release(e){t.delete(e)},releaseAll(){t.clear()},isPressed(e){return t.has(e)}}}const QL=["meta","alt","shift","control"],Io=qL(),Mo=[];let qi=0;function ZL(t,e,r,n,i){const o=p=>QL.includes(p),s=function(p){let v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const R=[];return p.altKey&&R.push("alt"),v&&(p.ctrlKey||p.metaKey)?R.push("control/meta"):(p.ctrlKey&&R.push("control"),p.metaKey&&R.push("meta")),p.shiftKey&&R.push("shift"),R},l=p=>{if(e(p)===!1||r(p)===!1||p.keyCode===229||typeof p.key!="string"||sn(p))return;const R=oa(p);let H=[];o(R)||(H=s(p));const M=[R].concat(H);!i(p,M)&&(jh()&&H.includes("meta")||!jh()&&H.includes("control"))&&i(p,[R].concat(s(p,!0))),n(p)},a=p=>{if(typeof p.key=="string"){const v=oa(p);o(v)&&Io.press(v)}},c=p=>{if(typeof p.key=="string"){const v=oa(p);o(v)&&Io.release(v)}},d=()=>{Io.releaseAll()};return{mount:()=>{let p=t;for(qi+=1;p;)qi===1&&(p.document.documentElement.addEventListener("keydown",a),Mo.push({event:"keydown",listener:a}),p.document.documentElement.addEventListener("keyup",c),Mo.push({event:"keyup",listener:c})),p.document.documentElement.addEventListener("keydown",l),p.document.documentElement.addEventListener("blur",d),p=fs(p)},unmount:()=>{let p=t;for(qi-=1;p;){if(qi===0){for(let v=0;v<Mo.length;v++){const{event:R,listener:H}=Mo[v];p.document.documentElement.removeEventListener(R,H)}Mo.length=0}p.document.documentElement.removeEventListener("keydown",l),p.document.documentElement.removeEventListener("blur",d),p=fs(p)}},isPressed:p=>Io.isPressed(p),releasePressedKeys:()=>Io.releaseAll()}}var Sf;const JL=t=>{let{ownerWindow:e,handleEvent:r,beforeKeyDown:n,afterKeyDown:i}=t;const o=As({errorIdExists:v=>'The "'.concat(v,'" context name is already registered.')});let s="grid";const l=v=>{const R=XL(v);return o.addItem(v,R),R},a=()=>s,c=v=>o.getItem(v),d=v=>{if(!o.hasItem(v))throw new Error(vt(Sf||(Sf=ot(["You've tried to activate the \"",'" shortcut context \n        that does not exist. Before activation, register the context using the "addContext" method.'],["You've tried to activate the \"",'" shortcut context\\x20\n        that does not exist. Before activation, register the context using the "addContext" method.'])),v));s=v};let f=!1;const m=function(v,R){let H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:a();const M=fv(H)?H:c(H);let b=!1;if(!M.hasShortcut(R))return b;const k=M.getShortcuts(R);for(let $=0;$<k.length;$++){const{callback:W,runOnlyIf:D,preventDefault:F,stopPropagation:ve,captureCtrl:V,forwardToContext:St}=k[$];if(D(v)===!0){if(f=V,b=W(v,R)===!1,f=!1,F&&v.preventDefault(),ve&&(Ps(v),v.stopPropagation()),b)break;St&&m(v,R,St)}}return b},p=ZL(e,r,n,i,m);return p.mount(),{addContext:l,getActiveContextName:a,getContext:c,setActiveContextName:d,isCtrlPressed:()=>!f&&(p.isPressed("control")||p.isPressed("meta")),releasePressedKeys:()=>p.releasePressedKeys(),destroy:()=>p.unmount()}};function ef(t){const{classNames:e}=$H(t,/ht-theme-[a-zA-Z0-9_-]+/);return e.pop()}let ia=null;const sa=new Map;function gv(t,e){var r,n=this;let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,o=this;const s=new Un(o);let l,a,c,d,f,m,p=!0;dN(i)&&hN(this),this.rootElement=t,this.rootDocument=t.ownerDocument,this.rootWindow=this.rootDocument.defaultView,this.isDestroyed=!1,this.renderSuspendedCounter=0,this.executionSuspendedCounter=0;const v=(r=e==null?void 0:e.layoutDirection)!==null&&r!==void 0?r:"inherit",R=["rtl","ltr"].includes(v)?v:this.rootWindow.getComputedStyle(this.rootElement).direction;this.rootElement.setAttribute("dir",R),this.isRtl=function(){return R==="rtl"},this.isLtr=function(){return!o.isRtl()},this.getDirectionFactor=function(){return o.isLtr()?1:-1},e.language=E$(e.language);const H=Object.fromEntries(Object.entries(e).filter(u=>{let[h]=u;return!(Le.getSingleton().isRegistered(h)||Le.getSingleton().isDeprecated(h))})),M=new lv(o,H,[x1,D1]),b=M.getTableMeta(),k=M.getGlobalMeta(),$=As();this.container=this.rootDocument.createElement("div"),t.insertBefore(this.container,t.firstChild),is(this)&&(CH(e.licenseKey,t),G(t,"ht-wrapper")),this.guid="ht_".concat(rm()),sa.set(this.guid,this),this.columnIndexMapper=new $a,this.rowIndexMapper=new $a,this.columnIndexMapper.addLocalHook("indexesSequenceChange",u=>{o.runHooks("afterColumnSequenceChange",u)}),this.rowIndexMapper.addLocalHook("indexesSequenceChange",u=>{o.runHooks("afterRowSequenceChange",u)}),s.addEventListener(this.rootDocument.documentElement,"compositionstart",u=>{o.runHooks("beforeCompositionStart",u)}),a=new AN(o),(!this.rootElement.id||this.rootElement.id.substring(0,3)==="ht_")&&(this.rootElement.id=this.guid);const W=u=>{const{row:h,col:g}=u;return o._createCellCoords(h>=0?o.rowIndexMapper.getRenderableFromVisualIndex(h):h,g>=0?o.columnIndexMapper.getRenderableFromVisualIndex(g):g)},D=u=>{const{row:h,col:g}=u;return o._createCellCoords(h>=0?o.rowIndexMapper.getVisualFromRenderableIndex(h):h,g>=0?o.columnIndexMapper.getVisualFromRenderableIndex(g):g)},F=(u,h)=>{const g=h>u?1:-1,w=Math.min(u,h),C=Math.max(u,h),S=o.rowIndexMapper.getNearestNotHiddenIndex(u,g);return S===null||g===1&&S>C||g===-1&&S<w?null:S>=0?o.rowIndexMapper.getRenderableFromVisualIndex(S):S},ve=(u,h)=>{const g=h>u?1:-1,w=Math.min(u,h),C=Math.max(u,h),S=o.columnIndexMapper.getNearestNotHiddenIndex(u,g);return S===null||g===1&&S>C||g===-1&&S<w?null:S>=0?o.columnIndexMapper.getRenderableFromVisualIndex(S):S};let V=new e1(b,{rowIndexMapper:o.rowIndexMapper,columnIndexMapper:o.columnIndexMapper,countCols:()=>o.countCols(),countRows:()=>o.countRows(),propToCol:u=>l.propToCol(u),isEditorOpened:()=>o.getActiveEditor()?o.getActiveEditor().isOpened():!1,countRenderableColumns:()=>this.view.countRenderableColumns(),countRenderableRows:()=>this.view.countRenderableRows(),countRowHeaders:()=>this.countRowHeaders(),countColHeaders:()=>this.countColHeaders(),countRenderableRowsInRange:function(){return n.view.countRenderableRowsInRange(...arguments)},countRenderableColumnsInRange:function(){return n.view.countRenderableColumnsInRange(...arguments)},getShortcutManager:()=>o.getShortcutManager(),createCellCoords:(u,h)=>o._createCellCoords(u,h),createCellRange:(u,h,g)=>o._createCellRange(u,h,g),visualToRenderableCoords:W,renderableToVisualCoords:D,findFirstNonHiddenRenderableRow:F,findFirstNonHiddenRenderableColumn:ve,isDisabledCellSelection:(u,h)=>u<0||h<0?o.getSettings().disableVisualSelection:o.getCellMeta(u,h).disableVisualSelection});this.selection=V;const St=u=>{let{hiddenIndexesChanged:h}=u;this.forceFullRender=!0,h&&this.selection.commit()};this.columnIndexMapper.addLocalHook("cacheUpdated",St),this.rowIndexMapper.addLocalHook("cacheUpdated",St),this.selection.addLocalHook("afterSetRangeEnd",(u,h)=>{const g=wt(!1),w=this.selection.getSelectedRange(),{from:C,to:S}=w.current(),E=w.size()-1;this.runHooks("afterSelection",C.row,C.col,S.row,S.col,g,E),this.runHooks("afterSelectionByProp",C.row,o.colToProp(C.col),S.row,o.colToProp(S.col),g,E),h&&(!g.isTouched()||g.isTouched()&&!g.value)&&m.scrollTo(u);const O=V.isSelectedByRowHeader(),A=V.isSelectedByColumnHeader();O&&A?G(this.rootElement,["ht__selection--rows","ht__selection--columns"]):O?(ce(this.rootElement,"ht__selection--columns"),G(this.rootElement,"ht__selection--rows")):A?(ce(this.rootElement,"ht__selection--rows"),G(this.rootElement,"ht__selection--columns")):ce(this.rootElement,["ht__selection--rows","ht__selection--columns"]),V.getSelectionSource()!=="shift"&&d.closeEditor(null),o.view.render(),d.prepareEditor()}),this.selection.addLocalHook("beforeSetFocus",u=>{this.runHooks("beforeSelectionFocusSet",u.row,u.col)}),this.selection.addLocalHook("afterSetFocus",u=>{const h=wt(!1);this.runHooks("afterSelectionFocusSet",u.row,u.col,h),(!h.isTouched()||h.isTouched()&&!h.value)&&m.scrollTo(u),d.closeEditor(),o.view.render(),d.prepareEditor()}),this.selection.addLocalHook("afterSelectionFinished",u=>{const h=u.length-1,{from:g,to:w}=u[h];this.runHooks("afterSelectionEnd",g.row,g.col,w.row,w.col,h),this.runHooks("afterSelectionEndByProp",g.row,o.colToProp(g.col),w.row,o.colToProp(w.col),h)}),this.selection.addLocalHook("afterIsMultipleSelection",u=>{const h=this.runHooks("afterIsMultipleSelection",u.value);u.value&&(u.value=h)}),this.selection.addLocalHook("afterDeselect",()=>{d.closeEditor(),o.view.render(),ce(this.rootElement,["ht__selection--rows","ht__selection--columns"]),this.runHooks("afterDeselect")}),this.selection.addLocalHook("beforeHighlightSet",()=>this.runHooks("beforeSelectionHighlightSet")).addLocalHook("beforeSetRangeStart",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("beforeSetRangeStart",...h)}).addLocalHook("beforeSetRangeStartOnly",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("beforeSetRangeStartOnly",...h)}).addLocalHook("beforeSetRangeEnd",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("beforeSetRangeEnd",...h)}).addLocalHook("beforeSelectColumns",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("beforeSelectColumns",...h)}).addLocalHook("afterSelectColumns",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("afterSelectColumns",...h)}).addLocalHook("beforeSelectRows",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("beforeSelectRows",...h)}).addLocalHook("afterSelectRows",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("afterSelectRows",...h)}).addLocalHook("beforeModifyTransformStart",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("modifyTransformStart",...h)}).addLocalHook("afterModifyTransformStart",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("afterModifyTransformStart",...h)}).addLocalHook("beforeModifyTransformFocus",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("modifyTransformFocus",...h)}).addLocalHook("afterModifyTransformFocus",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("afterModifyTransformFocus",...h)}).addLocalHook("beforeModifyTransformEnd",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("modifyTransformEnd",...h)}).addLocalHook("afterModifyTransformEnd",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("afterModifyTransformEnd",...h)}).addLocalHook("beforeRowWrap",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("beforeRowWrap",...h)}).addLocalHook("beforeColumnWrap",function(){for(var u=arguments.length,h=new Array(u),g=0;g<u;g++)h[g]=arguments[g];return n.runHooks("beforeColumnWrap",...h)}).addLocalHook("insertRowRequire",u=>this.alter("insert_row_above",u,1,"auto")).addLocalHook("insertColRequire",u=>this.alter("insert_col_start",u,1,"auto")),c={alter(u,h){let g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,w=arguments.length>3?arguments[3]:void 0,C=arguments.length>4?arguments[4]:void 0;const S=E=>{if(E.length===0)return[];const O=[...E];return O.sort((x,T)=>{let[j]=x,[K]=T;return j===K?0:j>K?1:-1}),Hs(O,(x,T)=>{let[j,K]=T;const Q=x[x.length-1],[Re,ge]=Q,ie=Re+ge;if(j<=ie){const Je=Math.max(K-(ie-j),0);Q[1]+=Je}else x.push([j,K]);return x},[O[0]])};switch(u){case"insert_row_below":case"insert_row_above":const E=o.countSourceRows();if(b.maxRows===E)return;const O=u==="insert_row_below"?"below":"above";h=h!=null?h:O==="below"?E:0;const{delta:A,startPhysicalIndex:x}=l.createRow(h,g,{source:w,mode:O});V.shiftRows(o.toVisualRow(x),A);break;case"insert_col_start":case"insert_col_end":const T=u==="insert_col_end"?"end":"start";h=h!=null?h:T==="end"?o.countSourceCols():0;const{delta:j,startPhysicalIndex:K}=l.createCol(h,g,{source:w,mode:T});if(j){if(Array.isArray(b.colHeaders)){const ge=[o.toVisualColumn(K),0];ge.length+=j,Array.prototype.splice.apply(b.colHeaders,ge)}V.shiftColumns(o.toVisualColumn(K),j)}break;case"remove_row":const Q=ge=>{let ie=0;U(ge,Je=>{let[se,_e]=Je;const ft=jn(se)?o.countRows()-1:Math.max(se-ie,0);if(Number.isInteger(se)&&(se=Math.max(se-ie,0)),!l.removeRow(se,_e,w))return;if(V.isSelected()){const{row:Et}=o.getSelectedRangeLast().highlight;Et>=se&&Et<=se+_e-1&&d.closeEditor(!0)}const Tt=o.countRows(),Be=b.fixedRowsTop;Be>=ft+1&&(b.fixedRowsTop-=Math.min(_e,Be-ft));const We=b.fixedRowsBottom;We&&ft>=Tt-We&&(b.fixedRowsBottom-=Math.min(_e,We)),Tt===0?V.deselect():w==="ContextMenu.removeRow"?V.refresh():V.shiftRows(se,-_e),ie+=_e})};Array.isArray(h)?Q(S(h)):Q([[h,g]]);break;case"remove_col":const Re=ge=>{let ie=0;U(ge,Je=>{let[se,_e]=Je;const ft=jn(se)?o.countCols()-1:Math.max(se-ie,0);let jt=o.toPhysicalColumn(ft);if(Number.isInteger(se)&&(se=Math.max(se-ie,0)),!l.removeCol(se,_e,w))return;if(V.isSelected()){const{col:Et}=o.getSelectedRangeLast().highlight;Et>=se&&Et<=se+_e-1&&d.closeEditor(!0)}o.countCols()===0?V.deselect():w==="ContextMenu.removeColumn"?V.refresh():V.shiftColumns(se,-_e);const We=b.fixedColumnsStart;We>=ft+1&&(b.fixedColumnsStart-=Math.min(_e,We-ft)),Array.isArray(b.colHeaders)&&(typeof jt>"u"&&(jt=-1),b.colHeaders.splice(jt,_e)),ie+=_e})};Array.isArray(h)?Re(S(h)):Re([[h,g]]);break;default:throw new Error('There is no such action "'.concat(u,'"'))}C||c.adjustRowsAndCols(),o.view.render(),o.view.adjustElementsSize()},adjustRowsAndCols(){const u=b.minRows,h=b.minSpareRows,g=b.minCols,w=b.minSpareCols;if(u){const C=o.countRows();C<u&&l.createRow(C,u-C,{source:"auto"})}if(h){const C=o.countEmptyRows(!0);if(C<h){const S=h-C,E=Math.min(S,b.maxRows-o.countSourceRows());l.createRow(o.countRows(),E,{source:"auto"})}}{let C;(g||w)&&(C=o.countEmptyCols(!0));let S=o.countCols();if(g&&!b.columns&&S<g){const E=g-S;C+=E,l.createCol(S,E,{source:"auto"})}if(w&&!b.columns&&o.dataType==="array"&&C<w){S=o.countCols();const E=w-C,O=Math.min(E,b.maxCols-S);l.createCol(S,O,{source:"auto"})}}},populateFromArray(u,h,g,w,C){let S,E,O,A;const x=[],T={},j=[],K=u.row,Q=u.col;if(E=h.length,E===0)return!1;let Re=0,ge=0;switch(dr(g)&&(Re=g.col-Q+1,ge=g.row-K+1),C){case"shift_down":const ie=Cl(h),Je=ie.length,se=Math.max(Je,Re),_e=o.getData().slice(K),ft=Cl(_e).slice(Q,Q+se);for(O=0;O<se;O+=1)if(O<Je){for(S=0,E=ie[O].length;S<ge-E;S+=1)ie[O].push(ie[O][S%E]);O<ft.length?j.push(ie[O].concat(ft[O])):j.push(ie[O].concat(new Array(_e.length).fill(null)))}else j.push(ie[O%Je].concat(ft[O]));o.populateFromArray(K,Q,Cl(j));break;case"shift_right":const jt=h.length,Tt=Math.max(jt,ge),Be=o.getData().slice(K).map(et=>et.slice(Q));for(S=0;S<Tt;S+=1)if(S<jt){for(O=0,A=h[S].length;O<Re-A;O+=1)h[S].push(h[S][O%A]);if(S<Be.length)for(let et=0;et<Be[S].length;et+=1)h[S].push(Be[S][et]);else h[S].push(...new Array(Be[0].length).fill(null))}else h.push(h[S%E].slice(0,Tt).concat(Be[S]));o.populateFromArray(K,Q,h);break;case"overwrite":default:T.row=u.row,T.col=u.col;let We=0,Et=0,xr=!0,_r;const Sn=function(tt){let gt=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;const pr=h[tt%h.length];return gt!==null?pr[gt%pr.length]:pr},eo=h.length,Pr=g?g.row-u.row+1:0;for(g?E=Pr:E=Math.max(eo,Pr),S=0;S<E&&!(g&&T.row>g.row&&Pr>eo||!b.allowInsertRow&&T.row>o.countRows()-1||T.row>=b.maxRows);S++){const et=S-We,tt=Sn(et).length,gt=g?g.col-u.col+1:0;if(g?A=gt:A=Math.max(tt,gt),T.col=u.col,_r=o.getCellMeta(T.row,T.col),(w==="CopyPaste.paste"||w==="Autofill.fill")&&_r.skipRowOnPaste){We+=1,T.row+=1,E+=1;continue}for(Et=0,O=0;O<A&&!(g&&T.col>g.col&&gt>tt||!b.allowInsertColumn&&T.col>o.countCols()-1||T.col>=b.maxCols);O++){if(_r=o.getCellMeta(T.row,T.col),(w==="CopyPaste.paste"||w==="Autofill.fill")&&_r.skipColumnOnPaste){Et+=1,T.col+=1,A+=1;continue}if(_r.readOnly&&w!=="UndoRedo.undo"){T.col+=1;continue}const pr=O-Et;let rt=Sn(et,pr),Ot=o.getDataAtCell(T.row,T.col);if(rt!==null&&typeof rt=="object")if(Array.isArray(rt)&&Ot===null&&(Ot=[]),Ot===null||typeof Ot!="object")xr=!1;else{const Gr=Ko(Array.isArray(Ot)?Ot:Ot[0]||Ot),Kr=Ko(Array.isArray(rt)?rt:rt[0]||rt);bm(Gr,Kr)||Array.isArray(Gr)&&Array.isArray(Kr)?rt=Br(rt):xr=!1}else Ot!==null&&typeof Ot=="object"&&(xr=!1);xr&&x.push([T.row,T.col,rt]),xr=!0,T.col+=1}T.row+=1}o.setDataAtCell(x,null,null,w||"populateFromArray");break}}};function te(u){const h=yp(u);Lc(h)?(o.runHooks("beforeLanguageChange",h),k.language=h,o.runHooks("afterLanguageChange",h)):Cp(u)}function Rt(u,h){const g=u==="className"?o.rootElement:o.table;if(p)G(g,h);else{let w=[],C=[];k[u]&&(w=Array.isArray(k[u])?k[u]:Rh(k[u])),h&&(C=Array.isArray(h)?h:Rh(h));const S=Sh(w,C),E=Sh(C,w);S.length&&ce(g,S),E.length&&G(g,E)}k[u]=h}this.init=function(){a.setData(b.data),o.runHooks("beforeInit"),(Vn()||CI())&&G(o.rootElement,"mobile"),this.updateSettings(e,!0),this.view=new yN(this);const u=b.themeName||ef(o.rootElement);o.useTheme(u),o.view.addClassNameToLicenseElement(o.getCurrentThemeName()),d=vM.getInstance(o,b,V),m=KL(o),f=new CM(o),is(this)&&VL(o),o.runHooks("init"),this.render(),p&&o.rootElement.offsetParent===null&&GH(o.rootElement,()=>{o.view._wt.wtOverlays.updateLastSpreaderSize(),o.render(),o.view.adjustElementsSize()}),typeof p=="object"&&(o.runHooks("afterChange",p[0],p[1]),p=!1),o.runHooks("afterInit")};function Ye(){let u=!1;return{validatorsInQueue:0,valid:!0,addValidatorToQueue(){this.validatorsInQueue+=1,u=!1},removeValidatorFormQueue(){this.validatorsInQueue=this.validatorsInQueue-1<0?0:this.validatorsInQueue-1,this.checkIfQueueIsEmpty()},onQueueEmpty(){},checkIfQueueIsEmpty(){this.validatorsInQueue===0&&u===!1&&(u=!0,this.onQueueEmpty(this.valid))}}}function Ft(u){const h=u.replace(",",".");return isNaN(parseFloat(h))===!1?parseFloat(h):u}function Bt(u,h,g){if(!u.length){g();return}const w=o.getActiveEditor(),C=new Ye;let S=!0;C.onQueueEmpty=()=>{w&&S&&w.cancelChanges(),g()};for(let E=u.length-1;E>=0;E--){const[O,A]=u[E],x=l.propToCol(A);let T;Number.isInteger(x)?T=o.getCellMeta(O,x):T={...Object.getPrototypeOf(b),...b},o.getCellValidator(T)&&(C.addValidatorToQueue(),o.validateCell(u[E][3],T,function(j,K){return function(Q){if(typeof Q!="boolean")throw new Error("Validation error: result is not boolean");Q===!1&&K.allowInvalid===!1&&(S=!1,u.splice(j,1),K.valid=!0),C.removeValidatorFormQueue()}}(E,T),h))}C.checkIfQueueIsEmpty()}function Wt(u,h){for(let w=u.length-1;w>=0;w--){let C=!1;if(u[w]===null){u.splice(w,1);continue}if(!((u[w][2]===null||u[w][2]===void 0)&&(u[w][3]===null||u[w][3]===void 0))){if(b.allowInsertRow)for(;u[w][0]>o.countRows()-1;){const{delta:S}=l.createRow(void 0,void 0,{source:"auto"});if(S===0){C=!0;break}}if(o.dataType==="array"&&(!b.columns||b.columns.length===0)&&b.allowInsertColumn)for(;l.propToCol(u[w][1])>o.countCols()-1;){const{delta:S}=l.createCol(void 0,void 0,{source:"auto"});if(S===0){C=!0;break}}C||l.set(u[w][0],u[w][1],u[w][3])}}if(u.length>0){c.adjustRowsAndCols(),o.runHooks("beforeChangeRender",u,h),d.closeEditor(),o.render(),d.prepareEditor(),o.view.adjustElementsSize(),o.runHooks("afterChange",u,h||"edit");const w=o.getActiveEditor();w&&X(w.refreshValue)&&w.refreshValue()}else o.render()}this._createCellCoords=function(u,h){return o.view._wt.createCellCoords(u,h)},this._createCellRange=function(u,h,g){return o.view._wt.createCellRange(u,h,g)},this.validateCell=function(u,h,g,w){let C=o.getCellValidator(h);function S(E){if(!(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0)||h.hidden===!0){g(E);return}const A=h.visualCol,x=h.visualRow,T=o.getCell(x,A,!0);if(T&&T.nodeName!=="TH"){const j=o.rowIndexMapper.getRenderableFromVisualIndex(x),K=o.columnIndexMapper.getRenderableFromVisualIndex(A);o.view._wt.getSetting("cellRenderer",j,K,T)}g(E)}wH(C)&&(C=function(E){return function(O,A){A(E.test(O))}}(C)),Z(C)?(u=o.runHooks("beforeValidate",u,h.visualRow,h.prop,w),o._registerImmediate(()=>{C.call(h,u,E=>{o&&(E=o.runHooks("afterValidate",E,u,h.visualRow,h.prop,w),h.valid=E,S(E),o.runHooks("postAfterValidate",E,u,h.visualRow,h.prop,w))})})):o._registerImmediate(()=>{h.valid=!0,S(h.valid,!1)})};function ue(u,h,g){return Array.isArray(u)?u:[[u,h,g]]}function ye(u,h){const g=o.runHooks("beforeChange",u,h||"edit"),w=u.filter(S=>S!==null);if(g===!1||w.length===0){var C;return(C=o.getActiveEditor())===null||C===void 0||C.cancelChanges(),[]}for(let S=w.length-1;S>=0;S--){const[E,O,,A]=w[S],x=l.propToCol(O);let T;Number.isInteger(x)?T=o.getCellMeta(E,x):T={...Object.getPrototypeOf(b),...b};const{type:j,checkedTemplate:K,uncheckedTemplate:Q}=T;if(j==="numeric"&&typeof A=="string"&&cx(A)&&(w[S][3]=Ft(A)),j==="checkbox"){const Re=Lo(A),ge=Re===Lo(K),ie=Re===Lo(Q);(ge||ie)&&(w[S][3]=ge?K:Q)}}return w}this.setDataAtCell=function(u,h,g,w){const C=ue(u,h,g),S=[];let E=w,O,A,x;for(O=0,A=C.length;O<A;O++){if(typeof C[O]!="object")throw new Error("Method `setDataAtCell` accepts row number or changes array of arrays as its first parameter");if(typeof C[O][1]!="number")throw new Error("Method `setDataAtCell` accepts row and column number as its parameters. If you want to use object property name, use method `setDataAtRowProp`");C[O][1]>=this.countCols()?x=C[O][1]:x=l.colToProp(C[O][1]),S.push([C[O][0],x,a.getAtCell(this.toPhysicalRow(C[O][0]),C[O][1]),C[O][2]])}!E&&typeof u=="object"&&(E=h);const T=ye(S,E);o.runHooks("afterSetDataAtCell",T,E),Bt(T,E,()=>{Wt(T,E)})},this.setDataAtRowProp=function(u,h,g,w){const C=ue(u,h,g),S=[];let E=w,O,A;for(O=0,A=C.length;O<A;O++)S.push([C[O][0],C[O][1],a.getAtCell(this.toPhysicalRow(C[O][0]),C[O][1]),C[O][2]]);!E&&typeof u=="object"&&(E=h);const x=ye(S,w);o.runHooks("afterSetDataAtRowProp",x,E),Bt(x,E,()=>{Wt(x,E)})},this.listen=function(){o&&!o.isListening()&&(sa.forEach(u=>{o!==u&&u.unlisten()}),ia=o.guid,o.runHooks("afterListen"))},this.unlisten=function(){this.isListening()&&(ia=null,o.runHooks("afterUnlisten"))},this.isListening=function(){return ia===o.guid},this.destroyEditor=function(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;d.closeEditor(u),o.view.render(),h&&V.isSelected()&&d.prepareEditor()},this.populateFromArray=function(u,h,g,w,C,S,E){if(!(typeof g=="object"&&typeof g[0]=="object"))throw new Error("populateFromArray parameter `input` must be an array of arrays");const O=typeof w=="number"?o._createCellCoords(w,C):null;return c.populateFromArray(o._createCellCoords(u,h),g,O,S,E)},this.spliceCol=function(u,h,g){for(var w=arguments.length,C=new Array(w>3?w-3:0),S=3;S<w;S++)C[S-3]=arguments[S];return l.spliceCol(u,h,g,...C)},this.spliceRow=function(u,h,g){for(var w=arguments.length,C=new Array(w>3?w-3:0),S=3;S<w;S++)C[S-3]=arguments[S];return l.spliceRow(u,h,g,...C)},this.getSelected=function(){if(V.isSelected())return cn(V.getSelectedRange(),u=>{let{from:h,to:g}=u;return[h.row,h.col,g.row,g.col]})},this.getSelectedLast=function(){const u=this.getSelected();let h;return u&&u.length>0&&(h=u[u.length-1]),h},this.getSelectedRange=function(){if(V.isSelected())return Array.from(V.getSelectedRange())},this.getSelectedRangeLast=function(){const u=this.getSelectedRange();let h;return u&&u.length>0&&(h=u[u.length-1]),h},this.emptySelectedCells=function(u){if(!V.isSelected()||this.countRows()===0||this.countCols()===0)return;const h=[];U(V.getSelectedRange(),g=>{if(g.isSingleHeader())return;const w=g.getTopStartCorner(),C=g.getBottomEndCorner();Nt(w.row,C.row,S=>{Nt(w.col,C.col,E=>{this.getCellMeta(S,E).readOnly||h.push([S,E,null])})})}),h.length>0&&this.setDataAtCell(h,u)},this.isRenderSuspended=function(){return this.renderSuspendedCounter>0},this.suspendRender=function(){this.renderSuspendedCounter+=1},this.resumeRender=function(){const u=this.renderSuspendedCounter-1;this.renderSuspendedCounter=Math.max(u,0),!this.isRenderSuspended()&&u===this.renderSuspendedCounter&&o.view.render()},this.render=function(){this.view&&(this.forceFullRender=!0,this.isRenderSuspended()||o.view.render())},this.batchRender=function(u){this.suspendRender();const h=u();return this.resumeRender(),h},this.isExecutionSuspended=function(){return this.executionSuspendedCounter>0},this.suspendExecution=function(){this.executionSuspendedCounter+=1,this.columnIndexMapper.suspendOperations(),this.rowIndexMapper.suspendOperations()},this.resumeExecution=function(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const h=this.executionSuspendedCounter-1;this.executionSuspendedCounter=Math.max(h,0),(!this.isExecutionSuspended()&&h===this.executionSuspendedCounter||u)&&(this.columnIndexMapper.resumeOperations(),this.rowIndexMapper.resumeOperations())},this.batchExecution=function(u){let h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;this.suspendExecution();const g=u();return this.resumeExecution(h),g},this.batch=function(u){this.suspendRender(),this.suspendExecution();const h=u();return this.resumeExecution(),this.resumeRender(),h},this.refreshDimensions=function(){if(!o.view)return;const u=o.view,{width:h,height:g}=u.getLastSize(),{width:w,height:C}=o.rootElement.getBoundingClientRect(),S=w!==h||C!==g;o.runHooks("beforeRefreshDimensions",{width:h,height:g},{width:w,height:C},S)!==!1&&((S||u._wt.wtOverlays.scrollableElement===o.rootWindow)&&(u.setLastSize(w,C),o.render(),u.adjustElementsSize()),o.runHooks("afterRefreshDimensions",{width:h,height:g},{width:w,height:C},S))},this.updateData=function(u,h){Yd(u,g=>{l=g},g=>{l=g,o.columnIndexMapper.fitToLength(this.getInitialColumnCount()),o.rowIndexMapper.fitToLength(this.countSourceRows()),c.adjustRowsAndCols(),V.refresh()},{hotInstance:o,dataMap:l,dataSource:a,internalSource:"updateData",source:h,metaManager:M,firstRun:p})},this.loadData=function(u,h){Yd(u,g=>{l=g},()=>{M.clearCellsCache(),o.initIndexMappers(),c.adjustRowsAndCols(),V.refresh(),p&&(p=[null,"loadData"])},{hotInstance:o,dataMap:l,dataSource:a,internalSource:"loadData",source:h,metaManager:M,firstRun:p})},this.getInitialColumnCount=function(){const u=b.columns;let h=0;if(Array.isArray(u))h=u.length;else if(Z(u))if(o.dataType==="array"){const g=this.countSourceCols();for(let w=0;w<g;w+=1)u(w)&&(h+=1)}else(o.dataType==="object"||o.dataType==="function")&&(h=l.colToPropCache.length);else if(X(b.dataSchema)){const g=l.getSchema();h=Array.isArray(g)?g.length:hc(g)}else h=this.countSourceCols();return h},this.initIndexMappers=function(){this.columnIndexMapper.initToLength(this.getInitialColumnCount()),this.rowIndexMapper.initToLength(this.countSourceRows())},this.getData=function(u,h,g,w){return ct(u)?l.getAll():l.getRange(o._createCellCoords(u,h),o._createCellCoords(g,w),l.DESTINATION_RENDERER)},this.getCopyableText=function(u,h,g,w){return l.getCopyableText(o._createCellCoords(u,h),o._createCellCoords(g,w))},this.getCopyableData=function(u,h){return l.getCopyable(u,l.colToProp(h))},this.getSchema=function(){return l.getSchema()},this.updateSettings=function(u){let h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const g=(p?o.loadData:o.updateData).bind(this);let w=!1,C,S;if(X(u.rows))throw new Error('The "rows" setting is no longer supported. Do you mean startRows, minRows or maxRows?');if(X(u.cols))throw new Error('The "cols" setting is no longer supported. Do you mean startCols, minCols or maxCols?');if(X(u.ganttChart))throw new Error('Since 8.0.0 the "ganttChart" setting is no longer supported.');u.language&&te(u.language);for(C in u)if(!(C==="data"||C==="language"))if(C==="className")Rt("className",u.className);else if(C==="tableClassName"&&o.table)Rt("tableClassName",u.tableClassName),o.view._wt.wtOverlays.syncOverlayTableClassNames();else if(Le.getSingleton().isRegistered(C)||Le.getSingleton().isDeprecated(C)){const T=u[C];Z(T)?(Le.getSingleton().addAsFixed(C,T,o),b[C]=T):Array.isArray(T)&&(Le.getSingleton().add(C,T,o),b[C]=T)}else!h&&un(u,C)&&(k[C]=u[C]);u.data===void 0&&b.data===void 0?g(null,"updateSettings"):u.data!==void 0?g(u.data,"updateSettings"):u.columns!==void 0&&(l.createMap(),o.initIndexMappers());const E=o.countCols(),O=b.columns;if(O&&Z(O)&&(w=!0),(u.cell!==void 0||u.cells!==void 0||u.columns!==void 0)&&M.clearCache(),E>0)for(C=0,S=0;C<E;C++){if(O){const T=w?O(C):O[S];T&&M.updateColumnMeta(S,T)}S+=1}X(u.cell)&&fe(u.cell,T=>{o.setCellMetaObject(T.row,T.col,T)}),o.runHooks("afterCellMetaReset");let A=o.rootElement.style.height;A!==""&&(A=parseInt(o.rootElement.style.height,10));let x=u.height;if(Z(x)&&(x=x()),h&&o.rootElement.getAttribute("style")&&o.rootElement.setAttribute("data-initialstyle",o.rootElement.getAttribute("style")),x===null){const T=o.rootElement.getAttribute("data-initialstyle");T&&(T.indexOf("height")>-1||T.indexOf("overflow")>-1)?o.rootElement.setAttribute("style",T):(o.rootElement.style.height="",o.rootElement.style.overflow="")}else x!==void 0&&(o.rootElement.style.height=isNaN(x)?"".concat(x):"".concat(x,"px"),o.rootElement.style.overflow="hidden");if(typeof u.width<"u"){let T=u.width;Z(T)&&(T=T()),o.rootElement.style.width=isNaN(T)?"".concat(T):"".concat(T,"px")}if(!h){if(o.view){o.view._wt.wtViewport.resetHasOversizedColumnHeadersMarked(),o.view._wt.exportSettingsAsClassNames();const T=o.getCurrentThemeName(),j=un(u,"themeName");T&&j&&T!==u.themeName&&(o.view.getStylesHandler().removeClassNames(),o.view.removeClassNameFromLicenseElement(T));const K=j&&u.themeName||ef(o.rootElement);o.useTheme(K),o.view.addClassNameToLicenseElement(o.getCurrentThemeName())}o.runHooks("afterUpdateSettings",u)}c.adjustRowsAndCols(),o.view&&!p&&(o.render(),o.view._wt.wtOverlays.adjustElementsSize()),!h&&o.view&&(A===""||x===""||x===void 0)&&A!==x&&o.view._wt.wtOverlays.updateMainScrollableElements()},this.getValue=function(){const u=o.getSelectedLast();if(b.getValue){if(Z(b.getValue))return b.getValue.call(o);if(u)return o.getData()[u[0][0]][b.getValue]}else if(u)return o.getDataAtCell(u[0],u[1])},this.getSettings=function(){return b},this.clear=function(){this.selectAll(),this.emptySelectedCells()},this.alter=function(u,h,g,w,C){c.alter(u,h,g,w,C)},this.getCell=function(u,h){let g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,w=h,C=u;if(h>=0){if(this.columnIndexMapper.isHidden(this.toPhysicalColumn(h)))return null;w=this.columnIndexMapper.getRenderableFromVisualIndex(h)}if(u>=0){if(this.rowIndexMapper.isHidden(this.toPhysicalRow(u)))return null;C=this.rowIndexMapper.getRenderableFromVisualIndex(u)}return C===null||w===null||C===void 0||w===void 0?null:o.view.getCellAtCoords(o._createCellCoords(C,w),g)},this.getCoords=function(u){const h=this.view._wt.wtTable.getCoords(u);if(h===null)return null;const{row:g,col:w}=h;let C=g,S=w;return g>=0&&(C=this.rowIndexMapper.getVisualFromRenderableIndex(g)),w>=0&&(S=this.columnIndexMapper.getVisualFromRenderableIndex(w)),o._createCellCoords(C,S)},this.colToProp=function(u){return l.colToProp(u)},this.propToCol=function(u){return l.propToCol(u)},this.toVisualRow=u=>this.rowIndexMapper.getVisualFromPhysicalIndex(u),this.toVisualColumn=u=>this.columnIndexMapper.getVisualFromPhysicalIndex(u),this.toPhysicalRow=u=>this.rowIndexMapper.getPhysicalFromVisualIndex(u),this.toPhysicalColumn=u=>this.columnIndexMapper.getPhysicalFromVisualIndex(u),this.getDataAtCell=function(u,h){return l.get(u,l.colToProp(h))},this.getDataAtRowProp=function(u,h){return l.get(u,h)},this.getDataAtCol=function(u){const h=[],g=l.getRange(o._createCellCoords(0,u),o._createCellCoords(b.data.length-1,u),l.DESTINATION_RENDERER);for(let w=0;w<g.length;w+=1)for(let C=0;C<g[w].length;C+=1)h.push(g[w][C]);return h},this.getDataAtProp=function(u){const h=[],g=l.getRange(o._createCellCoords(0,l.propToCol(u)),o._createCellCoords(b.data.length-1,l.propToCol(u)),l.DESTINATION_RENDERER);for(let w=0;w<g.length;w+=1)for(let C=0;C<g[w].length;C+=1)h.push(g[w][C]);return h},this.getSourceData=function(u,h,g,w){let C;return u===void 0?C=a.getData():C=a.getByRange(o._createCellCoords(u,h),o._createCellCoords(g,w)),C},this.getSourceDataArray=function(u,h,g,w){let C;return u===void 0?C=a.getData(!0):C=a.getByRange(o._createCellCoords(u,h),o._createCellCoords(g,w),!0),C},this.getSourceDataAtCol=function(u){return a.getAtColumn(u)},this.setSourceDataAtCell=function(u,h,g,w){const C=ue(u,h,g),S=this.hasHook("afterSetSourceDataAtCell"),E=[];S&&U(C,A=>{let[x,T,j]=A;E.push([x,T,a.getAtCell(x,T),j])}),U(C,A=>{let[x,T,j]=A;a.setAtCell(x,T,j)}),S&&this.runHooks("afterSetSourceDataAtCell",E,w),this.render();const O=o.getActiveEditor();O&&X(O.refreshValue)&&O.refreshValue()},this.getSourceDataAtRow=function(u){return a.getAtRow(u)},this.getSourceDataAtCell=function(u,h){return a.getAtCell(u,h)},this.getDataAtRow=function(u){return l.getRange(o._createCellCoords(u,0),o._createCellCoords(u,this.countCols()-1),l.DESTINATION_RENDERER)[0]||[]},this.getDataType=function(u,h,g,w){const C=u===void 0?[0,0,this.countRows(),this.countCols()]:[u,h,g,w],[S,E]=C;let[,,O,A]=C,x=null,T=null;O===void 0&&(O=S),A===void 0&&(A=E);let j="mixed";return Nt(Math.max(Math.min(S,O),0),Math.max(S,O),K=>{let Q=!0;return Nt(Math.max(Math.min(E,A),0),Math.max(E,A),Re=>(T=this.getCellMeta(K,Re).type,x?Q=x===T:x=T,Q)),j=Q?T:"mixed",Q}),j},this.removeCellMeta=function(u,h,g){const[w,C]=[this.toPhysicalRow(u),this.toPhysicalColumn(h)];let S=M.getCellMetaKeyValue(w,C,g);o.runHooks("beforeRemoveCellMeta",u,h,g,S)!==!1&&(M.removeCellMeta(w,C,g),o.runHooks("afterRemoveCellMeta",u,h,g,S)),S=null},this.spliceCellsMeta=function(u){let h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;for(var g=arguments.length,w=new Array(g>2?g-2:0),C=2;C<g;C++)w[C-2]=arguments[C];if(w.length>0&&!Array.isArray(w[0]))throw new Error("The 3rd argument (cellMetaRows) has to be passed as an array of cell meta objects array.");h>0&&M.removeRow(this.toPhysicalRow(u),h),w.length>0&&U(w.reverse(),S=>{M.createRow(this.toPhysicalRow(u)),U(S,(E,O)=>this.setCellMetaObject(u,O,E))}),o.render()},this.setCellMetaObject=function(u,h,g){typeof g=="object"&&fe(g,(w,C)=>{this.setCellMeta(u,h,C,w)})},this.setCellMeta=function(u,h,g,w){if(o.runHooks("beforeSetCellMeta",u,h,g,w)===!1)return;let S=u,E=h;u<this.countRows()&&(S=this.toPhysicalRow(u)),h<this.countCols()&&(E=this.toPhysicalColumn(h)),M.setCellMeta(S,E,g,w),o.runHooks("afterSetCellMeta",u,h,g,w)},this.getCellsMeta=function(){return M.getCellsMeta()},this.getCellMeta=function(u,h){let g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{skipMetaExtension:!1},w=this.toPhysicalRow(u),C=this.toPhysicalColumn(h);return w===null&&(w=u),C===null&&(C=h),M.getCellMeta(w,C,{visualRow:u,visualColumn:h,...g})},this.getColumnMeta=function(u){return M.getColumnMeta(this.toPhysicalColumn(u))},this.getCellMetaAtRow=function(u){return M.getCellsMetaAtRow(u)},this.isColumnModificationAllowed=function(){return!(o.dataType==="object"||b.columns)},this.getCellRenderer=function(u,h){const g=typeof u=="number"?o.getCellMeta(u,h).renderer:u.renderer;return typeof g=="string"?nd(g):ct(g)?nd("text"):g},this.getCellEditor=function(u,h){const g=typeof u=="number"?o.getCellMeta(u,h).editor:u.editor;return typeof g=="string"?Kh(g):ct(g)?Kh("text"):g},this.getCellValidator=function(u,h){const g=typeof u=="number"?o.getCellMeta(u,h).validator:u.validator;return typeof g=="string"?_x(g):g},this.validateCells=function(u){this._validateCells(u)},this.validateRows=function(u,h){if(!Array.isArray(u))throw new Error("validateRows parameter `rows` must be an array");this._validateCells(h,u)},this.validateColumns=function(u,h){if(!Array.isArray(u))throw new Error("validateColumns parameter `columns` must be an array");this._validateCells(h,void 0,u)},this._validateCells=function(u,h,g){const w=new Ye;u&&(w.onQueueEmpty=u);let C=o.countRows()-1;for(;C>=0;){if(h!==void 0&&h.indexOf(C)===-1){C-=1;continue}let S=o.countCols()-1;for(;S>=0;){if(g!==void 0&&g.indexOf(S)===-1){S-=1;continue}w.addValidatorToQueue(),o.validateCell(o.getDataAtCell(C,S),o.getCellMeta(C,S),E=>{if(typeof E!="boolean")throw new Error("Validation error: result is not boolean");E===!1&&(w.valid=!1),w.removeValidatorFormQueue()},"validateCells"),S-=1}C-=1}w.checkIfQueueIsEmpty()},this.getRowHeader=function(u){let h=b.rowHeaders,g=u;return g!==void 0&&(g=o.runHooks("modifyRowHeader",g)),g===void 0?(h=[],Nt(o.countRows()-1,w=>{h.push(o.getRowHeader(w))})):Array.isArray(h)&&h[g]!==void 0?h=h[g]:Z(h)?h=h(g):h&&typeof h!="string"&&typeof h!="number"&&(h=g+1),h},this.hasRowHeaders=function(){return!!b.rowHeaders},this.hasColHeaders=function(){if(b.colHeaders!==void 0&&b.colHeaders!==null)return!!b.colHeaders;for(let u=0,h=o.countCols();u<h;u++)if(o.getColHeader(u))return!0;return!1},this.getColHeader=function(u){let h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:-1;const g=o.runHooks("modifyColHeader",u);if(g===void 0){const O=[],A=o.countCols();for(let x=0;x<A;x++)O.push(o.getColHeader(x));return O}let w=b.colHeaders;const C=function(O){const A=[],x=o.countCols();let T=0;for(;T<x;T++)Z(b.columns)&&b.columns(T)&&A.push(T);return A[O]},S=o.toPhysicalColumn(g),E=C(S);return b.colHeaders===!1?w=null:b.columns&&Z(b.columns)&&b.columns(E)&&b.columns(E).title?w=b.columns(E).title:b.columns&&b.columns[S]&&b.columns[S].title?w=b.columns[S].title:Array.isArray(b.colHeaders)&&b.colHeaders[S]!==void 0?w=b.colHeaders[S]:Z(b.colHeaders)?w=b.colHeaders(S):b.colHeaders&&typeof b.colHeaders!="string"&&typeof b.colHeaders!="number"&&(w=MN(g)),w=o.runHooks("modifyColumnHeaderValue",w,u,h),w},this._getColWidthFromSettings=function(u){let h;if(u>=0&&(h=o.getCellMeta(0,u).width),(h===void 0||h===b.width)&&(h=b.colWidths),h!=null){switch(typeof h){case"object":h=h[u];break;case"function":h=h(u);break}typeof h=="string"&&(h=parseInt(h,10))}return h},this.getColWidth=function(u,h){let g=o._getColWidthFromSettings(u);return g=o.runHooks("modifyColWidth",g,u,h),g===void 0&&(g=$m),g},this._getRowHeightFromSettings=function(u){const h=this.view.getDefaultRowHeight();let g=b.rowHeights;if(g!=null){switch(typeof g){case"object":g=g[u];break;case"function":g=g(u);break}typeof g=="string"&&(g=parseInt(g,10))}return g!=null&&g<h?h:g},this.getRowHeight=function(u,h){let g=o._getRowHeightFromSettings(u);return g=o.runHooks("modifyRowHeight",g,u,h),g},this.countSourceRows=function(){return a.countRows()},this.countSourceCols=function(){return a.countFirstRowKeys()},this.countRows=function(){return l.getLength()},this.countCols=function(){const u=b.maxCols,h=this.columnIndexMapper.getNotTrimmedIndexesLength();return Math.min(u,h)},this.countRenderedRows=function(){return o.view._wt.drawn?o.view._wt.wtTable.getRenderedRowsCount():-1},this.countVisibleRows=function(){return o.view._wt.drawn?o.view._wt.wtTable.getVisibleRowsCount():-1},this.countRenderedCols=function(){return o.view._wt.drawn?o.view._wt.wtTable.getRenderedColumnsCount():-1},this.countVisibleCols=function(){return o.view._wt.drawn?o.view._wt.wtTable.getVisibleColumnsCount():-1},this.countRowHeaders=function(){return this.view.getRowHeadersCount()},this.countColHeaders=function(){return this.view.getColumnHeadersCount()},this.countEmptyRows=function(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,h=0;return td(o.countRows()-1,g=>{if(o.isEmptyRow(g))h+=1;else if(u===!0)return!1}),h},this.countEmptyCols=function(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,h=0;return td(o.countCols()-1,g=>{if(o.isEmptyCol(g))h+=1;else if(u===!0)return!1}),h},this.isEmptyRow=function(u){return b.isEmptyRow.call(o,u)},this.isEmptyCol=function(u){return b.isEmptyCol.call(o,u)},this.selectCell=function(u,h,g,w){let C=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,S=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0;return ct(u)||ct(h)?!1:this.selectCells([[u,h,g,w]],C,S)},this.selectCells=function(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[[]],h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;h===!1&&m.suspend();const w=V.selectCells(u);return w&&g&&o.listen(),m.resume(),w},this.selectColumns=function(u){let h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:u,g=arguments.length>2?arguments[2]:void 0;return V.selectColumns(u,h,g)},this.selectRows=function(u){let h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:u,g=arguments.length>2?arguments[2]:void 0;return V.selectRows(u,h,g)},this.deselectCell=function(){V.deselect()},this.selectAll=function(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:u,g=arguments.length>2?arguments[2]:void 0;m.skipNextScrollCycle(),V.selectAll(u,h,g)};const xe=(u,h)=>u.getNearestNotHiddenIndex(h,1,!0);this.scrollViewportTo=function(u,h){if(typeof u=="number"){var g;u={row:arguments[0],col:arguments[1],verticalSnap:arguments[2]?"bottom":"top",horizontalSnap:arguments[3]?"end":"start",considerHiddenIndexes:(g=arguments[4])!==null&&g!==void 0?g:!0}}const{row:w,col:C,considerHiddenIndexes:S}=u!=null?u:{};let E=w,O=C;if(Z(h)&&this.addHookOnce("afterScroll",h),S===void 0||S){const j=Number.isInteger(w)&&w>=0,K=Number.isInteger(C)&&C>=0,Q=j?xe(this.rowIndexMapper,w):void 0,Re=K?xe(this.columnIndexMapper,C):void 0;if(Q===null||Re===null)return!1;E=j?o.rowIndexMapper.getRenderableFromVisualIndex(Q):w,O=K?o.columnIndexMapper.getRenderableFromVisualIndex(Re):C}const A=Number.isInteger(E),x=Number.isInteger(O);let T=!1;return A&&E>=0&&x&&O>=0?T=o.view.scrollViewport(o._createCellCoords(E,O),u.horizontalSnap,u.verticalSnap):A&&E>=0&&(x&&O<0||!x)?T=o.view.scrollViewportVertically(E,u.verticalSnap):x&&O>=0&&(A&&E<0||!A)&&(T=o.view.scrollViewportHorizontally(O,u.horizontalSnap)),Z(h)&&(T?this.view.render():(this.removeHook("afterScroll",h),this._registerMicrotask(()=>h()))),T},this.scrollToFocusedCell=function(u){if(!this.selection.isSelected())return!1;Z(u)&&this.addHookOnce("afterScroll",u);const{highlight:h}=this.getSelectedRangeLast(),g=this.scrollViewportTo(h.toObject());return g?this.view.render():Z(u)&&(this.removeHook("afterScroll",u),this._registerMicrotask(()=>u())),g},this.destroy=function(){if(o._clearTimeouts(),o._clearImmediates(),o.view&&o.view.destroy(),a&&a.destroy(),a=null,this.getShortcutManager().destroy(),M.clearCache(),sa.delete(this.guid),is(o)){const u=this.rootDocument.querySelector(".hot-display-license-info");u&&u.parentNode.removeChild(u)}Uo(o.rootElement),s.destroy(),d&&d.destroy(),o.batchExecution(()=>{o.rowIndexMapper.unregisterAll(),o.columnIndexMapper.unregisterAll(),$.getItems().forEach(u=>{let[,h]=u;h.destroy()}),$.clear(),o.runHooks("afterDestroy")},!0),Le.getSingleton().destroy(o),fe(o,(u,h,g)=>{Z(u)?g[h]=mr(h):h!=="guid"&&(g[h]=null)}),o.isDestroyed=!0,l&&l.destroy(),l=null,c=null,V=null,d=null,o=null};function mr(u){return()=>{throw new Error('The "'.concat(u,'" method cannot be called because this Handsontable instance has been destroyed'))}}this.getActiveEditor=function(){return d.getActiveEditor()},this.getFirstRenderedVisibleRow=function(){return o.view.getFirstRenderedVisibleRow()},this.getLastRenderedVisibleRow=function(){return o.view.getLastRenderedVisibleRow()},this.getFirstRenderedVisibleColumn=function(){return o.view.getFirstRenderedVisibleColumn()},this.getLastRenderedVisibleColumn=function(){return o.view.getLastRenderedVisibleColumn()},this.getFirstFullyVisibleRow=function(){return o.view.getFirstFullyVisibleRow()},this.getLastFullyVisibleRow=function(){return o.view.getLastFullyVisibleRow()},this.getFirstFullyVisibleColumn=function(){return o.view.getFirstFullyVisibleColumn()},this.getLastFullyVisibleColumn=function(){return o.view.getLastFullyVisibleColumn()},this.getFirstPartiallyVisibleRow=function(){return o.view.getFirstPartiallyVisibleRow()},this.getLastPartiallyVisibleRow=function(){return o.view.getLastPartiallyVisibleRow()},this.getFirstPartiallyVisibleColumn=function(){return o.view.getFirstPartiallyVisibleColumn()},this.getLastPartiallyVisibleColumn=function(){return o.view.getLastPartiallyVisibleColumn()},this.getPlugin=function(u){return $.getItem(sc(u))},this.getPluginName=function(u){return u===this.undoRedo?this.undoRedo.constructor.PLUGIN_KEY:$.getId(u)},this.getInstance=function(){return o},this.addHook=function(u,h,g){Le.getSingleton().add(u,h,o,g)},this.hasHook=function(u){return Le.getSingleton().has(u,o)||Le.getSingleton().has(u)},this.addHookOnce=function(u,h,g){Le.getSingleton().once(u,h,o,g)},this.removeHook=function(u,h){Le.getSingleton().remove(u,h,o)},this.runHooks=function(u,h,g,w,C,S,E){return Le.getSingleton().run(o,u,h,g,w,C,S,E)},this.getTranslatedPhrase=function(u,h){return qw(b.language,u,h)},this.toHTML=()=>ed(this),this.toTableElement=()=>{const u=this.rootDocument.createElement("div");return u.insertAdjacentHTML("afterbegin",ed(this)),u.firstElementChild},this.timeouts=[],this.useTheme=u=>{this.view.getStylesHandler().useTheme(u),this.runHooks("afterSetTheme",u,!!p)},this.getCurrentThemeName=()=>this.view.getStylesHandler().getThemeName(),this._registerTimeout=function(u){let h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,g=u;typeof g=="function"&&(g=setTimeout(g,h)),this.timeouts.push(g)},this._clearTimeouts=function(){U(this.timeouts,u=>{clearTimeout(u)})},this.immediates=[],this._registerImmediate=function(u){this.immediates.push(setImmediate(u))},this._clearImmediates=function(){U(this.immediates,u=>{clearImmediate(u)})},this._registerMicrotask=function(u){this.rootWindow.queueMicrotask(()=>{this.isDestroyed||u()})},this._getEditorManager=function(){return d};const J=JL({handleEvent(){return o.isListening()},beforeKeyDown:u=>this.runHooks("beforeKeyDown",u),afterKeyDown:u=>{this.isDestroyed||o.runHooks("afterDocumentKeyDown",u)},ownerWindow:this.rootWindow});this.addHook("beforeOnCellMouseDown",u=>{u.ctrlKey===!1&&u.metaKey===!1&&J.releasePressedKeys()}),this.getShortcutManager=function(){return J},this.getFocusManager=function(){return f},Sx().forEach(u=>{const h=Rx(u);$.addItem(u,new h(this))}),LL(o),J.setActiveContextName("grid"),Le.getSingleton().run(o,"construct")}function tf(t,e,r){t.addEventListener(e,r,!1)}function rf(t,e,r){t.removeEventListener(e,r,!1)}function Qi(t){return t.ownerDocument.defaultView.getComputedStyle(t)}function eD(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r={minHeight:200,maxHeight:300,minWidth:100,maxWidth:300,textContent:f=>f.value,...e},n=t.body,i=t.createTextNode(""),o=t.createElement("span");let s;function l(){var f,m;i.textContent=r.textContent(s),o.style.position="absolute",o.style.fontSize=Qi(s).fontSize,o.style.fontFamily=Qi(s).fontFamily,o.style.whiteSpace="pre",n.appendChild(o);const p=parseInt(((f=Qi(s))===null||f===void 0?void 0:f.paddingInlineStart)||0,10),v=parseInt(((m=Qi(s))===null||m===void 0?void 0:m.paddingInlineEnd)||0,10),R=o.clientWidth+p+v+1;n.removeChild(o);const H=s.style;H.height="".concat(r.minHeight,"px"),r.minWidth>R?H.width="".concat(r.minWidth,"px"):R>r.maxWidth?H.width="".concat(r.maxWidth,"px"):H.width="".concat(R,"px");const M=s.scrollHeight?s.scrollHeight-1:0;r.minHeight>M?H.height="".concat(r.minHeight,"px"):r.maxHeight<M?(H.height="".concat(r.maxHeight,"px"),H.overflowY="visible"):H.height="".concat(M,"px")}function a(){t.defaultView.setTimeout(l,0)}function c(f){if(f&&f.minHeight)if(f.minHeight==="inherit")r.minHeight=s.clientHeight;else{const m=parseInt(f.minHeight,10);isNaN(m)||(r.minHeight=m)}if(f&&f.maxHeight)if(f.maxHeight==="inherit")r.maxHeight=s.clientHeight;else{const m=parseInt(f.maxHeight,10);isNaN(m)||(r.maxHeight=m)}if(f&&f.minWidth)if(f.minWidth==="inherit")r.minWidth=s.clientWidth;else{const m=parseInt(f.minWidth,10);isNaN(m)||(r.minWidth=m)}if(f&&f.maxWidth)if(f.maxWidth==="inherit")r.maxWidth=s.clientWidth;else{const m=parseInt(f.maxWidth,10);isNaN(m)||(r.maxWidth=m)}o.firstChild||(o.className="autoResize",o.style.display="inline-block",o.appendChild(i))}function d(f,m){let p=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;s=f,c(m),s.nodeName==="TEXTAREA"&&(s.style.resize="none",s.style.height="".concat(r.minHeight,"px"),s.style.minWidth="".concat(r.minWidth,"px"),s.style.maxWidth="".concat(r.maxWidth,"px"),s.style.overflowY="hidden"),p&&(tf(s,"input",l),tf(s,"keydown",a)),l()}return{init:d,resize:l,unObserve(){rf(s,"input",l),rf(s,"keydown",a)}}}function nf(t,e){const r=WH(e),n=e.value.split("\n");let i=r,o=0;for(let s=0;s<n.length;s++){const l=n[s];s!==0&&(o+=n[s-1].length+1);const a=o+l.length;if(t==="home"?i=o:t==="end"&&(i=a),r<=a)break}gm(e,i)}function on(t,e,r){return(e=tD(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tD(t){var e=rD(t,"string");return typeof e=="symbol"?e:e+""}function rD(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}const nD="ht_editor_visible",Zi="ht_editor_hidden",of="textEditor",oD="text";class iD extends Vc{static get EDITOR_TYPE(){return oD}constructor(e){super(e),on(this,"eventManager",new Un(this)),on(this,"autoResize",eD(this.hot.rootDocument)),on(this,"TEXTAREA",void 0),on(this,"textareaStyle",void 0),on(this,"TEXTAREA_PARENT",void 0),on(this,"textareaParentStyle",void 0),on(this,"layerClass",void 0),this.eventManager=new Un(this),this.createElements(),this.bindEvents(),this.hot.addHookOnce("afterDestroy",()=>this.destroy())}getValue(){return this.TEXTAREA.value}setValue(e){this.TEXTAREA.value=e}open(){this.refreshDimensions(),this.showEditableElement(),this.hot.getShortcutManager().setActiveContextName("editor"),this.registerShortcuts()}close(){this.autoResize.unObserve(),AH(this.hot.rootDocument.activeElement,this.hot.rootElement)&&this.hot.listen(),this.hideEditableElement(),this.unregisterShortcuts()}prepare(e,r,n,i,o,s){const l=this.state;if(super.prepare(e,r,n,i,o,s),!s.readOnly){this.refreshDimensions(!0);const{allowInvalid:a}=s;a&&!this.isOpened()&&(this.TEXTAREA.value=""),l!==He.FINISHED&&!this.isOpened()&&this.hideEditableElement()}}beginEditing(e,r){this.state===He.VIRGIN&&(this.TEXTAREA.value="",super.beginEditing(e,r))}focus(){this.TEXTAREA.select(),gm(this.TEXTAREA,this.TEXTAREA.value.length)}createElements(){const{rootDocument:e}=this.hot;this.TEXTAREA=e.createElement("TEXTAREA"),de(this.TEXTAREA,[["data-hot-input",""],oi(-1)]),G(this.TEXTAREA,"handsontableInput"),this.textareaStyle=this.TEXTAREA.style,this.textareaStyle.width=0,this.textareaStyle.height=0,this.textareaStyle.overflowY="visible",this.TEXTAREA_PARENT=e.createElement("DIV"),G(this.TEXTAREA_PARENT,"handsontableInputHolder"),ee(this.TEXTAREA_PARENT,this.layerClass)&&ce(this.TEXTAREA_PARENT,this.layerClass),G(this.TEXTAREA_PARENT,Zi),this.textareaParentStyle=this.TEXTAREA_PARENT.style,this.TEXTAREA_PARENT.appendChild(this.TEXTAREA),this.hot.rootElement.appendChild(this.TEXTAREA_PARENT)}hideEditableElement(){yI()&&(this.textareaStyle.textIndent="-99999px"),this.textareaStyle.overflowY="visible",this.textareaParentStyle.opacity="0",this.textareaParentStyle.height="1px",ce(this.TEXTAREA_PARENT,this.layerClass),G(this.TEXTAREA_PARENT,Zi)}showEditableElement(){this.textareaParentStyle.height="",this.textareaParentStyle.overflow="",this.textareaParentStyle.position="",this.textareaParentStyle[this.hot.isRtl()?"left":"right"]="auto",this.textareaParentStyle.opacity="1",this.textareaStyle.textIndent="";const e=this.TEXTAREA_PARENT.childNodes;let r=!1;Nt(e.length-1,n=>{const i=e[n];if(ee(i,"handsontableEditor"))return r=!0,!1}),ee(this.TEXTAREA_PARENT,Zi)&&ce(this.TEXTAREA_PARENT,Zi),r?(this.layerClass=nD,G(this.TEXTAREA_PARENT,this.layerClass)):(this.layerClass=this.getEditedCellsLayerClass(),G(this.TEXTAREA_PARENT,this.layerClass))}refreshValue(){const e=this.hot.toPhysicalRow(this.row),r=this.hot.getSourceDataAtCell(e,this.col);this.originalValue=r,this.setValue(r),this.refreshDimensions()}refreshDimensions(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(this.state!==He.EDITING&&!e)return;if(this.TD=this.getEditedCell(),!this.TD){e||this.close();return}const{top:r,start:n,width:i,maxWidth:o,height:s,maxHeight:l}=this.getEditedCellRect();this.textareaParentStyle.top="".concat(r,"px"),this.textareaParentStyle[this.hot.isRtl()?"right":"left"]="".concat(n,"px"),this.showEditableElement();const a=this.hot.rootWindow.getComputedStyle(this.TD);this.TEXTAREA.style.fontSize=a.fontSize,this.TEXTAREA.style.fontFamily=a.fontFamily,this.TEXTAREA.style.backgroundColor=this.TD.style.backgroundColor,this.autoResize.init(this.TEXTAREA,{minWidth:Math.min(i,o),minHeight:Math.min(s,l),maxWidth:o,maxHeight:l},!0)}bindEvents(){Sm()&&this.eventManager.addEventListener(this.TEXTAREA,"focusout",()=>this.finishEditing(!1)),this.addHook("afterScrollHorizontally",()=>this.refreshDimensions()),this.addHook("afterScrollVertically",()=>this.refreshDimensions()),this.addHook("afterColumnResize",()=>{this.refreshDimensions(),this.state===He.EDITING&&this.focus()}),this.addHook("afterRowResize",()=>{this.refreshDimensions(),this.state===He.EDITING&&this.focus()})}allowKeyEventPropagation(){}destroy(){this.eventManager.destroy(),this.clearHooks()}registerShortcuts(){const r=this.hot.getShortcutManager().getContext("editor"),n={runOnlyIf:()=>X(this.hot.getSelected()),group:of},i=()=>{this.hot.rootDocument.execCommand("insertText",!1,"\n")};r.addShortcuts([{keys:[["Control","Enter"]],callback:()=>(i(),!1),runOnlyIf:o=>!this.hot.selection.isMultiple()&&!o.altKey},{keys:[["Meta","Enter"]],callback:()=>(i(),!1),runOnlyIf:()=>!this.hot.selection.isMultiple()},{keys:[["Alt","Enter"]],callback:()=>(i(),!1)},{keys:[["Home"]],callback:(o,s)=>{let[l]=s;nf(l,this.TEXTAREA)}},{keys:[["End"]],callback:(o,s)=>{let[l]=s;nf(l,this.TEXTAREA)}}],n)}unregisterShortcuts(){this.hot.getShortcutManager().getContext("editor").removeShortcutsByGroup(of)}}const sD="base";function mv(t,e,r,n,i,o,s){const l=s.ariaTags,a=[],c=[],d=[],f=[];s.className&&G(e,s.className),s.readOnly?(a.push(s.readOnlyCellClassName),l&&f.push(Ih())):l&&d.push(Ih()[0]),s.valid===!1&&s.invalidCellClassName?(a.push(s.invalidCellClassName),l&&f.push(Mh())):(c.push(s.invalidCellClassName),l&&d.push(Mh()[0])),s.wordWrap===!1&&s.noWordWrapClassName&&a.push(s.noWordWrapClassName),!o&&s.placeholder&&a.push(s.placeholderCellClassName),ce(e,c),G(e,a),ii(e,d),de(e,f)}mv.RENDERER_TYPE=sD;const lD="text";function pv(t,e,r,n,i,o,s){mv.apply(this,[t,e,r,n,i,o,s]);let l=o;!l&&s.placeholder&&(l=s.placeholder),l=Lo(l),s.trimWhitespace&&(l=l.trim()),si(e,l)}pv.RENDERER_TYPE=lD;const aD="text",cD={CELL_TYPE:aD,editor:iD,renderer:pv};a1(cD);Ct.editors={BaseEditor:Vc};function Ct(t,e){const r=new gv(t,e||{},Pc);return r.init(),r}Ct.Core=function(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new gv(t,e,Pc)};Ct.DefaultSettings=sv();Ct.hooks=Le.getSingleton();Ct.CellCoords=vs;Ct.CellRange=wc;Ct.packageName="handsontable";Ct.buildDate="24/04/2025 10:59:00";Ct.version="15.3.0";Ct.languages={dictionaryKeys:v$,getLanguageDictionary:Xw,getLanguagesDictionaries:R$,registerLanguageDictionary:Yw,getTranslatedPhrase:qw};function uD(t,e,r){return(e=fD(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function hD(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?sf(Object(r),!0).forEach(function(n){uD(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sf(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function dD(t,e){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function fD(t){var e=dD(t,"string");return typeof e=="symbol"?e:e+""}function Wo(t){"@babel/helpers - typeof";return Wo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Wo(t)}var Wn=Symbol("unassigned"),gD="The Handsontable instance bound to this component was destroyed and cannot be used properly.";function bs(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function wv(t){var e=Ct.hooks.getRegistered(),r={};Object.assign(r,Ct.DefaultSettings);for(var n in r)r[n]={default:Wn};for(var i=0;i<e.length;i++)r[e[i]]={default:Wn};return r.settings={default:Wn},t==="HotTable"&&(r.id={type:String,default:"hot-".concat(Math.random().toString(36).substring(5))}),r}function vv(t){var e={},r=t.settings;if(r!==Wn)for(var n in r)bs(r,n)&&r[n]!==Wn&&(e[n]=r[n]);for(var i in t)bs(t,i)&&i!=="settings"&&t[i]!==Wn&&(e[i]=t[i]);return e}function lf(t,e){var r=vv(t),n=t.settings?t.settings:r,i=t.settings?r:null,o={};for(var s in n)bs(n,s)&&n[s]!==void 0&&(!(e&&s!=="data")||!af(e[s],n[s]))&&(o[s]=n[s]);for(var l in i)bs(i,l)&&l!=="id"&&l!=="settings"&&i[l]!==void 0&&(!(e&&l!=="data")||!af(e[l],i[l]))&&(o[l]=i[l]);return o}function af(t,e){var r=function(i){var o=function(){var s=new WeakSet;return function(l,a){if(Wo(a)==="object"&&a!==null){if(s.has(a))return;s.add(a)}return a}}();return JSON.stringify(i,o)};return typeof t=="function"&&typeof e=="function"?t.toString()===e.toString():Wo(t)!==Wo(e)?!1:r(t)===r(e)}var mD="15.3.0",kc=Tf({name:"HotTable",props:wv("HotTable"),provide:function(){return{columnsCache:this.columnsCache}},watch:{$props:{handler:function(e){var r=lf(e,this.hotInstance?this.hotInstance.getSettings():void 0);!this.hotInstance||r===void 0||(r.data&&(this.hotInstance.isColumnModificationAllowed()||!this.hotInstance.isColumnModificationAllowed()&&this.hotInstance.countSourceCols()===this.miscCache.currentSourceColumns)&&(this.matchHotMappersSize(),delete r.data),Object.keys(r).length?this.hotInstance.updateSettings(r):this.hotInstance.render(),this.miscCache.currentSourceColumns=this.hotInstance.countSourceCols())},deep:!0,immediate:!0}},data:function(){return{__hotInstance:null,miscCache:{currentSourceColumns:null},columnSettings:null,columnsCache:new Map,get hotInstance(){return!this.__hotInstance||this.__hotInstance&&!this.__hotInstance.isDestroyed?this.__hotInstance:(console.warn(gD),null)},set hotInstance(e){this.__hotInstance=e}}},methods:{hotInit:function(){var e=lf(this.$props);e.columns=this.columnSettings?this.columnSettings:e.columns,this.hotInstance=Ov(new Ct.Core(this.$el,e)),this.hotInstance.init(),this.miscCache.currentSourceColumns=this.hotInstance.countSourceCols()},matchHotMappersSize:function(){var e=this;if(this.hotInstance){var r=this.hotInstance.getSourceData(),n=[],i=[],o=this.hotInstance.rowIndexMapper.getNumberOfIndexes(),s=this.hotInstance.isColumnModificationAllowed(),l=0;if(r&&r.length!==o&&r.length<o)for(var a=r.length;a<o;a++)n.push(a);if(s){var c;if(l=this.hotInstance.columnIndexMapper.getNumberOfIndexes(),r&&r[0]&&((c=r[0])===null||c===void 0?void 0:c.length)!==l&&r[0].length<l)for(var d=r[0].length;d<l;d++)i.push(d)}this.hotInstance.batch(function(){n.length>0?e.hotInstance.rowIndexMapper.removeIndexes(n):e.hotInstance.rowIndexMapper.insertIndexes(o-1,r.length-o),s&&r.length!==0&&(i.length>0?e.hotInstance.columnIndexMapper.removeIndexes(i):e.hotInstance.columnIndexMapper.insertIndexes(l-1,r[0].length-l))})}},getColumnSettings:function(){var e=Array.from(this.columnsCache.values());return e.length?e:void 0}},mounted:function(){this.columnSettings=this.getColumnSettings(),this.hotInit()},beforeUnmount:function(){this.hotInstance&&this.hotInstance.destroy()},version:mD}),pD=["id"];function wD(t,e,r,n,i,o){return No(),la("div",{id:t.id},[Hv(t.$slots,"default")],8,pD)}kc.render=wD;kc.__file="src/HotTable.vue";var vD=Tf({name:"HotColumn",props:wv("HotColumn"),inject:["columnsCache"],methods:{createColumnSettings:function(){var e=vv(this.$props),r=hD({},e);e.renderer&&(r.renderer=e.renderer),e.editor&&(r.editor=e.editor),this.columnsCache.set(this,r)}},mounted:function(){this.createColumnSettings()},unmounted:function(){this.columnsCache.delete(this)},render:function(){return null}});vD.__file="src/HotColumn.vue";const yD={data(){return{lockTypes:["portrait-primary","portrait-secondary","landscape-primary","landscape-secondary"]}},computed:{lockFunction(){return(screen.orientation||{}).lock?screen.orientation.lock:screen.mozLockOrientation?t=>new Promise((e,r)=>{screen.mozLockOrientation(t)?e():r()}):screen.msLockOrientation?t=>new Promise((e,r)=>{screen.msLockOrientation(t)?e():r()}):t=>new Promise((e,r)=>r(new Error("No orientation")))}},methods:{getOrientation(){return(screen.orientation||{}).type||screen.mozOrientation||screen.msOrientation},unlock(){(screen.orientation||{}).unlock?screen.orientation.unlock():screen.mozUnlockOrientation?screen.mozUnlockOrientation():screen.msUnlockOrientation&&screen.msUnlockOrientation()},orientChange(){const t=this.getOrientation(),[e,r]=(t||"").split("-");e==="portrait"?this.orient("landscape-".concat(r)):e==="landscape"&&this.orient("portrait-".concat(r))},async orient(t){try{await this.lockFunction(t)}catch(e){e instanceof Error?this.$showToast("该环境下不支持翻转"):this.$showToast("翻转失败")}},emitOrientChange(){const t=this.getOrientation();this.$emit("change",t)}},beforeDestroy(){window.removeEventListener("orientationchange",this.emitOrientChange),this.orient("portrait-primary")},mounted(){window.addEventListener("orientationchange",this.emitOrientChange);const{orientationType:t}=this.$route.query;this.lockTypes.includes(t)&&t!==this.getOrientation()&&this.orient(t)}};function CD(t,e,r,n,i,o){const s=Ef("van-button");return No(),Of(s,{color:"#26ccff",class:"circle orient-button",onClick:o.orientChange},{default:Iv(()=>e[0]||(e[0]=[Mv("i",{class:"iconfont icon-fanzhuanpingmu text-20"},null,-1)])),_:1,__:[0]},8,["onClick"])}const BD=Rf(yD,[["render",CD],["__scopeId","data-v-ee597a3d"]]);function cf(t,e){t==null&&(t=""),typeof e!="number"&&(e=12);const r=document.createElement("span");r.innerText=t,r.style.fontSize=e+"px",r.style.opacity="0",r.style.position="fixed",r.style.bottom="0",r.style.left="0",document.body.appendChild(r);let n=window.getComputedStyle(r).width;return n=parseFloat(n.substring(0,n.indexOf("px"))),document.body.removeChild(r),n}const bD={components:{HotTable:kc},props:{isLine:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1},height:{type:[Number,String,Function],default:void 0},pageSize:{type:Number,default:20},params:{type:Object,default:()=>({})}},data(){return{currentPage:0,data:[],totalSize:0,dataSetting:{data:[]}}},watch:{params(t){this.currentPage=0,this.data=[],this.totalSize=0,this.dataSetting={data:[]},Object.keys(t).length>0&&this.$nextTick(e=>{this.getData(!0)})}},methods:{getRowHeaderWidth(t=[]){const e=Math.max(...t.map(r=>cf(r,14)+10));return Math.max(e,50)},async getLineData(){const t=await xv(this.params),{data:e,codeName:r,vectorName:n}=t;if(Array.isArray(e)){this.data=e.map(o=>({...o,watchTime:eu(o.watchTime,"YYYY-MM-DD HH:mm:ss")})),this.totalSize=this.data.length;const i=e.map(o=>r);this.dataSetting={fixedColumnsLeft:1,height:this.height,data:this.data,columns:[{data:"watchTime",editor:!1,width:150},{data:"value",editor:!1,width:150}],colHeaders:["日期",n],rowHeaders:i,rowHeaderWidth:this.getRowHeaderWidth(i),stretchH:"all"}}else this.dataSetting={data:[]};this.$nextTick(i=>{this.$refs.inputTable.hotInstance.updateSettings(this.dataSetting),this.$emit("response",t.data)})},async getValueData(t=!1){const e=await _v({page:this.currentPage,size:this.pageSize,...this.params});let r=e.data;const{info:n,totalSize:i}=e;if(Array.isArray(r)&&Array.isArray(n)){this.data=this.lazy&&!t?this.data.concat(r):r,this.totalSize=i;const o=this.data.map(s=>s.CODE_NAME);this.dataSetting={fixedColumnsLeft:1,height:this.height,data:this.data.map(s=>({...s,WATCH_TIME:eu(s.WATCH_TIME,"YYYY-MM-DD HH:mm:ss")})),columns:[{data:"WATCH_TIME",editor:!1,width:150},...n.map(s=>({data:s.valueVectorFieldName,editor:!1,width:cf(s.valueVectorName)+20}))],colHeaders:["日期",...n.map(s=>s.valueVectorName)],rowHeaders:o,rowHeaderWidth:this.getRowHeaderWidth(o)}}else this.currentPage===0&&(this.data=[],this.totalSize=0),this.dataSetting={data:[]};this.$nextTick(o=>{if(this.$refs.inputTable.hotInstance.updateSettings(this.dataSetting),this.$emit("response",e.data),this.lazy&&this.currentPage===0){const s=document.querySelector(".wtHolder");s&&s.addEventListener("scroll",this.scrollHandler)}})},getData(t=!1){this.isLine?this.getLineData():this.getValueData(t)},scrollHandler(t){this.scrollTimer&&clearTimeout(this.scrollTimer),t&&t.target&&t.target.scrollTop>0&&(this.scrollTimer=setTimeout(()=>{const e=document.querySelector(".wtHolder");e.scrollTop+window.innerHeight>e.scrollHeight-2&&this.data.length<this.totalSize&&(this.currentPage++,this.getData()),clearTimeout(this.scrollTimer)},500))}},beforeDestroy(){this.lazy&&document.querySelector(".wtHolder")&&document.querySelector(".wtHolder").removeEventListener("scroll",this.scrollHandler)}},SD={class:"monitor-table-style no-selection-handle"},RD={key:1,class:"text-center py-16 mb-0"};function TD(t,e,r,n,i,o){const s=Ef("hot-table");return No(),la("div",SD,[i.totalSize>0?(No(),Of(s,{key:0,ref:"inputTable",stretchH:"all",class:"border border-top-0","license-key":"non-commercial-and-evaluation",settings:i.dataSetting,height:r.height,width:"100%"},null,8,["settings","height"])):(No(),la("p",RD," 暂无数据 "))])}const WD=Rf(bD,[["render",TD],["__scopeId","data-v-2dd25217"]]);export{kc as H,WD as c,cf as g,BD as p};
