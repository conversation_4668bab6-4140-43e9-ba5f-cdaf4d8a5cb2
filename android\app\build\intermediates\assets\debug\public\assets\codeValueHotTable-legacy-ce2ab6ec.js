System.register(["./index-legacy-09188690.js","./verder-legacy-e6127216.js","./dateFormat-legacy-dd142601.js","./api-legacy-38baf147.js"],(function(e,t){"use strict";var o,r,i,n,s,l,a,h,c,u,d,g,f,m,p;return{setters:[e=>{o=e.k,r=e._},e=>{i=e.l,n=e.aa,s=e.R,l=e.S,a=e.a1,h=e.Q,c=e.X,u=e.V,d=e.U},e=>{g=e.h,f=e.d},e=>{m=e.e,p=e.f}],execute:function(){var t=document.createElement("style");t.textContent='@charset "UTF-8";.orient-button[data-v-ee597a3d]{position:fixed;z-index:181;bottom:17.6vw;right:4.26667vw;width:14.93333vw;height:14.93333vw;box-shadow:0 .53333vw 1.06667vw -1px rgba(0,0,0,.3);border-radius:50%}[data-v-2dd25217] .handsontable.ht_clone_top_left_corner .colHeader.cornerHeader:before{content:"测点"}\n',document.head.appendChild(t),e("g",pM);var w=function(e){return e&&e.Math===Math&&e},y=w("object"==typeof globalThis&&globalThis)||w("object"==typeof window&&window)||w("object"==typeof self&&self)||w("object"==typeof o&&o)||w("object"==typeof o&&o)||function(){return this}()||Function("return this")(),v={},b=function(e){try{return!!e()}catch(t){return!0}},C=!b((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),S=!b((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),R=S,T=Function.prototype.call,E=R?T.bind(T):function(){return T.apply(T,arguments)},H={},O={}.propertyIsEnumerable,M=Object.getOwnPropertyDescriptor,x=M&&!O.call({1:2},1);H.f=x?function(e){var t=M(this,e);return!!t&&t.enumerable}:O;var I,N,A=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},k=S,_=Function.prototype,P=_.call,V=k&&_.bind.bind(P,P),L=k?V:function(e){return function(){return P.apply(e,arguments)}},D=L,F=D({}.toString),B=D("".slice),W=function(e){return B(F(e),8,-1)},j=b,z=W,$=Object,U=L("".split),X=j((function(){return!$("z").propertyIsEnumerable(0)}))?function(e){return"String"===z(e)?U(e,""):$(e)}:$,G=function(e){return null==e},Y=G,K=TypeError,q=function(e){if(Y(e))throw new K("Can't call method on "+e);return e},Q=X,J=q,Z=function(e){return Q(J(e))},ee="object"==typeof document&&document.all,te=void 0===ee&&void 0!==ee?function(e){return"function"==typeof e||e===ee}:function(e){return"function"==typeof e},oe=te,re=function(e){return"object"==typeof e?null!==e:oe(e)},ie=y,ne=te,se=function(e,t){return arguments.length<2?(o=ie[e],ne(o)?o:void 0):ie[e]&&ie[e][t];var o},le=L({}.isPrototypeOf),ae=y.navigator,he=ae&&ae.userAgent,ce=he?String(he):"",ue=y,de=ce,ge=ue.process,fe=ue.Deno,me=ge&&ge.versions||fe&&fe.version,pe=me&&me.v8;pe&&(N=(I=pe.split("."))[0]>0&&I[0]<4?1:+(I[0]+I[1])),!N&&de&&(!(I=de.match(/Edge\/(\d+)/))||I[1]>=74)&&(I=de.match(/Chrome\/(\d+)/))&&(N=+I[1]);var we=N,ye=b,ve=y.String,be=!!Object.getOwnPropertySymbols&&!ye((function(){var e=Symbol("symbol detection");return!ve(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&we&&we<41})),Ce=be&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Se=se,Re=te,Te=le,Ee=Object,He=Ce?function(e){return"symbol"==typeof e}:function(e){var t=Se("Symbol");return Re(t)&&Te(t.prototype,Ee(e))},Oe=String,Me=function(e){try{return Oe(e)}catch(t){return"Object"}},xe=te,Ie=Me,Ne=TypeError,Ae=function(e){if(xe(e))return e;throw new Ne(Ie(e)+" is not a function")},ke=Ae,_e=G,Pe=function(e,t){var o=e[t];return _e(o)?void 0:ke(o)},Ve=E,Le=te,De=re,Fe=TypeError,Be={exports:{}},We=y,je=Object.defineProperty,ze=function(e,t){try{je(We,e,{value:t,configurable:!0,writable:!0})}catch(o){We[e]=t}return t},$e=y,Ue=ze,Xe="__core-js_shared__",Ge=Be.exports=$e[Xe]||Ue(Xe,{});(Ge.versions||(Ge.versions=[])).push({version:"3.42.0",mode:"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.42.0/LICENSE",source:"https://github.com/zloirock/core-js"});var Ye=Be.exports,Ke=Ye,qe=function(e,t){return Ke[e]||(Ke[e]=t||{})},Qe=q,Je=Object,Ze=function(e){return Je(Qe(e))},et=Ze,tt=L({}.hasOwnProperty),ot=Object.hasOwn||function(e,t){return tt(et(e),t)},rt=L,it=0,nt=Math.random(),st=rt(1..toString),lt=function(e){return"Symbol("+(void 0===e?"":e)+")_"+st(++it+nt,36)},at=qe,ht=ot,ct=lt,ut=be,dt=Ce,gt=y.Symbol,ft=at("wks"),mt=dt?gt.for||gt:gt&&gt.withoutSetter||ct,pt=function(e){return ht(ft,e)||(ft[e]=ut&&ht(gt,e)?gt[e]:mt("Symbol."+e)),ft[e]},wt=E,yt=re,vt=He,bt=Pe,Ct=function(e,t){var o,r;if("string"===t&&Le(o=e.toString)&&!De(r=Ve(o,e)))return r;if(Le(o=e.valueOf)&&!De(r=Ve(o,e)))return r;if("string"!==t&&Le(o=e.toString)&&!De(r=Ve(o,e)))return r;throw new Fe("Can't convert object to primitive value")},St=TypeError,Rt=pt("toPrimitive"),Tt=function(e,t){if(!yt(e)||vt(e))return e;var o,r=bt(e,Rt);if(r){if(void 0===t&&(t="default"),o=wt(r,e,t),!yt(o)||vt(o))return o;throw new St("Can't convert object to primitive value")}return void 0===t&&(t="number"),Ct(e,t)},Et=He,Ht=function(e){var t=Tt(e,"string");return Et(t)?t:t+""},Ot=re,Mt=y.document,xt=Ot(Mt)&&Ot(Mt.createElement),It=function(e){return xt?Mt.createElement(e):{}},Nt=It,At=!C&&!b((function(){return 7!==Object.defineProperty(Nt("div"),"a",{get:function(){return 7}}).a})),kt=C,_t=E,Pt=H,Vt=A,Lt=Z,Dt=Ht,Ft=ot,Bt=At,Wt=Object.getOwnPropertyDescriptor;v.f=kt?Wt:function(e,t){if(e=Lt(e),t=Dt(t),Bt)try{return Wt(e,t)}catch(o){}if(Ft(e,t))return Vt(!_t(Pt.f,e,t),e[t])};var jt={},zt=C&&b((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),$t=re,Ut=String,Xt=TypeError,Gt=function(e){if($t(e))return e;throw new Xt(Ut(e)+" is not an object")},Yt=C,Kt=At,qt=zt,Qt=Gt,Jt=Ht,Zt=TypeError,eo=Object.defineProperty,to=Object.getOwnPropertyDescriptor,oo="enumerable",ro="configurable",io="writable";jt.f=Yt?qt?function(e,t,o){if(Qt(e),t=Jt(t),Qt(o),"function"==typeof e&&"prototype"===t&&"value"in o&&io in o&&!o[io]){var r=to(e,t);r&&r[io]&&(e[t]=o.value,o={configurable:ro in o?o[ro]:r[ro],enumerable:oo in o?o[oo]:r[oo],writable:!1})}return eo(e,t,o)}:eo:function(e,t,o){if(Qt(e),t=Jt(t),Qt(o),Kt)try{return eo(e,t,o)}catch(r){}if("get"in o||"set"in o)throw new Zt("Accessors not supported");return"value"in o&&(e[t]=o.value),e};var no=jt,so=A,lo=C?function(e,t,o){return no.f(e,t,so(1,o))}:function(e,t,o){return e[t]=o,e},ao={exports:{}},ho=C,co=ot,uo=Function.prototype,go=ho&&Object.getOwnPropertyDescriptor,fo=co(uo,"name"),mo={EXISTS:fo,PROPER:fo&&"something"===function(){}.name,CONFIGURABLE:fo&&(!ho||ho&&go(uo,"name").configurable)},po=te,wo=Ye,yo=L(Function.toString);po(wo.inspectSource)||(wo.inspectSource=function(e){return yo(e)});var vo,bo,Co,So=wo.inspectSource,Ro=te,To=y.WeakMap,Eo=Ro(To)&&/native code/.test(String(To)),Ho=lt,Oo=qe("keys"),Mo=function(e){return Oo[e]||(Oo[e]=Ho(e))},xo={},Io=Eo,No=y,Ao=re,ko=lo,_o=ot,Po=Ye,Vo=Mo,Lo=xo,Do="Object already initialized",Fo=No.TypeError,Bo=No.WeakMap;if(Io||Po.state){var Wo=Po.state||(Po.state=new Bo);Wo.get=Wo.get,Wo.has=Wo.has,Wo.set=Wo.set,vo=function(e,t){if(Wo.has(e))throw new Fo(Do);return t.facade=e,Wo.set(e,t),t},bo=function(e){return Wo.get(e)||{}},Co=function(e){return Wo.has(e)}}else{var jo=Vo("state");Lo[jo]=!0,vo=function(e,t){if(_o(e,jo))throw new Fo(Do);return t.facade=e,ko(e,jo,t),t},bo=function(e){return _o(e,jo)?e[jo]:{}},Co=function(e){return _o(e,jo)}}var zo={set:vo,get:bo,has:Co,enforce:function(e){return Co(e)?bo(e):vo(e,{})},getterFor:function(e){return function(t){var o;if(!Ao(t)||(o=bo(t)).type!==e)throw new Fo("Incompatible receiver, "+e+" required");return o}}},$o=L,Uo=b,Xo=te,Go=ot,Yo=C,Ko=mo.CONFIGURABLE,qo=So,Qo=zo.enforce,Jo=zo.get,Zo=String,er=Object.defineProperty,tr=$o("".slice),or=$o("".replace),rr=$o([].join),ir=Yo&&!Uo((function(){return 8!==er((function(){}),"length",{value:8}).length})),nr=String(String).split("String"),sr=ao.exports=function(e,t,o){"Symbol("===tr(Zo(t),0,7)&&(t="["+or(Zo(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),o&&o.getter&&(t="get "+t),o&&o.setter&&(t="set "+t),(!Go(e,"name")||Ko&&e.name!==t)&&(Yo?er(e,"name",{value:t,configurable:!0}):e.name=t),ir&&o&&Go(o,"arity")&&e.length!==o.arity&&er(e,"length",{value:o.arity});try{o&&Go(o,"constructor")&&o.constructor?Yo&&er(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(i){}var r=Qo(e);return Go(r,"source")||(r.source=rr(nr,"string"==typeof t?t:"")),e};Function.prototype.toString=sr((function(){return Xo(this)&&Jo(this).source||qo(this)}),"toString");var lr=ao.exports,ar=te,hr=jt,cr=lr,ur=ze,dr=function(e,t,o,r){r||(r={});var i=r.enumerable,n=void 0!==r.name?r.name:t;if(ar(o)&&cr(o,n,r),r.global)i?e[t]=o:ur(t,o);else{try{r.unsafe?e[t]&&(i=!0):delete e[t]}catch(s){}i?e[t]=o:hr.f(e,t,{value:o,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return e},gr={},fr=Math.ceil,mr=Math.floor,pr=Math.trunc||function(e){var t=+e;return(t>0?mr:fr)(t)},wr=function(e){var t=+e;return t!=t||0===t?0:pr(t)},yr=wr,vr=Math.max,br=Math.min,Cr=wr,Sr=Math.min,Rr=function(e){var t=Cr(e);return t>0?Sr(t,9007199254740991):0},Tr=function(e){return Rr(e.length)},Er=Z,Hr=function(e,t){var o=yr(e);return o<0?vr(o+t,0):br(o,t)},Or=Tr,Mr=function(e){return function(t,o,r){var i=Er(t),n=Or(i);if(0===n)return!e&&-1;var s,l=Hr(r,n);if(e&&o!=o){for(;n>l;)if((s=i[l++])!=s)return!0}else for(;n>l;l++)if((e||l in i)&&i[l]===o)return e||l||0;return!e&&-1}},xr={includes:Mr(!0),indexOf:Mr(!1)},Ir=ot,Nr=Z,Ar=xr.indexOf,kr=xo,_r=L([].push),Pr=function(e,t){var o,r=Nr(e),i=0,n=[];for(o in r)!Ir(kr,o)&&Ir(r,o)&&_r(n,o);for(;t.length>i;)Ir(r,o=t[i++])&&(~Ar(n,o)||_r(n,o));return n},Vr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Lr=Pr,Dr=Vr.concat("length","prototype");gr.f=Object.getOwnPropertyNames||function(e){return Lr(e,Dr)};var Fr={};Fr.f=Object.getOwnPropertySymbols;var Br=se,Wr=gr,jr=Fr,zr=Gt,$r=L([].concat),Ur=Br("Reflect","ownKeys")||function(e){var t=Wr.f(zr(e)),o=jr.f;return o?$r(t,o(e)):t},Xr=ot,Gr=Ur,Yr=v,Kr=jt,qr=function(e,t,o){for(var r=Gr(t),i=Kr.f,n=Yr.f,s=0;s<r.length;s++){var l=r[s];Xr(e,l)||o&&Xr(o,l)||i(e,l,n(t,l))}},Qr=b,Jr=te,Zr=/#|\.prototype\./,ei=function(e,t){var o=oi[ti(e)];return o===ii||o!==ri&&(Jr(t)?Qr(t):!!t)},ti=ei.normalize=function(e){return String(e).replace(Zr,".").toLowerCase()},oi=ei.data={},ri=ei.NATIVE="N",ii=ei.POLYFILL="P",ni=ei,si=y,li=v.f,ai=lo,hi=dr,ci=ze,ui=qr,di=ni,gi=function(e,t){var o,r,i,n,s,l=e.target,a=e.global,h=e.stat;if(o=a?si:h?si[l]||ci(l,{}):si[l]&&si[l].prototype)for(r in t){if(n=t[r],i=e.dontCallGetSet?(s=li(o,r))&&s.value:o[r],!di(a?r:l+(h?".":"#")+r,e.forced)&&void 0!==i){if(typeof n==typeof i)continue;ui(n,i)}(e.sham||i&&i.sham)&&ai(n,"sham",!0),hi(o,r,n,e)}},fi=S,mi=Function.prototype,pi=mi.apply,wi=mi.call,yi="object"==typeof Reflect&&Reflect.apply||(fi?wi.bind(pi):function(){return wi.apply(pi,arguments)}),vi=L,bi=Ae,Ci=function(e,t,o){try{return vi(bi(Object.getOwnPropertyDescriptor(e,t)[o]))}catch(r){}},Si=re,Ri=function(e){return Si(e)||null===e},Ti=String,Ei=TypeError,Hi=Ci,Oi=re,Mi=q,xi=function(e){if(Ri(e))return e;throw new Ei("Can't set "+Ti(e)+" as a prototype")},Ii=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,o={};try{(e=Hi(Object.prototype,"__proto__","set"))(o,[]),t=o instanceof Array}catch(r){}return function(o,r){return Mi(o),xi(r),Oi(o)?(t?e(o,r):o.__proto__=r,o):o}}():void 0),Ni=jt.f,Ai=te,ki=re,_i=Ii,Pi={};Pi[pt("toStringTag")]="z";var Vi="[object z]"===String(Pi),Li=te,Di=W,Fi=pt("toStringTag"),Bi=Object,Wi="Arguments"===Di(function(){return arguments}()),ji=Vi?Di:function(e){var t,o,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(o=function(e,t){try{return e[t]}catch(o){}}(t=Bi(e),Fi))?o:Wi?Di(t):"Object"===(r=Di(t))&&Li(t.callee)?"Arguments":r},zi=ji,$i=String,Ui=function(e){if("Symbol"===zi(e))throw new TypeError("Cannot convert a Symbol value to a string");return $i(e)},Xi=Ui,Gi=re,Yi=lo,Ki=Error,qi=L("".replace),Qi=String(new Ki("zxcasd").stack),Ji=/\n\s*at [^:]*:[^\n]*/,Zi=Ji.test(Qi),en=A,tn=!b((function(){var e=new Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",en(1,7)),7!==e.stack)})),on=lo,rn=function(e,t){if(Zi&&"string"==typeof e&&!Ki.prepareStackTrace)for(;t--;)e=qi(e,Ji,"");return e},nn=tn,sn=Error.captureStackTrace,ln=se,an=ot,hn=lo,cn=le,un=Ii,dn=qr,gn=function(e,t,o){o in e||Ni(e,o,{configurable:!0,get:function(){return t[o]},set:function(e){t[o]=e}})},fn=function(e,t,o){var r,i;return _i&&Ai(r=t.constructor)&&r!==o&&ki(i=r.prototype)&&i!==o.prototype&&_i(e,i),e},mn=function(e,t){return void 0===e?arguments.length<2?"":t:Xi(e)},pn=function(e,t){Gi(t)&&"cause"in t&&Yi(e,"cause",t.cause)},wn=function(e,t,o,r){nn&&(sn?sn(e,t):on(e,"stack",rn(o,r)))},yn=C,vn=gi,bn=yi,Cn=function(e,t,o,r){var i="stackTraceLimit",n=r?2:1,s=e.split("."),l=s[s.length-1],a=ln.apply(null,s);if(a){var h=a.prototype;if(an(h,"cause")&&delete h.cause,!o)return a;var c=ln("Error"),u=t((function(e,t){var o=mn(r?t:e,void 0),i=r?new a(e):new a;return void 0!==o&&hn(i,"message",o),wn(i,u,i.stack,2),this&&cn(h,this)&&fn(i,this,u),arguments.length>n&&pn(i,arguments[n]),i}));u.prototype=h,"Error"!==l?un?un(u,c):dn(u,c,{name:!0}):yn&&i in a&&(gn(u,a,i),gn(u,a,"prepareStackTrace")),dn(u,a);try{h.name!==l&&hn(h,"name",l),h.constructor=u}catch(d){}return u}},Sn="WebAssembly",Rn=y[Sn],Tn=7!==new Error("e",{cause:7}).cause,En=function(e,t){var o={};o[e]=Cn(e,t,Tn),vn({global:!0,constructor:!0,arity:1,forced:Tn},o)},Hn=function(e,t){if(Rn&&Rn[e]){var o={};o[e]=Cn(Sn+"."+e,t,Tn),vn({target:Sn,stat:!0,constructor:!0,arity:1,forced:Tn},o)}};En("Error",(function(e){return function(t){return bn(e,this,arguments)}})),En("EvalError",(function(e){return function(t){return bn(e,this,arguments)}})),En("RangeError",(function(e){return function(t){return bn(e,this,arguments)}})),En("ReferenceError",(function(e){return function(t){return bn(e,this,arguments)}})),En("SyntaxError",(function(e){return function(t){return bn(e,this,arguments)}})),En("TypeError",(function(e){return function(t){return bn(e,this,arguments)}})),En("URIError",(function(e){return function(t){return bn(e,this,arguments)}})),Hn("CompileError",(function(e){return function(t){return bn(e,this,arguments)}})),Hn("LinkError",(function(e){return function(t){return bn(e,this,arguments)}})),Hn("RuntimeError",(function(e){return function(t){return bn(e,this,arguments)}}));var On=W,Mn=Array.isArray||function(e){return"Array"===On(e)},xn=C,In=Mn,Nn=TypeError,An=Object.getOwnPropertyDescriptor,kn=xn&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}(),_n=kn?function(e,t){if(In(e)&&!An(e,"length").writable)throw new Nn("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t},Pn=TypeError,Vn=function(e){if(e>9007199254740991)throw Pn("Maximum allowed index exceeded");return e},Ln=gi,Dn=Ze,Fn=Tr,Bn=_n,Wn=Vn,jn=b((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}();Ln({target:"Array",proto:!0,arity:1,forced:jn},{push:function(e){var t=Dn(this),o=Fn(t),r=arguments.length;Wn(o+r);for(var i=0;i<r;i++)t[o]=arguments[i],o++;return Bn(t,o),o}});var zn=W,$n=L,Un=function(e){if("Function"===zn(e))return $n(e)},Xn=Ae,Gn=S,Yn=Un(Un.bind),Kn=function(e,t){return Xn(e),void 0===t?e:Gn?Yn(e,t):function(){return e.apply(t,arguments)}},qn={},Qn=qn,Jn=pt("iterator"),Zn=Array.prototype,es=ji,ts=Pe,os=G,rs=qn,is=pt("iterator"),ns=function(e){if(!os(e))return ts(e,is)||ts(e,"@@iterator")||rs[es(e)]},ss=E,ls=Ae,as=Gt,hs=Me,cs=ns,us=TypeError,ds=E,gs=Gt,fs=Pe,ms=function(e,t,o){var r,i;gs(e);try{if(!(r=fs(e,"return"))){if("throw"===t)throw o;return o}r=ds(r,e)}catch(n){i=!0,r=n}if("throw"===t)throw o;if(i)throw r;return gs(r),o},ps=Kn,ws=E,ys=Gt,vs=Me,bs=function(e){return void 0!==e&&(Qn.Array===e||Zn[Jn]===e)},Cs=Tr,Ss=le,Rs=function(e,t){var o=arguments.length<2?cs(e):t;if(ls(o))return as(ss(o,e));throw new us(hs(e)+" is not iterable")},Ts=ns,Es=ms,Hs=TypeError,Os=function(e,t){this.stopped=e,this.result=t},Ms=Os.prototype,xs=function(e,t,o){var r,i,n,s,l,a,h,c=o&&o.that,u=!(!o||!o.AS_ENTRIES),d=!(!o||!o.IS_RECORD),g=!(!o||!o.IS_ITERATOR),f=!(!o||!o.INTERRUPTED),m=ps(t,c),p=function(e){return r&&Es(r,"normal",e),new Os(!0,e)},w=function(e){return u?(ys(e),f?m(e[0],e[1],p):m(e[0],e[1])):f?m(e,p):m(e)};if(d)r=e.iterator;else if(g)r=e;else{if(!(i=Ts(e)))throw new Hs(vs(e)+" is not iterable");if(bs(i)){for(n=0,s=Cs(e);s>n;n++)if((l=w(e[n]))&&Ss(Ms,l))return l;return new Os(!1)}r=Rs(e,i)}for(a=d?e.next:r.next;!(h=ws(a,r)).done;){try{l=w(h.value)}catch(y){Es(r,"throw",y)}if("object"==typeof l&&l&&Ss(Ms,l))return l}return new Os(!1)},Is=C,Ns=jt,As=A,ks=function(e,t,o){Is?Ns.f(e,t,As(0,o)):e[t]=o},_s=xs,Ps=ks;gi({target:"Object",stat:!0},{fromEntries:function(e){var t={};return _s(e,(function(e,o){Ps(t,e,o)}),{AS_ENTRIES:!0}),t}});var Vs=L,Ls=Set.prototype,Ds={Set:Set,add:Vs(Ls.add),has:Vs(Ls.has),remove:Vs(Ls.delete),proto:Ls},Fs=Ds.has,Bs=function(e){return Fs(e),e},Ws=E,js=function(e,t,o){for(var r,i,n=o?e:e.iterator,s=e.next;!(r=Ws(s,n)).done;)if(void 0!==(i=t(r.value)))return i},zs=L,$s=js,Us=Ds.Set,Xs=Ds.proto,Gs=zs(Xs.forEach),Ys=zs(Xs.keys),Ks=Ys(new Us).next,qs=function(e,t,o){return o?$s({iterator:Ys(e),next:Ks},t):Gs(e,t)},Qs=qs,Js=Ds.Set,Zs=Ds.add,el=function(e){var t=new Js;return Qs(e,(function(e){Zs(t,e)})),t},tl=Ci(Ds.proto,"size","get")||function(e){return e.size},ol=function(e){return{iterator:e,next:e.next,done:!1}},rl=Ae,il=Gt,nl=E,sl=wr,ll=ol,al="Invalid size",hl=RangeError,cl=TypeError,ul=Math.max,dl=function(e,t){this.set=e,this.size=ul(t,0),this.has=rl(e.has),this.keys=rl(e.keys)};dl.prototype={getIterator:function(){return ll(il(nl(this.keys,this.set)))},includes:function(e){return nl(this.has,this.set,e)}};var gl=function(e){il(e);var t=+e.size;if(t!=t)throw new cl(al);var o=sl(t);if(o<0)throw new hl(al);return new dl(e,o)},fl=Bs,ml=el,pl=tl,wl=gl,yl=qs,vl=js,bl=Ds.has,Cl=Ds.remove,Sl=se,Rl=function(e){return{size:e,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}},Tl=function(e){return{size:e,has:function(){return!0},keys:function(){throw new Error("e")}}},El=function(e,t){var o=Sl("Set");try{(new o)[e](Rl(0));try{return(new o)[e](Rl(-1)),!1}catch(i){if(!t)return!0;try{return(new o)[e](Tl(-1/0)),!1}catch(n){var r=new o;return r.add(1),r.add(2),t(r[e](Tl(1/0)))}}}catch(n){return!1}},Hl=function(e){var t=fl(this),o=wl(e),r=ml(t);return pl(t)<=o.size?yl(t,(function(e){o.includes(e)&&Cl(r,e)})):vl(o.getIterator(),(function(e){bl(t,e)&&Cl(r,e)})),r};gi({target:"Set",proto:!0,real:!0,forced:!El("difference",(function(e){return 0===e.size}))},{difference:Hl});var Ol=Bs,Ml=tl,xl=gl,Il=qs,Nl=js,Al=Ds.Set,kl=Ds.add,_l=Ds.has,Pl=b,Vl=function(e){var t=Ol(this),o=xl(e),r=new Al;return Ml(t)>o.size?Nl(o.getIterator(),(function(e){_l(t,e)&&kl(r,e)})):Il(t,(function(e){o.includes(e)&&kl(r,e)})),r};gi({target:"Set",proto:!0,real:!0,forced:!El("intersection",(function(e){return 2===e.size&&e.has(1)&&e.has(2)}))||Pl((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}))},{intersection:Vl});var Ll=Bs,Dl=Ds.has,Fl=tl,Bl=gl,Wl=qs,jl=js,zl=ms,$l=function(e){var t=Ll(this),o=Bl(e);if(Fl(t)<=o.size)return!1!==Wl(t,(function(e){if(o.includes(e))return!1}),!0);var r=o.getIterator();return!1!==jl(r,(function(e){if(Dl(t,e))return zl(r,"normal",!1)}))};gi({target:"Set",proto:!0,real:!0,forced:!El("isDisjointFrom",(function(e){return!e}))},{isDisjointFrom:$l});var Ul=Bs,Xl=tl,Gl=qs,Yl=gl,Kl=function(e){var t=Ul(this),o=Yl(e);return!(Xl(t)>o.size)&&!1!==Gl(t,(function(e){if(!o.includes(e))return!1}),!0)};gi({target:"Set",proto:!0,real:!0,forced:!El("isSubsetOf",(function(e){return e}))},{isSubsetOf:Kl});var ql=Bs,Ql=Ds.has,Jl=tl,Zl=gl,ea=js,ta=ms,oa=function(e){var t=ql(this),o=Zl(e);if(Jl(t)<o.size)return!1;var r=o.getIterator();return!1!==ea(r,(function(e){if(!Ql(t,e))return ta(r,"normal",!1)}))};gi({target:"Set",proto:!0,real:!0,forced:!El("isSupersetOf",(function(e){return!e}))},{isSupersetOf:oa});var ra=Bs,ia=el,na=gl,sa=js,la=Ds.add,aa=Ds.has,ha=Ds.remove,ca=function(e){var t=ra(this),o=na(e).getIterator(),r=ia(t);return sa(o,(function(e){aa(t,e)?ha(r,e):la(r,e)})),r};gi({target:"Set",proto:!0,real:!0,forced:!El("symmetricDifference")},{symmetricDifference:ca});var ua=Bs,da=Ds.add,ga=el,fa=gl,ma=js,pa=function(e){var t=ua(this),o=fa(e).getIterator(),r=ga(t);return ma(o,(function(e){da(r,e)})),r};gi({target:"Set",proto:!0,real:!0,forced:!El("union")},{union:pa});var wa=le,ya=TypeError,va=!b((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),ba=ot,Ca=te,Sa=Ze,Ra=va,Ta=Mo("IE_PROTO"),Ea=Object,Ha=Ea.prototype,Oa=Ra?Ea.getPrototypeOf:function(e){var t=Sa(e);if(ba(t,Ta))return t[Ta];var o=t.constructor;return Ca(o)&&t instanceof o?o.prototype:t instanceof Ea?Ha:null},Ma=lr,xa=jt,Ia={},Na=Pr,Aa=Vr,ka=Object.keys||function(e){return Na(e,Aa)},_a=C,Pa=zt,Va=jt,La=Gt,Da=Z,Fa=ka;Ia.f=_a&&!Pa?Object.defineProperties:function(e,t){La(e);for(var o,r=Da(t),i=Fa(t),n=i.length,s=0;n>s;)Va.f(e,o=i[s++],r[o]);return e};var Ba,Wa=se("document","documentElement"),ja=Gt,za=Ia,$a=Vr,Ua=xo,Xa=Wa,Ga=It,Ya="prototype",Ka="script",qa=Mo("IE_PROTO"),Qa=function(){},Ja=function(e){return"<"+Ka+">"+e+"</"+Ka+">"},Za=function(e){e.write(Ja("")),e.close();var t=e.parentWindow.Object;return e=null,t},eh=function(){try{Ba=new ActiveXObject("htmlfile")}catch(i){}var e,t,o;eh="undefined"!=typeof document?document.domain&&Ba?Za(Ba):(t=Ga("iframe"),o="java"+Ka+":",t.style.display="none",Xa.appendChild(t),t.src=String(o),(e=t.contentWindow.document).open(),e.write(Ja("document.F=Object")),e.close(),e.F):Za(Ba);for(var r=$a.length;r--;)delete eh[Ya][$a[r]];return eh()};Ua[qa]=!0;var th,oh,rh,ih=Object.create||function(e,t){var o;return null!==e?(Qa[Ya]=ja(e),o=new Qa,Qa[Ya]=null,o[qa]=e):o=eh(),void 0===t?o:za.f(o,t)},nh=b,sh=te,lh=re,ah=Oa,hh=dr,ch=pt("iterator"),uh=!1;[].keys&&("next"in(rh=[].keys())?(oh=ah(ah(rh)))!==Object.prototype&&(th=oh):uh=!0);var dh=!lh(th)||nh((function(){var e={};return th[ch].call(e)!==e}));dh&&(th={}),sh(th[ch])||hh(th,ch,(function(){return this}));var gh={IteratorPrototype:th,BUGGY_SAFARI_ITERATORS:uh},fh=gi,mh=y,ph=function(e,t){if(wa(t,e))return e;throw new ya("Incorrect invocation")},wh=Gt,yh=te,vh=Oa,bh=function(e,t,o){return o.get&&Ma(o.get,t,{getter:!0}),o.set&&Ma(o.set,t,{setter:!0}),xa.f(e,t,o)},Ch=ks,Sh=b,Rh=ot,Th=gh.IteratorPrototype,Eh=C,Hh="constructor",Oh="Iterator",Mh=pt("toStringTag"),xh=TypeError,Ih=mh[Oh],Nh=!yh(Ih)||Ih.prototype!==Th||!Sh((function(){Ih({})})),Ah=function(){if(ph(this,Th),vh(this)===Th)throw new xh("Abstract class Iterator not directly constructable")},kh=function(e,t){Eh?bh(Th,e,{configurable:!0,get:function(){return t},set:function(t){if(wh(this),this===Th)throw new xh("You can't redefine this property");Rh(this,e)?this[e]=t:Ch(this,e,t)}}):Th[e]=t};Rh(Th,Mh)||kh(Mh,Oh),!Nh&&Rh(Th,Hh)&&Th[Hh]!==Object||kh(Hh,Ah),Ah.prototype=Th,fh({global:!0,constructor:!0,forced:Nh},{Iterator:Ah});var _h=dr,Ph=E,Vh=ih,Lh=lo,Dh=function(e,t,o){for(var r in t)_h(e,r,t[r],o);return e},Fh=zo,Bh=Pe,Wh=gh.IteratorPrototype,jh=function(e,t){return{value:e,done:t}},zh=ms,$h=pt("toStringTag"),Uh="IteratorHelper",Xh="WrapForValidIterator",Gh=Fh.set,Yh=function(e){var t=Fh.getterFor(e?Xh:Uh);return Dh(Vh(Wh),{next:function(){var o=t(this);if(e)return o.nextHandler();if(o.done)return jh(void 0,!0);try{var r=o.nextHandler();return o.returnHandlerResult?r:jh(r,o.done)}catch(i){throw o.done=!0,i}},return:function(){var o=t(this),r=o.iterator;if(o.done=!0,e){var i=Bh(r,"return");return i?Ph(i,r):jh(void 0,!0)}if(o.inner)try{zh(o.inner.iterator,"normal")}catch(n){return zh(r,"throw",n)}return r&&zh(r,"normal"),jh(void 0,!0)}})},Kh=Yh(!0),qh=Yh(!1);Lh(qh,$h,"Iterator Helper");var Qh=function(e,t,o){var r=function(r,i){i?(i.iterator=r.iterator,i.next=r.next):i=r,i.type=t?Xh:Uh,i.returnHandlerResult=!!o,i.nextHandler=e,i.counter=0,i.done=!1,Gh(this,i)};return r.prototype=t?Kh:qh,r},Jh=Gt,Zh=ms,ec=function(e,t,o,r){try{return r?t(Jh(o)[0],o[1]):t(o)}catch(i){Zh(e,"throw",i)}},tc=y,oc=function(e,t){var o=tc.Iterator,r=o&&o.prototype,i=r&&r[e],n=!1;if(i)try{i.call({next:function(){return{done:!0}},return:function(){n=!0}},-1)}catch(s){s instanceof t||(n=!1)}if(!n)return i},rc=gi,ic=E,nc=Ae,sc=Gt,lc=ol,ac=Qh,hc=ec,cc=ms,uc=oc("filter",TypeError),dc=ac((function(){for(var e,t,o=this.iterator,r=this.predicate,i=this.next;;){if(e=sc(ic(i,o)),this.done=!!e.done)return;if(t=e.value,hc(o,r,[t,this.counter++],!0))return t}}));rc({target:"Iterator",proto:!0,real:!0,forced:uc},{filter:function(e){sc(this);try{nc(e)}catch(t){cc(this,"throw",t)}return uc?ic(uc,this,e):new dc(lc(this),{predicate:e})}});var gc=gi,fc=E,mc=xs,pc=Ae,wc=Gt,yc=ol,vc=ms,bc=oc("forEach",TypeError);gc({target:"Iterator",proto:!0,real:!0,forced:bc},{forEach:function(e){wc(this);try{pc(e)}catch(r){vc(this,"throw",r)}if(bc)return fc(bc,this,e);var t=yc(this),o=0;mc(t,(function(t){e(t,o++)}),{IS_RECORD:!0})}});var Cc=gi,Sc=E,Rc=Ae,Tc=Gt,Ec=ol,Hc=Qh,Oc=ec,Mc=ms,xc=oc("map",TypeError),Ic=Hc((function(){var e=this.iterator,t=Tc(Sc(this.next,e));if(!(this.done=!!t.done))return Oc(e,this.mapper,[t.value,this.counter++],!0)}));Cc({target:"Iterator",proto:!0,real:!0,forced:xc},{map:function(e){Tc(this);try{Rc(e)}catch(t){Mc(this,"throw",t)}return xc?Sc(xc,this,e):new Ic(Ec(this),{mapper:e})}});var Nc,Ac,kc,_c,Pc=L([].slice),Vc=TypeError,Lc=function(e,t){if(e<t)throw new Vc("Not enough arguments");return e},Dc=/(?:ipad|iphone|ipod).*applewebkit/i.test(ce),Fc=y,Bc=ce,Wc=W,jc=function(e){return Bc.slice(0,e.length)===e},zc=jc("Bun/")?"BUN":jc("Cloudflare-Workers")?"CLOUDFLARE":jc("Deno/")?"DENO":jc("Node.js/")?"NODE":Fc.Bun&&"string"==typeof Bun.version?"BUN":Fc.Deno&&"object"==typeof Deno.version?"DENO":"process"===Wc(Fc.process)?"NODE":Fc.window&&Fc.document?"BROWSER":"REST",$c=y,Uc=yi,Xc=Kn,Gc=te,Yc=ot,Kc=b,qc=Wa,Qc=Pc,Jc=It,Zc=Lc,eu=Dc,tu="NODE"===zc,ou=$c.setImmediate,ru=$c.clearImmediate,iu=$c.process,nu=$c.Dispatch,su=$c.Function,lu=$c.MessageChannel,au=$c.String,hu=0,cu={},uu="onreadystatechange";Kc((function(){Nc=$c.location}));var du=function(e){if(Yc(cu,e)){var t=cu[e];delete cu[e],t()}},gu=function(e){return function(){du(e)}},fu=function(e){du(e.data)},mu=function(e){$c.postMessage(au(e),Nc.protocol+"//"+Nc.host)};ou&&ru||(ou=function(e){Zc(arguments.length,1);var t=Gc(e)?e:su(e),o=Qc(arguments,1);return cu[++hu]=function(){Uc(t,void 0,o)},Ac(hu),hu},ru=function(e){delete cu[e]},tu?Ac=function(e){iu.nextTick(gu(e))}:nu&&nu.now?Ac=function(e){nu.now(gu(e))}:lu&&!eu?(_c=(kc=new lu).port2,kc.port1.onmessage=fu,Ac=Xc(_c.postMessage,_c)):$c.addEventListener&&Gc($c.postMessage)&&!$c.importScripts&&Nc&&"file:"!==Nc.protocol&&!Kc(mu)?(Ac=mu,$c.addEventListener("message",fu,!1)):Ac=uu in Jc("script")?function(e){qc.appendChild(Jc("script"))[uu]=function(){qc.removeChild(this),du(e)}}:function(e){setTimeout(gu(e),0)});var pu={set:ou,clear:ru},wu=pu.clear;gi({global:!0,bind:!0,enumerable:!0,forced:y.clearImmediate!==wu},{clearImmediate:wu});var yu=y,vu=yi,bu=te,Cu=zc,Su=ce,Ru=Pc,Tu=Lc,Eu=yu.Function,Hu=/MSIE .\./.test(Su)||"BUN"===Cu&&function(){var e=yu.Bun.version.split(".");return e.length<3||"0"===e[0]&&(e[1]<3||"3"===e[1]&&"0"===e[2])}(),Ou=gi,Mu=y,xu=pu.set,Iu=function(e,t){var o=t?2:1;return Hu?function(r,i){var n=Tu(arguments.length,1)>o,s=bu(r)?r:Eu(r),l=n?Ru(arguments,o):[],a=n?function(){vu(s,this,l)}:s;return t?e(a,i):e(a)}:e},Nu=Mu.setImmediate?Iu(xu,!1):xu;Ou({global:!0,bind:!0,enumerable:!0,forced:Mu.setImmediate!==Nu},{setImmediate:Nu});/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:Au,setPrototypeOf:ku,isFrozen:_u,getPrototypeOf:Pu,getOwnPropertyDescriptor:Vu}=Object;let{freeze:Lu,seal:Du,create:Fu}=Object,{apply:Bu,construct:Wu}="undefined"!=typeof Reflect&&Reflect;Lu||(Lu=function(e){return e}),Du||(Du=function(e){return e}),Bu||(Bu=function(e,t,o){return e.apply(t,o)}),Wu||(Wu=function(e,t){return new e(...t)});const ju=rd(Array.prototype.forEach),zu=rd(Array.prototype.lastIndexOf),$u=rd(Array.prototype.pop),Uu=rd(Array.prototype.push),Xu=rd(Array.prototype.splice),Gu=rd(String.prototype.toLowerCase),Yu=rd(String.prototype.toString),Ku=rd(String.prototype.match),qu=rd(String.prototype.replace),Qu=rd(String.prototype.indexOf),Ju=rd(String.prototype.trim),Zu=rd(Object.prototype.hasOwnProperty),ed=rd(RegExp.prototype.test),td=(od=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return Wu(od,t)});var od;function rd(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var o=arguments.length,r=new Array(o>1?o-1:0),i=1;i<o;i++)r[i-1]=arguments[i];return Bu(e,t,r)}}function id(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Gu;ku&&ku(e,null);let r=t.length;for(;r--;){let i=t[r];if("string"==typeof i){const e=o(i);e!==i&&(_u(t)||(t[r]=e),i=e)}e[i]=!0}return e}function nd(e){for(let t=0;t<e.length;t++)Zu(e,t)||(e[t]=null);return e}function sd(e){const t=Fu(null);for(const[o,r]of Au(e))Zu(e,o)&&(Array.isArray(r)?t[o]=nd(r):r&&"object"==typeof r&&r.constructor===Object?t[o]=sd(r):t[o]=r);return t}function ld(e,t){for(;null!==e;){const o=Vu(e,t);if(o){if(o.get)return rd(o.get);if("function"==typeof o.value)return rd(o.value)}e=Pu(e)}return function(){return null}}const ad=Lu(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),hd=Lu(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),cd=Lu(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),ud=Lu(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),dd=Lu(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),gd=Lu(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),fd=Lu(["#text"]),md=Lu(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),pd=Lu(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),wd=Lu(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),yd=Lu(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),vd=Du(/\{\{[\w\W]*|[\w\W]*\}\}/gm),bd=Du(/<%[\w\W]*|[\w\W]*%>/gm),Cd=Du(/\$\{[\w\W]*/gm),Sd=Du(/^data-[\-\w.\u00B7-\uFFFF]+$/),Rd=Du(/^aria-[\-\w]+$/),Td=Du(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Ed=Du(/^(?:\w+script|data):/i),Hd=Du(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Od=Du(/^html$/i),Md=Du(/^[a-z][.\w]*(-[.\w]+)+$/i);var xd=Object.freeze({__proto__:null,ARIA_ATTR:Rd,ATTR_WHITESPACE:Hd,CUSTOM_ELEMENT:Md,DATA_ATTR:Sd,DOCTYPE_NAME:Od,ERB_EXPR:bd,IS_ALLOWED_URI:Td,IS_SCRIPT_OR_DATA:Ed,MUSTACHE_EXPR:vd,TMPLIT_EXPR:Cd});const Id=1,Nd=3,Ad=7,kd=8,_d=9,Pd=function(){return"undefined"==typeof window?null:window};var Vd=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Pd();const o=t=>e(t);if(o.version="3.2.6",o.removed=[],!t||!t.document||t.document.nodeType!==_d||!t.Element)return o.isSupported=!1,o;let{document:r}=t;const i=r,n=i.currentScript,{DocumentFragment:s,HTMLTemplateElement:l,Node:a,Element:h,NodeFilter:c,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:d,DOMParser:g,trustedTypes:f}=t,m=h.prototype,p=ld(m,"cloneNode"),w=ld(m,"remove"),y=ld(m,"nextSibling"),v=ld(m,"childNodes"),b=ld(m,"parentNode");if("function"==typeof l){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let C,S="";const{implementation:R,createNodeIterator:T,createDocumentFragment:E,getElementsByTagName:H}=r,{importNode:O}=i;let M={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};o.isSupported="function"==typeof Au&&"function"==typeof b&&R&&void 0!==R.createHTMLDocument;const{MUSTACHE_EXPR:x,ERB_EXPR:I,TMPLIT_EXPR:N,DATA_ATTR:A,ARIA_ATTR:k,IS_SCRIPT_OR_DATA:_,ATTR_WHITESPACE:P,CUSTOM_ELEMENT:V}=xd;let{IS_ALLOWED_URI:L}=xd,D=null;const F=id({},[...ad,...hd,...cd,...dd,...fd]);let B=null;const W=id({},[...md,...pd,...wd,...yd]);let j=Object.seal(Fu(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),z=null,$=null,U=!0,X=!0,G=!1,Y=!0,K=!1,q=!0,Q=!1,J=!1,Z=!1,ee=!1,te=!1,oe=!1,re=!0,ie=!1,ne=!0,se=!1,le={},ae=null;const he=id({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ce=null;const ue=id({},["audio","video","img","source","image","track"]);let de=null;const ge=id({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),fe="http://www.w3.org/1998/Math/MathML",me="http://www.w3.org/2000/svg",pe="http://www.w3.org/1999/xhtml";let we=pe,ye=!1,ve=null;const be=id({},[fe,me,pe],Yu);let Ce=id({},["mi","mo","mn","ms","mtext"]),Se=id({},["annotation-xml"]);const Re=id({},["title","style","font","a","script"]);let Te=null;const Ee=["application/xhtml+xml","text/html"];let He=null,Oe=null;const Me=r.createElement("form"),xe=function(e){return e instanceof RegExp||e instanceof Function},Ie=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Oe||Oe!==e){if(e&&"object"==typeof e||(e={}),e=sd(e),Te=-1===Ee.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,He="application/xhtml+xml"===Te?Yu:Gu,D=Zu(e,"ALLOWED_TAGS")?id({},e.ALLOWED_TAGS,He):F,B=Zu(e,"ALLOWED_ATTR")?id({},e.ALLOWED_ATTR,He):W,ve=Zu(e,"ALLOWED_NAMESPACES")?id({},e.ALLOWED_NAMESPACES,Yu):be,de=Zu(e,"ADD_URI_SAFE_ATTR")?id(sd(ge),e.ADD_URI_SAFE_ATTR,He):ge,ce=Zu(e,"ADD_DATA_URI_TAGS")?id(sd(ue),e.ADD_DATA_URI_TAGS,He):ue,ae=Zu(e,"FORBID_CONTENTS")?id({},e.FORBID_CONTENTS,He):he,z=Zu(e,"FORBID_TAGS")?id({},e.FORBID_TAGS,He):sd({}),$=Zu(e,"FORBID_ATTR")?id({},e.FORBID_ATTR,He):sd({}),le=!!Zu(e,"USE_PROFILES")&&e.USE_PROFILES,U=!1!==e.ALLOW_ARIA_ATTR,X=!1!==e.ALLOW_DATA_ATTR,G=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Y=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,K=e.SAFE_FOR_TEMPLATES||!1,q=!1!==e.SAFE_FOR_XML,Q=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,oe=e.RETURN_TRUSTED_TYPE||!1,Z=e.FORCE_BODY||!1,re=!1!==e.SANITIZE_DOM,ie=e.SANITIZE_NAMED_PROPS||!1,ne=!1!==e.KEEP_CONTENT,se=e.IN_PLACE||!1,L=e.ALLOWED_URI_REGEXP||Td,we=e.NAMESPACE||pe,Ce=e.MATHML_TEXT_INTEGRATION_POINTS||Ce,Se=e.HTML_INTEGRATION_POINTS||Se,j=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&xe(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(j.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&xe(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(j.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(j.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),K&&(X=!1),te&&(ee=!0),le&&(D=id({},fd),B=[],!0===le.html&&(id(D,ad),id(B,md)),!0===le.svg&&(id(D,hd),id(B,pd),id(B,yd)),!0===le.svgFilters&&(id(D,cd),id(B,pd),id(B,yd)),!0===le.mathMl&&(id(D,dd),id(B,wd),id(B,yd))),e.ADD_TAGS&&(D===F&&(D=sd(D)),id(D,e.ADD_TAGS,He)),e.ADD_ATTR&&(B===W&&(B=sd(B)),id(B,e.ADD_ATTR,He)),e.ADD_URI_SAFE_ATTR&&id(de,e.ADD_URI_SAFE_ATTR,He),e.FORBID_CONTENTS&&(ae===he&&(ae=sd(ae)),id(ae,e.FORBID_CONTENTS,He)),ne&&(D["#text"]=!0),Q&&id(D,["html","head","body"]),D.table&&(id(D,["tbody"]),delete z.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw td('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw td('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');C=e.TRUSTED_TYPES_POLICY,S=C.createHTML("")}else void 0===C&&(C=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let o=null;const r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(o=t.getAttribute(r));const i="dompurify"+(o?"#"+o:"");try{return e.createPolicy(i,{createHTML:e=>e,createScriptURL:e=>e})}catch(n){return console.warn("TrustedTypes policy "+i+" could not be created."),null}}(f,n)),null!==C&&"string"==typeof S&&(S=C.createHTML(""));Lu&&Lu(e),Oe=e}},Ne=id({},[...hd,...cd,...ud]),Ae=id({},[...dd,...gd]),ke=function(e){Uu(o.removed,{element:e});try{b(e).removeChild(e)}catch(t){w(e)}},_e=function(e,t){try{Uu(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(r){Uu(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(ee||te)try{ke(t)}catch(r){}else try{t.setAttribute(e,"")}catch(r){}},Pe=function(e){let t=null,o=null;if(Z)e="<remove></remove>"+e;else{const t=Ku(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===Te&&we===pe&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const i=C?C.createHTML(e):e;if(we===pe)try{t=(new g).parseFromString(i,Te)}catch(s){}if(!t||!t.documentElement){t=R.createDocument(we,"template",null);try{t.documentElement.innerHTML=ye?S:i}catch(s){}}const n=t.body||t.documentElement;return e&&o&&n.insertBefore(r.createTextNode(o),n.childNodes[0]||null),we===pe?H.call(t,Q?"html":"body")[0]:Q?t.documentElement:n},Ve=function(e){return T.call(e.ownerDocument||e,e,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT|c.SHOW_PROCESSING_INSTRUCTION|c.SHOW_CDATA_SECTION,null)},Le=function(e){return e instanceof d&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof u)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},De=function(e){return"function"==typeof a&&e instanceof a};function Fe(e,t,r){ju(e,(e=>{e.call(o,t,r,Oe)}))}const Be=function(e){let t=null;if(Fe(M.beforeSanitizeElements,e,null),Le(e))return ke(e),!0;const r=He(e.nodeName);if(Fe(M.uponSanitizeElement,e,{tagName:r,allowedTags:D}),q&&e.hasChildNodes()&&!De(e.firstElementChild)&&ed(/<[/\w!]/g,e.innerHTML)&&ed(/<[/\w!]/g,e.textContent))return ke(e),!0;if(e.nodeType===Ad)return ke(e),!0;if(q&&e.nodeType===kd&&ed(/<[/\w]/g,e.data))return ke(e),!0;if(!D[r]||z[r]){if(!z[r]&&je(r)){if(j.tagNameCheck instanceof RegExp&&ed(j.tagNameCheck,r))return!1;if(j.tagNameCheck instanceof Function&&j.tagNameCheck(r))return!1}if(ne&&!ae[r]){const t=b(e)||e.parentNode,o=v(e)||e.childNodes;if(o&&t)for(let r=o.length-1;r>=0;--r){const i=p(o[r],!0);i.__removalCount=(e.__removalCount||0)+1,t.insertBefore(i,y(e))}}return ke(e),!0}return e instanceof h&&!function(e){let t=b(e);t&&t.tagName||(t={namespaceURI:we,tagName:"template"});const o=Gu(e.tagName),r=Gu(t.tagName);return!!ve[e.namespaceURI]&&(e.namespaceURI===me?t.namespaceURI===pe?"svg"===o:t.namespaceURI===fe?"svg"===o&&("annotation-xml"===r||Ce[r]):Boolean(Ne[o]):e.namespaceURI===fe?t.namespaceURI===pe?"math"===o:t.namespaceURI===me?"math"===o&&Se[r]:Boolean(Ae[o]):e.namespaceURI===pe?!(t.namespaceURI===me&&!Se[r])&&!(t.namespaceURI===fe&&!Ce[r])&&!Ae[o]&&(Re[o]||!Ne[o]):!("application/xhtml+xml"!==Te||!ve[e.namespaceURI]))}(e)?(ke(e),!0):"noscript"!==r&&"noembed"!==r&&"noframes"!==r||!ed(/<\/no(script|embed|frames)/i,e.innerHTML)?(K&&e.nodeType===Nd&&(t=e.textContent,ju([x,I,N],(e=>{t=qu(t,e," ")})),e.textContent!==t&&(Uu(o.removed,{element:e.cloneNode()}),e.textContent=t)),Fe(M.afterSanitizeElements,e,null),!1):(ke(e),!0)},We=function(e,t,o){if(re&&("id"===t||"name"===t)&&(o in r||o in Me))return!1;if(X&&!$[t]&&ed(A,t));else if(U&&ed(k,t));else if(!B[t]||$[t]){if(!(je(e)&&(j.tagNameCheck instanceof RegExp&&ed(j.tagNameCheck,e)||j.tagNameCheck instanceof Function&&j.tagNameCheck(e))&&(j.attributeNameCheck instanceof RegExp&&ed(j.attributeNameCheck,t)||j.attributeNameCheck instanceof Function&&j.attributeNameCheck(t))||"is"===t&&j.allowCustomizedBuiltInElements&&(j.tagNameCheck instanceof RegExp&&ed(j.tagNameCheck,o)||j.tagNameCheck instanceof Function&&j.tagNameCheck(o))))return!1}else if(de[t]);else if(ed(L,qu(o,P,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==Qu(o,"data:")||!ce[e])if(G&&!ed(_,qu(o,P,"")));else if(o)return!1;return!0},je=function(e){return"annotation-xml"!==e&&Ku(e,V)},ze=function(e){Fe(M.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Le(e))return;const r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:B,forceKeepAttr:void 0};let i=t.length;for(;i--;){const s=t[i],{name:l,namespaceURI:a,value:h}=s,c=He(l),u=h;let d="value"===l?u:Ju(u);if(r.attrName=c,r.attrValue=d,r.keepAttr=!0,r.forceKeepAttr=void 0,Fe(M.uponSanitizeAttribute,e,r),d=r.attrValue,!ie||"id"!==c&&"name"!==c||(_e(l,e),d="user-content-"+d),q&&ed(/((--!?|])>)|<\/(style|title)/i,d)){_e(l,e);continue}if(r.forceKeepAttr)continue;if(!r.keepAttr){_e(l,e);continue}if(!Y&&ed(/\/>/i,d)){_e(l,e);continue}K&&ju([x,I,N],(e=>{d=qu(d,e," ")}));const g=He(e.nodeName);if(We(g,c,d)){if(C&&"object"==typeof f&&"function"==typeof f.getAttributeType)if(a);else switch(f.getAttributeType(g,c)){case"TrustedHTML":d=C.createHTML(d);break;case"TrustedScriptURL":d=C.createScriptURL(d)}if(d!==u)try{a?e.setAttributeNS(a,l,d):e.setAttribute(l,d),Le(e)?ke(e):$u(o.removed)}catch(n){_e(l,e)}}else _e(l,e)}Fe(M.afterSanitizeAttributes,e,null)},$e=function e(t){let o=null;const r=Ve(t);for(Fe(M.beforeSanitizeShadowDOM,t,null);o=r.nextNode();)Fe(M.uponSanitizeShadowNode,o,null),Be(o),ze(o),o.content instanceof s&&e(o.content);Fe(M.afterSanitizeShadowDOM,t,null)};return o.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,n=null,l=null,h=null;if(ye=!e,ye&&(e="\x3c!--\x3e"),"string"!=typeof e&&!De(e)){if("function"!=typeof e.toString)throw td("toString is not a function");if("string"!=typeof(e=e.toString()))throw td("dirty is not a string, aborting")}if(!o.isSupported)return e;if(J||Ie(t),o.removed=[],"string"==typeof e&&(se=!1),se){if(e.nodeName){const t=He(e.nodeName);if(!D[t]||z[t])throw td("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof a)r=Pe("\x3c!----\x3e"),n=r.ownerDocument.importNode(e,!0),n.nodeType===Id&&"BODY"===n.nodeName||"HTML"===n.nodeName?r=n:r.appendChild(n);else{if(!ee&&!K&&!Q&&-1===e.indexOf("<"))return C&&oe?C.createHTML(e):e;if(r=Pe(e),!r)return ee?null:oe?S:""}r&&Z&&ke(r.firstChild);const c=Ve(se?e:r);for(;l=c.nextNode();)Be(l),ze(l),l.content instanceof s&&$e(l.content);if(se)return e;if(ee){if(te)for(h=E.call(r.ownerDocument);r.firstChild;)h.appendChild(r.firstChild);else h=r;return(B.shadowroot||B.shadowrootmode)&&(h=O.call(i,h,!0)),h}let u=Q?r.outerHTML:r.innerHTML;return Q&&D["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&ed(Od,r.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+u),K&&ju([x,I,N],(e=>{u=qu(u,e," ")})),C&&oe?C.createHTML(u):u},o.setConfig=function(){Ie(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),J=!0},o.clearConfig=function(){Oe=null,J=!1},o.isValidAttribute=function(e,t,o){Oe||Ie({});const r=He(e),i=He(t);return We(r,i,o)},o.addHook=function(e,t){"function"==typeof t&&Uu(M[e],t)},o.removeHook=function(e,t){if(void 0!==t){const o=zu(M[e],t);return-1===o?void 0:Xu(M[e],o,1)[0]}return $u(M[e])},o.removeHooks=function(e){M[e]=[]},o.removeAllHooks=function(){M={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},o}();function Ld(e,t){const o=t.length;let r=0;for(;r<o;)e.push(t[r]),r+=1}function Dd(e){const t=[];if(!e||0===e.length||!e[0]||0===e[0].length)return t;const o=e.length,r=e[0].length;for(let i=0;i<o;i++)for(let o=0;o<r;o++)t[o]||(t[o]=[]),t[o][i]=e[i][o];return t}function Fd(e,t,o,r){let i=-1,n=e,s=o;Array.isArray(e)||(n=Array.from(e));const l=n.length;for(r&&l&&(i+=1,s=n[i]),i+=1;i<l;)s=t(s,n[i],i,n),i+=1;return s}function Bd(e,t){let o=0,r=e;Array.isArray(e)||(r=Array.from(e));const i=r.length,n=[];let s=-1;for(;o<i;){const e=r[o];t(e,o,r)&&(s+=1,n[s]=e),o+=1}return n}function Wd(e,t){let o=0,r=e;Array.isArray(e)||(r=Array.from(e));const i=r.length,n=[];let s=-1;for(;o<i;){const e=r[o];s+=1,n[s]=t(e,o,r),o+=1}return n}function jd(e,t){let o=0,r=e;Array.isArray(e)||(r=Array.from(e));const i=r.length;for(;o<i&&!1!==t(r[o],o,r);)o+=1;return e}function zd(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];const[r,...i]=[...t];let n=r;return jd(i,(e=>{n=n.filter((t=>!e.includes(t)))})),n}function $d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return e.split(t)}function Ud(e){for(var t=arguments.length,o=new Array(t>1?t-1:0),r=1;r<t;r++)o[r-1]=arguments[r];const i=Fd(e,((e,t,r)=>e+t.replace(/\r?\n\s*/g,"")+(o[r]?o[r]:"")),"");return i.trim()}function Xd(e){let t;switch(typeof e){case"string":case"number":t=`${e}`;break;case"object":t=null===e?"":e.toString();break;case"undefined":t="";break;default:t=e.toString()}return t}function Gd(e){return void 0!==e}function Yd(e){return void 0===e}function Kd(e){return null===e||""===e||Yd(e)}const qd="length",Qd=e=>parseInt(e,16),Jd=e=>parseInt(e,10),Zd=(e,t,o)=>e.substr(t,o),eg=e=>e.codePointAt(0)-65,tg=e=>`${e}`.replace(/\-/g,"");let og=!1;const rg={invalid:()=>Ud`
    The license key for Handsontable is invalid.\x20
    If you need any help, contact <NAME_EMAIL>.`,expired:e=>{let{keyValidityDate:t,hotVersion:o}=e;return Ud`
    The license key for Handsontable expired on ${t}, and is not valid for the installed\x20
    version ${o}. Renew your license key at handsontable.com or downgrade to a version released prior\x20
    to ${t}. If you need any help, contact <NAME_EMAIL>.`},missing:()=>Ud`
    The license key for Handsontable is missing. Use your purchased key to activate the product.\x20
    Alternatively, you can activate Handsontable to use for non-commercial purposes by\x20
    passing the key: 'non-commercial-and-evaluation'. If you need any help, contact\x20
    <NAME_EMAIL>.`,non_commercial:()=>""},ig={invalid:()=>Ud`
    The license key for Handsontable is invalid.\x20
    <a href="https://handsontable.com/docs/tutorial-license-key.html" target="_blank">Read more</a> on how to\x20
    install it properly or contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.`,expired:e=>{let{keyValidityDate:t,hotVersion:o}=e;return Ud`
    The license key for Handsontable expired on ${t}, and is not valid for the installed\x20
    version ${o}. <a href="https://handsontable.com/pricing" target="_blank">Renew</a> your\x20
    license key or downgrade to a version released prior to ${t}. If you need any\x20
    help, contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.`},missing:()=>Ud`
    The license key for Handsontable is missing. Use your purchased key to activate the product.\x20
    Alternatively, you can activate Handsontable to use for non-commercial purposes by\x20
    passing the key: 'non-commercial-and-evaluation'.\x20
    <a href="https://handsontable.com/docs/tutorial-license-key.html" target="_blank">Read more</a> about it in\x20
    the documentation or contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.`,non_commercial:()=>""};function ng(e,t){const o=!Kd(e),r="string"==typeof e&&"non-commercial-and-evaluation"===e.toLowerCase(),i="15.3.0";let n,s="invalid",l="invalid";const a=function(e){let t=[][qd],o=t;if(e[qd]!==eg("Z"))return!1;for(let r="",i="B<H4P+".split(""),n=eg(i.shift());n;n=eg(i.shift()||"A"))--n<""[qd]?o|=(Jd(`${Jd(Qd(r)+(Qd(Zd(e,Math.abs(n),2))+[]).padStart(2,"0"))}`)%97||2)>>1:r=Zd(e,n,n?1===i[qd]?9:8:6);return o===t}(e=tg(e||""));if(o||r||a)if(a){const t=g("29/04/2025","DD/MM/YYYY"),o=Math.floor(t.toDate().getTime()/864e5),r=Qd(Zd(tg(h=e),Qd("12"),eg("F")))/(Qd(Zd(tg(h),eg("B"),~~![][qd]))||9);n=g(864e5*(r+1),"x").format("MMMM DD, YYYY"),o>r?(s="expired",l="expired"):(s="valid",l="valid")}else r?(s="non_commercial",l="valid"):(s="invalid",l="invalid");else s="missing",l="missing";var h;if("undefined"!=typeof location&&/^([a-z0-9\-]+\.)?\x68\x61\x6E\x64\x73\x6F\x6E\x74\x61\x62\x6C\x65\x2E\x63\x6F\x6D$/i.test(location.host)&&(s="valid",l="valid"),og||"valid"===s||(rg[s]({keyValidityDate:n,hotVersion:i})&&console["non_commercial"===s?"info":"warn"](rg[s]({keyValidityDate:n,hotVersion:i})),og=!0),"valid"!==l&&t.parentNode&&ig[l]({keyValidityDate:n,hotVersion:i})){const e=document.createElement("div");e.className="handsontable hot-display-license-info",e.innerHTML=ig[l]({keyValidityDate:n,hotVersion:i}),t.parentNode.insertBefore(e,t.nextSibling)}}function sg(e){return e[0].toUpperCase()+e.substr(1)}function lg(){function e(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return e()+e()+e()+e()}const ag=e=>["tabindex",e],hg=e=>["aria-rowcount",e],cg=e=>["aria-colcount",e],ug=e=>["aria-rowindex",e],dg=e=>["aria-colindex",e];function gg(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=-1,r=null,i=e;for(;null!==i;){if(o===t){r=i;break}i.host&&i.nodeType===Node.DOCUMENT_FRAGMENT_NODE?i=i.host:(o+=1,i=i.parentNode)}return r}function fg(e){return function(e){return Object.getPrototypeOf(e.parent)&&e.frameElement}(e)&&e.parent}function mg(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=arguments.length>2?arguments[2]:void 0;const{ELEMENT_NODE:r,DOCUMENT_FRAGMENT_NODE:i}=Node;let n=e;for(;null!=n&&n!==o;){const{nodeType:e,nodeName:o}=n;if(e===r&&(t.includes(o)||t.includes(n)))return n;const{host:s}=n;n=s&&e===i?s:n.parentNode}return null}function pg(e,t,o){const r=[];let i=e;for(;i&&(i=mg(i,t,o),i&&(!o||o.contains(i)));)r.push(i),i=i.host&&i.nodeType===Node.DOCUMENT_FRAGMENT_NODE?i.host:i.parentNode;const n=r.length;return n?r[n-1]:null}function wg(e,t){let o=e.parentNode,r=[];for("string"==typeof t?r=e.defaultView?Array.prototype.slice.call(e.querySelectorAll(t),0):Array.prototype.slice.call(e.ownerDocument.querySelectorAll(t),0):r.push(t);null!==o;){if(r.indexOf(o)>-1)return!0;o=o.parentNode}return!1}function yg(e,t,o){const r=o.parentElement.querySelector(`.ht_clone_${e}`);return r?r.contains(t):null}function vg(e){return e&&e.length?e.filter((e=>!!e)):[]}function bg(e,t){if(!e||!e.length)return t?{regexFree:[],regexes:[]}:[];const o=[],r=[];return r.push(...e.filter((e=>{const r=e instanceof RegExp;return r&&t&&o.push(e),!r}))),t?{regexFree:r,regexes:o}:r}function Cg(e,t){return void 0!==e.classList&&"string"==typeof t&&""!==t&&e.classList.contains(t)}function Sg(e,t){"string"==typeof t&&(t=t.split(" ")),(t=vg(t)).length>0&&e.classList.add(...t)}function Rg(e,t){"string"==typeof t?t=t.split(" "):t instanceof RegExp&&(t=[t]);let{regexFree:o,regexes:r}=bg(t,!0);o=vg(o),o.length>0&&e.classList.remove(...o),r.forEach((t=>{e.classList.forEach((o=>{t.test(o)&&e.classList.remove(o)}))}))}function Tg(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=arguments.length>2?arguments[2]:void 0;Array.isArray(t)||(t=[[t,o]]),t.forEach((t=>{Array.isArray(t)&&""!==t[0]&&e.setAttribute(...t)}))}function Eg(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];"string"==typeof t?t=t.split(" "):t instanceof RegExp&&(t=[t]);const{regexFree:o,regexes:r}=bg(t,!0);o.forEach((t=>{""!==t&&e.removeAttribute(t)})),r.forEach((t=>{e.getAttributeNames().forEach((o=>{t.test(o)&&e.removeAttribute(o)}))}))}function Hg(e){if(3===e.nodeType)e.parentNode.removeChild(e);else if(["TABLE","THEAD","TBODY","TFOOT","TR"].indexOf(e.nodeName)>-1){const t=e.childNodes;for(let e=t.length-1;e>=0;e--)Hg(t[e])}}function Og(e){let t;for(;t=e.lastChild;)e.removeChild(t)}const Mg=/(<(.*)>|&(.*);)/;function xg(e,t){let o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];var r,i;Mg.test(t)?e.innerHTML=o?(r=t,Vd.sanitize(r,i)):t:Ig(e,t)}function Ig(e,t){const o=e.firstChild;o&&3===o.nodeType&&null===o.nextSibling?o.textContent=t:(Og(e),e.appendChild(e.ownerDocument.createTextNode(t)))}function Ng(e){const t=e.ownerDocument.documentElement,o=e.ownerDocument.defaultView;let r=e;for(;r!==t;){if(null===r)return!1;if(r.nodeType===Node.DOCUMENT_FRAGMENT_NODE){if(r.host){if(r.host.impl)return Ng(r.host.impl);if(r.host)return Ng(r.host);throw new Error("Lost in Web Components world")}return!1}if("none"===o.getComputedStyle(r).display)return!1;r=r.parentNode}return!0}function Ag(e){const t=e.ownerDocument,o=t.defaultView,r=t.documentElement;let i,n,s,l=e;for(i=l.offsetLeft,n=l.offsetTop,s=l;(l=l.offsetParent)&&l!==t.body&&"offsetLeft"in l;)i+=l.offsetLeft,n+=l.offsetTop,s=l;return s&&"fixed"===s.style.position&&(i+=o.pageXOffset||r.scrollLeft,n+=o.pageYOffset||r.scrollTop),{left:i,top:n}}function kg(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:window).scrollY}function _g(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:window).scrollX}function Pg(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;return e===t?kg(t):e.scrollTop}function Vg(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;return e===t?_g(t):e.scrollLeft}function Lg(e){let t=e.ownerDocument,o=t?t.defaultView:void 0;t||(t=e.document?e.document:e,o=t.defaultView);const r=["auto","scroll"];let i=e.parentNode;for(;i&&i.style&&t.body!==i;){let{overflow:e,overflowX:t,overflowY:n}=i.style;if([e,t,n].includes("scroll"))return i;if(({overflow:e,overflowX:t,overflowY:n}=o.getComputedStyle(i)),r.includes(e)||r.includes(t)||r.includes(n))return i;if(i.clientHeight<=i.scrollHeight+1&&(r.includes(n)||r.includes(e)))return i;if(i.clientWidth<=i.scrollWidth+1&&(r.includes(t)||r.includes(e)))return i;i=i.parentNode}return o}function Dg(e){const t=e.ownerDocument,o=t.defaultView;let r=e.parentNode;for(;r&&r.style&&t.body!==r;){if("visible"!==r.style.overflow&&""!==r.style.overflow)return r;const e=o.getComputedStyle(r),t=["scroll","hidden","auto"],i=e.getPropertyValue("overflow"),n=e.getPropertyValue("overflow-y"),s=e.getPropertyValue("overflow-x");if(t.includes(i)||t.includes(n)||t.includes(s))return r;r=r.parentNode}return o}function Fg(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:window;if(!e)return;if(e===o)return"width"===t?`${o.innerWidth}px`:"height"===t?`${o.innerHeight}px`:void 0;const r=e.style[t];if(""!==r&&void 0!==r)return r;const i=o.getComputedStyle(e);return""!==i[t]&&void 0!==i[t]?i[t]:void 0}function Bg(e){return e.offsetWidth}function Wg(e){return e.offsetHeight}function jg(e){return e.clientHeight||e.innerHeight}function zg(e){return e.clientWidth||e.innerWidth}function $g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window;e.getSelection&&(e.getSelection().empty?e.getSelection().empty():e.getSelection().removeAllRanges&&e.getSelection().removeAllRanges())}function Ug(e,t,o){if(void 0===o&&(o=t),e.setSelectionRange){e.focus();try{e.setSelectionRange(t,o)}catch(r){const i=e.parentNode,n=i.style.display;i.style.display="block",e.setSelectionRange(t,o),i.style.display=n}}}let Xg;function Gg(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;return void 0===Xg&&(Xg=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;const t=e.createElement("div");t.style.height="200px",t.style.width="100%";const o=e.createElement("div");o.style.boxSizing="content-box",o.style.height="150px",o.style.left="0px",o.style.overflow="hidden",o.style.position="absolute",o.style.top="0px",o.style.width="200px",o.style.visibility="hidden",o.appendChild(t),(e.body||e.documentElement).appendChild(o);const r=t.offsetWidth;o.style.overflow="scroll";let i=t.offsetWidth;return r===i&&(i=o.clientWidth),(e.body||e.documentElement).removeChild(o),r-i}(e)),Xg}function Yg(e,t,o){e.style.transform=`translate3d(${t},${o},0)`}function Kg(e){e.style.transform&&""!==e.style.transform&&(e.style.transform="")}function qg(e){return e&&(["INPUT","SELECT","TEXTAREA"].indexOf(e.nodeName)>-1||"true"===e.contentEditable)}function Qg(e){return qg(e)&&!1===e.hasAttribute("data-hot-input")}function Jg(e){var t;const o=null==e||null===(t=e.ownerDocument)||void 0===t?void 0:t.defaultView.Element;return!!(o&&null!==o&&e instanceof o)}function Zg(e){return"function"==typeof e}function ef(e,t,o,r,i,n,s,l){return Gd(l)?e.call(t,o,r,i,n,s,l):Gd(s)?e.call(t,o,r,i,n,s):Gd(n)?e.call(t,o,r,i,n):Gd(i)?e.call(t,o,r,i):Gd(r)?e.call(t,o,r):Gd(o)?e.call(t,o):e.call(t)}var tf=Mn,of=te,rf=W,nf=Ui,sf=L([].push),lf=gi,af=se,hf=yi,cf=E,uf=L,df=b,gf=te,ff=He,mf=Pc,pf=function(e){if(of(e))return e;if(tf(e)){for(var t=e.length,o=[],r=0;r<t;r++){var i=e[r];"string"==typeof i?sf(o,i):"number"!=typeof i&&"Number"!==rf(i)&&"String"!==rf(i)||sf(o,nf(i))}var n=o.length,s=!0;return function(e,t){if(s)return s=!1,t;if(tf(this))return t;for(var r=0;r<n;r++)if(o[r]===e)return t}}},wf=be,yf=String,vf=af("JSON","stringify"),bf=uf(/./.exec),Cf=uf("".charAt),Sf=uf("".charCodeAt),Rf=uf("".replace),Tf=uf(1..toString),Ef=/[\uD800-\uDFFF]/g,Hf=/^[\uD800-\uDBFF]$/,Of=/^[\uDC00-\uDFFF]$/,Mf=!wf||df((function(){var e=af("Symbol")("stringify detection");return"[null]"!==vf([e])||"{}"!==vf({a:e})||"{}"!==vf(Object(e))})),xf=df((function(){return'"\\udf06\\ud834"'!==vf("\udf06\ud834")||'"\\udead"'!==vf("\udead")})),If=function(e,t){var o=mf(arguments),r=pf(t);if(gf(r)||void 0!==e&&!ff(e))return o[1]=function(e,t){if(gf(r)&&(t=cf(r,this,yf(e),t)),!ff(t))return t},hf(vf,null,o)},Nf=function(e,t,o){var r=Cf(o,t-1),i=Cf(o,t+1);return bf(Hf,e)&&!bf(Of,i)||bf(Of,e)&&!bf(Hf,r)?"\\u"+Tf(Sf(e,0),16):e};function Af(e){let t;return Array.isArray(e)?t=e.length?new Array(e.length).fill(null):[]:(t={},Bf(e,((e,o)=>{"__children"!==o&&(e&&"object"==typeof e&&!Array.isArray(e)?t[o]=Af(e):Array.isArray(e)?e.length&&"object"==typeof e[0]&&!Array.isArray(e[0])?t[o]=[Af(e[0])]:t[o]=[]:t[o]=null)}))),t}function kf(e,t,o){const r=Array.isArray(o);return Bf(t,((t,i)=>{(!1===r||o.includes(i))&&(e[i]=t)})),e}function _f(e,t){Bf(t,((o,r)=>{t[r]&&"object"==typeof t[r]?(e[r]||(Array.isArray(t[r])?e[r]=[]:"[object Date]"===Object.prototype.toString.call(t[r])?e[r]=t[r]:e[r]={}),_f(e[r],t[r])):e[r]=t[r]}))}function Pf(e){return"object"==typeof e?JSON.parse(JSON.stringify(e)):e}function Vf(e){e.MIXINS||(e.MIXINS=[]);for(var t=arguments.length,o=new Array(t>1?t-1:0),r=1;r<t;r++)o[r-1]=arguments[r];return jd(o,(t=>{e.MIXINS.push(t.MIXIN_NAME),Bf(t,((t,o)=>{if(void 0!==e.prototype[o])throw new Error(`Mixin conflict. Property '${o}' already exist and cannot be overwritten.`);if("function"==typeof t)e.prototype[o]=t;else{const r=function(e,t){const o=`_${e}`;return function(){return void 0===this[o]&&(this[o]=(e=>{let t=e;return(Array.isArray(t)||Df(t))&&(t=Pf(t)),t})(t)),this[o]}},i=function(e){const t=`_${e}`;return function(e){this[t]=e}};Object.defineProperty(e.prototype,o,{get:r(o,t),set:i(o),configurable:!0})}}))})),e}function Lf(e,t){return JSON.stringify(e)===JSON.stringify(t)}function Df(e){return"[object Object]"===Object.prototype.toString.call(e)}function Ff(e,t,o,r){r.value=o,r.writable=!1!==r.writable,r.enumerable=!1!==r.enumerable,r.configurable=!1!==r.configurable,Object.defineProperty(e,t,r)}function Bf(e,t){for(const o in e)if((!e.hasOwnProperty||e.hasOwnProperty&&Object.prototype.hasOwnProperty.call(e,o))&&!1===t(e[o],o,e))break;return e}function Wf(e,t,o){if("string"!=typeof t)return;const r=t.split(".");let i=e;r.forEach(((e,t)=>{"__proto__"!==e&&"constructor"!==e&&"prototype"!==e&&(t!==r.length-1?($f(i,e)||(i[e]={}),i=i[e]):i[e]=o)}))}function jf(e){if(!Df(e))return 0;const t=function(e){let o=0;return Df(e)?Bf(e,((e,r)=>{"__children"!==r&&(o+=t(e))})):o+=1,o};return t(e)}function zf(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"value";const o=`_${t}`,r={_touched:!1,[o]:e,isTouched(){return this._touched}};return Object.defineProperty(r,t,{get(){return this[o]},set(e){this._touched=!0,this[o]=e},enumerable:!0,configurable:!0}),r}function $f(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function Uf(e){return window.requestAnimationFrame(e)}vf&&lf({target:"JSON",stat:!0,arity:3,forced:Mf||xf},{stringify:function(e,t,o){var r=mf(arguments),i=hf(Mf?If:vf,null,r);return xf&&"string"==typeof i?Rf(i,Ef,Nf):i}});const Xf=e=>{const t={value:!1,test:(o,r)=>{t.value=e(o,r)}};return t},Gf={chrome:Xf(((e,t)=>/Chrome/.test(e)&&/Google/.test(t))),chromeWebKit:Xf((e=>/CriOS/.test(e))),edge:Xf((e=>/Edge/.test(e))),edgeWebKit:Xf((e=>/EdgiOS/.test(e))),firefox:Xf((e=>/Firefox/.test(e))),firefoxWebKit:Xf((e=>/FxiOS/.test(e))),mobile:Xf((e=>/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(e))),safari:Xf(((e,t)=>/Safari/.test(e)&&/Apple Computer/.test(t)))},Yf={mac:Xf((e=>/^Mac/.test(e))),win:Xf((e=>/^Win/.test(e))),linux:Xf((e=>/^Linux/.test(e))),ios:Xf((e=>/iPhone|iPad|iPod/i.test(e)))};function Kf(){return Gf.firefox.value}function qf(){return Gf.mobile.value}function Qf(){return Yf.ios.value}function Jf(){return Yf.mac.value}"undefined"!=typeof window&&(function(){let{userAgent:e=navigator.userAgent,vendor:t=navigator.vendor}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Bf(Gf,(o=>{let{test:r}=o;r(e,t)}))}(),function(){let{platform:e=navigator.platform}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Bf(Yf,(t=>{let{test:o}=t;o(e)}))}());const Zf={ALT:18,ARROW_DOWN:40,ARROW_LEFT:37,ARROW_RIGHT:39,ARROW_UP:38,AUDIO_DOWN:Kf()?182:174,AUDIO_MUTE:Kf()?181:173,AUDIO_UP:Kf()?183:175,BACKSPACE:8,CAPS_LOCK:20,COMMA:188,COMMAND_LEFT:91,COMMAND_RIGHT:93,COMMAND_FIREFOX:224,CONTROL:17,DELETE:46,END:35,ENTER:13,ESCAPE:27,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,F13:124,F14:125,F15:126,F16:127,F17:128,F18:129,F19:130,HOME:36,INSERT:45,MEDIA_NEXT:176,MEDIA_PLAY_PAUSE:179,MEDIA_PREV:177,MEDIA_STOP:178,NULL:0,NUM_LOCK:144,PAGE_DOWN:34,PAGE_UP:33,PAUSE:19,PERIOD:190,SCROLL_LOCK:145,SHIFT:16,SPACE:32,TAB:9,A:65,C:67,D:68,F:70,L:76,O:79,P:80,S:83,V:86,X:88,Y:89,Z:90},em=[Zf.ALT,Zf.ARROW_DOWN,Zf.ARROW_LEFT,Zf.ARROW_RIGHT,Zf.ARROW_UP,Zf.AUDIO_DOWN,Zf.AUDIO_MUTE,Zf.AUDIO_UP,Zf.BACKSPACE,Zf.CAPS_LOCK,Zf.DELETE,Zf.END,Zf.ENTER,Zf.ESCAPE,Zf.F1,Zf.F2,Zf.F3,Zf.F4,Zf.F5,Zf.F6,Zf.F7,Zf.F8,Zf.F9,Zf.F10,Zf.F11,Zf.F12,Zf.F13,Zf.F14,Zf.F15,Zf.F16,Zf.F17,Zf.F18,Zf.F19,Zf.HOME,Zf.INSERT,Zf.MEDIA_NEXT,Zf.MEDIA_PLAY_PAUSE,Zf.MEDIA_PREV,Zf.MEDIA_STOP,Zf.NULL,Zf.NUM_LOCK,Zf.PAGE_DOWN,Zf.PAGE_UP,Zf.PAUSE,Zf.SCROLL_LOCK,Zf.SHIFT,Zf.TAB];function tm(e){e.isImmediatePropagationEnabled=!1,e.cancelBubble=!0}function om(e){return!1===e.isImmediatePropagationEnabled}function rm(e){return 2===e.button}function im(e){return 0===e.button}function nm(){Gd(console)&&console.warn(...arguments)}const sm=["afterCellMetaReset","afterChange","afterContextMenuDefaultOptions","beforeContextMenuSetItems","afterDropdownMenuDefaultOptions","beforeDropdownMenuSetItems","afterContextMenuHide","beforeContextMenuShow","afterContextMenuShow","afterCopyLimit","beforeCreateCol","afterColumnSequenceChange","afterCreateCol","beforeCreateRow","afterCreateRow","afterDeselect","afterDestroy","afterDocumentKeyDown","afterDrawSelection","beforeRemoveCellClassNames","beforeCompositionStart","afterGetCellMeta","afterGetColHeader","afterGetRowHeader","afterInit","afterLoadData","afterUpdateData","afterMomentumScroll","afterOnCellCornerMouseDown","afterOnCellCornerDblClick","afterOnCellMouseDown","afterOnCellMouseUp","afterOnCellContextMenu","afterOnCellMouseOver","afterOnCellMouseOut","afterRemoveCol","afterRemoveRow","beforeRenderer","afterRenderer","afterRowSequenceChange","beforeViewportScrollVertically","beforeViewportScrollHorizontally","beforeViewportScroll","afterScrollHorizontally","afterScrollVertically","afterScroll","afterSelection","afterSelectionByProp","afterSelectionEnd","afterSelectionEndByProp","afterSelectionFocusSet","beforeSelectColumns","afterSelectColumns","beforeSelectRows","afterSelectRows","afterSetCellMeta","afterRemoveCellMeta","afterSetDataAtCell","afterSetDataAtRowProp","afterSetSourceDataAtCell","afterSetTheme","afterUpdateSettings","afterValidate","beforeLanguageChange","afterLanguageChange","beforeAutofill","afterAutofill","beforeCellAlignment","beforeChange","beforeChangeRender","beforeDrawBorders","beforeGetCellMeta","beforeRemoveCellMeta","beforeInit","beforeInitWalkontable","beforeLoadData","beforeUpdateData","beforeKeyDown","beforeOnCellMouseDown","beforeOnCellMouseUp","beforeOnCellContextMenu","beforeOnCellMouseOver","beforeOnCellMouseOut","beforeRemoveCol","beforeRemoveRow","beforeViewRender","afterViewRender","beforeRender","afterRender","beforeRowWrap","beforeColumnWrap","beforeSetCellMeta","beforeSelectionFocusSet","beforeSetRangeStartOnly","beforeSetRangeStart","beforeSetRangeEnd","beforeSelectionHighlightSet","beforeTouchScroll","beforeValidate","beforeValueRender","construct","init","modifyColHeader","modifyColWidth","modifyFiltersMultiSelectValue","modifyFocusedElement","modifyRowHeader","modifyRowHeight","modifyRowHeightByOverlayName","modifyData","modifySourceData","modifyRowData","modifyGetCellCoords","modifyGetCoordsElement","modifyFocusOnTabNavigation","beforeHighlightingRowHeader","beforeHighlightingColumnHeader","persistentStateLoad","persistentStateReset","persistentStateSave","beforeColumnSort","afterColumnSort","modifyAutofillRange","modifyCopyableRange","beforeCut","afterCut","beforeCopy","afterCopy","beforePaste","afterPaste","beforeColumnFreeze","afterColumnFreeze","beforeColumnMove","afterColumnMove","beforeColumnUnfreeze","afterColumnUnfreeze","beforeRowMove","afterRowMove","beforeColumnResize","afterColumnResize","beforeRowResize","afterRowResize","afterGetColumnHeaderRenderers","afterGetRowHeaderRenderers","beforeStretchingColumnWidth","beforeFilter","afterFilter","afterFormulasValuesUpdate","afterNamedExpressionAdded","afterNamedExpressionRemoved","afterSheetAdded","afterSheetRenamed","afterSheetRemoved","modifyColumnHeaderHeight","modifyColumnHeaderValue","beforeUndo","beforeUndoStackChange","afterUndo","afterUndoStackChange","beforeRedo","beforeRedoStackChange","afterRedo","afterRedoStackChange","modifyRowHeaderWidth","modifyTransformFocus","modifyTransformStart","modifyTransformEnd","afterModifyTransformFocus","afterModifyTransformStart","afterModifyTransformEnd","afterViewportRowCalculatorOverride","afterViewportColumnCalculatorOverride","afterPluginsInitialized","beforeHideRows","afterHideRows","beforeUnhideRows","afterUnhideRows","beforeHideColumns","afterHideColumns","beforeUnhideColumns","afterUnhideColumns","beforeTrimRow","afterTrimRow","beforeUntrimRow","afterUntrimRow","beforeDropdownMenuShow","afterDropdownMenuShow","afterDropdownMenuHide","beforeAddChild","afterAddChild","beforeDetachChild","afterDetachChild","beforeBeginEditing","afterBeginEditing","beforeMergeCells","afterMergeCells","beforeUnmergeCells","afterUnmergeCells","afterListen","afterUnlisten","afterRefreshDimensions","beforeRefreshDimensions","beforeColumnCollapse","afterColumnCollapse","beforeColumnExpand","afterColumnExpand","modifyAutoColumnSizeSeed"],lm=new Map([["modifyRow","8.0.0"],["modifyCol","8.0.0"],["unmodifyRow","8.0.0"],["unmodifyCol","8.0.0"],["skipLengthCache","8.0.0"],["hiddenColumn","8.0.0"],["hiddenRow","8.0.0"]]),am=new Map([[]]);var hm=Tr,cm=y,um=pt,dm=ih,gm=jt.f,fm=um("unscopables"),mm=Array.prototype;void 0===mm[fm]&&gm(mm,fm,{configurable:!0,value:dm(null)});var pm=function(e){mm[fm][e]=!0},wm=gi,ym=Ae,vm=Z,bm=function(e,t,o){for(var r=0,i=arguments.length>2?o:hm(t),n=new e(i);i>r;)n[r]=t[r++];return n},Cm=function(e,t){var o=cm[e],r=o&&o.prototype;return r&&r[t]},Sm=pm,Rm=Array,Tm=L(Cm("Array","sort"));wm({target:"Array",proto:!0},{toSorted:function(e){void 0!==e&&ym(e);var t=vm(this),o=bm(Rm,t);return Tm(o,e)}}),Sm("toSorted");var Em=gi,Hm=E,Om=xs,Mm=Ae,xm=Gt,Im=ol,Nm=ms,Am=oc("find",TypeError);function km(e,t,o){_m(e,t),t.set(e,o)}function _m(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Pm(e,t,o){return e.set(Lm(e,t),o),o}function Vm(e,t){return e.get(Lm(e,t))}function Lm(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}Em({target:"Iterator",proto:!0,real:!0,forced:Am},{find:function(e){xm(this);try{Mm(e)}catch(r){Nm(this,"throw",r)}if(Am)return Hm(Am,this,e);var t=Im(this),o=0;return Om(t,(function(t,r){if(e(t,o++))return r(t)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}});var Dm=new WeakMap,Fm=new WeakMap,Bm=new WeakMap,Wm=new WeakSet;class jm{constructor(){var e,t;_m(e=this,t=Wm),t.add(e),km(this,Dm,new Map),km(this,Fm,new Map),km(this,Bm,new Set),sm.forEach((e=>Lm(Wm,this,zm).call(this,e)))}getHooks(e){var t;return null!==(t=Vm(Dm,this).get(e))&&void 0!==t?t:[]}add(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Vm(Dm,this).has(e)||(Lm(Wm,this,zm).call(this,e),sm.push(e));const r=Vm(Dm,this).get(e);if(r.find((e=>e.callback===t)))return;const i=Number.isInteger(o.orderIndex)?o.orderIndex:0,n=!!o.runOnce,s=!!o.initialHook;let l=!1;if(s){const e=r.find((e=>e.initialHook));e&&(e.callback=t,l=!0)}if(!l){r.push({callback:t,orderIndex:i,runOnce:n,initialHook:s,skip:!1});let o=Vm(Bm,this).has(e);o||0===i||(o=!0,Vm(Bm,this).add(e)),o&&r.length>1&&Vm(Dm,this).set(e,r.toSorted(((e,t)=>e.orderIndex-t.orderIndex)))}}has(e){return Vm(Dm,this).has(e)&&Vm(Dm,this).get(e).length>0}remove(e,t){if(!Vm(Dm,this).has(e))return!1;const o=Vm(Dm,this).get(e),r=o.find((e=>e.callback===t));if(r){let t=Vm(Fm,this).get(e);return r.skip=!0,t+=1,t>100&&(Vm(Dm,this).set(e,o.filter((e=>!e.skip))),t=0),Vm(Fm,this).set(e,t),!0}return!1}destroy(){Vm(Dm,this).clear(),Vm(Fm,this).clear(),Pm(Dm,this,null),Pm(Fm,this,null)}}function zm(e){Vm(Dm,this).set(e,[]),Vm(Fm,this).set(e,0)}function $m(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const Um=Ud`The plugin hook "[hookName]" was removed in Handsontable [removedInVersion].\x20
  Please consult release notes https://github.com/handsontable/handsontable/releases/tag/[removedInVersion] to\x20
  learn about the migration path.`;class Xm{constructor(){$m(this,"globalBucket",new jm)}static getSingleton(){return Gm}getBucket(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return e?(e.pluginHookBucket||(e.pluginHookBucket=new jm),e.pluginHookBucket):this.globalBucket}add(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return Array.isArray(t)?jd(t,(t=>this.add(e,t,o))):(lm.has(e)&&nm(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return`${e}`.replace(/(?:\\)?\[([^[\]]+)]/g,((e,o)=>"\\"===e.charAt(0)?e.substr(1,e.length-1):void 0===t[o]?"":t[o]))}(Um,{hookName:e,removedInVersion:lm.get(e)})),am.has(e)&&nm(am.get(e)),this.getBucket(o).add(e,t,{orderIndex:r,runOnce:!1})),this}once(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return Array.isArray(t)?jd(t,(t=>this.once(e,t,o))):this.getBucket(o).add(e,t,{orderIndex:r,runOnce:!0}),this}addAsFixed(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return Array.isArray(t)?jd(t,(t=>this.addAsFixed(e,t,o))):this.getBucket(o).add(e,t,{initialHook:!0}),this}remove(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return this.getBucket(o).remove(e,t)}has(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return this.getBucket(t).has(e)}run(e,t,o,r,i,n,s,l){{const a=this.getBucket().getHooks(t),h=a?a.length:0;let c=0;if(h)for(;c<h;){if(!a[c]||a[c].skip){c+=1;continue}const h=ef(a[c].callback,e,o,r,i,n,s,l);void 0!==h&&(o=h),a[c]&&a[c].runOnce&&this.remove(t,a[c].callback),c+=1}}{const a=this.getBucket(e).getHooks(t),h=a?a.length:0;let c=0;if(h)for(;c<h;){if(!a[c]||a[c].skip){c+=1;continue}const h=ef(a[c].callback,e,o,r,i,n,s,l);void 0!==h&&(o=h),a[c]&&a[c].runOnce&&this.remove(t,a[c].callback,e),c+=1}}return o}destroy(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.getBucket(e).destroy()}register(e){this.isRegistered(e)||sm.push(e)}deregister(e){this.isRegistered(e)&&sm.splice(sm.indexOf(e),1)}isDeprecated(e){return am.has(e)||lm.has(e)}isRegistered(e){return sm.indexOf(e)>=0}getRegistered(){return sm}}const Gm=new Xm,Ym=new Map;function Km(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common";Ym.has(e)||Ym.set(e,new Map);const t=Ym.get(e);return{register:function(e,o){t.set(e,o)},getItem:function(e){return t.get(e)},hasItem:function(e){return t.has(e)},getNames:function(){return[...t.keys()]},getValues:function(){return[...t.values()]}}}const qm=new WeakMap,{register:Qm,getItem:Jm,hasItem:Zm,getNames:ep,getValues:tp}=Km("editors");function op(e){const t={},o=e;this.getConstructor=function(){return e},this.getInstance=function(e){return e.guid in t||(t[e.guid]=new o(e)),t[e.guid]},Xm.getSingleton().add("afterDestroy",(function(){t[this.guid]=null}))}function rp(e){if("function"==typeof e)return e;if(!Zm(e))throw Error(`No registered editor found under "${e}" name`);return Jm(e).getConstructor()}function ip(e,t){e&&"string"!=typeof e&&(e=(t=e).EDITOR_TYPE);const o=new op(t);"string"==typeof e&&Qm(e,o),qm.set(t,o)}function np(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const sp=class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;np(this,"context",void 0),this.context=e||this,this.context.eventListeners||(this.context.eventListeners=[])}addEventListener(e,t,o){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];function i(e){o.call(this,function(e){const t=e.stopImmediatePropagation;return e.stopImmediatePropagation=function(){t.apply(this),tm(this)},e}(e))}return this.context.eventListeners.push({element:e,event:t,callback:o,callbackProxy:i,options:r,eventManager:this}),e.addEventListener(t,i,r),()=>{this.removeEventListener(e,t,o)}}removeEventListener(e,t,o){let r,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=this.context.eventListeners.length;for(;n;)if(n-=1,r=this.context.eventListeners[n],r.event===t&&r.element===e){if(o&&o!==r.callback)continue;if(i&&r.eventManager!==this)continue;this.context.eventListeners.splice(n,1),r.element.removeEventListener(r.event,r.callbackProxy,r.options)}}clearEvents(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.context)return;let t=this.context.eventListeners.length;for(;t;){t-=1;const o=this.context.eventListeners[t];e&&o.eventManager!==this||(this.context.eventListeners.splice(t,1),o.element.removeEventListener(o.event,o.callbackProxy,o.options))}}clear(){this.clearEvents()}destroy(){this.clearEvents(),this.context=null}destroyWithOwnEventsOnly(){this.clearEvents(!0),this.context=null}fireEvent(e,t){let o=e.document,r=e;o||(o=e.ownerDocument?e.ownerDocument:e,r=o.defaultView);const i={bubbles:!0,cancelable:"mousemove"!==t,view:r,detail:0,screenX:0,screenY:0,clientX:1,clientY:1,ctrlKey:!1,altKey:!1,shiftKey:!1,metaKey:!1,button:0,relatedTarget:void 0};let n;o.createEvent?(n=o.createEvent("MouseEvents"),n.initMouseEvent(t,i.bubbles,i.cancelable,i.view,i.detail,i.screenX,i.screenY,i.clientX,i.clientY,i.ctrlKey,i.altKey,i.shiftKey,i.metaKey,i.button,o.body.parentNode)):n=o.createEventObject(),e.dispatchEvent?e.dispatchEvent(n):e.fireEvent(`on${t}`,n)}};function lp(e,t){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.add(e)}function ap(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function hp(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var cp=new WeakSet;class up{constructor(e,t,o){lp(this,cp),ap(this,"hot",void 0),ap(this,"tableMeta",void 0),ap(this,"selection",void 0),ap(this,"eventManager",void 0),ap(this,"destroyed",!1),ap(this,"activeEditor",void 0),ap(this,"cellProperties",void 0),this.hot=e,this.tableMeta=t,this.selection=o,this.eventManager=new sp(e),this.hot.addHook("afterDocumentKeyDown",(e=>hp(cp,this,dp).call(this,e))),this.hot.addHook("beforeCompositionStart",(e=>hp(cp,this,dp).call(this,e))),this.hot.view._wt.update("onCellDblClick",((e,t,o)=>hp(cp,this,gp).call(this,e,t,o)))}getActiveEditor(){return this.activeEditor}prepareEditor(){var e;if(this.activeEditor&&this.activeEditor.isWaiting())return void this.closeEditor(!1,!1,(e=>{e&&this.prepareEditor()}));const t=null===(e=this.hot.getSelectedRangeLast())||void 0===e?void 0:e.highlight;if(!t||t.isHeader())return;const{row:o,col:r}=t,i=this.hot.runHooks("modifyGetCellCoords",o,r,!1,"meta");let n=o,s=r;if(Array.isArray(i)&&([n,s]=i),this.cellProperties=this.hot.getCellMeta(n,s),!this.isCellEditable())return void this.clearActiveEditor();const l=this.hot.getCell(o,r,!0);if(l){const e=this.hot.getCellEditor(this.cellProperties),t=this.hot.colToProp(s),i=this.hot.getSourceDataAtCell(this.hot.toPhysicalRow(n),s);this.activeEditor=function(e,t){let o;if("function"==typeof e)qm.get(e)||ip(null,e),o=qm.get(e);else{if("string"!=typeof e)throw Error('Only strings and functions can be passed as "editor" parameter');o=Jm(e)}if(!o)throw Error(`No editor registered under name "${e}"`);return o.getInstance(t)}(e,this.hot),this.activeEditor.prepare(o,r,t,l,i,this.cellProperties)}}isEditorOpened(){return this.activeEditor&&this.activeEditor.isOpened()}openEditor(e,t){let o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!this.isCellEditable())return void this.clearActiveEditor();const r=this.hot.getSelectedRangeLast();let i=this.hot.runHooks("beforeBeginEditing",r.highlight.row,r.highlight.col,e,t,o);t instanceof MouseEvent&&"boolean"!=typeof i&&(i=0===this.hot.selection.getLayerLevel()&&r.isSingle()),!1!==i?(this.activeEditor||(this.hot.scrollToFocusedCell(),this.prepareEditor()),this.activeEditor&&(o&&this.activeEditor.enableFullEditMode(),this.activeEditor.beginEditing(e,t))):this.clearActiveEditor()}closeEditor(e,t,o){this.activeEditor?this.activeEditor.finishEditing(e,t,o):o&&o(!1)}closeEditorAndSaveChanges(e){this.closeEditor(!1,e)}closeEditorAndRestoreOriginalValue(e){this.closeEditor(!0,e)}clearActiveEditor(){this.activeEditor=void 0}isCellEditable(){const e=this.hot.getSelectedRangeLast();if(!e)return!1;const t=this.hot.getCellEditor(this.cellProperties),{row:o,col:r}=e.highlight,{rowIndexMapper:i,columnIndexMapper:n}=this.hot,s=i.isHidden(this.hot.toPhysicalRow(o))||n.isHidden(this.hot.toPhysicalColumn(r));return!(this.cellProperties.readOnly||!t||s)}moveSelectionAfterEnter(e){const t={..."function"==typeof this.tableMeta.enterMoves?this.tableMeta.enterMoves(e):this.tableMeta.enterMoves};e.shiftKey&&(t.row=-t.row,t.col=-t.col),this.hot.selection.isMultiple()?this.selection.transformFocus(t.row,t.col):this.selection.transformStart(t.row,t.col,!0)}destroy(){this.destroyed=!0,this.eventManager.destroy()}}function dp(e){const t=this.hot.getSelectedRangeLast();if(!this.hot.isListening()||!t||t.highlight.isHeader()||om(e))return;const{keyCode:o}=e,r=(e.ctrlKey||e.metaKey)&&!e.altKey;(!this.activeEditor||this.activeEditor&&!this.activeEditor.isWaiting())&&(function(e){return em.includes(e)}(o)||function(e){return[Zf.CONTROL,Zf.COMMAND_LEFT,Zf.COMMAND_RIGHT,Zf.COMMAND_FIREFOX].includes(e)}(o)||r||this.isEditorOpened()||this.openEditor("",e))}function gp(e,t){t.isCell()&&this.openEditor(null,e,!0)}const fp=new WeakMap;up.getInstance=function(e,t,o){let r=fp.get(e);return r||(r=new up(e,t,o),fp.set(e,r)),r};const mp=up;function pp(e,t,o){wp(e,t),t.set(e,o)}function wp(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function yp(e,t){return e.get(bp(e,t))}function vp(e,t,o){return e.set(bp(e,t),o),o}function bp(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}const Cp=Object.freeze({CELL:"cell",MIXED:"mixed"});var Sp=new WeakMap,Rp=new WeakMap,Tp=new WeakMap,Ep=new WeakMap,Hp=new WeakMap,Op=new WeakSet;class Mp{constructor(e){var t,o,r=this;wp(t=this,o=Op),o.add(t),pp(this,Sp,void 0),pp(this,Rp,void 0),pp(this,Tp,1),pp(this,Ep,null),pp(this,Hp,new Map);const i=e.getSettings();vp(Sp,this,e),vp(Rp,this,i.imeFastEdit?Cp.MIXED:Cp.CELL),yp(Sp,this).addHook("afterUpdateSettings",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return bp(Op,r,Ap).call(r,...t)})),yp(Sp,this).addHook("afterSelection",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return bp(Op,r,Ip).call(r,...t)})),yp(Sp,this).addHook("afterSelectionFocusSet",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return bp(Op,r,Ip).call(r,...t)})),yp(Sp,this).addHook("afterSelectionEnd",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return bp(Op,r,Np).call(r,...t)}))}getFocusMode(){return yp(Rp,this)}setFocusMode(e){Object.values(Cp).includes(e)?vp(Rp,this,e):nm(`"${e}" is not a valid focus mode.`)}getRefocusDelay(){return yp(Tp,this)}setRefocusDelay(e){vp(Tp,this,e)}setRefocusElementGetter(e){vp(Ep,this,e)}getRefocusElement(){var e;return"function"==typeof yp(Ep,this)?yp(Ep,this).call(this):null===(e=yp(Sp,this).getActiveEditor())||void 0===e?void 0:e.TEXTAREA}focusOnHighlightedCell(e){const t=e=>{var t,o;const r=null===(t=yp(Sp,this).getSelectedRangeLast())||void 0===t?void 0:t.highlight;if(!r)return;let i=yp(Sp,this).runHooks("modifyFocusedElement",r.row,r.col,e);Jg(i)||(i=e),!i||null!==(o=yp(Sp,this).getActiveEditor())&&void 0!==o&&o.isOpened()||i.focus({preventScroll:!0})};e?t(e):bp(Op,this,xp).call(this,(e=>t(e)))}refocusToEditorTextarea(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:yp(Tp,this);var o,r;!yp(Sp,this).getSettings().imeFastEdit||null!==(e=yp(Sp,this).getActiveEditor())&&void 0!==e&&e.isOpened()||(null===(o=yp(Sp,this).getActiveEditor())||void 0===o||null===(r=o.refreshValue)||void 0===r||r.call(o),yp(Hp,this).has(t)||yp(Hp,this).set(t,function(e){let t,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200,r=null;return function(){for(var i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];return r&&clearTimeout(r),r=setTimeout((()=>{t=e.apply(this,n)}),o),t}}((()=>{var e;yp(Sp,this).isDestroyed||null===(e=this.getRefocusElement())||void 0===e||e.select()}),t)),yp(Hp,this).get(t)())}}function xp(e){var t;const o=null===(t=yp(Sp,this).getSelectedRangeLast())||void 0===t?void 0:t.highlight;if(!o||!yp(Sp,this).selection.isCellVisible(o))return void e(null);const r=yp(Sp,this).getCell(o.row,o.col,!0);null===r?yp(Sp,this).addHookOnce("afterScroll",(()=>{e(yp(Sp,this).getCell(o.row,o.col,!0))})):e(r)}function Ip(){bp(Op,this,xp).call(this,(e=>{const{activeElement:t}=yp(Sp,this).rootDocument;t&&Qg(t)&&t.blur(),this.focusOnHighlightedCell(e)}))}function Np(){bp(Op,this,xp).call(this,(e=>{this.getFocusMode()===Cp.MIXED&&"TD"===(null==e?void 0:e.nodeName)&&this.refocusToEditorTextarea()}))}function Ap(e){"boolean"==typeof e.imeFastEdit&&this.setFocusMode(e.imeFastEdit?Cp.MIXED:Cp.CELL)}var kp=re,_p=W,Pp=pt("match"),Vp=Gt,Lp=E,Dp=ot,Fp=le,Bp=function(){var e=Vp(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t},Wp=RegExp.prototype,jp=L,zp=Ze,$p=Math.floor,Up=jp("".charAt),Xp=jp("".replace),Gp=jp("".slice),Yp=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Kp=/\$([$&'`]|\d{1,2})/g,qp=gi,Qp=E,Jp=L,Zp=q,ew=te,tw=re,ow=function(e){var t;return kp(e)&&(void 0!==(t=e[Pp])?!!t:"RegExp"===_p(e))},rw=Ui,iw=Pe,nw=function(e){var t=e.flags;return void 0!==t||"flags"in Wp||Dp(e,"flags")||!Fp(Wp,e)?t:Lp(Bp,e)},sw=function(e,t,o,r,i,n){var s=o+e.length,l=r.length,a=Kp;return void 0!==i&&(i=zp(i),a=Yp),Xp(n,a,(function(n,a){var h;switch(Up(a,0)){case"$":return"$";case"&":return e;case"`":return Gp(t,0,o);case"'":return Gp(t,s);case"<":h=i[Gp(a,1,-1)];break;default:var c=+a;if(0===c)return n;if(c>l){var u=$p(c/10);return 0===u?n:u<=l?void 0===r[u-1]?Up(a,1):r[u-1]+Up(a,1):n}h=r[c-1]}return void 0===h?"":h}))},lw=pt("replace"),aw=TypeError,hw=Jp("".indexOf);Jp("".replace);var cw=Jp("".slice),uw=Math.max;qp({target:"String",proto:!0},{replaceAll:function(e,t){var o,r,i,n,s,l,a,h,c,u=Zp(this),d=0,g="";if(tw(e)){if(ow(e)&&(o=rw(Zp(nw(e))),!~hw(o,"g")))throw new aw("`.replaceAll` does not allow non-global regexes");if(r=iw(e,lw))return Qp(r,e,u,t)}for(i=rw(u),n=rw(e),(s=ew(t))||(t=rw(t)),l=n.length,a=uw(1,l),h=hw(i,n);-1!==h;)c=s?rw(t(n,h,i)):sw(n,i,h,[],void 0,t),g+=cw(i,d,h)+c,d=h+l,h=h+a>i.length?-1:hw(i,n,h+a);return d<i.length&&(g+=cw(i,d)),g}});var dw=gi,gw=xs,fw=Ae,mw=Gt,pw=ol,ww=ms,yw=oc,vw=yi,bw=TypeError,Cw=b((function(){[].keys().reduce((function(){}),void 0)})),Sw=!Cw&&yw("reduce",bw);function Rw(e){const t=e.hasColHeaders(),o=e.hasRowHeaders(),r=[t?-1:0,o?-1:0,e.countRows()-1,e.countCols()-1],i=e.getData(...r),n=i.length,s=n>0?i[0].length:0,l=["<table>","</table>"],a=t?["<thead>","</thead>"]:[],h=["<tbody>","</tbody>"],c=o?1:0,u=t?1:0;for(let d=0;d<n;d+=1){const r=t&&0===d,n=[];for(let t=0;t<s;t+=1){const s=!r&&o&&0===t;let l="";if(r)l=`<th>${e.getColHeader(t-c)}</th>`;else if(s)l=`<th>${e.getRowHeader(d-u)}</th>`;else{const o=i[d][t],{hidden:r,rowspan:n,colspan:s}=e.getCellMeta(d-u,t-c);if(!r){const e=[];if(n&&e.push(`rowspan="${n}"`),s&&e.push(`colspan="${s}"`),Kd(o))l=`<td ${e.join(" ")}></td>`;else{const t=o.toString().replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/(<br(\s*|\/)>(\r\n|\n)?|\r\n|\n)/g,"<br>\r\n").replace(/\x20/gi,"&nbsp;").replace(/\t/gi,"&#9;");l=`<td ${e.join(" ")}>${t}</td>`}}}n.push(l)}const l=["<tr>",...n,"</tr>"].join("");r?a.splice(1,0,l):h.splice(-1,0,l)}return l.splice(1,0,a.join(""),h.join("")),l.join("")}function Tw(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];const o=typeof e;if("number"===o)return!isNaN(e)&&isFinite(e);if("string"===o){if(0===e.length)return!1;if(1===e.length)return/\d/.test(e);const o=Array.from(new Set([".",...t])).map((e=>`\\${e}`)).join("|");return new RegExp(`^[+-]?(((${o})?\\d+((${o})\\d+)?(e[+-]?\\d+)?)|(0x[a-f\\d]+))$`,"i").test(e.trim())}return"object"===o&&!(!e||"number"!=typeof e.valueOf()||e instanceof Date)}function Ew(e,t,o){let r=-1;for("function"==typeof t?(o=t,t=e):r=e-1;++r<=t&&!1!==o(r););}function Hw(e,t,o){let r=e+1;for("function"==typeof t&&(o=t,t=0);--r>=t&&!1!==o(r););}function Ow(e,t,o){return Math.min(e,t)===e?t:Math.max(e,o)===e?o:e}dw({target:"Iterator",proto:!0,real:!0,forced:Cw||Sw},{reduce:function(e){mw(this);try{fw(e)}catch(n){ww(this,"throw",n)}var t=arguments.length<2,o=t?void 0:arguments[1];if(Sw)return vw(Sw,this,t?[e]:[e,o]);var r=pw(this),i=0;if(gw(r,(function(r){t?(t=!1,o=r):o=e(o,r,i),i++}),{IS_RECORD:!0}),t)throw new bw("Reduce of empty iterator with no initial value");return o}}),new RegExp(Object.keys({"&nbsp;":" ","&amp;":"&","&lt;":"<","&gt;":">"}).map((e=>`(${e})`)).join("|"),"gi");const Mw="asc",xw=new Map([[Mw,[-1,1]],["desc",[1,-1]]]),Iw=e=>`The priority '${e}' is already declared in a map.`,Nw=e=>`The priority '${e}' is not a number.`,Aw=e=>`The id '${e}' is already declared in a map.`;function kw(){let{errorIdExists:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=new Map;function o(){return[...t]}function r(e){return t.has(e)}return e=Zg(e)?e:Aw,{addItem:function(o,i){if(r(o))throw new Error(e(o));t.set(o,i)},clear:function(){t.clear()},getId:function(e){const[t]=o().find((t=>{let[o,r]=t;return e===r&&o}))||[null];return t},getItem:function(e){return t.get(e)},getItems:o,hasItem:r,removeItem:function(e){return t.delete(e)}}}const _w=e=>`'${e}' value is already declared in a unique set.`,Pw=e=>`There is already registered "${e}" plugin.`,Vw=function(){let{errorPriorityExists:e,errorPriorityNaN:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const o=new Map;return e=Zg(e)?e:Iw,t=Zg(t)?t:Nw,{addItem:function(r,i){if(!Tw(r))throw new Error(t(r));if(o.has(r))throw new Error(e(r));o.set(r,i)},getItems:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Mw;const[t,r]=xw.get(e)||xw.get(Mw);return[...o].sort(((e,o)=>e[0]<o[0]?t:r)).map((e=>e[1]))}}}({errorPriorityExists:e=>`There is already registered plugin on priority "${e}".`,errorPriorityNaN:e=>`The priority "${e}" is not a number.`}),Lw=function(){let{errorItemExists:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=new Set;return e=Zg(e)?e:_w,{addItem:function(o){if(t.has(o))throw new Error(e(o));t.add(o)},clear:function(){t.clear()},getItems:function(){return[...t]}}}({errorItemExists:Pw}),Dw=kw({errorIdExists:Pw}),{register:Fw,getItem:Bw,hasItem:Ww,getNames:jw,getValues:zw}=Km("renderers");function $w(e){if("function"==typeof e)return e;if(!Ww(e))throw Error(`No registered renderer found under "${e}" name`);return Bw(e)}const{register:Uw,getItem:Xw,hasItem:Gw,getNames:Yw,getValues:Kw}=Km("validators");var qw=Ze,Qw=Tr,Jw=wr,Zw=pm;gi({target:"Array",proto:!0},{at:function(e){var t=qw(this),o=Qw(t),r=Jw(e),i=r>=0?r:o+r;return i<0||i>=o?void 0:t[i]}}),Zw("at");var ey=gi,ty=q,oy=wr,ry=Ui,iy=b,ny=L("".charAt);function sy(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}ey({target:"String",proto:!0,forced:iy((function(){return"\ud842"!=="𠮷".at(-2)}))},{at:function(e){var t=ry(ty(this)),o=t.length,r=oy(e),i=r>=0?r:o+r;return i<0||i>=o?void 0:ny(t,i)}});class ly{constructor(){sy(this,"count",0),sy(this,"startColumn",null),sy(this,"endColumn",null),sy(this,"startPosition",null),sy(this,"isVisibleInTrimmingContainer",!1)}initialize(){}process(e,t){const{totalCalculatedWidth:o,zeroBasedScrollOffset:r,viewportWidth:i,columnWidth:n}=t;o>=r&&o+n<=r+(r>0?i+1:i)&&(null!==this.startColumn&&void 0!==this.startColumn||(this.startColumn=e),this.endColumn=e)}finalize(e){var t;const{scrollOffset:o,viewportWidth:r,inlineStartOffset:i,zeroBasedScrollOffset:n,totalColumns:s,needReverse:l,startPositions:a,columnWidth:h}=e;if(this.endColumn===s-1&&l)for(this.startColumn=this.endColumn;this.startColumn>0;){const e=a[this.endColumn]+h-a[this.startColumn-1];if(e<=r&&(this.startColumn-=1),e>=r)break}this.startPosition=null!==(t=a[this.startColumn])&&void 0!==t?t:null;const c=o+r-(n>0?r+1:r),u=null===this.startColumn?0:e.getColumnWidth(this.startColumn);c<-1*i||o>a.at(-1)||-1*o-r>-1*u?this.isVisibleInTrimmingContainer=!1:this.isVisibleInTrimmingContainer=!0,s<this.endColumn&&(this.endColumn=s-1),null!==this.startColumn&&(this.count=this.endColumn-this.startColumn+1)}}function ay(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class hy{constructor(){ay(this,"count",0),ay(this,"startRow",null),ay(this,"endRow",null),ay(this,"startPosition",null),ay(this,"isVisibleInTrimmingContainer",!1)}initialize(){}process(e,t){const{totalCalculatedHeight:o,zeroBasedScrollOffset:r,innerViewportHeight:i,rowHeight:n}=t;o>=r&&o+n<=i&&(null===this.startRow&&(this.startRow=e),this.endRow=e)}finalize(e){var t;const{scrollOffset:o,viewportHeight:r,horizontalScrollbarHeight:i,totalRows:n,needReverse:s,startPositions:l,rowHeight:a}=e;if(this.endRow===n-1&&s)for(this.startRow=this.endRow;this.startRow>0;){const e=l[this.endRow]+a-l[this.startRow-1];if(e<=r-i&&(this.startRow-=1),e>=r-i)break}this.startPosition=null!==(t=l[this.startRow])&&void 0!==t?t:null,o+r-i<(null===this.startRow?0:e.getRowHeight(this.startRow))||o>l.at(-1)?this.isVisibleInTrimmingContainer=!1:this.isVisibleInTrimmingContainer=!0,n<this.endRow&&(this.endRow=n-1),null!==this.startRow&&(this.count=this.endRow-this.startRow+1)}}function cy(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class uy{constructor(){cy(this,"count",0),cy(this,"startColumn",null),cy(this,"endColumn",null),cy(this,"startPosition",null),cy(this,"isVisibleInTrimmingContainer",!1)}initialize(){}process(e,t){const{totalCalculatedWidth:o,zeroBasedScrollOffset:r,viewportWidth:i}=t;o<=r&&(this.startColumn=e),o>=r&&o<=r+(r>0?i+1:i)&&(null!==this.startColumn&&void 0!==this.startColumn||(this.startColumn=e)),this.endColumn=e}finalize(e){var t;const{scrollOffset:o,viewportWidth:r,inlineStartOffset:i,zeroBasedScrollOffset:n,totalColumns:s,needReverse:l,startPositions:a,columnWidth:h}=e;if(this.endColumn===s-1&&l)for(this.startColumn=this.endColumn;this.startColumn>0;){const e=a[this.endColumn]+h-a[this.startColumn-1];if(this.startColumn-=1,e>r)break}this.startPosition=null!==(t=a[this.startColumn])&&void 0!==t?t:null,o+r-(n>0?r+1:r)<-1*i||o>a.at(-1)+h||-1*o-r>0?this.isVisibleInTrimmingContainer=!1:this.isVisibleInTrimmingContainer=!0,s<this.endColumn&&(this.endColumn=s-1),null!==this.startColumn&&(this.count=this.endColumn-this.startColumn+1)}}function dy(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class gy{constructor(){dy(this,"count",0),dy(this,"startRow",null),dy(this,"endRow",null),dy(this,"startPosition",null),dy(this,"isVisibleInTrimmingContainer",!1)}initialize(){}process(e,t){const{totalCalculatedHeight:o,zeroBasedScrollOffset:r,innerViewportHeight:i}=t;o<=r&&(this.startRow=e),o>=r&&o<=i&&null===this.startRow&&(this.startRow=e),this.endRow=e}finalize(e){var t;const{scrollOffset:o,viewportHeight:r,horizontalScrollbarHeight:i,totalRows:n,needReverse:s,startPositions:l,rowHeight:a}=e;if(this.endRow===n-1&&s)for(this.startRow=this.endRow;this.startRow>0;){const e=l[this.endRow]+a-l[this.startRow-1];if(this.startRow-=1,e>=r-i)break}this.startPosition=null!==(t=l[this.startRow])&&void 0!==t?t:null,o+r-i<0||o>l.at(-1)+a?this.isVisibleInTrimmingContainer=!1:this.isVisibleInTrimmingContainer=!0,n<this.endRow&&(this.endRow=n-1),null!==this.startRow&&(this.count=this.endRow-this.startRow+1)}}function fy(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class my{constructor(){fy(this,"count",0),fy(this,"startColumn",0),fy(this,"endColumn",0),fy(this,"startPosition",0),fy(this,"isVisibleInTrimmingContainer",!0)}initialize(e){let{totalColumns:t}=e;this.count=t,this.endColumn=this.count-1}process(){}finalize(){}}function py(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class wy{constructor(){py(this,"count",0),py(this,"startRow",0),py(this,"endRow",0),py(this,"startPosition",0),py(this,"isVisibleInTrimmingContainer",!0)}initialize(e){let{totalRows:t}=e;this.count=t,this.endRow=this.count-1}process(){}finalize(){}}function yy(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class vy extends uy{constructor(){super(...arguments),yy(this,"columnStartOffset",0),yy(this,"columnEndOffset",0)}finalize(e){var t;super.finalize(e);const{overrideFn:o,totalColumns:r,startPositions:i}=e;if(null!==this.startColumn&&"function"==typeof o){const e=this.startColumn,t=this.endColumn;o(this),this.columnStartOffset=e-this.startColumn,this.columnEndOffset=this.endColumn-t}this.startColumn<0&&(this.startColumn=0),this.startPosition=null!==(t=i[this.startColumn])&&void 0!==t?t:null,r<this.endColumn&&(this.endColumn=r-1),null!==this.startColumn&&(this.count=this.endColumn-this.startColumn+1)}}function by(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Cy extends gy{constructor(){super(...arguments),by(this,"rowStartOffset",0),by(this,"rowEndOffset",0)}finalize(e){var t;super.finalize(e);const{overrideFn:o,totalRows:r,startPositions:i}=e;if(null!==this.startRow&&"function"==typeof o){const e=this.startRow,t=this.endRow;o(this),this.rowStartOffset=e-this.startRow,this.rowEndOffset=this.endRow-t}this.startRow<0&&(this.startRow=0),this.startPosition=null!==(t=i[this.startRow])&&void 0!==t?t:null,r<this.endRow&&(this.endRow=r-1),null!==this.startRow&&(this.count=this.endRow-this.startRow+1)}}function Sy(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Ry{constructor(e){Sy(this,"calculationTypes",[]),Sy(this,"calculationResults",new Map),this.calculationTypes=e}_initialize(e){this.calculationTypes.forEach((t=>{let[o,r]=t;this.calculationResults.set(o,r),r.initialize(e)}))}_process(e,t){this.calculationTypes.forEach((o=>{let[,r]=o;return r.process(e,t)}))}_finalize(e){this.calculationTypes.forEach((t=>{let[,o]=t;return o.finalize(e)}))}getResultsFor(e){return this.calculationResults.get(e)}}function Ty(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Ey extends Ry{constructor(e){let{calculationTypes:t,viewportWidth:o,scrollOffset:r,totalColumns:i,columnWidthFn:n,overrideFn:s,inlineStartOffset:l}=e;super(t),Ty(this,"viewportWidth",0),Ty(this,"scrollOffset",0),Ty(this,"zeroBasedScrollOffset",0),Ty(this,"totalColumns",0),Ty(this,"columnWidthFn",null),Ty(this,"columnWidth",0),Ty(this,"overrideFn",null),Ty(this,"inlineStartOffset",0),Ty(this,"totalCalculatedWidth",0),Ty(this,"startPositions",[]),Ty(this,"needReverse",!0),this.viewportWidth=o,this.scrollOffset=r,this.zeroBasedScrollOffset=Math.max(r,0),this.totalColumns=i,this.columnWidthFn=n,this.overrideFn=s,this.inlineStartOffset=l,this.calculate()}calculate(){this._initialize(this);for(let e=0;e<this.totalColumns;e++)if(this.columnWidth=this.getColumnWidth(e),this._process(e,this),this.startPositions.push(this.totalCalculatedWidth),this.totalCalculatedWidth+=this.columnWidth,this.totalCalculatedWidth>=this.zeroBasedScrollOffset+this.viewportWidth){this.needReverse=!1;break}this._finalize(this)}getColumnWidth(e){const t=this.columnWidthFn(e);return isNaN(t)?50:t}}function Hy(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Oy extends Ry{constructor(e){let{calculationTypes:t,viewportHeight:o,scrollOffset:r,totalRows:i,defaultRowHeight:n,rowHeightFn:s,overrideFn:l,horizontalScrollbarHeight:a}=e;super(t),Hy(this,"viewportHeight",0),Hy(this,"scrollOffset",0),Hy(this,"zeroBasedScrollOffset",0),Hy(this,"totalRows",0),Hy(this,"rowHeightFn",null),Hy(this,"rowHeight",0),Hy(this,"overrideFn",null),Hy(this,"horizontalScrollbarHeight",0),Hy(this,"innerViewportHeight",0),Hy(this,"totalCalculatedHeight",0),Hy(this,"startPositions",[]),Hy(this,"needReverse",!0),this.defaultHeight=n,this.viewportHeight=o,this.scrollOffset=r,this.zeroBasedScrollOffset=Math.max(r,0),this.totalRows=i,this.rowHeightFn=s,this.overrideFn=l,this.horizontalScrollbarHeight=null!=a?a:0,this.innerViewportHeight=this.zeroBasedScrollOffset+this.viewportHeight-this.horizontalScrollbarHeight,this.calculate()}calculate(){this._initialize(this);for(let e=0;e<this.totalRows;e++)if(this.rowHeight=this.getRowHeight(e),this._process(e,this),this.startPositions.push(this.totalCalculatedHeight),this.totalCalculatedHeight+=this.rowHeight,this.totalCalculatedHeight>=this.innerViewportHeight){this.needReverse=!1;break}this._finalize(this)}getRowHeight(e){const t=this.rowHeightFn(e);return isNaN(t)?this.defaultHeight:t}}function My(e,t,o){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,o)}function xy(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function Iy(e,t){return e.get(Ay(e,t))}function Ny(e,t,o){return e.set(Ay(e,t),o),o}function Ay(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var ky=new WeakMap;class _y{constructor(e,t){let o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];xy(this,"row",null),xy(this,"col",null),My(this,ky,!1),Ny(ky,this,o),void 0!==e&&void 0!==t&&(this.row=e,this.col=t)}isValid(e){const{countRows:t,countCols:o,countRowHeaders:r,countColHeaders:i}={countRows:0,countCols:0,countRowHeaders:0,countColHeaders:0,...e};return!(!Number.isInteger(this.row)||!Number.isInteger(this.col)||this.row<-i||this.col<-r||this.row>=t||this.col>=o)}isEqual(e){return e===this||this.row===e.row&&this.col===e.col}isHeader(){return!this.isCell()}isCell(){return this.row>=0&&this.col>=0}isRtl(){return Iy(ky,this)}isSouthEastOf(e){return this.row>=e.row&&(Iy(ky,this)?this.col<=e.col:this.col>=e.col)}isNorthWestOf(e){return this.row<=e.row&&(Iy(ky,this)?this.col>=e.col:this.col<=e.col)}isSouthWestOf(e){return this.row>=e.row&&(Iy(ky,this)?this.col>=e.col:this.col<=e.col)}isNorthEastOf(e){return this.row<=e.row&&(Iy(ky,this)?this.col<=e.col:this.col>=e.col)}normalize(){return this.row=null===this.row?this.row:Math.max(this.row,0),this.col=null===this.col?this.col:Math.max(this.col,0),this}assign(e){return Number.isInteger(null==e?void 0:e.row)&&(this.row=e.row),Number.isInteger(null==e?void 0:e.col)&&(this.col=e.col),e instanceof _y&&Ny(ky,this,e.isRtl()),this}clone(){return new _y(this.row,this.col,Iy(ky,this))}toObject(){return{row:this.row,col:this.col}}}const Py=_y;function Vy(e,t,o){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,o)}function Ly(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function Dy(e,t){return e.get(Fy(e,t))}function Fy(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var By=new WeakMap;class Wy{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];var i,n,s;Ly(this,"highlight",null),Ly(this,"from",null),Ly(this,"to",null),Vy(this,By,!1),this.highlight=e.clone(),this.from=t.clone(),this.to=o.clone(),n=this,s=r,(i=By).set(Fy(i,n),s)}setHighlight(e){return this.highlight=e.clone(),this}setFrom(e){return this.from=e.clone(),this}setTo(e){return this.to=e.clone(),this}normalize(){return this.highlight.normalize(),this.from.normalize(),this.to.normalize(),this}isValid(e){return this.from.isValid(e)&&this.to.isValid(e)}isSingle(){return this.isSingleCell()||this.isSingleHeader()}isSingleCell(){return this.from.row>=0&&this.from.row===this.to.row&&this.from.col>=0&&this.from.col===this.to.col}isSingleHeader(){return(this.from.row<0||this.from.col<0)&&this.from.row===this.to.row&&this.from.col===this.to.col}isHeader(){return!(!this.from.isHeader()||!this.to.isHeader())||this.from.col<0&&this.to.col<0||this.from.row<0&&this.to.row<0}containsHeaders(){return this.from.isHeader()||this.to.isHeader()}getOuterHeight(){return Math.max(this.from.row,this.to.row)-Math.min(this.from.row,this.to.row)+1}getOuterWidth(){return Math.max(this.from.col,this.to.col)-Math.min(this.from.col,this.to.col)+1}getHeight(){if(this.from.row<0&&this.to.row<0)return 0;const e=Math.max(this.from.row,0),t=Math.max(this.to.row,0);return Math.max(e,t)-Math.min(e,t)+1}getWidth(){if(this.from.col<0&&this.to.col<0)return 0;const e=Math.max(this.from.col,0),t=Math.max(this.to.col,0);return Math.max(e,t)-Math.min(e,t)+1}getCellsCount(){return this.getWidth()*this.getHeight()}includes(e){const{row:t,col:o}=e,r=this.getOuterTopStartCorner(),i=this.getOuterBottomEndCorner();return r.row<=t&&i.row>=t&&r.col<=o&&i.col>=o}includesRange(e){return this.includes(e.getOuterTopStartCorner())&&this.includes(e.getOuterBottomEndCorner())}isEqual(e){return Math.min(this.from.row,this.to.row)===Math.min(e.from.row,e.to.row)&&Math.max(this.from.row,this.to.row)===Math.max(e.from.row,e.to.row)&&Math.min(this.from.col,this.to.col)===Math.min(e.from.col,e.to.col)&&Math.max(this.from.col,this.to.col)===Math.max(e.from.col,e.to.col)}overlaps(e){return e.isSouthEastOf(this.getOuterTopLeftCorner())&&e.isNorthWestOf(this.getOuterBottomRightCorner())}isSouthEastOf(e){return this.getOuterTopLeftCorner().isSouthEastOf(e)||this.getOuterBottomRightCorner().isSouthEastOf(e)}isNorthWestOf(e){return this.getOuterTopLeftCorner().isNorthWestOf(e)||this.getOuterBottomRightCorner().isNorthWestOf(e)}isOverlappingHorizontally(e){return this.getOuterTopEndCorner().col>=e.getOuterTopStartCorner().col&&this.getOuterTopEndCorner().col<=e.getOuterTopEndCorner().col||this.getOuterTopStartCorner().col<=e.getOuterTopEndCorner().col&&this.getOuterTopStartCorner().col>=e.getOuterTopStartCorner().col}isOverlappingVertically(e){return this.getOuterBottomStartCorner().row>=e.getOuterTopRightCorner().row&&this.getOuterBottomStartCorner().row<=e.getOuterBottomStartCorner().row||this.getOuterTopEndCorner().row<=e.getOuterBottomStartCorner().row&&this.getOuterTopEndCorner().row>=e.getOuterTopRightCorner().row}expand(e){const t=this.getOuterTopStartCorner(),o=this.getOuterBottomEndCorner();return(e.row<t.row||e.col<t.col||e.row>o.row||e.col>o.col)&&(this.from=this._createCellCoords(Math.min(t.row,e.row),Math.min(t.col,e.col)),this.to=this._createCellCoords(Math.max(o.row,e.row),Math.max(o.col,e.col)),!0)}expandByRange(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this.includesRange(e)||!this.overlaps(e))return!1;const o=this.getOuterTopStartCorner(),r=this.getOuterBottomEndCorner(),i=this.getDirection(),n=e.getOuterTopStartCorner(),s=e.getOuterBottomEndCorner(),l=Math.min(o.row,n.row),a=Math.min(o.col,n.col),h=Math.max(r.row,s.row),c=Math.max(r.col,s.col),u=this._createCellCoords(l,a),d=this._createCellCoords(h,c);return this.from=u,this.to=d,this.setDirection(i),t&&(this.highlight.row===this.getOuterBottomRightCorner().row&&"N-S"===this.getVerticalDirection()&&this.flipDirectionVertically(),this.highlight.col===this.getOuterTopRightCorner().col&&"W-E"===this.getHorizontalDirection()&&this.flipDirectionHorizontally()),!0}getDirection(){return this.from.isNorthWestOf(this.to)?"NW-SE":this.from.isNorthEastOf(this.to)?"NE-SW":this.from.isSouthEastOf(this.to)?"SE-NW":this.from.isSouthWestOf(this.to)?"SW-NE":void 0}setDirection(e){switch(e){case"NW-SE":[this.from,this.to]=[this.getOuterTopLeftCorner(),this.getOuterBottomRightCorner()];break;case"NE-SW":[this.from,this.to]=[this.getOuterTopRightCorner(),this.getOuterBottomLeftCorner()];break;case"SE-NW":[this.from,this.to]=[this.getOuterBottomRightCorner(),this.getOuterTopLeftCorner()];break;case"SW-NE":[this.from,this.to]=[this.getOuterBottomLeftCorner(),this.getOuterTopRightCorner()]}}getVerticalDirection(){return["NE-SW","NW-SE"].indexOf(this.getDirection())>-1?"N-S":"S-N"}getHorizontalDirection(){return["NW-SE","SW-NE"].indexOf(this.getDirection())>-1?"W-E":"E-W"}flipDirectionVertically(){switch(this.getDirection()){case"NW-SE":this.setDirection("SW-NE");break;case"NE-SW":this.setDirection("SE-NW");break;case"SE-NW":this.setDirection("NE-SW");break;case"SW-NE":this.setDirection("NW-SE")}}flipDirectionHorizontally(){switch(this.getDirection()){case"NW-SE":this.setDirection("NE-SW");break;case"NE-SW":this.setDirection("NW-SE");break;case"SE-NW":this.setDirection("SW-NE");break;case"SW-NE":this.setDirection("SE-NW")}}getTopStartCorner(){return this._createCellCoords(Math.min(this.from.row,this.to.row),Math.min(this.from.col,this.to.col)).normalize()}getTopLeftCorner(){return Dy(By,this)?this.getTopEndCorner():this.getTopStartCorner()}getBottomEndCorner(){return this._createCellCoords(Math.max(this.from.row,this.to.row),Math.max(this.from.col,this.to.col)).normalize()}getBottomRightCorner(){return Dy(By,this)?this.getBottomStartCorner():this.getBottomEndCorner()}getTopEndCorner(){return this._createCellCoords(Math.min(this.from.row,this.to.row),Math.max(this.from.col,this.to.col)).normalize()}getTopRightCorner(){return Dy(By,this)?this.getTopStartCorner():this.getTopEndCorner()}getBottomStartCorner(){return this._createCellCoords(Math.max(this.from.row,this.to.row),Math.min(this.from.col,this.to.col)).normalize()}getBottomLeftCorner(){return Dy(By,this)?this.getBottomEndCorner():this.getBottomStartCorner()}getOuterTopStartCorner(){return this._createCellCoords(Math.min(this.from.row,this.to.row),Math.min(this.from.col,this.to.col))}getOuterTopLeftCorner(){return Dy(By,this)?this.getOuterTopEndCorner():this.getOuterTopStartCorner()}getOuterBottomEndCorner(){return this._createCellCoords(Math.max(this.from.row,this.to.row),Math.max(this.from.col,this.to.col))}getOuterBottomRightCorner(){return Dy(By,this)?this.getOuterBottomStartCorner():this.getOuterBottomEndCorner()}getOuterTopEndCorner(){return this._createCellCoords(Math.min(this.from.row,this.to.row),Math.max(this.from.col,this.to.col))}getOuterTopRightCorner(){return Dy(By,this)?this.getOuterTopStartCorner():this.getOuterTopEndCorner()}getOuterBottomStartCorner(){return this._createCellCoords(Math.max(this.from.row,this.to.row),Math.min(this.from.col,this.to.col))}getOuterBottomLeftCorner(){return Dy(By,this)?this.getOuterBottomEndCorner():this.getOuterBottomStartCorner()}isCorner(e){return e.isEqual(this.getOuterTopLeftCorner())||e.isEqual(this.getOuterTopRightCorner())||e.isEqual(this.getOuterBottomLeftCorner())||e.isEqual(this.getOuterBottomRightCorner())}getOppositeCorner(e){return e instanceof Py&&(e.isEqual(this.getOuterBottomEndCorner())?this.getOuterTopStartCorner():e.isEqual(this.getOuterTopStartCorner())?this.getOuterBottomEndCorner():e.isEqual(this.getOuterTopEndCorner())?this.getOuterBottomStartCorner():e.isEqual(this.getOuterBottomStartCorner())?this.getOuterTopEndCorner():void 0)}getBordersSharedWith(e){if(!this.includesRange(e))return[];const t=Math.min(this.from.row,this.to.row),o=Math.max(this.from.row,this.to.row),r=Math.min(this.from.col,this.to.col),i=Math.max(this.from.col,this.to.col),n=Math.min(e.from.row,e.to.row),s=Math.max(e.from.row,e.to.row),l=Math.min(e.from.col,e.to.col),a=Math.max(e.from.col,e.to.col),h=[];return t===n&&h.push("top"),i===a&&h.push(Dy(By,this)?"left":"right"),o===s&&h.push("bottom"),r===l&&h.push(Dy(By,this)?"right":"left"),h}getInner(){const e=this.getOuterTopStartCorner(),t=this.getOuterBottomEndCorner(),o=[];for(let r=e.row;r<=t.row;r++)for(let i=e.col;i<=t.col;i++)this.from.row===r&&this.from.col===i||this.to.row===r&&this.to.col===i||o.push(this._createCellCoords(r,i));return o}getAll(){const e=this.getOuterTopStartCorner(),t=this.getOuterBottomEndCorner(),o=[];for(let r=e.row;r<=t.row;r++)for(let i=e.col;i<=t.col;i++)e.row===r&&e.col===i?o.push(e):t.row===r&&t.col===i?o.push(t):o.push(this._createCellCoords(r,i));return o}forAll(e){const t=this.getOuterTopStartCorner(),o=this.getOuterBottomEndCorner();for(let r=t.row;r<=o.row;r++)for(let i=t.col;i<=o.col;i++)if(!1===e(r,i))return}clone(){return new Wy(this.highlight,this.from,this.to,Dy(By,this))}toObject(){return{from:this.from.toObject(),to:this.to.toObject()}}_createCellCoords(e,t){return new Py(e,t,Dy(By,this))}}const jy=Wy;function zy(e,t,o){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,o)}function $y(e,t){return e.get(Xy(e,t))}function Uy(e,t,o){return e.set(Xy(e,t),o),o}function Xy(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var Gy=new WeakMap,Yy=new WeakMap,Ky=new WeakMap,qy=new WeakMap,Qy=new WeakMap,Jy=new WeakMap,Zy=new WeakMap,ev=new WeakMap,tv=new WeakMap,ov=new WeakMap;const rv=class{constructor(e,t,o,r,i,n){let s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:null;zy(this,Gy,void 0),zy(this,Yy,void 0),zy(this,Ky,void 0),zy(this,qy,void 0),zy(this,Qy,void 0),zy(this,Jy,void 0),zy(this,Zy,void 0),zy(this,ev,void 0),zy(this,tv,[null,null]),zy(this,ov,[null,null]),Uy(Gy,this,o),Uy(Yy,this,t),Uy(Ky,this,i),Uy(qy,this,n),Uy(Qy,this,s),Uy(Jy,this,r),Uy(Zy,this,e),this.registerEvents()}registerEvents(){$y(Jy,this).addEventListener($y(Ky,this).holder,"contextmenu",(e=>this.onContextMenu(e))),$y(Jy,this).addEventListener($y(Ky,this).TABLE,"mouseover",(e=>this.onMouseOver(e))),$y(Jy,this).addEventListener($y(Ky,this).TABLE,"mouseout",(e=>this.onMouseOut(e)));const e=()=>{$y(Jy,this).addEventListener($y(Ky,this).holder,"touchstart",(e=>this.onTouchStart(e))),$y(Jy,this).addEventListener($y(Ky,this).holder,"touchend",(e=>this.onTouchEnd(e))),this.momentumScrolling||(this.momentumScrolling={}),$y(Jy,this).addEventListener($y(Ky,this).holder,"scroll",(()=>{clearTimeout(this.momentumScrolling._timeout),this.momentumScrolling.ongoing||$y(Gy,this).getSetting("onBeforeTouchScroll"),this.momentumScrolling.ongoing=!0,this.momentumScrolling._timeout=setTimeout((()=>{this.touchApplied||(this.momentumScrolling.ongoing=!1,$y(Gy,this).getSetting("onAfterMomentumScroll"))}),200)}))},t=()=>{$y(Jy,this).addEventListener($y(Ky,this).holder,"mouseup",(e=>this.onMouseUp(e))),$y(Jy,this).addEventListener($y(Ky,this).holder,"mousedown",(e=>this.onMouseDown(e)))};qf()?e():("ontouchstart"in window&&e(),t())}selectedCellWasTouched(e){const t=this.parentCell(e).coords;if($y(ev,this)&&t){const[e,o]=[t.row,$y(ev,this).from.row],[r,i]=[t.col,$y(ev,this).from.col];return e===o&&r===i}return!1}parentCell(e){const t={},o=pg(e,["TD","TH"],$y(Ky,this).TABLE);return o?(t.coords=$y(Ky,this).getCoords(o),t.TD=o):Cg(e,"wtBorder")&&Cg(e,"current")?(t.coords=$y(qy,this).getFocusSelection().cellRange.highlight,t.TD=$y(Ky,this).getCell(t.coords)):Cg(e,"wtBorder")&&Cg(e,"area")&&$y(qy,this).getAreaSelection().cellRange&&(t.coords=$y(qy,this).getAreaSelection().cellRange.to,t.TD=$y(Ky,this).getCell(t.coords)),t}onMouseDown(e){const t=$y(Yy,this).rootDocument.activeElement,o=function(e){for(var t=arguments.length,o=new Array(t>1?t-1:0),r=1;r<t;r++)o[r-1]=arguments[r];return function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return e.apply(this,o.concat(r))}}(gg,e.target),r=e.target;if(!["TD","TH"].includes(t.nodeName)&&(r===t||o(0)===t||o(1)===t))return;const i=this.parentCell(r);Cg(r,"corner")?$y(Gy,this).getSetting("onCellCornerMouseDown",e,r):i.TD&&$y(Gy,this).has("onCellMouseDown")&&this.callListener("onCellMouseDown",e,i.coords,i.TD),(0===e.button||this.touchApplied)&&i.TD&&($y(ov,this)[0]=i.TD,clearTimeout($y(tv,this)[0]),$y(tv,this)[0]=setTimeout((()=>{$y(ov,this)[0]=null}),1e3))}onContextMenu(e){if($y(Gy,this).has("onCellContextMenu")){const t=this.parentCell(e.target);t.TD&&this.callListener("onCellContextMenu",e,t.coords,t.TD)}}onMouseOver(e){if(!$y(Gy,this).has("onCellMouseOver"))return;const t=$y(Ky,this).TABLE,o=pg(e.target,["TD","TH"],t),r=$y(Qy,this)||this;o&&o!==r.lastMouseOver&&wg(o,t)&&(r.lastMouseOver=o,this.callListener("onCellMouseOver",e,$y(Ky,this).getCoords(o),o))}onMouseOut(e){if(!$y(Gy,this).has("onCellMouseOut"))return;const t=$y(Ky,this).TABLE,o=pg(e.target,["TD","TH"],t),r=pg(e.relatedTarget,["TD","TH"],t),i=$y(Qy,this)||this;o&&o!==r&&wg(o,t)&&(this.callListener("onCellMouseOut",e,$y(Ky,this).getCoords(o),o),null===r&&(i.lastMouseOver=null))}onMouseUp(e){const t=this.parentCell(e.target);t.TD&&$y(Gy,this).has("onCellMouseUp")&&this.callListener("onCellMouseUp",e,t.coords,t.TD),(0===e.button||this.touchApplied)&&(t.TD===$y(ov,this)[0]&&t.TD===$y(ov,this)[1]?(Cg(e.target,"corner")?this.callListener("onCellCornerDblClick",e,t.coords,t.TD):this.callListener("onCellDblClick",e,t.coords,t.TD),$y(ov,this)[0]=null,$y(ov,this)[1]=null):t.TD===$y(ov,this)[0]&&($y(ov,this)[1]=t.TD,clearTimeout($y(tv,this)[1]),$y(tv,this)[1]=setTimeout((()=>{$y(ov,this)[1]=null}),500)))}onTouchStart(e){Uy(ev,this,$y(qy,this).getFocusSelection().cellRange),this.touchApplied=!0,this.onMouseDown(e)}onTouchEnd(e){var t;const o=e.target,r=null===(t=this.parentCell(o))||void 0===t?void 0:t.coords,i=Gd(r)&&r.row>=0&&r.col>=0;if(e.cancelable&&i&&$y(Gy,this).getSetting("isDataViewInstance")){const t=["A","BUTTON","INPUT"];Qf()&&(Gf.chromeWebKit.value||Gf.firefoxWebKit.value)&&this.selectedCellWasTouched(o)&&!t.includes(o.tagName)?e.preventDefault():this.selectedCellWasTouched(o)||e.preventDefault()}this.onMouseUp(e),this.touchApplied=!1}callListener(e,t,o,r){const i=$y(Gy,this).getSettingPure(e);i&&i(t,o,r,$y(Zy,this).call(this))}destroy(){clearTimeout($y(tv,this)[0]),clearTimeout($y(tv,this)[1]),$y(Jy,this).destroy()}};function iv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const nv=class{constructor(e,t,o){iv(this,"offset",void 0),iv(this,"total",void 0),iv(this,"countTH",void 0),this.offset=e,this.total=t,this.countTH=o}offsetted(e){return e+this.offset}unOffsetted(e){return e-this.offset}renderedToSource(e){return this.offsetted(e)}sourceToRendered(e){return this.unOffsetted(e)}offsettedTH(e){return e-this.countTH}unOffsettedTH(e){return e+this.countTH}visibleRowHeadedColumnToSourceColumn(e){return this.renderedToSource(this.offsettedTH(e))}sourceColumnToVisibleRowHeadedColumn(e){return this.unOffsettedTH(this.sourceToRendered(e))}};function sv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const lv=class{constructor(e,t,o){sv(this,"offset",void 0),sv(this,"total",void 0),sv(this,"countTH",void 0),this.offset=e,this.total=t,this.countTH=o}offsetted(e){return e+this.offset}unOffsetted(e){return e-this.offset}renderedToSource(e){return this.offsetted(e)}sourceToRendered(e){return this.unOffsetted(e)}offsettedTH(e){return e-this.countTH}unOffsettedTH(e){return e+this.countTH}visibleColHeadedRowToSourceRow(e){return this.renderedToSource(this.offsettedTH(e))}sourceRowToVisibleColHeadedRow(e){return this.unOffsettedTH(this.sourceToRendered(e))}};function av(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class hv{constructor(){av(this,"currentSize",0),av(this,"nextSize",0),av(this,"currentOffset",0),av(this,"nextOffset",0)}setSize(e){this.currentSize=this.nextSize,this.nextSize=e}setOffset(e){this.currentOffset=this.nextOffset,this.nextOffset=e}}function cv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class uv{constructor(){cv(this,"size",new hv),cv(this,"workingSpace",0),cv(this,"sharedSize",null)}setSize(e){this.size.setSize(e)}setOffset(e){this.size.setOffset(e)}getViewSize(){return this.size}isShared(){return null!==this.sharedSize}isPlaceOn(e){return this.workingSpace===e}append(e){this.workingSpace=1,e.workingSpace=2,this.sharedSize=e.getViewSize()}prepend(e){this.workingSpace=2,e.workingSpace=1,this.sharedSize=e.getViewSize()}}var dv=Me,gv=TypeError,fv=gi,mv=Ze,pv=Tr,wv=_n,yv=function(e,t){if(!delete e[t])throw new gv("Cannot delete property "+dv(t)+" of "+dv(e))},vv=Vn,bv=1!==[].unshift(0)||!function(){try{Object.defineProperty([],"length",{writable:!1}).unshift()}catch(e){return e instanceof TypeError}}();function Cv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}fv({target:"Array",proto:!0,arity:1,forced:bv},{unshift:function(e){var t=mv(this),o=pv(t),r=arguments.length;if(r){vv(o+r);for(var i=o;i--;){var n=i+r;i in t?t[n]=t[i]:yv(t,n)}for(var s=0;s<r;s++)t[s]=arguments[s]}return wv(t,o+r)}});class Sv{constructor(e,t){Cv(this,"order",[]),this.order=[...Array(t).keys()].map((t=>e+t))}get length(){return this.order.length}has(e){return this.order.indexOf(e)>-1}get(e){return e<this.order.length?this.order[e]:-1}remove(e){this.order.splice(this.order.indexOf(e),1)}prepend(e){return this.order.unshift(e),this.order.pop()}}function Rv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Tv{constructor(e){Rv(this,"sizeSet",void 0),this.sizeSet=e}diff(){const{sizeSet:e}=this,{currentSize:t,nextSize:o}=e.getViewSize();let r=Math.max(o,t);if(0===r)return[];const{currentOffset:i,nextOffset:n}=e.getViewSize(),s=new Sv(i,t),l=new Sv(n,o),a=[];for(let h=0;h<r;h++){const t=s.get(h),i=l.get(h);if(-1===i)a.push(["remove",t]);else if(-1===t)!e.isShared()||e.isShared()&&e.isPlaceOn(2)?a.push(["append",i]):a.push(["prepend",i]);else if(i>t)s.has(i)&&(s.remove(i),o<=s.length&&(r-=1)),a.push(["replace",i,t]);else if(i<t){const e=s.prepend(i);a.push(["insert_before",i,t,e])}else a.push(["none",i])}return a}}function Ev(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Hv{constructor(e,t){Ev(this,"rootNode",void 0),Ev(this,"nodesPool",void 0),Ev(this,"sizeSet",new uv),Ev(this,"collectedNodes",[]),Ev(this,"viewDiffer",new Tv(this.sizeSet)),Ev(this,"leads",[]),this.rootNode=e,this.nodesPool=t}setSize(e){return this.sizeSet.setSize(e),this}setOffset(e){return this.sizeSet.setOffset(e),this}isSharedViewSet(){return this.sizeSet.isShared()}getNode(e){return e<this.collectedNodes.length?this.collectedNodes[e]:null}getCurrentNode(){const e=this.collectedNodes.length;return e>0?this.collectedNodes[e-1]:null}applyCommand(e){const{rootNode:t}=this,[o,r,i,n]=e,s=this.nodesPool(r);switch(this.collectedNodes.push(s),o){case"prepend":t.insertBefore(s,t.firstChild);break;case"append":t.appendChild(s);break;case"insert_before":t.insertBefore(s,this.nodesPool(i)),t.removeChild(this.nodesPool(n));break;case"replace":t.replaceChild(s,this.nodesPool(i));break;case"remove":t.removeChild(s)}}start(){this.collectedNodes.length=0,this.leads=this.viewDiffer.diff()}render(){this.leads.length>0&&this.applyCommand(this.leads.shift())}end(){for(;this.leads.length>0;)this.applyCommand(this.leads.shift())}}class Ov extends Hv{prependView(e){return this.sizeSet.prepend(e.sizeSet),e.sizeSet.append(this.sizeSet),this}appendView(e){return this.sizeSet.append(e.sizeSet),e.sizeSet.prepend(this.sizeSet),this}}function Mv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class xv{constructor(e){Mv(this,"nodeType",void 0),Mv(this,"pool",new Map),this.nodeType=e.toUpperCase()}setRootDocument(e){this.rootDocument=e}obtain(e,t){const o="number"==typeof t?`${e}x${t}`:e.toString();if(this.pool.has(o))return this.pool.get(o);const r=this.rootDocument.createElement(this.nodeType);return this.pool.set(o,r),r}}function Iv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Nv{constructor(e,t){Iv(this,"nodesPool",null),Iv(this,"nodeType",void 0),Iv(this,"rootNode",void 0),Iv(this,"table",null),Iv(this,"renderedNodes",0),this.nodesPool="string"==typeof e?new xv(e):null,this.nodeType=e,this.rootNode=t}setTable(e){this.nodesPool&&this.nodesPool.setRootDocument(e.rootDocument),this.table=e}adjust(){}render(){}}function Av(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class kv extends Nv{constructor(){super("TH"),Av(this,"orderViews",new WeakMap),Av(this,"sourceRowIndex",0)}obtainOrderView(e){let t;return this.orderViews.has(e)?t=this.orderViews.get(e):(t=new Ov(e,(e=>this.nodesPool.obtain(this.sourceRowIndex,e))),this.orderViews.set(e,t)),t}render(){const{rowsToRender:e,rowHeaderFunctions:t,rowHeadersCount:o,rows:r,cells:i}=this.table;for(let n=0;n<e;n++){const e=this.table.renderedRowToSource(n),s=r.getRenderedNode(n);this.sourceRowIndex=e;const l=this.obtainOrderView(s),a=i.obtainOrderView(s);l.appendView(a).setSize(o).setOffset(0).start();for(let r=o-1;r>=0;r--){l.render();const o=l.getCurrentNode();o.className="",o.removeAttribute("style"),Eg(o,[new RegExp("aria-(.*)"),new RegExp("role")]),this.table.isAriaEnabled()&&Tg(o,[["role","rowheader"],["scope","row"],dg(r+1),ag(-1)]),t[r](e,o,r)}l.end()}}}class _v extends Nv{constructor(e){super(null,e)}adjust(){const{columnHeadersCount:e,rowHeadersCount:t}=this.table;let o=this.rootNode.firstChild;if(e){const{columnsToRender:r}=this.table,i=r+t;for(let t=0,s=e;t<s;t++){for(o=this.rootNode.childNodes[t],o||(o=this.table.rootDocument.createElement("tr"),this.rootNode.appendChild(o)),this.renderedNodes=o.childNodes.length;this.renderedNodes<i;)o.appendChild(this.table.rootDocument.createElement("th")),this.renderedNodes+=1;for(;this.renderedNodes>i;)o.removeChild(o.lastChild),this.renderedNodes-=1}const n=this.rootNode.childNodes.length;if(n>e)for(let t=e;t<n;t++)this.rootNode.removeChild(this.rootNode.lastChild)}else o&&Og(o)}render(){const{columnHeadersCount:e}=this.table;this.table.isAriaEnabled()&&Tg(this.rootNode,[["role","rowgroup"]]);for(let t=0;t<e;t+=1){const{columnHeaderFunctions:e,columnsToRender:o,rowHeadersCount:r}=this.table,i=this.rootNode.childNodes[t];this.table.isAriaEnabled()&&Tg(i,[["role","row"],ug(t+1)]);for(let n=-1*r;n<o;n+=1){const o=this.table.renderedColumnToSource(n),s=i.childNodes[n+r];s.className="",s.removeAttribute("style"),Eg(s,[new RegExp("aria-(.*)"),new RegExp("role")]),this.table.isAriaEnabled()&&Tg(s,[dg(n+1+this.table.rowHeadersCount),ag(-1),["role","columnheader"],...n>=0?[["scope","col"]]:[["role","row"]]]),e[t](o,s,t)}}}}let Pv=!1;class Vv extends Nv{constructor(e){super(null,e)}adjust(){const{columnsToRender:e,rowHeadersCount:t}=this.table,o=e+t;for(;this.renderedNodes<o;)this.rootNode.appendChild(this.table.rootDocument.createElement("col")),this.renderedNodes+=1;for(;this.renderedNodes>o;)this.rootNode.removeChild(this.rootNode.lastChild),this.renderedNodes-=1}render(){this.adjust();const{columnsToRender:e,rowHeadersCount:t}=this.table;!Pv&&e>1e3&&(Pv=!0,nm(Ud`Performance tip: Handsontable rendered more than 1000 visible columns.\x20
        Consider limiting the number of rendered columns by specifying the table width and/or\x20
        turning off the "renderAllColumns" option.`));for(let r=0;r<t;r++){const e=this.table.renderedColumnToSource(r),t=this.table.columnUtils.getHeaderWidth(e);this.rootNode.childNodes[r].style.width=`${t}px`}for(let r=0;r<e;r++){const e=this.table.renderedColumnToSource(r),o=this.table.columnUtils.getWidth(e);this.rootNode.childNodes[r+t].style.width=`${o}px`}const o=this.rootNode.firstChild;o&&Sg(o,"rowHeader")}}function Lv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const Dv="ht__row_even",Fv="ht__row_odd";let Bv=!1;class Wv extends Nv{constructor(e){super("TR",e),Lv(this,"orderView",void 0),this.orderView=new Hv(e,(e=>this.nodesPool.obtain(e)))}getRenderedNode(e){return this.orderView.getNode(e)}hasStaleContent(e){return this.orderView.hasStaleContent(e)}render(){const{rowsToRender:e}=this.table;!Bv&&e>1e3&&(Bv=!0,nm(Ud`Performance tip: Handsontable rendered more than 1000 visible rows.\x20
        Consider limiting the number of rendered rows by specifying the table height and/or\x20
        turning off the "renderAllRows" option.`)),this.table.isAriaEnabled()&&Tg(this.rootNode,[["role","rowgroup"]]),this.orderView.setSize(e).setOffset(this.table.renderedRowToSource(0)).start();for(let r=0;r<e;r++){this.orderView.render();const e=this.orderView.getCurrentNode(),i=this.table.renderedRowToSource(r);var t,o;this.table.isAriaEnabled()&&Tg(e,[["role","row"],ug(i+(null!==(t=null===(o=this.table.rowUtils)||void 0===o||null===(o=o.dataAccessObject)||void 0===o?void 0:o.columnHeaders.length)&&void 0!==t?t:0)+1)]),(i+1)%2==0?Cg(e,Dv)||(Rg(e,Fv),Sg(e,Dv)):Cg(e,Fv)||(Rg(e,Dv),Sg(e,Fv))}this.orderView.end()}}function jv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class zv extends Nv{constructor(){super("TD"),jv(this,"orderViews",new WeakMap),jv(this,"sourceRowIndex",0)}obtainOrderView(e){let t;return this.orderViews.has(e)?t=this.orderViews.get(e):(t=new Ov(e,(e=>this.nodesPool.obtain(this.sourceRowIndex,e)),this.nodeType),this.orderViews.set(e,t)),t}render(){const{rowsToRender:e,columnsToRender:t,rows:o,rowHeaders:r}=this.table;for(let s=0;s<e;s++){const e=this.table.renderedRowToSource(s),l=o.getRenderedNode(s);this.sourceRowIndex=e;const a=this.obtainOrderView(l),h=r.obtainOrderView(l);a.prependView(h).setSize(t).setOffset(0).start();for(let o=0;o<t;o++){a.render();const t=this.table.renderedColumnToSource(o),r=a.getCurrentNode();var i,n;Cg(r,"hide")||(r.className=""),r.removeAttribute("style"),r.removeAttribute("dir"),Eg(r,[new RegExp("aria-(.*)"),new RegExp("role")]),this.table.cellRenderer(e,t,r),this.table.isAriaEnabled()&&Tg(r,[...r.hasAttribute("role")?[]:[["role","gridcell"]],ag(-1),dg(t+(null!==(i=null===(n=this.table.rowUtils)||void 0===n||null===(n=n.dataAccessObject)||void 0===n?void 0:n.rowHeaders.length)&&void 0!==i?i:0)+1)])}a.end()}}}function $v(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Uv{constructor(e){let{cellRenderer:t,stylesHandler:o}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};$v(this,"rootNode",void 0),$v(this,"rootDocument",void 0),$v(this,"rowHeaders",null),$v(this,"columnHeaders",null),$v(this,"colGroup",null),$v(this,"rows",null),$v(this,"cells",null),$v(this,"rowFilter",null),$v(this,"columnFilter",null),$v(this,"rowUtils",null),$v(this,"columnUtils",null),$v(this,"rowsToRender",0),$v(this,"columnsToRender",0),$v(this,"rowHeaderFunctions",[]),$v(this,"rowHeadersCount",0),$v(this,"columnHeaderFunctions",[]),$v(this,"columnHeadersCount",0),$v(this,"cellRenderer",void 0),$v(this,"activeOverlayName",void 0),$v(this,"stylesHandler",void 0),this.rootNode=e,this.rootDocument=this.rootNode.ownerDocument,this.cellRenderer=t,this.stylesHandler=o}setActiveOverlayName(e){this.activeOverlayName=e}setAxisUtils(e,t){this.rowUtils=e,this.columnUtils=t}setViewportSize(e,t){this.rowsToRender=e,this.columnsToRender=t}setFilters(e,t){this.rowFilter=e,this.columnFilter=t}setHeaderContentRenderers(e,t){this.rowHeaderFunctions=e,this.rowHeadersCount=e.length,this.columnHeaderFunctions=t,this.columnHeadersCount=t.length}setRenderers(){let{rowHeaders:e,columnHeaders:t,colGroup:o,rows:r,cells:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.setTable(this),t.setTable(this),o.setTable(this),r.setTable(this),i.setTable(this),this.rowHeaders=e,this.columnHeaders=t,this.colGroup=o,this.rows=r,this.cells=i}renderedRowToSource(e){return this.rowFilter.renderedToSource(e)}renderedColumnToSource(e){return this.columnFilter.renderedToSource(e)}isAriaEnabled(){return this.rowUtils.wtSettings.getSetting("ariaTags")}render(){this.colGroup.adjust(),this.columnHeaders.adjust(),this.rows.adjust(),this.rowHeaders.adjust(),this.columnHeaders.render(),this.rows.render(),this.rowHeaders.render(),this.cells.render(),this.columnUtils.calculateWidths(),this.colGroup.render();const{rowsToRender:e,rows:t}=this;for(let o=0;o<e;o++){const e=t.getRenderedNode(o),r=this.rowUtils;if(e.firstChild){const t=this.renderedRowToSource(o),i=r.getHeightByOverlayName(t,this.activeOverlayName),n=this.stylesHandler.areCellsBorderBox()?0:1;e.firstChild.style.height=i?i-n+"px":""}}}}class Xv{constructor(){let{TABLE:e,THEAD:t,COLGROUP:o,TBODY:r,rowUtils:i,columnUtils:n,cellRenderer:s,stylesHandler:l}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.renderer=new Uv(e,{cellRenderer:s,stylesHandler:l}),this.renderer.setRenderers({rowHeaders:new kv,columnHeaders:new _v(t),colGroup:new Vv(o),rows:new Wv(r),cells:new zv}),this.renderer.setAxisUtils(i,n)}setActiveOverlayName(e){return this.renderer.setActiveOverlayName(e),this}setFilters(e,t){return this.renderer.setFilters(e,t),this}setViewportSize(e,t){return this.renderer.setViewportSize(e,t),this}setHeaderContentRenderers(e,t){return this.renderer.setHeaderContentRenderers(e,t),this}adjust(){this.renderer.adjust()}render(){this.renderer.render()}}function Gv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Yv{constructor(e,t){Gv(this,"dataAccessObject",void 0),Gv(this,"wtSettings",void 0),Gv(this,"headerWidths",new Map),this.dataAccessObject=e,this.wtSettings=t}getWidth(e){return this.wtSettings.getSetting("columnWidth",e)||this.wtSettings.getSetting("defaultColumnWidth")}getHeaderHeight(e){let t=this.dataAccessObject.stylesHandler.getDefaultRowHeight();const o=this.dataAccessObject.wtViewport.oversizedColumnHeaders[e];return void 0!==o&&(t=t?Math.max(t,o):o),t}getHeaderWidth(e){return this.headerWidths.get(this.dataAccessObject.wtTable.columnFilter.sourceToRendered(e))}calculateWidths(){const{wtSettings:e}=this;let t=e.getSetting("rowHeaderWidth");if(t=e.getSetting("onModifyRowHeaderWidth",t),null!=t){const o=e.getSetting("rowHeaders").length,r=e.getSetting("defaultColumnWidth");for(let e=0;e<o;e++){let o=Array.isArray(t)?t[e]:t;o=null==o?r:o,this.headerWidths.set(e,o)}}}}function Kv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class qv{constructor(e,t){Kv(this,"dataAccessObject",void 0),Kv(this,"wtSettings",void 0),this.dataAccessObject=e,this.wtSettings=t}getHeight(e){let t=this.wtSettings.getSetting("rowHeight",e);const o=this.dataAccessObject.wtViewport.oversizedRows[e];return void 0!==o&&(t=void 0===t?o:Math.max(t,o)),t}getHeightByOverlayName(e,t){let o=this.wtSettings.getSetting("rowHeightByOverlayName",e,t);const r=this.dataAccessObject.wtViewport.oversizedRows[e];return void 0!==r&&(o=void 0===o?r:Math.max(o,r)),o}}function Qv(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const Jv=class{constructor(e,t,o,r,i){Qv(this,"wtSettings",null),Qv(this,"domBindings",void 0),Qv(this,"TBODY",null),Qv(this,"THEAD",null),Qv(this,"COLGROUP",null),Qv(this,"hasTableHeight",!0),Qv(this,"hasTableWidth",!0),Qv(this,"isTableVisible",!1),Qv(this,"tableOffset",0),Qv(this,"holderOffset",0),this.domBindings=o,this.isMaster="master"===i,this.name=i,this.dataAccessObject=e,this.facadeGetter=t,this.wtSettings=r,this.instance=this.dataAccessObject.wot,this.wot=this.dataAccessObject.wot,this.TABLE=o.rootTable,Hg(this.TABLE),this.spreader=this.createSpreader(this.TABLE),this.hider=this.createHider(this.spreader),this.holder=this.createHolder(this.hider),this.wtRootElement=this.holder.parentNode,this.isMaster&&this.alignOverlaysWithTrimmingContainer(),this.fixTableDomTree(),this.rowFilter=null,this.columnFilter=null,this.correctHeaderWidth=!1;const n=this.wtSettings.getSettingPure("rowHeaderWidth");this.wtSettings.update("rowHeaderWidth",(()=>this._modifyRowHeaderWidth(n))),this.rowUtils=new qv(this.dataAccessObject,this.wtSettings),this.columnUtils=new Yv(this.dataAccessObject,this.wtSettings),this.tableRenderer=new Xv({TABLE:this.TABLE,THEAD:this.THEAD,COLGROUP:this.COLGROUP,TBODY:this.TBODY,rowUtils:this.rowUtils,columnUtils:this.columnUtils,cellRenderer:this.wtSettings.getSettingPure("cellRenderer"),stylesHandler:this.dataAccessObject.stylesHandler})}is(e){return this.name===e}fixTableDomTree(){const e=this.domBindings.rootDocument;this.TBODY=this.TABLE.querySelector("tbody"),this.TBODY||(this.TBODY=e.createElement("tbody"),this.TABLE.appendChild(this.TBODY)),this.THEAD=this.TABLE.querySelector("thead"),this.THEAD||(this.THEAD=e.createElement("thead"),this.TABLE.insertBefore(this.THEAD,this.TBODY)),this.COLGROUP=this.TABLE.querySelector("colgroup"),this.COLGROUP||(this.COLGROUP=e.createElement("colgroup"),this.TABLE.insertBefore(this.COLGROUP,this.THEAD))}createSpreader(e){const t=e.parentNode;let o;return t&&t.nodeType===Node.ELEMENT_NODE&&Cg(t,"wtHolder")||(o=this.domBindings.rootDocument.createElement("div"),o.className="wtSpreader",t&&t.insertBefore(o,e),o.appendChild(e)),o.style.position="relative",this.wtSettings.getSetting("ariaTags")&&Tg(o,[["role","presentation"]]),o}createHider(e){const t=e.parentNode;let o;return t&&t.nodeType===Node.ELEMENT_NODE&&Cg(t,"wtHolder")||(o=this.domBindings.rootDocument.createElement("div"),o.className="wtHider",t&&t.insertBefore(o,e),o.appendChild(e)),this.wtSettings.getSetting("ariaTags")&&Tg(o,[["role","presentation"]]),o}createHolder(e){const t=e.parentNode;let o;return t&&t.nodeType===Node.ELEMENT_NODE&&Cg(t,"wtHolder")||(o=this.domBindings.rootDocument.createElement("div"),o.style.position="relative",o.className="wtHolder",Tg(o,[ag(-1)]),t&&t.insertBefore(o,e),this.isMaster&&(o.parentNode.className+="ht_master handsontable",o.parentNode.setAttribute("dir",this.wtSettings.getSettingPure("rtlMode")?"rtl":"ltr"),this.wtSettings.getSetting("ariaTags")&&Tg(o.parentNode,[["role","presentation"]])),o.appendChild(e)),this.wtSettings.getSetting("ariaTags")&&Tg(o,[["role","presentation"]]),o}draw(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const{wtSettings:t}=this,{wtOverlays:o,wtViewport:r}=this.dataAccessObject,i=t.getSetting("totalRows"),n=t.getSetting("totalColumns"),s=t.getSetting("rowHeaders"),l=s.length,a=t.getSetting("columnHeaders"),h=a.length;let c=e;if(this.isMaster&&(o.beforeDraw(),this.holderOffset=Ag(this.holder),c=r.createCalculators(c),l&&!t.getSetting("fixedColumnsStart"))){const e=o.inlineStartOverlay.getScrollPosition(),t=this.correctHeaderWidth;this.correctHeaderWidth=0!==e,t!==this.correctHeaderWidth&&(c=!1)}if(c)this.isMaster&&o.refresh(!0);else{this.isMaster?this.tableOffset=Ag(this.TABLE):this.tableOffset=this.dataAccessObject.parentTableOffset;const e=Math.max(this.getFirstRenderedRow(),0),t=Math.max(this.getFirstRenderedColumn(),0);this.rowFilter=new lv(e,i,h),this.columnFilter=new nv(t,n,l);let c=!0;if(this.isMaster){this.alignOverlaysWithTrimmingContainer();const e={};this.wtSettings.getSetting("beforeDraw",!0,e),c=!0!==e.skipRender}c&&(this.tableRenderer.setHeaderContentRenderers(s,a),(this.is(sb)||this.is(hb))&&this.tableRenderer.setHeaderContentRenderers(s,[]),this.resetOversizedRows(),this.tableRenderer.setActiveOverlayName(this.name).setViewportSize(this.getRenderedRowsCount(),this.getRenderedColumnsCount()).setFilters(this.rowFilter,this.columnFilter).render(),this.isMaster&&this.markOversizedColumnHeaders(),this.adjustColumnHeaderHeights(),(this.isMaster||this.is(sb))&&this.markOversizedRows(),this.isMaster?(this.wtSettings.getSetting("externalRowCalculator")||r.createVisibleCalculators(),o.refresh(!1),o.applyToDOM(),this.wtSettings.getSetting("onDraw",!0)):this.is(sb)&&this.dataAccessObject.cloneSource.wtOverlays.adjustElementsSize())}let u=!1;return this.isMaster&&(u=o.topOverlay.resetFixedPosition(),o.bottomOverlay.clone&&(u=o.bottomOverlay.resetFixedPosition()||u),u=o.inlineStartOverlay.resetFixedPosition()||u,o.topInlineStartCornerOverlay&&o.topInlineStartCornerOverlay.resetFixedPosition(),o.bottomInlineStartCornerOverlay&&o.bottomInlineStartCornerOverlay.clone&&o.bottomInlineStartCornerOverlay.resetFixedPosition()),u?(o.refreshAll(),o.adjustElementsSize()):this.dataAccessObject.selectionManager.setActiveOverlay(this.facadeGetter()).render(c),this.isMaster&&o.afterDraw(),this.dataAccessObject.drawn=!0,this}markIfOversizedColumnHeader(e){const t=this.columnFilter.renderedToSource(e);let o=this.wtSettings.getSetting("columnHeaders").length;const r=this.dataAccessObject.stylesHandler.getDefaultRowHeight();let i,n,s;const l=this.wtSettings.getSetting("columnHeaderHeight")||[];for(;o;)o-=1,i=this.getColumnHeaderHeight(o),n=this.getColumnHeader(t,o),n&&(s=jg(n),(!i&&r<s||i<s)&&(this.dataAccessObject.wtViewport.oversizedColumnHeaders[o]=s),Array.isArray(l)?null!==l[o]&&void 0!==l[o]&&(this.dataAccessObject.wtViewport.oversizedColumnHeaders[o]=l[o]):isNaN(l)||(this.dataAccessObject.wtViewport.oversizedColumnHeaders[o]=l),this.dataAccessObject.wtViewport.oversizedColumnHeaders[o]<(l[o]||l)&&(this.dataAccessObject.wtViewport.oversizedColumnHeaders[o]=l[o]||l))}adjustColumnHeaderHeights(){const{wtSettings:e}=this,t=this.THEAD.childNodes,o=this.dataAccessObject.wtViewport.oversizedColumnHeaders;for(let r=0,i=e.getSetting("columnHeaders").length;r<i;r++)if(o[r]){if(!t[r]||0===t[r].childNodes.length)return;t[r].childNodes[0].style.height=`${o[r]}px`}}resetOversizedRows(){const{wtSettings:e}=this,{wtViewport:t}=this.dataAccessObject;if((this.isMaster||this.is(sb))&&!e.getSetting("externalRowCalculator")){const e=this.getRenderedRowsCount();for(let o=0;o<e;o++){const e=this.rowFilter.renderedToSource(o);t.oversizedRows&&t.oversizedRows[e]&&(t.oversizedRows[e]=void 0)}}}getCell(e){let t=e.row,o=e.col;const r=this.wtSettings.getSetting("onModifyGetCellCoords",t,o,!this.isMaster,"render");if(r&&Array.isArray(r)&&([t,o]=r),this.isRowBeforeRenderedRows(t))return-1;if(this.isRowAfterRenderedRows(t))return-2;if(this.isColumnBeforeRenderedColumns(o))return-3;if(this.isColumnAfterRenderedColumns(o))return-4;const i=this.getRow(t);if(!i&&t>=0)throw new Error("TR was expected to be rendered but is not");const n=i.childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(o)];if(!n&&o>=0)throw new Error("TD or TH was expected to be rendered but is not");return n}getRow(e){let t=null,o=null;var r,i;return e<0?(t=null===(r=this.rowFilter)||void 0===r?void 0:r.sourceRowToVisibleColHeadedRow(e),o=this.THEAD):(t=null===(i=this.rowFilter)||void 0===i?void 0:i.sourceToRendered(e),o=this.TBODY),void 0!==t&&void 0!==o&&!(o.childNodes.length<t+1)&&o.childNodes[t]}getColumnHeader(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const o=this.THEAD.childNodes[t];return null==o?void 0:o.childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(e)]}getColumnHeaders(e){const t=[],o=this.columnFilter.sourceColumnToVisibleRowHeadedColumn(e);return this.THEAD.childNodes.forEach((e=>{const r=e.childNodes[o];r&&t.push(r)})),t}getRowHeader(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(t>=this.wtSettings.getSetting("rowHeaders").length)return;const o=this.rowFilter.sourceToRendered(e),r=o<0?this.rowFilter.sourceRowToVisibleColHeadedRow(e):o,i=(o<0?this.THEAD:this.TBODY).childNodes[r];return null==i?void 0:i.childNodes[t]}getRowHeaders(e){const t=[],o=this.wtSettings.getSetting("rowHeaders").length;for(let r=0;r<o;r++){const o=this.TBODY.childNodes[this.rowFilter.sourceToRendered(e)],i=null==o?void 0:o.childNodes[r];i&&t.push(i)}return t}getCoords(e){let t=e;if("TD"!==t.nodeName&&"TH"!==t.nodeName&&(t=mg(t,["TD","TH"])),null===t)return null;const o=t.parentNode;if(!o)return null;const r=o.parentNode;let i=function(e){let t=0,o=e;if(o.previousSibling)for(;o=o.previousSibling;)t+=1;return t}(o),n=t.cellIndex;yg(ab,t,this.wtRootElement)||yg(nb,t,this.wtRootElement)?"THEAD"===r.nodeName&&(i-=r.childNodes.length):yg(hb,t,this.wtRootElement)||yg(sb,t,this.wtRootElement)?i=this.wtSettings.getSetting("totalRows")-r.childNodes.length+i:r===this.THEAD?i=this.rowFilter.visibleColHeadedRowToSourceRow(i):this.rowFilter&&(i=this.rowFilter.renderedToSource(i)),yg(ab,t,this.wtRootElement)||yg(lb,t,this.wtRootElement)||yg(hb,t,this.wtRootElement)?n=this.columnFilter.offsettedTH(n):this.columnFilter&&(n=this.columnFilter.visibleRowHeadedColumnToSourceColumn(n));const s=this.wtSettings.getSetting("onModifyGetCoordsElement",i,n);return s&&Array.isArray(s)&&([i,n]=s),this.wot.createCellCoords(i,n)}markOversizedRows(){if(this.wtSettings.getSetting("externalRowCalculator"))return;let e=this.TBODY.childNodes.length;const t=e*this.dataAccessObject.stylesHandler.getDefaultRowHeight(),o=jg(this.TBODY)-1,r=this.wot.stylesHandler.areCellsBorderBox(),i=r?Wg:jg,n=r?0:1,s=r?1:0;let l,a,h,c,u;if(t!==o||this.wtSettings.getSetting("fixedRowsBottom"))for(;e;){e-=1,h=this.rowFilter.renderedToSource(e),l=this.getRowHeight(h),c=this.getTrForRow(h),u=c.querySelector("th");const t=0===h?s:0;a=u?i(u):i(c)-n,(!l&&this.dataAccessObject.stylesHandler.getDefaultRowHeight()<a-t||l<a)&&(r||(a+=1),this.dataAccessObject.wtViewport.oversizedRows[h]=a)}}getTrForRow(e){return this.TBODY.childNodes[this.rowFilter.sourceToRendered(e)]}isColumnHeaderRendered(e){if(e>=0)return!1;const t=this.wtSettings.getSetting("rowHeaders").length;return Math.abs(e)<=t}isRowHeaderRendered(e){if(e>=0)return!1;const t=this.wtSettings.getSetting("columnHeaders").length;return Math.abs(e)<=t}isRowBeforeRenderedRows(e){const t=this.getFirstRenderedRow();return e<0&&t<=0?!this.isRowHeaderRendered(e):e<t}isRowAfterRenderedRows(e){return e>this.getLastRenderedRow()}isColumnBeforeRenderedColumns(e){const t=this.getFirstRenderedColumn();return e<0&&t<=0?!this.isColumnHeaderRendered(e):e<t}isColumnAfterRenderedColumns(e){return this.columnFilter&&e>this.getLastRenderedColumn()}isColumnAfterViewport(e){return this.columnFilter&&e>this.getLastVisibleColumn()}isRowAfterViewport(e){return this.rowFilter&&e>this.getLastVisibleRow()}isColumnBeforeViewport(e){return this.columnFilter&&this.columnFilter.sourceToRendered(e)<0&&e>=0}isLastRowFullyVisible(){return this.getLastVisibleRow()===this.getLastRenderedRow()}isLastColumnFullyVisible(){return this.getLastVisibleColumn()===this.getLastRenderedColumn()}allRowsInViewport(){return this.wtSettings.getSetting("totalRows")===this.getVisibleRowsCount()}allColumnsInViewport(){return this.wtSettings.getSetting("totalColumns")===this.getVisibleColumnsCount()}getRowHeight(e){return this.rowUtils.getHeight(e)}getColumnHeaderHeight(e){return this.columnUtils.getHeaderHeight(e)}getColumnWidth(e){return this.columnUtils.getWidth(e)}hasDefinedSize(){return this.hasTableHeight&&this.hasTableWidth}getWidth(){return Bg(this.TABLE)}getHeight(){return Wg(this.TABLE)}getTotalWidth(){const e=Bg(this.hider);return 0!==e?e:this.getWidth()}getTotalHeight(){const e=Wg(this.hider);return 0!==e?e:this.getHeight()}isVisible(){return Ng(this.TABLE)}_modifyRowHeaderWidth(e){let t=Zg(e)?e():null;return Array.isArray(t)?(t=[...t],t[t.length-1]=this._correctRowHeaderWidth(t[t.length-1])):t=this._correctRowHeaderWidth(t),t}_correctRowHeaderWidth(e){let t=e;return"number"!=typeof e&&(t=this.wtSettings.getSetting("defaultColumnWidth")),this.correctHeaderWidth&&(t+=1),t}},Zv={getFirstRenderedRow(){const e=this.getRenderedRowsCount();return 0===e?-1:this.wtSettings.getSetting("totalRows")-e},getFirstVisibleRow(){return this.getFirstRenderedRow()},getFirstPartiallyVisibleRow(){return this.getFirstRenderedRow()},getLastRenderedRow(){return 0===this.getRenderedRowsCount()?-1:this.wtSettings.getSetting("totalRows")-1},getLastVisibleRow(){return this.getLastRenderedRow()},getLastPartiallyVisibleRow(){return this.getLastRenderedRow()},getRenderedRowsCount(){return Math.min(this.wtSettings.getSetting("totalRows"),this.wtSettings.getSetting("fixedRowsBottom"))},getVisibleRowsCount(){return this.getRenderedRowsCount()},getColumnHeadersCount:()=>0};Ff(Zv,"MIXIN_NAME","stickyRowsBottom",{writable:!1,enumerable:!1});const eb=Zv,tb={getFirstRenderedColumn(){return 0===this.getRenderedColumnsCount()?-1:0},getFirstVisibleColumn(){return this.getFirstRenderedColumn()},getFirstPartiallyVisibleColumn(){return this.getFirstRenderedColumn()},getLastRenderedColumn(){return this.getRenderedColumnsCount()-1},getLastVisibleColumn(){return this.getLastRenderedColumn()},getLastPartiallyVisibleColumn(){return this.getLastRenderedColumn()},getRenderedColumnsCount(){return Math.min(this.wtSettings.getSetting("totalColumns"),this.wtSettings.getSetting("fixedColumnsStart"))},getVisibleColumnsCount(){return this.getRenderedColumnsCount()},getRowHeadersCount(){return this.dataAccessObject.rowHeaders.length}};Ff(tb,"MIXIN_NAME","stickyColumnsStart",{writable:!1,enumerable:!1});const ob=tb;class rb extends Jv{constructor(e,t,o,r){super(e,t,o,r,hb)}}Vf(rb,eb),Vf(rb,ob);const ib=rb,nb="top",sb="bottom",lb="inline_start",ab="top_inline_start_corner",hb="bottom_inline_start_corner",cb=[nb,sb,lb,ab,hb],ub=new Map([[nb,`ht_clone_${nb}`],[sb,`ht_clone_${sb}`],[lb,`ht_clone_${lb} ht_clone_left`],[ab,`ht_clone_${ab} ht_clone_top_left_corner`],[hb,`ht_clone_${hb} ht_clone_bottom_left_corner`]]);function db(e,t){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.add(e)}function gb(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function fb(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var mb=new WeakSet;function pb(e){const{wtSettings:t,inlineStartOverlay:o,wtTable:r,wtViewport:i,totalColumns:n,rootWindow:s}=this.dataAccessObject;if(o.mainTableScrollableElement===s){let l=null;if(t.getSetting("rtlMode")){const e=r.TABLE.getBoundingClientRect(),t=this.dataAccessObject.rootWindow.document.documentElement.offsetWidth;l=Math.abs(e.right-t)}else l=Ag(r.wtRootElement).left;const a=Math.abs(Vg(s,s));if(l>a){const t=zg(s);let r=i.getRowHeaderWidth();for(let i=1;i<=n;i++)if(r+=o.sumCellSizes(i-1,i),l+r-a>=t){e=i-2;break}}}return e}function wb(e){const{topOverlay:t,wtTable:o,wtViewport:r,totalRows:i,rootWindow:n}=this.dataAccessObject;if(t.mainTableScrollableElement===n){const s=Ag(o.wtRootElement),l=Pg(n,n);if(s.top>l){const o=jg(n);let a=r.getColumnHeaderHeight();for(let r=1;r<=i;r++)if(a+=t.sumCellSizes(r-1,r),s.top+a-l>=o){e=r-2;break}}}return e}const yb=class{constructor(e){db(this,mb),gb(this,"dataAccessObject",void 0),this.dataAccessObject=e}scrollViewport(e,t,o){if(e.col<0||e.row<0)return!1;const r=this.scrollViewportHorizontally(e.col,t),i=this.scrollViewportVertically(e.row,o);return r||i}scrollViewportHorizontally(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"auto";const{drawn:o,totalColumns:r}=this.dataAccessObject;if(!o)return!1;const i=zf(t);if(e=this.dataAccessObject.wtSettings.getSetting("onBeforeViewportScrollHorizontally",e,i),!Number.isInteger(e)||e<0||e>r)return!1;t=i.value;const{fixedColumnsStart:n,inlineStartOverlay:s}=this.dataAccessObject,l="auto"===t;if(l&&e<n)return!1;const a=this.getFirstVisibleColumn(),h=this.getLastVisibleColumn();let c=!1;return(l&&(e<a||e>h)||!l)&&(c=s.scrollTo(e,l?e>=this.getLastPartiallyVisibleColumn():"end"===t)),c}scrollViewportVertically(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"auto";const{drawn:o,totalRows:r}=this.dataAccessObject;if(!o)return!1;const i=zf(t);if(e=this.dataAccessObject.wtSettings.getSetting("onBeforeViewportScrollVertically",e,i),!Number.isInteger(e)||e<0||e>r)return!1;t=i.value;const{fixedRowsBottom:n,fixedRowsTop:s,topOverlay:l}=this.dataAccessObject,a="auto"===t;if(a&&(e<s||e>r-n-1))return!1;const h=this.getFirstVisibleRow(),c=this.getLastVisibleRow();let u=!1;return(a&&(e<h||e>c)||!a)&&(u=l.scrollTo(e,a?e>=this.getLastPartiallyVisibleRow():"bottom"===t)),u}getFirstVisibleRow(){return this.dataAccessObject.wtTable.getFirstVisibleRow()}getLastVisibleRow(){return fb(mb,this,wb).call(this,this.dataAccessObject.wtTable.getLastVisibleRow())}getFirstPartiallyVisibleRow(){return this.dataAccessObject.wtTable.getFirstPartiallyVisibleRow()}getLastPartiallyVisibleRow(){return fb(mb,this,wb).call(this,this.dataAccessObject.wtTable.getLastPartiallyVisibleRow())}getFirstVisibleColumn(){return this.dataAccessObject.wtTable.getFirstVisibleColumn()}getLastVisibleColumn(){return fb(mb,this,pb).call(this,this.dataAccessObject.wtTable.getLastVisibleColumn())}getFirstPartiallyVisibleColumn(){return this.dataAccessObject.wtTable.getFirstPartiallyVisibleColumn()}getLastPartiallyVisibleColumn(){return fb(mb,this,pb).call(this,this.dataAccessObject.wtTable.getLastPartiallyVisibleColumn())}};function vb(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class bb{get eventManager(){return new sp(this)}constructor(e,t){vb(this,"wtTable",void 0),vb(this,"wtScroll",void 0),vb(this,"wtViewport",void 0),vb(this,"wtOverlays",void 0),vb(this,"selectionManager",void 0),vb(this,"wtEvent",void 0),vb(this,"guid",`wt_${lg()}`),vb(this,"drawInterrupted",!1),vb(this,"drawn",!1),vb(this,"activeOverlayName","master"),vb(this,"domBindings",void 0),vb(this,"wtSettings",void 0),this.domBindings={rootTable:e,rootDocument:e.ownerDocument,rootWindow:e.ownerDocument.defaultView},this.wtSettings=t,this.wtScroll=new yb(this.createScrollDao())}findOriginalHeaders(){const e=[];if(this.wtTable.THEAD.childNodes.length&&this.wtTable.THEAD.childNodes[0].childNodes.length){for(let t=0,o=this.wtTable.THEAD.childNodes[0].childNodes.length;t<o;t++)e.push(this.wtTable.THEAD.childNodes[0].childNodes[t].innerHTML);this.wtSettings.getSetting("columnHeaders").length||this.wtSettings.update("columnHeaders",[function(t,o){Ig(o,e[t])}])}}createCellCoords(e,t){return new Py(e,t,this.wtSettings.getSetting("rtlMode"))}createCellRange(e,t,o){return new jy(e,t,o,this.wtSettings.getSetting("rtlMode"))}draw(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this.drawInterrupted=!1,!this.wtTable.isVisible()||function(e){const t=e.ownerDocument.defaultView;let o=e;for(;o.parentNode;){if("0px"===o.style.height||"0"===o.style.height)return"hidden"===t.getComputedStyle(o).overflow;o=o.parentNode}return!1}(this.wtTable.wtRootElement.parentNode)?this.drawInterrupted=!0:this.wtTable.draw(e),this}getCell(e){if(!(arguments.length>1&&void 0!==arguments[1]&&arguments[1]))return this.wtTable.getCell(e);const t=this.wtSettings.getSetting("totalRows"),o=this.wtSettings.getSetting("fixedRowsTop"),r=this.wtSettings.getSetting("fixedRowsBottom"),i=this.wtSettings.getSetting("fixedColumnsStart");if(e.row<o&&e.col<i)return this.wtOverlays.topInlineStartCornerOverlay.clone.wtTable.getCell(e);if(e.row<o)return this.wtOverlays.topOverlay.clone.wtTable.getCell(e);if(e.col<i&&e.row>=t-r){if(this.wtOverlays.bottomInlineStartCornerOverlay&&this.wtOverlays.bottomInlineStartCornerOverlay.clone)return this.wtOverlays.bottomInlineStartCornerOverlay.clone.wtTable.getCell(e)}else{if(e.col<i)return this.wtOverlays.inlineStartOverlay.clone.wtTable.getCell(e);if(e.row<t&&e.row>=t-r&&this.wtOverlays.bottomOverlay&&this.wtOverlays.bottomOverlay.clone)return this.wtOverlays.bottomOverlay.clone.wtTable.getCell(e)}return this.wtTable.getCell(e)}scrollViewport(e,t,o){return this.wtScroll.scrollViewport(e,t,o)}scrollViewportHorizontally(e,t){return this.wtScroll.scrollViewportHorizontally(e,t)}scrollViewportVertically(e,t){return this.wtScroll.scrollViewportVertically(e,t)}getViewport(){return[this.wtTable.getFirstVisibleRow(),this.wtTable.getFirstVisibleColumn(),this.wtTable.getLastVisibleRow(),this.wtTable.getLastVisibleColumn()]}destroy(){this.wtOverlays.destroy(),this.wtEvent.destroy()}createScrollDao(){const e=this;return{get drawn(){return e.drawn},get topOverlay(){return e.wtOverlays.topOverlay},get inlineStartOverlay(){return e.wtOverlays.inlineStartOverlay},get wtTable(){return e.wtTable},get wtViewport(){return e.wtViewport},get wtSettings(){return e.wtSettings},get rootWindow(){return e.domBindings.rootWindow},get totalRows(){return e.wtSettings.getSetting("totalRows")},get totalColumns(){return e.wtSettings.getSetting("totalColumns")},get fixedRowsTop(){return e.wtSettings.getSetting("fixedRowsTop")},get fixedRowsBottom(){return e.wtSettings.getSetting("fixedRowsBottom")},get fixedColumnsStart(){return e.wtSettings.getSetting("fixedColumnsStart")}}}getTableDao(){const e=this;return{get wot(){return e},get parentTableOffset(){return e.cloneSource.wtTable.tableOffset},get cloneSource(){return e.cloneSource},get workspaceWidth(){return e.wtViewport.getWorkspaceWidth()},get wtViewport(){return e.wtViewport},get wtOverlays(){return e.wtOverlays},get selectionManager(){return e.selectionManager},get stylesHandler(){return e.stylesHandler},get drawn(){return e.drawn},set drawn(t){e.drawn=t},get wtTable(){return e.wtTable},get startColumnRendered(){return e.wtViewport.columnsRenderCalculator.startColumn},get startColumnVisible(){return e.wtViewport.columnsVisibleCalculator.startColumn},get startColumnPartiallyVisible(){return e.wtViewport.columnsPartiallyVisibleCalculator.startColumn},get endColumnRendered(){return e.wtViewport.columnsRenderCalculator.endColumn},get endColumnVisible(){return e.wtViewport.columnsVisibleCalculator.endColumn},get endColumnPartiallyVisible(){return e.wtViewport.columnsPartiallyVisibleCalculator.endColumn},get countColumnsRendered(){return e.wtViewport.columnsRenderCalculator.count},get countColumnsVisible(){return e.wtViewport.columnsVisibleCalculator.count},get startRowRendered(){return e.wtViewport.rowsRenderCalculator.startRow},get startRowVisible(){return e.wtViewport.rowsVisibleCalculator.startRow},get startRowPartiallyVisible(){return e.wtViewport.rowsPartiallyVisibleCalculator.startRow},get endRowRendered(){return e.wtViewport.rowsRenderCalculator.endRow},get endRowVisible(){return e.wtViewport.rowsVisibleCalculator.endRow},get endRowPartiallyVisible(){return e.wtViewport.rowsPartiallyVisibleCalculator.endRow},get countRowsRendered(){return e.wtViewport.rowsRenderCalculator.count},get countRowsVisible(){return e.wtViewport.rowsVisibleCalculator.count},get columnHeaders(){return e.wtSettings.getSetting("columnHeaders")},get rowHeaders(){return e.wtSettings.getSetting("rowHeaders")}}}}function Cb(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Sb extends bb{constructor(e,t,o){super(e,t),Cb(this,"cloneSource",void 0),Cb(this,"cloneOverlay",void 0);const r=this.wtSettings.getSetting("facade",this);this.cloneSource=o.source,this.cloneOverlay=o.overlay,this.stylesHandler=o.stylesHandler,this.wtTable=this.cloneOverlay.createTable(this.getTableDao(),r,this.domBindings,this.wtSettings),this.wtViewport=o.viewport,this.selectionManager=o.selectionManager,this.wtEvent=new rv(r,this.domBindings,this.wtSettings,this.eventManager,this.wtTable,this.selectionManager,o.event),this.findOriginalHeaders()}}function Rb(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Tb{constructor(e,t,o,r,i){Rb(this,"wtSettings",null),Ff(this,"wot",e,{writable:!1}),this.domBindings=i,this.facadeGetter=t,this.wtSettings=r;const{TABLE:n,hider:s,spreader:l,holder:a,wtRootElement:h}=this.wot.wtTable;this.instance=this.wot,this.type=o,this.mainTableScrollableElement=null,this.TABLE=n,this.hider=s,this.spreader=l,this.holder=a,this.wtRootElement=h,this.trimmingContainer=Dg(this.hider.parentNode.parentNode),this.needFullRender=this.shouldBeRendered(),this.clone=this.makeClone()}hasRenderingStateChanged(){return this.needFullRender!==this.shouldBeRendered()}updateStateOfRendering(e){"before"===e&&this.shouldBeRendered()?this.needFullRender=!0:"after"!==e||this.shouldBeRendered()||(this.needFullRender=!1)}shouldBeRendered(){return!0}updateTrimmingContainer(){this.trimmingContainer=Dg(this.hider.parentNode.parentNode)}updateMainScrollableElement(){const{wtTable:e}=this.wot,{rootWindow:t}=this.domBindings;"hidden"===t.getComputedStyle(e.wtRootElement.parentNode).getPropertyValue("overflow")?this.mainTableScrollableElement=this.wot.wtTable.holder:this.mainTableScrollableElement=Lg(e.TABLE)}getRelativeCellPosition(e,t,o){if(!1===this.clone.wtTable.holder.contains(e))return void nm(`The provided element is not a child of the ${this.type} overlay`);const r=this.mainTableScrollableElement===this.domBindings.rootWindow,i=o<this.wtSettings.getSetting("fixedColumnsStart"),n=t<this.wtSettings.getSetting("fixedRowsTop"),s=t>=this.wtSettings.getSetting("totalRows")-this.wtSettings.getSetting("fixedRowsBottom"),l=this.clone.wtTable.spreader,a={start:this.getRelativeStartPosition(l),top:l.offsetTop},h={start:this.getRelativeStartPosition(e),top:e.offsetTop};let c=null;return c=r?this.getRelativeCellPositionWithinWindow(n,i,h,a):this.getRelativeCellPositionWithinHolder(n,s,i,h,a),c}getRelativeStartPosition(e){return this.isRtl()?e.offsetParent.offsetWidth-e.offsetLeft-e.offsetWidth:e.offsetLeft}getRelativeCellPositionWithinWindow(e,t,o,r){const i=this.wot.wtTable.wtRootElement.getBoundingClientRect();let n=0,s=0;if(t){let e=i.left;this.isRtl()&&(e=this.domBindings.rootWindow.innerWidth-(i.left+i.width+Gg())),n=e<=0?-1*e:0}else n=r.start;return s=e?this.clone.wtTable.TABLE.getBoundingClientRect().top-i.top:r.top,{start:o.start+n,top:o.top+s}}getRelativeCellPositionWithinHolder(e,t,o,r,i){const n=this.wot.wtOverlays.inlineStartOverlay.getScrollPosition(),s=this.wot.wtOverlays.topOverlay.getScrollPosition();let l=0,a=0;if(o||(l=n-i.start),t){const e=this.wot.wtTable.wtRootElement.getBoundingClientRect();a=-1*this.clone.wtTable.TABLE.getBoundingClientRect().top+e.top}else e||(a=s-i.top);return{start:r.start-l,top:r.top-a}}makeClone(){if(-1===cb.indexOf(this.type))throw new Error(`Clone type "${this.type}" is not supported.`);const{wtTable:e,wtSettings:t}=this.wot,{rootDocument:o,rootWindow:r}=this.domBindings,i=o.createElement("div"),n=o.createElement("table"),s=e.wtRootElement.parentNode;i.className=`${ub.get(this.type)} handsontable`,i.setAttribute("dir",this.isRtl()?"rtl":"ltr"),i.style.position="absolute",i.style.top=0,i.style.overflow="visible",this.isRtl()?i.style.right=0:i.style.left=0,t.getSetting("ariaTags")&&Tg(i,[["role","presentation"]]),n.className=e.TABLE.className,e.TABLE.getAttribute("role")&&n.setAttribute("role",e.TABLE.getAttribute("role")),i.appendChild(n),s.appendChild(i);const l=this.wtSettings.getSetting("preventOverflow");return!0===l||"horizontal"===l&&this.type===nb||"vertical"===l&&this.type===lb?this.mainTableScrollableElement=r:"hidden"===r.getComputedStyle(s).getPropertyValue("overflow")?this.mainTableScrollableElement=e.holder:this.mainTableScrollableElement=Lg(e.TABLE),new Sb(n,this.wtSettings,{source:this.wot,overlay:this,viewport:this.wot.wtViewport,event:this.wot.wtEvent,selectionManager:this.wot.selectionManager,stylesHandler:this.wot.stylesHandler})}refresh(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.needFullRender){const t=this.clone.cloneSource;t.activeOverlayName=this.clone.wtTable.name,this.clone.draw(e),t.activeOverlayName="master"}}reset(){const e=this.clone.wtTable.holder,t=this.clone.wtTable.hider;[e.style,t.style,e.parentNode.style].forEach((e=>{e.width="",e.height=""}))}isRtl(){return this.wtSettings.getSetting("rtlMode")}destroy(){this.clone.eventManager.destroy()}}class Eb extends Tb{constructor(e,t,o,r,i,n){super(e,t,hb,o,r),this.bottomOverlay=i,this.inlineStartOverlay=n}createTable(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return new ib(...t)}shouldBeRendered(){return this.wtSettings.getSetting("shouldRenderBottomOverlay")&&this.wtSettings.getSetting("shouldRenderInlineStartOverlay")}resetFixedPosition(){const{wot:e}=this;if(this.updateTrimmingContainer(),!e.wtTable.holder.parentNode)return!1;const t=this.clone.wtTable.holder.parentNode;if(t.style.top="",this.trimmingContainer===this.domBindings.rootWindow){const e=this.inlineStartOverlay.getOverlayOffset(),o=this.bottomOverlay.getOverlayOffset();t.style[this.isRtl()?"right":"left"]=`${e}px`,t.style.bottom=`${o}px`}else Kg(t),this.repositionOverlay();let o=Wg(this.clone.wtTable.TABLE);const r=Bg(this.clone.wtTable.TABLE);return this.wot.wtTable.hasDefinedSize()||(o=0),t.style.height=`${o}px`,t.style.width=`${r}px`,!1}repositionOverlay(){const{wtTable:e,wtViewport:t}=this.wot,{rootDocument:o}=this.domBindings,r=this.clone.wtTable.holder.parentNode;let i=0;t.hasVerticalScroll()||(i+=t.getWorkspaceHeight()-e.getTotalHeight()),t.hasVerticalScroll()&&t.hasHorizontalScroll()&&(i+=Gg(o)),r.style.bottom=`${i}px`}}const Hb={getFirstRenderedColumn(){const e=this.dataAccessObject.startColumnRendered;return null===e?-1:e},getFirstVisibleColumn(){const e=this.dataAccessObject.startColumnVisible;return null===e?-1:e},getFirstPartiallyVisibleColumn(){const e=this.dataAccessObject.startColumnPartiallyVisible;return null===e?-1:e},getLastRenderedColumn(){const e=this.dataAccessObject.endColumnRendered;return null===e?-1:e},getLastVisibleColumn(){const e=this.dataAccessObject.endColumnVisible;return null===e?-1:e},getLastPartiallyVisibleColumn(){const e=this.dataAccessObject.endColumnPartiallyVisible;return null===e?-1:e},getRenderedColumnsCount(){return this.dataAccessObject.countColumnsRendered},getVisibleColumnsCount(){return this.dataAccessObject.countColumnsVisible},getRowHeadersCount(){return this.dataAccessObject.rowHeaders.length}};Ff(Hb,"MIXIN_NAME","calculatedColumns",{writable:!1,enumerable:!1});const Ob=Hb;class Mb extends Jv{constructor(e,t,o,r){super(e,t,o,r,sb)}}Vf(Mb,eb),Vf(Mb,Ob);const xb=Mb;function Ib(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class Nb extends Tb{constructor(e,t,o,r){super(e,t,sb,o,r),Ib(this,"cachedFixedRowsBottom",-1),this.cachedFixedRowsBottom=this.wtSettings.getSetting("fixedRowsBottom")}createTable(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return new xb(...t)}shouldBeRendered(){return this.wtSettings.getSetting("shouldRenderBottomOverlay")}resetFixedPosition(){if(!this.needFullRender||!this.shouldBeRendered()||!this.wot.wtTable.holder.parentNode)return!1;const{rootWindow:e}=this.domBindings,t=this.clone.wtTable.holder.parentNode;t.style.top="";let o=0;const r=this.wtSettings.getSetting("preventOverflow");this.trimmingContainer!==e||r&&"vertical"===r?(o=this.getScrollPosition(),this.repositionOverlay()):(o=this.getOverlayOffset(),t.style.bottom=`${o}px`);const i=this.adjustHeaderBordersPosition(o);return this.adjustElementsSize(),i}repositionOverlay(){const{wtTable:e,wtViewport:t}=this.wot,{rootDocument:o}=this.domBindings,r=this.clone.wtTable.holder.parentNode;let i=0;t.hasVerticalScroll()||(i+=t.getWorkspaceHeight()-e.getTotalHeight()),t.hasVerticalScroll()&&t.hasHorizontalScroll()&&(i+=Gg(o)),r.style.bottom=`${i}px`}setScrollPosition(e){const{rootWindow:t}=this.domBindings,o=this.mainTableScrollableElement;let r=!1;if(o===t&&e!==t.scrollY){const e=t.scrollY;t.scrollTo(_g(t),top),r=e!==t.scrollY}else if(e!==o.scrollTop){const t=o.scrollTop;o.scrollTop=e,r=t!==o.scrollTop}return r}onScroll(){this.wtSettings.getSetting("onScrollHorizontally")}sumCellSizes(e,t){const{wtTable:o,stylesHandler:r}=this.wot,i=r.getDefaultRowHeight();let n=e,s=0;for(;n<t;){const e=o.getRowHeight(n);s+=void 0===e?i:e,n+=1}return s}adjustElementsSize(){this.updateTrimmingContainer(),this.needFullRender&&(this.adjustRootElementSize(),this.adjustRootChildrenSize())}adjustRootElementSize(){const{wtTable:e,wtViewport:t}=this.wot,{rootDocument:o,rootWindow:r}=this.domBindings,i=this.clone.wtTable.holder.parentNode.style,n=this.wtSettings.getSetting("preventOverflow");if(this.trimmingContainer!==r||"horizontal"===n){let r=t.getWorkspaceWidth();t.hasVerticalScroll()&&(r-=Gg(o)),r=Math.min(r,e.wtRootElement.scrollWidth),i.width=`${r}px`}else i.width="";this.clone.wtTable.holder.style.width=i.width;let s=Wg(this.clone.wtTable.TABLE);e.hasDefinedSize()||(s=0),i.height=`${s}px`}adjustRootChildrenSize(){const{holder:e}=this.clone.wtTable;this.clone.wtTable.hider.style.width=this.hider.style.width,e.style.width=e.parentNode.style.width,e.style.height=e.parentNode.style.height}applyToDOM(){const e=this.wtSettings.getSetting("totalRows");if("number"==typeof this.wot.wtViewport.rowsRenderCalculator.startPosition)this.spreader.style.top=`${this.wot.wtViewport.rowsRenderCalculator.startPosition}px`;else{if(0!==e)throw new Error("Incorrect value of the rowsRenderCalculator");this.spreader.style.top="0"}this.spreader.style.bottom="",this.needFullRender&&this.syncOverlayOffset()}syncOverlayOffset(){const e=this.isRtl()?"right":"left",{spreader:t}=this.clone.wtTable;"number"==typeof this.wot.wtViewport.columnsRenderCalculator.startPosition?t.style[e]=`${this.wot.wtViewport.columnsRenderCalculator.startPosition}px`:t.style[e]=""}scrollTo(e,t){let o=this.getTableParentOffset();const r=(this.wot.cloneSource?this.wot.cloneSource:this.wot).wtTable.holder;let i=0;t&&r.offsetHeight!==r.clientHeight&&(i=Gg(this.domBindings.rootDocument)),t?(o+=this.sumCellSizes(0,e+1),o-=this.wot.wtViewport.getViewportHeight(),o+=1):o+=this.sumCellSizes(this.wtSettings.getSetting("fixedRowsBottom"),e),o+=i,this.setScrollPosition(o)}getTableParentOffset(){return this.mainTableScrollableElement===this.domBindings.rootWindow?this.wot.wtTable.holderOffset.top:0}getScrollPosition(){return Pg(this.mainTableScrollableElement,this.domBindings.rootWindow)}getOverlayOffset(){const{rootWindow:e}=this.domBindings,t=this.wtSettings.getSetting("preventOverflow");let o=0;if(this.trimmingContainer===e&&(!t||"vertical"!==t)){const e=this.wot.wtTable.getTotalHeight(),t=e-this.clone.wtTable.getTotalHeight(),r=this.domBindings.rootDocument.documentElement.clientHeight;o=Math.max(this.getTableParentOffset()-this.getScrollPosition()-r+e,0),o>t&&(o=0)}return o}adjustHeaderBordersPosition(e){const t=this.wtSettings.getSetting("fixedRowsBottom"),o=this.cachedFixedRowsBottom!==t,r=this.wtSettings.getSetting("columnHeaders");let i=!1;if((o||0===t)&&r.length>0){const t=this.wot.wtTable.holder.parentNode,o=Cg(t,"innerBorderBottom");this.cachedFixedRowsBottom=this.wtSettings.getSetting("fixedRowsBottom"),e||0===this.wtSettings.getSetting("totalRows")?(Sg(t,"innerBorderBottom"),i=!o):(Rg(t,"innerBorderBottom"),i=o)}return i}}const Ab={getFirstRenderedRow(){const e=this.dataAccessObject.startRowRendered;return null===e?-1:e},getFirstVisibleRow(){const e=this.dataAccessObject.startRowVisible;return null===e?-1:e},getFirstPartiallyVisibleRow(){const e=this.dataAccessObject.startRowPartiallyVisible;return null===e?-1:e},getLastRenderedRow(){const e=this.dataAccessObject.endRowRendered;return null===e?-1:e},getLastVisibleRow(){const e=this.dataAccessObject.endRowVisible;return null===e?-1:e},getLastPartiallyVisibleRow(){const e=this.dataAccessObject.endRowPartiallyVisible;return null===e?-1:e},getRenderedRowsCount(){return this.dataAccessObject.countRowsRendered},getVisibleRowsCount(){return this.dataAccessObject.countRowsVisible},getColumnHeadersCount(){return this.dataAccessObject.columnHeaders.length}};Ff(Ab,"MIXIN_NAME","calculatedRows",{writable:!1,enumerable:!1});const kb=Ab;class _b extends Jv{constructor(e,t,o,r){super(e,t,o,r,lb)}}Vf(_b,kb),Vf(_b,ob);const Pb=_b,Vb={_localHooks:Object.create(null),addLocalHook(e,t){return this._localHooks[e]||(this._localHooks[e]=[]),this._localHooks[e].push(t),this},runLocalHooks(e,t,o,r,i,n,s){if(this._localHooks[e]){const l=this._localHooks[e].length;for(let a=0;a<l;a++)ef(this._localHooks[e][a],this,t,o,r,i,n,s)}},clearLocalHooks(){return this._localHooks={},this}};Ff(Vb,"MIXIN_NAME","localHooks",{writable:!1,enumerable:!1});const Lb=Vb;let Db=class{constructor(e,t){this.settings=e,this.cellRange=t||null}isEmpty(){return null===this.cellRange}add(e){return this.isEmpty()?this.cellRange=this.settings.createCellRange(e):this.cellRange.expand(e),this}replace(e,t){if(!this.isEmpty()){if(this.cellRange.from.isEqual(e))return this.cellRange.from=t,!0;if(this.cellRange.to.isEqual(e))return this.cellRange.to=t,!0}return!1}clear(){return this.cellRange=null,this}getCorners(){const e=this.cellRange.getOuterTopStartCorner(),t=this.cellRange.getOuterBottomEndCorner();return[e.row,e.col,t.row,t.col]}destroy(){this.runLocalHooks("destroy")}};Vf(Db,Lb);const Fb=Db,Bb="header",Wb="area",jb="focus",zb=e=>{const t=e.stylesHandler;if(t.isClassicTheme())return Object.freeze({width:6,height:6,borderWidth:1,borderStyle:"solid",borderColor:"#FFF"});const o=t.getCSSVariableValue("cell-autofill-size"),r=t.getCSSVariableValue("cell-autofill-border-width"),i=t.getCSSVariableValue("cell-autofill-border-color");return Object.freeze({width:o,height:o,borderWidth:r,borderStyle:"solid",borderColor:i})};function $b(e,t,o){Ub(e,t),t.set(e,o)}function Ub(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Xb(e,t){return e.get(Yb(e,t))}function Gb(e,t,o){return e.set(Yb(e,t),o),o}function Yb(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}pm("flat");var Kb=new WeakMap,qb=new WeakMap,Qb=new WeakSet;class Jb{constructor(){var e,t;Ub(e=this,t=Qb),t.add(e),$b(this,Kb,void 0),$b(this,qb,void 0)}setActiveOverlay(e){return Gb(qb,this,e),this}setActiveSelection(e){return Gb(Kb,this,e),this}scan(){const e=Xb(Kb,this).settings.selectionType,t=new Set;return"active-header"===e?(this.scanColumnsInHeadersRange((e=>t.add(e))),this.scanRowsInHeadersRange((e=>t.add(e)))):"area"===e?this.scanCellsRange((e=>t.add(e))):"focus"===e?(this.scanColumnsInHeadersRange((e=>t.add(e))),this.scanRowsInHeadersRange((e=>t.add(e))),this.scanCellsRange((e=>t.add(e)))):"fill"===e?this.scanCellsRange((e=>t.add(e))):"header"===e?(this.scanColumnsInHeadersRange((e=>t.add(e))),this.scanRowsInHeadersRange((e=>t.add(e)))):"row"===e?(this.scanRowsInHeadersRange((e=>t.add(e))),this.scanRowsInCellsRange((e=>t.add(e)))):"column"===e&&(this.scanColumnsInHeadersRange((e=>t.add(e))),this.scanColumnsInCellsRange((e=>t.add(e)))),t}scanColumnsInHeadersRange(e){const[t,o,r,i]=Xb(Kb,this).getCorners(),{wtTable:n}=Xb(qb,this),s=n.getRenderedColumnsCount(),l=n.getColumnHeadersCount();let a=0;for(let h=-n.getRowHeadersCount();h<s;h++){const s=n.columnFilter.renderedToSource(h);if(!(s<o||s>i)){for(let h=-l;h<0;h++){if(h<t||h>r)continue;const c=h+l;let u=n.getColumnHeader(s,c);const d=Xb(qb,this).getSetting("onBeforeHighlightingColumnHeader",s,c,{selectionType:Xb(Kb,this).settings.selectionType,columnCursor:a,selectionWidth:i-o+1});null!==d&&(d!==s&&(u=n.getColumnHeader(d,c)),e(u))}a+=1}}}scanRowsInHeadersRange(e){const[t,o,r,i]=Xb(Kb,this).getCorners(),{wtTable:n}=Xb(qb,this),s=n.getRenderedRowsCount(),l=n.getRowHeadersCount();let a=0;for(let h=-n.getColumnHeadersCount();h<s;h++){const s=n.rowFilter.renderedToSource(h);if(!(s<t||s>r)){for(let h=-l;h<0;h++){if(h<o||h>i)continue;const c=h+l;let u=n.getRowHeader(s,c);const d=Xb(qb,this).getSetting("onBeforeHighlightingRowHeader",s,c,{selectionType:Xb(Kb,this).settings.selectionType,rowCursor:a,selectionHeight:r-t+1});null!==d&&(d!==s&&(u=n.getRowHeader(d,c)),e(u))}a+=1}}}scanCellsRange(e){const{wtTable:t}=Xb(qb,this);Yb(Qb,this,Zb).call(this,((o,r)=>{const i=t.getCell(Xb(qb,this).createCellCoords(o,r)),n=Xb(qb,this).getSetting("onAfterDrawSelection",o,r,Xb(Kb,this).settings.layerLevel);"string"==typeof n&&Sg(i,n),e(i)}))}scanRowsInCellsRange(e){const[t,,o]=Xb(Kb,this).getCorners(),{wtTable:r}=Xb(qb,this);Yb(Qb,this,eC).call(this,((i,n)=>{if(i>=t&&i<=o){const t=r.getCell(Xb(qb,this).createCellCoords(i,n));e(t)}}))}scanColumnsInCellsRange(e){const[,t,,o]=Xb(Kb,this).getCorners(),{wtTable:r}=Xb(qb,this);Yb(Qb,this,eC).call(this,((i,n)=>{if(n>=t&&n<=o){const t=r.getCell(Xb(qb,this).createCellCoords(i,n));e(t)}}))}}function Zb(e){let[t,o,r,i]=Xb(Kb,this).getCorners();if(t<0&&r<0||o<0&&i<0)return;const{wtTable:n}=Xb(qb,this),s=t!==r||o!==i;if(o=Math.max(o,0),i=Math.max(i,0),t=Math.max(t,0),r=Math.max(r,0),s){if(o=Math.max(o,n.getFirstRenderedColumn()),i=Math.min(i,n.getLastRenderedColumn()),t=Math.max(t,n.getFirstRenderedRow()),r=Math.min(r,n.getLastRenderedRow()),i<o||r<t)return}else if(!Jg(n.getCell(Xb(qb,this).createCellCoords(t,o))))return;for(let l=t;l<=r;l+=1)for(let t=o;t<=i;t+=1)e(l,t)}function eC(e){const{wtTable:t}=Xb(qb,this),o=t.getRenderedRowsCount(),r=t.getRenderedColumnsCount();for(let i=0;i<o;i+=1){const o=t.rowFilter.renderedToSource(i);for(let i=0;i<r;i+=1)e(o,t.columnFilter.renderedToSource(i))}}const tC=class{constructor(e,t){t&&(this.eventManager=e.eventManager,this.instance=e,this.wot=e,this.settings=t,this.mouseDown=!1,this.main=null,this.top=null,this.bottom=null,this.start=null,this.end=null,this.topStyle=null,this.bottomStyle=null,this.startStyle=null,this.endStyle=null,this.cornerDefaultStyle=zb(this.instance),this.cornerCenterPointOffset=-Math.ceil(parseInt(this.cornerDefaultStyle.width,10)/2),this.corner=null,this.cornerStyle=null,this.createBorders(t),this.registerListeners())}registerListeners(){const e=this.wot.rootDocument.body;this.eventManager.addEventListener(e,"mousedown",(()=>this.onMouseDown())),this.eventManager.addEventListener(e,"mouseup",(()=>this.onMouseUp()));for(let t=0,o=this.main.childNodes.length;t<o;t++){const e=this.main.childNodes[t];this.eventManager.addEventListener(e,"mouseenter",(e=>this.onMouseEnter(e,this.main.childNodes[t])))}}onMouseDown(){this.mouseDown=!0}onMouseUp(){this.mouseDown=!1}onMouseEnter(e,t){if(!this.mouseDown||!this.wot.getSetting("hideBorderOnMouseDownOver"))return;e.preventDefault(),tm(e);const o=this,r=this.wot.rootDocument.body,i=t.getBoundingClientRect();t.style.display="none",this.eventManager.addEventListener(r,"mousemove",(function e(n){var s;((s=n).clientY<Math.floor(i.top)||s.clientY>Math.ceil(i.top+i.height)||s.clientX<Math.floor(i.left)||s.clientX>Math.ceil(i.left+i.width))&&(o.eventManager.removeEventListener(r,"mousemove",e),t.style.display="block")}))}createBorders(e){const{rootDocument:t}=this.wot;this.main=t.createElement("div");const o=["top","start","bottom","end","corner"];let r=this.main.style;r.position="absolute",r.top=0,r.left=0;for(let s=0;s<5;s++){const i=o[s],n=t.createElement("div");n.className=`wtBorder ${this.settings.className||""}`,this.settings[i]&&this.settings[i].hide&&(n.className+=" hidden"),r=n.style,r.backgroundColor=this.settings[i]&&this.settings[i].color?this.settings[i].color:e.border.color,r.height=this.settings[i]&&this.settings[i].width?`${this.settings[i].width}px`:`${e.border.width}px`,r.width=this.settings[i]&&this.settings[i].width?`${this.settings[i].width}px`:`${e.border.width}px`,this.main.appendChild(n)}this.top=this.main.childNodes[0],this.start=this.main.childNodes[1],this.bottom=this.main.childNodes[2],this.end=this.main.childNodes[3],this.topStyle=this.top.style,this.startStyle=this.start.style,this.bottomStyle=this.bottom.style,this.endStyle=this.end.style,this.corner=this.main.childNodes[4],this.corner.className+=" corner",this.cornerStyle=this.corner.style,this.cornerStyle.width=`${this.cornerDefaultStyle.width}px`,this.cornerStyle.height=`${this.cornerDefaultStyle.height}px`,this.cornerStyle.border=[`${this.cornerDefaultStyle.borderWidth}px`,this.cornerDefaultStyle.borderStyle,this.cornerDefaultStyle.borderColor].join(" "),qf()&&this.instance.getSetting("isDataViewInstance")&&this.createMultipleSelectorHandles(),this.disappear();const{wtTable:i}=this.wot;let n=i.bordersHolder;n||(n=t.createElement("div"),n.className="htBorders",i.bordersHolder=n,i.spreader.appendChild(n)),n.appendChild(this.main)}createMultipleSelectorHandles(){const{rootDocument:e,stylesHandler:t}=this.wot,o=t.getCSSVariableValue("cell-mobile-handle-size"),r=t.getCSSVariableValue("cell-mobile-handle-border-radius"),i=t.getCSSVariableValue("cell-mobile-handle-background-color"),n=t.getCSSVariableValue("cell-mobile-handle-border-width"),s=t.getCSSVariableValue("cell-mobile-handle-border-color");this.selectionHandles={top:e.createElement("DIV"),topHitArea:e.createElement("DIV"),bottom:e.createElement("DIV"),bottomHitArea:e.createElement("DIV")},this.selectionHandles.top.className="topSelectionHandle topLeftSelectionHandle",this.selectionHandles.topHitArea.className="topSelectionHandle-HitArea topLeftSelectionHandle-HitArea",this.selectionHandles.bottom.className="bottomSelectionHandle bottomRightSelectionHandle",this.selectionHandles.bottomHitArea.className="bottomSelectionHandle-HitArea bottomRightSelectionHandle-HitArea",this.selectionHandles.styles={top:this.selectionHandles.top.style,topHitArea:this.selectionHandles.topHitArea.style,bottom:this.selectionHandles.bottom.style,bottomHitArea:this.selectionHandles.bottomHitArea.style},Bf({position:"absolute",height:"40px",width:"40px","border-radius":`${parseInt(40/1.5,10)}px`},((e,t)=>{this.selectionHandles.styles.bottomHitArea[t]=e,this.selectionHandles.styles.topHitArea[t]=e})),Bf(t.isClassicTheme()?{position:"absolute",height:"10px",width:"10px","border-radius":`${parseInt(10/1.5,10)}px`,background:"#F5F5FF",border:"1px solid #4285c8"}:{position:"absolute",height:`${o}px`,width:`${o}px`,"border-radius":`${r}px`,background:`${i}`,border:`${n}px solid ${s}`},((e,t)=>{this.selectionHandles.styles.bottom[t]=e,this.selectionHandles.styles.top[t]=e})),this.main.appendChild(this.selectionHandles.top),this.main.appendChild(this.selectionHandles.bottom),this.main.appendChild(this.selectionHandles.topHitArea),this.main.appendChild(this.selectionHandles.bottomHitArea)}isPartRange(e,t){const o=this.wot.selectionManager.getAreaSelection();return!(!o.cellRange||e===o.cellRange.to.row&&t===o.cellRange.to.col)}updateMultipleSelectionHandlesPosition(e,t,o,r,i,n){const s=this.wot.wtSettings.getSetting("rtlMode")?"right":"left",{top:l,topHitArea:a,bottom:h,bottomHitArea:c}=this.selectionHandles.styles,u=parseInt(l.borderWidth,10),d=parseInt(l.width,10),g=parseInt(a.width,10),f=this.wot.wtTable.getWidth(),m=this.wot.wtTable.getHeight();l.top=`${parseInt(o-d-1,10)}px`,l[s]=`${parseInt(r-d-1,10)}px`,a.top=`${parseInt(o-g/4*3,10)}px`,a[s]=`${parseInt(r-g/4*3,10)}px`;const p=Math.min(parseInt(r+i,10),f-d-2*u),w=Math.min(parseInt(r+i-g/4,10),f-g-2*u);h[s]=`${p}px`,c[s]=`${w}px`;const y=Math.min(parseInt(o+n,10),m-d-2*u),v=Math.min(parseInt(o+n-g/4,10),m-g-2*u);h.top=`${y}px`,c.top=`${v}px`,this.settings.border.cornerVisible&&this.settings.border.cornerVisible()?(l.display="block",a.display="block",this.isPartRange(e,t)?(h.display="none",c.display="none"):(h.display="block",c.display="block")):(l.display="none",h.display="none",a.display="none",c.display="none"),e===this.wot.wtSettings.getSetting("fixedRowsTop")||t===this.wot.wtSettings.getSetting("fixedColumnsStart")?(l.zIndex="9999",a.zIndex="9999"):(l.zIndex="",a.zIndex="")}appear(e){if(this.disabled)return;let[t,o,r,i]=e;if(t<0&&r<0||o<0&&i<0)return void this.disappear();const{wtTable:n,rootDocument:s,rootWindow:l}=this.wot,a=t!==r||o!==i,h=n.getFirstRenderedRow(),c=n.getLastRenderedRow(),u=n.getFirstRenderedColumn(),d=n.getLastRenderedColumn();if(u<0&&d<0||h<0&&c<0)return void this.disappear();let g;if(a){if(o=Math.max(o,u),i=Math.min(i,d),t=Math.max(t,h),r=Math.min(r,c),i<o||r<t)return void this.disappear();g=n.getCell(this.wot.createCellCoords(t,o))}else if(g=n.getCell(this.wot.createCellCoords(t,o)),!Jg(g))return void this.disappear();const f=a?n.getCell(this.wot.createCellCoords(r,i)):g,m=Ag(g),p=a?Ag(f):m,w=Ag(n.TABLE),y=m.top,v=m.left,b=this.wot.wtSettings.getSetting("rtlMode");let C=0,S=0;if(b){const e=Bg(n.TABLE),t=Bg(g),o=l.innerWidth-w.left-e;S=v+t-p.left,C=l.innerWidth-v-t-o-1}else S=p.left+Bg(f)-v,C=v-w.left-1;if(this.isEntireColumnSelected(t,r)){const e=t,r=this.getDimensionsFromHeader("columns",o,i,e,w);let n=null;r&&([n,C,S]=r),n&&(g=n)}let R=y-w.top-1,T=p.top+Wg(f)-y;if(this.isEntireRowSelected(o,i)){const e=o,i=this.getDimensionsFromHeader("rows",t,r,e,w);let n=null;i&&([n,R,T]=i),n&&(g=n)}const E=l.getComputedStyle(g);parseInt(E.borderTopWidth,10)>0&&(R+=1,T=T>0?T-1:0),parseInt(E[b?"borderRightWidth":"borderLeftWidth"],10)>0&&(C+=1,S=S>0?S-1:0);const H=b?"right":"left";this.topStyle.top=`${R}px`,this.topStyle[H]=`${C}px`,this.topStyle.width=`${S}px`,this.topStyle.display="block",this.startStyle.top=`${R}px`,this.startStyle[H]=`${C}px`,this.startStyle.height=`${T}px`,this.startStyle.display="block";const O=Math.floor(this.settings.border.width/2);this.bottomStyle.top=R+T-O+"px",this.bottomStyle[H]=`${C}px`,this.bottomStyle.width=`${S}px`,this.bottomStyle.display="block",this.endStyle.top=`${R}px`,this.endStyle[H]=C+S-O+"px",this.endStyle.height=`${T+1}px`,this.endStyle.display="block";let M=this.settings.border.cornerVisible;M="function"==typeof M?M(this.settings.layerLevel):M;const x=this.wot.getSetting("onModifyGetCellCoords",r,i,!1,"render");let[I,N]=[r,i];if(x&&Array.isArray(x)&&([,,I,N]=x),qf()||!M||this.isPartRange(I,N))this.cornerStyle.display="none";else{this.cornerStyle.top=R+T+this.cornerCenterPointOffset-this.cornerDefaultStyle.borderWidth+"px",this.cornerStyle[H]=C+S+this.cornerCenterPointOffset-this.cornerDefaultStyle.borderWidth+"px",this.cornerStyle.borderRightWidth=`${this.cornerDefaultStyle.borderWidth}px`,this.cornerStyle.borderLeftWidth=`${this.cornerDefaultStyle.borderWidth}px`,this.cornerStyle.borderBottomWidth=`${this.cornerDefaultStyle.borderWidth}px`,this.cornerStyle.width=this.cornerDefaultStyle.width,this.cornerStyle.display="none";let e=Dg(n.TABLE);const t=e===l;t&&(e=s.documentElement);const o=parseInt(this.cornerDefaultStyle.borderWidth,10)-1,a=Math.ceil(parseInt(this.cornerDefaultStyle.width,10)/2),h=Math.ceil(parseInt(this.cornerDefaultStyle.height,10)/2);if(i===this.wot.getSetting("totalColumns")-1){const r=t?f.getBoundingClientRect().left:f.offsetLeft;let i=!1,n=0;b?(n=r-parseInt(this.cornerDefaultStyle.width,10)/2,i=n<0):(n=r+Bg(f)+parseInt(this.cornerDefaultStyle.width,10)/2,i=n>=zg(e)),i&&(this.cornerStyle[H]=`${Math.floor(C+S+this.cornerCenterPointOffset-a-o)}px`,this.cornerStyle[b?"borderLeftWidth":"borderRightWidth"]=0)}if(r===this.wot.getSetting("totalRows")-1){const r=(t?f.getBoundingClientRect().top:f.offsetTop)+Wg(f)+parseInt(this.cornerDefaultStyle.height,10)/2>=jg(e),i=this.wot.stylesHandler.isClassicTheme();if(r){const e=Math.floor(R+T+this.cornerCenterPointOffset-h-o);i?(this.cornerStyle.top=`${e}px`,this.cornerStyle.borderBottomWidth=0):this.cornerStyle.top=e-1+"px"}}this.cornerStyle.display="block"}qf()&&this.instance.getSetting("isDataViewInstance")&&this.updateMultipleSelectionHandlesPosition(r,i,R,C,S,T)}isEntireColumnSelected(e,t){return e===this.wot.wtTable.getFirstRenderedRow()&&t===this.wot.wtTable.getLastRenderedRow()}isEntireRowSelected(e,t){return e===this.wot.wtTable.getFirstRenderedColumn()&&t===this.wot.wtTable.getLastRenderedColumn()}getDimensionsFromHeader(e,t,o,r,i){const{wtTable:n}=this.wot,s=n.wtRootElement.parentNode;let l=null,a=null,h=null,c=null,u=null,d=null,g=null,f=null;switch(e){case"rows":l=function(){return n.getRowHeader(...arguments)},a=function(){return Wg(...arguments)},h="ht__selection--rows",d="top";break;case"columns":l=function(){return n.getColumnHeader(...arguments)},a=function(){return Bg(...arguments)},h="ht__selection--columns",d="left"}if(s.classList.contains(h)){const e=this.wot.getSetting("columnHeaders").length;if(g=l(t,e-r),f=l(o,e-r),!g||!f)return!1;const n=Ag(g),s=Ag(f);return g&&f&&(c=n[d]-i[d]-1,u=s[d]+a(f)-n[d]),[g,c,u]}return!1}changeBorderStyle(e,t){const o=this[e].style,r=t[e];!r||r.hide?Sg(this[e],"hidden"):(Cg(this[e],"hidden")&&Rg(this[e],"hidden"),o.backgroundColor=r.color,"top"!==e&&"bottom"!==e||(o.height=`${r.width}px`),"start"!==e&&"end"!==e||(o.width=`${r.width}px`))}changeBorderToDefaultStyle(e){const t=1,o="#000",r=this[e].style;r.backgroundColor=o,r.width=`${t}px`,r.height=`${t}px`}toggleHiddenClass(e,t){this.changeBorderToDefaultStyle(e),t?Sg(this[e],"hidden"):Rg(this[e],"hidden")}disappear(){this.topStyle.display="none",this.bottomStyle.display="none",this.startStyle.display="none",this.endStyle.display="none",this.cornerStyle.display="none",qf()&&this.instance.getSetting("isDataViewInstance")&&(this.selectionHandles.styles.top.display="none",this.selectionHandles.styles.topHitArea.display="none",this.selectionHandles.styles.bottom.display="none",this.selectionHandles.styles.bottomHitArea.display="none")}destroy(){this.eventManager.destroyWithOwnEventsOnly(),this.main.parentNode.removeChild(this.main)}};function oC(e,t,o){rC(e,t),t.set(e,o)}function rC(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function iC(e,t){return e.get(sC(e,t))}function nC(e,t,o){return e.set(sC(e,t),o),o}function sC(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var lC=new WeakMap,aC=new WeakMap,hC=new WeakMap,cC=new WeakMap,uC=new WeakMap,dC=new WeakMap,gC=new WeakSet;class fC{constructor(e){var t,o;rC(t=this,o=gC),o.add(t),oC(this,lC,void 0),oC(this,aC,void 0),oC(this,hC,new Jb),oC(this,cC,new WeakMap),oC(this,uC,new WeakSet),oC(this,dC,new Map),nC(aC,this,e)}setActiveOverlay(e){return nC(lC,this,e),iC(hC,this).setActiveOverlay(iC(lC,this)),iC(cC,this).has(iC(lC,this))||iC(cC,this).set(iC(lC,this),new Set),this}getFocusSelection(){return null!==iC(aC,this)?iC(aC,this).getFocus():null}getAreaSelection(){return null!==iC(aC,this)?iC(aC,this).createLayeredArea():null}getBorderInstance(e){if(!e.settings.border)return null;if(iC(dC,this).has(e)){const t=iC(dC,this).get(e);if(t.has(iC(lC,this)))return t.get(iC(lC,this));const o=new tC(iC(lC,this),e.settings);return t.set(iC(lC,this),o),o}const t=new tC(iC(lC,this),e.settings);return iC(dC,this).set(e,new Map([[iC(lC,this),t]])),t}getBorderInstances(e){var t,o;return Array.from(null!==(t=null===(o=iC(dC,this).get(e))||void 0===o?void 0:o.values())&&void 0!==t?t:[])}destroyBorders(e){iC(dC,this).get(e).forEach((e=>e.destroy())),iC(dC,this).delete(e)}render(e){if(null===iC(aC,this))return;e&&sC(gC,this,mC).call(this);const t=Array.from(iC(aC,this)),o=new Map,r=new Map;for(let i=0;i<t.length;i++){const e=t[i],{className:n,headerAttributes:s,createLayers:l,selectionType:a}=e.settings;iC(uC,this).has(e)||(iC(uC,this).add(e),e.addLocalHook("destroy",(()=>this.destroyBorders(e))));const h=this.getBorderInstance(e);if(e.isEmpty()){null==h||h.disappear();continue}n&&iC(hC,this).setActiveSelection(e).scan().forEach((e=>{if(o.has(e)){const t=o.get(e);t.has(n)&&!0===l?t.set(n,t.get(n)+1):t.set(n,1)}else o.set(e,new Map([[n,1]]));s&&(r.has(e)||r.set(e,[]),"TH"===e.nodeName&&r.get(e).push(...s))}));const c=e.getCorners();iC(lC,this).getSetting("onBeforeDrawBorders",c,a),null==h||h.appear(c)}o.forEach(((e,t)=>{var o;const r=Array.from(e).map((e=>{let[t,o]=e;return 1===o?t:[t,...Array.from({length:o-1},((e,o)=>`${t}-${o+1}`))]})).flat();r.forEach((e=>iC(cC,this).get(iC(lC,this)).add(e))),Sg(t,r),"TD"===t.nodeName&&Array.isArray(null===(o=iC(aC,this).options)||void 0===o?void 0:o.cellAttributes)&&Tg(t,iC(aC,this).options.cellAttributes)})),Array.from(r.keys()).forEach((e=>{Tg(e,[...r.get(e)])}))}}function mC(){const e=iC(cC,this).get(iC(lC,this)),t=iC(lC,this).wtSettings.getSetting("onBeforeRemoveCellClassNames");if(Array.isArray(t))for(let o=0;o<t.length;o++)e.add(t[o]);e.forEach((e=>{var t,o;const r=iC(lC,this).wtTable.TABLE.querySelectorAll(`.${e}`);let i=[];Array.isArray(null===(t=iC(aC,this).options)||void 0===t?void 0:t.cellAttributes)&&(i=iC(aC,this).options.cellAttributes.map((e=>e[0]))),Array.isArray(null===(o=iC(aC,this).options)||void 0===o?void 0:o.headerAttributes)&&(i=[...i,...iC(aC,this).options.headerAttributes.map((e=>e[0]))]);for(let n=0,s=r.length;n<s;n++)Rg(r[n],e),Eg(r[n],i)})),e.clear()}class pC extends Tb{constructor(e,t,o,r){super(e,t,lb,o,r)}createTable(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return new Pb(...t)}shouldBeRendered(){return this.wtSettings.getSetting("shouldRenderInlineStartOverlay")}resetFixedPosition(){const{wtTable:e}=this.wot;if(!this.needFullRender||!this.shouldBeRendered()||!e.holder.parentNode)return!1;const{rootWindow:t}=this.domBindings,o=this.clone.wtTable.holder.parentNode,r=this.wtSettings.getSetting("preventOverflow");let i=0;this.trimmingContainer!==t||r&&"horizontal"===r?(i=this.getScrollPosition(),Kg(o)):(i=this.getOverlayOffset()*(this.isRtl()?-1:1),Yg(o,`${i}px`,"0px"));const n=this.adjustHeaderBordersPosition(i);return this.adjustElementsSize(),n}setScrollPosition(e){const{rootWindow:t}=this.domBindings;let o=!1;this.isRtl()&&(e=-e);const r=this.mainTableScrollableElement;if(r===t&&e!==t.scrollX){const r=t.scrollX;t.scrollTo(e,kg(t)),o=r!==t.scrollX}else if(e!==r.scrollLeft){const t=r.scrollLeft;r.scrollLeft=e,o=t!==r.scrollLeft}return o}onScroll(){this.wtSettings.getSetting("onScrollVertically")}sumCellSizes(e,t){const o=this.wtSettings.getSetting("defaultColumnWidth");let r=e,i=0;for(;r<t;)i+=this.wot.wtTable.getColumnWidth(r)||o,r+=1;return i}adjustElementsSize(){this.updateTrimmingContainer(),this.needFullRender&&(this.adjustRootElementSize(),this.adjustRootChildrenSize())}adjustRootElementSize(){const{wtTable:e,wtViewport:t}=this.wot,{rootDocument:o,rootWindow:r}=this.domBindings,i=this.clone.wtTable.holder.parentNode.style,n=this.wtSettings.getSetting("preventOverflow");if(this.trimmingContainer!==r||"vertical"===n){let r=t.getWorkspaceHeight();t.hasHorizontalScroll()&&(r-=Gg(o)),r=Math.min(r,e.wtRootElement.scrollHeight),i.height=`${r}px`}else i.height="";this.clone.wtTable.holder.style.height=i.height;const s=Bg(this.clone.wtTable.TABLE);i.width=`${s}px`}adjustRootChildrenSize(){const{holder:e}=this.clone.wtTable,t=zb(this.wot),o=this.wot.selectionManager.getFocusSelection()?parseInt(t.width,10)/2:0;this.clone.wtTable.hider.style.height=this.hider.style.height,e.style.height=e.parentNode.style.height,e.style.width=`${parseInt(e.parentNode.style.width,10)+o}px`}applyToDOM(){const e=this.wtSettings.getSetting("totalColumns"),t=this.isRtl()?"right":"left";if("number"==typeof this.wot.wtViewport.columnsRenderCalculator.startPosition)this.spreader.style[t]=`${this.wot.wtViewport.columnsRenderCalculator.startPosition}px`;else{if(0!==e)throw new Error("Incorrect value of the columnsRenderCalculator");this.spreader.style[t]="0"}this.isRtl()?this.spreader.style.left="":this.spreader.style.right="",this.needFullRender&&this.syncOverlayOffset()}syncOverlayOffset(){"number"==typeof this.wot.wtViewport.rowsRenderCalculator.startPosition?this.clone.wtTable.spreader.style.top=`${this.wot.wtViewport.rowsRenderCalculator.startPosition}px`:this.clone.wtTable.spreader.style.top=""}scrollTo(e,t){const{wtSettings:o}=this,r=o.getSetting("rowHeaders"),i=o.getSetting("fixedColumnsStart"),n=(this.wot.cloneSource?this.wot.cloneSource:this.wot).wtTable.holder,s=0===i&&r.length>0&&!Cg(n.parentNode,"innerBorderInlineStart")?1:0;let l=this.getTableParentOffset(),a=0;var h;return t&&this.wot.wtTable.getColumnWidth(e)>this.wot.wtViewport.getViewportWidth()&&(t=!1),t&&n.offsetWidth!==n.clientWidth&&(a=Gg(this.domBindings.rootDocument)),t?(l+=this.sumCellSizes(0,e+1),l-=this.wot.wtViewport.getViewportWidth(),l+=s):l+=this.sumCellSizes(this.wtSettings.getSetting("fixedColumnsStart"),e),l+=a,(h=this.mainTableScrollableElement).scrollWidth-h.clientWidth===l-s&&s>0&&this.wot.wtOverlays.expandHiderHorizontallyBy(s),this.setScrollPosition(l)}getTableParentOffset(){let e=0;return this.wtSettings.getSetting("preventOverflow")||this.trimmingContainer!==this.domBindings.rootWindow||(e=this.wot.wtTable.holderOffset.left),e}getScrollPosition(){return Math.abs(Vg(this.mainTableScrollableElement,this.domBindings.rootWindow))}getOverlayOffset(){const{rootWindow:e}=this.domBindings,t=this.wtSettings.getSetting("preventOverflow");let o=0;return this.trimmingContainer!==e||t&&"horizontal"===t||(o=this.isRtl()?Math.abs(Math.min(this.getTableParentOffset()-this.getScrollPosition(),0)):Math.max(this.getScrollPosition()-this.getTableParentOffset(),0),o>this.wot.wtTable.getTotalWidth()-this.clone.wtTable.getTotalWidth()&&(o=0)),o}adjustHeaderBordersPosition(e){const{wtSettings:t}=this,o=this.wot.wtTable.holder.parentNode,r=t.getSetting("rowHeaders"),i=t.getSetting("fixedColumnsStart"),n=t.getSetting("totalRows"),s="vertical"===t.getSetting("preventOverflow");n?Rg(o,"emptyRows"):Sg(o,"emptyRows");let l=!1;if(!s)if(i&&!r.length)Sg(o,"innerBorderLeft innerBorderInlineStart");else if(!i&&r.length){const t=Cg(o,"innerBorderInlineStart");e?(Sg(o,"innerBorderLeft innerBorderInlineStart"),l=!t):(Rg(o,"innerBorderLeft innerBorderInlineStart"),l=t)}return l}}const wC={getFirstRenderedRow(){return 0===this.getRenderedRowsCount()?-1:0},getFirstVisibleRow(){return this.getFirstRenderedRow()},getFirstPartiallyVisibleRow(){return this.getFirstRenderedRow()},getLastRenderedRow(){return this.getRenderedRowsCount()-1},getLastVisibleRow(){return this.getLastRenderedRow()},getLastPartiallyVisibleRow(){return this.getLastRenderedRow()},getRenderedRowsCount(){return Math.min(this.wtSettings.getSetting("totalRows"),this.wtSettings.getSetting("fixedRowsTop"))},getVisibleRowsCount(){return this.getRenderedRowsCount()},getColumnHeadersCount(){return this.dataAccessObject.columnHeaders.length}};Ff(wC,"MIXIN_NAME","stickyRowsTop",{writable:!1,enumerable:!1});const yC=wC;class vC extends Jv{constructor(e,t,o,r){super(e,t,o,r,ab)}}Vf(vC,yC),Vf(vC,ob);const bC=vC;function CC(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class SC extends Tb{constructor(e,t,o,r,i,n){super(e,t,ab,o,r),CC(this,"topOverlay",void 0),CC(this,"inlineStartOverlay",void 0),this.topOverlay=i,this.inlineStartOverlay=n}createTable(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return new bC(...t)}shouldBeRendered(){return this.wtSettings.getSetting("shouldRenderTopOverlay")&&this.wtSettings.getSetting("shouldRenderInlineStartOverlay")}resetFixedPosition(){if(this.updateTrimmingContainer(),!this.wot.wtTable.holder.parentNode)return!1;const e=this.clone.wtTable.holder.parentNode;this.trimmingContainer===this.domBindings.rootWindow?Yg(e,this.inlineStartOverlay.getOverlayOffset()*(this.isRtl()?-1:1)+"px",`${this.topOverlay.getOverlayOffset()}px`):Kg(e);let t=Wg(this.clone.wtTable.TABLE);const o=Bg(this.clone.wtTable.TABLE);return this.wot.wtTable.hasDefinedSize()||(t=0),e.style.height=`${t}px`,e.style.width=`${o}px`,!1}}class RC extends Jv{constructor(e,t,o,r){super(e,t,o,r,nb)}}Vf(RC,yC),Vf(RC,Ob);const TC=RC;function EC(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class HC extends Tb{constructor(e,t,o,r){super(e,t,nb,o,r),EC(this,"cachedFixedRowsTop",-1),this.cachedFixedRowsTop=this.wtSettings.getSetting("fixedRowsTop")}createTable(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return new TC(...t)}shouldBeRendered(){return this.wtSettings.getSetting("shouldRenderTopOverlay")}resetFixedPosition(){if(!this.needFullRender||!this.shouldBeRendered()||!this.wot.wtTable.holder.parentNode)return!1;const e=this.clone.wtTable.holder.parentNode,{rootWindow:t}=this.domBindings,o=this.wtSettings.getSetting("preventOverflow");let r=0,i=!1;if(this.trimmingContainer!==t||o&&"vertical"===o)r=this.getScrollPosition(),Kg(e);else{const{wtTable:t}=this.wot,o=t.hider.getBoundingClientRect();i=Math.ceil(o.bottom)===e.offsetHeight,r=this.getOverlayOffset(),Yg(e,"0px",`${r}px`)}const n=this.adjustHeaderBordersPosition(r,i);return this.adjustElementsSize(),n}setScrollPosition(e){const{rootWindow:t}=this.domBindings,o=this.mainTableScrollableElement;let r=!1;if(o===t&&e!==t.scrollY){const o=t.scrollY;t.scrollTo(_g(t),e),r=o!==t.scrollY}else if(e!==o.scrollTop){const t=o.scrollTop;o.scrollTop=e,r=t!==o.scrollTop}return r}onScroll(){this.wtSettings.getSetting("onScrollHorizontally")}sumCellSizes(e,t){const o=this.wot.stylesHandler.getDefaultRowHeight();let r=e,i=0;for(;r<t;){const e=this.wot.wtTable.getRowHeight(r);i+=void 0===e?o:e,r+=1}return i}adjustElementsSize(){this.updateTrimmingContainer(),this.needFullRender&&(this.adjustRootElementSize(),this.adjustRootChildrenSize())}adjustRootElementSize(){const{wtTable:e,wtViewport:t}=this.wot,{rootDocument:o,rootWindow:r}=this.domBindings,i=this.clone.wtTable.holder.parentNode.style,n=this.wtSettings.getSetting("preventOverflow");if(this.trimmingContainer!==r||"horizontal"===n){let r=t.getWorkspaceWidth();t.hasVerticalScroll()&&(r-=Gg(o)),r=Math.min(r,e.wtRootElement.scrollWidth),i.width=`${r}px`}else i.width="";this.clone.wtTable.holder.style.width=i.width;let s=Wg(this.clone.wtTable.TABLE);e.hasDefinedSize()||(s=0),i.height=`${s}px`}adjustRootChildrenSize(){const{holder:e}=this.clone.wtTable,t=zb(this.wot),o=this.wot.selectionManager.getFocusSelection()?parseInt(t.height,10)/2:0;this.clone.wtTable.hider.style.width=this.hider.style.width,e.style.width=e.parentNode.style.width,e.style.height=`${parseInt(e.parentNode.style.height,10)+o}px`}applyToDOM(){const e=this.wtSettings.getSetting("totalRows");if("number"==typeof this.wot.wtViewport.rowsRenderCalculator.startPosition)this.spreader.style.top=`${this.wot.wtViewport.rowsRenderCalculator.startPosition}px`;else{if(0!==e)throw new Error("Incorrect value of the rowsRenderCalculator");this.spreader.style.top="0"}this.spreader.style.bottom="",this.needFullRender&&this.syncOverlayOffset()}syncOverlayOffset(){const e=this.isRtl()?"right":"left",{spreader:t}=this.clone.wtTable;"number"==typeof this.wot.wtViewport.columnsRenderCalculator.startPosition?t.style[e]=`${this.wot.wtViewport.columnsRenderCalculator.startPosition}px`:t.style[e]=""}scrollTo(e,t){const{wot:o,wtSettings:r}=this,i=(o.cloneSource?o.cloneSource:o).wtTable.holder,n=r.getSetting("columnHeaders"),s=0===r.getSetting("fixedRowsTop")&&n.length>0&&!Cg(i.parentNode,"innerBorderTop")?1:0;let l=this.getTableParentOffset(),a=0;if(t&&this.wot.wtTable.getRowHeight(e)>this.wot.wtViewport.getViewportHeight()&&(t=!1),t&&i.offsetHeight!==i.clientHeight&&(a=Gg(this.domBindings.rootDocument)),t){const t=r.getSetting("fixedRowsBottom"),i=r.getSetting("totalRows");l+=this.sumCellSizes(0,e+1),l-=o.wtViewport.getViewportHeight()-this.sumCellSizes(i-t,i),l+=1,l+=s}else l+=this.sumCellSizes(r.getSetting("fixedRowsTop"),e);var h;return l+=a,(h=this.mainTableScrollableElement).scrollHeight-h.clientHeight===l-s&&s>0&&this.wot.wtOverlays.expandHiderVerticallyBy(s),this.setScrollPosition(l)}getTableParentOffset(){return this.mainTableScrollableElement===this.domBindings.rootWindow?this.wot.wtTable.holderOffset.top:0}getScrollPosition(){return Pg(this.mainTableScrollableElement,this.domBindings.rootWindow)}getOverlayOffset(){const{rootWindow:e}=this.domBindings,t=this.wtSettings.getSetting("preventOverflow");let o=0;if(this.trimmingContainer===e&&(!t||"vertical"!==t)){const e=this.wot.wtTable.getTotalHeight()-this.clone.wtTable.getTotalHeight();o=Math.max(this.getScrollPosition()-this.getTableParentOffset(),0),o>e&&(o=0)}return o}adjustHeaderBordersPosition(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const{wtSettings:o}=this,r=this.wot.wtTable.holder.parentNode,i=o.getSetting("totalColumns"),n="horizontal"===o.getSetting("preventOverflow");i?Rg(r,"emptyColumns"):Sg(r,"emptyColumns");let s=!1;if(!t&&!n){const t=o.getSetting("fixedRowsTop"),i=this.cachedFixedRowsTop!==t,n=o.getSetting("columnHeaders");if((i||0===t)&&n.length>0){const t=Cg(r,"innerBorderTop");this.cachedFixedRowsTop=o.getSetting("fixedRowsTop"),e||0===o.getSetting("totalRows")?(Sg(r,"innerBorderTop"),s=!t):(Rg(r,"innerBorderTop"),s=t)}}return s}}function OC(e,t,o){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,o)}function MC(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function xC(e,t,o){return e.set(NC(e,t),o),o}function IC(e,t){return e.get(NC(e,t))}function NC(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var AC=new WeakMap,kC=new WeakMap,_C=new WeakMap,PC=new WeakMap;const VC=class{constructor(e,t,o,r,i,n){MC(this,"wot",null),OC(this,AC,[]),MC(this,"topOverlay",null),MC(this,"bottomOverlay",null),MC(this,"inlineStartOverlay",null),MC(this,"topInlineStartCornerOverlay",null),MC(this,"bottomInlineStartCornerOverlay",null),MC(this,"browserLineHeight",void 0),MC(this,"wtSettings",null),OC(this,kC,!1),OC(this,_C,0),OC(this,PC,null),MC(this,"resizeObserver",new ResizeObserver((e=>{Uf((()=>{Array.isArray(e)&&e.length&&(xC(_C,this,IC(_C,this)+1),100===IC(_C,this)&&(nm("The ResizeObserver callback was fired too many times in direct succession.\nThis may be due to an infinite loop caused by setting a dynamic height/width (for example, with the `dvh` units) to a Handsontable container's parent. \nThe observer will be disconnected."),this.resizeObserver.disconnect()),null!==IC(PC,this)&&clearTimeout(IC(PC,this)),xC(PC,this,setTimeout((()=>{xC(_C,this,0)}),100)),this.wtSettings.getSetting("onContainerElementResize"))}))}))),this.wot=e,this.wtSettings=r,this.domBindings=o,this.facadeGetter=t,this.wtTable=n;const{rootDocument:s,rootWindow:l}=this.domBindings;this.instance=this.wot,this.eventManager=i,this.scrollbarSize=Gg(s);const a="hidden"===l.getComputedStyle(n.wtRootElement.parentNode).getPropertyValue("overflow");this.scrollableElement=a?n.holder:Lg(n.TABLE),this.initOverlays(),this.destroyed=!1,this.keyPressed=!1,this.spreaderLastSize={width:null,height:null},this.verticalScrolling=!1,this.horizontalScrolling=!1,this.initBrowserLineHeight(),this.registerListeners(),this.lastScrollX=l.scrollX,this.lastScrollY=l.scrollY}getOverlays(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const t=[...IC(AC,this)];return e&&t.push(this.wtTable),t}initBrowserLineHeight(){const{rootWindow:e,rootDocument:t}=this.domBindings,o=e.getComputedStyle(t.body),r=parseInt(o.lineHeight,10),i=1.2*parseInt(o.fontSize,10);this.browserLineHeight=r||i}initOverlays(){const e=[this.wot,this.facadeGetter,this.wtSettings,this.domBindings];this.topOverlay=new HC(...e),this.bottomOverlay=new Nb(...e),this.inlineStartOverlay=new pC(...e),this.topInlineStartCornerOverlay=new SC(...e,this.topOverlay,this.inlineStartOverlay),this.bottomInlineStartCornerOverlay=new Eb(...e,this.bottomOverlay,this.inlineStartOverlay),xC(AC,this,[this.topOverlay,this.bottomOverlay,this.inlineStartOverlay,this.topInlineStartCornerOverlay,this.bottomInlineStartCornerOverlay])}beforeDraw(){xC(kC,this,IC(AC,this).reduce(((e,t)=>t.hasRenderingStateChanged()||e),!1)),IC(AC,this).forEach((e=>e.updateStateOfRendering("before")))}afterDraw(){this.syncScrollWithMaster(),IC(AC,this).forEach((e=>{const t=e.hasRenderingStateChanged();e.updateStateOfRendering("after"),t&&!e.needFullRender&&e.reset()}))}refreshAll(){this.wot.drawn&&(this.wtTable.holder.parentNode?(this.wot.draw(!0),this.verticalScrolling&&this.inlineStartOverlay.onScroll(),this.horizontalScrolling&&this.topOverlay.onScroll(),this.verticalScrolling=!1,this.horizontalScrolling=!1):this.destroy())}registerListeners(){const{rootDocument:e,rootWindow:t}=this.domBindings,{mainTableScrollableElement:o}=this.topOverlay,{mainTableScrollableElement:r}=this.inlineStartOverlay;this.eventManager.addEventListener(e.documentElement,"keydown",(e=>this.onKeyDown(e))),this.eventManager.addEventListener(e.documentElement,"keyup",(()=>this.onKeyUp())),this.eventManager.addEventListener(e,"visibilitychange",(()=>this.onKeyUp())),this.eventManager.addEventListener(o,"scroll",(e=>this.onTableScroll(e)),{passive:!0}),o!==r&&this.eventManager.addEventListener(r,"scroll",(e=>this.onTableScroll(e)),{passive:!0});const i=t.devicePixelRatio&&t.devicePixelRatio>1,n=this.scrollableElement===t,s=this.wtSettings.getSetting("preventWheel"),l={passive:n};let a;(s||i||!Gf.chrome.value)&&this.eventManager.addEventListener(this.wtTable.wtRootElement,"wheel",(e=>this.onCloneWheel(e,s)),l),[this.topOverlay,this.bottomOverlay,this.inlineStartOverlay,this.topInlineStartCornerOverlay,this.bottomInlineStartCornerOverlay].forEach((e=>{this.eventManager.addEventListener(e.clone.wtTable.holder,"wheel",(e=>this.onCloneWheel(e,s)),l)})),this.eventManager.addEventListener(t,"resize",(()=>{Uf((()=>{clearTimeout(a),this.wtSettings.getSetting("onWindowResize"),a=setTimeout((()=>{xC(_C,this,0)}),200)}))})),n||this.resizeObserver.observe(this.wtTable.wtRootElement.parentElement)}onTableScroll(e){const t=this.domBindings.rootWindow,o=this.inlineStartOverlay.mainTableScrollableElement,r=this.topOverlay.mainTableScrollableElement,i=e.target;this.keyPressed&&(r!==t&&i!==t&&!e.target.contains(r)||o!==t&&i!==t&&!e.target.contains(o))||this.syncScrollPositions(e)}onCloneWheel(e,t){const{rootWindow:o}=this.domBindings,r=this.inlineStartOverlay.mainTableScrollableElement,i=this.topOverlay.mainTableScrollableElement,n=e.target,s=i!==o&&n!==o&&!n.contains(i),l=r!==o&&n!==o&&!n.contains(r);if(this.keyPressed&&(s||l)||this.scrollableElement===o)return;const a=this.translateMouseWheelToScroll(e);(t||this.scrollableElement!==o&&a)&&e.preventDefault()}onKeyDown(e){this.keyPressed=function(e,t){const o=t.split("|");let r=!1;return jd(o,(t=>{if(e===Zf[t])return r=!0,!1})),r}(e.keyCode,"ARROW_UP|ARROW_RIGHT|ARROW_DOWN|ARROW_LEFT")}onKeyUp(){this.keyPressed=!1}translateMouseWheelToScroll(e){let t=isNaN(e.deltaY)?-1*e.wheelDeltaY:e.deltaY,o=isNaN(e.deltaX)?-1*e.wheelDeltaX:e.deltaX;1===e.deltaMode&&(o+=o*this.browserLineHeight,t+=t*this.browserLineHeight);const r=this.scrollVertically(t),i=this.scrollHorizontally(o);return r||i}scrollVertically(e){const t=this.scrollableElement.scrollTop;return this.scrollableElement.scrollTop+=e,t!==this.scrollableElement.scrollTop}scrollHorizontally(e){const t=this.scrollableElement.scrollLeft;return this.scrollableElement.scrollLeft+=e,t!==this.scrollableElement.scrollLeft}syncScrollPositions(){if(this.destroyed)return;const e=this.topOverlay.clone.wtTable.holder,t=this.inlineStartOverlay.clone.wtTable.holder;let o=this.scrollableElement.scrollLeft,r=this.scrollableElement.scrollTop;if(this.wot.wtViewport.isHorizontallyScrollableByWindow()&&(o=this.scrollableElement.scrollX),this.wot.wtViewport.isVerticallyScrollableByWindow()&&(r=this.scrollableElement.scrollY),this.horizontalScrolling=this.lastScrollX!==o,this.verticalScrolling=this.lastScrollY!==r,this.lastScrollX=o,this.lastScrollY=r,this.horizontalScrolling){e.scrollLeft=o;const t=this.bottomOverlay.needFullRender?this.bottomOverlay.clone.wtTable.holder:null;t&&(t.scrollLeft=o)}this.verticalScrolling&&(t.scrollTop=r),this.refreshAll()}syncScrollWithMaster(){if(!IC(kC,this))return;const e=this.topOverlay.mainTableScrollableElement,{scrollLeft:t,scrollTop:o}=e;this.topOverlay.needFullRender&&(this.topOverlay.clone.wtTable.holder.scrollLeft=t),this.bottomOverlay.needFullRender&&(this.bottomOverlay.clone.wtTable.holder.scrollLeft=t),this.inlineStartOverlay.needFullRender&&(this.inlineStartOverlay.clone.wtTable.holder.scrollTop=o),xC(kC,this,!1)}updateMainScrollableElements(){this.eventManager.clearEvents(!0),this.inlineStartOverlay.updateMainScrollableElement(),this.topOverlay.updateMainScrollableElement(),this.bottomOverlay.needFullRender&&this.bottomOverlay.updateMainScrollableElement();const{wtTable:e}=this,{rootWindow:t}=this.domBindings;"hidden"===t.getComputedStyle(e.wtRootElement.parentNode).getPropertyValue("overflow")?this.scrollableElement=e.holder:this.scrollableElement=Lg(e.TABLE),this.registerListeners()}destroy(){this.resizeObserver.disconnect(),this.eventManager.destroy(),this.topOverlay.destroy(),this.bottomOverlay.clone&&this.bottomOverlay.destroy(),this.inlineStartOverlay.destroy(),this.topInlineStartCornerOverlay&&this.topInlineStartCornerOverlay.destroy(),this.bottomInlineStartCornerOverlay&&this.bottomInlineStartCornerOverlay.clone&&this.bottomInlineStartCornerOverlay.destroy(),this.destroyed=!0}refresh(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.updateLastSpreaderSize()&&this.adjustElementsSize(),this.bottomOverlay.clone&&this.bottomOverlay.refresh(e),this.inlineStartOverlay.refresh(e),this.topOverlay.refresh(e),this.topInlineStartCornerOverlay&&this.topInlineStartCornerOverlay.refresh(e),this.bottomInlineStartCornerOverlay&&this.bottomInlineStartCornerOverlay.clone&&this.bottomInlineStartCornerOverlay.refresh(e)}updateLastSpreaderSize(){const e=this.wtTable.spreader,t=e.clientWidth,o=e.clientHeight,r=t!==this.spreaderLastSize.width||o!==this.spreaderLastSize.height;return r&&(this.spreaderLastSize.width=t,this.spreaderLastSize.height=o),r}adjustElementsSize(){const{wtViewport:e}=this.wot,{wtTable:t}=this,{rootWindow:o}=this.domBindings,r=this.scrollableElement===o,i=this.wtSettings.getSetting("totalColumns"),n=this.wtSettings.getSetting("totalRows"),s=e.getRowHeaderWidth(),l=e.getColumnHeaderHeight()+this.topOverlay.sumCellSizes(0,n)+1,a=s+this.inlineStartOverlay.sumCellSizes(0,i),h=t.hider.style,c=(()=>!r&&this.scrollableElement.scrollTop>Math.max(0,l-t.holder.clientHeight))()?1:0,u=(()=>!r&&this.scrollableElement.scrollLeft>Math.max(0,a-t.holder.clientWidth))()?1:0;h.width=`${a+u}px`,h.height=`${l+c}px`,this.topOverlay.adjustElementsSize(),this.inlineStartOverlay.adjustElementsSize(),this.bottomOverlay.adjustElementsSize()}expandHiderVerticallyBy(e){const{wtTable:t}=this;t.hider.style.height=`${parseInt(t.hider.style.height,10)+e}px`}expandHiderHorizontallyBy(e){const{wtTable:t}=this;t.hider.style.width=`${parseInt(t.hider.style.width,10)+e}px`}applyToDOM(){this.wtTable.isVisible()&&(this.topOverlay.applyToDOM(),this.bottomOverlay.clone&&this.bottomOverlay.applyToDOM(),this.inlineStartOverlay.applyToDOM())}getParentOverlay(e){if(!e)return null;const t=[this.topOverlay,this.inlineStartOverlay,this.bottomOverlay,this.topInlineStartCornerOverlay,this.bottomInlineStartCornerOverlay];let o=null;return jd(t,(t=>{t&&t.clone&&t.clone.wtTable.TABLE.contains(e)&&(o=t.clone)})),o}syncOverlayTableClassNames(){const e=this.wtTable.TABLE;jd([this.topOverlay,this.inlineStartOverlay,this.bottomOverlay,this.topInlineStartCornerOverlay,this.bottomInlineStartCornerOverlay],(t=>{t&&(t.clone.wtTable.TABLE.className=e.className)}))}};function LC(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class DC{constructor(e){LC(this,"settings",{}),LC(this,"defaults",Object.freeze(this.getDefaults())),Bf(this.defaults,((t,o)=>{if(void 0!==e[o])this.settings[o]=e[o];else{if(void 0===t)throw new Error(`A required setting "${o}" was not provided`);this.settings[o]=t}}))}getDefaults(){return{facade:void 0,table:void 0,isDataViewInstance:!0,externalRowCalculator:!1,currentRowClassName:null,currentColumnClassName:null,preventOverflow:()=>!1,preventWheel:!1,data:void 0,fixedColumnsStart:0,fixedRowsTop:0,fixedRowsBottom:0,shouldRenderInlineStartOverlay:()=>this.getSetting("fixedColumnsStart")>0||this.getSetting("rowHeaders").length>0,shouldRenderTopOverlay:()=>this.getSetting("fixedRowsTop")>0||this.getSetting("columnHeaders").length>0,shouldRenderBottomOverlay:()=>this.getSetting("fixedRowsBottom")>0,minSpareRows:0,rowHeaders:()=>[],columnHeaders:()=>[],totalRows:void 0,totalColumns:void 0,cellRenderer:(e,t,o)=>{const r=this.getSetting("data",e,t);Ig(o,null==r?"":r)},columnWidth(){},rowHeight(){},rowHeightByOverlayName(){},defaultColumnWidth:50,selections:null,hideBorderOnMouseDownOver:!1,viewportRowCalculatorOverride:null,viewportColumnCalculatorOverride:null,viewportRowRenderingThreshold:null,viewportColumnRenderingThreshold:null,onCellMouseDown:null,onCellContextMenu:null,onCellMouseOver:null,onCellMouseOut:null,onCellMouseUp:null,onCellDblClick:null,onCellCornerMouseDown:null,onCellCornerDblClick:null,beforeDraw:null,onDraw:null,onBeforeRemoveCellClassNames:null,onAfterDrawSelection:null,onBeforeDrawBorders:null,onBeforeViewportScrollHorizontally:e=>e,onBeforeViewportScrollVertically:e=>e,onScrollHorizontally:null,onScrollVertically:null,onBeforeTouchScroll:null,onAfterMomentumScroll:null,onModifyRowHeaderWidth:null,onModifyGetCellCoords:null,onModifyGetCoordsElement:null,onModifyGetCoords:null,onBeforeHighlightingRowHeader:e=>e,onBeforeHighlightingColumnHeader:e=>e,onWindowResize:null,onContainerElementResize:null,renderAllColumns:!1,renderAllRows:!1,groups:!1,rowHeaderWidth:null,columnHeaderHeight:null,headerClassName:null,rtlMode:!1,ariaTags:!0}}update(e,t){return void 0===t?Bf(e,((e,t)=>{this.settings[t]=e})):this.settings[e]=t,this}getSetting(e,t,o,r,i){return"function"==typeof this.settings[e]?this.settings[e](t,o,r,i):void 0!==t&&Array.isArray(this.settings[e])?this.settings[e][t]:this.settings[e]}getSettingPure(e){return this.settings[e]}has(e){return!!this.settings[e]}}class FC extends Jv{constructor(e,t,o,r){super(e,t,o,r,"master")}alignOverlaysWithTrimmingContainer(){const e=Dg(this.wtRootElement),{rootWindow:t}=this.domBindings;if(e===t)this.wtSettings.getSetting("preventOverflow")||(this.holder.style.overflow="visible",this.wtRootElement.style.overflow="visible");else{const o=e.parentElement,r=Fg(e,"height",t),i=Fg(e,"overflow",t),n=this.holder.style,{scrollWidth:s,scrollHeight:l}=e;let a=e.offsetWidth,h=e.offsetHeight;if(o&&["auto","hidden","scroll"].includes(i)){const r=e.cloneNode(!1);r.style.overflow="auto",r.style.position="absolute",e.nextElementSibling?o.insertBefore(r,e.nextElementSibling):o.appendChild(r);const i=parseInt(t.getComputedStyle(r).height,10);o.removeChild(r),0===i&&(h=0)}h=Math.min(h,l),n.height="auto"===r?"auto":`${h}px`,a=Math.min(a,s),n.width=`${a}px`,n.overflow="",this.hasTableHeight="auto"===n.height||h>0,this.hasTableWidth=a>0}this.isTableVisible=Ng(this.TABLE)}markOversizedColumnHeaders(){const{wtSettings:e}=this,{wtViewport:t}=this.dataAccessObject,o="master",r=e.getSetting("columnHeaders").length;if(r&&!t.hasOversizedColumnHeadersMarked[o]){const i=e.getSetting("rowHeaders").length,n=this.getRenderedColumnsCount();for(let e=0;e<r;e++)for(let t=-1*i;t<n;t++)this.markIfOversizedColumnHeader(t);t.hasOversizedColumnHeadersMarked[o]=!0}}}Vf(FC,kb),Vf(FC,Ob);const BC=FC,WC=class{constructor(e,t,o,r,i){this.dataAccessObject=e,this.wot=e.wot,this.instance=this.wot,this.domBindings=t,this.wtSettings=o,this.wtTable=i,this.oversizedRows=[],this.oversizedColumnHeaders=[],this.hasOversizedColumnHeadersMarked={},this.clientHeight=0,this.rowHeaderWidth=NaN,this.rowsVisibleCalculator=null,this.columnsVisibleCalculator=null,this.rowsCalculatorTypes=new Map([["rendered",()=>this.wtSettings.getSetting("renderAllRows")?new wy:new Cy],["fullyVisible",()=>new hy],["partiallyVisible",()=>new gy]]),this.columnsCalculatorTypes=new Map([["rendered",()=>this.wtSettings.getSetting("renderAllColumns")?new my:new vy],["fullyVisible",()=>new ly],["partiallyVisible",()=>new uy]]),this.eventManager=r,this.eventManager.addEventListener(this.domBindings.rootWindow,"resize",(()=>{this.clientHeight=this.getWorkspaceHeight()}))}getWorkspaceHeight(){const e=this.domBindings.rootDocument,t=this.dataAccessObject.topOverlayTrimmingContainer;let o=0;return o=t===this.domBindings.rootWindow?e.documentElement.clientHeight:Wg(t)>0&&t.clientHeight>0?t.clientHeight:1/0,o}getViewportHeight(){let e=this.getWorkspaceHeight();if(e===1/0)return e;const t=this.getColumnHeaderHeight();return t>0&&(e-=t),e}getWorkspaceWidth(){const{rootDocument:e,rootWindow:t}=this.domBindings,o=this.dataAccessObject.inlineStartOverlayTrimmingContainer;let r;if(o===t){const t=this.wtSettings.getSetting("totalColumns");r=this.wtTable.holder.offsetWidth,this.getRowHeaderWidth()+this.sumColumnWidths(0,t)>r&&(r=e.documentElement.clientWidth)}else r=o.clientWidth;return r}getViewportWidth(){const e=this.getWorkspaceWidth();if(e===1/0)return e;const t=this.getRowHeaderWidth();return t>0?e-t:e}hasVerticalScroll(){if(this.isVerticallyScrollableByWindow()){const e=this.domBindings.rootDocument.documentElement;return e.scrollHeight>e.clientHeight}const{holder:e,hider:t}=this.wtTable,o=e.clientHeight,r=t.offsetHeight;return o<r||r>this.getWorkspaceHeight()}hasHorizontalScroll(){if(this.isVerticallyScrollableByWindow()){const e=this.domBindings.rootDocument.documentElement;return e.scrollWidth>e.clientWidth}const{holder:e,hider:t}=this.wtTable,o=e.clientWidth,r=t.offsetWidth;return o<r||r>this.getWorkspaceWidth()}isVerticallyScrollableByWindow(){return this.dataAccessObject.topOverlayTrimmingContainer===this.domBindings.rootWindow}isHorizontallyScrollableByWindow(){return this.dataAccessObject.inlineStartOverlayTrimmingContainer===this.domBindings.rootWindow}sumColumnWidths(e,t){let o=0,r=e;for(;r<t;)o+=this.wtTable.getColumnWidth(r),r+=1;return o}getWorkspaceOffset(){return Ag(this.wtTable.holder)}getColumnHeaderHeight(){return this.wtSettings.getSetting("columnHeaders").length?isNaN(this.columnHeaderHeight)&&(this.columnHeaderHeight=Wg(this.wtTable.THEAD)):this.columnHeaderHeight=0,this.columnHeaderHeight}getRowHeaderWidth(){const e=this.wtSettings.getSetting("rowHeaderWidth"),t=this.wtSettings.getSetting("rowHeaders");if(e){this.rowHeaderWidth=0;for(let o=0,r=t.length;o<r;o++)this.rowHeaderWidth+=e[o]||e}if(isNaN(this.rowHeaderWidth))if(t.length){let e=this.wtTable.TABLE.querySelector("TH");this.rowHeaderWidth=0;for(let o=0,r=t.length;o<r;o++)e?(this.rowHeaderWidth+=Bg(e),e=e.nextSibling):this.rowHeaderWidth+=50}else this.rowHeaderWidth=0;return this.rowHeaderWidth=this.wtSettings.getSetting("onModifyRowHeaderWidth",this.rowHeaderWidth)||this.rowHeaderWidth,this.rowHeaderWidth}createRowsCalculator(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["rendered","fullyVisible","partiallyVisible"];const{wtSettings:t,wtTable:o}=this;let r,i,n=this.getViewportHeight();this.rowHeaderWidth=NaN;let s=this.dataAccessObject.topScrollPosition-this.dataAccessObject.topParentOffset;const l=t.getSetting("fixedRowsTop"),a=t.getSetting("fixedRowsBottom"),h=t.getSetting("totalRows");return l&&s>=0&&(i=this.dataAccessObject.topOverlay.sumCellSizes(0,l),s+=i,n-=i),a&&this.dataAccessObject.bottomOverlay.clone&&(i=this.dataAccessObject.bottomOverlay.sumCellSizes(h-a,h),n-=i),r=o.holder.clientHeight===o.holder.offsetHeight?0:Gg(this.domBindings.rootDocument),new Oy({calculationTypes:e.map((e=>[e,this.rowsCalculatorTypes.get(e)()])),viewportHeight:n,scrollOffset:s,totalRows:t.getSetting("totalRows"),defaultRowHeight:this.instance.stylesHandler.getDefaultRowHeight(),rowHeightFn:e=>o.getRowHeight(e),overrideFn:t.getSettingPure("viewportRowCalculatorOverride"),horizontalScrollbarHeight:r})}createColumnsCalculator(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["rendered","fullyVisible","partiallyVisible"];const{wtSettings:t,wtTable:o}=this;let r=this.getViewportWidth(),i=Math.abs(this.dataAccessObject.inlineStartScrollPosition)-this.dataAccessObject.inlineStartParentOffset;this.columnHeaderHeight=NaN;const n=t.getSetting("fixedColumnsStart");if(n&&i>=0){const e=this.dataAccessObject.inlineStartOverlay.sumCellSizes(0,n);i+=e,r-=e}return o.holder.clientWidth!==o.holder.offsetWidth&&(r-=Gg(this.domBindings.rootDocument)),new Ey({calculationTypes:e.map((e=>[e,this.columnsCalculatorTypes.get(e)()])),viewportWidth:r,scrollOffset:i,totalColumns:t.getSetting("totalColumns"),columnWidthFn:e=>o.getColumnWidth(e),overrideFn:t.getSettingPure("viewportColumnCalculatorOverride"),inlineStartOffset:this.dataAccessObject.inlineStartParentOffset})}createCalculators(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const{wtSettings:t}=this,o=this.createRowsCalculator(),r=this.createColumnsCalculator();if(e&&!t.getSetting("renderAllRows")){const t=o.getResultsFor("fullyVisible");e=this.areAllProposedVisibleRowsAlreadyRendered(t)}if(e&&!t.getSetting("renderAllColumns")){const t=r.getResultsFor("fullyVisible");e=this.areAllProposedVisibleColumnsAlreadyRendered(t)}return e||(this.rowsRenderCalculator=o.getResultsFor("rendered"),this.columnsRenderCalculator=r.getResultsFor("rendered")),this.rowsVisibleCalculator=o.getResultsFor("fullyVisible"),this.columnsVisibleCalculator=r.getResultsFor("fullyVisible"),this.rowsPartiallyVisibleCalculator=o.getResultsFor("partiallyVisible"),this.columnsPartiallyVisibleCalculator=r.getResultsFor("partiallyVisible"),e}createVisibleCalculators(){const e=this.createRowsCalculator(["fullyVisible","partiallyVisible"]),t=this.createColumnsCalculator(["fullyVisible","partiallyVisible"]);this.rowsVisibleCalculator=e.getResultsFor("fullyVisible"),this.columnsVisibleCalculator=t.getResultsFor("fullyVisible"),this.rowsPartiallyVisibleCalculator=e.getResultsFor("partiallyVisible"),this.columnsPartiallyVisibleCalculator=t.getResultsFor("partiallyVisible")}areAllProposedVisibleRowsAlreadyRendered(e){if(!this.rowsVisibleCalculator)return!1;let{startRow:t,endRow:o}=e;if(null===t&&null===o){if(!e.isVisibleInTrimmingContainer)return!0;t=this.rowsPartiallyVisibleCalculator.startRow,o=this.rowsPartiallyVisibleCalculator.endRow}const{startRow:r,endRow:i,rowStartOffset:n,rowEndOffset:s}=this.rowsRenderCalculator,l=this.wtSettings.getSetting("totalRows")-1,a=this.wtSettings.getSetting("viewportRowRenderingThreshold");return Number.isInteger(a)&&a>0?(t=Math.max(0,t-Math.min(n,a)),o=Math.min(l,o+Math.min(s,a))):"auto"===a&&(t=Math.max(0,t-Math.ceil(n/2)),o=Math.min(l,o+Math.ceil(s/2))),!(t<r||t===r&&t>0||o>i||o===i&&o<l)}areAllProposedVisibleColumnsAlreadyRendered(e){if(!this.columnsVisibleCalculator)return!1;let{startColumn:t,endColumn:o}=e;if(null===t&&null===o){if(!e.isVisibleInTrimmingContainer)return!0;t=this.columnsPartiallyVisibleCalculator.startColumn,o=this.columnsPartiallyVisibleCalculator.endColumn}const{startColumn:r,endColumn:i,columnStartOffset:n,columnEndOffset:s}=this.columnsRenderCalculator,l=this.wtSettings.getSetting("totalColumns")-1,a=this.wtSettings.getSetting("viewportColumnRenderingThreshold");return Number.isInteger(a)&&a>0?(t=Math.max(0,t-Math.min(n,a)),o=Math.min(l,o+Math.min(s,a))):"auto"===a&&(t=Math.max(0,t-Math.ceil(n/2)),o=Math.min(l,o+Math.ceil(s/2))),!(t<r||t===r&&t>0||o>i||o===i&&o<l)}resetHasOversizedColumnHeadersMarked(){Bf(this.hasOversizedColumnHeadersMarked,((e,t,o)=>{o[t]=void 0}))}};function jC(e,t,o){zC(e,t),t.set(e,o)}function zC(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function $C(e,t){return e.get(XC(e,t))}function UC(e,t,o){return e.set(XC(e,t),o),o}function XC(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var GC=new WeakMap,YC=new WeakMap,KC=new WeakMap,qC=new WeakMap,QC=new WeakMap,JC=new WeakMap,ZC=new WeakMap,eS=new WeakSet;class tS{constructor(e){var t,o;zC(t=this,o=eS),o.add(t),jC(this,GC,void 0),jC(this,YC,void 0),jC(this,KC,void 0),jC(this,qC,void 0),jC(this,QC,!0),jC(this,JC,{}),jC(this,ZC,{}),UC(YC,this,e.rootTable.parentElement.parentElement),UC(qC,this,e.rootDocument)}isClassicTheme(){return $C(QC,this)}getCSSVariableValue(e){var t;if($C(QC,this))return null;if($C(JC,this)[`--ht-${e}`])return $C(JC,this)[`--ht-${e}`];const o=null!==(t=XC(eS,this,sS).call(this,`--ht-${e}`))&&void 0!==t?t:XC(eS,this,lS).call(this,`--ht-${e}`);return null!==o?($C(JC,this)[`--ht-${e}`]=o,o):void 0}getStyleForTD(e){var t;return null===(t=$C(ZC,this))||void 0===t?void 0:t.td[e]}getDefaultRowHeight(){if($C(QC,this))return 23;const e=XC(eS,this,oS).call(this);return!e&&Cg($C(YC,this),"ht-wrapper")?(nm(`The "${$C(GC,this)}" theme is enabled, but its stylesheets are missing or not imported correctly. Import the correct CSS files in order to use that theme.`),UC(QC,this,!0),this.useTheme(),23):e}areCellsBorderBox(){return"border-box"===this.getStyleForTD("box-sizing")}useTheme(e){if(!e)return XC(eS,this,iS).call(this),UC(QC,this,!0),void UC(GC,this,e||void 0);e&&e!==$C(GC,this)&&($C(GC,this)&&XC(eS,this,aS).call(this),UC(GC,this,e),UC(QC,this,!1),XC(eS,this,rS).call(this),XC(eS,this,iS).call(this))}getThemeName(){return $C(GC,this)}removeClassNames(){Cg($C(YC,this),$C(GC,this))&&Rg($C(YC,this),$C(GC,this))}}function oS(){const e=this.getCSSVariableValue("line-height"),t=this.getCSSVariableValue("cell-vertical-padding"),o=Math.ceil(parseFloat(this.getStyleForTD("border-bottom-width")));return null===e||null===t||isNaN(o)?null:e+2*t+o}function rS(){Rg($C(YC,this),/ht-theme-.*/g),Sg($C(YC,this),$C(GC,this))}function iS(){this.isClassicTheme()||UC(KC,this,getComputedStyle($C(YC,this)));const e=XC(eS,this,nS).call(this,["box-sizing","border-bottom-width"]);$C(ZC,this).td={...$C(ZC,this).td,"box-sizing":e["box-sizing"],"border-bottom-width":e["border-bottom-width"]}}function nS(e){const t=$C(qC,this),o=$C(YC,this),r=t.createElement("table"),i=t.createElement("tbody"),n=t.createElement("tr"),s=t.createElement("tr"),l=t.createElement("td");s.appendChild(l),i.appendChild(n),i.appendChild(s),r.appendChild(i),o.appendChild(r);const a=getComputedStyle(l),h={};return e.forEach((e=>{h[e]=a.getPropertyValue(e)})),o.removeChild(r),h}function sS(e){const t=Math.ceil(parseFloat(XC(eS,this,lS).call(this,e)));return Number.isNaN(t)?null:t}function lS(e){const t=$C(KC,this).getPropertyValue(e);return""===t?null:t}function aS(){UC(ZC,this,{}),UC(JC,this,{}),UC(QC,this,!0)}class hS extends bb{constructor(e,t){super(e,new DC(t)),this.stylesHandler=new tS(this.domBindings);const o=this.wtSettings.getSetting("facade",this);this.wtTable=new BC(this.getTableDao(),o,this.domBindings,this.wtSettings),this.wtViewport=new WC(this.getViewportDao(),this.domBindings,this.wtSettings,this.eventManager,this.wtTable),this.selectionManager=new fC(this.wtSettings.getSetting("selections")),this.wtEvent=new rv(o,this.domBindings,this.wtSettings,this.eventManager,this.wtTable,this.selectionManager),this.wtOverlays=new VC(this,o,this.domBindings,this.wtSettings,this.eventManager,this.wtTable),this.exportSettingsAsClassNames(),this.findOriginalHeaders()}exportSettingsAsClassNames(){const e=[],t=[];Bf({rowHeaders:"htRowHeaders",columnHeaders:"htColumnHeaders"},((o,r)=>{this.wtSettings.getSetting(r).length&&t.push(o),e.push(o)})),Rg(this.wtTable.wtRootElement.parentNode,e),Sg(this.wtTable.wtRootElement.parentNode,t)}getOverlayByName(e){var t;if(!cb.includes(e))return null;const o=e.replace(/_([a-z])/g,(e=>e[1].toUpperCase()));return null!==(t=this.wtOverlays[`${o}Overlay`])&&void 0!==t?t:null}getViewportDao(){const e=this;return{get wot(){return e},get topOverlayTrimmingContainer(){return e.wtOverlays.topOverlay.trimmingContainer},get inlineStartOverlayTrimmingContainer(){return e.wtOverlays.inlineStartOverlay.trimmingContainer},get topScrollPosition(){return e.wtOverlays.topOverlay.getScrollPosition()},get topParentOffset(){return e.wtOverlays.topOverlay.getTableParentOffset()},get inlineStartScrollPosition(){return e.wtOverlays.inlineStartOverlay.getScrollPosition()},get inlineStartParentOffset(){return e.wtOverlays.inlineStartOverlay.getTableParentOffset()},get topOverlay(){return e.wtOverlays.topOverlay},get inlineStartOverlay(){return e.wtOverlays.inlineStartOverlay},get bottomOverlay(){return e.wtOverlays.bottomOverlay}}}}class cS{constructor(e){e instanceof bb?this._wot=e:this._initFromSettings(e)}_initFromSettings(e){e.facade=e=>{const t=new cS(e);return()=>t},this._wot=new hS(e.table,e)}get guid(){return this._wot.guid}get rootDocument(){return this._wot.domBindings.rootDocument}get rootWindow(){return this._wot.domBindings.rootWindow}get wtSettings(){return this._wot.wtSettings}get cloneSource(){return this._wot.cloneSource}get cloneOverlay(){return this._wot.cloneOverlay}get selectionManager(){return this._wot.selectionManager}get wtViewport(){return this._wot.wtViewport}get wtOverlays(){return this._wot.wtOverlays}get wtTable(){return this._wot.wtTable}get wtEvent(){return this._wot.wtEvent}get wtScroll(){return this._wot.wtScroll}get drawn(){return this._wot.drawn}set drawn(e){this._wot.drawn=e}get activeOverlayName(){return this._wot.activeOverlayName}get drawInterrupted(){return this._wot.drawInterrupted}set drawInterrupted(e){this._wot.drawInterrupted=e}get lastMouseOver(){return this._wot.lastMouseOver}set lastMouseOver(e){this._wot.lastMouseOver=e}get momentumScrolling(){return this._wot.momentumScrolling}set momentumScrolling(e){this._wot.momentumScrolling=e}get touchApplied(){return this._wot.touchApplied}set touchApplied(e){this._wot.touchApplied=e}get domBindings(){return this._wot.domBindings}get eventListeners(){return this._wot.eventListeners}set eventListeners(e){this._wot.eventListeners=e}get eventManager(){return this._wot.eventManager}get stylesHandler(){return this._wot.stylesHandler}createCellCoords(e,t){return this._wot.createCellCoords(e,t)}createCellRange(e,t,o){return this._wot.createCellRange(e,t,o)}draw(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._wot.draw(e),this}getCell(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this._wot.getCell(e,t)}scrollViewport(e,t,o){return this._wot.scrollViewport(e,t,o)}scrollViewportHorizontally(e,t){return this._wot.scrollViewportHorizontally(e,t)}scrollViewportVertically(e,t){return this._wot.scrollViewportVertically(e,t)}getViewport(){return this._wot.getViewport()}getOverlayName(){return this._wot.cloneOverlay?this._wot.cloneOverlay.type:"master"}getOverlayByName(e){return this._wot.getOverlayByName(e)}exportSettingsAsClassNames(){return this._wot.exportSettingsAsClassNames()}update(e,t){return this._wot.wtSettings.update(e,t),this}getSetting(e,t,o,r,i){return this._wot.wtSettings.getSetting(e,t,o,r,i)}hasSetting(e){return this._wot.wtSettings.hasSetting(e)}destroy(){this._wot.destroy()}}function uS(e){let{isShiftKey:t,isLeftClick:o,isRightClick:r,coords:i,selection:n,controller:s,cellCoordsFactory:l}=e;const a=n.isSelected()?n.getSelectedRange().current():null,h=n.isSelectedByCorner(),c=n.isSelectedByRowHeader();if(n.markSource("mouse"),t&&a)i.row>=0&&i.col>=0&&!s.cell?n.setRangeEnd(i):(h||c)&&i.row>=0&&i.col>=0&&!s.cell?n.setRangeEnd(l(i.row,i.col)):h&&i.row<0&&!s.column?n.setRangeEnd(l(a.to.row,i.col)):c&&i.col<0&&!s.row?n.setRangeEnd(l(i.row,a.to.col)):(!h&&!c&&i.col<0||h&&i.col<0)&&!s.row?n.selectRows(Math.max(a.from.row,0),i.row,i.col):(!h&&!c&&i.row<0||c&&i.row<0)&&!s.column&&n.selectColumns(Math.max(a.from.col,0),i.col,i.row);else{const e=!n.inInSelection(i),t=o||r&&e;i.row<0&&i.col>=0&&!s.column?t&&n.selectColumns(i.col,i.col,i.row):i.col<0&&i.row>=0&&!s.row?t&&n.selectRows(i.row,i.row,i.col):i.col>=0&&i.row>=0&&!s.cell?t&&n.setRangeStart(i):i.col<0&&i.row<0&&n.selectAll(!0,!0,{disableHeadersHighlight:!0,focusPosition:{row:0,col:0}})}n.markEndSource()}const dS=new Map([["mousedown",uS],["mouseover",function(e){let{isLeftClick:t,coords:o,selection:r,controller:i,cellCoordsFactory:n}=e;if(!t)return;const s=r.isSelectedByRowHeader(),l=r.isSelectedByColumnHeader(),a=r.tableProps.countCols(),h=r.tableProps.countRows();r.markSource("mouse"),l&&!i.column?r.setRangeEnd(n(h-1,o.col)):s&&!i.row?r.setRangeEnd(n(o.row,a-1)):i.cell||r.setRangeEnd(o),r.markEndSource()}],["touchstart",uS]]);function gS(e,t){let{coords:o,selection:r,controller:i,cellCoordsFactory:n}=t;dS.get(e.type)({coords:o,selection:r,controller:i,cellCoordsFactory:n,isShiftKey:e.shiftKey,isLeftClick:im(e)||"touchstart"===e.type,isRightClick:rm(e)})}const fS=new WeakMap,mS=Symbol("rootInstance");function pS(e){return e===mS}function wS(e){return fS.has(e)}function yS(e,t,o){vS(e,t),t.set(e,o)}function vS(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function bS(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function CS(e,t){return e.get(RS(e,t))}function SS(e,t,o){return e.set(RS(e,t),o),o}function RS(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var TS=new WeakMap,ES=new WeakMap,HS=new WeakMap,OS=new WeakMap,MS=new WeakMap,xS=new WeakMap,IS=new WeakMap,NS=new WeakMap,AS=new WeakSet;function kS(){return parseInt(this.hot.rootElement.getAttribute(cg()[0]),10)}function _S(e){const t=RS(AS,this,kS).call(this)+e;Tg(this.hot.rootElement,...cg(t))}function PS(){const e=this.hot.rootElement;this.hasVerticalScroll()?Sg(e,"htHasScrollY"):Rg(e,"htHasScrollY"),this.hasHorizontalScroll()?Sg(e,"htHasScrollX"):Rg(e,"htHasScrollX")}const VS=class{constructor(e){var t,o;vS(t=this,o=AS),o.add(t),bS(this,"hot",void 0),bS(this,"eventManager",void 0),bS(this,"settings",void 0),bS(this,"THEAD",void 0),bS(this,"TBODY",void 0),bS(this,"_wt",void 0),bS(this,"activeWt",void 0),yS(this,TS,0),yS(this,ES,0),bS(this,"postponedAdjustElementsSize",!1),yS(this,HS,!1),yS(this,OS,void 0),yS(this,MS,void 0),yS(this,xS,0),yS(this,IS,0),yS(this,NS,null),this.hot=e,this.eventManager=new sp(this.hot),this.settings=this.hot.getSettings(),this.createElements(),this.registerEvents(),this.initializeWalkontable()}render(){if(!this.hot.isRenderSuspended()){const e=this.hot.forceFullRender;this.hot.runHooks("beforeRender",e),this.postponedAdjustElementsSize&&(this.postponedAdjustElementsSize=!1,this.adjustElementsSize()),this._wt.draw(!e),RS(AS,this,PS).call(this),this.hot.runHooks("afterRender",e),this.hot.forceFullRender=!1}}adjustElementsSize(){this.hot.isRenderSuspended()?this.postponedAdjustElementsSize=!0:this._wt.wtOverlays.adjustElementsSize()}getCellAtCoords(e,t){const o=this._wt.getCell(e,t);return o<0?null:o}scrollViewport(e,t,o){return this._wt.scrollViewport(e,t,o)}scrollViewportHorizontally(e,t){return this._wt.scrollViewportHorizontally(e,t)}scrollViewportVertically(e,t){return this._wt.scrollViewportVertically(e,t)}createElements(){const{rootElement:e,rootDocument:t}=this.hot,o=e.getAttribute("style");o&&e.setAttribute("data-originalstyle",o),Sg(e,"handsontable"),SS(MS,this,t.createElement("TABLE")),Sg(CS(MS,this),"htCore"),this.hot.getSettings().tableClassName&&Sg(CS(MS,this),this.hot.getSettings().tableClassName),this.settings.ariaTags&&(Tg(CS(MS,this),[["role","presentation"]]),Tg(e,[["role","treegrid"],hg(-1),cg(this.hot.countCols()),["aria-multiselectable","true"]])),this.THEAD=t.createElement("THEAD"),CS(MS,this).appendChild(this.THEAD),this.TBODY=t.createElement("TBODY"),CS(MS,this).appendChild(this.TBODY),this.hot.table=CS(MS,this),this.hot.container.insertBefore(CS(MS,this),this.hot.container.firstChild)}registerEvents(){const{rootElement:e,rootDocument:t,selection:o,rootWindow:r}=this.hot,i=t.documentElement;this.eventManager.addEventListener(e,"mousedown",(e=>{SS(HS,this,!0),this.isTextSelectionAllowed(e.target)||($g(r),e.preventDefault(),r.focus())})),this.eventManager.addEventListener(e,"mouseup",(()=>{SS(HS,this,!1)})),this.eventManager.addEventListener(e,"mousemove",(e=>{CS(HS,this)&&!this.isTextSelectionAllowed(e.target)&&(this.settings.fragmentSelection&&$g(r),e.preventDefault())})),this.eventManager.addEventListener(i,"keyup",(e=>{o.isInProgress()&&!e.shiftKey&&o.finish()})),this.eventManager.addEventListener(i,"mouseup",(r=>{o.isInProgress()&&im(r)&&o.finish(),SS(OS,this,!1);const i=Qg(t.activeElement);qg(t.activeElement)&&!i||!i&&(o.isSelected()||o.isSelectedByAnyHeader()||e.contains(r.target)||rm(r))||this.hot.unlisten()})),this.eventManager.addEventListener(i,"contextmenu",(e=>{o.isInProgress()&&rm(e)&&(o.finish(),SS(OS,this,!1))})),this.eventManager.addEventListener(i,"touchend",(()=>{o.isInProgress()&&o.finish(),SS(OS,this,!1)})),this.eventManager.addEventListener(i,"mousedown",(o=>{const r=o.target,n=o.x||o.clientX,s=o.y||o.clientY;let l=o.target;if(CS(OS,this)||!e||!this.hot.view)return;const{holder:a}=this._wt.wtTable;if(l===a){const e=Gg(t);if(t.elementFromPoint(n+e,s)!==a||t.elementFromPoint(n,s+e)!==a)return}else for(;l!==i;){if(null===l){if(o.isTargetWebComponent)break;return}if(l===e)return;l=l.parentNode}("function"==typeof this.settings.outsideClickDeselects?this.settings.outsideClickDeselects(r):this.settings.outsideClickDeselects)?this.hot.deselectCell():this.hot.destroyEditor(!1,!1)}));let n=fg(r);for(;null!==n;)this.eventManager.addEventListener(n.document.documentElement,"click",(()=>{this.hot.unlisten()})),n=fg(n);this.eventManager.addEventListener(CS(MS,this),"selectstart",(e=>{this.settings.fragmentSelection||qg(e.target)||e.preventDefault()}))}translateFromRenderableToVisualCoords(e){let{row:t,col:o}=e;return this.hot._createCellCoords(...this.translateFromRenderableToVisualIndex(t,o))}translateFromRenderableToVisualIndex(e,t){let o=e>=0?this.hot.rowIndexMapper.getVisualFromRenderableIndex(e):e,r=t>=0?this.hot.columnIndexMapper.getVisualFromRenderableIndex(t):t;return null===o&&(o=e),null===r&&(r=t),[o,r]}countRenderableIndexes(e,t){const o=Math.min(e.getNotTrimmedIndexesLength(),t),r=e.getNearestNotHiddenIndex(o-1,-1);return null===r?0:e.getRenderableFromVisualIndex(r)+1}countRenderableColumns(){return this.countRenderableIndexes(this.hot.columnIndexMapper,this.settings.maxCols)}countRenderableRows(){return this.countRenderableIndexes(this.hot.rowIndexMapper,this.settings.maxRows)}countNotHiddenRowIndexes(e,t){return this.countNotHiddenIndexes(e,t,this.hot.rowIndexMapper,this.countRenderableRows())}countNotHiddenColumnIndexes(e,t){return this.countNotHiddenIndexes(e,t,this.hot.columnIndexMapper,this.countRenderableColumns())}countNotHiddenIndexes(e,t,o,r){if(isNaN(e)||e<0)return 0;const i=o.getNearestNotHiddenIndex(e,t),n=o.getRenderableFromVisualIndex(i);if(!Number.isInteger(n))return 0;let s=0;return t<0?s=n+1:t>0&&(s=r-n),s}countNotHiddenFixedColumnsStart(){const e=this.hot.countCols(),t=Math.min(parseInt(this.settings.fixedColumnsStart,10),e)-1;return this.countNotHiddenColumnIndexes(t,-1)}countNotHiddenFixedRowsTop(){const e=this.hot.countRows(),t=Math.min(parseInt(this.settings.fixedRowsTop,10),e)-1;return this.countNotHiddenRowIndexes(t,-1)}countNotHiddenFixedRowsBottom(){const e=this.hot.countRows(),t=Math.max(e-parseInt(this.settings.fixedRowsBottom,10),0);return this.countNotHiddenRowIndexes(t,1)}countRenderableColumnsInRange(e,t){let o=0;for(let r=e;r<=t;r++)null!==this.hot.columnIndexMapper.getRenderableFromVisualIndex(r)&&(o+=1);return o}countRenderableRowsInRange(e,t){let o=0;for(let r=e;r<=t;r++)null!==this.hot.rowIndexMapper.getRenderableFromVisualIndex(r)&&(o+=1);return o}getStylesHandler(){return this._wt.stylesHandler}getDefaultRowHeight(){return this._wt.stylesHandler.getDefaultRowHeight()}addClassNameToLicenseElement(e){var t;const o=null===(t=this.hot.rootElement.parentNode)||void 0===t?void 0:t.querySelector(".hot-display-license-info");o&&Sg(o,e)}removeClassNameFromLicenseElement(e){var t;const o=null===(t=this.hot.rootElement.parentNode)||void 0===t?void 0:t.querySelector(".hot-display-license-info");o&&Rg(o,e)}isMainTableNotFullyCoveredByOverlays(){const e=this.countNotHiddenFixedRowsTop()+this.countNotHiddenFixedRowsBottom(),t=this.countNotHiddenFixedColumnsStart();return this.hot.countRenderedRows()>e&&this.hot.countRenderedCols()>t}initializeWalkontable(){const e={ariaTags:this.settings.ariaTags,rtlMode:this.hot.isRtl(),externalRowCalculator:this.hot.getPlugin("autoRowSize")&&this.hot.getPlugin("autoRowSize").isEnabled(),table:CS(MS,this),isDataViewInstance:()=>wS(this.hot),preventOverflow:()=>this.settings.preventOverflow,preventWheel:()=>this.settings.preventWheel,viewportColumnRenderingThreshold:()=>this.settings.viewportColumnRenderingThreshold,viewportRowRenderingThreshold:()=>this.settings.viewportRowRenderingThreshold,data:(e,t)=>this.hot.getDataAtCell(...this.translateFromRenderableToVisualIndex(e,t)),totalRows:()=>this.countRenderableRows(),totalColumns:()=>this.countRenderableColumns(),fixedColumnsStart:()=>this.countNotHiddenFixedColumnsStart(),fixedRowsTop:()=>this.countNotHiddenFixedRowsTop(),fixedRowsBottom:()=>this.countNotHiddenFixedRowsBottom(),shouldRenderInlineStartOverlay:()=>this.settings.fixedColumnsStart>0||e.rowHeaders().length>0,shouldRenderTopOverlay:()=>this.settings.fixedRowsTop>0||e.columnHeaders().length>0,shouldRenderBottomOverlay:()=>this.settings.fixedRowsBottom>0,minSpareRows:()=>this.settings.minSpareRows,renderAllRows:this.settings.renderAllRows,renderAllColumns:this.settings.renderAllColumns,rowHeaders:()=>{const e=[];return this.hot.hasRowHeaders()&&e.push(((e,t)=>{const o=e>=0?this.hot.rowIndexMapper.getVisualFromRenderableIndex(e):e;this.appendRowHeader(o,t)})),this.hot.runHooks("afterGetRowHeaderRenderers",e),SS(ES,this,e.length),this.hot.getSettings().ariaTags&&RS(AS,this,kS).call(this)===this.hot.countCols()&&RS(AS,this,_S).call(this,CS(ES,this)),e},columnHeaders:()=>{const e=[];return this.hot.hasColHeaders()&&e.push(((e,t)=>{const o=e>=0?this.hot.columnIndexMapper.getVisualFromRenderableIndex(e):e;this.appendColHeader(o,t)})),this.hot.runHooks("afterGetColumnHeaderRenderers",e),SS(TS,this,e.length),e},columnWidth:e=>{const t=this.hot.columnIndexMapper.getVisualFromRenderableIndex(e);return this.hot.getColWidth(null===t?e:t)},rowHeight:e=>{const t=this.hot.rowIndexMapper.getVisualFromRenderableIndex(e);return this.hot.getRowHeight(null===t?e:t)},rowHeightByOverlayName:(e,t)=>{const o=this.hot.rowIndexMapper.getVisualFromRenderableIndex(e),r=null===o?e:o;return this.hot.runHooks("modifyRowHeightByOverlayName",this.hot.getRowHeight(r),r,t)},cellRenderer:(e,t,o)=>{const[r,i]=this.translateFromRenderableToVisualIndex(e,t),n=this.hot.runHooks("modifyGetCellCoords",r,i,!1,"meta");let s=r,l=i;Array.isArray(n)&&([s,l]=n);const a=this.hot.getCellMeta(s,l),h=this.hot.colToProp(l);let c=this.hot.getDataAtRowProp(s,h);this.hot.hasHook("beforeValueRender")&&(c=this.hot.runHooks("beforeValueRender",c,a)),this.hot.runHooks("beforeRenderer",o,r,i,h,c,a),this.hot.getCellRenderer(a)(this.hot,o,r,i,h,c,a),this.hot.runHooks("afterRenderer",o,r,i,h,c,a)},selections:this.hot.selection.highlight,hideBorderOnMouseDownOver:()=>this.settings.fragmentSelection,onWindowResize:()=>{this.hot&&!this.hot.isDestroyed&&this.hot.refreshDimensions()},onContainerElementResize:()=>{this.hot&&!this.hot.isDestroyed&&Ng(this.hot.rootElement)&&this.hot.refreshDimensions()},onCellMouseDown:(e,t,o,r)=>{const i=this.translateFromRenderableToVisualCoords(t),n={row:!1,column:!1,cell:!1};this.hot.listen(),this.activeWt=r,SS(OS,this,!0),SS(NS,this,{x:e.clientX,y:e.clientY}),this.hot.runHooks("beforeOnCellMouseDown",e,i,o,n),om(e)||(gS(e,{coords:i,selection:this.hot.selection,controller:n,cellCoordsFactory:(e,t)=>this.hot._createCellCoords(e,t)}),this.hot.runHooks("afterOnCellMouseDown",e,i,o),this.activeWt=this._wt)},onCellContextMenu:(e,t,o,r)=>{const i=this.translateFromRenderableToVisualCoords(t);this.activeWt=r,SS(OS,this,!1),this.hot.selection.isInProgress()&&this.hot.selection.finish(),this.hot.runHooks("beforeOnCellContextMenu",e,i,o),om(e)||(this.hot.runHooks("afterOnCellContextMenu",e,i,o),this.activeWt=this._wt)},onCellMouseOut:(e,t,o,r)=>{const i=this.translateFromRenderableToVisualCoords(t);this.activeWt=r,this.hot.runHooks("beforeOnCellMouseOut",e,i,o),om(e)||(this.hot.runHooks("afterOnCellMouseOut",e,i,o),this.activeWt=this._wt)},onCellMouseOver:(e,t,o,r)=>{const i=this.translateFromRenderableToVisualCoords(t),n={row:!1,column:!1,cell:!1};this.activeWt=r,this.hot.runHooks("beforeOnCellMouseOver",e,i,o,n),om(e)||(!CS(OS,this)||CS(NS,this)&&CS(NS,this).x===e.clientX&&CS(NS,this).y===e.clientY||gS(e,{coords:i,selection:this.hot.selection,controller:n,cellCoordsFactory:(e,t)=>this.hot._createCellCoords(e,t)}),this.hot.runHooks("afterOnCellMouseOver",e,i,o),this.activeWt=this._wt,SS(NS,this,null))},onCellMouseUp:(e,t,o,r)=>{const i=this.translateFromRenderableToVisualCoords(t);this.activeWt=r,this.hot.runHooks("beforeOnCellMouseUp",e,i,o),om(e)||this.hot.isDestroyed||(this.hot.runHooks("afterOnCellMouseUp",e,i,o),this.activeWt=this._wt)},onCellCornerMouseDown:e=>{e.preventDefault(),this.hot.runHooks("afterOnCellCornerMouseDown",e)},onCellCornerDblClick:e=>{e.preventDefault(),this.hot.runHooks("afterOnCellCornerDblClick",e)},beforeDraw:(e,t)=>this.beforeRender(e,t),onDraw:e=>this.afterRender(e),onBeforeViewportScrollVertically:(e,t)=>{const o=this.hot.rowIndexMapper,r=e<0;let i=e;return r||(i=o.getVisualFromRenderableIndex(e),null!==i)?(i=this.hot.runHooks("beforeViewportScrollVertically",i,t),this.hot.runHooks("beforeViewportScroll"),r?i:o.getRenderableFromVisualIndex(i)):e},onBeforeViewportScrollHorizontally:(e,t)=>{const o=this.hot.columnIndexMapper,r=e<0;let i=e;return r||(i=o.getVisualFromRenderableIndex(e),null!==i)?(i=this.hot.runHooks("beforeViewportScrollHorizontally",i,t),this.hot.runHooks("beforeViewportScroll"),r?i:o.getRenderableFromVisualIndex(i)):e},onScrollVertically:()=>{this.hot.runHooks("afterScrollVertically"),this.hot.runHooks("afterScroll")},onScrollHorizontally:()=>{this.hot.runHooks("afterScrollHorizontally"),this.hot.runHooks("afterScroll")},onBeforeRemoveCellClassNames:()=>this.hot.runHooks("beforeRemoveCellClassNames"),onBeforeHighlightingRowHeader:(e,t,o)=>{const r=this.hot.rowIndexMapper,i=e<0;let n=e;i||(n=r.getVisualFromRenderableIndex(e));const s=this.hot.runHooks("beforeHighlightingRowHeader",n,t,o);return i?s:r.getRenderableFromVisualIndex(r.getNearestNotHiddenIndex(s,1))},onBeforeHighlightingColumnHeader:(e,t,o)=>{const r=this.hot.columnIndexMapper,i=e<0;let n=e;i||(n=r.getVisualFromRenderableIndex(e));const s=this.hot.runHooks("beforeHighlightingColumnHeader",n,t,o);return i?s:r.getRenderableFromVisualIndex(r.getNearestNotHiddenIndex(s,1))},onAfterDrawSelection:(e,t,o)=>{let r;const[i,n]=this.translateFromRenderableToVisualIndex(e,t),s=this.hot.selection.getSelectedRange();if(s.size()>0){const e=s.peekByIndex(null!=o?o:0);r=[e.from.row,e.from.col,e.to.row,e.to.col]}return this.hot.runHooks("afterDrawSelection",i,n,r,o)},onBeforeDrawBorders:(e,t)=>{const[o,r,i,n]=e,s=[this.hot.rowIndexMapper.getVisualFromRenderableIndex(o),this.hot.columnIndexMapper.getVisualFromRenderableIndex(r),this.hot.rowIndexMapper.getVisualFromRenderableIndex(i),this.hot.columnIndexMapper.getVisualFromRenderableIndex(n)];return this.hot.runHooks("beforeDrawBorders",s,t)},onBeforeTouchScroll:()=>this.hot.runHooks("beforeTouchScroll"),onAfterMomentumScroll:()=>this.hot.runHooks("afterMomentumScroll"),onModifyRowHeaderWidth:e=>this.hot.runHooks("modifyRowHeaderWidth",e),onModifyGetCellCoords:(e,t,o,r)=>{const i=this.hot.rowIndexMapper,n=this.hot.columnIndexMapper,s=t>=0?n.getVisualFromRenderableIndex(t):t,l=e>=0?i.getVisualFromRenderableIndex(e):e,a=this.hot.runHooks("modifyGetCellCoords",l,s,o,r);if(Array.isArray(a)){const[e,t,o,r]=a;return[e>=0?i.getRenderableFromVisualIndex(i.getNearestNotHiddenIndex(e,1)):e,t>=0?n.getRenderableFromVisualIndex(n.getNearestNotHiddenIndex(t,1)):t,o>=0?i.getRenderableFromVisualIndex(i.getNearestNotHiddenIndex(o,-1)):o,r>=0?n.getRenderableFromVisualIndex(n.getNearestNotHiddenIndex(r,-1)):r]}},onModifyGetCoordsElement:(e,t)=>{const o=this.hot.rowIndexMapper,r=this.hot.columnIndexMapper,i=t>=0?r.getVisualFromRenderableIndex(t):t,n=e>=0?o.getVisualFromRenderableIndex(e):e,s=this.hot.runHooks("modifyGetCoordsElement",n,i);if(Array.isArray(s)){const[e,t]=s;return[e>=0?o.getRenderableFromVisualIndex(o.getNearestNotHiddenIndex(e,1)):e,t>=0?r.getRenderableFromVisualIndex(r.getNearestNotHiddenIndex(t,1)):t]}},viewportRowCalculatorOverride:e=>{let t=this.settings.viewportRowRenderingOffset;if("auto"===t&&this.settings.fixedRowsTop&&(t=10),t>0||"auto"===t){const o=this.countRenderableRows(),r=e.startRow,i=e.endRow;if("number"==typeof t)e.startRow=Math.max(r-t,0),e.endRow=Math.min(i+t,o-1);else if("auto"===t){const t=Math.max(1,Math.ceil(i/o*12));e.startRow=Math.max(r-t,0),e.endRow=Math.min(i+t,o-1)}}this.hot.runHooks("afterViewportRowCalculatorOverride",e)},viewportColumnCalculatorOverride:e=>{let t=this.settings.viewportColumnRenderingOffset;if("auto"===t&&this.settings.fixedColumnsStart&&(t=10),t>0||"auto"===t){const o=this.countRenderableColumns(),r=e.startColumn,i=e.endColumn;if("number"==typeof t&&(e.startColumn=Math.max(r-t,0),e.endColumn=Math.min(i+t,o-1)),"auto"===t){const t=Math.max(1,Math.ceil(i/o*6));e.startColumn=Math.max(r-t,0),e.endColumn=Math.min(i+t,o-1)}}this.hot.runHooks("afterViewportColumnCalculatorOverride",e)},rowHeaderWidth:()=>this.settings.rowHeaderWidth,columnHeaderHeight:()=>{const e=this.hot.runHooks("modifyColumnHeaderHeight");return this.settings.columnHeaderHeight||e}};this.hot.runHooks("beforeInitWalkontable",e),this._wt=new cS(e),this.activeWt=this._wt;const t=this._wt.wtTable.spreader,{width:o,height:r}=this.hot.rootElement.getBoundingClientRect();this.setLastSize(o,r),this.eventManager.addEventListener(t,"mousedown",(e=>{e.target===t&&3===e.which&&e.stopPropagation()})),this.eventManager.addEventListener(t,"contextmenu",(e=>{e.target===t&&3===e.which&&e.stopPropagation()})),this.eventManager.addEventListener(this.hot.rootDocument.documentElement,"click",(()=>{this.settings.observeDOMVisibility&&this._wt.drawInterrupted&&this.hot.render()}))}isTextSelectionAllowed(e){if(qg(e))return!0;const t=wg(e,this._wt.wtTable.spreader);return!(!0!==this.settings.fragmentSelection||!t)||!("cell"!==this.settings.fragmentSelection||!this.isSelectedOnlyCell()||!t)||!(this.settings.fragmentSelection||!this.isCellEdited()||!this.isSelectedOnlyCell())}isMouseDown(){return CS(OS,this)}isSelectedOnlyCell(){var e,t;return null!==(e=null===(t=this.hot.getSelectedRangeLast())||void 0===t?void 0:t.isSingleCell())&&void 0!==e&&e}isCellEdited(){const e=this.hot.getActiveEditor();return e&&e.isOpened()}beforeRender(e,t){e&&this.hot.runHooks("beforeViewRender",this.hot.forceFullRender,t)}afterRender(e){e&&this.hot.runHooks("afterViewRender",this.hot.forceFullRender)}appendRowHeader(e,t){if(t.firstChild){const o=t.firstChild;if(!Cg(o,"relative"))return Og(t),void this.appendRowHeader(e,t);this.updateCellHeader(o.querySelector(".rowHeader"),e,this.hot.getRowHeader)}else{const{rootDocument:o,getRowHeader:r}=this.hot,i=o.createElement("div"),n=o.createElement("span");i.className="relative",n.className="rowHeader",this.updateCellHeader(n,e,r),i.appendChild(n),t.appendChild(i)}this.hot.runHooks("afterGetRowHeader",e,t)}appendColHeader(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hot.getColHeader,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;const i=()=>{const t=e>=0?this.hot.getColumnMeta(e).headerClassName:null;return t?t.split(" "):[]};if(t.firstChild){const n=t.firstChild;Cg(n,"relative")?(this.updateCellHeader(n.querySelector(".colHeader"),e,o,r),n.className="",Sg(n,["relative",...i()])):(Og(t),this.appendColHeader(e,t,o,r))}else{const{rootDocument:n}=this.hot,s=n.createElement("div"),l=n.createElement("span"),a=i();s.classList.add("relative",...a),l.className="colHeader",this.settings.ariaTags&&(Tg(s,"role","presentation"),Tg(l,"role","presentation")),this.updateCellHeader(l,e,o,r),s.appendChild(l),t.appendChild(s)}this.hot.runHooks("afterGetColHeader",e,t,r)}updateCellHeader(e,t,o){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=t;const n=this._wt.wtOverlays.getParentOverlay(e)||this._wt;e.parentNode&&(Cg(e,"colHeader")?i=n.wtTable.columnFilter.sourceToRendered(t):Cg(e,"rowHeader")&&(i=n.wtTable.rowFilter.sourceToRendered(t))),i>-1?xg(e,o(t,r)):(Ig(e,String.fromCharCode(160)),Sg(e,"cornerHeader"))}maximumVisibleElementWidth(e){const t=this._wt.wtViewport.getWorkspaceWidth()-e;return t>0?t:0}maximumVisibleElementHeight(e){const t=this._wt.wtViewport.getWorkspaceHeight()-e;return t>0?t:0}setLastSize(e,t){SS(xS,this,e),SS(IS,this,t)}getLastSize(){return{width:CS(xS,this),height:CS(IS,this)}}getFirstRenderedVisibleRow(){if(!this._wt.wtViewport.rowsRenderCalculator)return null;const e=this.hot.rowIndexMapper,t=e.getVisualFromRenderableIndex(this._wt.wtTable.getFirstRenderedRow());return e.getNearestNotHiddenIndex(null!=t?t:0,1)}getLastRenderedVisibleRow(){if(!this._wt.wtViewport.rowsRenderCalculator)return null;const e=this.hot.rowIndexMapper,t=e.getVisualFromRenderableIndex(this._wt.wtTable.getLastRenderedRow());return e.getNearestNotHiddenIndex(null!=t?t:this.hot.countRows()-1,-1)}getFirstRenderedVisibleColumn(){if(!this._wt.wtViewport.columnsRenderCalculator)return null;const e=this.hot.columnIndexMapper,t=e.getVisualFromRenderableIndex(this._wt.wtTable.getFirstRenderedColumn());return e.getNearestNotHiddenIndex(null!=t?t:0,1)}getLastRenderedVisibleColumn(){if(!this._wt.wtViewport.columnsRenderCalculator)return null;const e=this.hot.columnIndexMapper,t=e.getVisualFromRenderableIndex(this._wt.wtTable.getLastRenderedColumn());return e.getNearestNotHiddenIndex(null!=t?t:this.hot.countCols()-1,-1)}getFirstFullyVisibleRow(){return this.hot.rowIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getFirstVisibleRow())}getLastFullyVisibleRow(){return this.hot.rowIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getLastVisibleRow())}getFirstFullyVisibleColumn(){return this.hot.columnIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getFirstVisibleColumn())}getLastFullyVisibleColumn(){return this.hot.columnIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getLastVisibleColumn())}getFirstPartiallyVisibleRow(){return this.hot.rowIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getFirstPartiallyVisibleRow())}getLastPartiallyVisibleRow(){return this.hot.rowIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getLastPartiallyVisibleRow())}getFirstPartiallyVisibleColumn(){return this.hot.columnIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getFirstPartiallyVisibleColumn())}getLastPartiallyVisibleColumn(){return this.hot.columnIndexMapper.getVisualFromRenderableIndex(this._wt.wtScroll.getLastPartiallyVisibleColumn())}getColumnHeadersCount(){return CS(TS,this)}getRowHeadersCount(){return CS(ES,this)}getViewportWidth(){return this._wt.wtViewport.getViewportWidth()}getWorkspaceWidth(){return this._wt.wtViewport.getWorkspaceWidth()}getViewportHeight(){return this._wt.wtViewport.getViewportHeight()}getWorkspaceHeight(){return this._wt.wtViewport.getWorkspaceHeight()}getElementOverlayName(e){var t;return(null!==(t=this._wt.wtOverlays.getParentOverlay(e))&&void 0!==t?t:this._wt).wtTable.name}getOverlayByName(e){return this._wt.getOverlayByName(e)}getActiveOverlayName(){return this._wt.activeOverlayName}isVisible(){return this._wt.wtTable.isVisible()}hasVerticalScroll(){return this._wt.wtViewport.hasVerticalScroll()}hasHorizontalScroll(){return this._wt.wtViewport.hasHorizontalScroll()}getTableWidth(){return this._wt.wtTable.getWidth()}getTableHeight(){return this._wt.wtTable.getHeight()}getTableOffset(){return this._wt.wtViewport.getWorkspaceOffset()}getRowHeaderWidth(){return this._wt.wtViewport.getRowHeaderWidth()}getColumnHeaderHeight(){return this._wt.wtViewport.getColumnHeaderHeight()}isVerticallyScrollableByWindow(){return this._wt.wtViewport.isVerticallyScrollableByWindow()}isHorizontallyScrollableByWindow(){return this._wt.wtViewport.isHorizontallyScrollableByWindow()}destroy(){this._wt.destroy(),this.eventManager.destroy()}};var LS=gi,DS=E,FS=xs,BS=Ae,WS=Gt,jS=ol,zS=ms,$S=oc("every",TypeError);function US(e){let t=0;return Array.isArray(e)&&(e[0]&&Array.isArray(e[0])?t=e[0].length:e[0]&&Df(e[0])&&(t=jf(e[0]))),t}function XS(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}LS({target:"Iterator",proto:!0,real:!0,forced:$S},{every:function(e){WS(this);try{BS(e)}catch(r){zS(this,"throw",r)}if($S)return DS($S,this,e);var t=jS(this),o=0;return!FS(t,(function(t,r){if(!e(t,o++))return r()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}});const GS=class{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];XS(this,"hot",void 0),XS(this,"data",void 0),XS(this,"dataType","array"),XS(this,"colToProp",(()=>{})),XS(this,"propToCol",(()=>{})),this.hot=e,this.data=t}modifyRowData(e){let t;return this.hot.hasHook("modifyRowData")&&(t=this.hot.runHooks("modifyRowData",e)),void 0===t||Number.isInteger(t)?this.data[e]:t}getData(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this.data&&0!==this.data.length?this.getByRange(null,null,e):this.data}setData(e){this.data=e}getAtColumn(e){const t=[];return jd(this.data,((o,r)=>{const i=this.getAtCell(r,e);t.push(i)})),t}getAtRow(e,t,o){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const i=void 0===t&&void 0===o,{dataDotNotation:n}=this.hot.getSettings();let s=null,l=null;if(s=this.modifyRowData(e),Array.isArray(s))l=[],i?s.forEach(((t,o)=>{l[o]=this.getAtPhysicalCell(e,o,s)})):Ew(t,o,(o=>{l[o-t]=this.getAtPhysicalCell(e,o,s)}));else if(Df(s)||Zg(s))if(l=r?[]:{},!i||r){const i=0,a=this.countFirstRowKeys()-1;Ew(i,a,(h=>{const c=this.colToProp(h);if(h>=(t||i)&&h<=(o||a)&&!Number.isInteger(c)){const t=this.getAtPhysicalCell(e,c,s);r?l.push(t):n?Wf(l,c,t):l[c]=t}}))}else Bf(s,((t,o)=>{const r=this.getAtPhysicalCell(e,o,s);n?Wf(l,o,r):l[o]=r}));return l}setAtCell(e,t,o){if(!(e>=this.countRows()||t>=this.countFirstRowKeys())){if(this.hot.hasHook("modifySourceData")){const r=zf(o);this.hot.runHooks("modifySourceData",e,t,r,"set"),r.isTouched()&&(o=r.value)}["__proto__","constructor","prototype"].includes(e)||(Number.isInteger(t)?this.data[e][t]=o:Wf(this.data[e],t,o))}}getAtPhysicalCell(e,t,o){let r=null;if(o)if("string"==typeof t){const{dataDotNotation:e}=this.hot.getSettings();r=e?function(e,t){const o=t.split(".");let r=e;return Bf(o,(e=>{if(r=r[e],void 0===r)return r=void 0,!1})),r}(o,t):o[t]}else r="function"==typeof t?t(o):o[t];if(this.hot.hasHook("modifySourceData")){const o=zf(r);this.hot.runHooks("modifySourceData",e,t,o,"get"),o.isTouched()&&(r=o.value)}return r}getAtCell(e,t){const o=this.modifyRowData(e);return this.getAtPhysicalCell(e,this.colToProp(t),o)}getByRange(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!1,i=null,n=null,s=null,l=null;null===e||null===t?(r=!0,i=0,s=this.countRows()-1):(i=Math.min(e.row,t.row),n=Math.min(e.col,t.col),s=Math.max(e.row,t.row),l=Math.max(e.col,t.col));const a=[];return Ew(i,s,(e=>{a.push(r?this.getAtRow(e,void 0,void 0,o):this.getAtRow(e,n,l,o))})),a}countRows(){if(this.hot.hasHook("modifySourceLength")){const e=this.hot.runHooks("modifySourceLength");if(Number.isInteger(e))return e}return this.data.length}countFirstRowKeys(){return US(this.data)}destroy(){this.data=null,this.hot=null}};var YS=gi,KS=E,qS=xs,QS=Ae,JS=Gt,ZS=ol,eR=ms,tR=oc("some",TypeError);function oR(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}YS({target:"Iterator",proto:!0,real:!0,forced:tR},{some:function(e){JS(this);try{QS(e)}catch(r){eR(this,"throw",r)}if(tR)return KS(tR,this,e);var t=ZS(this),o=0;return qS(t,(function(t,r){if(e(t,o++))return r()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}});class rR{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;oR(this,"indexedValues",[]),oR(this,"initValueOrFn",void 0),this.initValueOrFn=e}getValues(){return this.indexedValues}getValueAtIndex(e){const t=this.indexedValues;if(e<t.length)return t[e]}setValues(e){this.indexedValues=e.slice(),this.runLocalHooks("change")}setValueAtIndex(e,t){return e<this.indexedValues.length&&(this.indexedValues[e]=t,this.runLocalHooks("change"),!0)}clear(){this.setDefaultValues()}getLength(){return this.getValues().length}setDefaultValues(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.indexedValues.length;this.indexedValues.length=0,Zg(this.initValueOrFn)?Ew(e-1,(e=>this.indexedValues.push(this.initValueOrFn(e)))):Ew(e-1,(()=>this.indexedValues.push(this.initValueOrFn))),this.runLocalHooks("change")}init(e){return this.setDefaultValues(e),this.runLocalHooks("init"),this}insert(){this.runLocalHooks("change")}remove(){this.runLocalHooks("change")}destroy(){this.clearLocalHooks(),this.indexedValues=null,this.initValueOrFn=null}}function iR(e,t,o,r){const i=o.length?o[0]:void 0;return[...e.slice(0,i),...o.map(((e,t)=>Zg(r)?r(e,t):r)),...void 0===i?[]:e.slice(i)]}function nR(e,t){return Bd(e,((e,o)=>!1===t.includes(o)))}Vf(rR,Lb);class sR extends rR{insert(e,t){this.indexedValues=iR(this.indexedValues,0,t,this.initValueOrFn),super.insert(e,t)}remove(e){this.indexedValues=nR(this.indexedValues,e),super.remove(e)}}class lR extends sR{constructor(){super(arguments.length>0&&void 0!==arguments[0]&&arguments[0])}getHiddenIndexes(){return Fd(this.getValues(),((e,t,o)=>(t&&e.push(o),e)),[])}}function aR(e,t,o){return[...e.slice(0,t),...o,...e.slice(t)]}function hR(e,t){return Bd(e,(e=>!1===t.includes(e)))}function cR(e,t){return Wd(e,(e=>e-t.filter((t=>t<e)).length))}function uR(e,t){const o=t[0],r=t.length;return Wd(e,(e=>e>=o?e+r:e))}function dR(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class gR extends sR{constructor(){super(arguments.length>0&&void 0!==arguments[0]&&arguments[0])}getTrimmedIndexes(){return Fd(this.getValues(),((e,t,o)=>(t&&e.push(o),e)),[])}}class fR extends rR{constructor(){super((e=>e))}insert(e,t){const o=uR(this.indexedValues,t);this.indexedValues=aR(o,e,t),super.insert(e,t)}remove(e){const t=hR(this.indexedValues,e);this.indexedValues=cR(t,e),super.remove(e)}}const mR=new Map([["hiding",lR],["index",rR],["linkedPhysicalIndexToValue",class extends rR{constructor(){super(...arguments),dR(this,"orderOfIndexes",[])}getValues(){return this.orderOfIndexes.map((e=>this.indexedValues[e]))}setValues(e){this.orderOfIndexes=[...Array(e.length).keys()],super.setValues(e)}setValueAtIndex(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.orderOfIndexes.length;return e<this.indexedValues.length&&(this.indexedValues[e]=t,!1===this.orderOfIndexes.includes(e)&&this.orderOfIndexes.splice(o,0,e),this.runLocalHooks("change"),!0)}clearValue(e){this.orderOfIndexes=hR(this.orderOfIndexes,[e]),Zg(this.initValueOrFn)?super.setValueAtIndex(e,this.initValueOrFn(e)):super.setValueAtIndex(e,this.initValueOrFn)}getLength(){return this.orderOfIndexes.length}setDefaultValues(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.indexedValues.length;this.orderOfIndexes.length=0,super.setDefaultValues(e)}insert(e,t){this.indexedValues=iR(this.indexedValues,0,t,this.initValueOrFn),this.orderOfIndexes=uR(this.orderOfIndexes,t),super.insert(e,t)}remove(e){this.indexedValues=nR(this.indexedValues,e),this.orderOfIndexes=hR(this.orderOfIndexes,e),this.orderOfIndexes=cR(this.orderOfIndexes,e),super.remove(e)}getEntries(){return this.orderOfIndexes.map((e=>[e,this.getValueAtIndex(e)]))}}],["physicalIndexToValue",sR],["trimming",gR]]);function pR(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class wR{constructor(){pR(this,"collection",new Map)}register(e,t){!1===this.collection.has(e)&&(this.collection.set(e,t),t.addLocalHook("change",(()=>this.runLocalHooks("change",t))))}unregister(e){const t=this.collection.get(e);Gd(t)&&(t.destroy(),this.collection.delete(e),this.runLocalHooks("change",t))}unregisterAll(){this.collection.forEach(((e,t)=>this.unregister(t))),this.collection.clear()}get(e){return Yd(e)?Array.from(this.collection.values()):this.collection.get(e)}getLength(){return this.collection.size}removeFromEvery(e){this.collection.forEach((t=>{t.remove(e)}))}insertToEvery(e,t){this.collection.forEach((o=>{o.insert(e,t)}))}initEvery(e){this.collection.forEach((t=>{t.init(e)}))}}function yR(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}Vf(wR,Lb);class vR extends wR{constructor(e,t){super(),yR(this,"mergedValuesCache",[]),yR(this,"aggregationFunction",void 0),yR(this,"fallbackValue",void 0),this.aggregationFunction=e,this.fallbackValue=t}getMergedValues(){if(!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0]))return this.mergedValuesCache;if(0===this.getLength())return[];const e=Wd(this.get(),(e=>e.getValues())),t=[],o=Gd(e[0])&&e[0].length||0;for(let r=0;r<o;r+=1){const o=[];for(let t=0;t<this.getLength();t+=1)o.push(e[t][r]);t.push(o)}return Wd(t,this.aggregationFunction)}getMergedValueAtIndex(e,t){const o=this.getMergedValues(t)[e];return Gd(o)?o:this.fallbackValue}updateCache(){this.mergedValuesCache=this.getMergedValues(!1)}}function bR(e,t,o){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,o)}function CR(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var SR=new WeakMap;class RR{constructor(){bR(this,SR,[])}subscribe(e){var t,o;return this.addLocalHook("change",e),this._write((o=this,(t=SR).get(CR(t,o)))),this}unsubscribe(){return this.runLocalHooks("unsubscribe"),this.clearLocalHooks(),this}_write(e){return e.length>0&&this.runLocalHooks("change",e),this}_writeInitialChanges(e){var t,o,r;o=this,r=e,(t=SR).set(CR(t,o),r)}}function TR(e,t){const o=[];let r=0,i=0;for(;r<e.length&&i<t.length;r++,i++)e[r]!==t[i]&&o.push({op:"replace",index:i,oldValue:e[r],newValue:t[i]});for(;r<t.length;r++)o.push({op:"insert",index:r,oldValue:void 0,newValue:t[r]});for(;i<e.length;i++)o.push({op:"remove",index:i,oldValue:e[i],newValue:void 0});return o}function ER(e,t,o){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,o)}function HR(e,t){return e.get(MR(e,t))}function OR(e,t,o){return e.set(MR(e,t),o),o}function MR(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}Vf(RR,Lb);var xR=new WeakMap,IR=new WeakMap,NR=new WeakMap,AR=new WeakMap,kR=new WeakMap;class _R{constructor(){let{initialIndexValue:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};ER(this,xR,new Set),ER(this,IR,[]),ER(this,NR,[]),ER(this,AR,!1),ER(this,kR,!1),OR(kR,this,null!=e&&e)}createObserver(){const e=new RR;return HR(xR,this).add(e),e.addLocalHook("unsubscribe",(()=>{HR(xR,this).delete(e)})),e._writeInitialChanges(TR(HR(IR,this),HR(NR,this))),e}emit(e){let t=HR(NR,this);HR(AR,this)&&HR(IR,this).length===e.length||(0===e.length?e=new Array(t.length).fill(HR(kR,this)):OR(IR,this,new Array(e.length).fill(HR(kR,this))),HR(AR,this)||(OR(AR,this,!0),t=HR(IR,this)));const o=TR(t,e);HR(xR,this).forEach((e=>e._write(o))),OR(NR,this,e)}}function PR(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class VR{constructor(){PR(this,"indexesSequence",new fR),PR(this,"trimmingMapsCollection",new vR((e=>e.some((e=>!0===e))),!1)),PR(this,"hidingMapsCollection",new vR((e=>e.some((e=>!0===e))),!1)),PR(this,"variousMapsCollection",new wR),PR(this,"hidingChangesObservable",new _R({initialIndexValue:!1})),PR(this,"notTrimmedIndexesCache",[]),PR(this,"notHiddenIndexesCache",[]),PR(this,"isBatched",!1),PR(this,"indexesSequenceChanged",!1),PR(this,"indexesChangeSource",void 0),PR(this,"trimmedIndexesChanged",!1),PR(this,"hiddenIndexesChanged",!1),PR(this,"renderablePhysicalIndexesCache",[]),PR(this,"fromPhysicalToVisualIndexesCache",new Map),PR(this,"fromVisualToRenderableIndexesCache",new Map),this.indexesSequence.addLocalHook("change",(()=>{this.indexesSequenceChanged=!0,this.updateCache(),this.runLocalHooks("indexesSequenceChange",this.indexesChangeSource),this.runLocalHooks("change",this.indexesSequence,null)})),this.trimmingMapsCollection.addLocalHook("change",(e=>{this.trimmedIndexesChanged=!0,this.updateCache(),this.runLocalHooks("change",e,this.trimmingMapsCollection)})),this.hidingMapsCollection.addLocalHook("change",(e=>{this.hiddenIndexesChanged=!0,this.updateCache(),this.runLocalHooks("change",e,this.hidingMapsCollection)})),this.variousMapsCollection.addLocalHook("change",(e=>{this.runLocalHooks("change",e,this.variousMapsCollection)}))}suspendOperations(){this.isBatched=!0}resumeOperations(){this.isBatched=!1,this.updateCache()}createChangesObserver(e){if("hiding"!==e)throw new Error(`Unsupported index map type "${e}".`);return this.hidingChangesObservable.createObserver()}createAndRegisterIndexMap(e,t,o){return this.registerMap(e,function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!mR.has(e))throw new Error(`The provided map type ("${e}") does not exist.`);return new(mR.get(e))(t)}(t,o))}registerMap(e,t){if(this.trimmingMapsCollection.get(e)||this.hidingMapsCollection.get(e)||this.variousMapsCollection.get(e))throw Error(`Map with name "${e}" has been already registered.`);t instanceof gR?this.trimmingMapsCollection.register(e,t):t instanceof lR?this.hidingMapsCollection.register(e,t):this.variousMapsCollection.register(e,t);const o=this.getNumberOfIndexes();return o>0&&t.init(o),t}unregisterMap(e){this.trimmingMapsCollection.unregister(e),this.hidingMapsCollection.unregister(e),this.variousMapsCollection.unregister(e)}unregisterAll(){this.trimmingMapsCollection.unregisterAll(),this.hidingMapsCollection.unregisterAll(),this.variousMapsCollection.unregisterAll()}getPhysicalFromVisualIndex(e){const t=this.notTrimmedIndexesCache[e];return Gd(t)?t:null}getPhysicalFromRenderableIndex(e){const t=this.renderablePhysicalIndexesCache[e];return Gd(t)?t:null}getVisualFromPhysicalIndex(e){const t=this.fromPhysicalToVisualIndexesCache.get(e);return Gd(t)?t:null}getVisualFromRenderableIndex(e){return this.getVisualFromPhysicalIndex(this.getPhysicalFromRenderableIndex(e))}getRenderableFromVisualIndex(e){const t=this.fromVisualToRenderableIndexesCache.get(e);return Gd(t)?t:null}getNearestNotHiddenIndex(e,t){let o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(null===this.getPhysicalFromVisualIndex(e))return null;if(this.fromVisualToRenderableIndexesCache.has(e))return e;const r=Array.from(this.fromVisualToRenderableIndexesCache.keys());let i=-1;return i=t>0?r.findIndex((t=>t>e)):r.reverse().findIndex((t=>t<e)),-1===i?o?this.getNearestNotHiddenIndex(e,-t,!1):null:r[i]}initToLength(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getNumberOfIndexes();this.notTrimmedIndexesCache=[...new Array(e).keys()],this.notHiddenIndexesCache=[...new Array(e).keys()],this.suspendOperations(),this.indexesChangeSource="init",this.indexesSequence.init(e),this.indexesChangeSource=void 0,this.trimmingMapsCollection.initEvery(e),this.resumeOperations(),this.suspendOperations(),this.hidingMapsCollection.initEvery(e),this.variousMapsCollection.initEvery(e),this.resumeOperations(),this.runLocalHooks("init")}fitToLength(e){const t=this.getNumberOfIndexes();if(e<t){const t=[...Array(this.getNumberOfIndexes()-e).keys()].map((t=>t+e));this.removeIndexes(t)}else this.insertIndexes(t,e-t)}getIndexesSequence(){return this.indexesSequence.getValues()}setIndexesSequence(e){void 0===this.indexesChangeSource&&(this.indexesChangeSource="update"),this.indexesSequence.setValues(e),"update"===this.indexesChangeSource&&(this.indexesChangeSource=void 0)}getNotTrimmedIndexes(){return!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])?this.notTrimmedIndexesCache:this.getIndexesSequence().filter((e=>!1===this.isTrimmed(e)))}getNotTrimmedIndexesLength(){return this.getNotTrimmedIndexes().length}getNotHiddenIndexes(){return!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])?this.notHiddenIndexesCache:this.getIndexesSequence().filter((e=>!1===this.isHidden(e)))}getNotHiddenIndexesLength(){return this.getNotHiddenIndexes().length}getRenderableIndexes(){return!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])?this.renderablePhysicalIndexesCache:this.getNotTrimmedIndexes().filter((e=>!1===this.isHidden(e)))}getRenderableIndexesLength(){return this.getRenderableIndexes().length}getNumberOfIndexes(){return this.getIndexesSequence().length}moveIndexes(e,t){"number"==typeof e&&(e=[e]);const o=Wd(e,(e=>this.getPhysicalFromVisualIndex(e))),r=this.getNotTrimmedIndexesLength(),i=e.length,n=hR(this.getIndexesSequence(),o),s=n.filter((e=>!1===this.isTrimmed(e)));let l=n.indexOf(s[s.length-1])+1;if(t+i<r){const e=s[t];l=n.indexOf(e)}this.indexesChangeSource="move",this.setIndexesSequence(aR(n,l,o)),this.indexesChangeSource=void 0}isTrimmed(e){return this.trimmingMapsCollection.getMergedValueAtIndex(e)}isHidden(e){return this.hidingMapsCollection.getMergedValueAtIndex(e)}insertIndexes(e,t){const o=this.getNotTrimmedIndexes()[e],r=Gd(o)?o:this.getNumberOfIndexes(),i=this.getIndexesSequence().includes(o)?this.getIndexesSequence().indexOf(o):this.getNumberOfIndexes(),n=Wd(new Array(t).fill(r),((e,t)=>e+t));this.suspendOperations(),this.indexesChangeSource="insert",this.indexesSequence.insert(i,n),this.indexesChangeSource=void 0,this.trimmingMapsCollection.insertToEvery(i,n),this.hidingMapsCollection.insertToEvery(i,n),this.variousMapsCollection.insertToEvery(i,n),this.resumeOperations()}removeIndexes(e){this.suspendOperations(),this.indexesChangeSource="remove",this.indexesSequence.remove(e),this.indexesChangeSource=void 0,this.trimmingMapsCollection.removeFromEvery(e),this.hidingMapsCollection.removeFromEvery(e),this.variousMapsCollection.removeFromEvery(e),this.resumeOperations()}updateCache(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const t=this.indexesSequenceChanged||this.trimmedIndexesChanged||this.hiddenIndexesChanged;(!0===e||!1===this.isBatched&&!0===t)&&(this.trimmingMapsCollection.updateCache(),this.hidingMapsCollection.updateCache(),this.notTrimmedIndexesCache=this.getNotTrimmedIndexes(!1),this.notHiddenIndexesCache=this.getNotHiddenIndexes(!1),this.renderablePhysicalIndexesCache=this.getRenderableIndexes(!1),this.cacheFromPhysicalToVisualIndexes(),this.cacheFromVisualToRenderableIndexes(),this.hiddenIndexesChanged&&this.hidingChangesObservable.emit(this.hidingMapsCollection.getMergedValues()),this.runLocalHooks("cacheUpdated",{indexesSequenceChanged:this.indexesSequenceChanged,trimmedIndexesChanged:this.trimmedIndexesChanged,hiddenIndexesChanged:this.hiddenIndexesChanged}),this.indexesSequenceChanged=!1,this.trimmedIndexesChanged=!1,this.hiddenIndexesChanged=!1)}cacheFromPhysicalToVisualIndexes(){const e=this.getNotTrimmedIndexesLength();this.fromPhysicalToVisualIndexesCache.clear();for(let t=0;t<e;t+=1){const e=this.getPhysicalFromVisualIndex(t);this.fromPhysicalToVisualIndexesCache.set(e,t)}}cacheFromVisualToRenderableIndexes(){const e=this.getRenderableIndexesLength();this.fromVisualToRenderableIndexesCache.clear();for(let t=0;t<e;t+=1){const e=this.getPhysicalFromRenderableIndex(t),o=this.getVisualFromPhysicalIndex(e);this.fromVisualToRenderableIndexesCache.set(o,t)}}}function LR(e){const t=/^([a-zA-Z]{2})-([a-zA-Z]{2})$/.exec(e);return t?`${t[1].toLowerCase()}-${t[2].toUpperCase()}`:e}function DR(e){Gd(e)&&function(){Gd(console)&&console.error(...arguments)}(Ud`Language with code "${e}" was not found. You should register particular language\x20
    before using it. Read more about this issue at: https://docs.handsontable.com/i18n/missing-language-code.`)}Vf(VR,Lb);const{register:FR,getValues:BR}=Km("phraseFormatters");FR("pluralize",(function(e,t){return Array.isArray(e)&&Number.isInteger(t)?e[t]:e}));const WR="ContextMenu:items",jR=`${WR}.noItems`,zR=`${WR}.insertRowAbove`,$R=`${WR}.insertRowBelow`,UR=`${WR}.insertColumnOnTheLeft`,XR=`${WR}.insertColumnOnTheRight`,GR=`${WR}.removeRow`,YR=`${WR}.removeColumn`,KR=`${WR}.undo`,qR=`${WR}.redo`,QR=`${WR}.readOnly`,JR=`${WR}.clearColumn`,ZR=`${WR}.copy`,eT=`${WR}.copyWithHeaders`,tT=`${WR}.copyWithGroupHeaders`,oT=`${WR}.copyHeadersOnly`,rT=`${WR}.cut`,iT=`${WR}.freezeColumn`,nT=`${WR}.unfreezeColumn`,sT=`${WR}.mergeCells`,lT=`${WR}.unmergeCells`,aT=`${WR}.addComment`,hT=`${WR}.editComment`,cT=`${WR}.removeComment`,uT=`${WR}.readOnlyComment`,dT=`${WR}.align`,gT=`${WR}.align.left`,fT=`${WR}.align.center`,mT=`${WR}.align.right`,pT=`${WR}.align.justify`,wT=`${WR}.align.top`,yT=`${WR}.align.middle`,vT=`${WR}.align.bottom`,bT=`${WR}.borders`,CT=`${WR}.borders.top`,ST=`${WR}.borders.right`,RT=`${WR}.borders.bottom`,TT=`${WR}.borders.left`,ET=`${WR}.borders.remove`,HT=`${WR}.nestedHeaders.insertChildRow`,OT=`${WR}.nestedHeaders.detachFromParent`,MT=`${WR}.hideColumn`,xT=`${WR}.showColumn`,IT=`${WR}.hideRow`,NT=`${WR}.showRow`,AT="Filters:",kT=`${AT}conditions`,_T=`${kT}.none`,PT=`${kT}.isEmpty`,VT=`${kT}.isNotEmpty`,LT=`${kT}.isEqualTo`,DT=`${kT}.isNotEqualTo`,FT=`${kT}.beginsWith`,BT=`${kT}.endsWith`,WT=`${kT}.contains`,jT=`${kT}.doesNotContain`,zT=`${kT}.byValue`,$T=`${kT}.greaterThan`,UT=`${kT}.greaterThanOrEqualTo`,XT=`${kT}.lessThan`,GT=`${kT}.lessThanOrEqualTo`,YT=`${kT}.isBetween`,KT=`${kT}.isNotBetween`,qT=`${kT}.after`,QT=`${kT}.before`,JT=`${kT}.today`,ZT=`${kT}.tomorrow`,eE=`${kT}.yesterday`,tE=`${AT}labels.filterByCondition`,oE=`${AT}labels.filterByValue`,rE=`${AT}labels.conjunction`,iE=`${AT}labels.disjunction`,nE=`${AT}values.blankCells`,sE=`${AT}buttons.selectAll`,lE=`${AT}buttons.clear`,aE=`${AT}buttons.ok`,hE=`${AT}buttons.cancel`,cE=`${AT}buttons.placeholder.search`,uE=`${AT}buttons.placeholder.value`,dE=`${AT}buttons.placeholder.secondValue`,gE="CheckboxRenderer:",fE=`${gE}checked`,mE=`${gE}unchecked`,pE=Object.freeze(Object.defineProperty({__proto__:null,CHECKBOX_CHECKED:fE,CHECKBOX_RENDERER_NAMESPACE:gE,CHECKBOX_UNCHECKED:mE,CONTEXTMENU_ITEMS_ADD_COMMENT:aT,CONTEXTMENU_ITEMS_ALIGNMENT:dT,CONTEXTMENU_ITEMS_ALIGNMENT_BOTTOM:vT,CONTEXTMENU_ITEMS_ALIGNMENT_CENTER:fT,CONTEXTMENU_ITEMS_ALIGNMENT_JUSTIFY:pT,CONTEXTMENU_ITEMS_ALIGNMENT_LEFT:gT,CONTEXTMENU_ITEMS_ALIGNMENT_MIDDLE:yT,CONTEXTMENU_ITEMS_ALIGNMENT_RIGHT:mT,CONTEXTMENU_ITEMS_ALIGNMENT_TOP:wT,CONTEXTMENU_ITEMS_BORDERS:bT,CONTEXTMENU_ITEMS_BORDERS_BOTTOM:RT,CONTEXTMENU_ITEMS_BORDERS_LEFT:TT,CONTEXTMENU_ITEMS_BORDERS_RIGHT:ST,CONTEXTMENU_ITEMS_BORDERS_TOP:CT,CONTEXTMENU_ITEMS_CLEAR_COLUMN:JR,CONTEXTMENU_ITEMS_COPY:ZR,CONTEXTMENU_ITEMS_COPY_COLUMN_HEADERS_ONLY:oT,CONTEXTMENU_ITEMS_COPY_WITH_COLUMN_GROUP_HEADERS:tT,CONTEXTMENU_ITEMS_COPY_WITH_COLUMN_HEADERS:eT,CONTEXTMENU_ITEMS_CUT:rT,CONTEXTMENU_ITEMS_EDIT_COMMENT:hT,CONTEXTMENU_ITEMS_FREEZE_COLUMN:iT,CONTEXTMENU_ITEMS_HIDE_COLUMN:MT,CONTEXTMENU_ITEMS_HIDE_ROW:IT,CONTEXTMENU_ITEMS_INSERT_LEFT:UR,CONTEXTMENU_ITEMS_INSERT_RIGHT:XR,CONTEXTMENU_ITEMS_MERGE_CELLS:sT,CONTEXTMENU_ITEMS_NESTED_ROWS_DETACH_CHILD:OT,CONTEXTMENU_ITEMS_NESTED_ROWS_INSERT_CHILD:HT,CONTEXTMENU_ITEMS_NO_ITEMS:jR,CONTEXTMENU_ITEMS_READ_ONLY:QR,CONTEXTMENU_ITEMS_READ_ONLY_COMMENT:uT,CONTEXTMENU_ITEMS_REDO:qR,CONTEXTMENU_ITEMS_REMOVE_BORDERS:ET,CONTEXTMENU_ITEMS_REMOVE_COLUMN:YR,CONTEXTMENU_ITEMS_REMOVE_COMMENT:cT,CONTEXTMENU_ITEMS_REMOVE_ROW:GR,CONTEXTMENU_ITEMS_ROW_ABOVE:zR,CONTEXTMENU_ITEMS_ROW_BELOW:$R,CONTEXTMENU_ITEMS_SHOW_COLUMN:xT,CONTEXTMENU_ITEMS_SHOW_ROW:NT,CONTEXTMENU_ITEMS_UNDO:KR,CONTEXTMENU_ITEMS_UNFREEZE_COLUMN:nT,CONTEXTMENU_ITEMS_UNMERGE_CELLS:lT,CONTEXT_MENU_ITEMS_NAMESPACE:WR,FILTERS_BUTTONS_CANCEL:hE,FILTERS_BUTTONS_CLEAR:lE,FILTERS_BUTTONS_OK:aE,FILTERS_BUTTONS_PLACEHOLDER_SEARCH:cE,FILTERS_BUTTONS_PLACEHOLDER_SECOND_VALUE:dE,FILTERS_BUTTONS_PLACEHOLDER_VALUE:uE,FILTERS_BUTTONS_SELECT_ALL:sE,FILTERS_CONDITIONS_AFTER:qT,FILTERS_CONDITIONS_BEFORE:QT,FILTERS_CONDITIONS_BEGINS_WITH:FT,FILTERS_CONDITIONS_BETWEEN:YT,FILTERS_CONDITIONS_BY_VALUE:zT,FILTERS_CONDITIONS_CONTAINS:WT,FILTERS_CONDITIONS_EMPTY:PT,FILTERS_CONDITIONS_ENDS_WITH:BT,FILTERS_CONDITIONS_EQUAL:LT,FILTERS_CONDITIONS_GREATER_THAN:$T,FILTERS_CONDITIONS_GREATER_THAN_OR_EQUAL:UT,FILTERS_CONDITIONS_LESS_THAN:XT,FILTERS_CONDITIONS_LESS_THAN_OR_EQUAL:GT,FILTERS_CONDITIONS_NAMESPACE:kT,FILTERS_CONDITIONS_NONE:_T,FILTERS_CONDITIONS_NOT_BETWEEN:KT,FILTERS_CONDITIONS_NOT_CONTAIN:jT,FILTERS_CONDITIONS_NOT_EMPTY:VT,FILTERS_CONDITIONS_NOT_EQUAL:DT,FILTERS_CONDITIONS_TODAY:JT,FILTERS_CONDITIONS_TOMORROW:ZT,FILTERS_CONDITIONS_YESTERDAY:eE,FILTERS_DIVS_FILTER_BY_CONDITION:tE,FILTERS_DIVS_FILTER_BY_VALUE:oE,FILTERS_LABELS_CONJUNCTION:rE,FILTERS_LABELS_DISJUNCTION:iE,FILTERS_NAMESPACE:AT,FILTERS_VALUES_BLANK_CELLS:nE},Symbol.toStringTag,{value:"Module"})),wE={languageCode:"en-US",[jR]:"No available options",[zR]:"Insert row above",[$R]:"Insert row below",[UR]:"Insert column left",[XR]:"Insert column right",[GR]:["Remove row","Remove rows"],[YR]:["Remove column","Remove columns"],[KR]:"Undo",[qR]:"Redo",[QR]:"Read only",[JR]:"Clear column",[dT]:"Alignment",[gT]:"Left",[fT]:"Center",[mT]:"Right",[pT]:"Justify",[wT]:"Top",[yT]:"Middle",[vT]:"Bottom",[iT]:"Freeze column",[nT]:"Unfreeze column",[bT]:"Borders",[CT]:"Top",[ST]:"Right",[RT]:"Bottom",[TT]:"Left",[ET]:"Remove border(s)",[aT]:"Add comment",[hT]:"Edit comment",[cT]:"Delete comment",[uT]:"Read-only comment",[sT]:"Merge cells",[lT]:"Unmerge cells",[ZR]:"Copy",[eT]:["Copy with header","Copy with headers"],[tT]:["Copy with group header","Copy with group headers"],[oT]:["Copy header only","Copy headers only"],[rT]:"Cut",[HT]:"Insert child row",[OT]:"Detach from parent",[MT]:["Hide column","Hide columns"],[xT]:["Show column","Show columns"],[IT]:["Hide row","Hide rows"],[NT]:["Show row","Show rows"],[_T]:"None",[PT]:"Is empty",[VT]:"Is not empty",[LT]:"Is equal to",[DT]:"Is not equal to",[FT]:"Begins with",[BT]:"Ends with",[WT]:"Contains",[jT]:"Does not contain",[$T]:"Greater than",[UT]:"Greater than or equal to",[XT]:"Less than",[GT]:"Less than or equal to",[YT]:"Is between",[KT]:"Is not between",[qT]:"After",[QT]:"Before",[JT]:"Today",[ZT]:"Tomorrow",[eE]:"Yesterday",[nE]:"Blank cells",[tE]:"Filter by condition",[oE]:"Filter by value",[rE]:"And",[iE]:"Or",[sE]:"Select all",[lE]:"Clear",[aE]:"OK",[hE]:"Cancel",[cE]:"Search",[uE]:"Value",[dE]:"Second value",[fE]:"Checked",[mE]:"Unchecked"},yE=pE,vE=wE.languageCode,{register:bE,getItem:CE,hasItem:SE,getValues:RE}=Km("languagesDictionaries");function TE(e,t){let o=e,r=t;return Df(e)&&(r=e,o=r.languageCode),function(e,t){var o;e!==vE&&(o=t,Bf(CE(vE),((e,t)=>{Yd(o[t])&&(o[t]=e)})))}(o,r),bE(o,Pf(r)),Pf(r)}function EE(e){return HE(e)?Pf(CE(e)):null}function HE(e){return SE(e)}function OE(e,t,o){const r=EE(e);if(null===r)return null;const i=r[t];if(Yd(i))return null;const n=function(e,t){let o=e;return jd(BR(),(r=>{o=r(e,t)})),o}(i,o);return Array.isArray(n)?n[0]:n}function ME(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}TE(wE);const xE=class extends Fb{constructor(e,t){super(e,null),ME(this,"visualCellRange",null),this.visualCellRange=t||null,this.commit()}add(e){return null===this.visualCellRange?this.visualCellRange=this.settings.createCellRange(e):this.visualCellRange.expand(e),this}clear(){return this.visualCellRange=null,super.clear()}trimToVisibleCellsRangeOnly(e){let{from:t,to:o}=e,r=this.getNearestNotHiddenCoords(t,1),i=this.getNearestNotHiddenCoords(o,-1);return null===r||null===i?null:((r.row>i.row||r.col>i.col)&&(r=t,i=o),this.settings.createCellRange(r,r,i))}getNearestNotHiddenCoords(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t;const r=this.getNearestNotHiddenIndex(this.settings.rowIndexMapper,e.row,t);if(null===r)return null;const i=this.getNearestNotHiddenIndex(this.settings.columnIndexMapper,e.col,o);return null===i?null:this.settings.createCellCoords(r,i)}getNearestNotHiddenIndex(e,t,o){return t<0?t:e.getNearestNotHiddenIndex(t,o)}commit(){if(null===this.visualCellRange)return this;const e=this.trimToVisibleCellsRangeOnly(this.visualCellRange);return this.cellRange=null===e?null:this.createRenderableCellRange(e.from,e.to),this}syncWith(e){const t=e.from.clone().normalize(),o="N-S"===e.getVerticalDirection()?1:-1,r="W-E"===e.getHorizontalDirection()?1:-1,i=this.settings.visualToRenderableCoords(this.visualCellRange.highlight);let n=null;if(null!==i&&null!==i.col&&null!==i.row||(n=this.getNearestNotHiddenCoords(t,o,r)),null!==n&&e.overlaps(n)){const t=e.highlight.clone();if(t.row>=0&&(t.row=n.row),t.col>=0&&(t.col=n.col),null===this.cellRange){const e=this.settings.visualToRenderableCoords(t);this.cellRange=this.settings.createCellRange(e)}e.setHighlight(t)}return"focus"===this.settings.selectionType&&null!==i&&null===n&&e.setHighlight(this.visualCellRange.highlight),this}getCorners(){const{from:e,to:t}=this.cellRange;return[Math.min(e.row,t.row),Math.min(e.col,t.col),Math.max(e.row,t.row),Math.max(e.col,t.col)]}getVisualCorners(){const e=this.settings.renderableToVisualCoords(this.cellRange.getTopStartCorner()),t=this.settings.renderableToVisualCoords(this.cellRange.getBottomEndCorner());return[e.row,e.col,t.row,t.col]}createRenderableCellRange(e,t){const o=this.settings.visualToRenderableCoords(e),r=this.settings.visualToRenderableCoords(t);return null===o.row||null===o.col||null===r.row||null===r.col?null:this.settings.createCellRange(o,o,r)}};function IE(e){let{activeHeaderClassName:t,...o}=e;return new xE({className:t,...o,selectionType:"active-header"})}function NE(e){let{areaCornerVisible:t,...o}=e;return new xE({className:"area",createLayers:!0,border:{width:1,color:"#4b89ff",cornerVisible:t},...o,selectionType:Wb})}function AE(e){let{...t}=e;return new xE({className:"highlight",...t,selectionType:Wb})}function kE(e){let{columnClassName:t,...o}=e;return new xE({className:t,...o,selectionType:"column"})}function _E(e){let{headerClassName:t,...o}=e;return new xE({className:t,...o,selectionType:Bb})}function PE(e){let{rowClassName:t,...o}=e;return new xE({className:t,...o,selectionType:"row"})}function VE(e,t){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.add(e)}function LE(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function DE(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var FE=new WeakSet;class BE{constructor(e){VE(this,FE),LE(this,"options",void 0),LE(this,"layerLevel",0),LE(this,"focus",void 0),LE(this,"fill",void 0),LE(this,"layeredAreas",new Map),LE(this,"areas",new Map),LE(this,"rowHeaders",new Map),LE(this,"columnHeaders",new Map),LE(this,"activeRowHeaders",new Map),LE(this,"activeColumnHeaders",new Map),LE(this,"activeCornerHeaders",new Map),LE(this,"rowHighlights",new Map),LE(this,"columnHighlights",new Map),LE(this,"customSelections",[]),this.options=e,this.focus=function(e){let{cellCornerVisible:t,...o}=e;return new xE({className:"current",headerAttributes:[["aria-selected","true"]],border:{width:2,color:"#4b89ff",cornerVisible:t},...o,selectionType:jb})}(e),this.fill=function(e){let{...t}=e;return new xE({className:"fill",border:{width:1,color:"#ff0000"},...t,selectionType:"fill"})}(e)}isEnabledFor(e,t){let o=e;e===jb&&(o="current");let r=this.options.disabledCellSelection(t.row,t.col);return"string"==typeof r&&(r=[r]),!1===r||Array.isArray(r)&&!r.includes(o)}useLayerLevel(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.layerLevel=e,this}getFocus(){return this.focus}getFill(){return this.fill}createLayeredArea(){return DE(FE,this,WE).call(this,this.layeredAreas,NE)}getLayeredAreas(){return[...this.layeredAreas.values()]}createArea(){return DE(FE,this,WE).call(this,this.areas,AE)}getAreas(){return[...this.areas.values()]}createRowHeader(){return DE(FE,this,WE).call(this,this.rowHeaders,_E)}getRowHeaders(){return[...this.rowHeaders.values()]}createColumnHeader(){return DE(FE,this,WE).call(this,this.columnHeaders,_E)}getColumnHeaders(){return[...this.columnHeaders.values()]}createActiveRowHeader(){return DE(FE,this,WE).call(this,this.activeRowHeaders,IE)}getActiveRowHeaders(){return[...this.activeRowHeaders.values()]}createActiveColumnHeader(){return DE(FE,this,WE).call(this,this.activeColumnHeaders,IE)}getActiveColumnHeaders(){return[...this.activeColumnHeaders.values()]}createActiveCornerHeader(){return DE(FE,this,WE).call(this,this.activeCornerHeaders,IE)}getActiveCornerHeaders(){return[...this.activeCornerHeaders.values()]}createRowHighlight(){return DE(FE,this,WE).call(this,this.rowHighlights,PE)}getRowHighlights(){return[...this.rowHighlights.values()]}createColumnHighlight(){return DE(FE,this,WE).call(this,this.columnHighlights,kE)}getColumnHighlights(){return[...this.columnHighlights.values()]}getCustomSelections(){return[...this.customSelections.values()]}addCustomSelection(e){this.customSelections.push(function(e){let{border:t,visualCellRange:o,...r}=e;return new xE({...t,...r,selectionType:"custom-selection"},o)}({...this.options,...e}))}clear(){this.focus.clear(),this.fill.clear(),jd(this.areas.values(),(e=>{e.clear()})),jd(this.layeredAreas.values(),(e=>{e.clear()})),jd(this.rowHeaders.values(),(e=>{e.clear()})),jd(this.columnHeaders.values(),(e=>{e.clear()})),jd(this.activeRowHeaders.values(),(e=>{e.clear()})),jd(this.activeColumnHeaders.values(),(e=>{e.clear()})),jd(this.activeCornerHeaders.values(),(e=>{e.clear()})),jd(this.rowHighlights.values(),(e=>{e.clear()})),jd(this.columnHighlights.values(),(e=>{e.clear()}))}[Symbol.iterator](){return[this.focus,this.fill,...this.areas.values(),...this.layeredAreas.values(),...this.rowHeaders.values(),...this.columnHeaders.values(),...this.activeRowHeaders.values(),...this.activeColumnHeaders.values(),...this.activeCornerHeaders.values(),...this.rowHighlights.values(),...this.columnHighlights.values(),...this.customSelections][Symbol.iterator]()}}function WE(e,t){const o=this.layerLevel;if(e.has(o))return e.get(o);const r=t({layerLevel:o,...this.options});return e.set(o,r),r}const jE=BE;function zE(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class $E{constructor(e){zE(this,"ranges",[]),zE(this,"createCellRange",void 0),this.createCellRange=e}isEmpty(){return 0===this.size()}set(e){return this.clear(),this.ranges.push(this.createCellRange(e)),this}add(e){return this.ranges.push(this.createCellRange(e)),this}pop(){return this.ranges.pop(),this}current(){return this.peekByIndex(this.size()-1)}previous(){return this.peekByIndex(this.size()-2)}includes(e){return this.ranges.some((t=>t.includes(e)))}clear(){return this.ranges.length=0,this}size(){return this.ranges.length}peekByIndex(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return t>=0&&t<this.size()&&(e=this.ranges[t]),e}[Symbol.iterator](){return this.ranges[Symbol.iterator]()}}const UE=$E;function XE(e,t,o){GE(e,t),t.set(e,o)}function GE(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function YE(e,t){return e.get(qE(e,t))}function KE(e,t,o){return e.set(qE(e,t),o),o}function qE(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var QE=new WeakMap,JE=new WeakMap,ZE=new WeakMap,eH=new WeakSet;class tH{constructor(e,t){var o,r;GE(o=this,r=eH),r.add(o),XE(this,QE,void 0),XE(this,JE,void 0),XE(this,ZE,{x:0,y:0}),KE(QE,this,e),KE(JE,this,t)}transformStart(e,t){let o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=YE(JE,this).createCellCoords(e,t);let i=YE(QE,this).current().highlight;const n=YE(JE,this).visualToRenderableCoords(i);let s=0,l=0;if(this.runLocalHooks("beforeTransformStart",r),null!==n.row&&null!==n.col){const{width:e,height:t}=qE(eH,this,rH).call(this),{row:n,col:a}=qE(eH,this,sH).call(this,i),h=YE(JE,this).fixedRowsBottom(),c=YE(JE,this).minSpareRows(),u=YE(JE,this).minSpareCols(),d=YE(JE,this).autoWrapRow(),g=YE(JE,this).autoWrapCol(),f=YE(JE,this).createCellCoords(n+r.row,a+r.col);if(f.row>=t){const r=zf(o&&c>0&&0===h),i=f.col+1,n=YE(JE,this).createCellCoords(f.row-t,i>=e?i-e:i);this.runLocalHooks("beforeColumnWrap",r,qE(eH,this,lH).call(this,n),i>=e),r.value?this.runLocalHooks("insertRowRequire",YE(JE,this).countRenderableRows()):g&&f.assign(n)}else if(f.row<0){const o=zf(g),r=f.col-1,i=YE(JE,this).createCellCoords(t+f.row,r<0?e+r:r);this.runLocalHooks("beforeColumnWrap",o,qE(eH,this,lH).call(this,i),r<0),g&&f.assign(i)}if(f.col>=e){const r=zf(o&&u>0),i=f.row+1,n=YE(JE,this).createCellCoords(i>=t?i-t:i,f.col-e);this.runLocalHooks("beforeRowWrap",r,qE(eH,this,lH).call(this,n),i>=t),r.value?this.runLocalHooks("insertColRequire",YE(JE,this).countRenderableColumns()):d&&f.assign(n)}else if(f.col<0){const o=zf(d),r=f.row-1,i=YE(JE,this).createCellCoords(r<0?t+r:r,e+f.col);this.runLocalHooks("beforeRowWrap",o,qE(eH,this,lH).call(this,i),r<0),d&&f.assign(i)}const{rowDir:m,colDir:p}=qE(eH,this,oH).call(this,f);s=m,l=p,i=qE(eH,this,lH).call(this,f)}return this.runLocalHooks("afterTransformStart",i,s,l),i}transformEnd(e,t){const o=YE(JE,this).createCellCoords(e,t),r=YE(QE,this).current(),i=YE(JE,this).visualToRenderableCoords(r.highlight),n=qE(eH,this,iH).call(this,r.to.row,r.from.row),s=qE(eH,this,nH).call(this,r.to.col,r.from.col),l=r.to.clone();let a=0,h=0;if(this.runLocalHooks("beforeTransformEnd",o),null!==i.row&&null!==i.col&&null!==n&&null!==s){const{row:e,col:t}=qE(eH,this,sH).call(this,r.highlight),i=YE(JE,this).createCellCoords(n+o.row,s+o.col),c=r.getTopStartCorner(),u=r.getTopEndCorner(),d=r.getBottomEndCorner();if(o.col<0&&s>=t&&i.col<t){const e=i.col-t;i.col=qE(eH,this,nH).call(this,c.col,u.col)+e}else if(o.col>0&&s<=t&&i.col>t){const e=qE(eH,this,nH).call(this,u.col,c.col),t=Math.max(i.col-e,1);i.col=e+t}if(o.row<0&&n>=e&&i.row<e){const t=i.row-e;i.row=qE(eH,this,iH).call(this,c.row,d.row)+t}else if(o.row>0&&n<=e&&i.row>e){const e=qE(eH,this,iH).call(this,d.row,c.row),t=Math.max(i.row-e,1);i.row=e+t}const{rowDir:g,colDir:f}=qE(eH,this,oH).call(this,i);a=g,h=f;const m=qE(eH,this,lH).call(this,i);0===o.row&&0!==o.col?l.col=m.col:0!==o.row&&0===o.col?l.row=m.row:(l.row=m.row,l.col=m.col)}return this.runLocalHooks("afterTransformEnd",l,a,h),l}setOffsetSize(e){let{x:t,y:o}=e;KE(ZE,this,{x:t,y:o})}resetOffsetSize(){KE(ZE,this,{x:0,y:0})}}function oH(e){const{width:t,height:o}=qE(eH,this,rH).call(this);let r=0,i=0;return e.row<0?(r=-1,e.row=0):e.row>0&&e.row>=o&&(r=1,e.row=o-1),e.col<0?(i=-1,e.col=0):e.col>0&&e.col>=t&&(i=1,e.col=t-1),{rowDir:r,colDir:i}}function rH(){return{width:YE(ZE,this).x+YE(JE,this).countRenderableColumns(),height:YE(ZE,this).y+YE(JE,this).countRenderableRows()}}function iH(e,t){const o=YE(JE,this).findFirstNonHiddenRenderableRow(e,t);return null===o?null:YE(ZE,this).y+o}function nH(e,t){const o=YE(JE,this).findFirstNonHiddenRenderableColumn(e,t);return null===o?null:YE(ZE,this).x+o}function sH(e){const{row:t,col:o}=YE(JE,this).visualToRenderableCoords(e);if(null===t||null===o)throw new Error("Renderable coords are not visible.");return YE(JE,this).createCellCoords(YE(ZE,this).y+t,YE(ZE,this).x+o)}function lH(e){const t=e.clone();return t.col=e.col-YE(ZE,this).x,t.row=e.row-YE(ZE,this).y,YE(JE,this).renderableToVisualCoords(t)}Vf(tH,Lb);const aH=tH,hH=[3,2],cH=[["number"],["number","string"],["number","undefined"],["number","string","undefined"]],uH=Symbol("root"),dH=Symbol("child");function gH(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:uH;if(t!==uH&&t!==dH)throw new Error("The second argument is used internally only and cannot be overwritten.");const o=Array.isArray(e),r=t===uH;let i=0;if(o){const t=e[0];if(0===e.length)i=1;else if(r&&t instanceof jy)i=3;else if(r&&Array.isArray(t))i=gH(t,dH);else if(e.length>=2&&e.length<=4){const t=!e.some(((e,t)=>!cH[t].includes(typeof e)));t&&(i=2)}}return i}function fH(e,t,o){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,o)}function mH(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function pH(e,t){return e.get(yH(e,t))}function wH(e,t,o){return e.set(yH(e,t),o),o}function yH(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}var vH=new WeakMap,bH=new WeakMap,CH=new WeakMap,SH=new WeakMap,RH=new WeakMap,TH=new WeakMap;class EH{constructor(e,t){var o=this;mH(this,"settings",void 0),mH(this,"tableProps",void 0),mH(this,"inProgress",!1),mH(this,"selectedRange",new UE(((e,t,o)=>this.tableProps.createCellRange(e,t,o)))),mH(this,"highlight",void 0),fH(this,vH,void 0),fH(this,bH,void 0),mH(this,"selectedByRowHeader",new Set),mH(this,"selectedByColumnHeader",new Set),fH(this,CH,!1),fH(this,SH,!1),fH(this,RH,"unknown"),fH(this,TH,-1),this.settings=e,this.tableProps=t,this.highlight=new jE({headerClassName:e.currentHeaderClassName,activeHeaderClassName:e.activeHeaderClassName,rowClassName:e.currentRowClassName,columnClassName:e.currentColClassName,cellAttributes:[["aria-selected","true"]],rowIndexMapper:this.tableProps.rowIndexMapper,columnIndexMapper:this.tableProps.columnIndexMapper,disabledCellSelection:(e,t)=>this.tableProps.isDisabledCellSelection(e,t),cellCornerVisible:function(){return o.isCellCornerVisible(...arguments)},areaCornerVisible:function(){return o.isAreaCornerVisible(...arguments)},visualToRenderableCoords:e=>this.tableProps.visualToRenderableCoords(e),renderableToVisualCoords:e=>this.tableProps.renderableToVisualCoords(e),createCellCoords:(e,t)=>this.tableProps.createCellCoords(e,t),createCellRange:(e,t,o)=>this.tableProps.createCellRange(e,t,o)}),wH(vH,this,new aH(this.selectedRange,{rowIndexMapper:this.tableProps.rowIndexMapper,columnIndexMapper:this.tableProps.columnIndexMapper,countRenderableRows:()=>this.tableProps.countRenderableRows(),countRenderableColumns:()=>this.tableProps.countRenderableColumns(),visualToRenderableCoords:e=>this.tableProps.visualToRenderableCoords(e),renderableToVisualCoords:e=>this.tableProps.renderableToVisualCoords(e),findFirstNonHiddenRenderableRow:function(){return o.tableProps.findFirstNonHiddenRenderableRow(...arguments)},findFirstNonHiddenRenderableColumn:function(){return o.tableProps.findFirstNonHiddenRenderableColumn(...arguments)},createCellCoords:(e,t)=>this.tableProps.createCellCoords(e,t),fixedRowsBottom:()=>e.fixedRowsBottom,minSpareRows:()=>e.minSpareRows,minSpareCols:()=>e.minSpareCols,autoWrapRow:()=>e.autoWrapRow,autoWrapCol:()=>e.autoWrapCol})),wH(bH,this,new aH(this.selectedRange,{rowIndexMapper:this.tableProps.rowIndexMapper,columnIndexMapper:this.tableProps.columnIndexMapper,countRenderableRows:()=>{const e=this.selectedRange.current();return this.tableProps.countRenderableRowsInRange(0,e.getOuterBottomEndCorner().row)},countRenderableColumns:()=>{const e=this.selectedRange.current();return this.tableProps.countRenderableColumnsInRange(0,e.getOuterBottomEndCorner().col)},visualToRenderableCoords:e=>this.tableProps.visualToRenderableCoords(e),renderableToVisualCoords:e=>this.tableProps.renderableToVisualCoords(e),findFirstNonHiddenRenderableRow:function(){return o.tableProps.findFirstNonHiddenRenderableRow(...arguments)},findFirstNonHiddenRenderableColumn:function(){return o.tableProps.findFirstNonHiddenRenderableColumn(...arguments)},createCellCoords:(e,t)=>this.tableProps.createCellCoords(e,t),fixedRowsBottom:()=>0,minSpareRows:()=>0,minSpareCols:()=>0,autoWrapRow:()=>!0,autoWrapCol:()=>!0})),pH(vH,this).addLocalHook("beforeTransformStart",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return o.runLocalHooks("beforeModifyTransformStart",...t)})),pH(vH,this).addLocalHook("afterTransformStart",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return o.runLocalHooks("afterModifyTransformStart",...t)})),pH(vH,this).addLocalHook("beforeTransformEnd",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return o.runLocalHooks("beforeModifyTransformEnd",...t)})),pH(vH,this).addLocalHook("afterTransformEnd",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return o.runLocalHooks("afterModifyTransformEnd",...t)})),pH(vH,this).addLocalHook("insertRowRequire",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return o.runLocalHooks("insertRowRequire",...t)})),pH(vH,this).addLocalHook("insertColRequire",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return o.runLocalHooks("insertColRequire",...t)})),pH(vH,this).addLocalHook("beforeRowWrap",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return o.runLocalHooks("beforeRowWrap",...t)})),pH(vH,this).addLocalHook("beforeColumnWrap",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return o.runLocalHooks("beforeColumnWrap",...t)})),pH(bH,this).addLocalHook("beforeTransformStart",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return o.runLocalHooks("beforeModifyTransformFocus",...t)})),pH(bH,this).addLocalHook("afterTransformStart",(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return o.runLocalHooks("afterModifyTransformFocus",...t)}))}getSelectedRange(){return this.selectedRange}markSource(e){wH(RH,this,e)}markEndSource(){wH(RH,this,"unknown")}getSelectionSource(){return pH(RH,this)}setExpectedLayers(e){wH(TH,this,e)}begin(){this.inProgress=!0}finish(){this.runLocalHooks("afterSelectionFinished",Array.from(this.selectedRange)),this.inProgress=!1,wH(TH,this,-1)}isInProgress(){return this.inProgress}setRangeStart(e,t){let o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e;const i="multiple"===this.settings.selectionMode,n=Yd(t)?this.tableProps.getShortcutManager().isCtrlPressed():t,s=e.clone();wH(CH,this,!1),this.runLocalHooks("beforeSetRangeStart"+(o?"Only":""),s),(!i||i&&!n&&Yd(t))&&this.selectedRange.clear(),this.selectedRange.add(s).current().setHighlight(r.clone()),0===this.getLayerLevel()&&(this.selectedByRowHeader.clear(),this.selectedByColumnHeader.clear()),o||this.setRangeEnd(e)}setRangeStartOnly(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;this.setRangeStart(e,t,!0,o)}setRangeEnd(e){if(this.selectedRange.isEmpty())return;const t=e.clone(),o=this.tableProps.countRows(),r=this.tableProps.countCols(),i=this.selectedRange.current().clone().setTo(e).isSingleHeader();if((o>0||r>0)&&(0===o&&t.col<0&&!i||0===r&&t.row<0&&!i))return;this.runLocalHooks("beforeSetRangeEnd",t),this.begin();const n=this.selectedRange.current();if(this.settings.navigableHeaders||n.highlight.normalize(),"single"===this.settings.selectionMode)n.setFrom(n.highlight),n.setTo(n.highlight);else{const e=n.getHorizontalDirection(),o=n.getVerticalDirection(),r=this.isMultiple();n.setTo(t),r&&(e!==n.getHorizontalDirection()||1===n.getWidth()&&!n.includes(n.highlight))&&n.from.assign({col:n.highlight.col}),r&&(o!==n.getVerticalDirection()||1===n.getHeight()&&!n.includes(n.highlight))&&n.from.assign({row:n.highlight.row})}o>0&&r>0&&(!this.settings.navigableHeaders||this.settings.navigableHeaders&&!n.isSingleHeader())&&n.to.normalize(),this.runLocalHooks("beforeHighlightSet"),this.setRangeFocus(this.selectedRange.current().highlight),this.applyAndCommit();const s=-1===pH(TH,this)||this.selectedRange.size()===pH(TH,this);this.runLocalHooks("afterSetRangeEnd",e,s)}applyAndCommit(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.selectedRange.current(),t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLayerLevel();const o=this.tableProps.countRows(),r=this.tableProps.countCols();t<this.highlight.layerLevel&&(jd(this.highlight.getAreas(),(e=>{e.clear()})),jd(this.highlight.getLayeredAreas(),(e=>{e.clear()})),jd(this.highlight.getRowHeaders(),(e=>{e.clear()})),jd(this.highlight.getColumnHeaders(),(e=>{e.clear()})),jd(this.highlight.getActiveRowHeaders(),(e=>{e.clear()})),jd(this.highlight.getActiveColumnHeaders(),(e=>{e.clear()})),jd(this.highlight.getActiveCornerHeaders(),(e=>{e.clear()})),jd(this.highlight.getRowHighlights(),(e=>{e.clear()})),jd(this.highlight.getColumnHighlights(),(e=>{e.clear()}))),this.highlight.useLayerLevel(t);const i=this.highlight.createArea(),n=this.highlight.createLayeredArea(),s=this.highlight.createRowHeader(),l=this.highlight.createColumnHeader(),a=this.highlight.createActiveRowHeader(),h=this.highlight.createActiveColumnHeader(),c=this.highlight.createActiveCornerHeader(),u=this.highlight.createRowHighlight(),d=this.highlight.createColumnHighlight();if(i.clear(),n.clear(),s.clear(),l.clear(),a.clear(),h.clear(),c.clear(),u.clear(),d.clear(),this.highlight.isEnabledFor(Wb,e.highlight)&&(this.isMultiple()||t>=1)&&(i.add(e.from).add(e.to).commit(),n.add(e.from).add(e.to).commit(),1===t)){const e=this.selectedRange.previous();this.highlight.useLayerLevel(t-1),this.highlight.createArea().add(e.from).commit().syncWith(e),this.highlight.createLayeredArea().add(e.from).commit().syncWith(e),this.highlight.useLayerLevel(t)}if(this.highlight.isEnabledFor(Bb,e.highlight)){if(!e.isSingleHeader()){const t=this.tableProps.createCellCoords(Math.max(e.from.row,0),-1),o=this.tableProps.createCellCoords(e.to.row,-1),r=this.tableProps.createCellCoords(-1,Math.max(e.from.col,0)),i=this.tableProps.createCellCoords(-1,e.to.col);"single"===this.settings.selectionMode?(s.add(t).commit(),l.add(r).commit(),u.add(t).commit(),d.add(r).commit()):(s.add(t).add(o).commit(),l.add(r).add(i).commit(),u.add(t).add(o).commit(),d.add(r).add(i).commit())}const t=!pH(SH,this)&&this.isEntireRowSelected()&&(r>0&&r===e.getWidth()||0===r&&this.isSelectedByRowHeader()),i=!pH(SH,this)&&this.isEntireColumnSelected()&&(o>0&&o===e.getHeight()||0===o&&this.isSelectedByColumnHeader());t&&a.add(this.tableProps.createCellCoords(Math.max(e.from.row,0),Math.min(-this.tableProps.countRowHeaders(),-1))).add(this.tableProps.createCellCoords(Math.max(e.to.row,0),-1)).commit(),i&&h.add(this.tableProps.createCellCoords(Math.min(-this.tableProps.countColHeaders(),-1),Math.max(e.from.col,0))).add(this.tableProps.createCellCoords(-1,Math.max(e.to.col,0))).commit(),t&&i&&c.add(this.tableProps.createCellCoords(-this.tableProps.countColHeaders(),-this.tableProps.countRowHeaders())).add(this.tableProps.createCellCoords(-1,-1)).commit()}}setRangeFocus(e){if(this.selectedRange.isEmpty())return;const t=this.selectedRange.current();this.inProgress||this.runLocalHooks("beforeSetFocus",e);const o=this.highlight.getFocus();o.clear(),t.setHighlight(e),this.inProgress||this.runLocalHooks("beforeHighlightSet"),this.highlight.isEnabledFor(jb,t.highlight)&&o.add(t.highlight).commit().syncWith(t),this.inProgress||(wH(CH,this,!0),this.runLocalHooks("afterSetFocus",t.highlight))}transformStart(e,t){let o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.settings.navigableHeaders?pH(vH,this).setOffsetSize({x:this.tableProps.countRowHeaders(),y:this.tableProps.countColHeaders()}):pH(vH,this).resetOffsetSize(),this.setRangeStart(pH(vH,this).transformStart(e,t,o))}transformEnd(e,t){this.settings.navigableHeaders?pH(vH,this).setOffsetSize({x:this.tableProps.countRowHeaders(),y:this.tableProps.countColHeaders()}):pH(vH,this).resetOffsetSize(),this.setRangeEnd(pH(vH,this).transformEnd(e,t))}transformFocus(e,t){const o=this.selectedRange.current(),{row:r,col:i}=o.getOuterTopStartCorner(),n=this.tableProps.countRenderableColumnsInRange(0,i-1),s=this.tableProps.countRenderableRowsInRange(0,r-1);o.highlight.isHeader()?pH(bH,this).setOffsetSize({x:i<0?Math.abs(i):-n,y:r<0?Math.abs(r):-s}):pH(bH,this).setOffsetSize({x:i<0?0:-n,y:r<0?0:-s});const l=pH(bH,this).transformStart(e,t);this.setRangeFocus(l.normalize())}shiftRows(e,t){if(!this.isSelected())return;const o=this.selectedRange.current();if(this.isSelectedByCorner())this.selectAll(!0,!0,{disableHeadersHighlight:!0});else if(this.isSelectedByColumnHeader()||o.getOuterTopStartCorner().row>=e){const{from:r,to:i,highlight:n}=o,s=this.tableProps.countRows(),l=this.isSelectedByRowHeader(),a=this.isSelectedByColumnHeader(),h=a?-1:0,c=a?0:t;this.getSelectedRange().pop();const u=this.tableProps.createCellCoords(Ow(r.row+c,h,s-1),r.col),d=this.tableProps.createCellCoords(Ow(i.row+t,h,s-1),i.col);this.markSource("shift"),n.row>=e?this.setRangeStartOnly(u,!0,this.tableProps.createCellCoords(Ow(n.row+t,0,s-1),n.col)):this.setRangeStartOnly(u,!0),l&&this.selectedByRowHeader.add(this.getLayerLevel()),a&&this.selectedByColumnHeader.add(this.getLayerLevel()),this.setRangeEnd(d),this.markEndSource()}}shiftColumns(e,t){if(!this.isSelected())return;const o=this.selectedRange.current();if(this.isSelectedByCorner())this.selectAll(!0,!0,{disableHeadersHighlight:!0});else if(this.isSelectedByRowHeader()||o.getOuterTopStartCorner().col>=e){const{from:r,to:i,highlight:n}=o,s=this.tableProps.countCols(),l=this.isSelectedByRowHeader(),a=this.isSelectedByColumnHeader(),h=l?-1:0,c=l?0:t;this.getSelectedRange().pop();const u=this.tableProps.createCellCoords(r.row,Ow(r.col+c,h,s-1)),d=this.tableProps.createCellCoords(i.row,Ow(i.col+t,h,s-1));this.markSource("shift"),n.col>=e?this.setRangeStartOnly(u,!0,this.tableProps.createCellCoords(n.row,Ow(n.col+t,0,s-1))):this.setRangeStartOnly(u,!0),l&&this.selectedByRowHeader.add(this.getLayerLevel()),a&&this.selectedByColumnHeader.add(this.getLayerLevel()),this.setRangeEnd(d),this.markEndSource()}}getLayerLevel(){return this.selectedRange.size()-1}isSelected(){return!this.selectedRange.isEmpty()}isMultiple(){if(!this.isSelected())return!1;const e=zf(!this.selectedRange.current().isSingle());return this.runLocalHooks("afterIsMultipleSelection",e),e.value}isFocusSelectionChanged(){return this.isSelected()&&pH(CH,this)}isSelectedByRowHeader(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getLayerLevel();return!this.isSelectedByCorner(e)&&(-1===e?this.selectedByRowHeader.size>0:this.selectedByRowHeader.has(e))}isEntireRowSelected(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getLayerLevel();const t=e=>{const{col:t}=e.getOuterTopStartCorner(),o=this.tableProps.countRowHeaders(),r=this.tableProps.countCols();return(o>0&&t<0||0===o)&&e.getWidth()===r};if(-1===e)return Array.from(this.selectedRange).some((e=>t(e)));const o=this.selectedRange.peekByIndex(e);return!!o&&t(o)}isSelectedByColumnHeader(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getLayerLevel();return!this.isSelectedByCorner()&&(-1===e?this.selectedByColumnHeader.size>0:this.selectedByColumnHeader.has(e))}isEntireColumnSelected(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getLayerLevel();const t=e=>{const{row:t}=e.getOuterTopStartCorner(),o=this.tableProps.countColHeaders(),r=this.tableProps.countRows();return(o>0&&t<0||0===o)&&e.getHeight()===r};if(-1===e)return Array.from(this.selectedRange).some((e=>t(e)));const o=this.selectedRange.peekByIndex(e);return!!o&&t(o)}isSelectedByAnyHeader(){return this.isSelectedByRowHeader(-1)||this.isSelectedByColumnHeader(-1)||this.isSelectedByCorner()}isSelectedByCorner(){return this.selectedByColumnHeader.has(this.getLayerLevel())&&this.selectedByRowHeader.has(this.getLayerLevel())}inInSelection(e){return this.selectedRange.includes(e)}isCellCornerVisible(){return this.settings.fillHandle&&!this.tableProps.isEditorOpened()&&!this.isMultiple()}isCellVisible(e){const t=this.tableProps.visualToRenderableCoords(e);return null!==t.row&&null!==t.col}isAreaCornerVisible(e){return(!Number.isInteger(e)||e===this.getLayerLevel())&&this.settings.fillHandle&&!this.tableProps.isEditorOpened()&&this.isMultiple()}clear(){this.selectedRange.clear(),this.highlight.clear()}deselect(){this.isSelected()&&(this.inProgress=!1,this.clear(),this.runLocalHooks("afterDeselect"))}selectAll(){var e;let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{focusPosition:!1,disableHeadersHighlight:!1};const i=this.tableProps.countRows(),n=this.tableProps.countCols(),s=this.tableProps.countRowHeaders(),l=this.tableProps.countColHeaders(),a=o?-l:0,h=t?-s:0;if(0===a&&0===h&&(0===i||0===n))return;let c=null===(e=this.getSelectedRange().current())||void 0===e?void 0:e.highlight;const{focusPosition:u,disableHeadersHighlight:d}=r;wH(SH,this,d),u&&Number.isInteger(null==u?void 0:u.row)&&Number.isInteger(null==u?void 0:u.col)&&(c=this.tableProps.createCellCoords(Ow(u.row,a,i-1),Ow(u.col,h,n-1)));const g=this.tableProps.createCellCoords(a,h),f=this.tableProps.createCellCoords(i-1,n-1);this.clear(),this.setRangeStartOnly(g,void 0,c),h<0&&this.selectedByRowHeader.add(this.getLayerLevel()),a<0&&this.selectedByColumnHeader.add(this.getLayerLevel()),this.setRangeEnd(f),this.finish(),wH(SH,this,!1)}selectCells(e){var t=this;const o=gH(e);if(1===o)return!1;if(0===o)throw new Error(Ud`Unsupported format of the selection ranges was passed. To select cells pass\x20
        the coordinates as an array of arrays ([[rowStart, columnStart/columnPropStart, rowEnd,\x20
        columnEnd/columnPropEnd]]) or as an array of CellRange objects.`);const r=function(e){let{createCellCoords:t,createCellRange:o,keepDirection:r=!1,propToCol:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!hH.includes(e))throw new Error("Unsupported selection ranges schema type was provided.");return function(n){const s=3===e;let l=s?n.from.row:n[0],a=s?n.from.col:n[1],h=s?n.to.row:n[2],c=s?n.to.col:n[3];if("function"==typeof i&&("string"==typeof a&&(a=i(a)),"string"==typeof c&&(c=i(c))),Yd(h)&&(h=l),Yd(c)&&(c=a),!r){const e=l,t=a,o=h,r=c;l=Math.min(e,o),a=Math.min(t,r),h=Math.max(e,o),c=Math.max(t,r)}const u=t(l,a),d=t(h,c);return o(u,u,d)}}(o,{createCellCoords:function(){return t.tableProps.createCellCoords(...arguments)},createCellRange:function(){return t.tableProps.createCellRange(...arguments)},propToCol:e=>this.tableProps.propToCol(e),keepDirection:!0}),i=this.settings.navigableHeaders,n={countRows:this.tableProps.countRows(),countCols:this.tableProps.countCols(),countRowHeaders:i?this.tableProps.countRowHeaders():0,countColHeaders:i?this.tableProps.countColHeaders():0},s=!e.some((e=>{const t=r(e),o=t.isValid(n);return!(o&&!t.containsHeaders()||o&&t.containsHeaders()&&t.isSingleHeader())}));return s&&(this.clear(),this.setExpectedLayers(e.length),jd(e,(e=>{const{from:t,to:o}=r(e);this.setRangeStartOnly(t.clone(),!1),this.setRangeEnd(o.clone())})),this.finish()),s}selectColumns(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const r="string"==typeof e?this.tableProps.propToCol(e):e,i="string"==typeof t?this.tableProps.propToCol(t):t,n=this.tableProps.countRows(),s=this.tableProps.countCols(),l=this.tableProps.countColHeaders(),a=0===l?0:-l,h=this.tableProps.createCellCoords(a,r),c=this.tableProps.createCellCoords(n-1,i),u=this.tableProps.createCellRange(h,h,c).isValid({countRows:n,countCols:s,countRowHeaders:0,countColHeaders:l});if(u){let e=0,t=0;Number.isInteger(null==o?void 0:o.row)&&Number.isInteger(null==o?void 0:o.col)?(e=Ow(o.row,a,n-1),t=Ow(o.col,Math.min(r,i),Math.max(r,i))):(e=Ow(o,a,n-1),t=r);const s=this.tableProps.createCellCoords(e,t),h=0===l?0:Ow(s.row,a,-1),c=n-1,u=this.tableProps.createCellCoords(h,r),d=this.tableProps.createCellCoords(c,i);this.runLocalHooks("beforeSelectColumns",u,d,s),u.row=h,d.row=c,this.setRangeStartOnly(u,void 0,s),this.selectedByColumnHeader.add(this.getLayerLevel()),this.setRangeEnd(d),this.runLocalHooks("afterSelectColumns",u,d,s),this.finish()}return u}selectRows(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const r=this.tableProps.countRows(),i=this.tableProps.countCols(),n=this.tableProps.countRowHeaders(),s=0===n?0:-n,l=this.tableProps.createCellCoords(e,s),a=this.tableProps.createCellCoords(t,i-1),h=this.tableProps.createCellRange(l,l,a).isValid({countRows:r,countCols:i,countRowHeaders:n,countColHeaders:0});if(h){let r=0,l=0;Number.isInteger(null==o?void 0:o.row)&&Number.isInteger(null==o?void 0:o.col)?(r=Ow(o.row,Math.min(e,t),Math.max(e,t)),l=Ow(o.col,s,i-1)):(r=e,l=Ow(o,s,i-1));const a=this.tableProps.createCellCoords(r,l),h=0===n?0:Ow(a.col,s,-1),c=i-1,u=this.tableProps.createCellCoords(e,h),d=this.tableProps.createCellCoords(t,c);this.runLocalHooks("beforeSelectRows",u,d,a),u.col=h,d.col=c,this.setRangeStartOnly(u,void 0,a),this.selectedByRowHeader.add(this.getLayerLevel()),this.setRangeEnd(d),this.runLocalHooks("afterSelectRows",u,d,a),this.finish()}return h}refresh(){if(!this.isSelected())return;const e=this.tableProps.countRows(),t=this.tableProps.countCols();if(0===e||0===t)return void this.deselect();const o=this.selectedRange.peekByIndex(this.selectedRange.size()-1),{from:r,to:i,highlight:n}=o;this.clear(),n.assign({row:Ow(n.row,-1/0,e-1),col:Ow(n.col,-1/0,t-1)}),r.assign({row:Ow(r.row,-1/0,e-1),col:Ow(r.col,-1/0,t-1)}),i.assign({row:Ow(i.row,0,e-1),col:Ow(i.col,0,t-1)}),this.selectedRange.ranges.push(o),this.highlight.isEnabledFor(jb,this.selectedRange.current().highlight)&&this.highlight.getFocus().add(n).commit().syncWith(o),this.applyAndCommit(o)}commit(){if(this.highlight.getCustomSelections().forEach((e=>{e.commit()})),!this.isSelected())return;const e=this.getLayerLevel(),t=this.selectedRange.current();this.highlight.isEnabledFor(jb,t.highlight)&&this.highlight.getFocus().commit().syncWith(t);for(let o=0;o<this.selectedRange.size();o+=1){this.highlight.useLayerLevel(o);const e=this.highlight.createArea(),t=this.highlight.createLayeredArea(),r=this.highlight.createRowHeader(),i=this.highlight.createColumnHeader(),n=this.highlight.createActiveRowHeader(),s=this.highlight.createActiveColumnHeader(),l=this.highlight.createActiveCornerHeader(),a=this.highlight.createRowHighlight(),h=this.highlight.createColumnHighlight();e.commit(),t.commit(),r.commit(),i.commit(),n.commit(),s.commit(),l.commit(),a.commit(),h.commit()}this.highlight.useLayerLevel(e)}}Vf(EH,Lb);const HH=EH;function OH(e){let t,o,r,i,n,s="";for(t=0,o=e.length;t<o;t+=1){for(i=e[t].length,r=0;r<i;r+=1)r>0&&(s+="\t"),n=e[t][r],"string"==typeof n?n.indexOf("\n")>-1?s+=`"${n.replace(/"/g,'""')}"`:s+=n:s+=null==n?"":n;t!==o-1&&(s+="\n")}return s}function MH(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class xH{static get DESTINATION_RENDERER(){return 1}static get DESTINATION_CLIPBOARD_GENERATOR(){return 2}constructor(e,t,o){MH(this,"hot",void 0),MH(this,"metaManager",void 0),MH(this,"tableMeta",void 0),MH(this,"dataSource",void 0),MH(this,"duckSchema",void 0),MH(this,"colToPropCache",void 0),MH(this,"propToColCache",void 0),this.hot=e,this.metaManager=o,this.tableMeta=o.getTableMeta(),this.dataSource=t,this.refreshDuckSchema(),this.createMap()}createMap(){const e=this.getSchema();if(void 0===e)throw new Error("trying to create `columns` definition but you didn't provide `schema` nor `data`");const t=this.tableMeta.columns;let o;if(this.colToPropCache=[],this.propToColCache=new Map,t){let r=0,i=0,n=!1;if("function"==typeof t){const t=jf(e);r=t>0?t:this.countFirstRowKeys(),n=!0}else{const e=this.tableMeta.maxCols;r=Math.min(e,t.length)}for(o=0;o<r;o++){const e=n?t(o):t[o];if(Df(e)){if(void 0!==e.data){const t=n?i:o;this.colToPropCache[t]=e.data,this.propToColCache.set(e.data,t)}i+=1}}}else this.recursiveDuckColumns(e)}countFirstRowKeys(){return US(this.dataSource)}recursiveDuckColumns(e,t,o){let r,i=t,n=o;return void 0===i&&(i=0,n=""),"object"!=typeof e||Array.isArray(e)||Bf(e,((e,t)=>{null===e?(r=n+t,this.colToPropCache.push(r),this.propToColCache.set(r,i),i+=1):i=this.recursiveDuckColumns(e,i,`${t}.`)})),i}colToProp(e){if(!1===Number.isInteger(e))return e;const t=this.hot.toPhysicalColumn(e);return null===t?e:this.colToPropCache&&Gd(this.colToPropCache[t])?this.colToPropCache[t]:t}propToCol(e){const t=this.propToColCache.get(e);if(Gd(t))return this.hot.toVisualColumn(t);const o=this.hot.toVisualColumn(e);return null===o?e:o}getSchema(){const e=this.tableMeta.dataSchema;return e?"function"==typeof e?e():e:this.duckSchema}createDuckSchema(){return this.dataSource&&this.dataSource[0]?Af(this.dataSource[0]):{}}refreshDuckSchema(){this.duckSchema=this.createDuckSchema()}createRow(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,{source:o,mode:r="above"}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=this.hot.countSourceRows();let n=i,s=0,l=e;if(("number"!=typeof l||l>=i)&&(l=i),l<this.hot.countRows()&&(n=this.hot.toPhysicalRow(l)),!1===this.hot.runHooks("beforeCreateRow",l,t,o)||null===n)return{delta:0};const a=this.tableMeta.maxRows,h=this.getSchema().length,c=[];for(;s<t&&i+s<a;){let e=null;"array"===this.hot.dataType?this.tableMeta.dataSchema?e=Pf(this.getSchema()):(e=[],Ew(h-1,(()=>e.push(null)))):"function"===this.hot.dataType?e=this.tableMeta.dataSchema(l+s):(e={},_f(e,this.getSchema())),c.push(e),s+=1}this.hot.rowIndexMapper.insertIndexes(l,s),"below"===r&&(n=Math.min(n+1,i)),this.spliceData(n,0,c);const u=this.hot.toVisualRow(n);return this.hot.countSourceRows()===c.length&&this.hot.columnIndexMapper.initToLength(this.hot.getInitialColumnCount()),s>0&&(null==e?this.metaManager.createRow(null,s):"auto"!==o&&this.metaManager.createRow(n,t)),this.hot.runHooks("afterCreateRow",u,s,o),{delta:s,startPhysicalIndex:n}}createCol(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,{source:o,mode:r="start"}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!this.hot.isColumnModificationAllowed())throw new Error("Cannot create new column. When data source in an object, you can only have as much columns as defined in first data row, data schema or in the 'columns' setting.If you want to be able to add new columns, you have to use array datasource.");const i=this.dataSource,n=this.tableMeta.maxCols,s=this.hot.countSourceCols();let l=e;if(("number"!=typeof l||l>=s)&&(l=s),!1===this.hot.runHooks("beforeCreateCol",l,t,o))return{delta:0};let a=s;l<this.hot.countCols()&&(a=this.hot.toPhysicalColumn(l));const h=this.hot.countSourceRows();let c=this.hot.countCols(),u=0,d=a;"end"===r&&(d=Math.min(d+1,s));const g=d;for(;u<t&&c<n;){if("number"!=typeof l||l>=c)if(h>0)for(let e=0;e<h;e+=1)void 0===i[e]&&(i[e]=[]),i[e].push(null);else i.push([null]);else for(let e=0;e<h;e++)i[e].splice(d,0,null);u+=1,d+=1,c+=1}this.hot.columnIndexMapper.insertIndexes(l,u),u>0&&(null==e?this.metaManager.createColumn(null,u):"auto"!==o&&this.metaManager.createColumn(g,t));const f=this.hot.toVisualColumn(g);return this.hot.runHooks("afterCreateCol",f,u,o),this.refreshDuckSchema(),{delta:u,startPhysicalIndex:g}}removeRow(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,o=arguments.length>2?arguments[2]:void 0,r=Number.isInteger(e)?e:-t;const i=this.visualRowsToPhysical(r,t),n=this.hot.countSourceRows();if(r=(n+r)%n,!1===this.hot.runHooks("beforeRemoveRow",r,i.length,i,o))return!1;const s=i.length;if(this.filterData(r,s,i),r<this.hot.countRows()){this.hot.rowIndexMapper.removeIndexes(i);const e=Gd(this.tableMeta.columns)||Gd(this.tableMeta.dataSchema)||this.tableMeta.colHeaders;0!==this.hot.rowIndexMapper.getNotTrimmedIndexesLength()||e||this.hot.columnIndexMapper.setIndexesSequence([])}return i.slice(0).sort(((e,t)=>t-e)).forEach((e=>{this.metaManager.removeRow(e,1)})),this.hot.runHooks("afterRemoveRow",r,s,i,o),!0}removeCol(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,o=arguments.length>2?arguments[2]:void 0;if("object"===this.hot.dataType||this.tableMeta.columns)throw new Error("cannot remove column with object data source or columns option specified");let r="number"!=typeof e?-t:e;r=(this.hot.countCols()+r)%this.hot.countCols();const i=this.visualColumnsToPhysical(r,t),n=i.slice(0).sort(((e,t)=>t-e));if(!1===this.hot.runHooks("beforeRemoveCol",r,t,i,o))return!1;let s=!0;const l=n.length,a=this.dataSource;for(let h=0;h<l;h++)s&&i[0]!==i[h]-h&&(s=!1);if(s)for(let h=0,c=this.hot.countSourceRows();h<c;h++)a[h].splice(i[0],t),0===h&&this.metaManager.removeColumn(i[0],t);else for(let h=0,c=this.hot.countSourceRows();h<c;h++)for(let e=0;e<l;e++)a[h].splice(n[e],1),0===h&&this.metaManager.removeColumn(n[e],1);return r<this.hot.countCols()&&(this.hot.columnIndexMapper.removeIndexes(i),this.tableMeta.rowHeaders||0!==this.hot.columnIndexMapper.getNotTrimmedIndexesLength()||this.hot.rowIndexMapper.setIndexesSequence([])),this.hot.runHooks("afterRemoveCol",r,t,i,o),this.refreshDuckSchema(),!0}spliceCol(e,t,o){const r=this.hot.getDataAtCol(e),i=r.slice(t,t+o),n=r.slice(t+o);for(var s=arguments.length,l=new Array(s>3?s-3:0),a=3;a<s;a++)l[a-3]=arguments[a];Ld(l,n);let h=0;for(;h<o;)l.push(null),h+=1;return function(e){const t=e.length;let o=0;for(;o<t;)e[o]=[e[o]],o+=1}(l),this.hot.populateFromArray(t,e,l,null,null,"spliceCol"),i}spliceRow(e,t,o){const r=this.hot.getSourceDataAtRow(e),i=r.slice(t,t+o),n=r.slice(t+o);for(var s=arguments.length,l=new Array(s>3?s-3:0),a=3;a<s;a++)l[a-3]=arguments[a];Ld(l,n);let h=0;for(;h<o;)l.push(null),h+=1;return this.hot.populateFromArray(e,t,[l],null,null,"spliceRow"),i}spliceData(e,t,o){if(!1!==this.hot.runHooks("beforeDataSplice",e,t,o)){const t=[...this.dataSource.slice(0,e),...o,...this.dataSource.slice(e)];this.dataSource.length=0,t.forEach((e=>this.dataSource.push(e)))}}filterData(e,t,o){let r=this.hot.runHooks("filterData",e,t,o);!1===Array.isArray(r)&&(r=this.dataSource.filter(((e,t)=>-1===o.indexOf(t)))),this.dataSource.length=0,Array.prototype.push.apply(this.dataSource,r)}get(e,t){const o=this.hot.toPhysicalRow(e);let r=this.dataSource[o];const i=this.hot.runHooks("modifyRowData",o);r=isNaN(i)?i:r;const{dataDotNotation:n}=this.hot.getSettings();let s=null;if(r&&r.hasOwnProperty&&$f(r,t))s=r[t];else if(n&&"string"==typeof t&&t.indexOf(".")>-1){let e=r;if(!e)return null;const o=t.split(".");for(let t=0,r=o.length;t<r;t++)if(e=e[o[t]],void 0===e)return null;s=e}else"function"==typeof t&&(s=t(this.dataSource.slice(o,o+1)[0]));if(this.hot.hasHook("modifyData")){const e=zf(s);this.hot.runHooks("modifyData",o,this.propToCol(t),e,"get"),e.isTouched()&&(s=e.value)}return s}getCopyable(e,t){return this.hot.getCellMeta(e,this.propToCol(t)).copyable?this.get(e,t):""}set(e,t,o){const r=this.hot.toPhysicalRow(e);let i=o,n=this.dataSource[r];const s=this.hot.runHooks("modifyRowData",r);if(n=isNaN(s)?s:n,this.hot.hasHook("modifyData")){const e=zf(i);this.hot.runHooks("modifyData",r,this.propToCol(t),e,"set"),e.isTouched()&&(i=e.value)}const{dataDotNotation:l}=this.hot.getSettings();if(n&&n.hasOwnProperty&&$f(n,t))n[t]=i;else if(l&&"string"==typeof t&&t.indexOf(".")>-1){let e,o=n,r=0;const s=t.split(".");for(r=0,e=s.length-1;r<e;r++){if("__proto__"===s[r]||"constructor"===s[r]||"prototype"===s[r])return;void 0===o[s[r]]&&(o[s[r]]={}),o=o[s[r]]}o[s[r]]=i}else if("function"==typeof t)t(this.dataSource.slice(r,r+1)[0],i);else{if("__proto__"===t||"constructor"===t||"prototype"===t)return;n[t]=i}}visualRowsToPhysical(e,t){const o=this.hot.countSourceRows(),r=[];let i,n=(o+e)%o,s=t;for(;n<o&&s;)i=this.hot.toPhysicalRow(n),r.push(i),s-=1,n+=1;return r}visualColumnsToPhysical(e,t){const o=this.hot.countCols(),r=[];let i=(o+e)%o,n=t;for(;i<o&&n;){const e=this.hot.toPhysicalColumn(i);r.push(e),n-=1,i+=1}return r}clear(){for(let e=0;e<this.hot.countSourceRows();e++)for(let t=0;t<this.hot.countCols();t++)this.set(e,this.colToProp(t),"")}getLength(){const e=this.tableMeta.maxRows;let t;t=e<0||0===e?0:e||1/0;const o=this.hot.rowIndexMapper.getNotTrimmedIndexesLength();return Math.min(o,t)}getAll(){const e={row:0,col:0},t={row:Math.max(this.hot.countRows()-1,0),col:Math.max(this.hot.countCols()-1,0)};return e.row-t.row!==0||this.hot.countSourceRows()?this.getRange(e,t,xH.DESTINATION_RENDERER):[]}countCachedColumns(){return this.colToPropCache.length}getRange(e,t,o){const r=[];let i,n,s;const l=this.tableMeta.maxRows,a=this.tableMeta.maxCols;if(0===l||0===a)return[];const h=o===xH.DESTINATION_CLIPBOARD_GENERATOR?this.getCopyable:this.get,c=Math.min(Math.max(l-1,0),Math.max(e.row,t.row)),u=Math.min(Math.max(a-1,0),Math.max(e.col,t.col));for(i=Math.min(e.row,t.row);i<=c;i++){s=[];const o=i>=0?this.hot.toPhysicalRow(i):i;for(n=Math.min(e.col,t.col);n<=u&&null!==o;n++)s.push(h.call(this,i,this.colToProp(n)));null!==o&&r.push(s)}return r}getText(e,t){return OH(this.getRange(e,t,xH.DESTINATION_RENDERER))}getCopyableText(e,t){return OH(this.getRange(e,t,xH.DESTINATION_CLIPBOARD_GENERATOR))}destroy(){this.hot=null,this.metaManager=null,this.dataSource=null,this.duckSchema=null,this.colToPropCache.length=0,this.propToColCache.clear(),this.propToColCache=void 0}}const IH=xH,{register:NH,getItem:AH,hasItem:kH,getNames:_H,getValues:PH}=Km("cellTypes");function VH(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;const r="string"==typeof t.type?function(e){if(!kH(e))throw Error(`You declared cell type "${e}" as a string that is not mapped to a known object.\n                 Cell type must be an object or a string mapped to an object registered by\n                 "Handsontable.cellTypes.registerCellType" method`);return AH(e)}(t.type):t.type;if(e._automaticallyAssignedMetaProps&&Bf(t,((t,o)=>{e._automaticallyAssignedMetaProps.delete(o)})),!Df(r))return;o!==e||e._automaticallyAssignedMetaProps||(e._automaticallyAssignedMetaProps=new Set);const i={};Bf(r,((t,r)=>{var n;(function(e,t){var o;return"CELL_TYPE"!==e&&((null===(o=t._automaticallyAssignedMetaProps)||void 0===o?void 0:o.has(e))||!$f(t,e))})(r,o)&&(i[r]=t,null===(n=e._automaticallyAssignedMetaProps)||void 0===n||n.add(r))})),kf(e,i)}function LH(e){return Number.isInteger(e)&&e>=0}function DH(e,t){if(!e())throw new Error(`Assertion failed: ${t}`)}function FH(e){return null==e}const BH=()=>({_automaticallyAssignedMetaProps:void 0,activeHeaderClassName:"ht__active_highlight",allowEmpty:!0,allowHtml:!1,allowInsertColumn:!0,allowInsertRow:!0,allowInvalid:!0,allowRemoveColumn:!0,allowRemoveRow:!0,ariaTags:!0,autoColumnSize:void 0,autoRowSize:void 0,autoWrapCol:!1,autoWrapRow:!1,bindRowsWithHeaders:void 0,cell:[],cells:void 0,checkedTemplate:void 0,className:void 0,colHeaders:null,collapsibleColumns:void 0,columnHeaderHeight:void 0,columns:void 0,columnSorting:void 0,columnSummary:void 0,colWidths:void 0,commentedCellClassName:"htCommentCell",comments:!1,contextMenu:void 0,copyable:!0,copyPaste:!0,correctFormat:!1,currentColClassName:void 0,currentHeaderClassName:"ht__highlight",currentRowClassName:void 0,customBorders:!1,data:void 0,dataDotNotation:!0,dataSchema:void 0,dateFormat:"DD/MM/YYYY",timeFormat:"h:mm:ss a",datePickerConfig:void 0,defaultDate:void 0,disableVisualSelection:!1,dragToScroll:!0,dropdownMenu:void 0,editor:void 0,enterBeginsEditing:!0,enterMoves:{col:0,row:1},fillHandle:{autoInsertRow:!1},filter:!0,filteringCaseSensitive:!1,filters:void 0,fixedColumnsLeft:0,fixedColumnsStart:0,fixedRowsBottom:0,fixedRowsTop:0,formulas:void 0,fragmentSelection:!1,headerClassName:void 0,height:void 0,hiddenColumns:void 0,hiddenRows:void 0,invalidCellClassName:"htInvalid",imeFastEdit:!1,isEmptyCol(e){let t,o,r;for(t=0,o=this.countRows();t<o;t++)if(r=this.getDataAtCell(t,e),!1===Kd(r))return!1;return!0},isEmptyRow(e){let t,o,r,i;for(t=0,o=this.countCols();t<o;t++)if(r=this.getDataAtCell(e,t),!1===Kd(r))return"object"==typeof r&&(i=this.getCellMeta(e,t),Lf(this.getSchema()[i.prop],r));return!0},label:void 0,language:"en-US",layoutDirection:"inherit",licenseKey:void 0,locale:"en-US",manualColumnFreeze:void 0,manualColumnMove:void 0,manualColumnResize:void 0,manualRowMove:void 0,manualRowResize:void 0,maxCols:1/0,maxRows:1/0,mergeCells:!1,minCols:0,minRows:0,minSpareCols:0,minSpareRows:0,multiColumnSorting:void 0,navigableHeaders:!1,tabNavigation:!0,nestedHeaders:void 0,nestedRows:void 0,noWordWrapClassName:"htNoWrap",numericFormat:void 0,observeDOMVisibility:!0,outsideClickDeselects:!0,persistentState:void 0,placeholder:void 0,placeholderCellClassName:"htPlaceholder",preventOverflow:!1,preventWheel:!1,readOnly:!1,readOnlyCellClassName:"htDimmed",renderAllRows:!1,renderAllColumns:!1,renderer:void 0,rowHeaders:void 0,rowHeaderWidth:void 0,rowHeights:void 0,search:!1,selectionMode:"multiple",selectOptions:void 0,skipColumnOnPaste:!1,skipRowOnPaste:!1,sortByRelevance:!0,source:void 0,startCols:5,startRows:5,stretchH:"none",strict:void 0,tableClassName:void 0,themeName:void 0,tabMoves:{row:0,col:1},title:void 0,trimDropdown:!0,trimRows:void 0,trimWhitespace:!0,type:"text",uncheckedTemplate:void 0,undo:!0,validator:void 0,viewportColumnRenderingOffset:"auto",viewportRowRenderingOffset:"auto",viewportColumnRenderingThreshold:0,viewportRowRenderingThreshold:0,visibleRows:10,width:void 0,wordWrap:!0});function WH(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class jH{constructor(e){WH(this,"metaCtor",class{}),WH(this,"meta",void 0),this.meta=this.metaCtor.prototype,kf(this.meta,BH()),this.meta.instance=e}getMetaConstructor(){return this.metaCtor}getMeta(){return this.meta}updateMeta(e){var t;kf(this.meta,e),VH(this.meta,{...e,type:null!==(t=e.type)&&void 0!==t?t:this.meta.type},e)}}function zH(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class $H{constructor(e){zH(this,"meta",void 0);const t=e.getMetaConstructor();this.meta=new t}getMeta(){return this.meta}updateMeta(e){kf(this.meta,e),VH(this.meta,e,e)}}function UH(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class XH{constructor(e){UH(this,"valueFactory",void 0),UH(this,"data",[]),UH(this,"index",[]),UH(this,"holes",new Set),this.valueFactory=e}obtain(e){DH((()=>LH(e)),"Expecting an unsigned number.");const t=this._getStorageIndexByKey(e);let o;if(t>=0)o=this.data[t],void 0===o&&(o=this.valueFactory(e),this.data[t]=o);else if(o=this.valueFactory(e),this.holes.size>0){const t=this.holes.values().next().value;this.holes.delete(t),this.data[t]=o,this.index[e]=t}else this.data.push(o),this.index[e]=this.data.length-1;return o}insert(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;DH((()=>LH(e)||FH(e)),"Expecting an unsigned number or null/undefined argument.");const o=[],r=this.data.length;for(let n=0;n<t;n++)o.push(r+n),this.data.push(void 0);const i=FH(e)?this.index.length:e;this.index=[...this.index.slice(0,i),...o,...this.index.slice(i)]}remove(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;DH((()=>LH(e)||FH(e)),"Expecting an unsigned number or null/undefined argument.");const o=this.index.splice(FH(e)?this.index.length-t:e,t);for(let r=0;r<o.length;r++){const e=o[r];"number"==typeof e&&this.holes.add(e)}}size(){return this.data.length-this.holes.size}values(){return this.data.filter(((e,t)=>void 0!==e&&!this.holes.has(t)))[Symbol.iterator]()}entries(){const e=[];for(let o=0;o<this.data.length;o++){const t=this._getKeyByStorageIndex(o);-1!==t&&void 0!==this.data[o]&&e.push([t,this.data[o]])}let t=0;return{next:()=>{if(t<e.length){const o=e[t];return t+=1,{value:o,done:!1}}return{done:!0}}}}clear(){this.data=[],this.index=[],this.holes.clear()}_getStorageIndexByKey(e){return this.index.length>e?this.index[e]:-1}_getKeyByStorageIndex(e){return this.index.indexOf(e)}[Symbol.iterator](){return this.entries()}}function GH(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const YH=["data","width"];class KH{constructor(e){GH(this,"globalMeta",void 0),GH(this,"metas",new XH((()=>this._createMeta()))),this.globalMeta=e,this.metas=new XH((()=>this._createMeta()))}updateMeta(e,t){const o=this.getMeta(e);kf(o,t),VH(o,t)}createColumn(e,t){this.metas.insert(e,t)}removeColumn(e,t){this.metas.remove(e,t)}getMeta(e){return this.metas.obtain(e)}getMetaConstructor(e){return this.metas.obtain(e).constructor}clearCache(){this.metas.clear()}_createMeta(){return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];function o(){}var r,i;r=o,(i=e).prototype.constructor=i,r.prototype=new i,r.prototype.constructor=r;for(let n=0;n<t.length;n++)o.prototype[t[n]]=void 0;return o}(this.globalMeta.getMetaConstructor(),YH).prototype}}function qH(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class QH{constructor(e){qH(this,"columnMeta",void 0),qH(this,"metas",new XH((()=>this._createRow()))),this.columnMeta=e}updateMeta(e,t,o){const r=this.getMeta(e,t);kf(r,o),VH(r,o)}createRow(e,t){this.metas.insert(e,t)}createColumn(e,t){for(let o=0;o<this.metas.size();o++)this.metas.obtain(o).insert(e,t)}removeRow(e,t){this.metas.remove(e,t)}removeColumn(e,t){for(let o=0;o<this.metas.size();o++)this.metas.obtain(o).remove(e,t)}getMeta(e,t,o){const r=this.metas.obtain(e).obtain(t);return void 0===o?r:r[o]}setMeta(e,t,o,r){var i;const n=this.metas.obtain(e).obtain(t);null===(i=n._automaticallyAssignedMetaProps)||void 0===i||i.delete(o),n[o]=r}removeMeta(e,t,o){delete this.metas.obtain(e).obtain(t)[o]}getMetas(){const e=[],t=Array.from(this.metas.values());for(let o=0;o<t.length;o++)Gd(t[o])&&e.push(...t[o].values());return e}getMetasAtRow(e){DH((()=>LH(e)),"Expecting an unsigned number.");const t=new Map(this.metas);return t.has(e)?Array.from(t.get(e).values()):[]}clearCache(){this.metas.clear()}_createRow(){return new XH((e=>this._createMeta(e)))}_createMeta(e){return new(this.columnMeta.getMetaConstructor(e))}}class JH{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];this.hot=e,this.globalMeta=new jH(e),this.tableMeta=new $H(this.globalMeta),this.columnMeta=new KH(this.globalMeta),this.cellMeta=new QH(this.columnMeta),o.forEach((e=>new e(this))),this.globalMeta.updateMeta(t)}getGlobalMeta(){return this.globalMeta.getMeta()}updateGlobalMeta(e){this.globalMeta.updateMeta(e)}getTableMeta(){return this.tableMeta.getMeta()}updateTableMeta(e){this.tableMeta.updateMeta(e)}getColumnMeta(e){return this.columnMeta.getMeta(e)}updateColumnMeta(e,t){this.columnMeta.updateMeta(e,t)}getCellMeta(e,t,o){const r=this.cellMeta.getMeta(e,t);return r.visualRow=o.visualRow,r.visualCol=o.visualColumn,r.row=e,r.col=t,o.skipMetaExtension||this.runLocalHooks("afterGetCellMeta",r),r}getCellMetaKeyValue(e,t,o){if("string"!=typeof o)throw new Error("The passed cell meta object key is not a string");return this.cellMeta.getMeta(e,t,o)}setCellMeta(e,t,o,r){this.cellMeta.setMeta(e,t,o,r)}updateCellMeta(e,t,o){this.cellMeta.updateMeta(e,t,o)}removeCellMeta(e,t,o){this.cellMeta.removeMeta(e,t,o)}getCellsMeta(){return this.cellMeta.getMetas()}getCellsMetaAtRow(e){return this.cellMeta.getMetasAtRow(e)}createRow(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.cellMeta.createRow(e,t)}removeRow(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.cellMeta.removeRow(e,t)}createColumn(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.cellMeta.createColumn(e,t),this.columnMeta.createColumn(e,t)}removeColumn(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.cellMeta.removeColumn(e,t),this.columnMeta.removeColumn(e,t)}clearCellsCache(){this.cellMeta.clearCache()}clearCache(){this.cellMeta.clearCache(),this.columnMeta.clearCache()}}function ZH(e,t,o,r){const{hotInstance:i,dataMap:n,dataSource:s,internalSource:l,source:a,metaManager:h,firstRun:c}=r,u=sg(l),d=i.getSettings();Array.isArray(d.dataSchema)?i.dataType="array":Zg(d.dataSchema)?i.dataType="function":i.dataType="object",n&&n.destroy(),e=i.runHooks(`before${u}`,e,c,a);const g=new IH(i,e,h);if(t(g),"object"==typeof e&&null!==e)e.push&&e.splice||(e=[e]);else{if(null!==e)throw new Error(`${l} only accepts array of objects or array of arrays (${typeof e} given)`);{const t=g.getSchema();let o;e=[];let r=0,n=0;for(r=0,n=d.startRows;r<n;r++)if("object"!==i.dataType&&"function"!==i.dataType||!d.dataSchema)if("array"===i.dataType)o=Pf(t[0]),e.push(o);else{o=[];for(let e=0,t=d.startCols;e<t;e++)o.push(null);e.push(o)}else o=Pf(t),e.push(o)}}Array.isArray(e[0])&&(i.dataType="array"),d.data=e,g.dataSource=e,s.data=e,s.dataType=i.dataType,s.colToProp=g.colToProp.bind(g),s.propToCol=g.propToCol.bind(g),s.countCachedColumns=g.countCachedColumns.bind(g),o(g),i.runHooks(`after${u}`,e,c,a),c||(i.runHooks("afterChange",null,l),i.render()),i.getSettings().ariaTags&&Tg(i.rootElement,[hg(-1),cg(i.countCols()+(i.view?i.countRowHeaders():0))])}function eO(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}Vf(JH,Lb);class tO{constructor(e){var t=this;eO(this,"metaManager",void 0),eO(this,"metaSyncMemo",new Map),this.metaManager=e,e.addLocalHook("afterGetCellMeta",(function(){return t.extendCellMeta(...arguments)})),Xm.getSingleton().add("beforeRender",(e=>{e&&this.metaSyncMemo.clear()}),this.metaManager.hot)}extendCellMeta(e){var t;const{row:o,col:r}=e;if(null!==(t=this.metaSyncMemo.get(o))&&void 0!==t&&t.has(r))return;const{visualRow:i,visualCol:n}=e,s=this.metaManager.hot,l=s.colToProp(n);e.prop=l,s.runHooks("beforeGetCellMeta",i,n,e);const a=$f(e,"type")?e.type:null;let h=Zg(e.cells)?e.cells(o,r,l):null;var c;a&&(h?h.type=null!==(c=h.type)&&void 0!==c?c:a:h={type:a}),h&&this.metaManager.updateCellMeta(o,r,h),s.runHooks("afterGetCellMeta",i,n,e),this.metaSyncMemo.has(o)||this.metaSyncMemo.set(o,new Set),this.metaSyncMemo.get(o).add(r)}}function oO(e,t,o){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,o)}function rO(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function iO(e,t){return e.get(function(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}(e,t))}var nO=new WeakMap;class sO{constructor(e){rO(this,"metaManager",void 0),rO(this,"usageTracker",new Set),rO(this,"propDescriptors",new Map([["ariaTags",{initOnly:!0}],["fixedColumnsLeft",{target:"fixedColumnsStart",onChange(e){if(this.metaManager.hot.isRtl()&&"fixedColumnsLeft"===e)throw new Error("The `fixedColumnsLeft` is not supported for RTL. Please use option `fixedColumnsStart`.");if(this.usageTracker.has("fixedColumnsLeft")&&this.usageTracker.has("fixedColumnsStart"))throw new Error("The `fixedColumnsLeft` and `fixedColumnsStart` should not be used together. Please use only the option `fixedColumnsStart`.")}}],["layoutDirection",{initOnly:!0}],["renderAllColumns",{initOnly:!0}],["renderAllRows",{initOnly:!0}]])),oO(this,nO,((e,t,o)=>{if(!o)throw new Error(`The \`${e}\` option can not be updated after the Handsontable is initialized.`)})),this.metaManager=e,this.extendMetaProps()}extendMetaProps(){this.propDescriptors.forEach(((e,t)=>{const{initOnly:o,target:r,onChange:i}=e,n="string"==typeof r,s=n?r:t,l=`_${s}`;this.metaManager.globalMeta.meta[l]=this.metaManager.globalMeta.meta[s],i?(this.installPropWatcher(t,l,i),n&&this.installPropWatcher(r,l,i)):o&&(this.installPropWatcher(t,l,iO(nO,this)),this.metaManager.globalMeta.meta._initOnlySettings||(this.metaManager.globalMeta.meta._initOnlySettings=[]),this.metaManager.globalMeta.meta._initOnlySettings.push(t))}))}installPropWatcher(e,t,o){const r=this;Object.defineProperty(this.metaManager.globalMeta.meta,e,{get(){return this[t]},set(i){const n=!r.usageTracker.has(e);r.usageTracker.add(e),o.call(r,e,i,n),this[t]=i},enumerable:!0,configurable:!0})}}const lO="gridDefault",aO="editorManager.handlingEditor",hO={name:"editorCloseAndSave",callback(e){e._getEditorManager().closeEditorAndSaveChanges()}},cO={_hooksStorage:Object.create(null),addHook(e,t){return this._hooksStorage[e]||(this._hooksStorage[e]=[]),this.hot.addHook(e,t),this._hooksStorage[e].push(t),this},removeHooksByKey(e){jd(this._hooksStorage[e]||[],(t=>{this.hot.removeHook(e,t)}))},clearHooks(){Bf(this._hooksStorage,((e,t)=>this.removeHooksByKey(t))),this._hooksStorage={}}};Ff(cO,"MIXIN_NAME","hooksRefRegisterer",{writable:!1,enumerable:!1});const uO=cO;function dO(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const gO=Object.freeze({VIRGIN:"STATE_VIRGIN",EDITING:"STATE_EDITING",WAITING:"STATE_WAITING",FINISHED:"STATE_FINISHED"});class fO{static get EDITOR_TYPE(){return"base"}constructor(e){dO(this,"hot",void 0),dO(this,"state",gO.VIRGIN),dO(this,"_opened",!1),dO(this,"_fullEditMode",!1),dO(this,"_closeCallback",null),dO(this,"TD",null),dO(this,"row",null),dO(this,"col",null),dO(this,"prop",null),dO(this,"originalValue",null),dO(this,"cellProperties",null),this.hot=e,this.init()}_fireCallbacks(e){this._closeCallback&&(this._closeCallback(e),this._closeCallback=null)}init(){}getValue(){throw Error("Editor getValue() method unimplemented")}setValue(){throw Error("Editor setValue() method unimplemented")}open(){throw Error("Editor open() method unimplemented")}close(){throw Error("Editor close() method unimplemented")}prepare(e,t,o,r,i,n){this.TD=r,this.row=e,this.col=t,this.prop=o,this.originalValue=i,this.cellProperties=n,this.state=this.isOpened()?this.state:gO.VIRGIN}extend(){return class extends this.constructor{}}saveValue(e,t){let o,r,i,n;if(t){const e=this.hot.getSelectedLast();o=Math.max(Math.min(e[0],e[2]),0),r=Math.max(Math.min(e[1],e[3]),0),i=Math.max(e[0],e[2]),n=Math.max(e[1],e[3])}else[o,r,i,n]=[this.row,this.col,null,null];const s=this.hot.runHooks("modifyGetCellCoords",o,r,!1,"meta");Array.isArray(s)&&([o,r]=s),this.hot.populateFromArray(o,r,e,i,n,"edit")}beginEditing(e,t){if(this.state!==gO.VIRGIN)return;const o=this.hot,r=o.rowIndexMapper.getRenderableFromVisualIndex(this.row),i=o.columnIndexMapper.getRenderableFromVisualIndex(this.col),n=()=>{if(this.state=gO.EDITING,this.isInFullEditMode()){const t="string"==typeof e?e:Xd(this.originalValue);this.setValue(t)}this.open(t),this._opened=!0,this.focus(),o.view.render(),o.runHooks("afterBeginEditing",this.row,this.col)};this.hot.addHookOnce("afterScroll",n),o.view.scrollViewport(o._createCellCoords(r,i))||(this.hot.removeHook("afterScroll",n),n())}finishEditing(e,t,o){let r;if(o){const e=this._closeCallback;this._closeCallback=t=>{e&&e(t),o(t),this.hot.view.render()}}if(!this.isWaiting())if(this.state!==gO.VIRGIN){if(this.state===gO.EDITING){if(e)return this.cancelChanges(),void this.hot.view.render();const o=this.getValue();r=this.cellProperties.trimWhitespace?[["string"==typeof o?String.prototype.trim.call(o||""):o]]:[[o]],this.state=gO.WAITING,this.saveValue(r,t),this.hot.getCellValidator(this.cellProperties)?this.hot.addHookOnce("postAfterValidate",(e=>{this.state=gO.FINISHED,this.discardEditor(e)})):(this.state=gO.FINISHED,this.discardEditor(!0))}}else this.hot._registerTimeout((()=>{this._fireCallbacks(!0)}))}cancelChanges(){this.state=gO.FINISHED,this.discardEditor()}discardEditor(e){this.state===gO.FINISHED&&(!1===e&&!0!==this.cellProperties.allowInvalid?(this.hot.selectCell(this.row,this.col),this.focus(),this.state=gO.EDITING,this._fireCallbacks(!1)):(this.close(),this._opened=!1,this._fullEditMode=!1,this.state=gO.VIRGIN,this._fireCallbacks(!0),this.hot.getShortcutManager().setActiveContextName("grid")))}enableFullEditMode(){this._fullEditMode=!0}isInFullEditMode(){return this._fullEditMode}isOpened(){return this._opened}isWaiting(){return this.state===gO.WAITING}getEditedCellRect(){var e;const t=this.getEditedCell();if(!t)return;const{wtOverlays:o,wtViewport:r}=this.hot.view._wt,i=this.hot.rootWindow,n=Ag(t),s=Bg(t),l=Ag(this.hot.rootElement),a=Bg(this.hot.rootElement),h=o.topOverlay.holder,c=o.inlineStartOverlay.holder,u=h!==i?h.scrollTop:0,d=c!==i?c.scrollLeft:0,g=i.innerWidth-l.left-a,{wtTable:f}=null!==(e=o.getParentOverlay(t))&&void 0!==e?e:this.hot.view._wt,m=f.name,p=["master","inline_start"].includes(m)?u:0,w=["master","top","bottom"].includes(m)?d:0,y=n.top===l.top?0:1;let v=n.top-l.top-y-p,b=0;b=this.hot.isRtl()?i.innerWidth-n.left-s-g-1+w:n.left-l.left-1-w,["top","top_inline_start_corner"].includes(m)&&(v+=o.topOverlay.getOverlayOffset()),["inline_start","top_inline_start_corner"].includes(m)&&(b+=Math.abs(o.inlineStartOverlay.getOverlayOffset()));const C=this.hot.hasColHeaders(),S=this.hot.rowIndexMapper.getRenderableFromVisualIndex(this.row),R=this.hot.columnIndexMapper.getRenderableFromVisualIndex(this.col),T=this.hot.rowIndexMapper.getRenderableIndexesLength()-this.hot.view._wt.getSetting("fixedRowsBottom");(C&&S<=0||S===T)&&(v+=1),R<=0&&(b+=1);const E=r.rowsRenderCalculator.startPosition,H=r.columnsRenderCalculator.startPosition,O=Math.abs(o.inlineStartOverlay.getScrollPosition()),M=o.topOverlay.getScrollPosition(),x=Gg(this.hot.rootDocument);let I=t.offsetTop;if(["inline_start","master"].includes(m)&&(I+=E-M),["bottom","bottom_inline_start_corner"].includes(m)){const{wtViewport:e,wtTable:t}=o.bottomOverlay.clone;I+=e.getWorkspaceHeight()-t.getHeight()-x}let N=t.offsetLeft;this.hot.isRtl()?(N=N>=0?f.getWidth()-t.offsetLeft:Math.abs(N),N+=H-O-s):["top","master","bottom"].includes(m)&&(N+=H-O);const A=i.getComputedStyle(this.TD),k=this.hot.isRtl()?"borderRightWidth":"borderLeftWidth",_=parseInt(A[k],10)>0?0:1,P=parseInt(A.borderTopWidth,10)>0?0:1,V=Bg(t)+_,L=Wg(t)+P,D=((F=h)instanceof Window?F.document.body.scrollHeight>F.innerHeight:F.offsetWidth!==F.clientWidth)?x:0;var F;const B=function(e){return e instanceof Window?e.document.body.scrollWidth>e.innerWidth:e.offsetHeight!==e.clientHeight}(c)?x:0,W=this.hot.view.maximumVisibleElementWidth(N)-D+_;return{top:v,start:b,height:L,maxHeight:Math.max(this.hot.view.maximumVisibleElementHeight(I)-B+P,this.hot.view.getDefaultRowHeight()),width:V,maxWidth:W}}getEditedCellsLayerClass(){switch(this.checkEditorSection()){case"inline-start":return"ht_clone_left ht_clone_inline_start";case"bottom":return"ht_clone_bottom";case"bottom-inline-start-corner":return"ht_clone_bottom_left_corner ht_clone_bottom_inline_start_corner";case"top":return"ht_clone_top";case"top-inline-start-corner":return"ht_clone_top_left_corner ht_clone_top_inline_start_corner";default:return"ht_clone_master"}}getEditedCell(){return this.hot.getCell(this.row,this.col,!0)}checkEditorSection(){const e=this.hot.countRows();let t="";return this.row<this.hot.getSettings().fixedRowsTop?t=this.col<this.hot.getSettings().fixedColumnsStart?"top-inline-start-corner":"top":this.hot.getSettings().fixedRowsBottom&&this.row>=e-this.hot.getSettings().fixedRowsBottom?t=this.col<this.hot.getSettings().fixedColumnsStart?"bottom-inline-start-corner":"bottom":this.col<this.hot.getSettings().fixedColumnsStart&&(t="inline-start"),t}}Vf(fO,uO);const mO={name:"editorCloseAndSaveByArrowKeys",callback(e,t,o){const r=e._getEditorManager(),i=r.getActiveEditor();i.isInFullEditMode()&&i.state===gO.EDITING||(r.closeEditorAndSaveChanges(),e.getSelected()&&(o.includes("arrowdown")?e.selection.transformStart(1,0):o.includes("arrowup")?e.selection.transformStart(-1,0):o.includes("arrowleft")?e.selection.transformStart(0,-1*e.getDirectionFactor()):o.includes("arrowright")&&e.selection.transformStart(0,e.getDirectionFactor())),t.preventDefault())}},pO={name:"editorCloseAndSaveByEnter",callback(e,t){const o=e._getEditorManager();o.closeEditorAndSaveChanges(t.ctrlKey||t.metaKey),o.moveSelectionAfterEnter(t)}},wO={name:"editorCloseWithoutSaving",callback(e){const t=e._getEditorManager();t.closeEditorAndRestoreOriginalValue(e.getShortcutManager().isCtrlPressed()),t.activeEditor.focus()}},yO={name:"editorFastOpen",callback(e,t){const{highlight:o}=e.getSelectedRangeLast();o.isHeader()||e._getEditorManager().openEditor(null,t,!0)}},vO={name:"editorOpen",callback(e,t,o){const r=e._getEditorManager(),i=e.getSelectedRangeLast(),{highlight:n}=i;if(e.selection.isMultiple()&&!i.isHeader()&&e.countRenderedCols()>0&&e.countRenderedRows()>0){const r=e.getSettings(),i="function"==typeof r.enterMoves?r.enterMoves(t):r.enterMoves;o.includes("shift")?e.selection.transformFocus(-i.row,-i.col):e.selection.transformFocus(i.row,i.col)}else n.isHeader()||(e.getSettings().enterBeginsEditing?r.cellProperties.readOnly?r.moveSelectionAfterEnter(t):r.openEditor(null,t,!0):r.moveSelectionAfterEnter(t),tm(t))}},bO=[hO,mO,pO,wO,yO,vO,{name:"extendCellsSelectionDown",callback(e){const{selection:t}=e,{highlight:o}=e.getSelectedRangeLast();t.isSelectedByColumnHeader()||t.isSelectedByCorner()||!(o.isCell()||o.isHeader()&&t.isSelectedByRowHeader())||(t.markSource("keyboard"),t.transformEnd(1,0),t.markEndSource())}},{name:"extendCellsSelectionDownByViewportHeight",callback(e){const{selection:t,rowIndexMapper:o}=e,{to:r}=e.getSelectedRangeLast(),i=Math.min(r.row+e.countVisibleRows(),e.countRows()-1),n=o.getNearestNotHiddenIndex(i,-1);if(null!==n){const o=e._createCellCoords(n,r.col),i=r.row-e.getFirstFullyVisibleRow(),s=Math.min(o.row-i,e.countRows()-1);t.markSource("keyboard"),t.setRangeEnd(o),t.markEndSource(),e.scrollViewportTo({row:s,verticalSnap:"top",horizontalSnap:"start"})}}},{name:"extendCellsSelectionLeft",callback(e){const{selection:t}=e,{highlight:o}=e.getSelectedRangeLast();t.isSelectedByRowHeader()||t.isSelectedByCorner()||!(o.isCell()||o.isHeader()&&t.isSelectedByColumnHeader())||(t.markSource("keyboard"),t.transformEnd(0,-1*e.getDirectionFactor()),t.markEndSource())}},{name:"extendCellsSelectionRight",callback(e){const{selection:t}=e,{highlight:o}=e.getSelectedRangeLast();t.isSelectedByRowHeader()||t.isSelectedByCorner()||!(o.isCell()||o.isHeader()&&t.isSelectedByColumnHeader())||(t.markSource("keyboard"),t.transformEnd(0,e.getDirectionFactor()),t.markEndSource())}},{name:"extendCellsSelectionToColumns",callback(e){const{selection:t}=e,{highlight:o,from:r,to:i}=e.getSelectedRangeLast();t.markSource("keyboard"),t.isSelectedByRowHeader()?t.selectAll(!0,!0):e.selectColumns(r.col,i.col,o),t.markEndSource()}},{name:"extendCellsSelectionToMostBottom",callback(e){const{selection:t,rowIndexMapper:o}=e,{highlight:r,from:i,to:n}=e.getSelectedRangeLast(),s=r.isHeader()&&t.isSelectedByRowHeader();if(r.isCell()||s){const l=o.getNearestNotHiddenIndex(e.countRows()-1,-1),a=i.clone();a.row=r.row,t.markSource("keyboard"),t.setRangeStart(a,void 0,!1,r.clone()),s&&t.selectedByRowHeader.add(t.getLayerLevel()),t.setRangeEnd(e._createCellCoords(l,n.col)),t.markEndSource()}}},{name:"extendCellsSelectionToMostInlineEnd",callback(e){const{selection:t,columnIndexMapper:o}=e,{highlight:r,from:i,to:n}=e.getSelectedRangeLast();if(!t.isSelectedByRowHeader()&&!t.isSelectedByCorner()&&r.isCell()){const s=o.getNearestNotHiddenIndex(e.countCols()-1,-1),l=i.clone();l.col=r.col,t.markSource("keyboard"),t.setRangeStart(l,void 0,!1,r.clone()),t.setRangeEnd(e._createCellCoords(n.row,s)),t.markEndSource()}}},{name:"extendCellsSelectionToMostInlineStart",callback(e){const{selection:t,columnIndexMapper:o}=e,{highlight:r,from:i,to:n}=e.getSelectedRangeLast();if(!t.isSelectedByRowHeader()&&!t.isSelectedByCorner()&&r.isCell()){const s=parseInt(e.getSettings().fixedColumnsStart,10),l=o.getNearestNotHiddenIndex(s,1),a=i.clone();a.col=r.col,t.markSource("keyboard"),t.setRangeStart(a,void 0,!1,r.clone()),t.setRangeEnd(e._createCellCoords(n.row,l)),t.markEndSource()}}},{name:"extendCellsSelectionToMostLeft",callback(e){const{selection:t,columnIndexMapper:o}=e,{highlight:r,from:i,to:n}=e.getSelectedRangeLast(),s=r.isHeader()&&t.isSelectedByColumnHeader();if(r.isCell()||s){const l=o.getNearestNotHiddenIndex(...e.isRtl()?[e.countCols()-1,-1]:[0,1]),a=i.clone();a.col=r.col,t.markSource("keyboard"),t.setRangeStart(a,void 0,!1,r.clone()),s&&t.selectedByColumnHeader.add(t.getLayerLevel()),t.setRangeEnd(e._createCellCoords(n.row,l)),t.markEndSource()}}},{name:"extendCellsSelectionToMostRight",callback(e){const{selection:t,columnIndexMapper:o}=e,{highlight:r,from:i,to:n}=e.getSelectedRangeLast(),s=r.isHeader()&&t.isSelectedByColumnHeader();if(r.isCell()||s){const l=o.getNearestNotHiddenIndex(...e.isRtl()?[0,1]:[e.countCols()-1,-1]),a=i.clone();a.col=r.col,t.markSource("keyboard"),t.setRangeStart(a,void 0,!1,r.clone()),s&&t.selectedByColumnHeader.add(t.getLayerLevel()),t.setRangeEnd(e._createCellCoords(n.row,l)),t.markEndSource()}}},{name:"extendCellsSelectionToMostTop",callback(e){const{selection:t,rowIndexMapper:o}=e,{highlight:r,from:i,to:n}=e.getSelectedRangeLast(),s=r.isHeader()&&t.isSelectedByRowHeader();if(r.isCell()||s){const l=o.getNearestNotHiddenIndex(0,1),a=i.clone();a.row=r.row,t.markSource("keyboard"),t.setRangeStart(a,void 0,!1,r.clone()),s&&t.selectedByRowHeader.add(t.getLayerLevel()),t.setRangeEnd(e._createCellCoords(l,n.col)),t.markEndSource()}}},{name:"extendCellsSelectionToRows",callback(e){const{selection:t}=e,{highlight:o,from:r,to:i}=e.getSelectedRangeLast();t.markSource("keyboard"),t.isSelectedByColumnHeader()?t.selectAll(!0,!0):e.selectRows(r.row,i.row,o),t.markEndSource()}},{name:"extendCellsSelectionUp",callback(e){const{selection:t}=e,{highlight:o}=e.getSelectedRangeLast();t.isSelectedByColumnHeader()||t.isSelectedByCorner()||!(o.isCell()||o.isHeader()&&t.isSelectedByRowHeader())||(t.markSource("keyboard"),t.transformEnd(-1,0),t.markEndSource())}},{name:"extendCellsSelectionUpByViewportHeight",callback(e){const{selection:t,rowIndexMapper:o}=e,{to:r}=e.getSelectedRangeLast(),i=Math.max(r.row-e.countVisibleRows(),0),n=o.getNearestNotHiddenIndex(i,1);if(null!==n){const o=e._createCellCoords(n,r.col),i=r.row-e.getFirstFullyVisibleRow(),s=Math.max(o.row-i,0);t.markSource("keyboard"),t.setRangeEnd(o),t.markEndSource(),e.scrollViewportTo({row:s,verticalSnap:"top",horizontalSnap:"start"})}}},{name:"moveCellSelectionDown",callback(e){let{selection:t}=e;t.markSource("keyboard"),t.transformStart(1,0),t.markEndSource()}},{name:"moveCellSelectionDownByViewportHeight",callback(e){const{selection:t}=e,{navigableHeaders:o}=e.getSettings(),r=o?e.countColHeaders():0,{row:i}=e.getSelectedRangeLast().highlight;let n=e.countVisibleRows()+r;n=0===n?1:n,i===e.countRows()-1?n=1:i+n>e.countRows()&&(n=e.countRows()-i-1),t.markSource("keyboard"),t.transformStart(n,0),t.markEndSource(),e.getSelectedRangeLast().highlight.row<0&&e.scrollViewportTo({row:0})}},{name:"moveCellSelectionInlineEnd",callback(e,t){const{selection:o}=e,r=e.getSettings(),i=e.getSelectedRangeLast(),n="function"==typeof r.tabMoves?r.tabMoves(t):r.tabMoves;o.markSource("keyboard"),o.isMultiple()&&!i.isHeader()&&e.countRenderedCols()>0&&e.countRenderedRows()>0?o.transformFocus(-n.row,-n.col):o.transformStart(-n.row,-n.col),o.markEndSource()}},{name:"moveCellSelectionInlineStart",callback(e,t){const{selection:o}=e,r=e.getSettings(),i=e.getSelectedRangeLast(),n="function"==typeof r.tabMoves?r.tabMoves(t):r.tabMoves;o.markSource("keyboard"),o.isMultiple()&&!i.isHeader()&&e.countRenderedCols()>0&&e.countRenderedRows()>0?o.transformFocus(n.row,n.col):o.transformStart(n.row,n.col),o.markEndSource()}},{name:"moveCellSelectionLeft",callback(e){const{selection:t}=e;t.markSource("keyboard"),t.transformStart(0,-1*e.getDirectionFactor()),t.markEndSource()}},{name:"moveCellSelectionRight",callback(e){const{selection:t}=e;t.markSource("keyboard"),t.transformStart(0,e.getDirectionFactor()),t.markEndSource()}},{name:"moveCellSelectionToMostBottom",callback(e){const{selection:t}=e,{col:o}=e.getSelectedRangeLast().highlight;let r=e.rowIndexMapper.getNearestNotHiddenIndex(e.countRows()-1,-1);null===r&&(r=-1),t.setRangeStart(e._createCellCoords(r,o))}},{name:"moveCellSelectionToMostBottomInlineEnd",callback(e){const{selection:t,rowIndexMapper:o,columnIndexMapper:r}=e,i=parseInt(e.getSettings().fixedRowsBottom,10),n=o.getNearestNotHiddenIndex(e.countRows()-i-1,-1),s=r.getNearestNotHiddenIndex(e.countCols()-1,-1);t.markSource("keyboard"),t.setRangeStart(e._createCellCoords(n,s)),t.markEndSource()}},{name:"moveCellSelectionToMostInlineEnd",callback(e){const{selection:t,columnIndexMapper:o}=e;t.markSource("keyboard"),t.setRangeStart(e._createCellCoords(e.getSelectedRangeLast().highlight.row,o.getNearestNotHiddenIndex(e.countCols()-1,-1))),t.markEndSource()}},{name:"moveCellSelectionToMostInlineStart",callback(e){const{selection:t,columnIndexMapper:o}=e,r=parseInt(e.getSettings().fixedColumnsStart,10),i=e.getSelectedRangeLast().highlight.row,n=o.getNearestNotHiddenIndex(r,1);t.markSource("keyboard"),t.setRangeStart(e._createCellCoords(i,n)),t.markEndSource()}},{name:"moveCellSelectionToMostLeft",callback(e){const{selection:t,columnIndexMapper:o}=e,r=e.getSelectedRangeLast().highlight.row;let i=o.getNearestNotHiddenIndex(...e.isRtl()?[e.countCols()-1,-1]:[0,1]);null===i&&(i=e.isRtl()?-1:-e.countRowHeaders()),t.markSource("keyboard"),t.setRangeStart(e._createCellCoords(r,i)),t.markEndSource()}},{name:"moveCellSelectionToMostRight",callback(e){const{selection:t,columnIndexMapper:o}=e,{row:r}=e.getSelectedRangeLast().highlight;let i=o.getNearestNotHiddenIndex(...e.isRtl()?[0,1]:[e.countCols()-1,-1]);null===i&&(i=e.isRtl()?-e.countRowHeaders():-1),t.markSource("keyboard"),t.setRangeStart(e._createCellCoords(r,i)),t.markEndSource()}},{name:"moveCellSelectionToMostTop",callback(e){const{selection:t}=e,{col:o}=e.getSelectedRangeLast().highlight;let r=e.rowIndexMapper.getNearestNotHiddenIndex(0,1);null===r&&(r=-e.countColHeaders()),t.markSource("keyboard"),t.setRangeStart(e._createCellCoords(r,o)),t.markEndSource()}},{name:"moveCellSelectionToMostTopInlineStart",callback(e){const{selection:t,rowIndexMapper:o,columnIndexMapper:r}=e,i=parseInt(e.getSettings().fixedRowsTop,10),n=parseInt(e.getSettings().fixedColumnsStart,10),s=o.getNearestNotHiddenIndex(i,1),l=r.getNearestNotHiddenIndex(n,1);t.markSource("keyboard"),t.setRangeStart(e._createCellCoords(s,l)),t.markEndSource()}},{name:"moveCellSelectionUp",callback(e){let{selection:t}=e;t.markSource("keyboard"),t.transformStart(-1,0),t.markEndSource()}},{name:"moveCellSelectionUpByViewportHight",callback(e){const{selection:t}=e,{navigableHeaders:o}=e.getSettings(),r=o?e.countColHeaders():0,{row:i}=e.getSelectedRangeLast().highlight;let n=e.countVisibleRows()+r;n=0===n?-1:-n,i===-r?n=-1:i+n<r&&(n=-(i+r)),t.markSource("keyboard"),t.transformStart(n,0),t.markEndSource(),e.getSelectedRangeLast().highlight.row<0&&e.scrollViewportTo({row:0})}},{name:"emptySelectedCells",callback(e){e.emptySelectedCells(),e._getEditorManager().prepareEditor()}},{name:"scrollToFocusedCell",callback(e){const{highlight:t}=e.getSelectedRangeLast(),o=e.getFirstFullyVisibleRow()-1,r=e.getFirstFullyVisibleColumn()-1,i=e.getLastFullyVisibleRow()+1,n=e.getLastFullyVisibleColumn()+1,s=e._createCellCoords(o,r),l=e._createCellCoords(i,n);if(!e._createCellRange(s,s,l).includes(t)&&(t.row>=0||t.col>=0)){const o={};if(t.col>=0){const r=Math.floor(e.countVisibleCols()/2);o.col=Math.max(t.col-r,0)}if(t.row>=0){const r=Math.floor(e.countVisibleRows()/2);o.row=Math.max(t.row-r,0)}e.scrollViewportTo({...o,verticalSnap:"top",horizontalSnap:"start"})}}},{name:"selectAllCells",callback(e){let{selection:t}=e;t.markSource("keyboard"),t.selectAll(!0,!0,{disableHeadersHighlight:!0}),t.markEndSource()}},{name:"selectAllCellsAndHeaders",callback(e){let{selection:t}=e;t.markSource("keyboard"),t.selectAll(!0,!0,{disableHeadersHighlight:!1}),t.markEndSource()}},{name:"populateSelectedCellsData",callback(e){const t=e.getSelectedRange(),{row:o,col:r}=t[t.length-1].highlight.normalize(),i=e.getDataAtCell(o,r),n=new Map;for(let s=0;s<t.length;s++)t[s].forAll(((t,s)=>{if(t>=0&&s>=0&&(t!==o||s!==r)){const{readOnly:o}=e.getCellMeta(t,s);o||n.set(`${t}x${s}`,[t,s,i])}}));e.setDataAtCell(Array.from(n.values()))}}];function CO(e){const t={};return bO.forEach((o=>{let{name:r,callback:i}=o;t[r]=function(){for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return i(e,...o)}})),t}function SO(e){const t=e.getShortcutManager().addContext("editor"),o=CO(e),r={group:aO};t.addShortcuts([{keys:[["Enter"],["Enter","Shift"]],callback:(e,t)=>o.editorCloseAndSaveByEnter(e,t)},{keys:[["Enter","Control/Meta"],["Enter","Control/Meta","Shift"]],captureCtrl:!0,callback:(e,t)=>o.editorCloseAndSaveByEnter(e,t)},{keys:[["Tab"],["Tab","Shift"],["PageDown"],["PageUp"]],forwardToContext:e.getShortcutManager().getContext("grid"),callback:(e,t)=>o.editorCloseAndSave(e,t)},{keys:[["ArrowDown"],["ArrowUp"],["ArrowLeft"],["ArrowRight"]],preventDefault:!1,callback:(e,t)=>o.editorCloseAndSaveByArrowKeys(e,t)},{keys:[["Escape"],["Escape","Control/Meta"]],callback:()=>o.editorCloseWithoutSaving()}],r)}function RO(e){const t=e.getShortcutManager().addContext("grid"),o=CO(e),r={runOnlyIf:()=>{const{navigableHeaders:t}=e.getSettings();return Gd(e.getSelected())&&(t||!t&&e.countRenderedRows()>0&&e.countRenderedCols()>0)},group:lO};t.addShortcuts([{keys:[["F2"]],callback:e=>o.editorFastOpen(e)},{keys:[["Enter"],["Enter","Shift"]],callback:(e,t)=>o.editorOpen(e,t)},{keys:[["Backspace"],["Delete"]],callback:()=>o.emptySelectedCells()}],{group:aO,runOnlyIf:()=>Gd(e.getSelected())}),t.addShortcuts([{keys:[["Control/Meta","A"]],callback:()=>o.selectAllCells(),runOnlyIf:()=>{var t;return!(null!==(t=e.getSelectedRangeLast())&&void 0!==t&&t.highlight.isHeader())}},{keys:[["Control/Meta","A"]],callback:()=>{},runOnlyIf:()=>{var t;return null===(t=e.getSelectedRangeLast())||void 0===t?void 0:t.highlight.isHeader()},preventDefault:!0},{keys:[["Control/Meta","Shift","Space"]],callback:()=>o.selectAllCellsAndHeaders()},{keys:[["Control/Meta","Enter"]],callback:()=>o.populateSelectedCellsData(),runOnlyIf:()=>{var t,o;return!(null!==(t=e.getSelectedRangeLast())&&void 0!==t&&t.highlight.isHeader())&&(null===(o=e.getSelectedRangeLast())||void 0===o?void 0:o.getCellsCount())>1}},{keys:[["Control","Space"]],captureCtrl:!0,callback:()=>o.extendCellsSelectionToColumns()},{keys:[["Shift","Space"]],stopPropagation:!0,callback:()=>o.extendCellsSelectionToRows()},{keys:[["ArrowUp"]],callback:()=>o.moveCellSelectionUp()},{keys:[["ArrowUp","Control/Meta"]],captureCtrl:!0,callback:()=>o.moveCellSelectionToMostTop()},{keys:[["ArrowUp","Shift"]],callback:()=>o.extendCellsSelectionUp()},{keys:[["ArrowUp","Shift","Control/Meta"]],captureCtrl:!0,callback:()=>o.extendCellsSelectionToMostTop(),runOnlyIf:()=>!(e.selection.isSelectedByCorner()||e.selection.isSelectedByColumnHeader())},{keys:[["ArrowDown"]],callback:()=>o.moveCellSelectionDown()},{keys:[["ArrowDown","Control/Meta"]],captureCtrl:!0,callback:()=>o.moveCellSelectionToMostBottom()},{keys:[["ArrowDown","Shift"]],callback:()=>o.extendCellsSelectionDown()},{keys:[["ArrowDown","Shift","Control/Meta"]],captureCtrl:!0,callback:()=>o.extendCellsSelectionToMostBottom(),runOnlyIf:()=>!(e.selection.isSelectedByCorner()||e.selection.isSelectedByColumnHeader())},{keys:[["ArrowLeft"]],callback:()=>o.moveCellSelectionLeft()},{keys:[["ArrowLeft","Control/Meta"]],captureCtrl:!0,callback:()=>o.moveCellSelectionToMostLeft()},{keys:[["ArrowLeft","Shift"]],callback:()=>o.extendCellsSelectionLeft()},{keys:[["ArrowLeft","Shift","Control/Meta"]],captureCtrl:!0,callback:()=>o.extendCellsSelectionToMostLeft(),runOnlyIf:()=>!(e.selection.isSelectedByCorner()||e.selection.isSelectedByRowHeader())},{keys:[["ArrowRight"]],callback:()=>o.moveCellSelectionRight()},{keys:[["ArrowRight","Control/Meta"]],captureCtrl:!0,callback:()=>o.moveCellSelectionToMostRight()},{keys:[["ArrowRight","Shift"]],callback:()=>o.extendCellsSelectionRight()},{keys:[["ArrowRight","Shift","Control/Meta"]],captureCtrl:!0,callback:()=>o.extendCellsSelectionToMostRight(),runOnlyIf:()=>!(e.selection.isSelectedByCorner()||e.selection.isSelectedByRowHeader())},{keys:[["Home"]],captureCtrl:!0,callback:()=>o.moveCellSelectionToMostInlineStart(),runOnlyIf:()=>e.view.isMainTableNotFullyCoveredByOverlays()},{keys:[["Home","Shift"]],callback:()=>o.extendCellsSelectionToMostInlineStart()},{keys:[["Home","Control/Meta"]],captureCtrl:!0,callback:()=>o.moveCellSelectionToMostTopInlineStart(),runOnlyIf:()=>e.view.isMainTableNotFullyCoveredByOverlays()},{keys:[["End"]],captureCtrl:!0,callback:()=>o.moveCellSelectionToMostInlineEnd(),runOnlyIf:()=>e.view.isMainTableNotFullyCoveredByOverlays()},{keys:[["End","Shift"]],callback:()=>o.extendCellsSelectionToMostInlineEnd()},{keys:[["End","Control/Meta"]],captureCtrl:!0,callback:()=>o.moveCellSelectionToMostBottomInlineEnd(),runOnlyIf:()=>e.view.isMainTableNotFullyCoveredByOverlays()},{keys:[["PageUp"]],callback:()=>o.moveCellSelectionUpByViewportHight()},{keys:[["PageUp","Shift"]],callback:()=>o.extendCellsSelectionUpByViewportHeight()},{keys:[["PageDown"]],callback:()=>o.moveCellSelectionDownByViewportHeight()},{keys:[["PageDown","Shift"]],callback:()=>o.extendCellsSelectionDownByViewportHeight()},{keys:[["Tab"]],preventDefault:!1,callback:e=>o.moveCellSelectionInlineStart(e)},{keys:[["Shift","Tab"]],preventDefault:!1,callback:e=>o.moveCellSelectionInlineEnd(e)},{keys:[["Control/Meta","Backspace"]],callback:()=>o.scrollToFocusedCell()}],r)}function TO(e){const t=e.rootDocument.createElement("input");var o;return t.type="text",t.name="__htFocusCatcher",t.classList.add("htFocusCatcher"),e.getSettings().ariaTags&&Tg(t,[(o="Focus catcher",["aria-label",o])]),t}function EO(e){const t=function(e){return t=>{if(!t)return null;const o=HO(e),r=OO(e);return t.col<o.col&&(t.col=o.col),t.col>r.col&&(t.col=r.col),t.row<o.row&&(t.row=o.row),t.row>r.row&&(t.row=r.row),t}}(e);let o;const{activate:r,deactivate:i}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const o=e.rootElement,r=TO(e),i=TO(e);return r.addEventListener("focus",(()=>null==t?void 0:t.onFocusFromTop())),i.addEventListener("focus",(()=>null==t?void 0:t.onFocusFromBottom())),o.firstChild.before(r),o.lastChild.after(i),{activate(){e._registerTimeout((()=>{r.tabIndex=0,i.tabIndex=0}),10)},deactivate(){e._registerTimeout((()=>{r.tabIndex=-1,i.tabIndex=-1}),10)}}}(e,{onFocusFromTop(){var r;const i=null!==(r=t(o))&&void 0!==r?r:HO(e);i&&(e.runHooks("modifyFocusOnTabNavigation","from_above",i),e.selectCell(i.row,i.col)),e.listen()},onFocusFromBottom(){var r;const i=null!==(r=t(o))&&void 0!==r?r:OO(e);i&&(e.runHooks("modifyFocusOnTabNavigation","from_below",i),e.selectCell(i.row,i.col)),e.listen()}}),n={wrapped:!1,flipped:!1};let s=!0,l=!1,a=!1;e.addHook("afterListen",(()=>i())),e.addHook("afterUnlisten",(()=>r())),e.addHook("afterSelection",((t,r,i,h,c)=>{var u;l&&(n.wrapped&&n.flipped||a)&&(a=!1,c.value=!0),s&&(o=null===(u=e.getSelectedRangeLast())||void 0===u?void 0:u.highlight)})),e.addHook("beforeRowWrap",((e,t,o)=>{n.wrapped=!0,n.flipped=o}));const h={keys:[["Tab"],["Shift","Tab"]],preventDefault:!1,stopPropagation:!1,relativeToGroup:lO,group:"focusCatcher"};e.getShortcutManager().getContext("grid").addShortcuts([{...h,callback:()=>{const{tabNavigation:t}=e.getSettings();l=!0,e.getSelectedRangeLast()&&!t&&(s=!1),t||(a=!0)},position:"before"},{...h,callback:t=>{const{tabNavigation:r,autoWrapRow:i}=e.getSettings();if(l=!1,s=!0,!r||!e.selection.isSelected()||i&&n.wrapped&&n.flipped||!i&&n.wrapped)return i&&n.wrapped&&n.flipped&&(o=t.shiftKey?HO(e):OO(e)),n.wrapped=!1,n.flipped=!1,e.deselectCell(),e.unlisten(),!1;t.preventDefault()},position:"after"}])}function HO(e){const{rowIndexMapper:t,columnIndexMapper:o}=e,{navigableHeaders:r}=e.getSettings();let i=r&&e.countColHeaders()>0?-e.countColHeaders():0,n=r&&e.countRowHeaders()>0?-e.countRowHeaders():0;return 0===i&&(i=t.getVisualFromRenderableIndex(i)),0===n&&(n=o.getVisualFromRenderableIndex(n)),null===i||null===n?null:e._createCellCoords(i,n)}function OO(e){var t,o;const{rowIndexMapper:r,columnIndexMapper:i}=e,{navigableHeaders:n}=e.getSettings();let s=r.getRenderableIndexesLength()-1,l=i.getRenderableIndexesLength()-1;if(s<0){if(!n||0===e.countColHeaders())return null;s=-1}if(l<0){if(!n||0===e.countColHeaders())return null;l=-1}return e._createCellCoords(null!==(t=r.getVisualFromRenderableIndex(s))&&void 0!==t?t:s,null!==(o=i.getVisualFromRenderableIndex(l))&&void 0!==o?o:l)}function MO(e){Jg(e)&&e.scrollIntoView({block:"nearest",inline:"nearest"})}function xO(e){const{selection:t,view:o}=e,r=e.getSelectedRangeLast(),i=t.getSelectionSource(),n=o.getFirstFullyVisibleColumn(),s=o.getLastFullyVisibleColumn(),l=r.getTopStartCorner().col,a=r.getBottomEndCorner().col,h=l<=n,c=a>=s,u=o.getFirstFullyVisibleRow(),d=o.getLastFullyVisibleRow(),g=r.getTopStartCorner().row,f=r.getBottomEndCorner().row,m=g<=u,p=f>=d;return{getComputedColumnTarget:e=>"mouse"===i||"keyboard"===i?e.col:h&&c?r.highlight.col:h?l:c?a:e.col,getComputedRowTarget:e=>"mouse"===i||"keyboard"===i?e.row:m&&p?r.highlight.row:m?g:p?f:e.row}}function IO(e){const{selection:t}=e;let o=!1,r=!1;return{resume(){r=!1},suspend(){r=!0},skipNextScrollCycle(){o=!0},scrollTo(i){var n;if(o||r)return void(o=!1);let s;t.isFocusSelectionChanged()?s=function(e){return t=>{e.scrollViewportTo(t.toObject(),(()=>{const{row:t,col:o}=e.getSelectedRangeLast().highlight;MO(e.getCell(t,o,!0))}))}}(e):t.isSelectedByCorner()?s=()=>{}:t.isSelectedByRowHeader()?s=function(e){return t=>{const o=xO(e).getComputedRowTarget(t);e.scrollViewportTo({row:o},(()=>{const t=!!e.getSettings().rowHeaders;MO(e.getCell(o,t?-1:0,!0))}))}}(e):t.isSelectedByColumnHeader()?s=function(e){return t=>{const o=xO(e).getComputedColumnTarget(t);e.scrollViewportTo({col:o},(()=>{const t=!!e.getSettings().colHeaders;MO(e.getCell(t?-1:0,o,!0))}))}}(e):1===t.getSelectedRange().size()&&t.isMultiple()?s=function(e){return t=>{const o=xO(e),r={row:o.getComputedRowTarget(t),col:o.getComputedColumnTarget(t)};e.scrollViewportTo(r,(()=>{const{row:t,col:o}=r;MO(e.getCell(t,o,!0))}))}}(e):1!==t.getSelectedRange().size()||t.isMultiple()?t.getSelectedRange().size()>1&&(s=function(e){return t=>{const o=xO(e),r={row:o.getComputedRowTarget(t),col:o.getComputedColumnTarget(t)};e.scrollViewportTo(r,(()=>{const{row:t,col:o}=r;MO(e.getCell(t,o,!0))}))}}(e)):s=function(e){return t=>{const o=e.selection.getSelectionSource(),{row:r,col:i}=t,n=()=>{MO(e.getCell(r,i,!0))};if(r<0&&i>=0)e.scrollViewportTo({col:i},n);else if(i<0&&r>=0)e.scrollViewportTo({row:r},n);else{if("mouse"===o&&(i===e.view.getLastPartiallyVisibleColumn()||r===e.view.getLastPartiallyVisibleRow()))return;e.scrollViewportTo({row:r,col:i},n)}}}(e),null===(n=s)||void 0===n||n(i)}}}const NO=new Map([[" ","space"],["spacebar","space"],["scroll","scrolllock"],["del","delete"],["esc","escape"],["medianexttrack","mediatracknext"],["mediaprevioustrack","mediatrackprevious"],["volumeup","audiovolumeup"],["volumedown","audiovolumedown"],["volumemute","audiovolumemute"],["multiply","*"],["add","+"],["divide","/"],["subtract","-"],["left","arrowleft"],["right","arrowright"],["up","arrowup"],["down","arrowdown"]]),AO=e=>e.map((e=>{const t=e.toLowerCase();return NO.has(t)?NO.get(t):t})).sort().join("+"),kO=new Map([[96,"numpad0"],[97,"numpad1"],[98,"numpad2"],[99,"numpad3"],[100,"numpad4"],[101,"numpad5"],[102,"numpad6"],[103,"numpad7"],[104,"numpad8"],[105,"numpad9"],[106,"multiply"],[107,"add"],[108,"decimal"],[109,"subtract"],[110,"decimal"],[111,"divide"],[112,"f1"],[113,"f2"],[114,"f3"],[115,"f4"],[116,"f5"],[117,"f6"],[118,"f7"],[119,"f8"],[120,"f9"],[121,"f10"],[122,"f11"],[123,"f12"],[186,"semicolon"],[187,"equal"],[188,"comma"],[189,"minus"],[190,"period"],[191,"slash"],[192,"backquote"],[219,"bracketleft"],[220,"backslash"],[221,"bracketright"],[222,"quote"]]),_O=e=>{let{which:t,key:o}=e;if(kO.has(t))return kO.get(t);const r=String.fromCharCode(t).toLowerCase();return/^[a-z0-9]$/.test(r)?r:o.toLowerCase()},PO=Symbol("shortcut-context");function VO(e){return Df(e)&&e.__kindOf===PO}const LO=["meta","alt","shift","control"],DO=function(){const e=new Set;return{press(t){e.add(t)},release(t){e.delete(t)},releaseAll(){e.clear()},isPressed:t=>e.has(t)}}(),FO=[];let BO=0;const WO=e=>{let{ownerWindow:t,handleEvent:o,beforeKeyDown:r,afterKeyDown:i}=e;const n=kw({errorIdExists:e=>`The "${e}" context name is already registered.`});let s="grid";const l=()=>s,a=e=>n.getItem(e);let h=!1;const c=function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:l();const r=VO(o)?o:a(o);let i=!1;if(!r.hasShortcut(t))return i;const n=r.getShortcuts(t);for(let s=0;s<n.length;s++){const{callback:o,runOnlyIf:r,preventDefault:l,stopPropagation:a,captureCtrl:u,forwardToContext:d}=n[s];if(!0===r(e)){if(h=u,i=!1===o(e,t),h=!1,l&&e.preventDefault(),a&&(tm(e),e.stopPropagation()),i)break;d&&c(e,t,d)}}return i},u=function(e,t,o,r,i){const n=e=>LO.includes(e),s=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=[];return e.altKey&&o.push("alt"),t&&(e.ctrlKey||e.metaKey)?o.push("control/meta"):(e.ctrlKey&&o.push("control"),e.metaKey&&o.push("meta")),e.shiftKey&&o.push("shift"),o},l=e=>{if(!1===t(e))return;if(!1===o(e)||229===e.keyCode||"string"!=typeof e.key||om(e))return;const l=_O(e);let a=[];n(l)||(a=s(e));const h=[l].concat(a);!i(e,h)&&(Jf()&&a.includes("meta")||!Jf()&&a.includes("control"))&&i(e,[l].concat(s(e,!0))),r(e)},a=e=>{if("string"==typeof e.key){const t=_O(e);n(t)&&DO.press(t)}},h=e=>{if("string"==typeof e.key){const t=_O(e);n(t)&&DO.release(t)}},c=()=>{DO.releaseAll()};return{mount:()=>{let t=e;for(BO+=1;t;)1===BO&&(t.document.documentElement.addEventListener("keydown",a),FO.push({event:"keydown",listener:a}),t.document.documentElement.addEventListener("keyup",h),FO.push({event:"keyup",listener:h})),t.document.documentElement.addEventListener("keydown",l),t.document.documentElement.addEventListener("blur",c),t=fg(t)},unmount:()=>{let t=e;for(BO-=1;t;){if(0===BO){for(let e=0;e<FO.length;e++){const{event:o,listener:r}=FO[e];t.document.documentElement.removeEventListener(o,r)}FO.length=0}t.document.documentElement.removeEventListener("keydown",l),t.document.documentElement.removeEventListener("blur",c),t=fg(t)}},isPressed:e=>DO.isPressed(e),releasePressedKeys:()=>DO.releaseAll()}}(t,o,r,i,c);return u.mount(),{addContext:e=>{const t=(e=>{const t=kw({errorIdExists:t=>`The "${t}" shortcut is already registered in the "${e}" context.`}),o=function(){let{keys:e,callback:o,group:r,runOnlyIf:i=()=>!0,captureCtrl:n=!1,preventDefault:s=!0,stopPropagation:l=!1,relativeToGroup:a,position:h,forwardToContext:c}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(Yd(r))throw new Error("You need to define the shortcut's group.");if(!1===Zg(o))throw new Error("The shortcut's callback needs to be a function.");if(!1===Array.isArray(e))throw new Error(Ud`Pass the shortcut\'s keys as an array of arrays,\x20
      using the KeyboardEvent.key properties:\x20
      https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key/Key_Values.`);const u={callback:o,group:r,runOnlyIf:i,captureCtrl:n,preventDefault:s,stopPropagation:l};Gd(a)&&(u.relativeToGroup=a,u.position=h),VO(c)&&(u.forwardToContext=c),e.forEach((e=>{const o=AO(e);if(t.hasItem(o)){const e=t.getItem(o);let r=e.findIndex((e=>e.group===a));-1!==r?"before"===h?r-=1:r+=1:r=e.length,e.splice(r,0,u)}else t.addItem(o,[u])}))},r=e=>{const o=AO(e);t.removeItem(o)};return{__kindOf:PO,addShortcut:o,addShortcuts:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e.forEach((e=>{Bf(t,((o,r)=>{!1===Object.prototype.hasOwnProperty.call(e,r)&&(e[r]=t[r])})),o(e)}))},getShortcuts:e=>{const o=AO(e),r=t.getItem(o);return Gd(r)?r.slice():[]},hasShortcut:e=>{const o=AO(e);return t.hasItem(o)},removeShortcutsByKeys:r,removeShortcutsByGroup:e=>{t.getItems().forEach((t=>{let[o,i]=t;const n=i.filter((t=>t.group!==e));0===n.length?r((e=>e.split("+"))(o)):(i.length=0,i.push(...n))}))}}})(e);return n.addItem(e,t),t},getActiveContextName:l,getContext:a,setActiveContextName:e=>{if(!n.hasItem(e))throw new Error(Ud`You've tried to activate the "${e}" shortcut context\x20
        that does not exist. Before activation, register the context using the "addContext" method.`);s=e},isCtrlPressed:()=>!h&&(u.isPressed("control")||u.isPressed("meta")),releasePressedKeys:()=>u.releasePressedKeys(),destroy:()=>u.unmount()}};function jO(e){const{classNames:t}=function(e,t){const o={element:void 0,classNames:[]};let r=e;for(;null!==r&&r!==e.ownerDocument.documentElement&&!o.element;){if("string"==typeof t&&r.classList.contains(t))o.element=r,o.classNames.push(t);else if(t instanceof RegExp){const e=Array.from(r.classList).filter((e=>t.test(e)));e.length&&(o.element=r,o.classNames.push(...e))}r=r.parentElement}return o}(e,/ht-theme-[a-zA-Z0-9_-]+/);return t.pop()}let zO=null;const $O=new Map;function UO(e,t){var o,r=this;let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=this;const s=new sp(n);let l,a,h,c,u,d,g=!0;var f;pS(i)&&(f=this,fS.set(f,!0)),this.rootElement=e,this.rootDocument=e.ownerDocument,this.rootWindow=this.rootDocument.defaultView,this.isDestroyed=!1,this.renderSuspendedCounter=0,this.executionSuspendedCounter=0;const m=null!==(o=null==t?void 0:t.layoutDirection)&&void 0!==o?o:"inherit",p=["rtl","ltr"].includes(m)?m:this.rootWindow.getComputedStyle(this.rootElement).direction;this.rootElement.setAttribute("dir",p),this.isRtl=function(){return"rtl"===p},this.isLtr=function(){return!n.isRtl()},this.getDirectionFactor=function(){return n.isLtr()?1:-1},t.language=function(e){let t=LR(e);return HE(t)||(t=vE,DR(e)),t}(t.language);const w=Object.fromEntries(Object.entries(t).filter((e=>{let[t]=e;return!(Xm.getSingleton().isRegistered(t)||Xm.getSingleton().isDeprecated(t))}))),y=new JH(n,w,[tO,sO]),v=y.getTableMeta(),b=y.getGlobalMeta(),C=kw();this.container=this.rootDocument.createElement("div"),e.insertBefore(this.container,e.firstChild),wS(this)&&(ng(t.licenseKey,e),Sg(e,"ht-wrapper")),this.guid=`ht_${lg()}`,$O.set(this.guid,this),this.columnIndexMapper=new VR,this.rowIndexMapper=new VR,this.columnIndexMapper.addLocalHook("indexesSequenceChange",(e=>{n.runHooks("afterColumnSequenceChange",e)})),this.rowIndexMapper.addLocalHook("indexesSequenceChange",(e=>{n.runHooks("afterRowSequenceChange",e)})),s.addEventListener(this.rootDocument.documentElement,"compositionstart",(e=>{n.runHooks("beforeCompositionStart",e)})),a=new GS(n),this.rootElement.id&&"ht_"!==this.rootElement.id.substring(0,3)||(this.rootElement.id=this.guid);let S=new HH(v,{rowIndexMapper:n.rowIndexMapper,columnIndexMapper:n.columnIndexMapper,countCols:()=>n.countCols(),countRows:()=>n.countRows(),propToCol:e=>l.propToCol(e),isEditorOpened:()=>!!n.getActiveEditor()&&n.getActiveEditor().isOpened(),countRenderableColumns:()=>this.view.countRenderableColumns(),countRenderableRows:()=>this.view.countRenderableRows(),countRowHeaders:()=>this.countRowHeaders(),countColHeaders:()=>this.countColHeaders(),countRenderableRowsInRange:function(){return r.view.countRenderableRowsInRange(...arguments)},countRenderableColumnsInRange:function(){return r.view.countRenderableColumnsInRange(...arguments)},getShortcutManager:()=>n.getShortcutManager(),createCellCoords:(e,t)=>n._createCellCoords(e,t),createCellRange:(e,t,o)=>n._createCellRange(e,t,o),visualToRenderableCoords:e=>{const{row:t,col:o}=e;return n._createCellCoords(t>=0?n.rowIndexMapper.getRenderableFromVisualIndex(t):t,o>=0?n.columnIndexMapper.getRenderableFromVisualIndex(o):o)},renderableToVisualCoords:e=>{const{row:t,col:o}=e;return n._createCellCoords(t>=0?n.rowIndexMapper.getVisualFromRenderableIndex(t):t,o>=0?n.columnIndexMapper.getVisualFromRenderableIndex(o):o)},findFirstNonHiddenRenderableRow:(e,t)=>{const o=t>e?1:-1,r=Math.min(e,t),i=Math.max(e,t),s=n.rowIndexMapper.getNearestNotHiddenIndex(e,o);return null===s||1===o&&s>i||-1===o&&s<r?null:s>=0?n.rowIndexMapper.getRenderableFromVisualIndex(s):s},findFirstNonHiddenRenderableColumn:(e,t)=>{const o=t>e?1:-1,r=Math.min(e,t),i=Math.max(e,t),s=n.columnIndexMapper.getNearestNotHiddenIndex(e,o);return null===s||1===o&&s>i||-1===o&&s<r?null:s>=0?n.columnIndexMapper.getRenderableFromVisualIndex(s):s},isDisabledCellSelection:(e,t)=>e<0||t<0?n.getSettings().disableVisualSelection:n.getCellMeta(e,t).disableVisualSelection});this.selection=S;const R=e=>{let{hiddenIndexesChanged:t}=e;this.forceFullRender=!0,t&&this.selection.commit()};function T(e,t){const o="className"===e?n.rootElement:n.table;if(g)Sg(o,t);else{let r=[],i=[];b[e]&&(r=Array.isArray(b[e])?b[e]:$d(b[e])),t&&(i=Array.isArray(t)?t:$d(t));const n=zd(r,i),s=zd(i,r);n.length&&Rg(o,n),s.length&&Sg(o,s)}b[e]=t}function E(){let e=!1;return{validatorsInQueue:0,valid:!0,addValidatorToQueue(){this.validatorsInQueue+=1,e=!1},removeValidatorFormQueue(){this.validatorsInQueue=this.validatorsInQueue-1<0?0:this.validatorsInQueue-1,this.checkIfQueueIsEmpty()},onQueueEmpty(){},checkIfQueueIsEmpty(){0===this.validatorsInQueue&&!1===e&&(e=!0,this.onQueueEmpty(this.valid))}}}function H(e){const t=e.replace(",",".");return!1===isNaN(parseFloat(t))?parseFloat(t):e}function O(e,t,o){if(!e.length)return void o();const r=n.getActiveEditor(),i=new E;let s=!0;i.onQueueEmpty=()=>{r&&s&&r.cancelChanges(),o()};for(let a=e.length-1;a>=0;a--){const[o,r]=e[a],h=l.propToCol(r);let c;c=Number.isInteger(h)?n.getCellMeta(o,h):{...Object.getPrototypeOf(v),...v},n.getCellValidator(c)&&(i.addValidatorToQueue(),n.validateCell(e[a][3],c,function(t,o){return function(r){if("boolean"!=typeof r)throw new Error("Validation error: result is not boolean");!1===r&&!1===o.allowInvalid&&(s=!1,e.splice(t,1),o.valid=!0),i.removeValidatorFormQueue()}}(a,c),t))}i.checkIfQueueIsEmpty()}function M(e,t){for(let o=e.length-1;o>=0;o--){let t=!1;if(null!==e[o]){if(null!==e[o][2]&&void 0!==e[o][2]||null!==e[o][3]&&void 0!==e[o][3]){if(v.allowInsertRow)for(;e[o][0]>n.countRows()-1;){const{delta:e}=l.createRow(void 0,void 0,{source:"auto"});if(0===e){t=!0;break}}if("array"===n.dataType&&(!v.columns||0===v.columns.length)&&v.allowInsertColumn)for(;l.propToCol(e[o][1])>n.countCols()-1;){const{delta:e}=l.createCol(void 0,void 0,{source:"auto"});if(0===e){t=!0;break}}t||l.set(e[o][0],e[o][1],e[o][3])}}else e.splice(o,1)}if(e.length>0){h.adjustRowsAndCols(),n.runHooks("beforeChangeRender",e,t),c.closeEditor(),n.render(),c.prepareEditor(),n.view.adjustElementsSize(),n.runHooks("afterChange",e,t||"edit");const o=n.getActiveEditor();o&&Gd(o.refreshValue)&&o.refreshValue()}else n.render()}function x(e,t,o){return Array.isArray(e)?e:[[e,t,o]]}function I(e,t){const o=n.runHooks("beforeChange",e,t||"edit"),r=e.filter((e=>null!==e));var i;if(!1===o||0===r.length)return null===(i=n.getActiveEditor())||void 0===i||i.cancelChanges(),[];for(let s=r.length-1;s>=0;s--){const[e,t,,o]=r[s],i=l.propToCol(t);let a;a=Number.isInteger(i)?n.getCellMeta(e,i):{...Object.getPrototypeOf(v),...v};const{type:h,checkedTemplate:c,uncheckedTemplate:u}=a;if("numeric"===h&&"string"==typeof o&&Tw(o,[","])&&(r[s][3]=H(o)),"checkbox"===h){const e=Xd(o),t=e===Xd(c),i=e===Xd(u);(t||i)&&(r[s][3]=t?c:u)}}return r}this.columnIndexMapper.addLocalHook("cacheUpdated",R),this.rowIndexMapper.addLocalHook("cacheUpdated",R),this.selection.addLocalHook("afterSetRangeEnd",((e,t)=>{const o=zf(!1),r=this.selection.getSelectedRange(),{from:i,to:s}=r.current(),l=r.size()-1;this.runHooks("afterSelection",i.row,i.col,s.row,s.col,o,l),this.runHooks("afterSelectionByProp",i.row,n.colToProp(i.col),s.row,n.colToProp(s.col),o,l),t&&(!o.isTouched()||o.isTouched()&&!o.value)&&d.scrollTo(e);const a=S.isSelectedByRowHeader(),h=S.isSelectedByColumnHeader();a&&h?Sg(this.rootElement,["ht__selection--rows","ht__selection--columns"]):a?(Rg(this.rootElement,"ht__selection--columns"),Sg(this.rootElement,"ht__selection--rows")):h?(Rg(this.rootElement,"ht__selection--rows"),Sg(this.rootElement,"ht__selection--columns")):Rg(this.rootElement,["ht__selection--rows","ht__selection--columns"]),"shift"!==S.getSelectionSource()&&c.closeEditor(null),n.view.render(),c.prepareEditor()})),this.selection.addLocalHook("beforeSetFocus",(e=>{this.runHooks("beforeSelectionFocusSet",e.row,e.col)})),this.selection.addLocalHook("afterSetFocus",(e=>{const t=zf(!1);this.runHooks("afterSelectionFocusSet",e.row,e.col,t),(!t.isTouched()||t.isTouched()&&!t.value)&&d.scrollTo(e),c.closeEditor(),n.view.render(),c.prepareEditor()})),this.selection.addLocalHook("afterSelectionFinished",(e=>{const t=e.length-1,{from:o,to:r}=e[t];this.runHooks("afterSelectionEnd",o.row,o.col,r.row,r.col,t),this.runHooks("afterSelectionEndByProp",o.row,n.colToProp(o.col),r.row,n.colToProp(r.col),t)})),this.selection.addLocalHook("afterIsMultipleSelection",(e=>{const t=this.runHooks("afterIsMultipleSelection",e.value);e.value&&(e.value=t)})),this.selection.addLocalHook("afterDeselect",(()=>{c.closeEditor(),n.view.render(),Rg(this.rootElement,["ht__selection--rows","ht__selection--columns"]),this.runHooks("afterDeselect")})),this.selection.addLocalHook("beforeHighlightSet",(()=>this.runHooks("beforeSelectionHighlightSet"))).addLocalHook("beforeSetRangeStart",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("beforeSetRangeStart",...t)})).addLocalHook("beforeSetRangeStartOnly",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("beforeSetRangeStartOnly",...t)})).addLocalHook("beforeSetRangeEnd",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("beforeSetRangeEnd",...t)})).addLocalHook("beforeSelectColumns",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("beforeSelectColumns",...t)})).addLocalHook("afterSelectColumns",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("afterSelectColumns",...t)})).addLocalHook("beforeSelectRows",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("beforeSelectRows",...t)})).addLocalHook("afterSelectRows",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("afterSelectRows",...t)})).addLocalHook("beforeModifyTransformStart",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("modifyTransformStart",...t)})).addLocalHook("afterModifyTransformStart",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("afterModifyTransformStart",...t)})).addLocalHook("beforeModifyTransformFocus",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("modifyTransformFocus",...t)})).addLocalHook("afterModifyTransformFocus",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("afterModifyTransformFocus",...t)})).addLocalHook("beforeModifyTransformEnd",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("modifyTransformEnd",...t)})).addLocalHook("afterModifyTransformEnd",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("afterModifyTransformEnd",...t)})).addLocalHook("beforeRowWrap",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("beforeRowWrap",...t)})).addLocalHook("beforeColumnWrap",(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return r.runHooks("beforeColumnWrap",...t)})).addLocalHook("insertRowRequire",(e=>this.alter("insert_row_above",e,1,"auto"))).addLocalHook("insertColRequire",(e=>this.alter("insert_col_start",e,1,"auto"))),h={alter(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;const s=e=>{if(0===e.length)return[];const t=[...e];return t.sort(((e,t)=>{let[o]=e,[r]=t;return o===r?0:o>r?1:-1})),Fd(t,((e,t)=>{let[o,r]=t;const i=e[e.length-1],[n,s]=i,l=n+s;if(o<=l){const e=Math.max(r-(l-o),0);i[1]+=e}else e.push([o,r]);return e}),[t[0]])};switch(e){case"insert_row_below":case"insert_row_above":const i=n.countSourceRows();if(v.maxRows===i)return;const a="insert_row_below"===e?"below":"above";t=null!=t?t:"below"===a?i:0;const{delta:h,startPhysicalIndex:u}=l.createRow(t,o,{source:r,mode:a});S.shiftRows(n.toVisualRow(u),h);break;case"insert_col_start":case"insert_col_end":const d="insert_col_end"===e?"end":"start";t=null!=t?t:"end"===d?n.countSourceCols():0;const{delta:g,startPhysicalIndex:f}=l.createCol(t,o,{source:r,mode:d});if(g){if(Array.isArray(v.colHeaders)){const e=[n.toVisualColumn(f),0];e.length+=g,Array.prototype.splice.apply(v.colHeaders,e)}S.shiftColumns(n.toVisualColumn(f),g)}break;case"remove_row":const m=e=>{let t=0;jd(e,(e=>{let[o,i]=e;const s=Kd(o)?n.countRows()-1:Math.max(o-t,0);if(Number.isInteger(o)&&(o=Math.max(o-t,0)),!l.removeRow(o,i,r))return;if(S.isSelected()){const{row:e}=n.getSelectedRangeLast().highlight;e>=o&&e<=o+i-1&&c.closeEditor(!0)}const a=n.countRows(),h=v.fixedRowsTop;h>=s+1&&(v.fixedRowsTop-=Math.min(i,h-s));const u=v.fixedRowsBottom;u&&s>=a-u&&(v.fixedRowsBottom-=Math.min(i,u)),0===a?S.deselect():"ContextMenu.removeRow"===r?S.refresh():S.shiftRows(o,-i),t+=i}))};Array.isArray(t)?m(s(t)):m([[t,o]]);break;case"remove_col":const p=e=>{let t=0;jd(e,(e=>{let[o,i]=e;const s=Kd(o)?n.countCols()-1:Math.max(o-t,0);let a=n.toPhysicalColumn(s);if(Number.isInteger(o)&&(o=Math.max(o-t,0)),!l.removeCol(o,i,r))return;if(S.isSelected()){const{col:e}=n.getSelectedRangeLast().highlight;e>=o&&e<=o+i-1&&c.closeEditor(!0)}0===n.countCols()?S.deselect():"ContextMenu.removeColumn"===r?S.refresh():S.shiftColumns(o,-i);const h=v.fixedColumnsStart;h>=s+1&&(v.fixedColumnsStart-=Math.min(i,h-s)),Array.isArray(v.colHeaders)&&(void 0===a&&(a=-1),v.colHeaders.splice(a,i)),t+=i}))};Array.isArray(t)?p(s(t)):p([[t,o]]);break;default:throw new Error(`There is no such action "${e}"`)}i||h.adjustRowsAndCols(),n.view.render(),n.view.adjustElementsSize()},adjustRowsAndCols(){const e=v.minRows,t=v.minSpareRows,o=v.minCols,r=v.minSpareCols;if(e){const t=n.countRows();t<e&&l.createRow(t,e-t,{source:"auto"})}if(t){const e=n.countEmptyRows(!0);if(e<t){const o=t-e,r=Math.min(o,v.maxRows-n.countSourceRows());l.createRow(n.countRows(),r,{source:"auto"})}}{let e;(o||r)&&(e=n.countEmptyCols(!0));let t=n.countCols();if(o&&!v.columns&&t<o){const r=o-t;e+=r,l.createCol(t,r,{source:"auto"})}if(r&&!v.columns&&"array"===n.dataType&&e<r){t=n.countCols();const o=r-e,i=Math.min(o,v.maxCols-t);l.createCol(t,i,{source:"auto"})}}},populateFromArray(e,t,o,r,i){let s,l,a,h;const c=[],u={},d=[],g=e.row,f=e.col;if(l=t.length,0===l)return!1;let m=0,p=0;switch(Df(o)&&(m=o.col-f+1,p=o.row-g+1),i){case"shift_down":const i=Dd(t),w=i.length,y=Math.max(w,m),b=n.getData().slice(g),C=Dd(b).slice(f,f+y);for(a=0;a<y;a+=1)if(a<w){for(s=0,l=i[a].length;s<p-l;s+=1)i[a].push(i[a][s%l]);a<C.length?d.push(i[a].concat(C[a])):d.push(i[a].concat(new Array(b.length).fill(null)))}else d.push(i[a%w].concat(C[a]));n.populateFromArray(g,f,Dd(d));break;case"shift_right":const S=t.length,R=Math.max(S,p),T=n.getData().slice(g).map((e=>e.slice(f)));for(s=0;s<R;s+=1)if(s<S){for(a=0,h=t[s].length;a<m-h;a+=1)t[s].push(t[s][a%h]);if(s<T.length)for(let e=0;e<T[s].length;e+=1)t[s].push(T[s][e]);else t[s].push(...new Array(T[0].length).fill(null))}else t.push(t[s%l].slice(0,R).concat(T[s]));n.populateFromArray(g,f,t);break;default:u.row=e.row,u.col=e.col;let E,H=0,O=0,M=!0;const x=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const r=t[e%t.length];return null!==o?r[o%r.length]:r},I=t.length,N=o?o.row-e.row+1:0;for(l=o?N:Math.max(I,N),s=0;s<l&&!(o&&u.row>o.row&&N>I||!v.allowInsertRow&&u.row>n.countRows()-1||u.row>=v.maxRows);s++){const t=s-H,i=x(t).length,d=o?o.col-e.col+1:0;if(h=o?d:Math.max(i,d),u.col=e.col,E=n.getCellMeta(u.row,u.col),"CopyPaste.paste"!==r&&"Autofill.fill"!==r||!E.skipRowOnPaste){for(O=0,a=0;a<h&&!(o&&u.col>o.col&&d>i||!v.allowInsertColumn&&u.col>n.countCols()-1||u.col>=v.maxCols);a++){if(E=n.getCellMeta(u.row,u.col),("CopyPaste.paste"===r||"Autofill.fill"===r)&&E.skipColumnOnPaste){O+=1,u.col+=1,h+=1;continue}if(E.readOnly&&"UndoRedo.undo"!==r){u.col+=1;continue}let e=x(t,a-O),o=n.getDataAtCell(u.row,u.col);if(null!==e&&"object"==typeof e)if(Array.isArray(e)&&null===o&&(o=[]),null===o||"object"!=typeof o)M=!1;else{const t=Af(Array.isArray(o)?o:o[0]||o),r=Af(Array.isArray(e)?e:e[0]||e);Lf(t,r)||Array.isArray(t)&&Array.isArray(r)?e=Pf(e):M=!1}else null!==o&&"object"==typeof o&&(M=!1);M&&c.push([u.row,u.col,e]),M=!0,u.col+=1}u.row+=1}else H+=1,u.row+=1,l+=1}n.setDataAtCell(c,null,null,r||"populateFromArray")}}},this.init=function(){a.setData(v.data),n.runHooks("beforeInit"),(qf()||function(){let{maxTouchPoints:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:navigator;return e>2&&Yf.mac.value}())&&Sg(n.rootElement,"mobile"),this.updateSettings(t,!0),this.view=new VS(this);const e=v.themeName||jO(n.rootElement);n.useTheme(e),n.view.addClassNameToLicenseElement(n.getCurrentThemeName()),c=mp.getInstance(n,v,S),d=IO(n),u=new Mp(n),wS(this)&&EO(n),n.runHooks("init"),this.render(),g&&null===n.rootElement.offsetParent&&function(e,t){const o=new IntersectionObserver(((o,r)=>{o.forEach((o=>{o.isIntersecting&&null!==e.offsetParent&&(t(),r.unobserve(e))}))}),{root:e.ownerDocument.body});o.observe(e)}(n.rootElement,(()=>{n.view._wt.wtOverlays.updateLastSpreaderSize(),n.render(),n.view.adjustElementsSize()})),"object"==typeof g&&(n.runHooks("afterChange",g[0],g[1]),g=!1),n.runHooks("afterInit")},this._createCellCoords=function(e,t){return n.view._wt.createCellCoords(e,t)},this._createCellRange=function(e,t,o){return n.view._wt.createCellRange(e,t,o)},this.validateCell=function(e,t,o,r){let i=n.getCellValidator(t);function s(e){if(arguments.length>1&&void 0!==arguments[1]&&!arguments[1]||!0===t.hidden)return void o(e);const r=t.visualCol,i=t.visualRow,s=n.getCell(i,r,!0);if(s&&"TH"!==s.nodeName){const e=n.rowIndexMapper.getRenderableFromVisualIndex(i),t=n.columnIndexMapper.getRenderableFromVisualIndex(r);n.view._wt.getSetting("cellRenderer",e,t,s)}o(e)}var l,a;a=i,"[object RegExp]"===Object.prototype.toString.call(a)&&(l=i,i=function(e,t){t(l.test(e))}),Zg(i)?(e=n.runHooks("beforeValidate",e,t.visualRow,t.prop,r),n._registerImmediate((()=>{i.call(t,e,(o=>{n&&(o=n.runHooks("afterValidate",o,e,t.visualRow,t.prop,r),t.valid=o,s(o),n.runHooks("postAfterValidate",o,e,t.visualRow,t.prop,r))}))}))):n._registerImmediate((()=>{t.valid=!0,s(t.valid,!1)}))},this.setDataAtCell=function(e,t,o,r){const i=x(e,t,o),s=[];let h,c,u,d=r;for(h=0,c=i.length;h<c;h++){if("object"!=typeof i[h])throw new Error("Method `setDataAtCell` accepts row number or changes array of arrays as its first parameter");if("number"!=typeof i[h][1])throw new Error("Method `setDataAtCell` accepts row and column number as its parameters. If you want to use object property name, use method `setDataAtRowProp`");u=i[h][1]>=this.countCols()?i[h][1]:l.colToProp(i[h][1]),s.push([i[h][0],u,a.getAtCell(this.toPhysicalRow(i[h][0]),i[h][1]),i[h][2]])}d||"object"!=typeof e||(d=t);const g=I(s,d);n.runHooks("afterSetDataAtCell",g,d),O(g,d,(()=>{M(g,d)}))},this.setDataAtRowProp=function(e,t,o,r){const i=x(e,t,o),s=[];let l,h,c=r;for(l=0,h=i.length;l<h;l++)s.push([i[l][0],i[l][1],a.getAtCell(this.toPhysicalRow(i[l][0]),i[l][1]),i[l][2]]);c||"object"!=typeof e||(c=t);const u=I(s,r);n.runHooks("afterSetDataAtRowProp",u,c),O(u,c,(()=>{M(u,c)}))},this.listen=function(){n&&!n.isListening()&&($O.forEach((e=>{n!==e&&e.unlisten()})),zO=n.guid,n.runHooks("afterListen"))},this.unlisten=function(){this.isListening()&&(zO=null,n.runHooks("afterUnlisten"))},this.isListening=function(){return zO===n.guid},this.destroyEditor=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];c.closeEditor(e),n.view.render(),t&&S.isSelected()&&c.prepareEditor()},this.populateFromArray=function(e,t,o,r,i,s,l){if("object"!=typeof o||"object"!=typeof o[0])throw new Error("populateFromArray parameter `input` must be an array of arrays");const a="number"==typeof r?n._createCellCoords(r,i):null;return h.populateFromArray(n._createCellCoords(e,t),o,a,s,l)},this.spliceCol=function(e,t,o){for(var r=arguments.length,i=new Array(r>3?r-3:0),n=3;n<r;n++)i[n-3]=arguments[n];return l.spliceCol(e,t,o,...i)},this.spliceRow=function(e,t,o){for(var r=arguments.length,i=new Array(r>3?r-3:0),n=3;n<r;n++)i[n-3]=arguments[n];return l.spliceRow(e,t,o,...i)},this.getSelected=function(){if(S.isSelected())return Wd(S.getSelectedRange(),(e=>{let{from:t,to:o}=e;return[t.row,t.col,o.row,o.col]}))},this.getSelectedLast=function(){const e=this.getSelected();let t;return e&&e.length>0&&(t=e[e.length-1]),t},this.getSelectedRange=function(){if(S.isSelected())return Array.from(S.getSelectedRange())},this.getSelectedRangeLast=function(){const e=this.getSelectedRange();let t;return e&&e.length>0&&(t=e[e.length-1]),t},this.emptySelectedCells=function(e){if(!S.isSelected()||0===this.countRows()||0===this.countCols())return;const t=[];jd(S.getSelectedRange(),(e=>{if(e.isSingleHeader())return;const o=e.getTopStartCorner(),r=e.getBottomEndCorner();Ew(o.row,r.row,(e=>{Ew(o.col,r.col,(o=>{this.getCellMeta(e,o).readOnly||t.push([e,o,null])}))}))})),t.length>0&&this.setDataAtCell(t,e)},this.isRenderSuspended=function(){return this.renderSuspendedCounter>0},this.suspendRender=function(){this.renderSuspendedCounter+=1},this.resumeRender=function(){const e=this.renderSuspendedCounter-1;this.renderSuspendedCounter=Math.max(e,0),this.isRenderSuspended()||e!==this.renderSuspendedCounter||n.view.render()},this.render=function(){this.view&&(this.forceFullRender=!0,this.isRenderSuspended()||n.view.render())},this.batchRender=function(e){this.suspendRender();const t=e();return this.resumeRender(),t},this.isExecutionSuspended=function(){return this.executionSuspendedCounter>0},this.suspendExecution=function(){this.executionSuspendedCounter+=1,this.columnIndexMapper.suspendOperations(),this.rowIndexMapper.suspendOperations()},this.resumeExecution=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const t=this.executionSuspendedCounter-1;this.executionSuspendedCounter=Math.max(t,0),(!this.isExecutionSuspended()&&t===this.executionSuspendedCounter||e)&&(this.columnIndexMapper.resumeOperations(),this.rowIndexMapper.resumeOperations())},this.batchExecution=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.suspendExecution();const o=e();return this.resumeExecution(t),o},this.batch=function(e){this.suspendRender(),this.suspendExecution();const t=e();return this.resumeExecution(),this.resumeRender(),t},this.refreshDimensions=function(){if(!n.view)return;const e=n.view,{width:t,height:o}=e.getLastSize(),{width:r,height:i}=n.rootElement.getBoundingClientRect(),s=r!==t||i!==o;!1===n.runHooks("beforeRefreshDimensions",{width:t,height:o},{width:r,height:i},s)||((s||e._wt.wtOverlays.scrollableElement===n.rootWindow)&&(e.setLastSize(r,i),n.render(),e.adjustElementsSize()),n.runHooks("afterRefreshDimensions",{width:t,height:o},{width:r,height:i},s))},this.updateData=function(e,t){ZH(e,(e=>{l=e}),(e=>{l=e,n.columnIndexMapper.fitToLength(this.getInitialColumnCount()),n.rowIndexMapper.fitToLength(this.countSourceRows()),h.adjustRowsAndCols(),S.refresh()}),{hotInstance:n,dataMap:l,dataSource:a,internalSource:"updateData",source:t,metaManager:y,firstRun:g})},this.loadData=function(e,t){ZH(e,(e=>{l=e}),(()=>{y.clearCellsCache(),n.initIndexMappers(),h.adjustRowsAndCols(),S.refresh(),g&&(g=[null,"loadData"])}),{hotInstance:n,dataMap:l,dataSource:a,internalSource:"loadData",source:t,metaManager:y,firstRun:g})},this.getInitialColumnCount=function(){const e=v.columns;let t=0;if(Array.isArray(e))t=e.length;else if(Zg(e))if("array"===n.dataType){const o=this.countSourceCols();for(let r=0;r<o;r+=1)e(r)&&(t+=1)}else"object"!==n.dataType&&"function"!==n.dataType||(t=l.colToPropCache.length);else if(Gd(v.dataSchema)){const e=l.getSchema();t=Array.isArray(e)?e.length:jf(e)}else t=this.countSourceCols();return t},this.initIndexMappers=function(){this.columnIndexMapper.initToLength(this.getInitialColumnCount()),this.rowIndexMapper.initToLength(this.countSourceRows())},this.getData=function(e,t,o,r){return Yd(e)?l.getAll():l.getRange(n._createCellCoords(e,t),n._createCellCoords(o,r),l.DESTINATION_RENDERER)},this.getCopyableText=function(e,t,o,r){return l.getCopyableText(n._createCellCoords(e,t),n._createCellCoords(o,r))},this.getCopyableData=function(e,t){return l.getCopyable(e,l.colToProp(t))},this.getSchema=function(){return l.getSchema()},this.updateSettings=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=(g?n.loadData:n.updateData).bind(this);let r,i,s=!1;if(Gd(e.rows))throw new Error('The "rows" setting is no longer supported. Do you mean startRows, minRows or maxRows?');if(Gd(e.cols))throw new Error('The "cols" setting is no longer supported. Do you mean startCols, minCols or maxCols?');if(Gd(e.ganttChart))throw new Error('Since 8.0.0 the "ganttChart" setting is no longer supported.');for(r in e.language&&function(e){const t=LR(e);HE(t)?(n.runHooks("beforeLanguageChange",t),b.language=t,n.runHooks("afterLanguageChange",t)):DR(e)}(e.language),e)if("data"===r||"language"===r);else if("className"===r)T("className",e.className);else if("tableClassName"===r&&n.table)T("tableClassName",e.tableClassName),n.view._wt.wtOverlays.syncOverlayTableClassNames();else if(Xm.getSingleton().isRegistered(r)||Xm.getSingleton().isDeprecated(r)){const t=e[r];Zg(t)?(Xm.getSingleton().addAsFixed(r,t,n),v[r]=t):Array.isArray(t)&&(Xm.getSingleton().add(r,t,n),v[r]=t)}else!t&&$f(e,r)&&(b[r]=e[r]);void 0===e.data&&void 0===v.data?o(null,"updateSettings"):void 0!==e.data?o(e.data,"updateSettings"):void 0!==e.columns&&(l.createMap(),n.initIndexMappers());const a=n.countCols(),c=v.columns;if(c&&Zg(c)&&(s=!0),void 0===e.cell&&void 0===e.cells&&void 0===e.columns||y.clearCache(),a>0)for(r=0,i=0;r<a;r++){if(c){const e=s?c(r):c[i];e&&y.updateColumnMeta(i,e)}i+=1}Gd(e.cell)&&Bf(e.cell,(e=>{n.setCellMetaObject(e.row,e.col,e)})),n.runHooks("afterCellMetaReset");let u=n.rootElement.style.height;""!==u&&(u=parseInt(n.rootElement.style.height,10));let d=e.height;if(Zg(d)&&(d=d()),t&&n.rootElement.getAttribute("style")&&n.rootElement.setAttribute("data-initialstyle",n.rootElement.getAttribute("style")),null===d){const e=n.rootElement.getAttribute("data-initialstyle");e&&(e.indexOf("height")>-1||e.indexOf("overflow")>-1)?n.rootElement.setAttribute("style",e):(n.rootElement.style.height="",n.rootElement.style.overflow="")}else void 0!==d&&(n.rootElement.style.height=isNaN(d)?`${d}`:`${d}px`,n.rootElement.style.overflow="hidden");if(void 0!==e.width){let t=e.width;Zg(t)&&(t=t()),n.rootElement.style.width=isNaN(t)?`${t}`:`${t}px`}if(!t){if(n.view){n.view._wt.wtViewport.resetHasOversizedColumnHeadersMarked(),n.view._wt.exportSettingsAsClassNames();const t=n.getCurrentThemeName(),o=$f(e,"themeName");t&&o&&t!==e.themeName&&(n.view.getStylesHandler().removeClassNames(),n.view.removeClassNameFromLicenseElement(t));const r=o&&e.themeName||jO(n.rootElement);n.useTheme(r),n.view.addClassNameToLicenseElement(n.getCurrentThemeName())}n.runHooks("afterUpdateSettings",e)}h.adjustRowsAndCols(),n.view&&!g&&(n.render(),n.view._wt.wtOverlays.adjustElementsSize()),t||!n.view||""!==u&&""!==d&&void 0!==d||u===d||n.view._wt.wtOverlays.updateMainScrollableElements()},this.getValue=function(){const e=n.getSelectedLast();if(v.getValue){if(Zg(v.getValue))return v.getValue.call(n);if(e)return n.getData()[e[0][0]][v.getValue]}else if(e)return n.getDataAtCell(e[0],e[1])},this.getSettings=function(){return v},this.clear=function(){this.selectAll(),this.emptySelectedCells()},this.alter=function(e,t,o,r,i){h.alter(e,t,o,r,i)},this.getCell=function(e,t){let o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t,i=e;if(t>=0){if(this.columnIndexMapper.isHidden(this.toPhysicalColumn(t)))return null;r=this.columnIndexMapper.getRenderableFromVisualIndex(t)}if(e>=0){if(this.rowIndexMapper.isHidden(this.toPhysicalRow(e)))return null;i=this.rowIndexMapper.getRenderableFromVisualIndex(e)}return null===i||null===r||void 0===i||void 0===r?null:n.view.getCellAtCoords(n._createCellCoords(i,r),o)},this.getCoords=function(e){const t=this.view._wt.wtTable.getCoords(e);if(null===t)return null;const{row:o,col:r}=t;let i=o,s=r;return o>=0&&(i=this.rowIndexMapper.getVisualFromRenderableIndex(o)),r>=0&&(s=this.columnIndexMapper.getVisualFromRenderableIndex(r)),n._createCellCoords(i,s)},this.colToProp=function(e){return l.colToProp(e)},this.propToCol=function(e){return l.propToCol(e)},this.toVisualRow=e=>this.rowIndexMapper.getVisualFromPhysicalIndex(e),this.toVisualColumn=e=>this.columnIndexMapper.getVisualFromPhysicalIndex(e),this.toPhysicalRow=e=>this.rowIndexMapper.getPhysicalFromVisualIndex(e),this.toPhysicalColumn=e=>this.columnIndexMapper.getPhysicalFromVisualIndex(e),this.getDataAtCell=function(e,t){return l.get(e,l.colToProp(t))},this.getDataAtRowProp=function(e,t){return l.get(e,t)},this.getDataAtCol=function(e){const t=[],o=l.getRange(n._createCellCoords(0,e),n._createCellCoords(v.data.length-1,e),l.DESTINATION_RENDERER);for(let r=0;r<o.length;r+=1)for(let e=0;e<o[r].length;e+=1)t.push(o[r][e]);return t},this.getDataAtProp=function(e){const t=[],o=l.getRange(n._createCellCoords(0,l.propToCol(e)),n._createCellCoords(v.data.length-1,l.propToCol(e)),l.DESTINATION_RENDERER);for(let r=0;r<o.length;r+=1)for(let e=0;e<o[r].length;e+=1)t.push(o[r][e]);return t},this.getSourceData=function(e,t,o,r){let i;return i=void 0===e?a.getData():a.getByRange(n._createCellCoords(e,t),n._createCellCoords(o,r)),i},this.getSourceDataArray=function(e,t,o,r){let i;return i=void 0===e?a.getData(!0):a.getByRange(n._createCellCoords(e,t),n._createCellCoords(o,r),!0),i},this.getSourceDataAtCol=function(e){return a.getAtColumn(e)},this.setSourceDataAtCell=function(e,t,o,r){const i=x(e,t,o),s=this.hasHook("afterSetSourceDataAtCell"),l=[];s&&jd(i,(e=>{let[t,o,r]=e;l.push([t,o,a.getAtCell(t,o),r])})),jd(i,(e=>{let[t,o,r]=e;a.setAtCell(t,o,r)})),s&&this.runHooks("afterSetSourceDataAtCell",l,r),this.render();const h=n.getActiveEditor();h&&Gd(h.refreshValue)&&h.refreshValue()},this.getSourceDataAtRow=function(e){return a.getAtRow(e)},this.getSourceDataAtCell=function(e,t){return a.getAtCell(e,t)},this.getDataAtRow=function(e){return l.getRange(n._createCellCoords(e,0),n._createCellCoords(e,this.countCols()-1),l.DESTINATION_RENDERER)[0]||[]},this.getDataType=function(e,t,o,r){const i=void 0===e?[0,0,this.countRows(),this.countCols()]:[e,t,o,r],[n,s]=i;let[,,l,a]=i,h=null,c=null;void 0===l&&(l=n),void 0===a&&(a=s);let u="mixed";return Ew(Math.max(Math.min(n,l),0),Math.max(n,l),(e=>{let t=!0;return Ew(Math.max(Math.min(s,a),0),Math.max(s,a),(o=>{const r=this.getCellMeta(e,o);return c=r.type,h?t=h===c:h=c,t})),u=t?c:"mixed",t})),u},this.removeCellMeta=function(e,t,o){const[r,i]=[this.toPhysicalRow(e),this.toPhysicalColumn(t)];let s=y.getCellMetaKeyValue(r,i,o);!1!==n.runHooks("beforeRemoveCellMeta",e,t,o,s)&&(y.removeCellMeta(r,i,o),n.runHooks("afterRemoveCellMeta",e,t,o,s)),s=null},this.spliceCellsMeta=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;for(var o=arguments.length,r=new Array(o>2?o-2:0),i=2;i<o;i++)r[i-2]=arguments[i];if(r.length>0&&!Array.isArray(r[0]))throw new Error("The 3rd argument (cellMetaRows) has to be passed as an array of cell meta objects array.");t>0&&y.removeRow(this.toPhysicalRow(e),t),r.length>0&&jd(r.reverse(),(t=>{y.createRow(this.toPhysicalRow(e)),jd(t,((t,o)=>this.setCellMetaObject(e,o,t)))})),n.render()},this.setCellMetaObject=function(e,t,o){"object"==typeof o&&Bf(o,((o,r)=>{this.setCellMeta(e,t,r,o)}))},this.setCellMeta=function(e,t,o,r){if(!1===n.runHooks("beforeSetCellMeta",e,t,o,r))return;let i=e,s=t;e<this.countRows()&&(i=this.toPhysicalRow(e)),t<this.countCols()&&(s=this.toPhysicalColumn(t)),y.setCellMeta(i,s,o,r),n.runHooks("afterSetCellMeta",e,t,o,r)},this.getCellsMeta=function(){return y.getCellsMeta()},this.getCellMeta=function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{skipMetaExtension:!1},r=this.toPhysicalRow(e),i=this.toPhysicalColumn(t);return null===r&&(r=e),null===i&&(i=t),y.getCellMeta(r,i,{visualRow:e,visualColumn:t,...o})},this.getColumnMeta=function(e){return y.getColumnMeta(this.toPhysicalColumn(e))},this.getCellMetaAtRow=function(e){return y.getCellsMetaAtRow(e)},this.isColumnModificationAllowed=function(){return!("object"===n.dataType||v.columns)},this.getCellRenderer=function(e,t){const o="number"==typeof e?n.getCellMeta(e,t).renderer:e.renderer;return"string"==typeof o?$w(o):Yd(o)?$w("text"):o},this.getCellEditor=function(e,t){const o="number"==typeof e?n.getCellMeta(e,t).editor:e.editor;return"string"==typeof o?rp(o):Yd(o)?rp("text"):o},this.getCellValidator=function(e,t){const o="number"==typeof e?n.getCellMeta(e,t).validator:e.validator;return"string"==typeof o?function(e){if("function"==typeof e)return e;if(!Gw(e))throw Error(`No registered validator found under "${e}" name`);return Xw(e)}(o):o},this.validateCells=function(e){this._validateCells(e)},this.validateRows=function(e,t){if(!Array.isArray(e))throw new Error("validateRows parameter `rows` must be an array");this._validateCells(t,e)},this.validateColumns=function(e,t){if(!Array.isArray(e))throw new Error("validateColumns parameter `columns` must be an array");this._validateCells(t,void 0,e)},this._validateCells=function(e,t,o){const r=new E;e&&(r.onQueueEmpty=e);let i=n.countRows()-1;for(;i>=0;){if(void 0!==t&&-1===t.indexOf(i)){i-=1;continue}let e=n.countCols()-1;for(;e>=0;)void 0===o||-1!==o.indexOf(e)?(r.addValidatorToQueue(),n.validateCell(n.getDataAtCell(i,e),n.getCellMeta(i,e),(e=>{if("boolean"!=typeof e)throw new Error("Validation error: result is not boolean");!1===e&&(r.valid=!1),r.removeValidatorFormQueue()}),"validateCells"),e-=1):e-=1;i-=1}r.checkIfQueueIsEmpty()},this.getRowHeader=function(e){let t=v.rowHeaders,o=e;return void 0!==o&&(o=n.runHooks("modifyRowHeader",o)),void 0===o?(t=[],Ew(n.countRows()-1,(e=>{t.push(n.getRowHeader(e))}))):Array.isArray(t)&&void 0!==t[o]?t=t[o]:Zg(t)?t=t(o):t&&"string"!=typeof t&&"number"!=typeof t&&(t=o+1),t},this.hasRowHeaders=function(){return!!v.rowHeaders},this.hasColHeaders=function(){if(void 0!==v.colHeaders&&null!==v.colHeaders)return!!v.colHeaders;for(let e=0,t=n.countCols();e<t;e++)if(n.getColHeader(e))return!0;return!1},this.getColHeader=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;const o=n.runHooks("modifyColHeader",e);if(void 0===o){const e=[],t=n.countCols();for(let o=0;o<t;o++)e.push(n.getColHeader(o));return e}let r=v.colHeaders;const i=n.toPhysicalColumn(o),s=function(e){const t=[],o=n.countCols();let r=0;for(;r<o;r++)Zg(v.columns)&&v.columns(r)&&t.push(r);return t[e]}(i);return!1===v.colHeaders?r=null:v.columns&&Zg(v.columns)&&v.columns(s)&&v.columns(s).title?r=v.columns(s).title:v.columns&&v.columns[i]&&v.columns[i].title?r=v.columns[i].title:Array.isArray(v.colHeaders)&&void 0!==v.colHeaders[i]?r=v.colHeaders[i]:Zg(v.colHeaders)?r=v.colHeaders(i):v.colHeaders&&"string"!=typeof v.colHeaders&&"number"!=typeof v.colHeaders&&(r=function(e){let t,o=e+1,r="";for(;o>0;)t=(o-1)%26,r=String.fromCharCode(65+t)+r,o=parseInt((o-t)/26,10);return r}(o)),r=n.runHooks("modifyColumnHeaderValue",r,e,t),r},this._getColWidthFromSettings=function(e){let t;if(e>=0&&(t=n.getCellMeta(0,e).width),void 0!==t&&t!==v.width||(t=v.colWidths),null!=t){switch(typeof t){case"object":t=t[e];break;case"function":t=t(e)}"string"==typeof t&&(t=parseInt(t,10))}return t},this.getColWidth=function(e,t){let o=n._getColWidthFromSettings(e);return o=n.runHooks("modifyColWidth",o,e,t),void 0===o&&(o=50),o},this._getRowHeightFromSettings=function(e){const t=this.view.getDefaultRowHeight();let o=v.rowHeights;if(null!=o){switch(typeof o){case"object":o=o[e];break;case"function":o=o(e)}"string"==typeof o&&(o=parseInt(o,10))}return null!=o&&o<t?t:o},this.getRowHeight=function(e,t){let o=n._getRowHeightFromSettings(e);return o=n.runHooks("modifyRowHeight",o,e,t),o},this.countSourceRows=function(){return a.countRows()},this.countSourceCols=function(){return a.countFirstRowKeys()},this.countRows=function(){return l.getLength()},this.countCols=function(){const e=v.maxCols,t=this.columnIndexMapper.getNotTrimmedIndexesLength();return Math.min(e,t)},this.countRenderedRows=function(){return n.view._wt.drawn?n.view._wt.wtTable.getRenderedRowsCount():-1},this.countVisibleRows=function(){return n.view._wt.drawn?n.view._wt.wtTable.getVisibleRowsCount():-1},this.countRenderedCols=function(){return n.view._wt.drawn?n.view._wt.wtTable.getRenderedColumnsCount():-1},this.countVisibleCols=function(){return n.view._wt.drawn?n.view._wt.wtTable.getVisibleColumnsCount():-1},this.countRowHeaders=function(){return this.view.getRowHeadersCount()},this.countColHeaders=function(){return this.view.getColumnHeadersCount()},this.countEmptyRows=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=0;return Hw(n.countRows()-1,(o=>{if(n.isEmptyRow(o))t+=1;else if(!0===e)return!1})),t},this.countEmptyCols=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=0;return Hw(n.countCols()-1,(o=>{if(n.isEmptyCol(o))t+=1;else if(!0===e)return!1})),t},this.isEmptyRow=function(e){return v.isEmptyRow.call(n,e)},this.isEmptyCol=function(e){return v.isEmptyCol.call(n,e)},this.selectCell=function(e,t,o,r){let i=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],n=!(arguments.length>5&&void 0!==arguments[5])||arguments[5];return!Yd(e)&&!Yd(t)&&this.selectCells([[e,t,o,r]],i,n)},this.selectCells=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[[]],t=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];!1===(!(arguments.length>1&&void 0!==arguments[1])||arguments[1])&&d.suspend();const o=S.selectCells(e);return o&&t&&n.listen(),d.resume(),o},this.selectColumns=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,o=arguments.length>2?arguments[2]:void 0;return S.selectColumns(e,t,o)},this.selectRows=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,o=arguments.length>2?arguments[2]:void 0;return S.selectRows(e,t,o)},this.deselectCell=function(){S.deselect()},this.selectAll=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,o=arguments.length>2?arguments[2]:void 0;d.skipNextScrollCycle(),S.selectAll(e,t,o)};const N=(e,t)=>e.getNearestNotHiddenIndex(t,1,!0);this.scrollViewportTo=function(e,t){var o;"number"==typeof e&&(e={row:arguments[0],col:arguments[1],verticalSnap:arguments[2]?"bottom":"top",horizontalSnap:arguments[3]?"end":"start",considerHiddenIndexes:null===(o=arguments[4])||void 0===o||o});const{row:r,col:i,considerHiddenIndexes:s}=null!=e?e:{};let l=r,a=i;if(Zg(t)&&this.addHookOnce("afterScroll",t),void 0===s||s){const e=Number.isInteger(r)&&r>=0,t=Number.isInteger(i)&&i>=0,o=e?N(this.rowIndexMapper,r):void 0,s=t?N(this.columnIndexMapper,i):void 0;if(null===o||null===s)return!1;l=e?n.rowIndexMapper.getRenderableFromVisualIndex(o):r,a=t?n.columnIndexMapper.getRenderableFromVisualIndex(s):i}const h=Number.isInteger(l),c=Number.isInteger(a);let u=!1;return h&&l>=0&&c&&a>=0?u=n.view.scrollViewport(n._createCellCoords(l,a),e.horizontalSnap,e.verticalSnap):h&&l>=0&&(c&&a<0||!c)?u=n.view.scrollViewportVertically(l,e.verticalSnap):c&&a>=0&&(h&&l<0||!h)&&(u=n.view.scrollViewportHorizontally(a,e.horizontalSnap)),Zg(t)&&(u?this.view.render():(this.removeHook("afterScroll",t),this._registerMicrotask((()=>t())))),u},this.scrollToFocusedCell=function(e){if(!this.selection.isSelected())return!1;Zg(e)&&this.addHookOnce("afterScroll",e);const{highlight:t}=this.getSelectedRangeLast(),o=this.scrollViewportTo(t.toObject());return o?this.view.render():Zg(e)&&(this.removeHook("afterScroll",e),this._registerMicrotask((()=>e()))),o},this.destroy=function(){if(n._clearTimeouts(),n._clearImmediates(),n.view&&n.view.destroy(),a&&a.destroy(),a=null,this.getShortcutManager().destroy(),y.clearCache(),$O.delete(this.guid),wS(n)){const e=this.rootDocument.querySelector(".hot-display-license-info");e&&e.parentNode.removeChild(e)}Og(n.rootElement),s.destroy(),c&&c.destroy(),n.batchExecution((()=>{n.rowIndexMapper.unregisterAll(),n.columnIndexMapper.unregisterAll(),C.getItems().forEach((e=>{let[,t]=e;t.destroy()})),C.clear(),n.runHooks("afterDestroy")}),!0),Xm.getSingleton().destroy(n),Bf(n,((e,t,o)=>{var r;Zg(e)?o[t]=(r=t,()=>{throw new Error(`The "${r}" method cannot be called because this Handsontable instance has been destroyed`)}):"guid"!==t&&(o[t]=null)})),n.isDestroyed=!0,l&&l.destroy(),l=null,h=null,S=null,c=null,n=null},this.getActiveEditor=function(){return c.getActiveEditor()},this.getFirstRenderedVisibleRow=function(){return n.view.getFirstRenderedVisibleRow()},this.getLastRenderedVisibleRow=function(){return n.view.getLastRenderedVisibleRow()},this.getFirstRenderedVisibleColumn=function(){return n.view.getFirstRenderedVisibleColumn()},this.getLastRenderedVisibleColumn=function(){return n.view.getLastRenderedVisibleColumn()},this.getFirstFullyVisibleRow=function(){return n.view.getFirstFullyVisibleRow()},this.getLastFullyVisibleRow=function(){return n.view.getLastFullyVisibleRow()},this.getFirstFullyVisibleColumn=function(){return n.view.getFirstFullyVisibleColumn()},this.getLastFullyVisibleColumn=function(){return n.view.getLastFullyVisibleColumn()},this.getFirstPartiallyVisibleRow=function(){return n.view.getFirstPartiallyVisibleRow()},this.getLastPartiallyVisibleRow=function(){return n.view.getLastPartiallyVisibleRow()},this.getFirstPartiallyVisibleColumn=function(){return n.view.getFirstPartiallyVisibleColumn()},this.getLastPartiallyVisibleColumn=function(){return n.view.getLastPartiallyVisibleColumn()},this.getPlugin=function(e){return C.getItem(sg(e))},this.getPluginName=function(e){return e===this.undoRedo?this.undoRedo.constructor.PLUGIN_KEY:C.getId(e)},this.getInstance=function(){return n},this.addHook=function(e,t,o){Xm.getSingleton().add(e,t,n,o)},this.hasHook=function(e){return Xm.getSingleton().has(e,n)||Xm.getSingleton().has(e)},this.addHookOnce=function(e,t,o){Xm.getSingleton().once(e,t,n,o)},this.removeHook=function(e,t){Xm.getSingleton().remove(e,t,n)},this.runHooks=function(e,t,o,r,i,s,l){return Xm.getSingleton().run(n,e,t,o,r,i,s,l)},this.getTranslatedPhrase=function(e,t){return OE(v.language,e,t)},this.toHTML=()=>Rw(this),this.toTableElement=()=>{const e=this.rootDocument.createElement("div");return e.insertAdjacentHTML("afterbegin",Rw(this)),e.firstElementChild},this.timeouts=[],this.useTheme=e=>{this.view.getStylesHandler().useTheme(e),this.runHooks("afterSetTheme",e,!!g)},this.getCurrentThemeName=()=>this.view.getStylesHandler().getThemeName(),this._registerTimeout=function(e){let t=e;"function"==typeof t&&(t=setTimeout(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)),this.timeouts.push(t)},this._clearTimeouts=function(){jd(this.timeouts,(e=>{clearTimeout(e)}))},this.immediates=[],this._registerImmediate=function(e){this.immediates.push(setImmediate(e))},this._clearImmediates=function(){jd(this.immediates,(e=>{clearImmediate(e)}))},this._registerMicrotask=function(e){this.rootWindow.queueMicrotask((()=>{this.isDestroyed||e()}))},this._getEditorManager=function(){return c};const A=WO({handleEvent:()=>n.isListening(),beforeKeyDown:e=>this.runHooks("beforeKeyDown",e),afterKeyDown:e=>{this.isDestroyed||n.runHooks("afterDocumentKeyDown",e)},ownerWindow:this.rootWindow});var k;this.addHook("beforeOnCellMouseDown",(e=>{!1===e.ctrlKey&&!1===e.metaKey&&A.releasePressedKeys()})),this.getShortcutManager=function(){return A},this.getFocusManager=function(){return u},[...Vw.getItems(),...Lw.getItems()].forEach((e=>{const t=function(e){const t=sg(e);return Dw.getItem(t)}(e);C.addItem(e,new t(this))})),k=n,[RO,SO].forEach((e=>e(k))),A.setActiveContextName("grid"),Xm.getSingleton().run(n,"construct")}function XO(e,t,o){e.addEventListener(t,o,!1)}function GO(e,t,o){e.removeEventListener(t,o,!1)}function YO(e){return e.ownerDocument.defaultView.getComputedStyle(e)}function KO(e){const t={minHeight:200,maxHeight:300,minWidth:100,maxWidth:300,textContent:e=>e.value,...arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}},o=e.body,r=e.createTextNode(""),i=e.createElement("span");let n;function s(){var e,s;r.textContent=t.textContent(n),i.style.position="absolute",i.style.fontSize=YO(n).fontSize,i.style.fontFamily=YO(n).fontFamily,i.style.whiteSpace="pre",o.appendChild(i);const l=parseInt((null===(e=YO(n))||void 0===e?void 0:e.paddingInlineStart)||0,10),a=parseInt((null===(s=YO(n))||void 0===s?void 0:s.paddingInlineEnd)||0,10),h=i.clientWidth+l+a+1;o.removeChild(i);const c=n.style;c.height=`${t.minHeight}px`,t.minWidth>h?c.width=`${t.minWidth}px`:h>t.maxWidth?c.width=`${t.maxWidth}px`:c.width=`${h}px`;const u=n.scrollHeight?n.scrollHeight-1:0;t.minHeight>u?c.height=`${t.minHeight}px`:t.maxHeight<u?(c.height=`${t.maxHeight}px`,c.overflowY="visible"):c.height=`${u}px`}function l(){e.defaultView.setTimeout(s,0)}return{init:function(e,o){let a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];n=e,function(e){if(e&&e.minHeight)if("inherit"===e.minHeight)t.minHeight=n.clientHeight;else{const o=parseInt(e.minHeight,10);isNaN(o)||(t.minHeight=o)}if(e&&e.maxHeight)if("inherit"===e.maxHeight)t.maxHeight=n.clientHeight;else{const o=parseInt(e.maxHeight,10);isNaN(o)||(t.maxHeight=o)}if(e&&e.minWidth)if("inherit"===e.minWidth)t.minWidth=n.clientWidth;else{const o=parseInt(e.minWidth,10);isNaN(o)||(t.minWidth=o)}if(e&&e.maxWidth)if("inherit"===e.maxWidth)t.maxWidth=n.clientWidth;else{const o=parseInt(e.maxWidth,10);isNaN(o)||(t.maxWidth=o)}i.firstChild||(i.className="autoResize",i.style.display="inline-block",i.appendChild(r))}(o),"TEXTAREA"===n.nodeName&&(n.style.resize="none",n.style.height=`${t.minHeight}px`,n.style.minWidth=`${t.minWidth}px`,n.style.maxWidth=`${t.maxWidth}px`,n.style.overflowY="hidden"),a&&(XO(n,"input",s),XO(n,"keydown",l)),s()},resize:s,unObserve(){GO(n,"input",s),GO(n,"keydown",l)}}}function qO(e,t){const o=(r=t).selectionStart?r.selectionStart:0;var r;const i=t.value.split("\n");let n=o,s=0;for(let l=0;l<i.length;l++){const t=i[l];0!==l&&(s+=i[l-1].length+1);const r=s+t.length;if("home"===e?n=s:"end"===e&&(n=r),o<=r)break}Ug(t,n)}function QO(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const JO="ht_editor_hidden",ZO="textEditor";function eM(e,t,o,r,i,n,s){const l=s.ariaTags,a=[],h=[],c=[],u=[];s.className&&Sg(t,s.className),s.readOnly?(a.push(s.readOnlyCellClassName),l&&u.push(["aria-readonly","true"])):l&&c.push("aria-readonly"),!1===s.valid&&s.invalidCellClassName?(a.push(s.invalidCellClassName),l&&u.push(["aria-invalid","true"])):(h.push(s.invalidCellClassName),l&&c.push("aria-invalid")),!1===s.wordWrap&&s.noWordWrapClassName&&a.push(s.noWordWrapClassName),!n&&s.placeholder&&a.push(s.placeholderCellClassName),Rg(t,h),Sg(t,a),Eg(t,c),Tg(t,u)}function tM(e,t,o,r,i,n,s){eM.apply(this,[e,t,o,r,i,n,s]);let l=n;!l&&s.placeholder&&(l=s.placeholder),l=Xd(l),s.trimWhitespace&&(l=l.trim()),Ig(t,l)}eM.RENDERER_TYPE="base",tM.RENDERER_TYPE="text";const oM={CELL_TYPE:"text",editor:class extends fO{static get EDITOR_TYPE(){return"text"}constructor(e){super(e),QO(this,"eventManager",new sp(this)),QO(this,"autoResize",KO(this.hot.rootDocument)),QO(this,"TEXTAREA",void 0),QO(this,"textareaStyle",void 0),QO(this,"TEXTAREA_PARENT",void 0),QO(this,"textareaParentStyle",void 0),QO(this,"layerClass",void 0),this.eventManager=new sp(this),this.createElements(),this.bindEvents(),this.hot.addHookOnce("afterDestroy",(()=>this.destroy()))}getValue(){return this.TEXTAREA.value}setValue(e){this.TEXTAREA.value=e}open(){this.refreshDimensions(),this.showEditableElement(),this.hot.getShortcutManager().setActiveContextName("editor"),this.registerShortcuts()}close(){this.autoResize.unObserve(),function(e,t){const o=e.closest(".handsontable");return!!o&&(o.parentNode===t||o===t)}(this.hot.rootDocument.activeElement,this.hot.rootElement)&&this.hot.listen(),this.hideEditableElement(),this.unregisterShortcuts()}prepare(e,t,o,r,i,n){const s=this.state;if(super.prepare(e,t,o,r,i,n),!n.readOnly){this.refreshDimensions(!0);const{allowInvalid:e}=n;e&&!this.isOpened()&&(this.TEXTAREA.value=""),s===gO.FINISHED||this.isOpened()||this.hideEditableElement()}}beginEditing(e,t){this.state===gO.VIRGIN&&(this.TEXTAREA.value="",super.beginEditing(e,t))}focus(){this.TEXTAREA.select(),Ug(this.TEXTAREA,this.TEXTAREA.value.length)}createElements(){const{rootDocument:e}=this.hot;this.TEXTAREA=e.createElement("TEXTAREA"),Tg(this.TEXTAREA,[["data-hot-input",""],ag(-1)]),Sg(this.TEXTAREA,"handsontableInput"),this.textareaStyle=this.TEXTAREA.style,this.textareaStyle.width=0,this.textareaStyle.height=0,this.textareaStyle.overflowY="visible",this.TEXTAREA_PARENT=e.createElement("DIV"),Sg(this.TEXTAREA_PARENT,"handsontableInputHolder"),Cg(this.TEXTAREA_PARENT,this.layerClass)&&Rg(this.TEXTAREA_PARENT,this.layerClass),Sg(this.TEXTAREA_PARENT,JO),this.textareaParentStyle=this.TEXTAREA_PARENT.style,this.TEXTAREA_PARENT.appendChild(this.TEXTAREA),this.hot.rootElement.appendChild(this.TEXTAREA_PARENT)}hideEditableElement(){Gf.edge.value&&(this.textareaStyle.textIndent="-99999px"),this.textareaStyle.overflowY="visible",this.textareaParentStyle.opacity="0",this.textareaParentStyle.height="1px",Rg(this.TEXTAREA_PARENT,this.layerClass),Sg(this.TEXTAREA_PARENT,JO)}showEditableElement(){this.textareaParentStyle.height="",this.textareaParentStyle.overflow="",this.textareaParentStyle.position="",this.textareaParentStyle[this.hot.isRtl()?"left":"right"]="auto",this.textareaParentStyle.opacity="1",this.textareaStyle.textIndent="";const e=this.TEXTAREA_PARENT.childNodes;let t=!1;Ew(e.length-1,(o=>{if(Cg(e[o],"handsontableEditor"))return t=!0,!1})),Cg(this.TEXTAREA_PARENT,JO)&&Rg(this.TEXTAREA_PARENT,JO),t?(this.layerClass="ht_editor_visible",Sg(this.TEXTAREA_PARENT,this.layerClass)):(this.layerClass=this.getEditedCellsLayerClass(),Sg(this.TEXTAREA_PARENT,this.layerClass))}refreshValue(){const e=this.hot.toPhysicalRow(this.row),t=this.hot.getSourceDataAtCell(e,this.col);this.originalValue=t,this.setValue(t),this.refreshDimensions()}refreshDimensions(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.state!==gO.EDITING&&!e)return;if(this.TD=this.getEditedCell(),!this.TD)return void(e||this.close());const{top:t,start:o,width:r,maxWidth:i,height:n,maxHeight:s}=this.getEditedCellRect();this.textareaParentStyle.top=`${t}px`,this.textareaParentStyle[this.hot.isRtl()?"right":"left"]=`${o}px`,this.showEditableElement();const l=this.hot.rootWindow.getComputedStyle(this.TD);this.TEXTAREA.style.fontSize=l.fontSize,this.TEXTAREA.style.fontFamily=l.fontFamily,this.TEXTAREA.style.backgroundColor=this.TD.style.backgroundColor,this.autoResize.init(this.TEXTAREA,{minWidth:Math.min(r,i),minHeight:Math.min(n,s),maxWidth:i,maxHeight:s},!0)}bindEvents(){Qf()&&this.eventManager.addEventListener(this.TEXTAREA,"focusout",(()=>this.finishEditing(!1))),this.addHook("afterScrollHorizontally",(()=>this.refreshDimensions())),this.addHook("afterScrollVertically",(()=>this.refreshDimensions())),this.addHook("afterColumnResize",(()=>{this.refreshDimensions(),this.state===gO.EDITING&&this.focus()})),this.addHook("afterRowResize",(()=>{this.refreshDimensions(),this.state===gO.EDITING&&this.focus()}))}allowKeyEventPropagation(){}destroy(){this.eventManager.destroy(),this.clearHooks()}registerShortcuts(){const e=this.hot.getShortcutManager().getContext("editor"),t={runOnlyIf:()=>Gd(this.hot.getSelected()),group:ZO},o=()=>{this.hot.rootDocument.execCommand("insertText",!1,"\n")};e.addShortcuts([{keys:[["Control","Enter"]],callback:()=>(o(),!1),runOnlyIf:e=>!this.hot.selection.isMultiple()&&!e.altKey},{keys:[["Meta","Enter"]],callback:()=>(o(),!1),runOnlyIf:()=>!this.hot.selection.isMultiple()},{keys:[["Alt","Enter"]],callback:()=>(o(),!1)},{keys:[["Home"]],callback:(e,t)=>{let[o]=t;qO(o,this.TEXTAREA)}},{keys:[["End"]],callback:(e,t)=>{let[o]=t;qO(o,this.TEXTAREA)}}],t)}unregisterShortcuts(){this.hot.getShortcutManager().getContext("editor").removeShortcutsByGroup(ZO)}},renderer:tM};function rM(e,t){const o=new UO(e,t||{},mS);return o.init(),o}function iM(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function nM(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,r)}return o}function sM(e){return sM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sM(e)}!function(e,t){"string"!=typeof e&&(e=(t=e).CELL_TYPE);const{editor:o,renderer:r,validator:i}=t;o&&ip(e,o),r&&function(e,t){"string"!=typeof e&&(e=(t=e).RENDERER_TYPE),Fw(e,t)}(e,r),i&&function(e,t){"string"!=typeof e&&(e=(t=e).VALIDATOR_TYPE),Uw(e,t)}(e,i),NH(e,t)}(oM),rM.editors={BaseEditor:fO},rM.Core=function(e){return new UO(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},mS)},rM.DefaultSettings=BH(),rM.hooks=Xm.getSingleton(),rM.CellCoords=Py,rM.CellRange=jy,rM.packageName="handsontable",rM.buildDate="24/04/2025 10:59:00",rM.version="15.3.0",rM.languages={dictionaryKeys:yE,getLanguageDictionary:EE,getLanguagesDictionaries:function(){return RE()},registerLanguageDictionary:TE,getTranslatedPhrase:OE};var lM=Symbol("unassigned");function aM(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function hM(e){var t=rM.hooks.getRegistered(),o={};for(var r in Object.assign(o,rM.DefaultSettings),o)o[r]={default:lM};for(var i=0;i<t.length;i++)o[t[i]]={default:lM};return o.settings={default:lM},"HotTable"===e&&(o.id={type:String,default:"hot-".concat(Math.random().toString(36).substring(5))}),o}function cM(e){var t={},o=e.settings;if(o!==lM)for(var r in o)aM(o,r)&&o[r]!==lM&&(t[r]=o[r]);for(var i in e)aM(e,i)&&"settings"!==i&&e[i]!==lM&&(t[i]=e[i]);return t}function uM(e,t){var o=cM(e),r=e.settings?e.settings:o,i=e.settings?o:null,n={};for(var s in r)!aM(r,s)||void 0===r[s]||t&&"data"!==s&&dM(t[s],r[s])||(n[s]=r[s]);for(var l in i)!aM(i,l)||"id"===l||"settings"===l||void 0===i[l]||t&&"data"!==l&&dM(t[l],i[l])||(n[l]=i[l]);return n}function dM(e,t){var o=function(e){var t,o=(t=new WeakSet,function(e,o){if("object"===sM(o)&&null!==o){if(t.has(o))return;t.add(o)}return o});return JSON.stringify(e,o)};return"function"==typeof e&&"function"==typeof t?e.toString()===t.toString():sM(e)===sM(t)&&o(e)===o(t)}var gM=e("H",i({name:"HotTable",props:hM("HotTable"),provide:function(){return{columnsCache:this.columnsCache}},watch:{$props:{handler:function(e){var t=uM(e,this.hotInstance?this.hotInstance.getSettings():void 0);this.hotInstance&&void 0!==t&&(t.data&&(this.hotInstance.isColumnModificationAllowed()||!this.hotInstance.isColumnModificationAllowed()&&this.hotInstance.countSourceCols()===this.miscCache.currentSourceColumns)&&(this.matchHotMappersSize(),delete t.data),Object.keys(t).length?this.hotInstance.updateSettings(t):this.hotInstance.render(),this.miscCache.currentSourceColumns=this.hotInstance.countSourceCols())},deep:!0,immediate:!0}},data:function(){return{__hotInstance:null,miscCache:{currentSourceColumns:null},columnSettings:null,columnsCache:new Map,get hotInstance(){return!this.__hotInstance||this.__hotInstance&&!this.__hotInstance.isDestroyed?this.__hotInstance:(console.warn("The Handsontable instance bound to this component was destroyed and cannot be used properly."),null)},set hotInstance(e){this.__hotInstance=e}}},methods:{hotInit:function(){var e=uM(this.$props);e.columns=this.columnSettings?this.columnSettings:e.columns,this.hotInstance=n(new rM.Core(this.$el,e)),this.hotInstance.init(),this.miscCache.currentSourceColumns=this.hotInstance.countSourceCols()},matchHotMappersSize:function(){var e=this;if(this.hotInstance){var t,o=this.hotInstance.getSourceData(),r=[],i=[],n=this.hotInstance.rowIndexMapper.getNumberOfIndexes(),s=this.hotInstance.isColumnModificationAllowed(),l=0;if(o&&o.length!==n&&o.length<n)for(var a=o.length;a<n;a++)r.push(a);if(s&&(l=this.hotInstance.columnIndexMapper.getNumberOfIndexes(),o&&o[0]&&(null===(t=o[0])||void 0===t?void 0:t.length)!==l&&o[0].length<l))for(var h=o[0].length;h<l;h++)i.push(h);this.hotInstance.batch((function(){r.length>0?e.hotInstance.rowIndexMapper.removeIndexes(r):e.hotInstance.rowIndexMapper.insertIndexes(n-1,o.length-n),s&&0!==o.length&&(i.length>0?e.hotInstance.columnIndexMapper.removeIndexes(i):e.hotInstance.columnIndexMapper.insertIndexes(l-1,o[0].length-l))}))}},getColumnSettings:function(){var e=Array.from(this.columnsCache.values());return e.length?e:void 0}},mounted:function(){this.columnSettings=this.getColumnSettings(),this.hotInit()},beforeUnmount:function(){this.hotInstance&&this.hotInstance.destroy()},version:"15.3.0"})),fM=["id"];gM.render=function(e,t,o,r,i,n){return s(),l("div",{id:e.id},[a(e.$slots,"default")],8,fM)},gM.__file="src/HotTable.vue";var mM=i({name:"HotColumn",props:hM("HotColumn"),inject:["columnsCache"],methods:{createColumnSettings:function(){var e=cM(this.$props),t=function(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?nM(Object(o),!0).forEach((function(t){iM(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):nM(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}({},e);e.renderer&&(t.renderer=e.renderer),e.editor&&(t.editor=e.editor),this.columnsCache.set(this,t)}},mounted:function(){this.createColumnSettings()},unmounted:function(){this.columnsCache.delete(this)},render:function(){return null}});function pM(e,t){null==e&&(e=""),"number"!=typeof t&&(t=12);const o=document.createElement("span");o.innerText=e,o.style.fontSize=t+"px",o.style.opacity="0",o.style.position="fixed",o.style.bottom="0",o.style.left="0",document.body.appendChild(o);let r=window.getComputedStyle(o).width;return r=parseFloat(r.substring(0,r.indexOf("px"))),document.body.removeChild(o),r}mM.__file="src/HotColumn.vue",e("p",r({data:()=>({lockTypes:["portrait-primary","portrait-secondary","landscape-primary","landscape-secondary"]}),computed:{lockFunction:()=>(screen.orientation||{}).lock?screen.orientation.lock:screen.mozLockOrientation?e=>new Promise(((t,o)=>{screen.mozLockOrientation(e)?t():o()})):screen.msLockOrientation?e=>new Promise(((t,o)=>{screen.msLockOrientation(e)?t():o()})):e=>new Promise(((e,t)=>t(new Error("No orientation"))))},methods:{getOrientation:()=>(screen.orientation||{}).type||screen.mozOrientation||screen.msOrientation,unlock(){(screen.orientation||{}).unlock?screen.orientation.unlock():screen.mozUnlockOrientation?screen.mozUnlockOrientation():screen.msUnlockOrientation&&screen.msUnlockOrientation()},orientChange(){const e=this.getOrientation(),[t,o]=(e||"").split("-");"portrait"===t?this.orient(`landscape-${o}`):"landscape"===t&&this.orient(`portrait-${o}`)},async orient(e){try{await this.lockFunction(e)}catch(t){t instanceof Error?this.$showToast("该环境下不支持翻转"):this.$showToast("翻转失败")}},emitOrientChange(){const e=this.getOrientation();this.$emit("change",e)}},beforeDestroy(){window.removeEventListener("orientationchange",this.emitOrientChange),this.orient("portrait-primary")},mounted(){window.addEventListener("orientationchange",this.emitOrientChange);const{orientationType:e}=this.$route.query;this.lockTypes.includes(e)&&e!==this.getOrientation()&&this.orient(e)}},[["render",function(e,t,o,r,i,n){const l=h("van-button");return s(),c(l,{color:"#26ccff",class:"circle orient-button",onClick:n.orientChange},{default:u((()=>t[0]||(t[0]=[d("i",{class:"iconfont icon-fanzhuanpingmu text-20"},null,-1)]))),_:1,__:[0]},8,["onClick"])}],["__scopeId","data-v-ee597a3d"]]));const wM={components:{HotTable:gM},props:{isLine:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1},height:{type:[Number,String,Function],default:void 0},pageSize:{type:Number,default:20},params:{type:Object,default:()=>({})}},data:()=>({currentPage:0,data:[],totalSize:0,dataSetting:{data:[]}}),watch:{params(e){this.currentPage=0,this.data=[],this.totalSize=0,this.dataSetting={data:[]},Object.keys(e).length>0&&this.$nextTick((e=>{this.getData(!0)}))}},methods:{getRowHeaderWidth(e=[]){const t=Math.max(...e.map((e=>pM(e,14)+10)));return Math.max(t,50)},async getLineData(){const e=await m(this.params),{data:t,codeName:o,vectorName:r}=e;if(Array.isArray(t)){this.data=t.map((e=>({...e,watchTime:f(e.watchTime,"YYYY-MM-DD HH:mm:ss")}))),this.totalSize=this.data.length;const e=t.map((e=>o));this.dataSetting={fixedColumnsLeft:1,height:this.height,data:this.data,columns:[{data:"watchTime",editor:!1,width:150},{data:"value",editor:!1,width:150}],colHeaders:["日期",r],rowHeaders:e,rowHeaderWidth:this.getRowHeaderWidth(e),stretchH:"all"}}else this.dataSetting={data:[]};this.$nextTick((t=>{this.$refs.inputTable.hotInstance.updateSettings(this.dataSetting),this.$emit("response",e.data)}))},async getValueData(e=!1){const t=await p({page:this.currentPage,size:this.pageSize,...this.params});let o=t.data;const{info:r,totalSize:i}=t;if(Array.isArray(o)&&Array.isArray(r)){this.data=this.lazy&&!e?this.data.concat(o):o,this.totalSize=i;const t=this.data.map((e=>e.CODE_NAME));this.dataSetting={fixedColumnsLeft:1,height:this.height,data:this.data.map((e=>({...e,WATCH_TIME:f(e.WATCH_TIME,"YYYY-MM-DD HH:mm:ss")}))),columns:[{data:"WATCH_TIME",editor:!1,width:150},...r.map((e=>({data:e.valueVectorFieldName,editor:!1,width:pM(e.valueVectorName)+20})))],colHeaders:["日期",...r.map((e=>e.valueVectorName))],rowHeaders:t,rowHeaderWidth:this.getRowHeaderWidth(t)}}else 0===this.currentPage&&(this.data=[],this.totalSize=0),this.dataSetting={data:[]};this.$nextTick((e=>{if(this.$refs.inputTable.hotInstance.updateSettings(this.dataSetting),this.$emit("response",t.data),this.lazy&&0===this.currentPage){const e=document.querySelector(".wtHolder");e&&e.addEventListener("scroll",this.scrollHandler)}}))},getData(e=!1){this.isLine?this.getLineData():this.getValueData(e)},scrollHandler(e){this.scrollTimer&&clearTimeout(this.scrollTimer),e&&e.target&&e.target.scrollTop>0&&(this.scrollTimer=setTimeout((()=>{const e=document.querySelector(".wtHolder");e.scrollTop+window.innerHeight>e.scrollHeight-2&&this.data.length<this.totalSize&&(this.currentPage++,this.getData()),clearTimeout(this.scrollTimer)}),500))}},beforeDestroy(){this.lazy&&document.querySelector(".wtHolder")&&document.querySelector(".wtHolder").removeEventListener("scroll",this.scrollHandler)}},yM={class:"monitor-table-style no-selection-handle"},vM={key:1,class:"text-center py-16 mb-0"};e("c",r(wM,[["render",function(e,t,o,r,i,n){const a=h("hot-table");return s(),l("div",yM,[i.totalSize>0?(s(),c(a,{key:0,ref:"inputTable",stretchH:"all",class:"border border-top-0","license-key":"non-commercial-and-evaluation",settings:i.dataSetting,height:o.height,width:"100%"},null,8,["settings","height"])):(s(),l("p",vM," 暂无数据 "))])}],["__scopeId","data-v-2dd25217"]]))}}}));
