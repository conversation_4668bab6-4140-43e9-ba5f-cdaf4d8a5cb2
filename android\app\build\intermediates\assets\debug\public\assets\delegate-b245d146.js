import{m as l}from"./index-4829f8e2.js";var s=9;if(typeof Element<"u"&&!Element.prototype.matches){var a=Element.prototype;a.matches=a.matchesSelector||a.mozMatchesSelector||a.msMatchesSelector||a.oMatchesSelector||a.webkitMatchesSelector}function u(t,r){for(;t&&t.nodeType!==s;){if(typeof t.matches=="function"&&t.matches(r))return t;t=t.parentNode}}var f=u,d=f;function i(t,r,n,c,e){var o=y.apply(this,arguments);return t.addEventListener(n,o,e),{destroy:function(){t.removeEventListener(n,o,e)}}}function p(t,r,n,c,e){return typeof t.addEventListener=="function"?i.apply(null,arguments):typeof n=="function"?i.bind(null,document).apply(null,arguments):(typeof t=="string"&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,function(o){return i(o,r,n,c,e)}))}function y(t,r,n,c){return function(e){e.delegateTarget=d(e.target,r),e.delegateTarget&&c.call(t,e)}}var g=p;const m=l(g);export{m as B};
