import{h as t}from"./index-4829f8e2.js";function l(e){return t({url:"/sys-storage/file",method:"post",data:e})}function n(e){return t({url:"/sys-storage/file",method:"delete",data:e})}function u(e){let{fileToken:o,width:r,height:s}=e;return t({method:"get",url:"/sys-storage/down_thumbnail?thumbnail=true&f8s=".concat(o,"&width=").concat(r,"&height=").concat(s),timeout:0,responseType:"blob",params:e})}export{n as a,u as d,l as g};
