import{h as l}from"./dateFormat-1fb6392c.js";import{Q as i,R as o,X as p,V as u,S as y,F as x,W as V,t as w,k as d,v as C,U as v,B as b,Z as S}from"./verder-361ae6c7.js";import{_ as Y}from"./index-4829f8e2.js";import{N as F}from"./vant-91101745.js";const I="1900/01/01",M="2050/10/01",k=l().format("YYYY-MM-DD [00:00:00]"),R=l(k).format("YYYY-MM-DD").split("-"),B={props:{defaultDateRange:{type:String,default:"1,M"}},data(){const[e,t]=this.defaultDateRange.split(","),s=l(k).subtract(e,t).format("YYYY-MM-DD").split("-");return{startDate:s,endDate:R,lastStartDate:s,lastEndDate:R,minDate:new Date(I),maxDate:new Date(M),dateRange:this.defaultDateRange,lastDateRange:this.defaultDateRange,dateRangeList:[{name:"最近七天",value:"7,d"},{name:"最近一月",value:"1,M"},{name:"最近一年",value:"1,y"}]}},computed:{dateRangeTitle(){return this.lastDateRange?this.dateRangeList.find(e=>e.value===this.lastDateRange).name:"查询日期"}},methods:{initDateRange(){this.startDate=this.lastStartDate,this.endDate=this.lastEndDate,this.dateRange=this.lastDateRange},dateRangeClick(e){this.dateRange=e;const[t,s]=e.split(",");this.startDate=l(k).subtract(t,s).format("YYYY-MM-DD").split("-"),this.endDate=R},dateRangeChange(){for(const{value:e}of this.dateRangeList){const[t,s]=e.split(","),_=l(this.startDate).set({hour:0,minute:0,second:0}),n=l(this.endDate).set({hour:0,minute:0,second:0});if(_.add(t,s).valueOf()===n.valueOf()){this.dateRange=e;return}}this.dateRange=""},dateRangeConfirm(){if(l(this.startDate).isAfter(l(this.endDate)))return F({message:"开始时间不能大于结束时间",background:"rgba(0, 0, 0, 0.7)",duration:2e3}),!1;this.$refs.dropdownItem&&this.$refs.dropdownItem.toggle(!1),this.lastStartDate=this.startDate,this.lastEndDate=this.endDate,this.lastDateRange=this.dateRange,this.$emit("confirm",{startDate:this.startDate,endDate:this.endDate})}},created(){this.$emit("load",{startDate:this.startDate,endDate:this.endDate})}},N={style:{display:"flex","align-items":"center","justify-content":"center"}},O={class:"py-4 px-8"};function T(e,t,s,_,n,a){const g=i("van-icon"),m=i("van-cell"),h=i("van-date-picker"),D=i("van-button"),r=i("van-dropdown-item");return o(),p(r,{ref:"dropdownItem",title:a.dateRangeTitle,onOpen:a.initDateRange},{default:u(()=>[(o(!0),y(x,null,V(n.dateRangeList,({name:c,value:f})=>(o(),p(m,{key:f,title:c,"title-class":{"text-blue":n.dateRange===f},size:"large",center:"",onClick:z=>a.dateRangeClick(f)},{"right-icon":u(()=>[w(d(g,{name:"success",size:"20px",color:"#1989fa"},null,512),[[C,n.dateRange===f]])]),_:2},1032,["title","title-class","onClick"]))),128)),d(m,{size:"large"},{title:u(()=>[v("div",N,[d(h,{class:"inline-datetime-picker",style:{width:"calc((100% - 34px) / 2)"},modelValue:n.startDate,"onUpdate:modelValue":t[0]||(t[0]=c=>n.startDate=c),type:"date",title:"请选择","visible-option-num":"1","show-toolbar":!1,"min-date":n.minDate,"max-date":n.maxDate,onChange:a.dateRangeChange},null,8,["modelValue","min-date","max-date","onChange"]),t[2]||(t[2]=v("div",{class:"text-center",style:{width:"34px"}},"至",-1)),d(h,{class:"inline-datetime-picker",style:{width:"calc((100% - 34px) / 2)"},modelValue:n.endDate,"onUpdate:modelValue":t[1]||(t[1]=c=>n.endDate=c),type:"date",title:"请选择","visible-option-num":"1","show-toolbar":!1,"min-date":n.minDate,"max-date":n.maxDate,onChange:a.dateRangeChange},null,8,["modelValue","min-date","max-date","onChange"])])]),_:1}),v("div",O,[d(D,{type:"primary",block:"",onClick:a.dateRangeConfirm},{default:u(()=>t[3]||(t[3]=[b("确定")])),_:1,__:[3]},8,["onClick"])])]),_:1},8,["title","onOpen"])}const X=Y(B,[["render",T],["__scopeId","data-v-25076006"]]);const A={props:{multiple:{type:Boolean,default:!1},required:{type:Boolean,default:!1},options:{type:Array,default:()=>[]},defaultProps:{type:Object,default:()=>({name:"name",value:"value"})},defaultTitle:{type:String,required:!0},defaultValue:{type:[Boolean,Number,String,Array],default:null}},data(){const e=this.defaultValue!==null?this.defaultValue:this.multiple?[]:"";return{data:this.options,value:e,lastValue:e}},watch:{defaultValue(e){this.lastValue=e},options(e){this.data=e}},computed:{nameField(){return this.defaultProps?this.defaultProps.name:"name"},valueField(){return this.defaultProps?this.defaultProps.value:"value"},hasSelection(){return this.multiple&&this.value.length>0||this.data.find(e=>e[this.valueField]===this.value)},title(){if(this.multiple){if(this.lastValue.length>0)return this.data.filter(e=>this.lastValue.includes(e[this.valueField])).map(e=>e[this.nameField]).join("/")}else if(this.lastValue&&this.data.length>0)return this.data.find(e=>e[this.valueField]===this.lastValue)[this.nameField];return this.defaultTitle}},methods:{initValue(){this.value=this.lastValue},cellClick(e){if(this.multiple){const t=this.value.indexOf(e);t>-1?this.value.splice(t,1):this.value.push(e)}else this.required?(this.value=e,this.confirm()):this.value=this.value===e?"":e},isSelected(e){return this.multiple?this.value.includes(e):this.value===e},confirm(){if(this.required&&!this.hasSelection)return F({message:"请选择".concat(this.defaultTitle),background:"rgba(0, 0, 0, 0.7)",duration:2e3}),!1;this.$refs.dropdownItem&&this.$refs.dropdownItem.toggle(!1),this.lastValue=this.value,this.$emit("confirm",this.value)}}},E={key:0,class:"py-4 px-8"};function q(e,t,s,_,n,a){const g=i("van-icon"),m=i("van-cell"),h=i("van-button"),D=i("van-dropdown-item");return o(),p(D,{ref:"dropdownItem",title:a.title,onOpen:a.initValue},{default:u(()=>[(o(!0),y(x,null,V(n.data,r=>(o(),p(m,{key:r[a.valueField],title:r[a.nameField],"title-class":{"text-blue":a.isSelected(r[a.valueField])},size:"large",center:"",onClick:c=>a.cellClick(r[a.valueField])},{"right-icon":u(()=>[w(d(g,{name:"success",size:"20px",color:"#1989fa"},null,512),[[C,a.isSelected(r[a.valueField])]])]),_:2},1032,["title","title-class","onClick"]))),128)),s.multiple||!s.required?(o(),y("div",E,[d(h,{type:"primary",block:"",onClick:a.confirm},{default:u(()=>t[0]||(t[0]=[b("确定")])),_:1,__:[0]},8,["onClick"])])):S("",!0)]),_:1},8,["title","onOpen"])}const Q=Y(A,[["render",q],["__scopeId","data-v-9c7c6b0f"]]);export{X as d,Q as f};
