System.register(["./form-legacy-20b7cd9d.js","./validate-legacy-4e8f0db9.js"],(function(t,i){"use strict";var e,s;return{setters:[t=>{e=t.e},t=>{s=t.b}],execute:function(){t("f",{data:()=>({bpmPersonObj:{approverConfigList:[],ccUserConfigList:[],approverParamList:[],ccParamList:[],initiatorInfo:null},detailParamList:[]}),props:{isCustomeInit:{type:Boolean,default:!1},excludeProps:{type:Array,default:()=>[]}},computed:{bizId(){var t,i;return(null===(t=this.$route.query)||void 0===t?void 0:t.bizId)||(null===(i=this.$route.params)||void 0===i?void 0:i.bizId)||""},taskId(){var t,i;return(null===(t=this.$route.query)||void 0===t?void 0:t.taskId)||(null===(i=this.$route.params)||void 0===i?void 0:i.taskId)||""},processInstanceId(){var t,i;return(null===(t=this.$route.query)||void 0===t?void 0:t.processInstanceId)||(null===(i=this.$route.params)||void 0===i?void 0:i.processInstanceId)||""},type(){var t,i;return(null===(t=this.$route.query)||void 0===t?void 0:t.type)||(null===(i=this.$route.params)||void 0===i?void 0:i.type)||"add"},taskKey(){var t,i;return(null===(t=this.$route.query)||void 0===t?void 0:t.taskKey)||(null===(i=this.$route.params)||void 0===i?void 0:i.taskKey)||""},approverConfigList(){return this.bpmPersonObj.approverConfigList||[]},ccUserConfigList(){return this.bpmPersonObj.ccUserConfigList||[]},approverParamList(){return this.bpmPersonObj.approverParamList||[]},ccParamList(){return this.bpmPersonObj.ccParamList||[]},initiatorInfo(){return this.bpmPersonObj.initiatorInfo},flowConfig(){let t={};"view"!==this.type&&"execute"!==this.type||Object.keys(this.formData||{}).forEach((i=>{"execute"===this.type&&"notifyMethod"===i?t[i]="":t[i]="readonly"}));const i=this.taskKey||"UserTask_0";return"execute"!==this.type||"UserTask_0"!==i&&"fawkes_custom_flow_start"!==i||Object.keys(this.formData||{}).forEach((i=>{t[i]=""})),this.customConfig&&this.customConfig(t),t}},created(){!this.isCustomeInit&&this.getFormDataInit()},methods:{isVisibleTaskKey(t){const i=(this.allTaskKeys||[]).filter((i=>i.includes(t)));for(let e=0;e<i.length;e++){const s=i[e],r=s.findIndex((i=>i===t)),o=s.findIndex((t=>{var i;return t===((null===(i=this.formData)||void 0===i?void 0:i.taskKey)||this.taskKey)}));return"view"===this.type?o>r:o>=r}return!1},isEnableTaskKey(t){return this.isVisibleTaskKey(t)},async getFormDataInit(){try{var t,i;this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),this.beforeGetFormData&&await(null===(t=this.beforeGetFormData)||void 0===t?void 0:t.call(this)),this.bizId&&"0"!==this.bizId?await this._getFormDataWithBizId():await this._getFormDataForNew(),this.afterGetFormData&&await(null===(i=this.afterGetFormData)||void 0===i?void 0:i.call(this))}catch(e){}finally{this.$closeToast()}},async _getFormDataWithBizId(){const t={entityName:this.entityName,taskKey:this.taskKey,id:this.bizId,type:this.type,detailEntityNameList:this.detailEntityNameList||[]},i=await e(this.service.query,t);if(i){let t=(null==i?void 0:i.entityObject)||{};t.notifyMethod?s(t.notifyMethod)&&(t.notifyMethod=JSON.parse(t.notifyMethod)):t.notifyMethod=[0],i.entityObject=t;const e=this.beforeInit?await this.beforeInit(i):null;let r={approverConfigList:(null==i?void 0:i.approverConfigList)||[],ccUserConfigList:(null==i?void 0:i.ccUserConfigList)||[],approverParamList:(null==i?void 0:i.approverParamList)||[],ccParamList:"view"===this.type&&(null==i?void 0:i.ccParamList)||[],initiatorInfo:null==i?void 0:i.initiatorInfo};this.excludeProps&&this.excludeProps.length>0&&(r.approverConfigList=r.approverConfigList.filter((t=>this.excludeProps.indexOf(t.prop)<0))),e&&3===e.processState&&(r.approverConfigList=r.approverConfigList.map((t=>{let i=r.approverParamList.find((i=>i.prop===t.prop));return{...t,...i}}))),r=this.beforeSetBpmPerson?await this.beforeSetBpmPerson(r,e||t,i):r,this.bpmPersonObj=r,console.log("this.bpmPersonObj===> ",this.bpmPersonObj),e?this.formData=e:(this.formData=t,this.detailParamList=(null==i?void 0:i.detailParamList)||[])}},async _getFormDataForNew(){const t={entityName:this.entityName,detailEntityNameList:this.detailEntityNameList||[],taskKey:this.taskKey,type:this.type},i=await e(this.service.query,t);if(i){let t={approverConfigList:(null==i?void 0:i.approverConfigList)||[],ccUserConfigList:(null==i?void 0:i.ccUserConfigList)||[]};this.excludeProps&&this.excludeProps.length>0&&(t.approverConfigList=t.approverConfigList.filter((t=>this.excludeProps.indexOf(t.prop)<0))),t=this.beforeSetBpmPerson?await this.beforeSetBpmPerson(t,{},i):t,this.bpmPersonObj=t}},setBpmPerson(t){t&&Object.keys(t).forEach((i=>{this.bpmPersonObj[i]=t[i]}))}},beforeDestroy(){this.detailParamList=[],this.bpmPersonObj={approverConfigList:[],ccUserConfigList:[],approverParamList:[],ccParamList:[],initiatorInfo:null}}})}}}));
