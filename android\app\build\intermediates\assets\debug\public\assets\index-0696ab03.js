import{F as P,D as T}from"./index-1be3ad72.js";import{_ as U}from"./index-4829f8e2.js";import{Q as f,R as L,X as k,V as p,k as n,U as t,Y as d}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const D={name:"CB38",components:{FormTemplate:P,DocumentPart:T},emits:[],props:{},setup(u,{attrs:e,slots:b,emit:V}){},data(){return{detailTable:[],attachmentDesc:"1、\n2、"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:u,detailParamList:e}){},onBeforeSubmit({formData:u,detailParamList:e,taskComment3:b},V){return new Promise((i,m)=>{try{i()}catch(s){m(s)}})}}},B={class:"one-line"},F={class:"form-info"},O={class:"form-info"},h={class:"form-info"},z={class:"comment-wp"},A={class:"textarea-wp"},W={class:"attachment-desc"},g={class:"comment-wp"},I={class:"textarea-wp"},E={class:"comment-wp"},Q={class:"textarea-wp"},R={class:"footer-input"},X={class:"form-info"},Y={class:"form-info"},q={class:"form-info"};function G(u,e,b,V,i,m){const s=f("van-field"),c=f("DocumentPart"),y=f("FormTemplate");return L(),k(y,{ref:"FormTemplate",nature:"确认","on-after-init":m.onAfterInit,"on-before-submit":m.onBeforeSubmit,"detail-table":i.detailTable,"is-show-confirm1":!1,attachmentDesc:i.attachmentDesc},{default:p(({formData:o,formTable:w,baseObj:r,uploadAccept:x,taskStart:a,taskComment2:v,taskComment3:_,taskComment4:C,taskComment5:N})=>[n(c,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:o.constructionDeptName,deptOptions:r.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!a},{default:p(()=>[t("div",B,[e[0]||(e[0]=t("span",{style:{"padding-left":"2em"}},"按照贵方审核通过的关于",-1)),t("span",F,d(o.field1),1),e[1]||(e[1]=t("span",null,"的工作计划",-1)),e[2]||(e[2]=t("span",null,"（回复单编号：承包 [",-1)),t("span",O,d(o.field2),1),e[3]||(e[3]=t("span",null,"] 回复",-1)),t("span",h,d(o.field3),1),e[4]||(e[4]=t("span",null,"号，",-1)),e[5]||(e[5]=t("span",null,"或监理文件编号），",-1)),e[6]||(e[6]=t("span",null,"我方已完成相关工作，执行情况如下，",-1)),e[7]||(e[7]=t("span",null,"请贵方确认。",-1))]),t("div",z,[t("div",A,[n(s,{modelValue:o.field4,"onUpdate:modelValue":l=>o.field4=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"（完成情况说明）",readonly:!a},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),t("div",W,[e[8]||(e[8]=t("div",null,"附件：",-1)),n(s,{modelValue:o.attachmentDesc,"onUpdate:modelValue":l=>o.attachmentDesc=l,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!a},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),n(c,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:r.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!a},{default:p(()=>[t("div",g,[e[9]||(e[9]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",I,[n(s,{modelValue:o.comment2,"onUpdate:modelValue":l=>o.comment2=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),n(c,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!a},{default:p(()=>[t("div",E,[e[10]||(e[10]=t("div",null,"审核意见：",-1)),t("div",Q,[n(s,{modelValue:o.comment3,"onUpdate:modelValue":l=>o.comment3=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!_},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:o,formTable:w,baseObj:r,uploadAccept:x,taskStart:a,taskComment2:v,taskComment3:_,taskComment4:C,taskComment5:N})=>[t("div",R,[e[11]||(e[11]=t("span",null,"注：1、本表一式",-1)),t("span",X,d(o.num1),1),e[12]||(e[12]=t("span",null,"份，由承包人填写，监理机构确认后，承包人",-1)),t("span",Y,d(o.num2),1),e[13]||(e[13]=t("span",null,"份，监理机构",-1)),t("span",q,d(o.num3),1),e[14]||(e[14]=t("span",null,"份。",-1))]),e[15]||(e[15]=t("div",{class:"footer-input",style:{"text-indent":"2em"}}," 2、本表主要用于承包人对监理机构发出的监理通知、指示的执行情况确认。 ",-1))]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const de=U(D,[["render",G]]);export{de as default};
