import{F as P,D as k}from"./index-1be3ad72.js";import{_ as T}from"./index-4829f8e2.js";import{Q as c,R as x,X as L,V as l,k as a,U as t,Y as n}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const B={name:"CB24",components:{FormTemplate:P,DocumentPart:k},emits:[],props:{},setup(r,{attrs:e,slots:b,emit:_}){},data(){return{detailTable:[],attachmentDesc:"变更建议书（承包人提出的变更建议，应附变更建议书。\n变更实施方案（承包人收到监理机构发出的变更意向书或变更指示，应提交变更实施方案）。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:r,detailParamList:e}){},onBeforeSubmit({formData:r,detailParamList:e,taskComment3:b},_){return new Promise((p,i)=>{try{p()}catch(m){i(m)}})}}},F={class:"one-line"},O={class:"form-info"},U={class:"form-info"},A={class:"form-info"},h={class:"attachment-desc"},W={class:"comment-wp"},g={class:"textarea-wp"},z={class:"footer-input"},I={class:"form-info"},E={class:"form-info"},Q={class:"form-info"},R={class:"form-info"};function S(r,e,b,_,p,i){const m=c("van-field"),u=c("DocumentPart"),v=c("FormTemplate");return x(),L(v,{ref:"FormTemplate",nature:"变更","on-after-init":i.onAfterInit,"on-before-submit":i.onBeforeSubmit,"detail-table":p.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:p.attachmentDesc},{default:l(({formData:o,formTable:C,baseObj:d,uploadAccept:N,taskStart:s,taskComment2:V,taskComment3:w,taskComment4:y,taskComment5:D})=>[a(u,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:o.constructionDeptName,deptOptions:d.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!s},{default:l(()=>[t("div",F,[e[0]||(e[0]=t("span",{style:{"padding-left":"2em"}},"我方根据贵方变更",-1)),t("span",O,n(o.field1),1),e[1]||(e[1]=t("span",null,"由于",-1)),t("span",U,n(o.field2),1),e[2]||(e[2]=t("span",null,"原因，",-1)),e[3]||(e[3]=t("span",null,"现提交",-1)),t("span",A,n(o.field3),1),e[4]||(e[4]=t("span",null,"，",-1)),e[5]||(e[5]=t("span",null,"请贵方审批。",-1))]),t("div",h,[e[6]||(e[6]=t("div",null,"附件：",-1)),a(m,{modelValue:o.attachmentDesc,"onUpdate:modelValue":f=>o.attachmentDesc=f,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),a(u,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:d.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!s},{default:l(()=>[t("div",W,[e[7]||(e[7]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",g,[a(m,{modelValue:o.comment2,"onUpdate:modelValue":f=>o.comment2=f,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!V},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),a(u,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:d.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!s},{default:l(()=>e[8]||(e[8]=[t("div",{class:"comment-wp"},[t("div",null,"监理机构另行签发审批意见。")],-1)])),_:2,__:[8]},1032,["deptValue","deptOptions","disabled"])]),footer:l(({formData:o,formTable:C,baseObj:d,uploadAccept:N,taskStart:s,taskComment2:V,taskComment3:w,taskComment4:y,taskComment5:D})=>[t("div",z,[e[9]||(e[9]=t("span",null,"说明：本表一式",-1)),t("span",I,n(o.num1),1),e[10]||(e[10]=t("span",null,"份，由承包人填写，监理机构签收后，发包人",-1)),t("span",E,n(o.num2),1),e[11]||(e[11]=t("span",null,"份，监理机构",-1)),t("span",Q,n(o.num3),1),e[12]||(e[12]=t("span",null,"份，承包人",-1)),t("span",R,n(o.num4),1),e[13]||(e[13]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const se=T(B,[["render",S]]);export{se as default};
