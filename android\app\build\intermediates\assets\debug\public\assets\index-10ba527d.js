import{g as C}from"./api-58f40a5a.js";import{L as w}from"./ListItem-e25471c1.js";import{_ as T,u as b}from"./index-4829f8e2.js";import{Q as o,R as p,X as k,V as l,k as t,S as m,F as y,W as R,Z as x,a2 as v,U as I,B as g,y as N}from"./verder-361ae6c7.js";import{S as P}from"./sift-bc945174.js";import{F as S}from"./FormItemPicker-d3f69283.js";import"./vant-91101745.js";const D={name:"List",components:{ListItem:w},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(n,{attrs:e,slots:c,emit:u}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}},computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const n={...this.searchParams,...this.search};n.page===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e=await C(n),c=this.searchParams.page<=1?[]:this.list||[];this.list.length>=e.total&&(this.finished=!0),this.list=[...c,...e.records],this.searchParams.page++}catch(n){this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}}}},Q={key:0,class:"p-[10px]"};function U(n,e,c,u,s,r){const f=o("ListItem"),_=o("van-empty"),d=o("van-list"),h=o("van-pull-refresh");return p(),k(h,{modelValue:s.refreshing,"onUpdate:modelValue":e[1]||(e[1]=a=>s.refreshing=a),onRefresh:r.onRefresh},{default:l(()=>[t(d,{loading:s.loading,"onUpdate:loading":e[0]||(e[0]=a=>s.loading=a),finished:s.finished,"finished-text":s.list&&s.list.length?"没有更多了":"",onLoad:r.onLoadList,"immediate-check":!1},{default:l(()=>[s.list&&s.list.length?(p(!0),m(y,{key:0},R(s.list||[],(a,L)=>(p(),k(f,{key:a.id,item:a,tabName:"质量检查",onDelSuccess:r.onRefresh},null,8,["item","onDelSuccess"]))),128)):(p(),m(y,{key:1},[s.loading?x("",!0):(p(),m("div",Q,[t(_,{description:"暂无数据"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])}const V=T(D,[["render",U],["__scopeId","data-v-6e43433e"]]);const F=b(),z={name:"QualityInspection",components:{FormItemPicker:S,List:V},props:{},emits:[],setup(n,{attrs:e,slots:c,emit:u}){},data(){return{search:{hazardNumber:"",sectionId:"",inspectionType:"",inspectionResult:""},Sift:P,showTop:!1}},mounted(){},methods:{handleAdd(){F.QUALITY_INSPECTION={},this.$router.push({path:"/QualityInspectionDetail",query:{type:"add",title:"新增质量检查"}})},handleQuery(){this.showTop=!1,this.$nextTick(()=>{this.$refs.List&&this.$refs.List.onRefresh(this.search)})},handleResetting(){this.search={inspectionNumber:"",sectionId:"",inspectionType:"",inspectionResult:""},this.handleQuery()}}},B={class:"btn-group"};function E(n,e,c,u,s,r){const f=o("van-icon"),_=o("Navbar"),d=o("FormItemPicker"),h=o("van-button"),a=o("van-popup"),L=o("List");return p(),m(y,null,[t(_,{back:""},{right:l(()=>[t(f,{name:s.Sift,size:"2em",onClick:e[0]||(e[0]=v(i=>s.showTop=!s.showTop,["stop","prevent"]))},null,8,["name"])]),_:1}),t(a,{show:s.showTop,"onUpdate:show":e[4]||(e[4]=i=>s.showTop=i),position:"top"},{default:l(()=>[t(d,{value:s.search.sectionId,"onUpdate:value":e[1]||(e[1]=i=>s.search.sectionId=i),"dict-name":n.$DICT_CODE.project_section,placeholder:"选择所属标段"},null,8,["value","dict-name"]),t(d,{value:s.search.inspectionType,"onUpdate:value":e[2]||(e[2]=i=>s.search.inspectionType=i),"dict-name":n.$DICT_CODE.safe_inspection_type,placeholder:"选择检查类型"},null,8,["value","dict-name"]),t(d,{value:s.search.inspectionResult,"onUpdate:value":e[3]||(e[3]=i=>s.search.inspectionResult=i),"dict-name":n.$DICT_CODE.safe_inspection_result,placeholder:"选择检查结果"},null,8,["value","dict-name"]),I("div",B,[t(h,{round:"",type:"primary",plain:"",onClick:v(r.handleQuery,["stop","prevent"])},{default:l(()=>e[6]||(e[6]=[g("查询")])),_:1,__:[6]},8,["onClick"]),t(h,{round:"",plain:"",onClick:v(r.handleResetting,["stop","prevent"])},{default:l(()=>e[7]||(e[7]=[g("重置")])),_:1,__:[7]},8,["onClick"])])]),_:1},8,["show"]),I("div",{class:N(["view-height",{"no-tabbar":n.envFeishu}])},[t(L,{ref:"List",search:s.search},null,8,["search"])],2),t(h,{type:"primary",size:"normal",style:{width:"100%"},onClick:e[5]||(e[5]=i=>r.handleAdd())},{default:l(()=>e[8]||(e[8]=[g("新增检查")])),_:1,__:[8]})],64)}const Y=T(z,[["render",E],["__scopeId","data-v-a37c72ca"]]);export{Y as default};
