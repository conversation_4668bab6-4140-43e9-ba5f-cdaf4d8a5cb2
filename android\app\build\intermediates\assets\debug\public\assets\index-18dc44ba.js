import{F as x,D as L}from"./index-1be3ad72.js";import{_ as B}from"./index-4829f8e2.js";import{Q as b,R as _,X as F,V as l,k as p,U as e,Y as s}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const O={name:"CB39",components:{FormTemplate:x,DocumentPart:L},emits:[],props:{},setup(r,{attrs:t,slots:V,emit:v}){},data(){return{detailTable:[],attachmentDesc:"计算资料、证明文件等。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:r,detailParamList:t}){},onBeforeSubmit({formData:r,detailParamList:t,taskComment3:V},v){return new Promise((i,d)=>{try{i()}catch(a){d(a)}})}}},U={class:"one-line"},A={class:"form-info"},D={class:"form-info"},g={class:"form-info"},W={class:"one-line"},z={class:"form-info"},I={class:"form-info"},E={class:"form-info"},Q={class:"form-info"},R={class:"form-info"},S={class:"form-info"},X={class:"form-info"},Y={class:"attachment-desc"},j={class:"comment-wp"},q={class:"textarea-wp"},G={class:"footer-input"},H={class:"form-info"},J={class:"form-info"},K={class:"form-info"};function M(r,t,V,v,i,d){const a=b("van-field"),u=b("DocumentPart"),N=b("FormTemplate");return _(),F(N,{ref:"FormTemplate",nature:"付结","on-after-init":d.onAfterInit,"on-before-submit":d.onBeforeSubmit,"detail-table":i.detailTable,"is-show-confirm1":!1,attachmentDesc:i.attachmentDesc},{default:l(({formData:n,formTable:y,baseObj:m,uploadAccept:w,taskStart:o,taskComment2:C,taskComment3:P,taskComment4:k,taskComment5:T})=>[p(u,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:n.constructionDeptName,deptOptions:m.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!o},{default:l(()=>[e("div",U,[t[0]||(t[0]=e("span",{style:{"padding-left":"2em"}},"依据施工合同约定，我方已完成合同工程",-1)),e("span",A,s(n.projectName),1),t[1]||(t[1]=e("span",null,"工程的施工，",-1)),t[2]||(t[2]=e("span",null,"收到发包人签发的",-1)),e("span",D,s(n.field1),1),t[3]||(t[3]=e("span",null,"证书。",-1)),t[4]||(t[4]=e("span",null,"现申请该工程的",-1)),e("span",g,s(n.field2),1),t[5]||(t[5]=e("span",null,"付款。",-1))]),e("div",W,[t[6]||(t[6]=e("span",{style:{"padding-left":"2em"}},"经核计，我方共应获得工程价款合计为（大写）",-1)),e("span",z,s(n.field3),1),t[7]||(t[7]=e("span",null,"元（小写",-1)),e("span",I,s(n.field4),1),t[8]||(t[8]=e("span",null,"元）",-1)),t[9]||(t[9]=e("span",null,"截至本次申请已得到",-1)),t[10]||(t[10]=e("span",null,"各项付款金额总计为",-1)),t[11]||(t[11]=e("span",null,"（大写）",-1)),e("span",E,s(n.field5),1),t[12]||(t[12]=e("span",null,"元（小写",-1)),e("span",Q,s(n.field6),1),t[13]||(t[13]=e("span",null,"元）",-1)),t[14]||(t[14]=e("span",null,"现申请 ",-1)),e("span",R,s(n.field7),1),t[15]||(t[15]=e("span",null,"付款金额总计为",-1)),t[16]||(t[16]=e("span",null,"（大写）",-1)),e("span",S,s(n.field8),1),t[17]||(t[17]=e("span",null,"元（小写",-1)),e("span",X,s(n.field9),1),t[18]||(t[18]=e("span",null,"元）。",-1)),t[19]||(t[19]=e("span",null,"请贵方审核。",-1))]),e("div",Y,[t[20]||(t[20]=e("div",null,"附件：",-1)),p(a,{modelValue:n.attachmentDesc,"onUpdate:modelValue":f=>n.attachmentDesc=f,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),p(u,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:n.epcDeptName,deptOptions:m.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!o},{default:l(()=>[e("div",j,[t[21]||(t[21]=e("div",null,"EPC总承包项目部意见：",-1)),e("div",q,[p(a,{modelValue:n.comment2,"onUpdate:modelValue":f=>n.comment2=f,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),p(u,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:n.supervisionDeptName,deptOptions:m.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!o},{default:l(()=>t[22]||(t[22]=[e("div",{class:"comment-wp"},[e("div",null,"监理机构审核后，另行签发意见。")],-1)])),_:2,__:[22]},1032,["deptValue","deptOptions","disabled"])]),footer:l(({formData:n,formTable:y,baseObj:m,uploadAccept:w,taskStart:o,taskComment2:C,taskComment3:P,taskComment4:k,taskComment5:T})=>[e("div",G,[t[23]||(t[23]=e("span",null,"注：1、本表一式",-1)),e("span",H,s(n.num1),1),t[24]||(t[24]=e("span",null,"份，由承包人填写，监理机构确认后，承包人",-1)),e("span",J,s(n.num2),1),t[25]||(t[25]=e("span",null,"份，监理机构",-1)),e("span",K,s(n.num3),1),t[26]||(t[26]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const rt=B(O,[["render",M]]);export{rt as default};
