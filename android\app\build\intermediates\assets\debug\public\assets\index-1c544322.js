import{F as P,D as k}from"./index-1be3ad72.js";import{_ as T}from"./index-4829f8e2.js";import{Q as b,R as x,X as L,V as s,k as l,U as t,Y as a}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const B={name:"CB33",components:{FormTemplate:P,DocumentPart:k},emits:[],props:{},setup(r,{attrs:e,slots:c,emit:V}){},data(){return{detailTable:[],attachmentDesc:"1、工程进度付款汇总表。\n2、已完工程量汇总表。\n3、合同分类分项项目进度付款明细表。4、合同措施项目进度付款明细表。\n5、变更项目进度付款明细表。6、计日工项目进度付款明细表。\n7、索赔确认单清单。\n8、其他。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:r,detailParamList:e}){},onBeforeSubmit({formData:r,detailParamList:e,taskStart:c},V){return new Promise((p,i)=>{try{p()}catch(d){i(d)}})}}},F={class:"one-line"},O={class:"form-info"},U={class:"form-info"},A={class:"form-info"},W={class:"form-info"},g={class:"attachment-desc"},z={class:"comment-wp"},I={class:"textarea-wp"},S={class:"footer-input"},E={class:"form-info"};function Q(r,e,c,V,p,i){const d=b("van-field"),u=b("DocumentPart"),D=b("FormTemplate");return x(),L(D,{ref:"FormTemplate",nature:"进度付","on-after-init":i.onAfterInit,"on-before-submit":i.onBeforeSubmit,"detail-table":p.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:p.attachmentDesc},{default:s(({formData:o,formTable:_,baseObj:m,uploadAccept:C,taskStart:n,taskComment2:v,taskComment3:N,taskComment4:w,taskComment5:y})=>[l(u,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:o.constructionDeptName,deptOptions:m.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!n},{default:s(()=>[t("div",F,[e[0]||(e[0]=t("span",{style:{"padding-left":"2em"}},"我方今申请支付",-1)),t("span",O,a(o.field1),1),e[1]||(e[1]=t("span",null,"年",-1)),t("span",U,a(o.field2),1),e[2]||(e[2]=t("span",null,"月",-1)),e[3]||(e[3]=t("span",null,"工程进度付款，",-1)),e[4]||(e[4]=t("span",null,"总金额为",-1)),e[5]||(e[5]=t("span",null,"（大写）",-1)),t("span",A,a(o.field3),1),e[6]||(e[6]=t("span",null,"元",-1)),e[7]||(e[7]=t("span",null,"（小写",-1)),t("span",W,a(o.field4),1),e[8]||(e[8]=t("span",null,"元）",-1)),e[9]||(e[9]=t("span",null,"，请贵方审核。",-1))]),t("div",g,[e[10]||(e[10]=t("div",null,"附件：",-1)),l(d,{modelValue:o.attachmentDesc,"onUpdate:modelValue":f=>o.attachmentDesc=f,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!n},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),l(u,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:m.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!n},{default:s(()=>[t("div",z,[e[11]||(e[11]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",I,[l(d,{modelValue:o.comment2,"onUpdate:modelValue":f=>o.comment2=f,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),l(u,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:m.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!n},{default:s(()=>e[12]||(e[12]=[t("div",{class:"comment-wp"},[t("div",null,"审核后，监理机构将另行签发工程进度付款证书。")],-1)])),_:2,__:[12]},1032,["deptValue","deptOptions","disabled"])]),footer:s(({formData:o,formTable:_,baseObj:m,uploadAccept:C,taskStart:n,taskComment2:v,taskComment3:N,taskComment4:w,taskComment5:y})=>[t("div",S,[e[13]||(e[13]=t("span",null,"说明：本申请书及附表一式",-1)),t("span",E,a(o.num1),1),e[14]||(e[14]=t("span",null,"份，由承包人填写呈报监理机构审核后，作为工程进度付款证书的附件报送发包人批准。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const oe=T(B,[["render",Q]]);export{oe as default};
