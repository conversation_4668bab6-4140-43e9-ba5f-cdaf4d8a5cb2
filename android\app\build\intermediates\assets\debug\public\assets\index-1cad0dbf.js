import{h as T,_ as L,A as w}from"./index-4829f8e2.js";import{R as b}from"./constants-94a272fa.js";import{d as N}from"./form-a8596e72.js";import{Q as r,R as p,S as y,k as o,V as l,U as n,Y as _,B as g,a2 as I,X as C,F as k,W as z,Z as E,y as P}from"./verder-361ae6c7.js";import{F as M}from"./FormItemPicker-d3f69283.js";import{S as A}from"./sift-bc945174.js";import"./vant-91101745.js";const x="project-yjp-tcs";function D(t){return T({url:"".concat(x,"/safe/inspection/hazard/page"),method:"get",params:t})}const R={name:"ListItem",components:{},props:{item:{type:Object,default:()=>({})}},emits:[],setup(t,{attrs:e,slots:i,emit:d}){},data(){return{statusMap:b}},watch:{},created(){},computed:{statusTypeMap(){var t;return((t=this.statusMap)==null?void 0:t.TYPE_MAP)||{}},statusLabelMap(){var t;return((t=this.statusMap)==null?void 0:t.LABEL_MAP)||{}},user(){return this.$store.USER_INFO}},methods:{handelDel(){let t={formKey:"SafeInspectionHazard",entityName:"SafeInspectionHazard",detailEntityNameList:[]};this.$confirm({title:"提示",message:"确认删除".concat(this.item.hazardNumber,"?")}).then(()=>{N(w.VUE_APP_TCS_API_SERVICENAME,this.item.id,t).then(e=>{this.$emit("delSuccess")})}).catch(()=>{})},toFormCenter(){const{id:t,formKey:e,taskId:i,taskKey:d,processInstanceId:s,formName:a}=this.item;this.$router.push({path:"/FormCenter/SafeInspectionHazard",query:{type:this.user.userName===this.item.createBy&&this.item.processState===3?"execute":"view",taskKey:d,formKey:"SafeInspectionHazard",bizId:t,taskId:i,processInstanceId:s,title:"质量检查问题整改流程"}})}}},F={class:"body"},V={class:"item-info header"},O={class:"value"},U={class:"item-info"},B={class:"value"},j={class:"item-info"},H={class:"value"},K={class:"item-info"},Y={class:"value"},Q={class:"item-info"},q={class:"value"},G={class:"right"};function W(t,e,i,d,s,a){const f=r("van-tag"),v=r("van-button"),u=r("van-swipe-cell");return p(),y("div",{class:"task-item",onClick:e[0]||(e[0]=I(h=>a.toFormCenter(),["stop","prevent"]))},[o(u,null,{right:l(()=>[o(v,{square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:a.user.userName!==i.item.createBy||![s.statusMap.PENDING_REVIEW,s.statusMap.STAGE].includes(i.item.rectificationStatus),onClick:a.handelDel},null,8,["disabled","onClick"])]),default:l(()=>[n("div",F,[n("div",V,[e[1]||(e[1]=n("span",{class:"key"},"隐患编号",-1)),n("span",O,_(i.item.hazardNumber),1)]),n("div",U,[e[2]||(e[2]=n("span",{class:"key"},"所属标段",-1)),n("span",B,_(t.$formatLabel(i.item.sectionId,t.$DICT_CODE.project_section)),1)]),n("div",j,[e[3]||(e[3]=n("span",{class:"key"},"隐患级别",-1)),n("span",H,_(t.$formatLabel(i.item.level,t.$DICT_CODE.safe_hazard_level)),1)]),n("div",K,[e[4]||(e[4]=n("span",{class:"key"},"整改期限",-1)),n("span",Y,_(t.$dayjs(i.item.deadline).format("YYYY-MM-DD")),1)]),n("div",Q,[e[5]||(e[5]=n("span",{class:"key"},"整改内容",-1)),n("span",q,_(i.item.description),1)]),n("div",G,[o(f,{class:"tag",color:a.statusTypeMap[i.item.rectificationStatus],plain:"",size:"medium"},{default:l(()=>[g(_(a.statusLabelMap[i.item.rectificationStatus]),1)]),_:1},8,["color"])])])]),_:1})])}const X=L(R,[["render",W],["__scopeId","data-v-ce6aa895"]]);const Z={name:"List",components:{ListItem:X},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(t,{attrs:e,slots:i,emit:d}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}},computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const t={...this.searchParams,...this.search};t.page===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e=await D(t),i=this.searchParams.page<=1?[]:this.list||[];this.list.length>=e.total&&(this.finished=!0),this.list=[...i,...e.records],this.searchParams.page++}catch(t){this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}}}},J={key:0,class:"p-[10px]"};function $(t,e,i,d,s,a){const f=r("ListItem"),v=r("van-empty"),u=r("van-list"),h=r("van-pull-refresh");return p(),C(h,{modelValue:s.refreshing,"onUpdate:modelValue":e[1]||(e[1]=c=>s.refreshing=c),onRefresh:a.onRefresh},{default:l(()=>[o(u,{loading:s.loading,"onUpdate:loading":e[0]||(e[0]=c=>s.loading=c),finished:s.finished,"finished-text":s.list&&s.list.length?"没有更多了":"",onLoad:a.onLoadList,"immediate-check":!1},{default:l(()=>[s.list&&s.list.length?(p(!0),y(k,{key:0},z(s.list||[],(c,S)=>(p(),C(f,{key:c.id,item:c,onDelSuccess:a.onRefresh},null,8,["item","onDelSuccess"]))),128)):(p(),y(k,{key:1},[s.loading?E("",!0):(p(),y("div",J,[o(v,{description:"暂无数据"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])}const ee=L(Z,[["render",$],["__scopeId","data-v-e324c0cf"]]);const te={name:"SafeInspectionHazard",components:{FormItemPicker:M,List:ee},props:{},emits:[],setup(t,{attrs:e,slots:i,emit:d}){},data(){return{search:{hazardNumber:"",sectionId:"",rectificationStatus:""},Sift:A,showTop:!1,statusOptions:Object.entries(b.LABEL_MAP).map(([t,e])=>({value:t,label:e}))}},watch:{},mounted(){},methods:{handleAdd(){this.$router.push({path:"/FormCenter/SafeInspectionHazard",query:{type:"add",title:"新增隐患",taskKey:"UserTask_0"}})},handleQuery(){this.showTop=!1,this.$nextTick(()=>{this.$refs.List&&this.$refs.List.onRefresh(this.search)})},handleResetting(){this.search={correctionNumber:"",sectionId:"",rectificationStatus:""},this.handleQuery()}}},se={class:"btn-group"};function ne(t,e,i,d,s,a){const f=r("van-icon"),v=r("Navbar"),u=r("FormItemPicker"),h=r("van-button"),c=r("van-popup"),S=r("List");return p(),y(k,null,[o(v,{back:""},{right:l(()=>[o(f,{name:s.Sift,size:"2em",onClick:e[0]||(e[0]=I(m=>s.showTop=!s.showTop,["stop","prevent"]))},null,8,["name"])]),_:1}),o(c,{show:s.showTop,"onUpdate:show":e[3]||(e[3]=m=>s.showTop=m),position:"top"},{default:l(()=>[o(u,{value:s.search.sectionId,"onUpdate:value":e[1]||(e[1]=m=>s.search.sectionId=m),"dict-name":t.$DICT_CODE.project_section,placeholder:"选择所属标段"},null,8,["value","dict-name"]),o(u,{value:s.search.rectificationStatus,"onUpdate:value":e[2]||(e[2]=m=>s.search.rectificationStatus=m),placeholder:"选择流转状态",columns:[...s.statusOptions],"columns-field-names":{text:"label",value:"value",children:"none"}},null,8,["value","columns"]),n("div",se,[o(h,{round:"",type:"primary",plain:"",onClick:I(a.handleQuery,["stop","prevent"])},{default:l(()=>e[5]||(e[5]=[g("查询")])),_:1,__:[5]},8,["onClick"]),o(h,{round:"",plain:"",onClick:I(a.handleResetting,["stop","prevent"])},{default:l(()=>e[6]||(e[6]=[g("重置")])),_:1,__:[6]},8,["onClick"])])]),_:1},8,["show"]),n("div",{class:P(["view-height",{"no-tabbar":t.envFeishu}])},[o(S,{ref:"List",search:s.search},null,8,["search"])],2),o(h,{type:"primary",size:"normal",style:{width:"100%"},onClick:e[4]||(e[4]=m=>a.handleAdd())},{default:l(()=>e[7]||(e[7]=[g("新增整改")])),_:1,__:[7]})],64)}const me=L(te,[["render",ne],["__scopeId","data-v-65022d2f"]]);export{me as default};
