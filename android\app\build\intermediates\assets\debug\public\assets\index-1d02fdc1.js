import{F as N,D as L}from"./index-a831f9da.js";import{_ as P}from"./index-4829f8e2.js";import{Q as b,R as c,X as D,V as h,k as n,U as e,Y as m,S as _,W as U,F as w}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const B={name:"JL14",components:{FormTemplate:N,DocumentPart:L},emits:[],props:{},setup(o,{attrs:l,slots:v,emit:y}){},data(){return{detailTable:[{},{},{},{},{},{},{}],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:o,detailParamList:l}){},onBeforeSubmit({formData:o,detailParamList:l,taskStart:v},y){return new Promise((f,i)=>{try{if(y==="submit"&&v){if(!o.check1&&!o.check2&&!o.check3)return this.$showNotify({type:"danger",message:"请选择协商意见!",duration:3*1e3}),i(!1),!1;if(o.check2&&!o.field1)return this.$showNotify({type:"danger",message:"请填写延期天数!",duration:3*1e3}),i(!1),!1}f()}catch(u){i(u)}})},changeCheck1(o){o&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.check3=!1,this.$refs.FormTemplate.formData.field1="",this.$nextTick(()=>{var l;(l=this.$refs.FormTemplate.$refs.form)==null||l.resetValidation()}))},changeCheck2(o){o&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check3=!1)},changeCheck3(o){o&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.field1="",this.$nextTick(()=>{var l;(l=this.$refs.FormTemplate.$refs.form)==null||l.resetValidation()}))}}},O={class:"comment-wp"},A={class:"check-wp"},W={class:"check-wp"},q={class:"form-info"},I={class:"check-wp"},E={class:"table-wp"},J={class:"jl-table"},Q={class:"cell"},R={class:"cell"},X={class:"cell"},Y={class:"cell"},z={class:"cell"},G={class:"cell"},H={class:"cell"},K={class:"cell"},M={class:"cell"},Z={class:"cell"},j={class:"split-part"},S={class:"footer-input"},ee={class:"form-info"},le={class:"form-info"},te={class:"form-info"},de={class:"form-info"};function se(o,l,v,y,f,i){const u=b("van-checkbox"),p=b("van-field"),k=b("DocumentPart"),C=b("FormTemplate");return c(),D(C,{ref:"FormTemplate",nature:"变确","on-after-init":i.onAfterInit,"on-before-submit":i.onBeforeSubmit,"detail-table":f.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],"show-target":!1,attachmentDesc:f.attachmentDesc},{default:h(({formData:d,formTable:g,baseObj:V,uploadAccept:T,taskStart:a,taskComment2:F,taskComment3:x,taskComment4:$})=>[n(k,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:d.supervisionDeptName,deptOptions:V.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!a},{default:h(()=>[e("div",O,[l[6]||(l[6]=e("div",{style:{"text-indent":"2em"}},[e("span",null,"根据有关规定和施工合同约定，经友好协商，发包人和承包人就变更项目价格协商如下，"),e("span",null,"同时变更项目工期协商意见：")],-1)),e("div",null,[e("div",A,[n(u,{modelValue:d.check1,"onUpdate:modelValue":t=>d.check1=t,shape:"square",disabled:!0,onChange:i.changeCheck1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),l[0]||(l[0]=e("span",null,"不延期",-1)),l[1]||(l[1]=e("span",null,"/",-1)),e("div",W,[n(u,{modelValue:d.check2,"onUpdate:modelValue":t=>d.check2=t,shape:"square",disabled:!0,onChange:i.changeCheck2},null,8,["modelValue","onUpdate:modelValue","onChange"])]),l[2]||(l[2]=e("span",null,"延期",-1)),e("span",q,m(d.field1),1),l[3]||(l[3]=e("span",null,"天",-1)),l[4]||(l[4]=e("span",null,"/",-1)),e("div",I,[n(u,{modelValue:d.check3,"onUpdate:modelValue":t=>d.check3=t,shape:"square",disabled:!0,onChange:i.changeCheck3},null,8,["modelValue","onUpdate:modelValue","onChange"])]),l[5]||(l[5]=e("span",null,"另行协商",-1))])]),e("div",E,[e("table",J,[l[9]||(l[9]=e("colgroup",null,[e("col",{width:"50"}),e("col",{width:"30"}),e("col",{"min-width":"50"}),e("col",{"min-width":"50"}),e("col",{"min-width":"50"}),e("col",{"min-width":"50"})],-1)),e("tbody",null,[l[7]||(l[7]=e("tr",null,[e("th",{rowspan:"5"},[e("div",{class:"cell"},"双方协商一致的")]),e("th",null,[e("div",{class:"cell"},"序号")]),e("th",null,[e("div",{class:"cell"},"项目名称")]),e("th",null,[e("div",{class:"cell"},"单位")]),e("th",null,[e("div",{class:"cell"},"确认价格（单价或合价）")]),e("th",null,[e("div",{class:"cell"},"备注")])],-1)),(c(!0),_(w,null,U(g.slice(0,4),(t,r)=>(c(),_("tr",{key:r},[e("th",null,[e("div",Q,m(r+1),1)]),e("td",null,[e("div",R,[n(p,{modelValue:t.field2,"onUpdate:modelValue":s=>t.field2=s,label:"","label-width":"0",placeholder:"",readonly:!a,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("td",null,[e("div",X,[n(p,{modelValue:t.field3,"onUpdate:modelValue":s=>t.field3=s,label:"","label-width":"0",placeholder:"",readonly:!a,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("td",null,[e("div",Y,[n(p,{modelValue:t.field4,"onUpdate:modelValue":s=>t.field4=s,label:"","label-width":"0",placeholder:"",readonly:!a,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("td",null,[e("div",z,[n(p,{modelValue:t.field5,"onUpdate:modelValue":s=>t.field5=s,label:"","label-width":"0",placeholder:"",readonly:!a,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]))),128)),l[8]||(l[8]=e("tr",null,[e("th",{rowspan:"5"},[e("div",{class:"cell"},"双方未协商一致的")]),e("th",null,[e("div",{class:"cell"},"序号")]),e("th",null,[e("div",{class:"cell"},"项目名称")]),e("th",null,[e("div",{class:"cell"},"单位")]),e("th",null,[e("div",{class:"cell"},"总监理工程师确定的暂定价格（单价或合价）")]),e("th",null,[e("div",{class:"cell"},"备注")])],-1)),(c(!0),_(w,null,U(g.slice(4,7),(t,r)=>(c(),_("tr",{key:"4_7".concat(r)},[e("th",null,[e("div",G,m(r+1),1)]),e("td",null,[e("div",H,[n(p,{modelValue:t.field2,"onUpdate:modelValue":s=>t.field2=s,label:"","label-width":"0",placeholder:"",readonly:!a,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("td",null,[e("div",K,[n(p,{modelValue:t.field3,"onUpdate:modelValue":s=>t.field3=s,label:"","label-width":"0",placeholder:"",readonly:!a,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("td",null,[e("div",M,[n(p,{modelValue:t.field4,"onUpdate:modelValue":s=>t.field4=s,label:"","label-width":"0",placeholder:"",readonly:!a,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("td",null,[e("div",Z,[n(p,{modelValue:t.field5,"onUpdate:modelValue":s=>t.field5=s,label:"","label-width":"0",placeholder:"",readonly:!a,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]))),128))])])]),e("div",j,[n(k,{deptLabel:"发包人：",deptProp:"employerName",deptValue:d.employerName,deptOptions:V.employerName,personLabel:"负责人：",labelWidth:"4em",disabled:!a},{default:h(()=>l[10]||(l[10]=[e("div",{style:{height:"30px"}},null,-1)])),_:2,__:[10]},1032,["deptValue","deptOptions","disabled"]),n(k,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:d.epcDeptName,deptOptions:V.epcDeptName,personLabel:"签收人：",labelWidth:"4em",disabled:!a},{default:h(()=>l[11]||(l[11]=[e("div",{style:{height:"30px"}},null,-1)])),_:2,__:[11]},1032,["deptValue","deptOptions","disabled"])]),l[12]||(l[12]=e("div",{style:{"text-indent":"2em",padding:"20px 0"}}," 合同双方就上述协商一致的变更项目价格、工期、按确认的意见执行，合同双方未协商一致的，按监理工程师确定的暂定价格随工程进度付款暂定支付。后续事宜按合同约定执行。 ",-1))]),_:2,__:[12]},1032,["deptValue","deptOptions","disabled"])]),footer:h(({formData:d,formTable:g,baseObj:V,uploadAccept:T,taskStart:a,taskComment2:F,taskComment3:x,taskComment4:$})=>[e("div",S,[l[13]||(l[13]=e("span",null,"说明：本表一式",-1)),e("span",ee,m(d.num1),1),l[14]||(l[14]=e("span",null,"份，由监理机构填写，各方签字后，监理机构",-1)),e("span",le,m(d.num2),1),l[15]||(l[15]=e("span",null,"份，发包人",-1)),e("span",te,m(d.num3),1),l[16]||(l[16]=e("span",null,"份，承包人",-1)),e("span",de,m(d.num4),1),l[17]||(l[17]=e("span",null,"份，",-1)),l[18]||(l[18]=e("span",null,"办理结算时使用。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ye=P(B,[["render",se],["__scopeId","data-v-dfecde95"]]);export{ye as default};
