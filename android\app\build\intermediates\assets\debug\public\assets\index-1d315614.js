import{F as B,D as T}from"./index-1be3ad72.js";import{_ as O}from"./index-4829f8e2.js";import{Q as N,R as c,X as S,V as m,k as b,U as t,Y as o,B as i,S as w,W as k,F as y}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const A={name:"CB05",components:{FormTemplate:B,DocumentPart:T},emits:[],props:{},setup(a,{attrs:e,slots:f,emit:d}){},data(){return{detailTable:[{},{},{},{},{},{}],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:a,detailParamList:e}){},onBeforeSubmit({formData:a,detailParamList:e,taskComment3:f},d){return new Promise((n,s)=>{try{n()}catch(p){s(p)}})},getSummaries(a=[],e){return a.reduce((d,n)=>{const s=Number(n[e]);return isNaN(s)?Number(Number(d).toFixed(2)):Number(Number(d+s).toFixed(2))},0)}}},W={style:{margin:"20px 0"}},U={class:"cb-table"},E={colspan:"7"},I={class:"cell"},z={class:"cell",style:{"text-align":"center"}},Q=["data-i"],R={class:"cell"},X={colspan:"1"},Y={class:"cell",style:{"text-align":"center"}},q={class:"comment-wp"},G={class:"textarea-wp"},H={class:"footer-input"},J={class:"form-info"},K={class:"form-info"},M={class:"form-info"},Z={class:"form-info"};function $(a,e,f,d,n,s){const p=N("DocumentPart"),C=N("van-field"),D=N("FormTemplate");return c(),S(D,{ref:"FormTemplate",nature:"分包","on-after-init":s.onAfterInit,"on-before-submit":s.onBeforeSubmit,"detail-table":n.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:n.attachmentDesc},{default:m(({formData:l,formTable:_,baseObj:r,uploadAccept:P,taskStart:u,taskComment2:V,taskComment3:g,taskComment4:F,taskComment5:L})=>[b(p,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:l.constructionDeptName,deptOptions:r.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!u},{default:m(()=>[e[4]||(e[4]=t("div",{class:"one-line"},[t("span",{style:{"padding-left":"2em"}},"根据施工合同约定和工程需要，我方拟将本申请表中所列项目分包给所选分包人，经考察，所选分包人具备按照合同要求完成所分包工程的资质、经验、技术与管理水平、资源和财务能力，并具有良好的业绩和信誉，请贵方审核。")],-1)),t("div",W,[t("table",U,[e[3]||(e[3]=t("colgroup",null,[t("col",{width:"30px"}),t("col",{width:"60px"}),t("col",{"min-width":"40px"}),t("col",{"min-width":"40px"}),t("col",{"min-width":"40px"}),t("col",{"min-width":"40px"}),t("col",{"min-width":"40px"}),t("col",{"min-width":"40px"}),t("col",{width:"40px"})],-1)),t("tbody",null,[t("tr",null,[e[0]||(e[0]=t("th",{colspan:"2"},[t("div",{class:"cell"},"分包人名称")],-1)),t("td",E,[t("div",I,o(l.field1),1)])]),e[2]||(e[2]=t("tr",null,[t("th",null,[t("div",{class:"cell"},"序号")]),t("th",null,[t("div",{class:"cell"},"合同工程量清单项目编号")]),t("th",null,[t("div",{class:"cell"},"分包工作名称")]),t("th",null,[t("div",{class:"cell"},"单位")]),t("th",null,[t("div",{class:"cell"},"合同工程量")]),t("th",null,[t("div",{class:"cell"},[i("合同单价"),t("br"),i("（元）")])]),t("th",null,[t("div",{class:"cell"},[i("合同金额"),t("br"),i("（元）")])]),t("th",null,[t("div",{class:"cell"},[i("分包工作金额"),t("br"),i("（元）")])]),t("th",null,[t("div",{class:"cell"},"分包工作金额占签约合同价的比例（%）")])],-1)),(c(!0),w(y,null,k(_||[],(v,h)=>(c(),w("tr",{key:h},[t("td",null,[t("div",z,o(h+1),1)]),(c(),w(y,null,k(8,x=>t("td",{key:h+"_"+x,"data-i":x},[t("div",R,o(v["field".concat(x)]),1)],8,Q)),64))]))),128)),t("tr",null,[e[1]||(e[1]=t("th",{colspan:"8"},[t("div",{class:"cell"},"合计")],-1)),t("td",X,[t("div",Y,o(s.getSummaries(_,"field8")),1)])])])])])]),_:2,__:[4]},1032,["deptValue","deptOptions","disabled"]),b(p,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:l.epcDeptName,deptOptions:r.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!u},{default:m(()=>[t("div",q,[e[5]||(e[5]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",G,[b(C,{modelValue:l.comment2,"onUpdate:modelValue":v=>l.comment2=v,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!V},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),b(p,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:l.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!u},{default:m(()=>e[6]||(e[6]=[t("div",{class:"comment-wp"},[t("div",null,"监理机构将另行签发审批意见。")],-1)])),_:2,__:[6]},1032,["deptValue","deptOptions","disabled"])]),footer:m(({formData:l,formTable:_,baseObj:r,uploadAccept:P,taskStart:u,taskComment2:V,taskComment3:g,taskComment4:F,taskComment5:L})=>[t("div",H,[e[7]||(e[7]=t("span",null,"说明：1、本表一式",-1)),t("span",J,o(l.num1),1),e[8]||(e[8]=t("span",null,"份，由承包人填写，监理机构签收后，发包人",-1)),t("span",K,o(l.num2),1),e[9]||(e[9]=t("span",null,"份，监理机构",-1)),t("span",M,o(l.num3),1),e[10]||(e[10]=t("span",null,"份，承包人",-1)),t("span",Z,o(l.num4),1),e[11]||(e[11]=t("span",null,"份。",-1))]),e[12]||(e[12]=t("div",{class:"footer-input"}," 2、本表中的分包工作金额=合同单价×分包工程量。 ",-1))]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const bt=O(A,[["render",$]]);export{bt as default};
