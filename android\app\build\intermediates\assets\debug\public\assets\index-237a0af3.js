import{F as L,D as B}from"./index-1be3ad72.js";import{_ as A}from"./index-4829f8e2.js";import{Q as _,R as f,X as z,V as i,k as s,U as e,Y as m,S as V,W as w,F as C,B as O}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const W={name:"CB13",components:{FormTemplate:L,DocumentPart:B},emits:[],props:{},setup(n,{attrs:t,slots:p,emit:r}){},data(){return{detailTable:[],attachmentDesc:"",defaultChecks:[{checkName:"check1",fieldName:"field3",label:"施工控制测量",child:["1、测量数据","2、数据分析及平差成果"]},{checkName:"check2",fieldName:"field4",label:"工程计量测量",child:["1、工程量计算表","2、断面图","3、其他"]},{checkName:"check3",fieldName:"field5",label:"地形测量",child:["1、测量数据","2、数据分析及成果（数据处理方法、断面图或地形图）"]},{checkName:"check4",fieldName:"field6",label:"施工期变形监测",child:["1、观测数据","2、数据分析及评价"]}]}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:n,detailParamList:t}){setTimeout(()=>{this.setDefaultAttachText()})},onBeforeSubmit({formData:n,detailParamList:t},p){return new Promise((r,d)=>{try{if(p==="submit"&&!n.check1&&!n.check2&&!n.check3&&!n.check4)return this.$showNotify({type:"danger",message:"请选择附件类型!",duration:3*1e3}),d(!1),!1;r()}catch(u){d(u)}})},setDefaultAttachText(){this.defaultChecks.forEach(n=>{(this.type==="add"||!this.$refs.FormTemplate.formData[n.fieldName])&&(this.$refs.FormTemplate.formData[n.fieldName]=n.child.join("\n"))})},onCheckChange(n,{checkName:t,fieldName:p,child:r}){n||(this.$refs.FormTemplate.formData[p]=r.join("\n"))}}},E={class:"comment-wp"},D={class:"one-line"},I={class:"form-info"},j={class:"comment-wp"},q={class:"textarea-wp"},Q={class:"comment-wp"},R={class:"textarea-wp"},X={class:"comment-wp"},Y={class:"form-table"},$={class:"check-input"},G={class:"comment-wp"},H={class:"textarea-wp"},J={class:"comment-wp"},K={class:"textarea-wp"},M={class:"footer-input"},Z={class:"form-info"},S={class:"form-info"},ee={class:"form-info"},te={class:"form-info"};function le(n,t,p,r,d,u){const c=_("van-field"),x=_("van-checkbox"),k=_("DocumentPart"),T=_("FormTemplate");return f(),z(T,{ref:"FormTemplate",nature:"测量","on-after-init":u.onAfterInit,"on-before-submit":u.onBeforeSubmit,"detail-table":d.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:d.attachmentDesc},{default:i(({formData:l,formTable:U,baseObj:b,uploadAccept:F,taskStart:a,taskComment2:v,taskComment3:y,taskComment4:P,taskComment5:g})=>[s(k,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:l.constructionDeptName,deptOptions:b.constructionDeptName,personLabel:"技术负责人：",labelWidth:"10em",disabled:!a},{default:i(()=>[e("div",E,[e("div",D,[t[0]||(t[0]=e("span",{style:{"margin-left":"2em"}},"我方已完成",-1)),e("span",I,m(l.projectName),1),t[1]||(t[1]=e("span",null,"工程的施工测量工作，",-1)),t[2]||(t[2]=e("span",null,"经自检合格，请贵方审核。",-1))])]),e("div",j,[t[3]||(t[3]=e("div",null,"施测部位：",-1)),e("div",q,[s(c,{modelValue:l.field1,"onUpdate:modelValue":o=>l.field1=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!a},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",Q,[t[4]||(t[4]=e("div",null,"施测说明：",-1)),e("div",R,[s(c,{modelValue:l.field2,"onUpdate:modelValue":o=>l.field2=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!a},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",X,[t[5]||(t[5]=e("div",null,"附件：",-1)),e("div",Y,[e("table",null,[e("thead",null,[e("tr",null,[(f(!0),V(C,null,w(d.defaultChecks||[],(o,N)=>(f(),V("th",{key:N},[e("div",$,[s(x,{modelValue:l[o.checkName],"onUpdate:modelValue":h=>l[o.checkName]=h,shape:"square",onChange:h=>u.onCheckChange(h,o),disabled:!a},{default:i(()=>[O(m(o.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue","onChange","disabled"])])]))),128))])]),e("tbody",null,[e("tr",null,[(f(!0),V(C,null,w(d.defaultChecks||[],(o,N)=>(f(),V("td",{key:N},[s(c,{modelValue:l[o.fieldName],"onUpdate:modelValue":h=>l[o.fieldName]=h,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!(l[o.checkName]&&a)},null,8,["modelValue","onUpdate:modelValue","readonly"])]))),128))])])])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),s(k,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:l.epcDeptName,deptOptions:b.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!a},{default:i(()=>[e("div",G,[t[6]||(t[6]=e("div",null,"EPC总承包项目部意见：",-1)),e("div",H,[s(c,{modelValue:l.comment2,"onUpdate:modelValue":o=>l.comment2=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),s(k,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:l.supervisionDeptName,deptOptions:b.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!a},{default:i(()=>[e("div",J,[t[7]||(t[7]=e("div",null,"监理机构审核意见：",-1)),e("div",K,[s(c,{modelValue:l.comment3,"onUpdate:modelValue":o=>l.comment3=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!y},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:i(({formData:l,formTable:U,baseObj:b,uploadAccept:F,taskStart:a,taskComment2:v,taskComment3:y,taskComment4:P,taskComment5:g})=>[e("div",M,[t[8]||(t[8]=e("span",null,"说明：本表一式",-1)),e("span",Z,m(l.num1),1),t[9]||(t[9]=e("span",null,"份，由承包人填写，监理机构审核后，发包人",-1)),e("span",S,m(l.num2),1),t[10]||(t[10]=e("span",null,"份，监理机构",-1)),e("span",ee,m(l.num3),1),t[11]||(t[11]=e("span",null,"份，承包人",-1)),e("span",te,m(l.num4),1),t[12]||(t[12]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const Ve=A(W,[["render",le]]);export{Ve as default};
