import{F as B,D as F}from"./index-a831f9da.js";import{_ as S}from"./index-4829f8e2.js";import{Q as A,R as e,X as L,V as b,U as l,Y as n,B as i,S as u,W as h,F as w}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const P={name:"JL32",components:{FormTemplate:B,DocumentPart:F},emits:[],props:{},setup(p,{attrs:s,slots:m,emit:c}){},data(){return{detailTable:[{},{},{},{},{},{},{},{},{},{}],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:p,detailParamList:s}){},onBeforeSubmit({formData:p,detailParamList:s,taskStart:m},c){return new Promise((o,a)=>{try{o()}catch(d){a(d)}})}}},j={class:"jl-table"},V={colspan:"12"},$={class:"cell"},I={class:"form-info"},N={class:"form-info"},O={class:"form-info"},D={class:"form-info"},E={class:"form-info"},J={class:"cell"},Q={class:"cell"},R={class:"footer-input"},U={class:"form-info"},W={class:"form-info"},X={class:"form-info"},Y={class:"form-info"},q={class:"form-info"};function z(p,s,m,c,o,a){const d=A("FormTemplate");return e(),L(d,{ref:"FormTemplate",nature:"设备","on-after-init":a.onAfterInit,"on-before-submit":a.onBeforeSubmit,"detail-table":o.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:o.attachmentDesc},{default:b(({formData:t,formTable:v,baseObj:k,uploadAccept:g,taskStart:y,taskComment2:x,taskComment3:T,taskComment4:_})=>[l("table",j,[s[11]||(s[11]=l("colgroup",null,[l("col",{width:"30"}),l("col",{width:"50"}),l("col",{"min-width":"60"}),l("col",{"min-width":"60"}),l("col",{"min-width":"60"}),l("col",{"min-width":"60"}),l("col",{"min-width":"60"}),l("col",{"min-width":"60"}),l("col",{"min-width":"60"}),l("col",{"min-width":"60"}),l("col",{"min-width":"60"}),l("col",{"min-width":"60"})],-1)),l("tbody",null,[l("tr",null,[l("td",V,[l("div",$,[l("span",I,n(t.field1),1),s[0]||(s[0]=l("span",null,"设备于",-1)),l("span",N,n(t.field2),1),s[1]||(s[1]=l("span",null,"年",-1)),l("span",O,n(t.field3),1),s[2]||(s[2]=l("span",null,"月",-1)),l("span",D,n(t.field4),1),s[3]||(s[3]=l("span",null,"日",-1)),s[4]||(s[4]=l("span",null,"到达",-1)),l("span",E,n(t.field5),1),s[5]||(s[5]=l("span",null,"施工现场，",-1)),s[6]||(s[6]=l("span",null,"设备材料数量及开箱验收情况如下",-1))])])]),s[7]||(s[7]=l("tr",null,[l("th",{rowspan:"2"},[l("div",{class:"cell"},"序号")]),l("th",{rowspan:"2"},[l("div",{class:"cell"},"名称")]),l("th",{rowspan:"2"},[l("div",{class:"cell"},[i("规格"),l("br"),i("/"),l("br"),i("型号")])]),l("th",{rowspan:"2"},[l("div",{class:"cell"},[i("单位"),l("br"),i("/"),l("br"),i("数量")])]),l("th",{rowspan:"1",colspan:"7"},[l("div",{class:"cell"},"检查")]),l("th",{rowspan:"2"},[l("div",{class:"cell"},[i("开箱"),l("br"),l("br"),i("日期")])])],-1)),s[8]||(s[8]=l("tr",null,[l("th",null,[l("div",{class:"cell"},"外包装情况（是否完好）")]),l("th",null,[l("div",{class:"cell"},"开箱后设备外观质量（有无磨损、撞击）")]),l("th",null,[l("div",{class:"cell"},"备品备件检查情况")]),l("th",null,[l("div",{class:"cell"},"设备合格证")]),l("th",null,[l("div",{class:"cell"},"产品检验证")]),l("th",null,[l("div",{class:"cell"},"产品说明书")]),l("th",null,[l("div",{class:"cell"},"备注")])],-1)),(e(!0),u(w,null,h(v,(C,r)=>(e(),u("tr",{key:r},[l("th",null,[l("div",J,n(r+1),1)]),(e(),u(w,null,h(11,f=>l("td",{key:"".concat(r,"_").concat(f)},[l("div",Q,[l("span",null,n(C["field".concat(f)]),1)])])),64))]))),128)),s[9]||(s[9]=l("tr",null,[l("td",{colspan:"12"},[l("div",{class:"cell"},[l("div",{style:{"text-indent":"2em"}}," 备注：经发包人、监理机构、承包人和供货单位四方现场开箱，进行设备的数量及外观检查，符合设备移交条件，自开箱验收之日起移交承包人保管。 ")])])],-1)),s[10]||(s[10]=l("tr",null,[l("td",{colspan:"3"},[l("div",{class:"part-div"},[l("div",{class:"part-sign"},[l("div",null,[l("span",null,"承包人："),l("span",null,"（现场机构名称及盖章）")]),l("div",{style:{height:"30px"}}),l("div",null,[l("span",null,"代表："),l("span")]),l("div",null,[l("span",null,"日期："),l("span",null,"年"),l("span",null,"月"),l("span",null,"日")])])])]),l("td",{colspan:"3"},[l("div",{class:"part-div"},[l("div",{class:"part-sign"},[l("div",null,[l("span",null,"供货单："),l("span",null,"（名称及盖章）")]),l("div",{style:{height:"30px"}}),l("div",null,[l("span",null,"代表："),l("span")]),l("div",null,[l("span",null,"日期："),l("span",null,"年"),l("span",null,"月"),l("span",null,"日")])])])]),l("td",{colspan:"3"},[l("div",{class:"part-div"},[l("div",{class:"part-sign"},[l("div",null,[l("span",null,"监理机构："),l("span",null,"（名称及盖章）")]),l("div",{style:{height:"30px"}}),l("div",null,[l("span",null,"代表："),l("span")]),l("div",null,[l("span",null,"日期："),l("span",null,"年"),l("span",null,"月"),l("span",null,"日")])])])]),l("td",{colspan:"3"},[l("div",{class:"part-div"},[l("div",{class:"part-sign"},[l("div",null,[l("span",null,"发包人："),l("span",null,"（名称及盖章）")]),l("div",{style:{height:"30px"}}),l("div",null,[l("span",null,"代表："),l("span")]),l("div",null,[l("span",null,"日期："),l("span",null,"年"),l("span",null,"月"),l("span",null,"日")])])])])],-1))])])]),footer:b(({formData:t,formTable:v,baseObj:k,uploadAccept:g,taskStart:y,taskComment2:x,taskComment3:T,taskComment4:_})=>[l("div",R,[s[12]||(s[12]=l("span",null,"说明：本表一式",-1)),l("span",U,n(t.num1),1),s[13]||(s[13]=l("span",null,"份，发包人",-1)),l("span",W,n(t.num2),1),s[14]||(s[14]=l("span",null,"份，监理机构",-1)),l("span",X,n(t.num3),1),s[15]||(s[15]=l("span",null,"份，承包人",-1)),l("span",Y,n(t.num4),1),s[16]||(s[16]=l("span",null,"份、供货商",-1)),l("span",q,n(t.num5),1),s[17]||(s[17]=l("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const rl=S(P,[["render",z]]);export{rl as default};
