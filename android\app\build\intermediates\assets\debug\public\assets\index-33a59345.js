import{F as k,D as x}from"./index-1be3ad72.js";import{_ as L}from"./index-4829f8e2.js";import{Q as b,R as U,X as B,V as a,k as o,U as n,Y as t}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const F={name:"CB34",components:{FormTemplate:k,DocumentPart:x},emits:[],props:{},setup(l,{attrs:e,slots:V,emit:y}){},data(){return{detailTable:[],attachmentDesc:"施工月报"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:l,detailParamList:e}){},onBeforeSubmit({formData:l,detailParamList:e,taskStart:V},y){return new Promise((m,u)=>{try{l.sendingType="施工月报表（".concat(l.field1,"年").concat(l.field2,"月）"),m()}catch(p){u(p)}})}}},O={class:"one-line"},A={class:"form-info"},g={class:"form-info"},W={class:"form-info"},z={class:"form-info"},I={class:"form-info"},S={class:"form-info"},$={class:"form-info"},c={class:"form-info"},E={class:"attachment-desc"},Q={class:"comment-wp"},R={class:"textarea-wp"},X={class:"comment-wp"},Y={class:"form-info"},q={class:"form-info"},G={class:"form-info"},H={class:"form-info",style:{display:"inline-block"}},J={class:"footer-input"},K={class:"form-info"},M={class:"form-info"},Z={class:"form-info"},j={class:"form-info"},D={class:"form-info"};function h(l,e,V,y,m,u){const p=b("van-field"),f=b("DocumentPart"),w=b("FormTemplate");return U(),B(w,{ref:"FormTemplate",nature:"月报","on-after-init":u.onAfterInit,"on-before-submit":u.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:m.attachmentDesc},{default:a(({formData:s,formTable:C,baseObj:r,uploadAccept:P,taskStart:i,taskComment2:v,taskComment3:N,taskComment4:T,taskComment5:_})=>[o(f,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:s.constructionDeptName,deptOptions:r.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!i},{default:a(()=>[n("div",O,[e[0]||(e[0]=n("span",{style:{"padding-left":"2em"}},"现呈报我方编写的",-1)),n("span",A,t(s.field1),1),e[1]||(e[1]=n("span",null,"年",-1)),n("span",g,t(s.field2),1),e[2]||(e[2]=n("span",null,"月",-1)),e[3]||(e[3]=n("span",null,"施工月报",-1)),e[4]||(e[4]=n("span",null,"（",-1)),n("span",W,t(s.field3),1),e[5]||(e[5]=n("span",null,"年",-1)),n("span",z,t(s.field4),1),e[6]||(e[6]=n("span",null,"月",-1)),n("span",I,t(s.field5),1),e[7]||(e[7]=n("span",null,"日",-1)),e[8]||(e[8]=n("span",null,"至",-1)),n("span",S,t(s.field6),1),e[9]||(e[9]=n("span",null,"年",-1)),n("span",$,t(s.field7),1),e[10]||(e[10]=n("span",null,"月",-1)),n("span",c,t(s.field8),1),e[11]||(e[11]=n("span",null,"日）",-1)),e[12]||(e[12]=n("span",null,"，请贵方审阅。",-1))]),n("div",E,[e[13]||(e[13]=n("div",null,"附件：",-1)),o(p,{modelValue:s.attachmentDesc,"onUpdate:modelValue":d=>s.attachmentDesc=d,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),o(f,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:s.epcDeptName,deptOptions:r.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!i},{default:a(()=>[n("div",Q,[e[14]||(e[14]=n("div",null,"EPC总承包项目部意见：",-1)),n("div",R,[o(p,{modelValue:s.comment2,"onUpdate:modelValue":d=>s.comment2=d,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),o(f,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!i},{default:a(()=>[n("div",X,[n("div",null,[e[15]||(e[15]=n("span",null,"今已收到",-1)),n("span",Y,t(s.constructionDeptName),1),e[16]||(e[16]=n("span",null,"所报",-1)),n("span",q,t(s.field1),1),e[17]||(e[17]=n("span",null,"年",-1)),n("span",G,t(s.field2),1),e[18]||(e[18]=n("span",null,"月",-1)),e[19]||(e[19]=n("span",null,"的施工月报及附件共",-1)),n("span",H,[o(p,{modelValue:s.field10,"onUpdate:modelValue":d=>s.field10=d,label:"",placeholder:"",readonly:!N,style:{width:"60px"}},null,8,["modelValue","onUpdate:modelValue","readonly"])]),e[20]||(e[20]=n("span",null,"份。",-1))])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:a(({formData:s,formTable:C,baseObj:r,uploadAccept:P,taskStart:i,taskComment2:v,taskComment3:N,taskComment4:T,taskComment5:_})=>[n("div",J,[e[21]||(e[21]=n("span",null,"注：本表一式",-1)),n("span",K,t(s.num1),1),e[22]||(e[22]=n("span",null,"份，由承包人填写，每月",-1)),n("span",M,t(s.field9),1),e[23]||(e[23]=n("span",null,"日前报监理机构， ",-1)),e[24]||(e[24]=n("span",null,"监理机构签收后返回发包人 ",-1)),n("span",Z,t(s.num2),1),e[25]||(e[25]=n("span",null,"份，监理机构",-1)),n("span",j,t(s.num3),1),e[26]||(e[26]=n("span",null,"份，承包人",-1)),n("span",D,t(s.num4),1),e[27]||(e[27]=n("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const Ve=L(F,[["render",h]]);export{Ve as default};
