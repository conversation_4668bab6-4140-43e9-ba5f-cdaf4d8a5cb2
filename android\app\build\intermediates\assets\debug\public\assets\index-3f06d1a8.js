import{F as k,D}from"./index-1be3ad72.js";import{_ as T}from"./index-4829f8e2.js";import{Q as b,R as x,X as L,V as l,k as p,U as t,Y as o}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const B={name:"CB09",components:{FormTemplate:k,DocumentPart:D},emits:[],props:{},setup(r,{attrs:e,slots:c,emit:V}){},data(){return{detailTable:[],attachmentDesc:"1、已具备工程预付款支付条件的证明材料。\n2、计算依据及结果。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:r,detailParamList:e}){},onBeforeSubmit({formData:r,detailParamList:e,taskComment3:c},V){return new Promise((a,i)=>{try{a()}catch(m){i(m)}})}}},F={class:"one-line"},O={class:"form-info"},U={class:"form-info"},A={class:"form-info"},W={class:"form-info"},g={class:"attachment-desc"},z={class:"comment-wp"},I={class:"textarea-wp"},E={class:"footer-input"},Q={class:"form-info"},R={class:"form-info"},S={class:"form-info"},X={class:"form-info"};function Y(r,e,c,V,a,i){const m=b("van-field"),u=b("DocumentPart"),C=b("FormTemplate");return x(),L(C,{ref:"FormTemplate",nature:"工预付","on-after-init":i.onAfterInit,"on-before-submit":i.onBeforeSubmit,"detail-table":a.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:a.attachmentDesc},{default:l(({formData:n,formTable:N,baseObj:d,uploadAccept:_,taskStart:s,taskComment2:v,taskComment3:w,taskComment4:y,taskComment5:P})=>[p(u,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:n.constructionDeptName,deptOptions:d.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!s},{default:l(()=>[t("div",F,[e[0]||(e[0]=t("span",{style:{"margin-left":"2em"}},"我方承担的",-1)),t("span",O,o(n.projectName),1),e[1]||(e[1]=t("span",null,"工程，",-1)),e[2]||(e[2]=t("span",null,"依据施工合同约定，",-1)),e[3]||(e[3]=t("span",null,"已具备工程预付款支付条件，",-1)),e[4]||(e[4]=t("span",null,"现申请支付第",-1)),t("span",U,o(n.field1),1),e[5]||(e[5]=t("span",null,"次预付款，",-1)),e[6]||(e[6]=t("span",null,"计（大写）",-1)),t("span",A,o(n.field2),1),e[7]||(e[7]=t("span",null,"元（小写",-1)),t("span",W,o(n.field3),1),e[8]||(e[8]=t("span",null,"元）。",-1)),e[9]||(e[9]=t("span",null,"请贵方核查。",-1))]),t("div",g,[e[10]||(e[10]=t("div",null,"附件：",-1)),p(m,{modelValue:n.attachmentDesc,"onUpdate:modelValue":f=>n.attachmentDesc=f,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),p(u,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:n.epcDeptName,deptOptions:d.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!s},{default:l(()=>[t("div",z,[e[11]||(e[11]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",I,[p(m,{modelValue:n.comment2,"onUpdate:modelValue":f=>n.comment2=f,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),p(u,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:n.supervisionDeptName,deptOptions:d.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!s},{default:l(()=>e[12]||(e[12]=[t("div",{class:"comment-wp"},[t("div",null,"监理机构将另行签发工程预付款支付证书。")],-1)])),_:2,__:[12]},1032,["deptValue","deptOptions","disabled"])]),footer:l(({formData:n,formTable:N,baseObj:d,uploadAccept:_,taskStart:s,taskComment2:v,taskComment3:w,taskComment4:y,taskComment5:P})=>[t("div",E,[e[13]||(e[13]=t("span",null,"注：本表一式",-1)),t("span",Q,o(n.num1),1),e[14]||(e[14]=t("span",null,"份，由承包人填写，监理机构审批后，发包人",-1)),t("span",R,o(n.num2),1),e[15]||(e[15]=t("span",null,"份，监理机构",-1)),t("span",S,o(n.num3),1),e[16]||(e[16]=t("span",null,"份，承包人",-1)),t("span",X,o(n.num4),1),e[17]||(e[17]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const le=T(B,[["render",Y]]);export{le as default};
