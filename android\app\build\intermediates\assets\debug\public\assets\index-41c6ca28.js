import{h,_ as C}from"./index-4829f8e2.js";import{F as k}from"./index-8d635ba7.js";import{F as R}from"./FormItemPicker-d3f69283.js";import{F as E}from"./FormItemDate-ba00d9d5.js";import{F as U}from"./FormItemCalendar-905fde75.js";import{F as I}from"./FormItemPerson-bd0e3e57.js";import{F as w}from"./FormItemCoord-9e82e1bf.js";import{U as V}from"./index-fc22947f.js";import{Q as m,R as f,X as p,V as u,k as a,Z as g}from"./verder-361ae6c7.js";import"./vant-91101745.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./validate-2249584f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";function q(n){return h({url:"/cybereng-safety/safety/hiddenDanger/delayTask",method:"get",params:n})}function x(n){return h({url:"/cybereng-safety/safety/hiddenDanger/sendMessage",method:"get",params:n})}function S(n){return h({url:"/cybereng-safety/yxst/safety/hiddenDanger",method:"put",params:n})}const P={name:"SafetyHiddenDanger",components:{FlowForm:k,FormItemPicker:R,FormItemDate:E,FormItemCalendar:U,FormItemPerson:I,FormItemCoord:w,UploadFiles:V},props:{},emits:[],setup(n,{attrs:t,slots:d,emit:s}){},data(){var n,t;return{type:((n=this.$route.query)==null?void 0:n.type)||"",taskKey:((t=this.$route.query)==null?void 0:t.taskKey)||"",entityName:"SafetyHiddenDanger",formKey:"SafetyHiddenDanger",modelKey:"safety_hidden_danger_flow",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-safety/form/query",submit:"/cybereng-safety/form/commit"},formData:{type:"1",id:void 0,portalId:void 0,subProjectId:"",subProjectName:"",unitEngineeringId:"",unitEngineeringName:"",divisionEngineeringId:"",divisionEngineeringName:"",longitude:"",latitude:"",hiddenDangerCode:void 0,hiddenDangerLevel:"",hiddenDangerCategory:"",rectifyDate:"",overdueState:"",hiddenDangerContent:"",beforeFileToken:"",hiddenDangerReportor:"",hiddenDangerReportorFullname:"",hiddenDangerReportorDeptName:"",hiddenDangerReportorDeptCode:"",hiddenDangerRectifier:"",hiddenDangerRectifierFullname:"",hiddenDangerRectifierDeptName:"",hiddenDangerRectifierDeptCode:"",hiddenDangerRectifyApprover:"",hiddenDangerRectifyApproverFullname:"",hiddenDangerRectifyApproverDeptName:"",hiddenDangerRectifyApproverDeptCode:"",rectifyState:"待整改",rectifyMeasures:"",rectifySituation:"",afterFileToken:"",finishDate:""}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},unitEngineeringList(){if(this.portal.type==1){if(!this.formData.subProjectId)return[];const n=this.subProjectList.find(t=>t.id==this.formData.subProjectId);return n?n.children||[]:[]}else return this.subProjectList[0]?this.subProjectList[0].children||[]:[]},divisionEngineeringList(){if(!this.formData.unitEngineeringId)return[];const n=this.unitEngineeringList.find(t=>t.id==this.formData.unitEngineeringId);return n?n.children||[]:[]},canEdit0(){return this.type==="add"||this.taskKey==="UserTask_0"},canEdit1(){return this.type==="execute"&&this.taskKey==="UserTask_1"},canEdit2(){return this.type==="execute"&&this.taskKey==="UserTask_2"}},watch:{},created(){},mounted(){this.initForm()},methods:{copyCallBack(n){let t=["hiddenDangerContent","beforeFileToken","latitude","longitude"];this.formData={...n},t.forEach(d=>{this.formData[d]=""})},async initForm(){if(this.type==="add"){if(this.formData.portalId=this.portalId,this.portal.type!=1){const o=this.subProjectList.find(l=>l.portalId==this.portal.id);this.formData.subProjectId=o==null?void 0:o.id,this.formData.subProjectName=o==null?void 0:o.nodeName}const{userName:n="",userFullname:t="",orgList:d=[]}=this.user||{},s=d.find(o=>{var l;return o.portalId==((l=this.portal)==null?void 0:l.id)})||d[0],e=(s==null?void 0:s.name)||"",i=(s==null?void 0:s.orgNo)||"";this.formData.hiddenDangerReportor=n,this.formData.hiddenDangerReportorFullname=t,this.formData.hiddenDangerReportorDeptName=e,this.formData.hiddenDangerReportorDeptCode=i,this.formData.hiddenDangerRectifyApprover=n,this.formData.hiddenDangerRectifyApproverFullname=t,this.formData.hiddenDangerRectifyApproverDeptName=e,this.formData.hiddenDangerRectifyApproverDeptCode=i}else this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,n=>{const{detailParamList:t=[],entityObject:d}=n;this.detailParamList=t,this.formData={...this.formData,...d}})})},async onDraft(){try{const n={...this.formData,rectifyState:"暂存"};this.$refs.FlowForm.onSaveDraft(this.service.submit,n)}catch(n){console.log(n)}},async onSubmit(){try{await this.$refs.form.validate();let n="待整改";this.taskKey==="UserTask_0"?n="待整改":this.taskKey==="UserTask_2"?n="待审核":this.taskKey==="UserTask_3"&&(n="整改完成",this.formData.finishDate=this.$dayjs().format("YYYY-MM-DD HH:mm:ss"));const t={...this.formData,rectifyState:n};this.$refs.FlowForm.onSubmit(this.service.submit,t)}catch(n){console.log(n)}},async updateFiles(){return new Promise(async(n,t)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),n()}catch(d){t()}})},afterSubmit(n,t){this.updateFiles(),n==="submit"&&(this.taskKey=="UserTask_0"&&q({id:this.formData.id}),(this.taskKey=="UserTask_1"||this.taskKey=="UserTask_2")&&x({id:this.formData.id}),this.formData.id&&S({id:this.formData.id}))},handleSubProjectChange(n={}){this.formData.unitEngineeringId="",this.formData.unitEngineeringName="",this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""},handleUnitEngineeringChange(n={}){this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""}}};function L(n,t,d,s,e,i){const o=m("van-field"),l=m("FormItemPicker"),F=m("FormItemCoord"),b=m("FormItemCalendar"),D=m("van-cell-group"),v=m("UploadFiles"),y=m("FormItemPerson"),c=m("van-form"),N=m("FlowForm");return f(),p(N,{ref:"FlowForm","model-key":e.modelKey,"form-key":e.formKey,"entity-name":e.entityName,"detail-param-list":e.detailParamList,"detail-entity-name-list":e.detailEntityNameList,onDraftClick:i.onDraft,onSubmitClick:i.onSubmit,onAfterSubmit:i.afterSubmit,onCopyCallBack:i.copyCallBack},{default:u(()=>[a(c,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:u(()=>[a(D,{border:!1},{default:u(()=>[e.formData.hiddenDangerCode?(f(),p(o,{key:0,modelValue:e.formData.hiddenDangerCode,"onUpdate:modelValue":t[0]||(t[0]=r=>e.formData.hiddenDangerCode=r),label:"隐患整改单号",placeholder:"审批完成后自动生成",readonly:""},null,8,["modelValue"])):g("",!0),a(l,{label:"子工程",value:e.formData.subProjectId,"onUpdate:value":t[1]||(t[1]=r=>e.formData.subProjectId=r),text:e.formData.subProjectName,"onUpdate:text":t[2]||(t[2]=r=>e.formData.subProjectName=r),columns:[...i.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:i.portal.type!=1||e.type==="view"||!i.canEdit0,onChange:i.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),a(l,{label:"单位工程",value:e.formData.unitEngineeringId,"onUpdate:value":t[3]||(t[3]=r=>e.formData.unitEngineeringId=r),text:e.formData.unitEngineeringName,"onUpdate:text":t[4]||(t[4]=r=>e.formData.unitEngineeringName=r),columns:[...i.unitEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择单位工程",required:"",rules:[{required:!0,message:"请选择单位工程"}],readonly:e.type==="view"||!i.canEdit0,onChange:i.handleUnitEngineeringChange},null,8,["value","text","columns","readonly","onChange"]),a(l,{label:"分部工程",value:e.formData.divisionEngineeringId,"onUpdate:value":t[5]||(t[5]=r=>e.formData.divisionEngineeringId=r),text:e.formData.divisionEngineeringName,"onUpdate:text":t[6]||(t[6]=r=>e.formData.divisionEngineeringName=r),columns:[...i.divisionEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择分部工程",required:"",rules:[{required:!0,message:"请选择分部工程"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","text","columns","readonly"]),a(F,{label:"定位",longitude:e.formData.longitude,"onUpdate:longitude":t[7]||(t[7]=r=>e.formData.longitude=r),latitude:e.formData.latitude,"onUpdate:latitude":t[8]||(t[8]=r=>e.formData.latitude=r),title:"选择定位",readonly:e.type==="view"||!i.canEdit0},null,8,["longitude","latitude","readonly"]),a(l,{label:"隐患级别",value:e.formData.hiddenDangerLevel,"onUpdate:value":t[9]||(t[9]=r=>e.formData.hiddenDangerLevel=r),"dict-name":"hidden_danger_level",title:"选择隐患级别",required:"",rules:[{required:!0,message:"请选择隐患级别"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),a(l,{label:"隐患分类",value:e.formData.hiddenDangerCategory,"onUpdate:value":t[10]||(t[10]=r=>e.formData.hiddenDangerCategory=r),"dict-name":"hidden_danger_category",title:"选择隐患分类",required:"",rules:[{required:!0,message:"请选择隐患分类"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),a(b,{label:"整改期限",value:e.formData.rectifyDate,"onUpdate:value":t[11]||(t[11]=r=>e.formData.rectifyDate=r),title:"选择整改期限",required:"",rules:[{required:!0,message:"请选择整改期限"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),e.type!=="add"?(f(),p(o,{key:1,modelValue:e.formData.rectifyState,"onUpdate:modelValue":t[12]||(t[12]=r=>e.formData.rectifyState=r),label:"整改状态",placeholder:"",readonly:""},null,8,["modelValue"])):g("",!0),e.type!=="add"?(f(),p(o,{key:2,modelValue:e.formData.overdueState,"onUpdate:modelValue":t[13]||(t[13]=r=>e.formData.overdueState=r),label:"逾期情况",placeholder:"",readonly:""},null,8,["modelValue"])):g("",!0)]),_:1}),a(D,{border:!1},{default:u(()=>[a(o,{label:"隐患内容",modelValue:e.formData.hiddenDangerContent,"onUpdate:modelValue":t[14]||(t[14]=r=>e.formData.hiddenDangerContent=r),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入隐患内容",required:"",rules:[{required:!0,message:"请输入隐患内容"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),a(o,{label:"附件照片","label-align":"top","input-align":"left"},{input:u(()=>[a(v,{ref:"beforeFiles",g9s:e.formData.beforeFileToken,"onUpdate:g9s":t[15]||(t[15]=r=>e.formData.beforeFileToken=r),accept:"image/*",multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:e.type==="view"||!i.canEdit0},null,8,["g9s","readonly"])]),_:1})]),_:1}),a(D,{border:!1},{default:u(()=>[a(y,{label:"隐患上报人",userName:e.formData.hiddenDangerReportor,"onUpdate:userName":t[16]||(t[16]=r=>e.formData.hiddenDangerReportor=r),userFullname:e.formData.hiddenDangerReportorFullname,"onUpdate:userFullname":t[17]||(t[17]=r=>e.formData.hiddenDangerReportorFullname=r),deptName:e.formData.hiddenDangerReportorDeptName,"onUpdate:deptName":t[18]||(t[18]=r=>e.formData.hiddenDangerReportorDeptName=r),deptCode:e.formData.hiddenDangerReportorDeptCode,"onUpdate:deptCode":t[19]||(t[19]=r=>e.formData.hiddenDangerReportorDeptCode=r),title:"选择隐患上报人",required:"",rules:[{required:!0,message:"请选择隐患上报人"}],readonly:!0},null,8,["userName","userFullname","deptName","deptCode"]),a(o,{modelValue:e.formData.hiddenDangerReportorDeptName,"onUpdate:modelValue":t[20]||(t[20]=r=>e.formData.hiddenDangerReportorDeptName=r),label:"上报人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),a(y,{label:"隐患整改人",userName:e.formData.hiddenDangerRectifier,"onUpdate:userName":t[21]||(t[21]=r=>e.formData.hiddenDangerRectifier=r),userFullname:e.formData.hiddenDangerRectifierFullname,"onUpdate:userFullname":t[22]||(t[22]=r=>e.formData.hiddenDangerRectifierFullname=r),deptName:e.formData.hiddenDangerRectifierDeptName,"onUpdate:deptName":t[23]||(t[23]=r=>e.formData.hiddenDangerRectifierDeptName=r),deptCode:e.formData.hiddenDangerRectifierDeptCode,"onUpdate:deptCode":t[24]||(t[24]=r=>e.formData.hiddenDangerRectifierDeptCode=r),title:"选择隐患整改人",required:"",rules:[{required:!0,message:"请选择隐患整改人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),a(o,{modelValue:e.formData.hiddenDangerRectifierDeptName,"onUpdate:modelValue":t[25]||(t[25]=r=>e.formData.hiddenDangerRectifierDeptName=r),label:"整改人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),a(y,{label:"隐患审核人",userName:e.formData.hiddenDangerRectifyApprover,"onUpdate:userName":t[26]||(t[26]=r=>e.formData.hiddenDangerRectifyApprover=r),userFullname:e.formData.hiddenDangerRectifyApproverFullname,"onUpdate:userFullname":t[27]||(t[27]=r=>e.formData.hiddenDangerRectifyApproverFullname=r),deptName:e.formData.hiddenDangerRectifyApproverDeptName,"onUpdate:deptName":t[28]||(t[28]=r=>e.formData.hiddenDangerRectifyApproverDeptName=r),deptCode:e.formData.hiddenDangerRectifyApproverDeptCode,"onUpdate:deptCode":t[29]||(t[29]=r=>e.formData.hiddenDangerRectifyApproverDeptCode=r),title:"选择隐患审核人",required:"",rules:[{required:!0,message:"请选择隐患审核人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),a(o,{modelValue:e.formData.hiddenDangerRectifyApproverDeptName,"onUpdate:modelValue":t[30]||(t[30]=r=>e.formData.hiddenDangerRectifyApproverDeptName=r),label:"审核人所在部门",placeholder:"",readonly:""},null,8,["modelValue"])]),_:1}),e.type==="view"||e.taskKey&&e.taskKey!=="UserTask_0"?(f(),p(D,{key:0,border:!1},{default:u(()=>[a(o,{label:"整改措施",modelValue:e.formData.rectifyMeasures,"onUpdate:modelValue":t[31]||(t[31]=r=>e.formData.rectifyMeasures=r),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改措施",required:"",rules:[{required:!0,message:"请填写整改措施"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),a(o,{label:"整改情况",modelValue:e.formData.rectifySituation,"onUpdate:modelValue":t[32]||(t[32]=r=>e.formData.rectifySituation=r),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改情况",required:"",rules:[{required:!0,message:"请填写整改情况"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),a(o,{label:"附件照片","label-align":"top","input-align":"left"},{input:u(()=>[a(v,{ref:"afterFiles",g9s:e.formData.afterFileToken,"onUpdate:g9s":t[33]||(t[33]=r=>e.formData.afterFileToken=r),accept:"image/*",multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:e.type==="view"||!i.canEdit2},null,8,["g9s","readonly"])]),_:1})]),_:1})):g("",!0)]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit","onCopyCallBack"])}const ee=C(P,[["render",L]]);export{ee as default};
