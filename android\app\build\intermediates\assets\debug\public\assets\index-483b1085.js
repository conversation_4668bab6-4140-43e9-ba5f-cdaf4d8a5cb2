import{F as c}from"./index-8d635ba7.js";import{D as g,a as b}from"./DesignDispatchDrawingTable-897743ce.js";import{_ as h}from"./index-4829f8e2.js";import{Q as m,R as F,X as v,V as s,k as r}from"./verder-361ae6c7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";const T={name:"DesignEPCAuditNew",components:{FlowForm:c,DesignDispatchCommonTopNew:g,DesignDispatchDrawingTable:b},data(){var a,t;return{type:((a=this.$route.query)==null?void 0:a.type)||"",taskKey:((t=this.$route.query)==null?void 0:t.taskKey)||"",modelKey:"technology_design_EPC_audit_2",entityName:"TechnologyCommonFile",detailEntityName:"TechnologyDesignEPCAudit",detailParamList:[],detailEntityNameList:["TechnologyDesignEPCAudit","TechnologyDesignEPCAuditDrawing"],service:{query:"/cybereng-technology/design/EPCAudit/query",submit:"cybereng-technology/design/form/commit"},formKey:"DesignEPCAuditNew",formData:{formKey:"DesignEPCAuditNew",portalId:"",subProjectName:"",subProjectId:null,modelType:"",sendingName:"",sendingCode:"",sendingType:"",sendingTypeCode:"",contentDescription:"",profession:"",relavancePartName:"",relavancePart:"",leadDesigner:"",leadDesignerFullname:"",userTelephoneNum:"",fileNum:"",fileName:"",notifyMethods:[],isconfirm1:!0,approverUsername1:"",approverFullname1:"",approverUnit1:"",time1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",time2:"",expireTime2:"",isconfirm2:!0,approverUsername3:"",approverFullname3:"",approverUnit3:"",time3:"",expireTime1:"",duplicateUsername1:"",duplicateFullname1:"",duplicateUsername2:"",duplicateFullname2:""},formDetailTable:[],detailId:""}},computed:{},mounted(){this.initForm()},methods:{async initForm(){this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,a=>{const{detailParamList:t=[],entityObject:i}=a;this.detailParamList=t,this.detailId=t[0].detailEntityArray[0].id,this.formData={...this.formData,...t[0].detailEntityArray[0],...i,notifyMethods:i.notifyMethod?i.notifyMethod.split(","):[]},this.formDetailTable=t[1].detailEntityArray})})},async onDraft(){try{const a={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,a)}catch(a){console.log(a)}},async onSubmit(){try{if(["fileName","fileNum","relavancePartName","profession","leadDesignerFullname","subProjectName","approverFullname1","approverFullname2"].some(d=>!this.formData[d]))return this.$showToast({message:"请登录数智建管系统完善表单"});if(!this.formDetailTable.length)return this.$showToast({message:"请登录数智建管系统完善表单"});this.setTime();const i={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,i)}catch(a){console.log(a)}},setTime(){switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":{this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_2":{this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_3":{this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}}},async afterSubmit(){}}};function V(a,t,i,d,e,n){const p=m("design-dispatch-common-top-new"),u=m("design-dispatch-drawing-table"),l=m("van-field"),f=m("van-cell-group"),y=m("van-form"),D=m("FlowForm");return F(),v(D,{ref:"FlowForm","model-key":e.modelKey,"form-key":e.formKey,"entity-name":e.entityName,"detail-param-list":e.detailParamList,"detail-entity-name-list":e.detailEntityNameList,onDraftClick:n.onDraft,onSubmitClick:n.onSubmit,onAfterSubmit:n.afterSubmit},{default:s(()=>[r(y,{ref:"form","label-width":"8em","input-align":"right","error-message-align":"right"},{default:s(()=>[r(p,{"form-data":e.formData,type:e.type,readonly:""},null,8,["form-data","type"]),r(u,{"form-detail-table":e.formDetailTable,ref:"detailForm","form-data":e.formData,type:e.type},null,8,["form-detail-table","form-data","type"]),r(f,{border:!1},{default:s(()=>[r(l,{modelValue:e.formData.userFullname,"onUpdate:modelValue":t[0]||(t[0]=o=>e.formData.userFullname=o),label:"发起人",readonly:"",required:""},null,8,["modelValue"]),r(l,{modelValue:e.formData.prjDepName,"onUpdate:modelValue":t[1]||(t[1]=o=>e.formData.prjDepName=o),label:"发起人部门",readonly:""},null,8,["modelValue"]),r(l,{modelValue:e.formData.approverFullname1,"onUpdate:modelValue":t[2]||(t[2]=o=>e.formData.approverFullname1=o),label:"合同部审查人",readonly:"",required:""},null,8,["modelValue"]),r(l,{modelValue:e.formData.approverUnit1,"onUpdate:modelValue":t[3]||(t[3]=o=>e.formData.approverUnit1=o),label:"合同部审查人部门",readonly:""},null,8,["modelValue"]),r(l,{modelValue:e.formData.expireTime1,"onUpdate:modelValue":t[4]||(t[4]=o=>e.formData.expireTime1=o),label:"设置审批截止时间",formatter:o=>"".concat(o?a.$dayjs(o).format("YYYY-MM-DD"):""),readonly:""},null,8,["modelValue","formatter"]),r(l,{modelValue:e.formData.approverFullname2,"onUpdate:modelValue":t[5]||(t[5]=o=>e.formData.approverFullname2=o),label:"施工项目部审查人","label-width":"9em",readonly:"",required:""},null,8,["modelValue"]),r(l,{"label-width":"10em",modelValue:e.formData.approverUnit2,"onUpdate:modelValue":t[6]||(t[6]=o=>e.formData.approverUnit2=o),label:"施工项目部审查人部门",readonly:""},null,8,["modelValue"]),r(l,{modelValue:e.formData.expireTime2,"onUpdate:modelValue":t[7]||(t[7]=o=>e.formData.expireTime2=o),formatter:o=>"".concat(o?a.$dayjs(o).format("YYYY-MM-DD"):""),label:"设置审批截止时间",readonly:""},null,8,["modelValue","formatter"]),r(l,{modelValue:e.formData.duplicateFullname1,"onUpdate:modelValue":t[8]||(t[8]=o=>e.formData.duplicateFullname1=o),label:"抄送至",readonly:""},null,8,["modelValue"])]),_:1})]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}const q=h(T,[["render",V]]);export{q as default};
