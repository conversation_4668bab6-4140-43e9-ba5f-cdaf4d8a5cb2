import{d as T,g as w}from"./api-d09fbc1f.js";import{Q as r,R as h,S as v,k as o,V as l,U as s,Y as m,B as g,a2 as k,X as b,F as C,W as D,Z as R,y as N}from"./verder-361ae6c7.js";import{_ as S,u as L}from"./index-4829f8e2.js";import{L as F}from"./ListItem-e25471c1.js";import{S as x}from"./sift-bc945174.js";import{F as O}from"./FormItemPicker-d3f69283.js";import"./vant-91101745.js";import"./api-58f40a5a.js";const E={name:"TaskItem",components:{},props:{item:{type:Object,default:()=>({})}},emits:[],setup(n,{attrs:e,slots:i,emit:f}){},data(){return{}},computed:{taskStateText(){switch(String(this.item.inspectionResult)){case"0":return"正常";case"1":return"有隐患";default:return"有隐患"}},tagColor(){var e;switch(String((e=this.item)==null?void 0:e.inspectionResult)){case"0":return"#07c160";case"1":return"#ff4d4f";default:return"#ff4d4f"}},user(){return this.$store.USER_INFO}},watch:{},mounted(){},methods:{handelDel(){this.$confirm({title:"提示",message:"确认删除".concat(this.item.inspectionNumber,"?")}).then(()=>{T(this.item).then(()=>{this.$emit("delSuccess")})}).catch(()=>{})},toFormCenter(){this.$store.SAFE_INSPECTION_FORM={},this.$router.push({path:"/SafetyCheckDetail",query:{id:this.item.id,type:this.user.userName===this.item.createBy?"update":"detail",title:this.user.userName===this.item.createBy?"编辑安全检查":"安全检查详情"}})}}},P={class:"body"},U={class:"item-info header"},B={class:"value"},V={class:"item-info"},A={class:"value"},j={class:"item-info"},z={class:"value"},M={class:"item-info"},Y={class:"value"},Q={class:"item-info"},q={class:"value"},W={class:"item-info"},X={class:"value"},Z={class:"right"};function G(n,e,i,f,t,a){const _=r("van-tag"),y=r("van-button"),p=r("van-swipe-cell");return h(),v("div",{class:"task-item",onClick:e[0]||(e[0]=k(u=>a.toFormCenter(),["stop","prevent"]))},[o(p,null,{right:l(()=>[o(y,{square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:a.user.userName!==i.item.createBy||!i.item.showDel,onClick:a.handelDel},null,8,["disabled","onClick"])]),default:l(()=>[s("div",P,[s("div",U,[e[1]||(e[1]=s("span",{class:"key"},"检查编号",-1)),s("span",B,m(i.item.inspectionNumber),1)]),s("div",V,[e[2]||(e[2]=s("span",{class:"key"},"检查类型",-1)),s("span",A,m(n.$formatLabel(i.item.inspectionType,n.$DICT_CODE.safe_inspection_type)),1)]),s("div",j,[e[3]||(e[3]=s("span",{class:"key"},"检查单位",-1)),s("span",z,m(i.item.inspectionUnitName),1)]),s("div",M,[e[4]||(e[4]=s("span",{class:"key"},"所属标段",-1)),s("span",Y,m(n.$formatLabel(i.item.sectionId,n.$DICT_CODE.project_section)),1)]),s("div",Q,[e[5]||(e[5]=s("span",{class:"key"},"详细区域",-1)),s("span",q,m(i.item.inspectionArea),1)]),s("div",W,[e[6]||(e[6]=s("span",{class:"key"},"检查日期",-1)),s("span",X,m(n.$dayjs(i.item.inspectionDate).format("YYYY-MM-DD")),1)]),s("div",Z,[o(_,{class:"tag",color:a.tagColor,plain:"",size:"medium"},{default:l(()=>[g(m(a.taskStateText),1)]),_:1},8,["color"])])])]),_:1})])}const H=S(E,[["render",G],["__scopeId","data-v-1772d1c4"]]);const J={name:"SafetyCheck",components:{ListItem:F,SafetyCheckItem:H},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(n,{attrs:e,slots:i,emit:f}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}},computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const n={...this.searchParams,...this.search};n.page===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e=await w(n),i=this.searchParams.page<=1?[]:this.list||[];this.list.length>=e.total&&(this.finished=!0),this.list=[...i,...e.records],this.searchParams.page++}catch(n){this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}}}},K={key:0,class:"p-[10px]"};function $(n,e,i,f,t,a){const _=r("SafetyCheckItem"),y=r("van-empty"),p=r("van-list"),u=r("van-pull-refresh");return h(),b(u,{modelValue:t.refreshing,"onUpdate:modelValue":e[1]||(e[1]=d=>t.refreshing=d),onRefresh:a.onRefresh},{default:l(()=>[o(p,{loading:t.loading,"onUpdate:loading":e[0]||(e[0]=d=>t.loading=d),finished:t.finished,"finished-text":t.list&&t.list.length?"没有更多了":"",onLoad:a.onLoadList,"immediate-check":!1},{default:l(()=>[t.list&&t.list.length?(h(!0),v(C,{key:0},D(t.list||[],(d,I)=>(h(),b(_,{key:d.id,item:d,tabName:"安全检查",onDelSuccess:a.onRefresh},null,8,["item","onDelSuccess"]))),128)):(h(),v(C,{key:1},[t.loading?R("",!0):(h(),v("div",K,[o(y,{description:"暂无安全检查"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])}const ee=S(J,[["render",$],["__scopeId","data-v-9fbf9be2"]]);const te=L(),se={name:"SafetyCheckIndex",components:{FormItemPicker:O,SafetyCheck:ee},props:{},emits:[],setup(n,{attrs:e,slots:i,emit:f}){},data(){return{search:{inspectionNumber:"",sectionId:"",inspectionType:"",inspectionResult:""},Sift:x,showTop:!1}},mounted(){},methods:{handleAdd(){te.SAFE_INSPECTION_FORM={},this.$router.push({path:"/SafetyCheckDetail",query:{type:"add",title:"新增安全检查"}})},onCancel(){},handleQuery(){this.showTop=!1,this.$nextTick(()=>{this.$refs.SafetyCheckRef&&this.$refs.SafetyCheckRef.onRefresh(this.search)})},handleResetting(){this.search={inspectionNumber:"",sectionId:"",inspectionType:"",inspectionResult:""},this.handleQuery()}}},ne={class:"btn-group"};function ie(n,e,i,f,t,a){const _=r("van-icon"),y=r("Navbar"),p=r("FormItemPicker"),u=r("van-button"),d=r("van-popup"),I=r("SafetyCheck");return h(),v(C,null,[o(y,{back:""},{right:l(()=>[o(_,{name:t.Sift,size:"2em",onClick:e[0]||(e[0]=k(c=>t.showTop=!t.showTop,["stop","prevent"]))},null,8,["name"])]),_:1}),o(d,{show:t.showTop,"onUpdate:show":e[4]||(e[4]=c=>t.showTop=c),position:"top"},{default:l(()=>[o(p,{value:t.search.sectionId,"onUpdate:value":e[1]||(e[1]=c=>t.search.sectionId=c),"dict-name":n.$DICT_CODE.project_section,placeholder:"选择所属标段"},null,8,["value","dict-name"]),o(p,{value:t.search.inspectionType,"onUpdate:value":e[2]||(e[2]=c=>t.search.inspectionType=c),"dict-name":n.$DICT_CODE.safe_inspection_type,placeholder:"选择检查类型"},null,8,["value","dict-name"]),o(p,{value:t.search.inspectionResult,"onUpdate:value":e[3]||(e[3]=c=>t.search.inspectionResult=c),"dict-name":n.$DICT_CODE.safe_inspection_result,placeholder:"选择检查结果"},null,8,["value","dict-name"]),s("div",ne,[o(u,{round:"",type:"primary",plain:"",onClick:k(a.handleQuery,["stop","prevent"])},{default:l(()=>e[6]||(e[6]=[g("查询")])),_:1,__:[6]},8,["onClick"]),o(u,{round:"",plain:"",onClick:k(a.handleResetting,["stop","prevent"])},{default:l(()=>e[7]||(e[7]=[g("重置")])),_:1,__:[7]},8,["onClick"])])]),_:1},8,["show"]),s("div",{class:N(["view-height",{"no-tabbar":n.envFeishu}])},[o(I,{ref:"SafetyCheckRef",search:t.search},null,8,["search"])],2),o(u,{type:"primary",size:"normal",style:{width:"100%"},onClick:e[5]||(e[5]=c=>a.handleAdd())},{default:l(()=>e[8]||(e[8]=[g("新增检查")])),_:1,__:[8]})],64)}const he=S(se,[["render",ie],["__scopeId","data-v-57f36d55"]]);export{he as default};
