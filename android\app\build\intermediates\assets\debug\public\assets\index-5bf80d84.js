import{O as F,r as _,Q as o,R as s,S as u,k as t,u as V,V as a,F as d,W as w,U as i,X as C,Y as m,a2 as M,B as b}from"./verder-361ae6c7.js";import{s as D,_ as P}from"./stationButtonGroup-08a296f6.js";import{l as G}from"./lodash-d061b41e.js";import{g as H}from"./api-e44c60fe.js";import{_ as L}from"./index-4829f8e2.js";import"./api-d1b3ba40.js";import"./vant-91101745.js";const O={class:"title van-ellipsis"},Q={class:"subtitle van-ellipsis"},W={key:1,class:"text-center",style:{margin:"24% 0"}},X={__name:"index",setup(Y){const B=F(),f=_(""),x=_(),v=_([]),g=_(!1);function y(){H({key:f.value,damId:x.value,envType:1}).then(n=>{n.forEach(e=>{e.projects.content.forEach(c=>{c.lastRecTime=c.lastRecTime?c.lastRecTime.split(" ")[0]:""})}),v.value=n}).finally(n=>{g.value=!1})}function N(n){x.value=n,y()}function R(n){B.push("/SafetyMonitoring/".concat(n,"/input"))}const h=G.debounce(function(n){y()},500);return(n,e)=>{const c=o("Navbar"),E=o("van-search"),p=o("van-col"),I=o("van-button"),k=o("van-row"),T=o("van-cell"),S=o("van-cell-group"),U=o("van-list"),$=o("van-pull-refresh");return s(),u(d,null,[t(c,{back:!n.envFeishu,backEvent:()=>n.$router.replace({name:"Home"})},null,8,["back","backEvent"]),t(E,{modelValue:f.value,"onUpdate:modelValue":[e[0]||(e[0]=r=>f.value=r),V(h)],placeholder:"请输入搜索内容",autocomplete:!1,class:"border-bottom",onClear:V(h)},null,8,["modelValue","onUpdate:modelValue","onClear"]),t(D,{onChange:N,class:"border-bottom"}),t($,{modelValue:g.value,"onUpdate:modelValue":e[1]||(e[1]=r=>g.value=r),onRefresh:y,class:"flex-grow-1 ofy-auto no-scrollbar"},{default:a(()=>[v.value.length>0?(s(),u(d,{key:0},[t(U,null,{default:a(()=>[(s(!0),u(d,null,w(v.value,r=>(s(),C(S,{key:r.damId},{default:a(()=>[(s(!0),u(d,null,w(r.projects.content,l=>(s(),C(T,{key:l.id,to:"/SafetyMonitoring/".concat(l.id),class:"listItem"},{title:a(()=>[t(k,{gutter:8,type:"flex",justify:"space-between",align:"center",class:"flex-nowrap top"},{default:a(()=>[t(p,{class:"titleBox"},{default:a(()=>[i("div",O,m(l.projectName),1),i("p",Q," 仪器类型："+m(l.instrName),1)]),_:2},1024),t(p,{class:"flex-shrink-0 button"},{default:a(()=>[t(I,{plain:"",hairline:"",type:"primary",class:"input-button",onClick:M(q=>R(l.id),["stop"])},{default:a(()=>e[2]||(e[2]=[b("录入")])),_:2,__:[2]},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),label:a(()=>[t(k,{gutter:8,type:"flex",justify:"space-between",align:"center",class:"bottom border-top-dashed",style:{"line-height":"35px"}},{default:a(()=>[t(p,{class:"time"},{default:a(()=>[e[3]||(e[3]=i("i",{class:"iconfont icon-ic_calendar text-gray-4-1 text-16"},null,-1)),b(" 最近测值："+m(l.lastRecTime||"无"),1)]),_:2,__:[3]},1024),t(p,{class:"count"},{default:a(()=>[b(" 测点数: "+m(l.codeCount),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["to"]))),128))]),_:2},1024))),128))]),_:1}),e[4]||(e[4]=i("p",{class:"text-gray-6-1 text-center py-3 mb-0"},"没有更多了",-1))],64)):(s(),u("div",W,e[5]||(e[5]=[i("img",{src:P,width:"87",height:"113",alt:"暂无数据",class:"mb-6"},null,-1),i("p",{class:"text-gray-6-1",style:{"margin-top":"0"}},"暂无环境测点",-1)])))]),_:1},8,["modelValue"])],64)}}},te=L(X,[["__scopeId","data-v-4e22043c"]]);export{te as default};
