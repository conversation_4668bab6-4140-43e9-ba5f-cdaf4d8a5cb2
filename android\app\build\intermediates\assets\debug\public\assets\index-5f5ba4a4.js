import{F as g,D as T}from"./index-1be3ad72.js";import{_ as U}from"./index-4829f8e2.js";import{Q as u,R as P,X as L,V as p,k as s,U as t,Y as a,B as v}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const B={name:"CB37",components:{FormTemplate:g,DocumentPart:T},emits:[],props:{},setup(n,{attrs:e,slots:c,emit:f}){},data(){return{detailTable:[],attachmentDesc:"1、\n2、"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:n,detailParamList:e}){},onBeforeSubmit({formData:n,detailParamList:e},c){return new Promise((f,m)=>{try{if(c=="submit"&&!n.check1&&!n.check2)return this.$showNotify({type:"danger",message:"请选择：通知/指示",duration:3*1e3}),m(!1),!1;f()}catch(i){m(i)}})},changeCheck1(n){n&&(this.$refs.FormTemplate.formData.check2=!1)},changeCheck2(n){n&&(this.$refs.FormTemplate.formData.check1=!1)}}},F={class:"one-line"},O={class:"form-info"},A={class:"form-info"},z={class:"form-info"},D={class:"form-info"},W={class:"form-info"},q={class:"check-wp",style:{"text-indent":"0"}},I={class:"attachment-desc"},E={class:"comment-wp"},Q={class:"textarea-wp"},R={class:"comment-wp"},X={class:"textarea-wp"},Y={class:"footer-input"},$={class:"form-info"},G={class:"form-info"},H={class:"form-info"};function J(n,e,c,f,m,i){const h=u("van-checkbox"),b=u("van-field"),V=u("DocumentPart"),C=u("FormTemplate");return P(),L(C,{ref:"FormTemplate",nature:"回复","on-after-init":i.onAfterInit,"on-before-submit":i.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!1,attachmentDesc:m.attachmentDesc},{default:p(({formData:o,formTable:y,baseObj:r,uploadAccept:w,taskStart:d,taskComment2:_,taskComment3:k,taskComment4:x,taskComment5:N})=>[s(V,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:o.constructionDeptName,deptOptions:r.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!d},{default:p(()=>[t("div",F,[e[3]||(e[3]=t("span",{style:{"margin-left":"2em"}},"我方于",-1)),t("span",O,a(o.field1),1),e[4]||(e[4]=t("span",null,"年",-1)),t("span",A,a(o.field2),1),e[5]||(e[5]=t("span",null,"月",-1)),t("span",z,a(o.field3),1),e[6]||(e[6]=t("span",null,"日收到",-1)),t("span",D,a(o.field4),1),e[7]||(e[7]=t("span",null,"(监理文件文号)关于",-1)),t("span",W,a(o.field5),1),e[8]||(e[8]=t("span",null,"的",-1)),t("div",q,[s(h,{modelValue:o.check1,"onUpdate:modelValue":l=>o.check1=l,shape:"square",disabled:!d,onChange:i.changeCheck1},{default:p(()=>e[0]||(e[0]=[v("通知")])),_:2,__:[0]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),e[2]||(e[2]=t("span",null," / ",-1)),s(h,{modelValue:o.check2,"onUpdate:modelValue":l=>o.check2=l,shape:"square",disabled:!d,onChange:i.changeCheck2},{default:p(()=>e[1]||(e[1]=[v("指示")])),_:2,__:[1]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[9]||(e[9]=t("span",null,"，回复如下：",-1))]),t("div",I,[e[10]||(e[10]=t("div",null,"附件：",-1)),s(b,{modelValue:o.attachmentDesc,"onUpdate:modelValue":l=>o.attachmentDesc=l,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),s(V,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:r.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!d},{default:p(()=>[t("div",E,[e[11]||(e[11]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",Q,[s(b,{modelValue:o.comment2,"onUpdate:modelValue":l=>o.comment2=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!_},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),s(V,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!d},{default:p(()=>[t("div",R,[e[12]||(e[12]=t("div",null,"审核意见：",-1)),t("div",X,[s(b,{modelValue:o.comment3,"onUpdate:modelValue":l=>o.comment3=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!k},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:o,formTable:y,baseObj:r,uploadAccept:w,taskStart:d,taskComment2:_,taskComment3:k,taskComment4:x,taskComment5:N})=>[t("div",Y,[e[13]||(e[13]=t("span",null,"说明：1、本表一式",-1)),t("span",$,a(o.num1),1),e[14]||(e[14]=t("span",null,"份，由承包人填写，监理机构审核后，承包人",-1)),t("span",G,a(o.num2),1),e[15]||(e[15]=t("span",null,"份，监理机构",-1)),t("span",H,a(o.num3),1),e[16]||(e[16]=t("span",null,"份。",-1))]),e[17]||(e[17]=t("div",{class:"footer-input"},[t("span",null,"2、本表主要用于承包人对监理机构发出的监理通知、指示的回复。")],-1))]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const me=U(B,[["render",J]]);export{me as default};
