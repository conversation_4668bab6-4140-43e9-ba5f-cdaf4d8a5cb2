import{F as g,D as F}from"./index-1be3ad72.js";import{_ as N}from"./index-4829f8e2.js";import{Q as u,R as U,X as $,V as p,k as d,U as s,Y as n,B as P}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const Y={name:"CB12",components:{FormTemplate:g,DocumentPart:F},emits:[],props:{},setup(l,{attrs:e,slots:c,emit:h}){},data(){return{detailTable:[],showCalendar:!1,attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:l,detailParamList:e}){},onBeforeSubmit({formData:l,detailParamList:e,taskComment3:c},h){return new Promise((m,a)=>{try{if(h==="submit"&&c){if(!l.check1&&!l.check2)return this.$showNotify({type:"danger",message:"请选择测量意见!",duration:3*1e3}),a(!1),!1;if(l.check1&&(!l.field10||!l.field11||!l.field12))return this.$showNotify({type:"danger",message:"请填写测量时间!",duration:3*1e3}),a(!1),!1}m()}catch(r){a(r)}})},changeCheck1(l){l&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.taskComment3&&(this.showCalendar=!0))},changeCheck2(l){l&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.field10="",this.$refs.FormTemplate.formData.field11="",this.$refs.FormTemplate.formData.field12="",this.$nextTick(()=>{var e;(e=this.$refs.FormTemplate.$refs.form)==null||e.resetValidation()}))},onConfirmCalendar(l){const e=this.$dayjs(l).format("YYYY-MM-DD");this.$refs.FormTemplate.formData.field10=this.$dayjs(e).format("YYYY"),this.$refs.FormTemplate.formData.field11=this.$dayjs(e).format("MM"),this.$refs.FormTemplate.formData.field12=this.$dayjs(e).format("DD"),this.showCalendar=!1}}},L={class:"comment-wp"},B={class:"textarea-wp"},O={class:"comment-wp"},z={class:"textarea-wp"},A={class:"comment-wp"},M={class:"textarea-wp"},I={class:"comment-wp"},W={class:"one-line"},q={class:"form-info"},j={class:"form-info"},E={class:"form-info"},Q={class:"form-info"},R={class:"form-info"},X={class:"form-info"},D={class:"comment-wp"},G={class:"textarea-wp"},H={class:"comment-wp"},J={class:"check-wp"},K={class:"one-line"},Z={class:"input-field",style:{padding:"0 4px"}},S={class:"input-field",style:{padding:"0 4px"}},ee={class:"input-field",style:{padding:"0 4px"}},se={class:"check-wp"},te={class:"footer-input"},le={class:"form-info"},oe={class:"form-info"},ne={class:"form-info"},de={class:"form-info"};function ae(l,e,c,h,m,a){const r=u("van-field"),_=u("DocumentPart"),v=u("van-checkbox"),w=u("van-calendar"),C=u("FormTemplate");return U(),$(C,{ref:"FormTemplate",nature:"联测","on-after-init":a.onAfterInit,"on-before-submit":a.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:m.attachmentDesc},{default:p(({formData:t,formTable:y,baseObj:f,uploadAccept:k,taskStart:i,taskComment2:V,taskComment3:b,taskComment4:x,taskComment5:T})=>[d(_,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:t.constructionDeptName,deptOptions:f.constructionDeptName,personLabel:"技术负责人：",labelWidth:"10em",disabled:!i},{default:p(()=>[e[11]||(e[11]=s("div",{class:"comment-wp"},[s("div",{class:"one-line"},[s("span",{style:{"margin-left":"2em"}}," 根据合同约定和工程进度，我方拟进行工程测量工作，请贵方派员参加。 ")])],-1)),s("div",L,[e[1]||(e[1]=s("div",null,"施测工程部位：",-1)),s("div",B,[d(r,{modelValue:t.field1,"onUpdate:modelValue":o=>t.field1=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),s("div",O,[e[2]||(e[2]=s("div",null,"项目工作内容：",-1)),s("div",z,[d(r,{modelValue:t.field2,"onUpdate:modelValue":o=>t.field2=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),s("div",A,[e[3]||(e[3]=s("div",null,"任务要点：",-1)),s("div",M,[d(r,{modelValue:t.field3,"onUpdate:modelValue":o=>t.field3=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),s("div",I,[s("div",W,[e[4]||(e[4]=s("span",null,"施测计划时间：",-1)),s("span",q,n(t.field4),1),e[5]||(e[5]=s("span",null,"年",-1)),s("span",j,n(t.field5),1),e[6]||(e[6]=s("span",null,"月",-1)),s("span",E,n(t.field6),1),e[7]||(e[7]=s("span",null,"日至",-1)),s("span",Q,n(t.field7),1),e[8]||(e[8]=s("span",null,"年",-1)),s("span",R,n(t.field8),1),e[9]||(e[9]=s("span",null,"月",-1)),s("span",X,n(t.field9),1),e[10]||(e[10]=s("span",null,"日",-1))])])]),_:2,__:[11]},1032,["deptValue","deptOptions","disabled"]),d(_,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:t.epcDeptName,deptOptions:f.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!i},{default:p(()=>[s("div",D,[e[12]||(e[12]=s("div",null,"EPC总承包项目部意见：",-1)),s("div",G,[d(r,{modelValue:t.comment2,"onUpdate:modelValue":o=>t.comment2=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!V},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),d(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:t.supervisionDeptName,deptOptions:f.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!i},{default:p(()=>[s("div",H,[s("div",null,[s("div",J,[d(v,{modelValue:t.check1,"onUpdate:modelValue":o=>t.check1=o,shape:"square",disabled:!b,onChange:a.changeCheck1},{default:p(()=>[s("div",K,[e[13]||(e[13]=s("span",null,"拟于",-1)),s("span",Z,n(t.field10),1),e[14]||(e[14]=s("span",null,"年",-1)),s("span",S,n(t.field11),1),e[15]||(e[15]=s("span",null,"月",-1)),s("span",ee,n(t.field12),1),e[16]||(e[16]=s("span",null,"日派监理人员参加测量",-1))])]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),s("div",null,[s("div",se,[d(v,{modelValue:t.check2,"onUpdate:modelValue":o=>t.check2=o,shape:"square",disabled:!b,onChange:a.changeCheck2},{default:p(()=>e[17]||(e[17]=[P(" 不派人参加联合测量，你方测量后将测量结果报给我方审核。 ")])),_:2,__:[17]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),d(w,{show:m.showCalendar,"onUpdate:show":e[0]||(e[0]=o=>m.showCalendar=o),onConfirm:a.onConfirmCalendar},null,8,["show","onConfirm"])]),footer:p(({formData:t,formTable:y,baseObj:f,uploadAccept:k,taskStart:i,taskComment2:V,taskComment3:b,taskComment4:x,taskComment5:T})=>[s("div",te,[e[18]||(e[18]=s("span",null,"说明：本表一式",-1)),s("span",le,n(t.num1),1),e[19]||(e[19]=s("span",null,"份，由承包人填写，监理机构签署后，发包人",-1)),s("span",oe,n(t.num2),1),e[20]||(e[20]=s("span",null,"份，监理机构",-1)),s("span",ne,n(t.num3),1),e[21]||(e[21]=s("span",null,"份，承包人",-1)),s("span",de,n(t.num4),1),e[22]||(e[22]=s("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ke=N(Y,[["render",ae],["__scopeId","data-v-80593692"]]);export{ke as default};
