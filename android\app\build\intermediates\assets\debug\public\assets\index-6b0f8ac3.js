import{F as x,D as y}from"./index-a831f9da.js";import{_ as k}from"./index-4829f8e2.js";import{Q as i,R as T,X as C,V as d,k as p,U as e}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const P={name:"JL39",components:{FormTemplate:x,DocumentPart:y},emits:[],props:{},setup(r,{attrs:t,slots:u,emit:c}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:r,detailParamList:t}){},onBeforeSubmit({formData:r,detailParamList:t,taskStart:u},c){return new Promise((a,s)=>{try{a()}catch(n){s(n)}})}}},B={class:"comment-wp"},F={class:"textarea-wp"},L={class:"attachment-desc"};function N(r,t,u,c,a,s){const n=i("van-field"),_=i("DocumentPart"),b=i("FormTemplate");return T(),C(b,{ref:"FormTemplate",nature:"联系","on-after-init":s.onAfterInit,"on-before-submit":s.onBeforeSubmit,"detail-table":a.detailTable,"is-show-confirm1":!1,"is-show-confirm2":!0,"is-show-confirm3":!0,"show-target":!0,attachmentDesc:a.attachmentDesc},{default:d(({formData:o,formTable:v,baseObj:f,uploadAccept:h,taskStart:l,taskComment2:V,taskComment3:w,taskComment4:D})=>[p(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:f.supervisionDeptName,personLabel:"总监理工程师/监理工程师：",labelWidth:"10em",disabled:!l},{default:d(()=>[e("div",B,[t[0]||(t[0]=e("div",null,"事由：",-1)),e("div",F,[p(n,{modelValue:o.field1,"onUpdate:modelValue":m=>o.field1=m,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",L,[t[1]||(t[1]=e("div",null,"附件：",-1)),p(n,{modelValue:o.attachmentDesc,"onUpdate:modelValue":m=>o.attachmentDesc=m,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),t[2]||(t[2]=e("div",{class:"part-div"},[e("div",{style:{height:"30px"}}),e("div",{class:"part-sign"},[e("div",null,[e("span",null,"被联系单位签收人：")]),e("div",null,[e("span",null,"日期：")])])],-1))]),footer:d(({formData:o,formTable:v,baseObj:f,uploadAccept:h,taskStart:l,taskComment2:V,taskComment3:w,taskComment4:D})=>t[3]||(t[3]=[e("div",{class:"footer-input"},[e("div",null,"说明：本表用于监理机构与监理工作有关单位的联系，监理单位、被联系单位各1份。")],-1)])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const G=k(P,[["render",N],["__scopeId","data-v-9aebf8ae"]]);export{G as default};
