import{F as D}from"./index-8d635ba7.js";import{D as h,a as g}from"./DesignDispatchDrawingTable-897743ce.js";import{_ as b}from"./index-4829f8e2.js";import{Q as l,R as F,X as _,V as s,k as r}from"./verder-361ae6c7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";const v={name:"TechnologyDesignOuterAuditNew",components:{FlowForm:D,DesignDispatchCommonTopNew:h,DesignDispatchDrawingTable:g},data(){var a,t;return{type:((a=this.$route.query)==null?void 0:a.type)||"",taskKey:((t=this.$route.query)==null?void 0:t.taskKey)||"",modelKey:"technology_design_outer_audit",entityName:"TechnologyCommonFile",detailEntityName:"TechnologyDesignEPCAudit",detailParamList:[],detailEntityNameList:["TechnologyDesignEPCAudit","TechnologyDesignEPCAuditDrawing"],service:{query:"/cybereng-technology/design/EPCAudit/query",submit:"/cybereng-technology/design/form/commit"},formKey:"TechnologyDesignOuterAuditNew",formData:{formKey:"TechnologyDesignOuterAuditNew",portalId:"",subProjectName:"",subProjectId:null,modelType:"",sendingName:"",sendingCode:"",sendingType:"",sendingTypeCode:"",contentDescription:"",profession:"",relavancePartName:"",relavancePart:"",leadDesigner:"",leadDesignerFullname:"",userTelephoneNum:"",fileNum:"",fileName:"",isconfirm1:!0,approverUsername1:"",approverFullname1:"",approverUnit1:"",time1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",time2:"",duplicateUsername1:"",duplicateFullname1:"",duplicateUsername2:"",duplicateFullname2:"",relatedBpmId:""},formDetailTable:[],detailId:""}},computed:{},mounted(){this.initForm()},methods:{async initForm(){this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,a=>{const{detailParamList:t=[],entityObject:i}=a;this.detailParamList=t,this.detailId=t[0].detailEntityArray[0].id,this.formData={...this.formData,...t[0].detailEntityArray[0],...i,notifyMethods:i.notifyMethod?i.notifyMethod.split(","):[]},this.formDetailTable=t[1].detailEntityArray})})},async onDraft(){try{const a={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,a)}catch(a){console.log(a)}},async onSubmit(){try{if(["fileName","relavancePartName","profession","leadDesignerFullname","subProjectName","approverFullname1"].some(d=>!this.formData[d]))return this.$showToast({message:"请登录数智建管系统完善表单"});if(!this.formDetailTable.length)return this.$showToast({message:"请登录数智建管系统完善表单"});await this.$refs.form.validate(),this.setTime();const i={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,i)}catch(a){console.log(a)}},setTime(){switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":{this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_2":{this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}}},async afterSubmit(){}}};function w(a,t,i,d,e,m){const u=l("design-dispatch-common-top-new"),p=l("design-dispatch-drawing-table"),n=l("van-field"),f=l("van-cell-group"),y=l("van-form"),c=l("FlowForm");return F(),_(c,{ref:"FlowForm","model-key":e.modelKey,"form-key":e.formKey,"entity-name":e.entityName,"detail-param-list":e.detailParamList,"detail-entity-name-list":e.detailEntityNameList,onDraftClick:m.onDraft,onSubmitClick:m.onSubmit,onAfterSubmit:m.afterSubmit},{default:s(()=>[r(y,{ref:"form","label-width":"8em","input-align":"right","error-message-align":"right"},{default:s(()=>[r(u,{"form-data":e.formData,type:e.type,readonly:""},null,8,["form-data","type"]),r(p,{"form-detail-table":e.formDetailTable,ref:"detailForm","form-data":e.formData,type:e.type},null,8,["form-detail-table","form-data","type"]),r(f,{border:!1},{default:s(()=>[r(n,{modelValue:e.formData.userFullname,"onUpdate:modelValue":t[0]||(t[0]=o=>e.formData.userFullname=o),label:"发起人",readonly:""},null,8,["modelValue"]),r(n,{modelValue:e.formData.prjDepName,"onUpdate:modelValue":t[1]||(t[1]=o=>e.formData.prjDepName=o),label:"发起人部门",readonly:""},null,8,["modelValue"]),r(n,{modelValue:e.formData.approverFullname1,"onUpdate:modelValue":t[2]||(t[2]=o=>e.formData.approverFullname1=o),label:"审查单位接收人",readonly:"",required:""},null,8,["modelValue"]),r(n,{modelValue:e.formData.approverUnit1,"onUpdate:modelValue":t[3]||(t[3]=o=>e.formData.approverUnit1=o),label:"审查单位接收人部门","label-width":"9em",readonly:""},null,8,["modelValue"]),r(n,{modelValue:e.formData.duplicateFullname1,"onUpdate:modelValue":t[4]||(t[4]=o=>e.formData.duplicateFullname1=o),label:"抄送至",readonly:""},null,8,["modelValue"])]),_:1})]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}const q=b(v,[["render",w]]);export{q as default};
