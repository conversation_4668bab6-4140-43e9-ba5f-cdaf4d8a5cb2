import{l as D}from"./lodash-d061b41e.js";import{h as p}from"./dateFormat-1fb6392c.js";import{d as P,f as B}from"./filterDropdownItem-8764eaeb.js";import{p as Y,c as S}from"./codeValueHotTable-b9cf8ce9.js";import{d as M,b as O}from"./api-e44c60fe.js";import{Q as o,R as l,S as u,k as s,V as c,U as _,a2 as R,F as f,W as h,B as E,X as v,t as H,v as z}from"./verder-361ae6c7.js";import{_ as V}from"./index-4829f8e2.js";import"./vant-91101745.js";const A={components:{dateRangeDropdownItem:P,filterDropdownItem:B,phoneOrientButton:Y,codeValueHotTable:S},data(){return{project:{},codeOptions:[],paramList:[{name:"采集类型",fieldName:"codeAutoList",options:[{name:"人工",value:0},{name:"自动",value:1}],values:[0,1]},{name:"测值状态",fieldName:"valueStatusList",options:[{name:"正常",value:1},{name:"异常",value:2},{name:"错误",value:3}],values:[1,2,3]},{name:"审核状态",fieldName:"valueCheckList",options:[{name:"未审核",value:0},{name:"通过",value:1},{name:"未通过",value:2}],values:[0,1,2]}],hotTableParams:{},params:{codeId:"",codeAutoList:[0,1],valueStatusList:[1,2,3],valueCheckList:[0,1,2],valueTypeList:[0,1,2],startTime:"",endTime:""}}},methods:{goBack(){this.$router.push("/SafetyMonitoring")},codeConfirm(e){this.params.codeId=e,this.setHotTableParams()},dateRangeLoad({startDate:e,endDate:t}){this.params.startTime=p(e.join("/")).format("YYYY-MM-DD [00:00:00]"),this.params.endTime=p(t.join("/")).format("YYYY-MM-DD [23:59:59]")},dateRangeConfirm(e){this.dateRangeLoad(e),this.setHotTableParams()},selectChange(e,t){const i=this.paramList.find(n=>n.fieldName===e).values,d=i.indexOf(t);d>-1?i.splice(d,1):i.push(t)},isSelected(e,t){return this.paramList.find(i=>i.fieldName===e).values.indexOf(t)>-1},moreDropdownItemConfirm(){this.paramList.forEach(({fieldName:e,values:t})=>{this.params[e]=D.cloneDeep(t)}),this.$refs.moreDropdownItem.toggle(!1),this.setHotTableParams()},toProcessLine(){const{id:e}=this.$route.params,t=this.$refs.orientButton.getOrientation();this.$router.push("/SafetyMonitoring/".concat(e,"/processLine?orientationType=").concat(t))},async getCodes(){const e=await M(this.project);this.codeOptions=Array.isArray(e)?e.map(t=>({name:t.codeName,value:t.codeId})):[]},setHotTableParams(){const{id:e,projectInstrId:t,projectDamId:i,projectSort:d}=this.project,n=p().format("YYYY-MM-DD");this.hotTableParams={dayStart:"".concat(n," 00:00:00"),dayEnd:"".concat(n," 23:59:59"),projectId:e,instrId:t,damId:i,sort:d,...this.params}},async getProject(){const{id:e}=this.$route.params,t=await O(e);this.project=t,await this.getCodes(),this.setHotTableParams()}},mounted(){this.getProject()}},F={class:"monitor-container"},Q={class:"py-4 px-8"};function U(e,t,i,d,n,a){const g=o("Navbar"),b=o("date-range-dropdown-item"),C=o("filter-dropdown-item"),w=o("van-icon"),y=o("van-cell"),L=o("van-cell-group"),k=o("van-button"),x=o("van-dropdown-item"),I=o("van-dropdown-menu"),T=o("code-value-hot-table"),j=o("phone-orient-button");return l(),u("div",F,[s(g,{back:!e.envFeishu,backEvent:a.goBack,title:n.project.projectName},{right:c(()=>[_("span",{style:{height:"42px","line-height":"42px",color:"white","font-size":"17px"},onClick:t[0]||(t[0]=R((...r)=>a.toProcessLine&&a.toProcessLine(...r),["stop"]))},"过程线")]),_:1},8,["back","backEvent","title"]),s(I,{"z-index":"181","active-color":"#1890ff"},{default:c(()=>[s(b,{onLoad:a.dateRangeLoad,onConfirm:a.dateRangeConfirm},null,8,["onLoad","onConfirm"]),s(C,{options:n.codeOptions,"default-title":"测点",onConfirm:a.codeConfirm},null,8,["options","onConfirm"]),s(x,{ref:"moreDropdownItem",title:"高级过滤"},{default:c(()=>[(l(!0),u(f,null,h(n.paramList,r=>(l(),v(L,{key:r.fieldName,title:r.name},{default:c(()=>[(l(!0),u(f,null,h(r.options,({name:N,value:m})=>(l(),v(y,{key:m,title:N,"title-class":{"text-blue":a.isSelected(r.fieldName,m)},size:"large",center:"",onClick:W=>a.selectChange(r.fieldName,m)},{"right-icon":c(()=>[H(s(w,{name:"success",size:"20px",color:"#1989fa"},null,512),[[z,a.isSelected(r.fieldName,m)]])]),_:2},1032,["title","title-class","onClick"]))),128))]),_:2},1032,["title"]))),128)),_("div",Q,[s(k,{type:"info",block:"",onClick:a.moreDropdownItemConfirm},{default:c(()=>t[1]||(t[1]=[E("确定")])),_:1,__:[1]},8,["onClick"])])]),_:1},512)]),_:1}),s(T,{lazy:!0,params:n.hotTableParams,height:"800"},null,8,["params"]),s(j,{ref:"orientButton"},null,512)])}const te=V(A,[["render",U],["__scopeId","data-v-26772333"]]);export{te as default};
