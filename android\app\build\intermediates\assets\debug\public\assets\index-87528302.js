import{F as P,D as L}from"./index-1be3ad72.js";import{_ as T}from"./index-4829f8e2.js";import{Q as f,R as b,X as B,V as d,k as s,U as e,Y as l,S as h,W as F,F as U}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const O={name:"CB11",components:{FormTemplate:P,DocumentPart:L},emits:[],props:{},setup(u,{attrs:t,slots:_,emit:V}){},data(){return{detailTable:[{},{},{},{}],attachmentDesc:"1、放样测量成果。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:u,detailParamList:t}){},onBeforeSubmit({formData:u,detailParamList:t,taskComment3:_},V){return new Promise((m,i)=>{try{m()}catch(a){i(a)}})}}},A={class:"one-line"},W={class:"form-info"},g={class:"form-table"},z={class:"attachment-desc"},E={class:"comment-wp"},I={class:"textarea-wp"},S={class:"comment-wp"},Q={class:"textarea-wp"},R={class:"footer-input"},X={class:"form-info"},Y={class:"form-info"},q={class:"form-info"},G={class:"form-info"};function H(u,t,_,V,m,i){const a=f("van-field"),c=f("DocumentPart"),C=f("FormTemplate");return b(),B(C,{ref:"FormTemplate",nature:"放样","on-after-init":i.onAfterInit,"on-before-submit":i.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:m.attachmentDesc},{default:d(({formData:o,formTable:v,baseObj:r,uploadAccept:N,taskStart:p,taskComment2:w,taskComment3:y,taskComment4:k,taskComment5:x})=>[s(c,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:o.constructionDeptName,deptOptions:r.constructionDeptName,personLabel:"技术负责人：",labelWidth:"10em",disabled:!p},{default:d(()=>[e("div",A,[t[0]||(t[0]=e("span",{style:{"margin-left":"2em"}}," 根据合同要求，我方已完成 ",-1)),e("span",W,l(o.field1),1),t[1]||(t[1]=e("span",null,"的施工放样工作，",-1)),t[2]||(t[2]=e("span",null,"经自检合格，请贵方审核。",-1))]),e("div",g,[e("table",null,[t[3]||(t[3]=e("thead",null,[e("tr",null,[e("th",{colspan:"1",rowspan:"1"},"序号或位置"),e("th",{colspan:"1",rowspan:"1"},"工程或部位名称"),e("th",{colspan:"1",rowspan:"1"},"放样内容"),e("th",{colspan:"1",rowspan:"1"},"备注")])],-1)),e("tbody",null,[(b(!0),h(U,null,F(v||[],(n,D)=>(b(),h("tr",{key:D},[e("td",null,l(n.field1),1),e("td",null,l(n.field2),1),e("td",null,l(n.field3),1),e("td",null,l(n.field4),1)]))),128))])])]),e("div",z,[t[4]||(t[4]=e("div",null,"附件：",-1)),s(a,{modelValue:o.attachmentDesc,"onUpdate:modelValue":n=>o.attachmentDesc=n,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),s(c,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:r.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!p},{default:d(()=>[e("div",E,[t[5]||(t[5]=e("div",null,"EPC总承包项目部意见：",-1)),e("div",I,[s(a,{modelValue:o.comment2,"onUpdate:modelValue":n=>o.comment2=n,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!w},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),s(c,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!p},{default:d(()=>[e("div",S,[t[6]||(t[6]=e("div",null,"监理机构审核意见：",-1)),e("div",Q,[s(a,{modelValue:o.comment3,"onUpdate:modelValue":n=>o.comment3=n,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!y},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:d(({formData:o,formTable:v,baseObj:r,uploadAccept:N,taskStart:p,taskComment2:w,taskComment3:y,taskComment4:k,taskComment5:x})=>[e("div",R,[t[7]||(t[7]=e("span",null,"说明：本表一式",-1)),e("span",X,l(o.num1),1),t[8]||(t[8]=e("span",null,"份，由承包人填写，监理机构审核后，发包人",-1)),e("span",Y,l(o.num2),1),t[9]||(t[9]=e("span",null,"份，监理机构",-1)),e("span",q,l(o.num3),1),t[10]||(t[10]=e("span",null,"份，承包人",-1)),e("span",G,l(o.num4),1),t[11]||(t[11]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const me=T(O,[["render",H]]);export{me as default};
