import{F as c,D as C}from"./index-a831f9da.js";import{_ as P}from"./index-4829f8e2.js";import{Q as f,R as L,X as V,V as i,k as b,U as e,Y as n}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const w={name:"JL01",components:{FormTemplate:c,DocumentPart:C},emits:[],props:{},setup(l,{attrs:t,slots:r,emit:a}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:l,detailParamList:t}){},onBeforeSubmit({formData:l,detailParamList:t},r){return new Promise((a,o)=>{try{a()}catch(p){o(p)}})}}},x={class:"one-line"},y={class:"form-info"},B={class:"one-line"},F={class:"form-info"},A={class:"form-info"},O={class:"form-info"},S={class:"footer-input"},g={class:"form-info"},I={class:"form-info"},W={class:"form-info"},j={class:"form-info"};function J(l,t,r,a,o,p){const d=f("DocumentPart"),k=f("FormTemplate");return L(),V(k,{ref:"FormTemplate",nature:"开工","on-after-init":p.onAfterInit,"on-before-submit":p.onBeforeSubmit,"detail-table":o.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:o.attachmentDesc},{default:i(({formData:s,formTable:_,baseObj:m,uploadAccept:v,taskStart:u,taskComment2:D,taskComment3:N,taskComment4:T})=>[b(d,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:m.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!u},{default:i(()=>[e("div",x,[t[0]||(t[0]=e("span",{style:{"padding-left":"2em"}},"根据施工合同约定，现签发",-1)),e("span",y,n(s.projectName),1),t[1]||(t[1]=e("span",null,"合同工程开工通知。",-1)),t[2]||(t[2]=e("span",null,"贵方在接到该通知后，",-1)),t[3]||(t[3]=e("span",null,"及时调遣人员和施工设备、材料进场，",-1)),t[4]||(t[4]=e("span",null,"完成各项施工准备工作，尽快提交《合同工程开工申请表》。",-1))]),e("div",B,[t[5]||(t[5]=e("span",{style:{"padding-left":"2em"}},"该合同工程开工日期为",-1)),e("span",F,n(s.field2),1),t[6]||(t[6]=e("span",null,"年",-1)),e("span",A,n(s.field3),1),t[7]||(t[7]=e("span",null,"月",-1)),e("span",O,n(s.field4),1),t[8]||(t[8]=e("span",null,"日。",-1))])]),_:2},1032,["deptValue","deptOptions","disabled"]),b(d,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:s.epcDeptName,deptOptions:m.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!0},{default:i(()=>t[9]||(t[9]=[e("div",{class:"comment-wp"},[e("div",null,"今已收到合同工程开工通知。")],-1)])),_:2,__:[9]},1032,["deptValue","deptOptions"])]),footer:i(({formData:s,formTable:_,baseObj:m,uploadAccept:v,taskStart:u,taskComment2:D,taskComment3:N,taskComment4:T})=>[e("div",S,[t[10]||(t[10]=e("span",null,"说明：本表一式",-1)),e("span",g,n(s.num1),1),t[11]||(t[11]=e("span",null,"份，由监理机构填写，承包人签收后，承包人",-1)),e("span",I,n(s.num2),1),t[12]||(t[12]=e("span",null,"份，监理机构",-1)),e("span",W,n(s.num3),1),t[13]||(t[13]=e("span",null,"份，发包人",-1)),e("span",j,n(s.num4),1),t[14]||(t[14]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const tt=P(w,[["render",J]]);export{tt as default};
