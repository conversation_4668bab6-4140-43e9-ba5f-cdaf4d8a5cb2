import{a as Hi,a8 as <PERSON>,Q as X,R as z,X as he,V as me,S as ie,F as Ee,W as At,U as be,Y as rt,Z as de,k as ne,B as Ft,a9 as qi,a1 as ge,y as Ln,a2 as Yi,t as pn,v as fn}from"./verder-361ae6c7.js";import{g as Gi,a as Xi,b as Ji,c as <PERSON>,e as Zi,s as ea,p as ta,f as ra,h as na,i as ia,u as aa,j as sa}from"./form-a8596e72.js";import{u as oa}from"./api-77f046f1.js";import{_ as yt,m as la,x as ua}from"./index-4829f8e2.js";import{U as ca}from"./index-fc22947f.js";import{f as pa}from"./formKeys-f34a583f.js";import{F as fa}from"./FormItemPerson-bd0e3e57.js";const tt=e=>{const t=Hi({loading:!1});return[(...n)=>new Promise(async(i,a)=>{if(!(t.loading||!e))try{t.loading=!0;const o=await e(...n);i(o)}catch(o){a(o)}finally{setTimeout(()=>{t.loading=!1},30)}}),Ki(t,"loading")]},dn={_Object:{submit:{type:"submit",text:"同意",code:"SUBMIT",customCode:"SUBMIT"},saveDraft:{type:"saveDraft",text:"暂存",code:"SAVEDRAFT",customCode:"SAVEDRAFT"},reject:{type:"reject",text:"退回",code:"REJECT",customCode:"REJECT",targetNode:"fawkes_custom_flow_start"},abandon:{type:"abandon",text:"废弃",code:"ABANDON",customCode:"ABANDON"},addCountersignee:{type:"addCountersignee",text:"加签",code:"ADDCOUNTERSIGNEE",customCode:"ADDCOUNTERSIGNEE"},circulate:{type:"circulate",text:"抄送",code:"CIRCULATE",customCode:"CIRCULATE"}},_Array:[{type:"submit",text:"同意",code:"SUBMIT",customCode:"SUBMIT"},{type:"saveDraft",text:"暂存",code:"SAVEDRAFT",customCode:"SAVEDRAFT"},{type:"reject",text:"退回",code:"REJECT",customCode:"REJECT",targetNode:"fawkes_custom_flow_start"},{type:"abandon",text:"废弃",code:"ABANDON",customCode:"ABANDON"},{type:"addCountersignee",text:"加签",code:"ADDCOUNTERSIGNEE",customCode:"ADDCOUNTERSIGNEE"},{type:"circulate",text:"抄送",code:"CIRCULATE",customCode:"CIRCULATE"}]};const da={name:"FlowTimeline",components:{},props:{bizId:{type:String,default:""},taskId:{type:String,default:""}},emits:[],setup(e,{attrs:t,slots:r,emit:n}){},data(){return{loading:!1,list:[],signUrl:"/api/sys-user/user/sign_token"}},computed:{},watch:{},created(){},mounted(){this.getProcessHistory()},methods:{async getProcessHistory(){try{this.loading=!0;const e=await Gi({bizId:this.bizId,taskId:this.taskId});this.list=(e||[]).reverse()}catch(e){console.log(e)}finally{this.loading=!1}}}},ha={class:"mt-0 mb-[5px]"},ma={key:0},ya={key:1};function ga(e,t,r,n,i,a){const o=X("van-step"),l=X("van-steps"),u=X("van-empty");return i.list&&i.list.length?(z(),he(l,{key:0,class:"flow-step",direction:"vertical","inactive-icon":"checked","active-icon":"clock",active:0},{default:me(()=>[(z(!0),ie(Ee,null,At([...i.list],(h,b)=>(z(),he(o,{key:b,class:"step"},{default:me(()=>[be("h4",ha,rt(h.taskName),1),be("div",null,"处理人: "+rt(h.assigneeName),1),be("div",null,"状态: "+rt(h.approveStateName),1),be("div",null,"发起时间:"+rt(h.createDate),1),h.approveDate?(z(),ie("div",ma,"处理时间:"+rt(h.approveDate),1)):de("",!0),h.comment?(z(),ie("div",ya,"处理意见:"+rt(h.comment),1)):de("",!0)]),_:2},1024))),128))]),_:1})):(z(),he(u,{key:1,description:"暂无流转"}))}const va=yt(da,[["render",ga],["__scopeId","data-v-ed60f061"]]);function Ke(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}const Ut=Object.prototype.toString,ba=Object.prototype.hasOwnProperty;function kr(e){return e===void 0}function Pt(e){return e!==void 0}function Bn(e){return e==null}function Fe(e){return Ut.call(e)==="[object Array]"}function dt(e){return Ut.call(e)==="[object Object]"}function nt(e){return Ut.call(e)==="[object Number]"}function Ye(e){const t=Ut.call(e);return t==="[object Function]"||t==="[object AsyncFunction]"||t==="[object GeneratorFunction]"||t==="[object AsyncGeneratorFunction]"||t==="[object Proxy]"}function We(e){return Ut.call(e)==="[object String]"}function mr(e,t){return!Bn(e)&&ba.call(e,t)}function je(e,t){const r=Ur(t);let n;return I(e,function(i,a){if(r(i,a))return n=i,!1}),n}function _a(e,t){const r=Ur(t);let n=Fe(e)?-1:void 0;return I(e,function(i,a){if(r(i,a))return n=a,!1}),n}function Rt(e,t){const r=Ur(t);let n=[];return I(e,function(i,a){r(i,a)&&n.push(i)}),n}function I(e,t){let r,n;if(kr(e))return;const i=Fe(e)?xa:wa;for(let a in e)if(mr(e,a)&&(r=e[a],n=t(r,i(a)),n===!1))return r}function Ct(e,t,r){return I(e,function(n,i){r=t(r,n,i)}),r}function Fn(e,t){return!!Ct(e,function(r,n,i){return r&&t(n,i)},!0)}function jn(e,t){return!!je(e,t)}function Ea(e,t){let r=[];return I(e,function(n,i){r.push(t(n,i))}),r}function hn(e){return function(t){return Fn(e,function(r,n){return t[n]===r})}}function Ur(e){return Ye(e)?e:t=>t===e}function wa(e){return e}function xa(e){return Number(e)}function Sa(e,t){let r,n,i,a;function o(y){let k=Date.now(),R=y?0:a+t-k;if(R>0)return l(R);e.apply(i,n),u()}function l(y){r=setTimeout(o,y)}function u(){r&&clearTimeout(r),r=a=n=i=void 0}function h(){r&&o(!0),u()}function b(...y){a=Date.now(),n=y,i=this,r||l(t)}return b.flush=h,b.cancel=u,b}function it(e,t){return e.bind(t)}function B(e,...t){return Object.assign(e,...t)}function Pa(e,t,r){let n=e;return I(t,function(i,a){if(typeof i!="number"&&typeof i!="string")throw new Error("illegal key type: "+typeof i+". Key should be of type number or string.");if(i==="constructor")throw new Error("illegal key: constructor");if(i==="__proto__")throw new Error("illegal key: __proto__");let o=t[a+1],l=n[i];Pt(o)&&Bn(l)&&(l=n[i]=isNaN(+o)?{}:[]),kr(o)?kr(r)?delete n[i]:n[i]=r:n=l}),e}function Aa(e,t){let r={},n=Object(e);return I(t,function(i){i in n&&(r[i]=e[i])}),r}function Ra(e,t){let r={},n=Object(e);return I(n,function(i,a){t.indexOf(a)===-1&&(r[a]=i)}),r}var Ca=1e3;function Je(e,t){var r=this;t=t||Ca,e.on(["render.shape","render.connection"],t,function(n,i){var a=n.type,o=i.element,l=i.gfx,u=i.attrs;if(r.canRender(o))return a==="render.shape"?r.drawShape(l,o,u):r.drawConnection(l,o,u)}),e.on(["render.getShapePath","render.getConnectionPath"],t,function(n,i){if(r.canRender(i))return n.type==="render.getShapePath"?r.getShapePath(i):r.getConnectionPath(i)})}Je.prototype.canRender=function(e){};Je.prototype.drawShape=function(e,t){};Je.prototype.drawConnection=function(e,t){};Je.prototype.getShapePath=function(e){};Je.prototype.getConnectionPath=function(e){};function F(e,t){var r=le(e);return r&&typeof r.$instanceOf=="function"&&r.$instanceOf(t)}function Ta(e,t){return jn(t,function(r){return F(e,r)})}function le(e){return e&&e.businessObject||e}function Tt(e){return e&&e.di}function Nr(e,t){return F(e,"bpmn:CallActivity")?!1:F(e,"bpmn:SubProcess")?(t=t||Tt(e),t&&F(t,"bpmndi:BPMNPlane")?!0:t&&!!t.isExpanded):F(e,"bpmn:Participant")?!!le(e).processRef:!0}function ka(e){return e&&!!le(e).triggeredByEvent}function Na(e){return dt(e)&&mr(e,"waypoints")}var ur={width:90,height:20},mn=15;function Ma(e){return F(e,"bpmn:Event")||F(e,"bpmn:Gateway")||F(e,"bpmn:DataStoreReference")||F(e,"bpmn:DataObjectReference")||F(e,"bpmn:DataInput")||F(e,"bpmn:DataOutput")||F(e,"bpmn:SequenceFlow")||F(e,"bpmn:MessageFlow")||F(e,"bpmn:Group")}function Da(e){var t=e.length/2-1,r=e[Math.floor(t)],n=e[Math.ceil(t+.01)],i=Ia(e),a=Math.atan((n.y-r.y)/(n.x-r.x)),o=i.x,l=i.y;return Math.abs(a)<Math.PI/2?l-=mn:o+=mn,{x:o,y:l}}function Ia(e){var t=e.length/2-1,r=e[Math.floor(t)],n=e[Math.ceil(t+.01)];return{x:r.x+(n.x-r.x)/2,y:r.y+(n.y-r.y)/2}}function Oa(e){return e.waypoints?Da(e.waypoints):F(e,"bpmn:Group")?{x:e.x+e.width/2,y:e.y+ur.height/2}:{x:e.x+e.width/2,y:e.y+e.height+ur.height/2}}function La(e,t){var r,n,i,a=e.label;return a&&a.bounds?(i=a.bounds,n={width:Math.max(ur.width,i.width),height:i.height},r={x:i.x+i.width/2,y:i.y+i.height/2}):(r=Oa(t),n=ur),B({x:r.x-n.width/2,y:r.y-n.height/2},n)}function Ba(e){if(F(e,"bpmn:FlowElement")||F(e,"bpmn:Participant")||F(e,"bpmn:Lane")||F(e,"bpmn:SequenceFlow")||F(e,"bpmn:MessageFlow")||F(e,"bpmn:DataInput")||F(e,"bpmn:DataOutput"))return"name";if(F(e,"bpmn:TextAnnotation"))return"text";if(F(e,"bpmn:Group"))return"categoryValueRef"}function Fa(e){var t=e.categoryValueRef;return t&&t.value||""}function cr(e){var t=e.businessObject,r=Ba(t);if(r)return r==="categoryValueRef"?Fa(t):t[r]||""}function ja(e,t){if(e.ownerDocument!==t.ownerDocument)try{return t.ownerDocument.importNode(e,!0)}catch(r){}return e}function Vn(e,t){return t.appendChild(ja(e,t))}function ce(e,t){return Vn(t,e),e}var Mr=2,$n={"alignment-baseline":1,"baseline-shift":1,clip:1,"clip-path":1,"clip-rule":1,color:1,"color-interpolation":1,"color-interpolation-filters":1,"color-profile":1,"color-rendering":1,cursor:1,direction:1,display:1,"dominant-baseline":1,"enable-background":1,fill:1,"fill-opacity":1,"fill-rule":1,filter:1,"flood-color":1,"flood-opacity":1,font:1,"font-family":1,"font-size":Mr,"font-size-adjust":1,"font-stretch":1,"font-style":1,"font-variant":1,"font-weight":1,"glyph-orientation-horizontal":1,"glyph-orientation-vertical":1,"image-rendering":1,kerning:1,"letter-spacing":1,"lighting-color":1,marker:1,"marker-end":1,"marker-mid":1,"marker-start":1,mask:1,opacity:1,overflow:1,"pointer-events":1,"shape-rendering":1,"stop-color":1,"stop-opacity":1,stroke:1,"stroke-dasharray":1,"stroke-dashoffset":1,"stroke-linecap":1,"stroke-linejoin":1,"stroke-miterlimit":1,"stroke-opacity":1,"stroke-width":Mr,"text-anchor":1,"text-decoration":1,"text-rendering":1,"unicode-bidi":1,visibility:1,"word-spacing":1,"writing-mode":1};function Va(e,t){return $n[t]?e.style[t]:e.getAttributeNS(null,t)}function Un(e,t,r){var n=t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),i=$n[n];i?(i===Mr&&typeof r=="number"&&(r=String(r)+"px"),e.style[n]=r):e.setAttributeNS(null,t,r)}function $a(e,t){var r=Object.keys(t),n,i;for(n=0,i;i=r[n];n++)Un(e,i,t[i])}function Z(e,t,r){if(typeof t=="string")if(r!==void 0)Un(e,t,r);else return Va(e,t);else $a(e,t);return e}const Ua=Object.prototype.toString;function Te(e){return new at(e)}function at(e){if(!e||!e.nodeType)throw new Error("A DOM element reference is required");this.el=e,this.list=e.classList}at.prototype.add=function(e){return this.list.add(e),this};at.prototype.remove=function(e){return Ua.call(e)=="[object RegExp]"?this.removeMatching(e):(this.list.remove(e),this)};at.prototype.removeMatching=function(e){const t=this.array();for(let r=0;r<t.length;r++)e.test(t[r])&&this.remove(t[r]);return this};at.prototype.toggle=function(e,t){return typeof t<"u"?t!==this.list.toggle(e,t)&&this.list.toggle(e):this.list.toggle(e),this};at.prototype.array=function(){return Array.from(this.list)};at.prototype.has=at.prototype.contains=function(e){return this.list.contains(e)};function zn(e){for(var t;t=e.firstChild;)e.removeChild(t);return e}var zr={svg:"http://www.w3.org/2000/svg"},yn='<svg xmlns="'+zr.svg+'"';function Wn(e){var t=!1;e.substring(0,4)==="<svg"?e.indexOf(zr.svg)===-1&&(e=yn+e.substring(4)):(e=yn+">"+e+"</svg>",t=!0);var r=za(e);if(!t)return r;for(var n=document.createDocumentFragment(),i=r.firstChild;i.firstChild;)n.appendChild(i.firstChild);return n}function za(e){var t;return t=new DOMParser,t.async=!1,t.parseFromString(e,"text/xml")}function te(e,t){var r;return e=e.trim(),e.charAt(0)==="<"?(r=Wn(e).firstChild,r=document.importNode(r,!0)):r=document.createElementNS(zr.svg,e),t&&Z(r,t),r}var Pr=null;function Dr(){return Pr===null&&(Pr=te("svg")),Pr}function gn(e,t){var r,n,i=Object.keys(t);for(r=0;n=i[r];r++)e[n]=t[n];return e}function Wa(e,t,r,n,i,a){var o=Dr().createSVGMatrix();switch(arguments.length){case 0:return o;case 1:return gn(o,e);case 6:return gn(o,{a:e,b:t,c:r,d:n,e:i,f:a})}}function jt(e){return e?Dr().createSVGTransformFromMatrix(e):Dr().createSVGTransform()}var vn=/([&<>]{1})/g,Ha=/([&<>\n\r"]{1})/g,Ka={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"'"};function Ar(e,t){function r(n,i){return Ka[i]||i}return e.replace(t,r)}function Hn(e,t){var r,n,i,a,o;switch(e.nodeType){case 3:t.push(Ar(e.textContent,vn));break;case 1:if(t.push("<",e.tagName),e.hasAttributes())for(i=e.attributes,r=0,n=i.length;r<n;++r)a=i.item(r),t.push(" ",a.name,'="',Ar(a.value,Ha),'"');if(e.hasChildNodes()){for(t.push(">"),o=e.childNodes,r=0,n=o.length;r<n;++r)Hn(o.item(r),t);t.push("</",e.tagName,">")}else t.push("/>");break;case 8:t.push("<!--",Ar(e.nodeValue,vn),"-->");break;case 4:t.push("<![CDATA[",e.nodeValue,"]]>");break;default:throw new Error("unable to handle node "+e.nodeType)}return t}function qa(e,t){var r=Wn(t);if(zn(e),!!t){Ga(r)||(r=r.documentElement);for(var n=Xa(r.childNodes),i=0;i<n.length;i++)Vn(n[i],e)}}function Ya(e){for(var t=e.firstChild,r=[];t;)Hn(t,r),t=t.nextSibling;return r.join("")}function Ga(e){return e.nodeName==="#document-fragment"}function bn(e,t){if(t!==void 0){try{qa(e,t)}catch(r){throw new Error("error parsing SVG: "+r.message)}return e}else return Ya(e)}function Xa(e){return Array.prototype.slice.call(e)}function zt(e){var t=e.parentNode;return t&&t.removeChild(e),e}function Ja(e,t){return t instanceof SVGMatrix?e.createSVGTransformFromMatrix(t):t}function Qa(e,t){var r,n;for(e.clear(),r=0;n=t[r];r++)e.appendItem(Ja(e,n))}function $t(e,t){var r=e.transform.baseVal;return t&&(Array.isArray(t)||(t=[t]),Qa(r,t)),r.consolidate()}function gt(e){return e.flat().join(",").replace(/,?([A-z]),?/g,"$1")}function Za(e){return["M",e.x,e.y]}function Rr(e){return["L",e.x,e.y]}function es(e,t,r){return["C",e.x,e.y,t.x,t.y,r.x,r.y]}function ts(e,t){const r=e.length,n=[Za(e[0])];for(let i=1;i<r;i++){const a=e[i-1],o=e[i],l=e[i+1];if(!l||!t){n.push(Rr(o));continue}const u=Math.min(t,Ir(o.x-a.x,o.y-a.y),Ir(l.x-o.x,l.y-o.y));if(!u){n.push(Rr(o));continue}const h=Qt(o,a,u),b=Qt(o,a,u*.5),y=Qt(o,l,u),k=Qt(o,l,u*.5);n.push(Rr(h)),n.push(es(b,k,y))}return n}function Qt(e,t,r){const n=t.x-e.x,i=t.y-e.y,a=Ir(n,i),o=r/a;return{x:e.x+n*o,y:e.y+i*o}}function Ir(e,t){return Math.sqrt(Math.pow(e,2)+Math.pow(t,2))}function Wr(e,t,r){nt(t)&&(r=t,t=null),t||(t={});const n=te("path",t);return nt(r)&&(n.dataset.cornerRadius=String(r)),Kn(n,e)}function Kn(e,t){const r=parseInt(e.dataset.cornerRadius,10)||0;return Z(e,{d:gt(ts(t,r))}),e}var Bt="hsl(225, 10%, 15%)";function Ue(e,t){return jn(e.eventDefinitions,function(r){return r.$type===t})}function rs(e){return e.$type==="bpmn:IntermediateThrowEvent"||e.$type==="bpmn:EndEvent"}function ns(e){var t=e.dataObjectRef;return e.isCollection||t&&t.isCollection}function q(e,t){var r=Tt(e);return r.get("color:background-color")||r.get("bioc:fill")||t||"white"}function A(e,t){var r=Tt(e);return r.get("color:border-color")||r.get("bioc:stroke")||t||Bt}function Lt(e,t,r){var n=Tt(e),i=n.get("label");return i&&i.get("color:color")||t||A(e,r)}function is(e){var t=e.x+e.width/2,r=e.y+e.height/2,n=e.width/2,i=[["M",t,r],["m",0,-n],["a",n,n,0,1,1,0,2*n],["a",n,n,0,1,1,0,-2*n],["z"]];return gt(i)}function as(e,t){var r=e.x,n=e.y,i=e.width,a=e.height,o=[["M",r+t,n],["l",i-t*2,0],["a",t,t,0,0,1,t,t],["l",0,a-t*2],["a",t,t,0,0,1,-t,t],["l",t*2-i,0],["a",t,t,0,0,1,-t,-t],["l",0,t*2-a],["a",t,t,0,0,1,t,-t],["z"]];return gt(o)}function ss(e){var t=e.width,r=e.height,n=e.x,i=e.y,a=t/2,o=r/2,l=[["M",n+a,i],["l",a,o],["l",-a,o],["l",-a,-o],["z"]];return gt(l)}function os(e){var t=e.x,r=e.y,n=e.width,i=e.height,a=[["M",t,r],["l",n,0],["l",0,i],["l",-n,0],["z"]];return gt(a)}function ls(e,t){return t.forEach(function(r){r&&typeof r!="string"&&!Array.isArray(r)&&Object.keys(r).forEach(function(n){if(n!=="default"&&!(n in e)){var i=Object.getOwnPropertyDescriptor(r,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return r[n]}})}})}),Object.freeze(e)}const us=Object.prototype.toString,cs=Object.prototype.hasOwnProperty;function ps(e){return e===void 0}function fs(e){return us.call(e)==="[object Array]"}function ds(e,t){return cs.call(e,t)}function _n(e,t){let r,n;if(ps(e))return;const i=fs(e)?ms:hs;for(let a in e)if(ds(e,a)&&(r=e[a],n=t(r,i(a)),n===!1))return r}function hs(e){return e}function ms(e){return Number(e)}function Re(e,...t){const r=e.style;return _n(t,function(n){n&&_n(n,function(i,a){r[a]=i})}),e}function ys(e,t,r){return arguments.length==2?e.getAttribute(t):r===null?e.removeAttribute(t):(e.setAttribute(t,r),e)}const gs=Object.prototype.toString;function yr(e){return new st(e)}function st(e){if(!e||!e.nodeType)throw new Error("A DOM element reference is required");this.el=e,this.list=e.classList}st.prototype.add=function(e){return this.list.add(e),this};st.prototype.remove=function(e){return gs.call(e)=="[object RegExp]"?this.removeMatching(e):(this.list.remove(e),this)};st.prototype.removeMatching=function(e){const t=this.array();for(let r=0;r<t.length;r++)e.test(t[r])&&this.remove(t[r]);return this};st.prototype.toggle=function(e,t){return typeof t<"u"?t!==this.list.toggle(e,t)&&this.list.toggle(e):this.list.toggle(e),this};st.prototype.array=function(){return Array.from(this.list)};st.prototype.has=st.prototype.contains=function(e){return this.list.contains(e)};function qn(e){for(var t;t=e.firstChild;)e.removeChild(t);return e}function vs(e,t,r){var n=r?e:e.parentNode;return n&&typeof n.closest=="function"&&n.closest(t)||null}var pr={},fr,Or,Hr;function Yn(){fr=window.addEventListener?"addEventListener":"attachEvent",Or=window.removeEventListener?"removeEventListener":"detachEvent",Hr=fr!=="addEventListener"?"on":""}var bs=pr.bind=function(e,t,r,n){return fr||Yn(),e[fr](Hr+t,r,n||!1),r},_s=pr.unbind=function(e,t,r,n){return Or||Yn(),e[Or](Hr+t,r,n||!1),r},Kr=ls({__proto__:null,bind:bs,unbind:_s,default:pr},[pr]),Gn=["focus","blur"];function Es(e,t,r,n,i){return Gn.indexOf(r)!==-1&&(i=!0),Kr.bind(e,r,function(a){var o=a.target||a.srcElement;a.delegateTarget=vs(o,t,!0),a.delegateTarget&&n.call(e,a)},i)}function ws(e,t,r,n){return Gn.indexOf(t)!==-1&&(n=!0),Kr.unbind(e,t,r,n)}var Lr={bind:Es,unbind:ws},xs=Ss,Xn=!1,Zt;typeof document<"u"&&(Zt=document.createElement("div"),Zt.innerHTML='  <link/><table></table><a href="/a">a</a><input type="checkbox"/>',Xn=!Zt.getElementsByTagName("link").length,Zt=void 0);var fe={legend:[1,"<fieldset>","</fieldset>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],_default:Xn?[1,"X<div>","</div>"]:[0,"",""]};fe.td=fe.th=[3,"<table><tbody><tr>","</tr></tbody></table>"];fe.option=fe.optgroup=[1,'<select multiple="multiple">',"</select>"];fe.thead=fe.tbody=fe.colgroup=fe.caption=fe.tfoot=[1,"<table>","</table>"];fe.polyline=fe.ellipse=fe.polygon=fe.circle=fe.text=fe.line=fe.path=fe.rect=fe.g=[1,'<svg xmlns="http://www.w3.org/2000/svg" version="1.1">',"</svg>"];function Ss(e,t){if(typeof e!="string")throw new TypeError("String expected");t||(t=document);var r=/<([\w:]+)/.exec(e);if(!r)return t.createTextNode(e);e=e.replace(/^\s+|\s+$/g,"");var n=r[1];if(n=="body"){var u=t.createElement("html");return u.innerHTML=e,u.removeChild(u.lastChild)}var i=Object.prototype.hasOwnProperty.call(fe,n)?fe[n]:fe._default,a=i[0],o=i[1],l=i[2],u=t.createElement("div");for(u.innerHTML=o+e+l;a--;)u=u.lastChild;if(u.firstChild==u.lastChild)return u.removeChild(u.firstChild);for(var h=t.createDocumentFragment();u.firstChild;)h.appendChild(u.removeChild(u.firstChild));return h}var He=xs;function Be(e,t){return t=t||document,t.querySelector(e)}function Ps(e,t){return t=t||document,t.querySelectorAll(e)}function dr(e){e.parentNode&&e.parentNode.removeChild(e)}function En(e,t,r,n,i){var a=jt();a.setTranslate(t,r);var o=jt();o.setRotate(n||0,0,0);var l=jt();l.setScale(i||1,i||1),$t(e,[a,o,l])}function Jn(e,t,r){var n=jt();n.setTranslate(t,r),$t(e,n)}function As(e,t){var r=jt();r.setRotate(t,0,0),$t(e,r)}function Rs(e,t){return t={exports:{}},e(t,t.exports),t.exports}var Cs=Rs(function(e){var t=e.exports=function(r,n){if(n||(n=16),r===void 0&&(r=128),r<=0)return"0";for(var i=Math.log(Math.pow(2,r))/Math.log(n),a=2;i===1/0;a*=2)i=Math.log(Math.pow(2,r/a))/Math.log(n)*a;for(var o=i-Math.floor(i),l="",a=0;a<Math.floor(i);a++){var u=Math.floor(Math.random()*n).toString(n);l=u+l}if(o){var h=Math.pow(n,o),u=Math.floor(Math.random()*h).toString(n);l=u+l}var b=parseInt(l,n);return b!==1/0&&b>=Math.pow(2,r)?t(r,n):l};t.rack=function(r,n,i){var a=function(l){var u=0;do{if(u++>10)if(i)r+=i;else throw new Error("too many ID collisions, use more bits");var h=t(r,n)}while(Object.hasOwnProperty.call(o,h));return o[h]=l,h},o=a.hats={};return a.get=function(l){return a.hats[l]},a.set=function(l,u){return a.hats[l]=u,a},a.bits=r||128,a.base=n||16,a}});function Xe(e){if(!(this instanceof Xe))return new Xe(e);e=e||[128,36,1],this._seed=e.length?Cs.rack(e[0],e[1],e[2]):e}Xe.prototype.next=function(e){return this._seed(e||!0)};Xe.prototype.nextPrefixed=function(e,t){var r;do r=e+this.next(!0);while(this.assigned(r));return this.claim(r,t),r};Xe.prototype.claim=function(e,t){this._seed.set(e,t||!0)};Xe.prototype.assigned=function(e){return this._seed.get(e)||!1};Xe.prototype.unclaim=function(e){delete this._seed.hats[e]};Xe.prototype.clear=function(){var e=this._seed.hats,t;for(t in e)this.unclaim(t)};var Ts=new Xe,ir=10,Cr=3,xt=.95,ks=.35,Ns=10;function vt(e,t,r,n,i,a,o){Je.call(this,t,o);var l=e&&e.defaultFillColor,u=e&&e.defaultStrokeColor,h=e&&e.defaultLabelColor,b=Ts.next(),y={};function k(c){return r.computeStyle(c,{strokeLinecap:"round",strokeLinejoin:"round",stroke:Bt,strokeWidth:2,fill:"white"})}function R(c){return r.computeStyle(c,["no-fill"],{strokeLinecap:"round",strokeLinejoin:"round",stroke:Bt,strokeWidth:2})}function V(c,s){var{ref:p={x:0,y:0},scale:d=1,element:f}=s,m=te("marker",{id:c,viewBox:"0 0 20 20",refX:p.x,refY:p.y,markerWidth:20*d,markerHeight:20*d,orient:"auto"});ce(m,f);var E=Be("defs",i._svg);E||(E=te("defs"),ce(i._svg,E)),ce(E,m),y[c]=m}function $(c){return c.replace(/[^0-9a-zA-Z]+/g,"_")}function W(c,s,p){var d=c+"-"+$(s)+"-"+$(p)+"-"+b;return y[d]||H(d,c,s,p),"url(#"+d+")"}function H(c,s,p,d){if(s==="sequenceflow-end"){var f=te("path",{d:"M 1 5 L 11 10 L 1 15 Z",...k({fill:d,stroke:d,strokeWidth:1})});V(c,{element:f,ref:{x:11,y:10},scale:.5})}if(s==="messageflow-start"){var m=te("circle",{cx:6,cy:6,r:3.5,...k({fill:p,stroke:d,strokeWidth:1,strokeDasharray:[1e4,1]})});V(c,{element:m,ref:{x:6,y:6}})}if(s==="messageflow-end"){var E=te("path",{d:"m 1 5 l 0 -3 l 7 3 l -7 3 z",...k({fill:p,stroke:d,strokeWidth:1,strokeDasharray:[1e4,1]})});V(c,{element:E,ref:{x:8.5,y:5}})}if(s==="association-start"){var U=te("path",{d:"M 11 5 L 1 10 L 11 15",...R({fill:"none",stroke:d,strokeWidth:1.5,strokeDasharray:[1e4,1]})});V(c,{element:U,ref:{x:1,y:10},scale:.5})}if(s==="association-end"){var oe=te("path",{d:"M 1 5 L 11 10 L 1 15",...R({fill:"none",stroke:d,strokeWidth:1.5,strokeDasharray:[1e4,1]})});V(c,{element:oe,ref:{x:11,y:10},scale:.5})}if(s==="conditional-flow-marker"){var G=te("path",{d:"M 0 10 L 8 6 L 16 10 L 8 14 Z",...k({fill:p,stroke:d})});V(c,{element:G,ref:{x:-1,y:10},scale:.5})}if(s==="conditional-default-flow-marker"){var Se=te("path",{d:"M 6 4 L 10 16",...k({stroke:d})});V(c,{element:Se,ref:{x:0,y:10},scale:.5})}}function v(c,s,p,d,f){dt(d)&&(f=d,d=0),d=d||0,f=k(f),f.fill==="none"&&delete f.fillOpacity;var m=s/2,E=p/2,U=te("circle",{cx:m,cy:E,r:Math.round((s+p)/4-d),...f});return ce(c,U),U}function N(c,s,p,d,f,m){dt(f)&&(m=f,f=0),f=f||0,m=k(m);var E=te("rect",{x:f,y:f,width:s-f*2,height:p-f*2,rx:d,ry:d,...m});return ce(c,E),E}function w(c,s,p,d){var f=s/2,m=p/2,E=[{x:f,y:0},{x:s,y:m},{x:f,y:p},{x:0,y:m}],U=E.map(function(G){return G.x+","+G.y}).join(" ");d=k(d);var oe=te("polygon",{...d,points:U});return ce(c,oe),oe}function S(c,s,p,d){p=R(p);var f=Wr(s,p,d);return ce(c,f),f}function C(c,s,p){return S(c,s,p,5)}function P(c,s,p){p=R(p);var d=te("path",{...p,d:s});return ce(c,d),d}function _(c,s,p,d){return P(s,p,B({"data-marker":c},d))}function g(c){return Y[c]}function x(c){return function(s,p,d){return g(c)(s,p,d)}}function D(c,s){var p=le(c),d=rs(p);return p.eventDefinitions&&p.eventDefinitions.length>1?p.parallelMultiple?g("bpmn:ParallelMultipleEventDefinition")(s,c,d):g("bpmn:MultipleEventDefinition")(s,c,d):Ue(p,"bpmn:MessageEventDefinition")?g("bpmn:MessageEventDefinition")(s,c,d):Ue(p,"bpmn:TimerEventDefinition")?g("bpmn:TimerEventDefinition")(s,c,d):Ue(p,"bpmn:ConditionalEventDefinition")?g("bpmn:ConditionalEventDefinition")(s,c):Ue(p,"bpmn:SignalEventDefinition")?g("bpmn:SignalEventDefinition")(s,c,d):Ue(p,"bpmn:EscalationEventDefinition")?g("bpmn:EscalationEventDefinition")(s,c,d):Ue(p,"bpmn:LinkEventDefinition")?g("bpmn:LinkEventDefinition")(s,c,d):Ue(p,"bpmn:ErrorEventDefinition")?g("bpmn:ErrorEventDefinition")(s,c,d):Ue(p,"bpmn:CancelEventDefinition")?g("bpmn:CancelEventDefinition")(s,c,d):Ue(p,"bpmn:CompensateEventDefinition")?g("bpmn:CompensateEventDefinition")(s,c,d):Ue(p,"bpmn:TerminateEventDefinition")?g("bpmn:TerminateEventDefinition")(s,c,d):null}function T(c,s,p){p=B({size:{width:100}},p);var d=a.createText(s||"",p);return Te(d).add("djs-label"),ce(c,d),d}function M(c,s,p){var d=le(s);return T(c,d.name,{box:s,align:p,padding:7,style:{fill:Lt(s,h,u)}})}function O(c,s){var p={width:90,height:30,x:s.width/2+s.x,y:s.height/2+s.y};return T(c,cr(s),{box:p,fitBox:!0,style:B({},a.getExternalStyle(),{fill:Lt(s,h,u)})})}function se(c,s,p){var d=T(c,s,{box:{height:30,width:p.height},align:"center-middle",style:{fill:Lt(p,h,u)}}),f=-1*p.height;En(d,0,-f,270)}var Y=this.handlers={"bpmn:Event":function(c,s,p){return"fillOpacity"in p||(p.fillOpacity=xt),v(c,s.width,s.height,p)},"bpmn:StartEvent":function(c,s,p){var d={fill:q(s,l),stroke:A(s,u)},f=le(s);f.isInterrupting||(d={strokeDasharray:"6",fill:q(s,l),stroke:A(s,u)});var m=g("bpmn:Event")(c,s,d);return(!p||p.renderIcon!==!1)&&D(s,c),m},"bpmn:MessageEventDefinition":function(c,s,p){var d=n.getScaledPath("EVENT_MESSAGE",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:s.width,containerHeight:s.height,position:{mx:.235,my:.315}}),f=p?A(s,u):q(s,l),m=p?q(s,l):A(s,u),E=P(c,d,{strokeWidth:1,fill:f,stroke:m});return E},"bpmn:TimerEventDefinition":function(c,s){var p=v(c,s.width,s.height,.2*s.height,{strokeWidth:2,fill:q(s,l),stroke:A(s,u)}),d=n.getScaledPath("EVENT_TIMER_WH",{xScaleFactor:.75,yScaleFactor:.75,containerWidth:s.width,containerHeight:s.height,position:{mx:.5,my:.5}});P(c,d,{strokeWidth:2,stroke:A(s,u)});for(var f=0;f<12;f++){var m=n.getScaledPath("EVENT_TIMER_LINE",{xScaleFactor:.75,yScaleFactor:.75,containerWidth:s.width,containerHeight:s.height,position:{mx:.5,my:.5}}),E=s.width/2,U=s.height/2;P(c,m,{strokeWidth:1,transform:"rotate("+f*30+","+U+","+E+")",stroke:A(s,u)})}return p},"bpmn:EscalationEventDefinition":function(c,s,p){var d=n.getScaledPath("EVENT_ESCALATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:.5,my:.2}}),f=p?A(s,u):"none";return P(c,d,{strokeWidth:1,fill:f,stroke:A(s,u)})},"bpmn:ConditionalEventDefinition":function(c,s){var p=n.getScaledPath("EVENT_CONDITIONAL",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:.5,my:.222}});return P(c,p,{strokeWidth:1,stroke:A(s,u)})},"bpmn:LinkEventDefinition":function(c,s,p){var d=n.getScaledPath("EVENT_LINK",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:.57,my:.263}}),f=p?A(s,u):"none";return P(c,d,{strokeWidth:1,fill:f,stroke:A(s,u)})},"bpmn:ErrorEventDefinition":function(c,s,p){var d=n.getScaledPath("EVENT_ERROR",{xScaleFactor:1.1,yScaleFactor:1.1,containerWidth:s.width,containerHeight:s.height,position:{mx:.2,my:.722}}),f=p?A(s,u):"none";return P(c,d,{strokeWidth:1,fill:f,stroke:A(s,u)})},"bpmn:CancelEventDefinition":function(c,s,p){var d=n.getScaledPath("EVENT_CANCEL_45",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:.638,my:-.055}}),f=p?A(s,u):"none",m=P(c,d,{strokeWidth:1,fill:f,stroke:A(s,u)});return As(m,45),m},"bpmn:CompensateEventDefinition":function(c,s,p){var d=n.getScaledPath("EVENT_COMPENSATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:.22,my:.5}}),f=p?A(s,u):"none";return P(c,d,{strokeWidth:1,fill:f,stroke:A(s,u)})},"bpmn:SignalEventDefinition":function(c,s,p){var d=n.getScaledPath("EVENT_SIGNAL",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:s.width,containerHeight:s.height,position:{mx:.5,my:.2}}),f=p?A(s,u):"none";return P(c,d,{strokeWidth:1,fill:f,stroke:A(s,u)})},"bpmn:MultipleEventDefinition":function(c,s,p){var d=n.getScaledPath("EVENT_MULTIPLE",{xScaleFactor:1.1,yScaleFactor:1.1,containerWidth:s.width,containerHeight:s.height,position:{mx:.222,my:.36}}),f=p?A(s,u):"none";return P(c,d,{strokeWidth:1,fill:f})},"bpmn:ParallelMultipleEventDefinition":function(c,s){var p=n.getScaledPath("EVENT_PARALLEL_MULTIPLE",{xScaleFactor:1.2,yScaleFactor:1.2,containerWidth:s.width,containerHeight:s.height,position:{mx:.458,my:.194}});return P(c,p,{strokeWidth:1,fill:A(s,u),stroke:A(s,u)})},"bpmn:EndEvent":function(c,s,p){var d=g("bpmn:Event")(c,s,{strokeWidth:4,fill:q(s,l),stroke:A(s,u)});return(!p||p.renderIcon!==!1)&&D(s,c),d},"bpmn:TerminateEventDefinition":function(c,s){var p=v(c,s.width,s.height,8,{strokeWidth:4,fill:A(s,u),stroke:A(s,u)});return p},"bpmn:IntermediateEvent":function(c,s,p){var d=g("bpmn:Event")(c,s,{strokeWidth:1.5,fill:q(s,l),stroke:A(s,u)});return v(c,s.width,s.height,Cr,{strokeWidth:1.5,fill:q(s,"none"),stroke:A(s,u)}),(!p||p.renderIcon!==!1)&&D(s,c),d},"bpmn:IntermediateCatchEvent":x("bpmn:IntermediateEvent"),"bpmn:IntermediateThrowEvent":x("bpmn:IntermediateEvent"),"bpmn:Activity":function(c,s,p){return p=p||{},"fillOpacity"in p||(p.fillOpacity=xt),N(c,s.width,s.height,ir,p)},"bpmn:Task":function(c,s){var p={fill:q(s,l),stroke:A(s,u)},d=g("bpmn:Activity")(c,s,p);return M(c,s,"center-middle"),K(c,s),d},"bpmn:ServiceTask":function(c,s){var p=g("bpmn:Task")(c,s),d=n.getScaledPath("TASK_TYPE_SERVICE",{abspos:{x:12,y:18}});P(c,d,{strokeWidth:1,fill:q(s,l),stroke:A(s,u)});var f=n.getScaledPath("TASK_TYPE_SERVICE_FILL",{abspos:{x:17.2,y:18}});P(c,f,{strokeWidth:0,fill:q(s,l)});var m=n.getScaledPath("TASK_TYPE_SERVICE",{abspos:{x:17,y:22}});return P(c,m,{strokeWidth:1,fill:q(s,l),stroke:A(s,u)}),p},"bpmn:UserTask":function(c,s){var p=g("bpmn:Task")(c,s),d=15,f=12,m=n.getScaledPath("TASK_TYPE_USER_1",{abspos:{x:d,y:f}});P(c,m,{strokeWidth:.5,fill:q(s,l),stroke:A(s,u)});var E=n.getScaledPath("TASK_TYPE_USER_2",{abspos:{x:d,y:f}});P(c,E,{strokeWidth:.5,fill:q(s,l),stroke:A(s,u)});var U=n.getScaledPath("TASK_TYPE_USER_3",{abspos:{x:d,y:f}});return P(c,U,{strokeWidth:.5,fill:A(s,u),stroke:A(s,u)}),p},"bpmn:ManualTask":function(c,s){var p=g("bpmn:Task")(c,s),d=n.getScaledPath("TASK_TYPE_MANUAL",{abspos:{x:17,y:15}});return P(c,d,{strokeWidth:.5,fill:q(s,l),stroke:A(s,u)}),p},"bpmn:SendTask":function(c,s){var p=g("bpmn:Task")(c,s),d=n.getScaledPath("TASK_TYPE_SEND",{xScaleFactor:1,yScaleFactor:1,containerWidth:21,containerHeight:14,position:{mx:.285,my:.357}});return P(c,d,{strokeWidth:1,fill:A(s,u),stroke:q(s,l)}),p},"bpmn:ReceiveTask":function(c,s){var p=le(s),d=g("bpmn:Task")(c,s),f;return p.instantiate?(v(c,28,28,20*.22,{strokeWidth:1}),f=n.getScaledPath("TASK_TYPE_INSTANTIATING_SEND",{abspos:{x:7.77,y:9.52}})):f=n.getScaledPath("TASK_TYPE_SEND",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:21,containerHeight:14,position:{mx:.3,my:.4}}),P(c,f,{strokeWidth:1,fill:q(s,l),stroke:A(s,u)}),d},"bpmn:ScriptTask":function(c,s){var p=g("bpmn:Task")(c,s),d=n.getScaledPath("TASK_TYPE_SCRIPT",{abspos:{x:15,y:20}});return P(c,d,{strokeWidth:1,stroke:A(s,u)}),p},"bpmn:BusinessRuleTask":function(c,s){var p=g("bpmn:Task")(c,s),d=n.getScaledPath("TASK_TYPE_BUSINESS_RULE_HEADER",{abspos:{x:8,y:8}}),f=P(c,d);Z(f,{strokeWidth:1,fill:q(s,"#aaaaaa"),stroke:A(s,u)});var m=n.getScaledPath("TASK_TYPE_BUSINESS_RULE_MAIN",{abspos:{x:8,y:8}}),E=P(c,m);return Z(E,{strokeWidth:1,stroke:A(s,u)}),p},"bpmn:SubProcess":function(c,s,p){p={fill:q(s,l),stroke:A(s,u),...p};var d=g("bpmn:Activity")(c,s,p),f=Nr(s);return ka(s)&&Z(d,{strokeDasharray:"0, 5.5",strokeWidth:2.5}),M(c,s,f?"center-top":"center-middle"),f?K(c,s):K(c,s,["SubProcessMarker"]),d},"bpmn:AdHocSubProcess":function(c,s){return g("bpmn:SubProcess")(c,s)},"bpmn:Transaction":function(c,s){var p=g("bpmn:SubProcess")(c,s,{strokeWidth:1.5}),d=r.style(["no-fill","no-events"],{stroke:A(s,u),strokeWidth:1.5});return N(c,s.width,s.height,ir-3,Cr,d),p},"bpmn:CallActivity":function(c,s){return g("bpmn:SubProcess")(c,s,{strokeWidth:5})},"bpmn:Participant":function(c,s){var p=1.5,d={fillOpacity:xt,fill:q(s,l),stroke:A(s,u),strokeWidth:p},f=g("bpmn:Lane")(c,s,d),m=Nr(s);if(m){S(c,[{x:30,y:0},{x:30,y:s.height}],{stroke:A(s,u),strokeWidth:p});var E=le(s).name;se(c,E,s)}else{var U=le(s).name;T(c,U,{box:s,align:"center-middle",style:{fill:Lt(s,h,u)}})}var oe=!!le(s).participantMultiplicity;return oe&&g("ParticipantMultiplicityMarker")(c,s),f},"bpmn:Lane":function(c,s,p){var d=N(c,s.width,s.height,0,{fill:q(s,l),fillOpacity:ks,stroke:A(s,u),strokeWidth:1.5,...p}),f=le(s);if(f.$type==="bpmn:Lane"){var m=f.name;se(c,m,s)}return d},"bpmn:InclusiveGateway":function(c,s){var p=g("bpmn:Gateway")(c,s);return v(c,s.width,s.height,s.height*.24,{strokeWidth:2.5,fill:q(s,l),stroke:A(s,u)}),p},"bpmn:ExclusiveGateway":function(c,s){var p=g("bpmn:Gateway")(c,s),d=n.getScaledPath("GATEWAY_EXCLUSIVE",{xScaleFactor:.4,yScaleFactor:.4,containerWidth:s.width,containerHeight:s.height,position:{mx:.32,my:.3}});return Tt(s).isMarkerVisible&&P(c,d,{strokeWidth:1,fill:A(s,u),stroke:A(s,u)}),p},"bpmn:ComplexGateway":function(c,s){var p=g("bpmn:Gateway")(c,s),d=n.getScaledPath("GATEWAY_COMPLEX",{xScaleFactor:.5,yScaleFactor:.5,containerWidth:s.width,containerHeight:s.height,position:{mx:.46,my:.26}});return P(c,d,{strokeWidth:1,fill:A(s,u),stroke:A(s,u)}),p},"bpmn:ParallelGateway":function(c,s){var p=g("bpmn:Gateway")(c,s),d=n.getScaledPath("GATEWAY_PARALLEL",{xScaleFactor:.6,yScaleFactor:.6,containerWidth:s.width,containerHeight:s.height,position:{mx:.46,my:.2}});return P(c,d,{strokeWidth:1,fill:A(s,u),stroke:A(s,u)}),p},"bpmn:EventBasedGateway":function(c,s){var p=le(s),d=g("bpmn:Gateway")(c,s);v(c,s.width,s.height,s.height*.2,{strokeWidth:1,fill:"none",stroke:A(s,u)});var f=p.eventGatewayType,m=!!p.instantiate;function E(){var oe=n.getScaledPath("GATEWAY_EVENT_BASED",{xScaleFactor:.18,yScaleFactor:.18,containerWidth:s.width,containerHeight:s.height,position:{mx:.36,my:.44}});P(c,oe,{strokeWidth:2,fill:q(s,"none"),stroke:A(s,u)})}if(f==="Parallel"){var U=n.getScaledPath("GATEWAY_PARALLEL",{xScaleFactor:.4,yScaleFactor:.4,containerWidth:s.width,containerHeight:s.height,position:{mx:.474,my:.296}});P(c,U,{strokeWidth:1,fill:"none"})}else f==="Exclusive"&&(m||v(c,s.width,s.height,s.height*.26,{strokeWidth:1,fill:"none",stroke:A(s,u)}),E());return d},"bpmn:Gateway":function(c,s){return w(c,s.width,s.height,{fill:q(s,l),fillOpacity:xt,stroke:A(s,u)})},"bpmn:SequenceFlow":function(c,s){var p=q(s,l),d=A(s,u),f=C(c,s.waypoints,{markerEnd:W("sequenceflow-end",p,d),stroke:A(s,u)}),m=le(s),E;return s.source&&(E=s.source.businessObject,m.conditionExpression&&E.$instanceOf("bpmn:Activity")&&Z(f,{markerStart:W("conditional-flow-marker",p,d)}),E.default&&(E.$instanceOf("bpmn:Gateway")||E.$instanceOf("bpmn:Activity"))&&E.default===m&&Z(f,{markerStart:W("conditional-default-flow-marker",p,d)})),f},"bpmn:Association":function(c,s,p){var d=le(s),f=q(s,l),m=A(s,u);return p={strokeDasharray:"0, 5",stroke:A(s,u),...p},(d.associationDirection==="One"||d.associationDirection==="Both")&&(p.markerEnd=W("association-end",f,m)),d.associationDirection==="Both"&&(p.markerStart=W("association-start",f,m)),C(c,s.waypoints,p)},"bpmn:DataInputAssociation":function(c,s){var p=q(s,l),d=A(s,u);return g("bpmn:Association")(c,s,{markerEnd:W("association-end",p,d)})},"bpmn:DataOutputAssociation":function(c,s){var p=q(s,l),d=A(s,u);return g("bpmn:Association")(c,s,{markerEnd:W("association-end",p,d)})},"bpmn:MessageFlow":function(c,s){var p=le(s),d=Tt(s),f=q(s,l),m=A(s,u),E=C(c,s.waypoints,{markerEnd:W("messageflow-end",f,m),markerStart:W("messageflow-start",f,m),strokeDasharray:"10, 11",strokeWidth:1.5,stroke:A(s,u)});if(p.messageRef){var U=E.getPointAtLength(E.getTotalLength()/2),oe=n.getScaledPath("MESSAGE_FLOW_MARKER",{abspos:{x:U.x,y:U.y}}),G={strokeWidth:1};d.messageVisibleKind==="initiating"?(G.fill="white",G.stroke=Bt):(G.fill="#888",G.stroke="white");var Se=P(c,oe,G),ue=p.messageRef.name,De=T(c,ue,{align:"center-top",fitBox:!0,style:{fill:A(s,h)}}),Oe=Se.getBBox(),re=De.getBBox(),ye=U.x-re.width/2,qe=U.y+Oe.height/2+Ns;En(De,ye,qe,0)}return E},"bpmn:DataObject":function(c,s){var p=n.getScaledPath("DATA_OBJECT_PATH",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:.474,my:.296}}),d=P(c,p,{fill:q(s,l),fillOpacity:xt,stroke:A(s,u)}),f=le(s);return ns(f)&&Ve(c,s),d},"bpmn:DataObjectReference":x("bpmn:DataObject"),"bpmn:DataInput":function(c,s){var p=n.getRawPath("DATA_ARROW"),d=g("bpmn:DataObject")(c,s);return P(c,p,{strokeWidth:1}),d},"bpmn:DataOutput":function(c,s){var p=n.getRawPath("DATA_ARROW"),d=g("bpmn:DataObject")(c,s);return P(c,p,{strokeWidth:1,fill:Bt}),d},"bpmn:DataStoreReference":function(c,s){var p=n.getScaledPath("DATA_STORE",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:0,my:.133}}),d=P(c,p,{strokeWidth:2,fill:q(s,l),fillOpacity:xt,stroke:A(s,u)});return d},"bpmn:BoundaryEvent":function(c,s,p){var d=le(s),f=d.cancelActivity,m={strokeWidth:1.5,fill:q(s,l),stroke:A(s,u)};f||(m.strokeDasharray="6");var E={...m,fillOpacity:1},U={...m,fill:"none"},oe=g("bpmn:Event")(c,s,E);return v(c,s.width,s.height,Cr,U),(!p||p.renderIcon!==!1)&&D(s,c),oe},"bpmn:Group":function(c,s){return N(c,s.width,s.height,ir,{stroke:A(s,u),strokeWidth:1.5,strokeDasharray:"10,6,0,6",fill:"none",pointerEvents:"none"})},label:function(c,s){return O(c,s)},"bpmn:TextAnnotation":function(c,s){var p=N(c,s.width,s.height,0,0,{fill:"none",stroke:"none"}),d=n.getScaledPath("TEXT_ANNOTATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:0,my:0}});P(c,d,{stroke:A(s,u)});var f=le(s).text||"";return T(c,f,{box:s,align:"left-top",padding:7,style:{fill:Lt(s,h,u)}}),p},ParticipantMultiplicityMarker:function(c,s){var p=n.getScaledPath("MARKER_PARALLEL",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:s.width/2/s.width,my:(s.height-15)/s.height}});_("participant-multiplicity",c,p,{strokeWidth:2,fill:q(s,l),stroke:A(s,u)})},SubProcessMarker:function(c,s){var p=N(c,14,14,0,{strokeWidth:1,fill:q(s,l),stroke:A(s,u)});Jn(p,s.width/2-7.5,s.height-20);var d=n.getScaledPath("MARKER_SUB_PROCESS",{xScaleFactor:1.5,yScaleFactor:1.5,containerWidth:s.width,containerHeight:s.height,position:{mx:(s.width/2-7.5)/s.width,my:(s.height-20)/s.height}});_("sub-process",c,d,{fill:q(s,l),stroke:A(s,u)})},ParallelMarker:function(c,s,p){var d=n.getScaledPath("MARKER_PARALLEL",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:(s.width/2+p.parallel)/s.width,my:(s.height-20)/s.height}});_("parallel",c,d,{fill:q(s,l),stroke:A(s,u)})},SequentialMarker:function(c,s,p){var d=n.getScaledPath("MARKER_SEQUENTIAL",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:(s.width/2+p.seq)/s.width,my:(s.height-19)/s.height}});_("sequential",c,d,{fill:q(s,l),stroke:A(s,u)})},CompensationMarker:function(c,s,p){var d=n.getScaledPath("MARKER_COMPENSATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:(s.width/2+p.compensation)/s.width,my:(s.height-13)/s.height}});_("compensation",c,d,{strokeWidth:1,fill:q(s,l),stroke:A(s,u)})},LoopMarker:function(c,s,p){var d=n.getScaledPath("MARKER_LOOP",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:(s.width/2+p.loop)/s.width,my:(s.height-7)/s.height}});_("loop",c,d,{strokeWidth:1.5,fill:q(s,l),stroke:A(s,u),strokeMiterlimit:.5})},AdhocMarker:function(c,s,p){var d=n.getScaledPath("MARKER_ADHOC",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:(s.width/2+p.adhoc)/s.width,my:(s.height-15)/s.height}});_("adhoc",c,d,{strokeWidth:1,fill:A(s,u),stroke:A(s,u)})}};function K(c,s,p){var d=le(s),f=p&&p.indexOf("SubProcessMarker")!==-1,m;f?m={seq:-21,parallel:-22,compensation:-42,loop:-18,adhoc:10}:m={seq:-3,parallel:-6,compensation:-27,loop:0,adhoc:10},I(p,function(oe){g(oe)(c,s,m)}),d.isForCompensation&&g("CompensationMarker")(c,s,m),d.$type==="bpmn:AdHocSubProcess"&&g("AdhocMarker")(c,s,m);var E=d.loopCharacteristics,U=E&&E.isSequential;E&&(U===void 0&&g("LoopMarker")(c,s,m),U===!1&&g("ParallelMarker")(c,s,m),U===!0&&g("SequentialMarker")(c,s,m))}function Ve(c,s){var p=(s.height-18)/s.height,d=n.getScaledPath("DATA_OBJECT_COLLECTION_PATH",{xScaleFactor:1,yScaleFactor:1,containerWidth:s.width,containerHeight:s.height,position:{mx:.33,my:p}});P(c,d,{strokeWidth:2})}this._drawPath=P,this._renderer=g}Ke(vt,Je);vt.$inject=["config.bpmnRenderer","eventBus","styles","pathMap","canvas","textRenderer"];vt.prototype.canRender=function(e){return F(e,"bpmn:BaseElement")};vt.prototype.drawShape=function(e,t){var r=t.type,n=this._renderer(r);return n(e,t)};vt.prototype.drawConnection=function(e,t){var r=t.type,n=this._renderer(r);return n(e,t)};vt.prototype.getShapePath=function(e){return F(e,"bpmn:Event")?is(e):F(e,"bpmn:Activity")?as(e,ir):F(e,"bpmn:Gateway")?ss(e):os(e)};var Ms=0,Ds={width:150,height:50};function Is(e){var t=e.split("-");return{horizontal:t[0]||"center",vertical:t[1]||"top"}}function Os(e){return dt(e)?B({top:0,left:0,right:0,bottom:0},e):{top:e,left:e,right:e,bottom:e}}function Ls(e,t){t.textContent=e;var r;try{var n,i=e==="";return t.textContent=i?"dummy":e,r=t.getBBox(),n={width:r.width+r.x*2,height:r.height},i&&(n.width=0),n}catch(a){return{width:0,height:0}}}function Bs(e,t,r){for(var n=e.shift(),i=n,a;;){if(a=Ls(i,r),a.width=i?a.width:0,i===" "||i===""||a.width<Math.round(t)||i.length<2)return Fs(e,i,n,a);i=Vs(i,a.width,t)}}function Fs(e,t,r,n){if(t.length<r.length){var i=r.slice(t.length).trim();e.unshift(i)}return{width:n.width,height:n.height,text:t}}var wn="­";function js(e,t){var r=e.split(/(\s|-|\u00AD)/g),n,i=[],a=0;if(r.length>1)for(;n=r.shift();)if(n.length+a<t)i.push(n),a+=n.length;else{(n==="-"||n===wn)&&i.pop();break}var o=i[i.length-1];return o&&o===wn&&(i[i.length-1]="-"),i.join("")}function Vs(e,t,r){var n=Math.max(e.length*(r/t),1),i=js(e,n);return i||(i=e.slice(0,Math.max(Math.round(n-1),1))),i}function $s(){var e=document.getElementById("helper-svg");return e||(e=te("svg"),Z(e,{id:"helper-svg"}),Re(e,{visibility:"hidden",position:"fixed",width:0,height:0}),document.body.appendChild(e)),e}function gr(e){this._config=B({},{size:Ds,padding:Ms,style:{},align:"center-top"},e||{})}gr.prototype.createText=function(e,t){return this.layoutText(e,t).element};gr.prototype.getDimensions=function(e,t){return this.layoutText(e,t).dimensions};gr.prototype.layoutText=function(e,t){var r=B({},this._config.size,t.box),n=B({},this._config.style,t.style),i=Is(t.align||this._config.align),a=Os(t.padding!==void 0?t.padding:this._config.padding),o=t.fitBox||!1,l=Us(n),u=e.split(/\u00AD?\r?\n/),h=[],b=r.width-a.left-a.right,y=te("text");Z(y,{x:0,y:0}),Z(y,n);var k=$s();for(ce(k,y);u.length;)h.push(Bs(u,b,y));i.vertical==="middle"&&(a.top=a.bottom=0);var R=Ct(h,function(v,N,w){return v+(l||N.height)},0)+a.top+a.bottom,V=Ct(h,function(v,N,w){return N.width>v?N.width:v},0),$=a.top;i.vertical==="middle"&&($+=(r.height-R)/2),$-=(l||h[0].height)/4;var W=te("text");Z(W,n),I(h,function(v){var N;switch($+=l||v.height,i.horizontal){case"left":N=a.left;break;case"right":N=(o?V:b)-a.right-v.width;break;default:N=Math.max(((o?V:b)-v.width)/2+a.left,0)}var w=te("tspan");Z(w,{x:N,y:$}),w.textContent=v.text,ce(W,w)}),zt(y);var H={width:V,height:R};return{dimensions:H,element:W}};function Us(e){if("fontSize"in e&&"lineHeight"in e)return e.lineHeight*parseInt(e.fontSize,10)}var zs=12,Ws=1.2,Hs=30;function Qn(e){var t=B({fontFamily:"Arial, sans-serif",fontSize:zs,fontWeight:"normal",lineHeight:Ws},e&&e.defaultStyle||{}),r=parseInt(t.fontSize,10)-1,n=B({},t,{fontSize:r},e&&e.externalStyle||{}),i=new gr({style:t});this.getExternalLabelBounds=function(a,o){var l=i.getDimensions(o,{box:{width:90,height:30},style:n});return{x:Math.round(a.x+a.width/2-l.width/2),y:Math.round(a.y),width:Math.ceil(l.width),height:Math.ceil(l.height)}},this.getTextAnnotationBounds=function(a,o){var l=i.getDimensions(o,{box:a,style:t,align:"left-top",padding:5});return{x:a.x,y:a.y,width:a.width,height:Math.max(Hs,Math.round(l.height))}},this.createText=function(a,o){return i.createText(a,o||{})},this.getDefaultStyle=function(){return t},this.getExternalStyle=function(){return n}}Qn.$inject=["config.textRenderer"];function Ks(){this.pathMap={EVENT_MESSAGE:{d:"m {mx},{my} l 0,{e.y1} l {e.x1},0 l 0,-{e.y1} z l {e.x0},{e.y0} l {e.x0},-{e.y0}",height:36,width:36,heightElements:[6,14],widthElements:[10.5,21]},EVENT_SIGNAL:{d:"M {mx},{my} l {e.x0},{e.y0} l -{e.x1},0 Z",height:36,width:36,heightElements:[18],widthElements:[10,20]},EVENT_ESCALATION:{d:"M {mx},{my} l {e.x0},{e.y0} l -{e.x0},-{e.y1} l -{e.x0},{e.y1} Z",height:36,width:36,heightElements:[20,7],widthElements:[8]},EVENT_CONDITIONAL:{d:"M {e.x0},{e.y0} l {e.x1},0 l 0,{e.y2} l -{e.x1},0 Z M {e.x2},{e.y3} l {e.x0},0 M {e.x2},{e.y4} l {e.x0},0 M {e.x2},{e.y5} l {e.x0},0 M {e.x2},{e.y6} l {e.x0},0 M {e.x2},{e.y7} l {e.x0},0 M {e.x2},{e.y8} l {e.x0},0 ",height:36,width:36,heightElements:[8.5,14.5,18,11.5,14.5,17.5,20.5,23.5,26.5],widthElements:[10.5,14.5,12.5]},EVENT_LINK:{d:"m {mx},{my} 0,{e.y0} -{e.x1},0 0,{e.y1} {e.x1},0 0,{e.y0} {e.x0},-{e.y2} -{e.x0},-{e.y2} z",height:36,width:36,heightElements:[4.4375,6.75,7.8125],widthElements:[9.84375,13.5]},EVENT_ERROR:{d:"m {mx},{my} {e.x0},-{e.y0} {e.x1},-{e.y1} {e.x2},{e.y2} {e.x3},-{e.y3} -{e.x4},{e.y4} -{e.x5},-{e.y5} z",height:36,width:36,heightElements:[.023,8.737,8.151,16.564,10.591,8.714],widthElements:[.085,6.672,6.97,4.273,5.337,6.636]},EVENT_CANCEL_45:{d:"m {mx},{my} -{e.x1},0 0,{e.x0} {e.x1},0 0,{e.y1} {e.x0},0 0,-{e.y1} {e.x1},0 0,-{e.y0} -{e.x1},0 0,-{e.y1} -{e.x0},0 z",height:36,width:36,heightElements:[4.75,8.5],widthElements:[4.75,8.5]},EVENT_COMPENSATION:{d:"m {mx},{my} {e.x0},-{e.y0} 0,{e.y1} z m {e.x1},-{e.y2} {e.x2},-{e.y3} 0,{e.y1} -{e.x2},-{e.y3} z",height:36,width:36,heightElements:[6.5,13,.4,6.1],widthElements:[9,9.3,8.7]},EVENT_TIMER_WH:{d:"M {mx},{my} l {e.x0},-{e.y0} m -{e.x0},{e.y0} l {e.x1},{e.y1} ",height:36,width:36,heightElements:[10,2],widthElements:[3,7]},EVENT_TIMER_LINE:{d:"M {mx},{my} m {e.x0},{e.y0} l -{e.x1},{e.y1} ",height:36,width:36,heightElements:[10,3],widthElements:[0,0]},EVENT_MULTIPLE:{d:"m {mx},{my} {e.x1},-{e.y0} {e.x1},{e.y0} -{e.x0},{e.y1} -{e.x2},0 z",height:36,width:36,heightElements:[6.28099,12.56199],widthElements:[3.1405,9.42149,12.56198]},EVENT_PARALLEL_MULTIPLE:{d:"m {mx},{my} {e.x0},0 0,{e.y1} {e.x1},0 0,{e.y0} -{e.x1},0 0,{e.y1} -{e.x0},0 0,-{e.y1} -{e.x1},0 0,-{e.y0} {e.x1},0 z",height:36,width:36,heightElements:[2.56228,7.68683],widthElements:[2.56228,7.68683]},GATEWAY_EXCLUSIVE:{d:"m {mx},{my} {e.x0},{e.y0} {e.x1},{e.y0} {e.x2},0 {e.x4},{e.y2} {e.x4},{e.y1} {e.x2},0 {e.x1},{e.y3} {e.x0},{e.y3} {e.x3},0 {e.x5},{e.y1} {e.x5},{e.y2} {e.x3},0 z",height:17.5,width:17.5,heightElements:[8.5,6.5312,-6.5312,-8.5],widthElements:[6.5,-6.5,3,-3,5,-5]},GATEWAY_PARALLEL:{d:"m {mx},{my} 0,{e.y1} -{e.x1},0 0,{e.y0} {e.x1},0 0,{e.y1} {e.x0},0 0,-{e.y1} {e.x1},0 0,-{e.y0} -{e.x1},0 0,-{e.y1} -{e.x0},0 z",height:30,width:30,heightElements:[5,12.5],widthElements:[5,12.5]},GATEWAY_EVENT_BASED:{d:"m {mx},{my} {e.x0},{e.y0} {e.x0},{e.y1} {e.x1},{e.y2} {e.x2},0 z",height:11,width:11,heightElements:[-6,6,12,-12],widthElements:[9,-3,-12]},GATEWAY_COMPLEX:{d:"m {mx},{my} 0,{e.y0} -{e.x0},-{e.y1} -{e.x1},{e.y2} {e.x0},{e.y1} -{e.x2},0 0,{e.y3} {e.x2},0  -{e.x0},{e.y1} l {e.x1},{e.y2} {e.x0},-{e.y1} 0,{e.y0} {e.x3},0 0,-{e.y0} {e.x0},{e.y1} {e.x1},-{e.y2} -{e.x0},-{e.y1} {e.x2},0 0,-{e.y3} -{e.x2},0 {e.x0},-{e.y1} -{e.x1},-{e.y2} -{e.x0},{e.y1} 0,-{e.y0} -{e.x3},0 z",height:17.125,width:17.125,heightElements:[4.875,3.4375,2.125,3],widthElements:[3.4375,2.125,4.875,3]},DATA_OBJECT_PATH:{d:"m 0,0 {e.x1},0 {e.x0},{e.y0} 0,{e.y1} -{e.x2},0 0,-{e.y2} {e.x1},0 0,{e.y0} {e.x0},0",height:61,width:51,heightElements:[10,50,60],widthElements:[10,40,50,60]},DATA_OBJECT_COLLECTION_PATH:{d:"m{mx},{my} m 3,2 l 0,10 m 3,-10 l 0,10 m 3,-10 l 0,10",height:10,width:10,heightElements:[],widthElements:[]},DATA_ARROW:{d:"m 5,9 9,0 0,-3 5,5 -5,5 0,-3 -9,0 z",height:61,width:51,heightElements:[],widthElements:[]},DATA_STORE:{d:"m  {mx},{my} l  0,{e.y2} c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0 l  0,-{e.y2} c -{e.x0},-{e.y1} -{e.x1},-{e.y1} -{e.x2},0c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0 m  -{e.x2},{e.y0}c  {e.x0},{e.y1} {e.x1},{e.y1} {e.x2},0m  -{e.x2},{e.y0}c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0",height:61,width:61,heightElements:[7,10,45],widthElements:[2,58,60]},TEXT_ANNOTATION:{d:"m {mx}, {my} m 10,0 l -10,0 l 0,{e.y0} l 10,0",height:30,width:10,heightElements:[30],widthElements:[10]},MARKER_SUB_PROCESS:{d:"m{mx},{my} m 7,2 l 0,10 m -5,-5 l 10,0",height:10,width:10,heightElements:[],widthElements:[]},MARKER_PARALLEL:{d:"m{mx},{my} m 3,2 l 0,10 m 3,-10 l 0,10 m 3,-10 l 0,10",height:10,width:10,heightElements:[],widthElements:[]},MARKER_SEQUENTIAL:{d:"m{mx},{my} m 0,3 l 10,0 m -10,3 l 10,0 m -10,3 l 10,0",height:10,width:10,heightElements:[],widthElements:[]},MARKER_COMPENSATION:{d:"m {mx},{my} 7,-5 0,10 z m 7.1,-0.3 6.9,-4.7 0,10 -6.9,-4.7 z",height:10,width:21,heightElements:[],widthElements:[]},MARKER_LOOP:{d:"m {mx},{my} c 3.526979,0 6.386161,-2.829858 6.386161,-6.320661 0,-3.490806 -2.859182,-6.320661 -6.386161,-6.320661 -3.526978,0 -6.38616,2.829855 -6.38616,6.320661 0,1.745402 0.714797,3.325567 1.870463,4.469381 0.577834,0.571908 1.265885,1.034728 2.029916,1.35457 l -0.718163,-3.909793 m 0.718163,3.909793 -3.885211,0.802902",height:13.9,width:13.7,heightElements:[],widthElements:[]},MARKER_ADHOC:{d:"m {mx},{my} m 0.84461,2.64411 c 1.05533,-1.23780996 2.64337,-2.07882 4.29653,-1.97997996 2.05163,0.0805 3.85579,1.15803 5.76082,1.79107 1.06385,0.34139996 2.24454,0.1438 3.18759,-0.43767 0.61743,-0.33642 1.2775,-0.64078 1.7542,-1.17511 0,0.56023 0,1.12046 0,1.6807 -0.98706,0.96237996 -2.29792,1.62393996 -3.6918,1.66181996 -1.24459,0.0927 -2.46671,-0.2491 -3.59505,-0.74812 -1.35789,-0.55965 -2.75133,-1.33436996 -4.27027,-1.18121996 -1.37741,0.14601 -2.41842,1.13685996 -3.44288,1.96782996 z",height:4,width:15,heightElements:[],widthElements:[]},TASK_TYPE_SEND:{d:"m {mx},{my} l 0,{e.y1} l {e.x1},0 l 0,-{e.y1} z l {e.x0},{e.y0} l {e.x0},-{e.y0}",height:14,width:21,heightElements:[6,14],widthElements:[10.5,21]},TASK_TYPE_SCRIPT:{d:"m {mx},{my} c 9.966553,-6.27276 -8.000926,-7.91932 2.968968,-14.938 l -8.802728,0 c -10.969894,7.01868 6.997585,8.66524 -2.968967,14.938 z m -7,-12 l 5,0 m -4.5,3 l 4.5,0 m -3,3 l 5,0m -4,3 l 5,0",height:15,width:12.6,heightElements:[6,14],widthElements:[10.5,21]},TASK_TYPE_USER_1:{d:"m {mx},{my} c 0.909,-0.845 1.594,-2.049 1.594,-3.385 0,-2.554 -1.805,-4.62199999 -4.357,-4.62199999 -2.55199998,0 -4.28799998,2.06799999 -4.28799998,4.62199999 0,1.348 0.974,2.562 1.89599998,3.405 -0.52899998,0.187 -5.669,2.097 -5.794,4.7560005 v 6.718 h 17 v -6.718 c 0,-2.2980005 -5.5279996,-4.5950005 -6.0509996,-4.7760005 zm -8,6 l 0,5.5 m 11,0 l 0,-5"},TASK_TYPE_USER_2:{d:"m {mx},{my} m 2.162,1.009 c 0,2.4470005 -2.158,4.4310005 -4.821,4.4310005 -2.66499998,0 -4.822,-1.981 -4.822,-4.4310005 "},TASK_TYPE_USER_3:{d:"m {mx},{my} m -6.9,-3.80 c 0,0 2.25099998,-2.358 4.27399998,-1.177 2.024,1.181 4.221,1.537 4.124,0.965 -0.098,-0.57 -0.117,-3.79099999 -4.191,-4.13599999 -3.57499998,0.001 -4.20799998,3.36699999 -4.20699998,4.34799999 z"},TASK_TYPE_MANUAL:{d:"m {mx},{my} c 0.234,-0.01 5.604,0.008 8.029,0.004 0.808,0 1.271,-0.172 1.417,-0.752 0.227,-0.898 -0.334,-1.314 -1.338,-1.316 -2.467,-0.01 -7.886,-0.004 -8.108,-0.004 -0.014,-0.079 0.016,-0.533 0,-0.61 0.195,-0.042 8.507,0.006 9.616,0.002 0.877,-0.007 1.35,-0.438 1.353,-1.208 0.003,-0.768 -0.479,-1.09 -1.35,-1.091 -2.968,-0.002 -9.619,-0.013 -9.619,-0.013 v -0.591 c 0,0 5.052,-0.016 7.225,-0.016 0.888,-0.002 1.354,-0.416 1.351,-1.193 -0.006,-0.761 -0.492,-1.196 -1.361,-1.196 -3.473,-0.005 -10.86,-0.003 -11.0829995,-0.003 -0.022,-0.047 -0.045,-0.094 -0.069,-0.139 0.3939995,-0.319 2.0409995,-1.626 2.4149995,-2.017 0.469,-0.4870005 0.519,-1.1650005 0.162,-1.6040005 -0.414,-0.511 -0.973,-0.5 -1.48,-0.236 -1.4609995,0.764 -6.5999995,3.6430005 -7.7329995,4.2710005 -0.9,0.499 -1.516,1.253 -1.882,2.19 -0.37000002,0.95 -0.17,2.01 -0.166,2.979 0.004,0.718 -0.27300002,1.345 -0.055,2.063 0.629,2.087 2.425,3.312 4.859,3.318 4.6179995,0.014 9.2379995,-0.139 13.8569995,-0.158 0.755,-0.004 1.171,-0.301 1.182,-1.033 0.012,-0.754 -0.423,-0.969 -1.183,-0.973 -1.778,-0.01 -5.824,-0.004 -6.04,-0.004 10e-4,-0.084 0.003,-0.586 10e-4,-0.67 z"},TASK_TYPE_INSTANTIATING_SEND:{d:"m {mx},{my} l 0,8.4 l 12.6,0 l 0,-8.4 z l 6.3,3.6 l 6.3,-3.6"},TASK_TYPE_SERVICE:{d:"m {mx},{my} v -1.71335 c 0.352326,-0.0705 0.703932,-0.17838 1.047628,-0.32133 0.344416,-0.14465 0.665822,-0.32133 0.966377,-0.52145 l 1.19431,1.18005 1.567487,-1.57688 -1.195028,-1.18014 c 0.403376,-0.61394 0.683079,-1.29908 0.825447,-2.01824 l 1.622133,-0.01 v -2.2196 l -1.636514,0.01 c -0.07333,-0.35153 -0.178319,-0.70024 -0.323564,-1.04372 -0.145244,-0.34406 -0.321407,-0.6644 -0.522735,-0.96217 l 1.131035,-1.13631 -1.583305,-1.56293 -1.129598,1.13589 c -0.614052,-0.40108 -1.302883,-0.68093 -2.022633,-0.82247 l 0.0093,-1.61852 h -2.241173 l 0.0042,1.63124 c -0.353763,0.0736 -0.705369,0.17977 -1.049785,0.32371 -0.344415,0.14437 -0.665102,0.32092 -0.9635006,0.52046 l -1.1698628,-1.15823 -1.5667691,1.5792 1.1684265,1.15669 c -0.4026573,0.61283 -0.68308,1.29797 -0.8247287,2.01713 l -1.6588041,0.003 v 2.22174 l 1.6724648,-0.006 c 0.073327,0.35077 0.1797598,0.70243 0.3242851,1.04472 0.1452428,0.34448 0.3214064,0.6644 0.5227339,0.96066 l -1.1993431,1.19723 1.5840256,1.56011 1.1964668,-1.19348 c 0.6140517,0.40346 1.3028827,0.68232 2.0233517,0.82331 l 7.19e-4,1.69892 h 2.226848 z m 0.221462,-3.9957 c -1.788948,0.7502 -3.8576,-0.0928 -4.6097055,-1.87438 -0.7521065,-1.78321 0.090598,-3.84627 1.8802645,-4.59604 1.78823,-0.74936 3.856881,0.0929 4.608987,1.87437 0.752106,1.78165 -0.0906,3.84612 -1.879546,4.59605 z"},TASK_TYPE_SERVICE_FILL:{d:"m {mx},{my} c -1.788948,0.7502 -3.8576,-0.0928 -4.6097055,-1.87438 -0.7521065,-1.78321 0.090598,-3.84627 1.8802645,-4.59604 1.78823,-0.74936 3.856881,0.0929 4.608987,1.87437 0.752106,1.78165 -0.0906,3.84612 -1.879546,4.59605 z"},TASK_TYPE_BUSINESS_RULE_HEADER:{d:"m {mx},{my} 0,4 20,0 0,-4 z"},TASK_TYPE_BUSINESS_RULE_MAIN:{d:"m {mx},{my} 0,12 20,0 0,-12 zm 0,8 l 20,0 m -13,-4 l 0,8"},MESSAGE_FLOW_MARKER:{d:"m {mx},{my} m -10.5 ,-7 l 0,14 l 21,0 l 0,-14 z l 10.5,6 l 10.5,-6"}},this.getRawPath=function(t){return this.pathMap[t].d},this.getScaledPath=function(t,r){var n=this.pathMap[t],i,a;r.abspos?(i=r.abspos.x,a=r.abspos.y):(i=r.containerWidth*r.position.mx,a=r.containerHeight*r.position.my);var o={};if(r.position){for(var l=r.containerHeight/n.height*r.yScaleFactor,u=r.containerWidth/n.width*r.xScaleFactor,h=0;h<n.heightElements.length;h++)o["y"+h]=n.heightElements[h]*l;for(var b=0;b<n.widthElements.length;b++)o["x"+b]=n.widthElements[b]*u}var y=Xs(n.d,{mx:i,my:a,e:o});return y}}var qs=/\{([^{}]+)\}/g,Ys=/(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g;function Gs(e,t,r){var n=r;return t.replace(Ys,function(i,a,o,l,u){a=a||l,n&&(a in n&&(n=n[a]),typeof n=="function"&&u&&(n=n()))}),n=(n==null||n==r?e:n)+"",n}function Xs(e,t){return String(e).replace(qs,function(r,n){return Gs(r,n,t)})}const Js={__init__:["bpmnRenderer"],bpmnRenderer:["type",vt],textRenderer:["type",Qn],pathMap:["type",Ks]};function Qs(e,t){return t=t||{},e.replace(/{([^}]+)}/g,function(r,n){return t[n]||"{"+n+"}"})}const Zn={translate:["value",Qs]};function Zs(e){return{x:Math.round(e.x),y:Math.round(e.y)}}function Br(e){return{top:e.y,right:e.x+(e.width||0),bottom:e.y+(e.height||0),left:e.x}}function eo(e){return{x:e.left,y:e.top,width:e.right-e.left,height:e.bottom-e.top}}function to(e){return Zs({x:e.x+(e.width||0)/2,y:e.y+(e.height||0)/2})}function ro(e){for(var t=e.waypoints,r=t.reduce(function(h,b,y){var k=t[y-1];if(k){var R=h[h.length-1],V=R&&R.endLength||0,$=no(k,b);h.push({start:k,end:b,startLength:V,endLength:V+$,length:$})}return h},[]),n=r.reduce(function(h,b){return h+b.length},0),i=n/2,a=0,o=r[a];o.endLength<i;)o=r[++a];var l=(i-o.startLength)/o.length,u={x:o.start.x+(o.end.x-o.start.x)*l,y:o.start.y+(o.end.y-o.start.y)*l};return u}function Fr(e){return Na(e)?ro(e):to(e)}function no(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function _e(e){return e?"<"+e.$type+(e.id?' id="'+e.id:"")+'" />':"<null>"}function ar(e,t,r){return B({id:e.id,type:e.$type,businessObject:e,di:t},r)}function io(e,t,r){var n=e.waypoint;return!n||n.length<2?[Fr(t),Fr(r)]:n.map(function(i){return{x:i.x,y:i.y}})}function ei(e,t,r,n){return new Error(e("element {element} referenced by {referenced}#{property} not yet drawn",{element:_e(r),referenced:_e(t),property:n}))}function Qe(e,t,r,n,i,a){this._eventBus=e,this._canvas=t,this._elementFactory=r,this._elementRegistry=n,this._translate=i,this._textRenderer=a}Qe.$inject=["eventBus","canvas","elementFactory","elementRegistry","translate","textRenderer"];Qe.prototype.add=function(e,t,r){var n,i=this._translate,a,o;if(F(t,"bpmndi:BPMNPlane")){var l=F(e,"bpmn:SubProcess")?{id:e.id+"_plane"}:{};n=this._elementFactory.createRoot(ar(e,t,l)),this._canvas.addRootElement(n)}else if(F(t,"bpmndi:BPMNShape")){var u=!Nr(e,t),h=so(e);a=r&&(r.hidden||r.collapsed);var b=t.bounds;n=this._elementFactory.createShape(ar(e,t,{collapsed:u,hidden:a,x:Math.round(b.x),y:Math.round(b.y),width:Math.round(b.width),height:Math.round(b.height),isFrame:h})),F(e,"bpmn:BoundaryEvent")&&this._attachBoundary(e,n),F(e,"bpmn:Lane")&&(o=0),F(e,"bpmn:DataStoreReference")&&(ao(r,Fr(b))||(r=this._canvas.findRoot(r))),this._canvas.addShape(n,r,o)}else if(F(t,"bpmndi:BPMNEdge")){var y=this._getSource(e),k=this._getTarget(e);a=r&&(r.hidden||r.collapsed),n=this._elementFactory.createConnection(ar(e,t,{hidden:a,source:y,target:k,waypoints:io(t,y,k)})),F(e,"bpmn:DataAssociation")&&(r=this._canvas.findRoot(r)),this._canvas.addConnection(n,r,o)}else throw new Error(i("unknown di {di} for element {semantic}",{di:_e(t),semantic:_e(e)}));return Ma(e)&&cr(n)&&this.addLabel(e,t,n),this._eventBus.fire("bpmnElement.added",{element:n}),n};Qe.prototype._attachBoundary=function(e,t){var r=this._translate,n=e.attachedToRef;if(!n)throw new Error(r("missing {semantic}#attachedToRef",{semantic:_e(e)}));var i=this._elementRegistry.get(n.id),a=i&&i.attachers;if(!i)throw ei(r,e,n,"attachedToRef");t.host=i,a||(i.attachers=a=[]),a.indexOf(t)===-1&&a.push(t)};Qe.prototype.addLabel=function(e,t,r){var n,i,a;return n=La(t,r),i=cr(r),i&&(n=this._textRenderer.getExternalLabelBounds(n,i)),a=this._elementFactory.createLabel(ar(e,t,{id:e.id+"_label",labelTarget:r,type:"label",hidden:r.hidden||!cr(r),x:Math.round(n.x),y:Math.round(n.y),width:Math.round(n.width),height:Math.round(n.height)})),this._canvas.addShape(a,r.parent)};Qe.prototype._getConnectedElement=function(e,t){var r,n,i=e.$type,a=this._translate;if(n=e[t+"Ref"],t==="source"&&i==="bpmn:DataInputAssociation"&&(n=n&&n[0]),(t==="source"&&i==="bpmn:DataOutputAssociation"||t==="target"&&i==="bpmn:DataInputAssociation")&&(n=e.$parent),r=n&&this._getElement(n),r)return r;throw n?ei(a,e,n,t+"Ref"):new Error(a("{semantic}#{side} Ref not specified",{semantic:_e(e),side:t}))};Qe.prototype._getSource=function(e){return this._getConnectedElement(e,"source")};Qe.prototype._getTarget=function(e){return this._getConnectedElement(e,"target")};Qe.prototype._getElement=function(e){return this._elementRegistry.get(e.id)};function ao(e,t){var r=t.x,n=t.y;return r>=e.x&&r<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}function so(e){return F(e,"bpmn:Group")}const oo={__depends__:[Zn],bpmnImporter:["type",Qe]},lo={__depends__:[Js,oo]};function qr(e){return e.originalEvent||e.srcEvent}function uo(){return/mac/i.test(navigator.platform)}function ti(e,t){return(qr(e)||e).button===t}function Wt(e){return ti(e,0)}function co(e){return ti(e,1)}function po(e){var t=qr(e)||e;return Wt(e)?uo()?t.metaKey:t.ctrlKey:!1}function fo(e){var t=qr(e)||e;return Wt(e)&&t.shiftKey}function ho(e){return!0}function er(e){return Wt(e)||co(e)}var xn=500;function ri(e,t,r){var n=this;function i(_,g,x){if(!l(_,g)){var D,T,M;x?T=t.getGraphics(x):(D=g.delegateTarget||g.target,D&&(T=D,x=t.get(T))),!(!T||!x)&&(M=e.fire(_,{element:x,gfx:T,originalEvent:g}),M===!1&&(g.stopPropagation(),g.preventDefault()))}}var a={};function o(_){return a[_]}function l(_,g){var x=h[_]||Wt;return!x(g)}var u={click:"element.click",contextmenu:"element.contextmenu",dblclick:"element.dblclick",mousedown:"element.mousedown",mousemove:"element.mousemove",mouseover:"element.hover",mouseout:"element.out",mouseup:"element.mouseup"},h={"element.contextmenu":ho,"element.mousedown":er,"element.mouseup":er,"element.click":er,"element.dblclick":er};function b(_,g,x){var D=u[_];if(!D)throw new Error("unmapped DOM event name <"+_+">");return i(D,g,x)}var y="svg, .djs-element";function k(_,g,x,D){var T=a[x]=function(M){i(x,M)};D&&(h[x]=D),T.$delegate=Lr.bind(_,y,g,T)}function R(_,g,x){var D=o(x);D&&Lr.unbind(_,g,D.$delegate)}function V(_){I(u,function(g,x){k(_,x,g)})}function $(_){I(u,function(g,x){R(_,x,g)})}e.on("canvas.destroy",function(_){$(_.svg)}),e.on("canvas.init",function(_){V(_.svg)}),e.on(["shape.added","connection.added"],function(_){var g=_.element,x=_.gfx;e.fire("interactionEvents.createHit",{element:g,gfx:x})}),e.on(["shape.changed","connection.changed"],xn,function(_){var g=_.element,x=_.gfx;e.fire("interactionEvents.updateHit",{element:g,gfx:x})}),e.on("interactionEvents.createHit",xn,function(_){var g=_.element,x=_.gfx;n.createDefaultHit(g,x)}),e.on("interactionEvents.updateHit",function(_){var g=_.element,x=_.gfx;n.updateDefaultHit(g,x)});var W=S("djs-hit djs-hit-stroke"),H=S("djs-hit djs-hit-click-stroke"),v=S("djs-hit djs-hit-all"),N=S("djs-hit djs-hit-no-move"),w={all:v,"click-stroke":H,stroke:W,"no-move":N};function S(_,g){return g=B({stroke:"white",strokeWidth:15},g||{}),r.cls(_,["no-fill","no-border"],g)}function C(_,g){var x=w[g];if(!x)throw new Error("invalid hit type <"+g+">");return Z(_,x),_}function P(_,g){ce(_,g)}this.removeHits=function(_){var g=Ps(".djs-hit",_);I(g,zt)},this.createDefaultHit=function(_,g){var x=_.waypoints,D=_.isFrame,T;return x?this.createWaypointsHit(g,x):(T=D?"stroke":"all",this.createBoxHit(g,T,{width:_.width,height:_.height}))},this.createWaypointsHit=function(_,g){var x=Wr(g);return C(x,"stroke"),P(_,x),x},this.createBoxHit=function(_,g,x){x=B({x:0,y:0},x);var D=te("rect");return C(D,g),Z(D,x),P(_,D),D},this.updateDefaultHit=function(_,g){var x=Be(".djs-hit",g);if(x)return _.waypoints?Kn(x,_.waypoints):Z(x,{width:_.width,height:_.height}),x},this.fire=i,this.triggerMouseEvent=b,this.mouseHandler=o,this.registerEvent=k,this.unregisterEvent=R}ri.$inject=["eventBus","elementRegistry","styles"];const mo={__init__:["interactionEvents"],interactionEvents:["type",ri]};function ht(e,t){t=!!t,Fe(e)||(e=[e]);var r,n,i,a;return I(e,function(o){var l=o;o.waypoints&&!t&&(l=ht(o.waypoints,!0));var u=l.x,h=l.y,b=l.height||0,y=l.width||0;(u<r||r===void 0)&&(r=u),(h<n||n===void 0)&&(n=h),(u+y>i||i===void 0)&&(i=u+y),(h+b>a||a===void 0)&&(a=h+b)}),{x:r,y:n,height:a-n,width:i-r}}function ni(e){return"waypoints"in e?"connection":"x"in e?"shape":"root"}function ii(e){return!!(e&&e.isFrame)}var yo=500,go=1e3;function bt(e,t){this._eventBus=e,this.offset=5;var r=t.cls("djs-outline",["no-fill"]),n=this;function i(a){var o=te("rect");return Z(o,B({x:0,y:0,rx:4,width:100,height:100},r)),o}e.on(["shape.added","shape.changed"],yo,function(a){var o=a.element,l=a.gfx,u=Be(".djs-outline",l);u||(u=n.getOutline(o)||i(),ce(l,u)),n.updateShapeOutline(u,o)}),e.on(["connection.added","connection.changed"],function(a){var o=a.element,l=a.gfx,u=Be(".djs-outline",l);u||(u=i(),ce(l,u)),n.updateConnectionOutline(u,o)})}bt.prototype.updateShapeOutline=function(e,t){var r=!1,n=this._getProviders();n.length&&I(n,function(i){r=r||i.updateOutline(t,e)}),r||Z(e,{x:-this.offset,y:-this.offset,width:t.width+this.offset*2,height:t.height+this.offset*2})};bt.prototype.updateConnectionOutline=function(e,t){var r=ht(t);Z(e,{x:r.x-this.offset,y:r.y-this.offset,width:r.width+this.offset*2,height:r.height+this.offset*2})};bt.prototype.registerProvider=function(e,t){t||(t=e,e=go),this._eventBus.on("outline.getProviders",e,function(r){r.providers.push(t)})};bt.prototype._getProviders=function(){var e=this._eventBus.createEvent({type:"outline.getProviders",providers:[]});return this._eventBus.fire(e),e.providers};bt.prototype.getOutline=function(e){var t,r=this._getProviders();return I(r,function(n){Ye(n.getOutline)&&(t=t||n.getOutline(e))}),t};bt.$inject=["eventBus","styles","elementRegistry"];const vo={__init__:["outline"],outline:["type",bt]};function Mt(e,t){this._eventBus=e,this._canvas=t,this._selectedElements=[];var r=this;e.on(["shape.remove","connection.remove"],function(n){var i=n.element;r.deselect(i)}),e.on(["diagram.clear","root.set"],function(n){r.select(null)})}Mt.$inject=["eventBus","canvas"];Mt.prototype.deselect=function(e){var t=this._selectedElements,r=t.indexOf(e);if(r!==-1){var n=t.slice();t.splice(r,1),this._eventBus.fire("selection.changed",{oldSelection:n,newSelection:t})}};Mt.prototype.get=function(){return this._selectedElements};Mt.prototype.isSelected=function(e){return this._selectedElements.indexOf(e)!==-1};Mt.prototype.select=function(e,t){var r=this._selectedElements,n=r.slice();Fe(e)||(e=e?[e]:[]);var i=this._canvas,a=i.getRootElement();e=e.filter(function(o){var l=i.findRoot(o);return a===l}),t?I(e,function(o){r.indexOf(o)===-1&&r.push(o)}):this._selectedElements=r=e.slice(),this._eventBus.fire("selection.changed",{oldSelection:n,newSelection:r})};var Sn="hover",Pn="selected",tr=6;function Yr(e,t,r){this._canvas=e;var n=this;this._multiSelectionBox=null;function i(o,l){e.addMarker(o,l)}function a(o,l){e.removeMarker(o,l)}t.on("element.hover",function(o){i(o.element,Sn)}),t.on("element.out",function(o){a(o.element,Sn)}),t.on("selection.changed",function(o){function l(y){a(y,Pn)}function u(y){i(y,Pn)}var h=o.oldSelection,b=o.newSelection;I(h,function(y){b.indexOf(y)===-1&&l(y)}),I(b,function(y){h.indexOf(y)===-1&&u(y)}),n._updateSelectionOutline(b)}),t.on("element.changed",function(o){r.isSelected(o.element)&&n._updateSelectionOutline(r.get())})}Yr.$inject=["canvas","eventBus","selection"];Yr.prototype._updateSelectionOutline=function(e){var t=this._canvas.getLayer("selectionOutline");zn(t);var r=e.length>1,n=this._canvas.getContainer();if(Te(n)[r?"add":"remove"]("djs-multi-select"),!!r){var i=bo(ht(e)),a=te("rect");Z(a,B({rx:3},i)),Te(a).add("djs-selection-outline"),ce(t,a)}};function bo(e){return{x:e.x-tr,y:e.y-tr,width:e.width+tr*2,height:e.height+tr*2}}function ai(e,t,r,n){e.on("create.end",500,function(i){var a=i.context,o=a.canExecute,l=a.elements,u=a.hints||{},h=u.autoSelect;if(o){if(h===!1)return;Fe(h)?t.select(h):t.select(l.filter(_o))}}),e.on("connect.end",500,function(i){var a=i.context,o=a.connection;o&&t.select(o)}),e.on("shape.move.end",500,function(i){var a=i.previousSelection||[],o=n.get(i.context.shape.id),l=je(a,function(u){return o.id===u.id});l||t.select(o)}),e.on("element.click",function(i){if(Wt(i)){var a=i.element;a===r.getRootElement()&&(a=null);var o=t.isSelected(a),l=t.get().length>1,u=po(i)||fo(i);if(o&&l)return u?t.deselect(a):t.select(a);o?t.deselect(a):t.select(a,u)}})}ai.$inject=["eventBus","selection","canvas","elementRegistry"];function _o(e){return!e.hidden}const Eo={__init__:["selectionVisuals","selectionBehavior"],__depends__:[mo,vo],selection:["type",Mt],selectionVisuals:["type",Yr],selectionBehavior:["type",ai]};function si(e){this._counter=0,this._prefix=(e?e+"-":"")+Math.floor(Math.random()*1e9)+"-"}si.prototype.next=function(){return this._prefix+ ++this._counter};var wo=new si("ov"),xo=500;function ve(e,t,r,n){this._eventBus=t,this._canvas=r,this._elementRegistry=n,this._ids=wo,this._overlayDefaults=B({show:null,scale:!0},e&&e.defaults),this._overlays={},this._overlayContainers=[],this._overlayRoot=So(r.getContainer()),this._init()}ve.$inject=["config.overlays","eventBus","canvas","elementRegistry"];ve.prototype.get=function(e){if(We(e)&&(e={id:e}),We(e.element)&&(e.element=this._elementRegistry.get(e.element)),e.element){var t=this._getOverlayContainer(e.element,!0);return t?e.type?Rt(t.overlays,hn({type:e.type})):t.overlays.slice():[]}else return e.type?Rt(this._overlays,hn({type:e.type})):e.id?this._overlays[e.id]:null};ve.prototype.add=function(e,t,r){if(dt(t)&&(r=t,t=null),e.id||(e=this._elementRegistry.get(e)),!r.position)throw new Error("must specifiy overlay position");if(!r.html)throw new Error("must specifiy overlay html");if(!e)throw new Error("invalid element specified");var n=this._ids.next();return r=B({},this._overlayDefaults,r,{id:n,type:t,element:e,html:r.html}),this._addOverlay(r),n};ve.prototype.remove=function(e){var t=this.get(e)||[];Fe(t)||(t=[t]);var r=this;I(t,function(n){var i=r._getOverlayContainer(n.element,!0);if(n&&(dr(n.html),dr(n.htmlContainer),delete n.htmlContainer,delete n.element,delete r._overlays[n.id]),i){var a=i.overlays.indexOf(n);a!==-1&&i.overlays.splice(a,1)}})};ve.prototype.isShown=function(){return this._overlayRoot.style.display!=="none"};ve.prototype.show=function(){vr(this._overlayRoot)};ve.prototype.hide=function(){vr(this._overlayRoot,!1)};ve.prototype.clear=function(){this._overlays={},this._overlayContainers=[],qn(this._overlayRoot)};ve.prototype._updateOverlayContainer=function(e){var t=e.element,r=e.html,n=t.x,i=t.y;if(t.waypoints){var a=ht(t);n=a.x,i=a.y}oi(r,n,i),ys(e.html,"data-container-id",t.id)};ve.prototype._updateOverlay=function(e){var t=e.position,r=e.htmlContainer,n=e.element,i=t.left,a=t.top;if(t.right!==void 0){var o;n.waypoints?o=ht(n).width:o=n.width,i=t.right*-1+o}if(t.bottom!==void 0){var l;n.waypoints?l=ht(n).height:l=n.height,a=t.bottom*-1+l}oi(r,i||0,a||0),this._updateOverlayVisibilty(e,this._canvas.viewbox())};ve.prototype._createOverlayContainer=function(e){var t=He('<div class="djs-overlays" />');Re(t,{position:"absolute"}),this._overlayRoot.appendChild(t);var r={html:t,element:e,overlays:[]};return this._updateOverlayContainer(r),this._overlayContainers.push(r),r};ve.prototype._updateRoot=function(e){var t=e.scale||1,r="matrix("+[t,0,0,t,-1*e.x*t,-1*e.y*t].join(",")+")";li(this._overlayRoot,r)};ve.prototype._getOverlayContainer=function(e,t){var r=je(this._overlayContainers,function(n){return n.element===e});return!r&&!t?this._createOverlayContainer(e):r};ve.prototype._addOverlay=function(e){var t=e.id,r=e.element,n=e.html,i,a;n.get&&n.constructor.prototype.jquery&&(n=n.get(0)),We(n)&&(n=He(n)),a=this._getOverlayContainer(r),i=He('<div class="djs-overlay" data-overlay-id="'+t+'">'),Re(i,{position:"absolute"}),i.appendChild(n),e.type&&yr(i).add("djs-overlay-"+e.type);var o=this._canvas.findRoot(r),l=this._canvas.getRootElement();vr(i,o===l),e.htmlContainer=i,a.overlays.push(e),a.html.appendChild(i),this._overlays[t]=e,this._updateOverlay(e),this._updateOverlayVisibilty(e,this._canvas.viewbox())};ve.prototype._updateOverlayVisibilty=function(e,t){var r=e.show,n=this._canvas.findRoot(e.element),i=r&&r.minZoom,a=r&&r.maxZoom,o=e.htmlContainer,l=this._canvas.getRootElement(),u=!0;(n!==l||r&&(Pt(i)&&i>t.scale||Pt(a)&&a<t.scale))&&(u=!1),vr(o,u),this._updateOverlayScale(e,t)};ve.prototype._updateOverlayScale=function(e,t){var r=e.scale,n,i,a=e.htmlContainer,o,l="";r!==!0&&(r===!1?(n=1,i=1):(n=r.min,i=r.max),Pt(n)&&t.scale<n&&(o=(1/t.scale||1)*n),Pt(i)&&t.scale>i&&(o=(1/t.scale||1)*i)),Pt(o)&&(l="scale("+o+","+o+")"),li(a,l)};ve.prototype._updateOverlaysVisibilty=function(e){var t=this;I(this._overlays,function(r){t._updateOverlayVisibilty(r,e)})};ve.prototype._init=function(){var e=this._eventBus,t=this;function r(n){t._updateRoot(n),t._updateOverlaysVisibilty(n),t.show()}e.on("canvas.viewbox.changing",function(n){t.hide()}),e.on("canvas.viewbox.changed",function(n){r(n.viewbox)}),e.on(["shape.remove","connection.remove"],function(n){var i=n.element,a=t.get({element:i});I(a,function(u){t.remove(u.id)});var o=t._getOverlayContainer(i);if(o){dr(o.html);var l=t._overlayContainers.indexOf(o);l!==-1&&t._overlayContainers.splice(l,1)}}),e.on("element.changed",xo,function(n){var i=n.element,a=t._getOverlayContainer(i,!0);a&&(I(a.overlays,function(o){t._updateOverlay(o)}),t._updateOverlayContainer(a))}),e.on("element.marker.update",function(n){var i=t._getOverlayContainer(n.element,!0);i&&yr(i.html)[n.add?"add":"remove"](n.marker)}),e.on("root.set",function(){t._updateOverlaysVisibilty(t._canvas.viewbox())}),e.on("diagram.clear",this.clear,this)};function So(e){var t=He('<div class="djs-overlay-container" />');return Re(t,{position:"absolute",width:0,height:0}),e.insertBefore(t,e.firstChild),t}function oi(e,t,r){Re(e,{left:t+"px",top:r+"px"})}function vr(e,t){e.style.display=t===!1?"none":""}function li(e,t){e.style["transform-origin"]="top left",["","-ms-","-webkit-"].forEach(function(r){e.style[r+"transform"]=t})}const ui={__init__:["overlays"],overlays:["type",ve]};function ci(e,t,r,n){e.on("element.changed",function(i){var a=i.element;(a.parent||a===t.getRootElement())&&(i.gfx=r.getGraphics(a)),i.gfx&&e.fire(ni(a)+".changed",i)}),e.on("elements.changed",function(i){var a=i.elements;a.forEach(function(o){e.fire("element.changed",{element:o})}),n.updateContainments(a)}),e.on("shape.changed",function(i){n.update("shape",i.element,i.gfx)}),e.on("connection.changed",function(i){n.update("connection",i.element,i.gfx)})}ci.$inject=["eventBus","canvas","elementRegistry","graphicsFactory"];const Po={__init__:["changeSupport"],changeSupport:["type",ci]};var Ao=1e3;function Ae(e){this._eventBus=e}Ae.$inject=["eventBus"];function Ro(e,t){return function(r){return e.call(t||null,r.context,r.command,r)}}Ae.prototype.on=function(e,t,r,n,i,a){if((Ye(t)||nt(t))&&(a=i,i=n,n=r,r=t,t=null),Ye(r)&&(a=i,i=n,n=r,r=Ao),dt(i)&&(a=i,i=!1),!Ye(n))throw new Error("handlerFn must be a function");Fe(e)||(e=[e]);var o=this._eventBus;I(e,function(l){var u=["commandStack",l,t].filter(function(h){return h}).join(".");o.on(u,r,i?Ro(n,a):n,a)})};Ae.prototype.canExecute=Ze("canExecute");Ae.prototype.preExecute=Ze("preExecute");Ae.prototype.preExecuted=Ze("preExecuted");Ae.prototype.execute=Ze("execute");Ae.prototype.executed=Ze("executed");Ae.prototype.postExecute=Ze("postExecute");Ae.prototype.postExecuted=Ze("postExecuted");Ae.prototype.revert=Ze("revert");Ae.prototype.reverted=Ze("reverted");function Ze(e){return function(r,n,i,a,o){(Ye(r)||nt(r))&&(o=a,a=i,i=n,n=r,r=null),this.on(r,e,n,i,a,o)}}function Gr(e,t){t.invoke(Ae,this),this.executed(function(r){var n=r.context;n.rootElement?e.setRootElement(n.rootElement):n.rootElement=e.getRootElement()}),this.revert(function(r){var n=r.context;n.rootElement&&e.setRootElement(n.rootElement)})}Ke(Gr,Ae);Gr.$inject=["canvas","injector"];const Co={__init__:["rootElementsBehavior"],rootElementsBehavior:["type",Gr]};var To={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function ko(e){return e=""+e,e&&e.replace(/[&<>"']/g,function(t){return To[t]})}var No="_plane";function Xr(e){var t=e.id;return F(e,"bpmn:SubProcess")?Mo(t):t}function Mo(e){return e+No}var Do="bjs-breadcrumbs-shown";function pi(e,t,r){var n=He('<ul class="bjs-breadcrumbs"></ul>'),i=r.getContainer(),a=yr(i);i.appendChild(n);var o=[];e.on("element.changed",function(u){var h=u.element,b=le(h),y=je(o,function(k){return k===b});y&&l()});function l(u){u&&(o=Io(u));var h=o.map(function(y){var k=ko(y.name||y.id),R=He('<li><span class="bjs-crumb"><a title="'+k+'">'+k+"</a></span></li>"),V=r.findRoot(Xr(y))||r.findRoot(y.id);if(!V&&F(y,"bpmn:Process")){var $=t.find(function(W){var H=le(W);return H&&H.get("processRef")&&H.get("processRef")===y});V=r.findRoot($.id)}return R.addEventListener("click",function(){r.setRootElement(V)}),R});n.innerHTML="";var b=h.length>1;a.toggle(Do,b),h.forEach(function(y){n.appendChild(y)})}e.on("root.set",function(u){l(u.element)})}pi.$inject=["eventBus","elementRegistry","canvas"];function Io(e){for(var t=le(e),r=[],n=t;n;n=n.$parent)(F(n,"bpmn:SubProcess")||F(n,"bpmn:Process"))&&r.push(n);return r.reverse()}function fi(e,t){var r=null,n=new Oo;e.on("root.set",function(i){var a=i.element,o=t.viewbox(),l=n.get(a);if(n.set(r,{x:o.x,y:o.y,zoom:o.scale}),r=a,!(F(a,"bpmn:Collaboration")&&!l)){l=l||{x:0,y:0,zoom:1};var u=(o.x-l.x)*o.scale,h=(o.y-l.y)*o.scale;(u!==0||h!==0)&&t.scroll({dx:u,dy:h}),l.zoom!==o.scale&&t.zoom(l.zoom,{x:0,y:0})}}),e.on("diagram.clear",function(){n.clear(),r=null})}fi.$inject=["eventBus","canvas"];function Oo(){this._entries=[],this.set=function(e,t){var r=!1;for(var n in this._entries)if(this._entries[n][0]===e){this._entries[n][1]=t,r=!0;break}r||this._entries.push([e,t])},this.get=function(e){for(var t in this._entries)if(this._entries[t][0]===e)return this._entries[t][1];return null},this.clear=function(){this._entries.length=0},this.remove=function(e){var t=-1;for(var r in this._entries)if(this._entries[r][0]===e){t=r;break}t!==-1&&this._entries.splice(t,1)}}var An={x:180,y:160};function _t(e,t){this._eventBus=e,this._moddle=t;var r=this;e.on("import.render.start",1500,function(n,i){r._handleImport(i.definitions)})}_t.prototype._handleImport=function(e){if(e.diagrams){var t=this;this._definitions=e,this._processToDiagramMap={},e.diagrams.forEach(function(n){!n.plane||!n.plane.bpmnElement||(t._processToDiagramMap[n.plane.bpmnElement.id]=n)});var r=[];e.diagrams.forEach(function(n){var i=t._createNewDiagrams(n.plane);Array.prototype.push.apply(r,i)}),r.forEach(function(n){t._movePlaneElementsToOrigin(n.plane)})}};_t.prototype._createNewDiagrams=function(e){var t=this,r=[],n=[];e.get("planeElement").forEach(function(a){var o=a.bpmnElement;if(o){var l=o.$parent;F(o,"bpmn:SubProcess")&&!a.isExpanded&&r.push(o),Bo(o,e)&&n.push({diElement:a,parent:l})}});var i=[];return r.forEach(function(a){if(!t._processToDiagramMap[a.id]){var o=t._createDiagram(a);t._processToDiagramMap[a.id]=o,i.push(o)}}),n.forEach(function(a){for(var o=a.diElement,l=a.parent;l&&r.indexOf(l)===-1;)l=l.$parent;if(l){var u=t._processToDiagramMap[l.id];t._moveToDiPlane(o,u.plane)}}),i};_t.prototype._movePlaneElementsToOrigin=function(e){var t=e.get("planeElement"),r=Lo(e),n={x:r.x-An.x,y:r.y-An.y};t.forEach(function(i){i.waypoint?i.waypoint.forEach(function(a){a.x=a.x-n.x,a.y=a.y-n.y}):i.bounds&&(i.bounds.x=i.bounds.x-n.x,i.bounds.y=i.bounds.y-n.y)})};_t.prototype._moveToDiPlane=function(e,t){var r=di(e),n=r.plane.get("planeElement");n.splice(n.indexOf(e),1),t.get("planeElement").push(e)};_t.prototype._createDiagram=function(e){var t=this._moddle.create("bpmndi:BPMNPlane",{bpmnElement:e}),r=this._moddle.create("bpmndi:BPMNDiagram",{plane:t});return t.$parent=r,t.bpmnElement=e,r.$parent=this._definitions,this._definitions.diagrams.push(r),r};_t.$inject=["eventBus","moddle"];function di(e){return F(e,"bpmndi:BPMNDiagram")?e:di(e.$parent)}function Lo(e){var t={top:1/0,right:-1/0,bottom:-1/0,left:1/0};return e.planeElement.forEach(function(r){if(r.bounds){var n=Br(r.bounds);t.top=Math.min(n.top,t.top),t.left=Math.min(n.left,t.left)}}),eo(t)}function Bo(e,t){var r=e.$parent;return!(!F(r,"bpmn:SubProcess")||r===t.bpmnElement||Ta(e,["bpmn:DataInputAssociation","bpmn:DataOutputAssociation"]))}var rr=250,Fo='<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M4.81801948,3.50735931 L10.4996894,9.1896894 L10.5,4 L12,4 L12,12 L4,12 L4,10.5 L9.6896894,10.4996894 L3.75735931,4.56801948 C3.46446609,4.27512627 3.46446609,3.80025253 3.75735931,3.50735931 C4.05025253,3.21446609 4.52512627,3.21446609 4.81801948,3.50735931 Z"/></svg>',jo="bjs-drilldown-empty";function ut(e,t,r,n){Ae.call(this,t),this._canvas=e,this._eventBus=t,this._elementRegistry=r,this._overlays=n;var i=this;this.executed("shape.toggleCollapse",rr,function(a){var o=a.shape;i._canDrillDown(o)?i._addOverlay(o):i._removeOverlay(o)},!0),this.reverted("shape.toggleCollapse",rr,function(a){var o=a.shape;i._canDrillDown(o)?i._addOverlay(o):i._removeOverlay(o)},!0),this.executed(["shape.create","shape.move","shape.delete"],rr,function(a){var o=a.oldParent,l=a.newParent||a.parent,u=a.shape;i._canDrillDown(u)&&i._addOverlay(u),i._updateDrilldownOverlay(o),i._updateDrilldownOverlay(l),i._updateDrilldownOverlay(u)},!0),this.reverted(["shape.create","shape.move","shape.delete"],rr,function(a){var o=a.oldParent,l=a.newParent||a.parent,u=a.shape;i._canDrillDown(u)&&i._addOverlay(u),i._updateDrilldownOverlay(o),i._updateDrilldownOverlay(l),i._updateDrilldownOverlay(u)},!0),t.on("import.render.complete",function(){r.filter(function(a){return i._canDrillDown(a)}).map(function(a){i._addOverlay(a)})})}Ke(ut,Ae);ut.prototype._updateDrilldownOverlay=function(e){var t=this._canvas;if(e){var r=t.findRoot(e);r&&this._updateOverlayVisibility(r)}};ut.prototype._canDrillDown=function(e){var t=this._canvas;return F(e,"bpmn:SubProcess")&&t.findRoot(Xr(e))};ut.prototype._updateOverlayVisibility=function(e){var t=this._overlays,r=le(e),n=t.get({element:r.id,type:"drilldown"})[0];if(n){var i=r&&r.get("flowElements")&&r.get("flowElements").length;yr(n.html).toggle(jo,!i)}};ut.prototype._addOverlay=function(e){var t=this._canvas,r=this._overlays,n=r.get({element:e,type:"drilldown"});n.length&&this._removeOverlay(e);var i=He('<button class="bjs-drilldown">'+Fo+"</button>");i.addEventListener("click",function(){t.setRootElement(t.findRoot(Xr(e)))}),r.add(e,"drilldown",{position:{bottom:-7,right:-8},html:i}),this._updateOverlayVisibility(e)};ut.prototype._removeOverlay=function(e){var t=this._overlays;t.remove({element:e,type:"drilldown"})};ut.$inject=["canvas","eventBus","elementRegistry","overlays"];const Vo={__depends__:[ui,Po,Co],__init__:["drilldownBreadcrumbs","drilldownOverlayBehavior","drilldownCentering","subprocessCompatibility"],drilldownBreadcrumbs:["type",pi],drilldownCentering:["type",fi],drilldownOverlayBehavior:["type",ut],subprocessCompatibility:["type",_t]},$o=/^class[ {]/;function Uo(e){return $o.test(e.toString())}function Jr(e){return Array.isArray(e)}function Tr(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function sr(...e){e.length===1&&Jr(e[0])&&(e=e[0]),e=[...e];const t=e.pop();return t.$inject=e,t}const zo=/constructor\s*[^(]*\(\s*([^)]*)\)/m,Wo=/^(?:async\s+)?(?:function\s*[^(]*)?(?:\(\s*([^)]*)\)|(\w+))/m,Ho=/\/\*([^*]*)\*\//m;function Ko(e){if(typeof e!="function")throw new Error('Cannot annotate "'.concat(e,'". Expected a function!'));const t=e.toString().match(Uo(e)?zo:Wo);if(!t)return[];const r=t[1]||t[2];return r&&r.split(",").map(n=>{const i=n.match(Ho);return(i&&i[1]||n).trim()})||[]}function hi(e,t){t=t||{get:function(v,N){if(r.push(v),N===!1)return null;throw o('No provider for "'.concat(v,'"!'))}};const r=[],n=this._providers=Object.create(t._providers||null),i=this._instances=Object.create(null),a=i.injector=this,o=function(v){const N=r.join(" -> ");return r.length=0,new Error(N?"".concat(v," (Resolving: ").concat(N,")"):v)};function l(v,N){if(!n[v]&&v.indexOf(".")!==-1){const w=v.split(".");let S=l(w.shift());for(;w.length;)S=S[w.shift()];return S}if(Tr(i,v))return i[v];if(Tr(n,v)){if(r.indexOf(v)!==-1)throw r.push(v),o("Cannot resolve circular dependency!");return r.push(v),i[v]=n[v][0](n[v][1]),r.pop(),i[v]}return t.get(v,N)}function u(v,N){if(typeof N>"u"&&(N={}),typeof v!="function")if(Jr(v))v=sr(v.slice());else throw o('Cannot invoke "'.concat(v,'". Expected a function!'));const S=(v.$inject||Ko(v)).map(C=>Tr(N,C)?N[C]:l(C));return{fn:v,dependencies:S}}function h(v){const{fn:N,dependencies:w}=u(v),S=Function.prototype.bind.apply(N,[null].concat(w));return new S}function b(v,N,w){const{fn:S,dependencies:C}=u(v,w);return S.apply(N,C)}function y(v){return sr(N=>v.get(N))}function k(v,N){if(N&&N.length){const w=Object.create(null),S=Object.create(null),C=[],P=[],_=[];let g,x,D,T;for(let M in n)g=n[M],N.indexOf(M)!==-1&&(g[2]==="private"?(x=C.indexOf(g[3]),x===-1?(D=g[3].createChild([],N),T=y(D),C.push(g[3]),P.push(D),_.push(T),w[M]=[T,M,"private",D]):w[M]=[_[x],M,"private",P[x]]):w[M]=[g[2],g[1]],S[M]=!0),(g[2]==="factory"||g[2]==="type")&&g[1].$scope&&N.forEach(O=>{g[1].$scope.indexOf(O)!==-1&&(w[M]=[g[2],g[1]],S[O]=!0)});N.forEach(M=>{if(!S[M])throw new Error('No provider for "'+M+'". Cannot use provider from the parent!')}),v.unshift(w)}return new hi(v,a)}const R={factory:b,type:h,value:function(v){return v}};function V(v,N){const w=v.__init__||[];return function(){w.forEach(S=>{typeof S=="string"?N.get(S):N.invoke(S)})}}function $(v){const N=v.__exports__;if(N){const w=v.__modules__,S=Object.keys(v).reduce((x,D)=>(D!=="__exports__"&&D!=="__modules__"&&D!=="__init__"&&D!=="__depends__"&&(x[D]=v[D]),x),Object.create(null)),C=(w||[]).concat(S),P=k(C),_=sr(function(x){return P.get(x)});N.forEach(function(x){n[x]=[_,x,"private",P]});const g=(v.__init__||[]).slice();return g.unshift(function(){P.init()}),v=Object.assign({},v,{__init__:g}),V(v,P)}return Object.keys(v).forEach(function(w){if(w==="__init__"||w==="__depends__")return;if(v[w][2]==="private"){n[w]=v[w];return}const S=v[w][0],C=v[w][1];n[w]=[R[S],qo(S,C),S]}),V(v,a)}function W(v,N){return v.indexOf(N)!==-1||(v=(N.__depends__||[]).reduce(W,v),v.indexOf(N)!==-1)?v:v.concat(N)}function H(v){const N=v.reduce(W,[]).map($);let w=!1;return function(){w||(w=!0,N.forEach(S=>S()))}}this.get=l,this.invoke=b,this.instantiate=h,this.createChild=k,this.init=H(e)}function qo(e,t){return e!=="value"&&Jr(t)&&(t=sr(t.slice())),t}var Yo=1;function ct(e,t){Je.call(this,e,Yo),this.CONNECTION_STYLE=t.style(["no-fill"],{strokeWidth:5,stroke:"fuchsia"}),this.SHAPE_STYLE=t.style({fill:"white",stroke:"fuchsia",strokeWidth:2}),this.FRAME_STYLE=t.style(["no-fill"],{stroke:"fuchsia",strokeDasharray:4,strokeWidth:2})}Ke(ct,Je);ct.prototype.canRender=function(){return!0};ct.prototype.drawShape=function(t,r,n){var i=te("rect");return Z(i,{x:0,y:0,width:r.width||0,height:r.height||0}),ii(r)?Z(i,B({},this.FRAME_STYLE,n||{})):Z(i,B({},this.SHAPE_STYLE,n||{})),ce(t,i),i};ct.prototype.drawConnection=function(t,r,n){var i=Wr(r.waypoints,B({},this.CONNECTION_STYLE,n||{}));return ce(t,i),i};ct.prototype.getShapePath=function(t){var r=t.x,n=t.y,i=t.width,a=t.height,o=[["M",r,n],["l",i,0],["l",0,a],["l",-i,0],["z"]];return gt(o)};ct.prototype.getConnectionPath=function(t){var r=t.waypoints,n,i,a=[];for(n=0;i=r[n];n++)i=i.original||i,a.push([n===0?"M":"L",i.x,i.y]);return gt(a)};ct.$inject=["eventBus","styles"];function Go(){var e={"no-fill":{fill:"none"},"no-border":{strokeOpacity:0},"no-events":{pointerEvents:"none"}},t=this;this.cls=function(r,n,i){var a=this.style(n,i);return B(a,{class:r})},this.style=function(r,n){!Fe(r)&&!n&&(n=r,r=[]);var i=Ct(r,function(a,o){return B(a,e[o]||{})},{});return n?B(i,n):i},this.computeStyle=function(r,n,i){return Fe(n)||(i=n,n=[]),t.style(n||[],B({},i,r||{}))}}const Xo={__init__:["defaultRenderer"],defaultRenderer:["type",ct],styles:["type",Go]};function Jo(e,t){if(!e||!t)return-1;var r=e.indexOf(t);return r!==-1&&e.splice(r,1),r}function Qo(e,t,r){if(!(!e||!t)){typeof r!="number"&&(r=-1);var n=e.indexOf(t);if(n!==-1){if(n===r)return;if(r!==-1)e.splice(n,1);else return}r!==-1?e.splice(r,0,t):e.push(t)}}function or(e,t){return Math.round(e*t)/t}function Rn(e){return nt(e)?e+"px":e}function Zo(e){for(;e.parent;)e=e.parent;return e}function el(e){e=B({},{width:"100%",height:"100%"},e);const t=e.container||document.body,r=document.createElement("div");return r.setAttribute("class","djs-container djs-parent"),Re(r,{position:"relative",overflow:"hidden",width:Rn(e.width),height:Rn(e.height)}),t.appendChild(r),r}function mi(e,t,r){const n=te("g");Te(n).add(t);const i=r!==void 0?r:e.childNodes.length-1;return e.insertBefore(n,e.childNodes[i]||null),n}const tl="base",yi=0,rl=1,nl={shape:["x","y","width","height"],connection:["waypoints"]};function j(e,t,r,n){this._eventBus=t,this._elementRegistry=n,this._graphicsFactory=r,this._rootsIdx=0,this._layers={},this._planes=[],this._rootElement=null,this._init(e||{})}j.$inject=["config.canvas","eventBus","graphicsFactory","elementRegistry"];j.prototype._init=function(e){const t=this._eventBus,r=this._container=el(e),n=this._svg=te("svg");Z(n,{width:"100%",height:"100%"}),ce(r,n);const i=this._viewport=mi(n,"viewport");e.deferUpdate&&(this._viewboxChanged=Sa(it(this._viewboxChanged,this),300)),t.on("diagram.init",()=>{t.fire("canvas.init",{svg:n,viewport:i})}),t.on(["shape.added","connection.added","shape.removed","connection.removed","elements.changed","root.set"],()=>{delete this._cachedViewbox}),t.on("diagram.destroy",500,this._destroy,this),t.on("diagram.clear",500,this._clear,this)};j.prototype._destroy=function(){this._eventBus.fire("canvas.destroy",{svg:this._svg,viewport:this._viewport});const e=this._container.parentNode;e&&e.removeChild(this._container),delete this._svg,delete this._container,delete this._layers,delete this._planes,delete this._rootElement,delete this._viewport};j.prototype._clear=function(){this._elementRegistry.getAll().forEach(t=>{const r=ni(t);r==="root"?this.removeRootElement(t):this._removeElement(t,r)}),this._planes=[],this._rootElement=null,delete this._cachedViewbox};j.prototype.getDefaultLayer=function(){return this.getLayer(tl,yi)};j.prototype.getLayer=function(e,t){if(!e)throw new Error("must specify a name");let r=this._layers[e];if(r||(r=this._layers[e]=this._createLayer(e,t)),typeof t<"u"&&r.index!==t)throw new Error("layer <"+e+"> already created at index <"+t+">");return r.group};j.prototype._getChildIndex=function(e){return Ct(this._layers,function(t,r){return r.visible&&e>=r.index&&t++,t},0)};j.prototype._createLayer=function(e,t){typeof t>"u"&&(t=rl);const r=this._getChildIndex(t);return{group:mi(this._viewport,"layer-"+e,r),index:t,visible:!0}};j.prototype.showLayer=function(e){if(!e)throw new Error("must specify a name");const t=this._layers[e];if(!t)throw new Error("layer <"+e+"> does not exist");const r=this._viewport,n=t.group,i=t.index;if(t.visible)return n;const a=this._getChildIndex(i);return r.insertBefore(n,r.childNodes[a]||null),t.visible=!0,n};j.prototype.hideLayer=function(e){if(!e)throw new Error("must specify a name");const t=this._layers[e];if(!t)throw new Error("layer <"+e+"> does not exist");const r=t.group;return t.visible&&(zt(r),t.visible=!1),r};j.prototype._removeLayer=function(e){const t=this._layers[e];t&&(delete this._layers[e],zt(t.group))};j.prototype.getActiveLayer=function(){const e=this._findPlaneForRoot(this.getRootElement());return e?e.layer:null};j.prototype.findRoot=function(e){return typeof e=="string"&&(e=this._elementRegistry.get(e)),e?(this._findPlaneForRoot(Zo(e))||{}).rootElement:void 0};j.prototype.getRootElements=function(){return this._planes.map(function(e){return e.rootElement})};j.prototype._findPlaneForRoot=function(e){return je(this._planes,function(t){return t.rootElement===e})};j.prototype.getContainer=function(){return this._container};j.prototype._updateMarker=function(e,t,r){let n;e.id||(e=this._elementRegistry.get(e)),n=this._elementRegistry._elements[e.id],n&&(I([n.gfx,n.secondaryGfx],function(i){i&&(r?Te(i).add(t):Te(i).remove(t))}),this._eventBus.fire("element.marker.update",{element:e,gfx:n.gfx,marker:t,add:!!r}))};j.prototype.addMarker=function(e,t){this._updateMarker(e,t,!0)};j.prototype.removeMarker=function(e,t){this._updateMarker(e,t,!1)};j.prototype.hasMarker=function(e,t){e.id||(e=this._elementRegistry.get(e));const r=this.getGraphics(e);return Te(r).has(t)};j.prototype.toggleMarker=function(e,t){this.hasMarker(e,t)?this.removeMarker(e,t):this.addMarker(e,t)};j.prototype.getRootElement=function(){const e=this._rootElement;return e||this._planes.length?e:this.setRootElement(this.addRootElement(null))};j.prototype.addRootElement=function(e){const t=this._rootsIdx++;e||(e={id:"__implicitroot_"+t,children:[],isImplicit:!0});const r=e.layer="root-"+t;this._ensureValid("root",e);const n=this.getLayer(r,yi);return this.hideLayer(r),this._addRoot(e,n),this._planes.push({rootElement:e,layer:n}),e};j.prototype.removeRootElement=function(e){if(typeof e=="string"&&(e=this._elementRegistry.get(e)),!!this._findPlaneForRoot(e))return this._removeRoot(e),this._removeLayer(e.layer),this._planes=this._planes.filter(function(r){return r.rootElement!==e}),this._rootElement===e&&(this._rootElement=null),e};j.prototype.setRootElement=function(e){if(e===this._rootElement)return;let t;if(!e)throw new Error("rootElement required");return t=this._findPlaneForRoot(e),t||(e=this.addRootElement(e)),this._setRoot(e),e};j.prototype._removeRoot=function(e){const t=this._elementRegistry,r=this._eventBus;r.fire("root.remove",{element:e}),r.fire("root.removed",{element:e}),t.remove(e)};j.prototype._addRoot=function(e,t){const r=this._elementRegistry,n=this._eventBus;n.fire("root.add",{element:e}),r.add(e,t),n.fire("root.added",{element:e,gfx:t})};j.prototype._setRoot=function(e,t){const r=this._rootElement;r&&(this._elementRegistry.updateGraphics(r,null,!0),this.hideLayer(r.layer)),e&&(t||(t=this._findPlaneForRoot(e).layer),this._elementRegistry.updateGraphics(e,this._svg,!0),this.showLayer(e.layer)),this._rootElement=e,this._eventBus.fire("root.set",{element:e})};j.prototype._ensureValid=function(e,t){if(!t.id)throw new Error("element must have an id");if(this._elementRegistry.get(t.id))throw new Error("element <"+t.id+"> already exists");const r=nl[e];if(!Fn(r,function(i){return typeof t[i]<"u"}))throw new Error("must supply { "+r.join(", ")+" } with "+e)};j.prototype._setParent=function(e,t,r){Qo(t.children,e,r),e.parent=t};j.prototype._addElement=function(e,t,r,n){r=r||this.getRootElement();const i=this._eventBus,a=this._graphicsFactory;this._ensureValid(e,t),i.fire(e+".add",{element:t,parent:r}),this._setParent(t,r,n);const o=a.create(e,t,n);return this._elementRegistry.add(t,o),a.update(e,t,o),i.fire(e+".added",{element:t,gfx:o}),t};j.prototype.addShape=function(e,t,r){return this._addElement("shape",e,t,r)};j.prototype.addConnection=function(e,t,r){return this._addElement("connection",e,t,r)};j.prototype._removeElement=function(e,t){const r=this._elementRegistry,n=this._graphicsFactory,i=this._eventBus;if(e=r.get(e.id||e),!!e)return i.fire(t+".remove",{element:e}),n.remove(e),Jo(e.parent&&e.parent.children,e),e.parent=null,i.fire(t+".removed",{element:e}),r.remove(e),e};j.prototype.removeShape=function(e){return this._removeElement(e,"shape")};j.prototype.removeConnection=function(e){return this._removeElement(e,"connection")};j.prototype.getGraphics=function(e,t){return this._elementRegistry.getGraphics(e,t)};j.prototype._changeViewbox=function(e){this._eventBus.fire("canvas.viewbox.changing"),e.apply(this),this._cachedViewbox=null,this._viewboxChanged()};j.prototype._viewboxChanged=function(){this._eventBus.fire("canvas.viewbox.changed",{viewbox:this.viewbox()})};j.prototype.viewbox=function(e){if(e===void 0&&this._cachedViewbox)return this._cachedViewbox;const t=this._viewport,r=this.getSize();let n,i,a,o,l,u,h;if(e)this._changeViewbox(function(){l=Math.min(r.width/e.width,r.height/e.height);const b=this._svg.createSVGMatrix().scale(l).translate(-e.x,-e.y);$t(t,b)});else return a=this._rootElement?this.getActiveLayer():null,n=a&&a.getBBox()||{},o=$t(t),i=o?o.matrix:Wa(),l=or(i.a,1e3),u=or(-i.e||0,1e3),h=or(-i.f||0,1e3),e=this._cachedViewbox={x:u?u/l:0,y:h?h/l:0,width:r.width/l,height:r.height/l,scale:l,inner:{width:n.width||0,height:n.height||0,x:n.x||0,y:n.y||0},outer:r},e;return e};j.prototype.scroll=function(e){const t=this._viewport;let r=t.getCTM();return e&&this._changeViewbox(function(){e=B({dx:0,dy:0},e||{}),r=this._svg.createSVGMatrix().translate(e.dx,e.dy).multiply(r),gi(t,r)}),{x:r.e,y:r.f}};j.prototype.scrollToElement=function(e,t){let r=100;typeof e=="string"&&(e=this._elementRegistry.get(e));const n=this.findRoot(e);if(n!==this.getRootElement()&&this.setRootElement(n),n===e)return;t||(t={}),typeof t=="number"&&(r=t),t={top:t.top||r,right:t.right||r,bottom:t.bottom||r,left:t.left||r};const i=ht(e),a=Br(i),o=this.viewbox(),l=this.zoom();let u,h;o.y+=t.top/l,o.x+=t.left/l,o.width-=(t.right+t.left)/l,o.height-=(t.bottom+t.top)/l;const b=Br(o);if(!(i.width<o.width&&i.height<o.height))u=i.x-o.x,h=i.y-o.y;else{const k=Math.max(0,a.right-b.right),R=Math.min(0,a.left-b.left),V=Math.max(0,a.bottom-b.bottom),$=Math.min(0,a.top-b.top);u=k||R,h=V||$}this.scroll({dx:-u*l,dy:-h*l})};j.prototype.zoom=function(e,t){if(!e)return this.viewbox(e).scale;if(e==="fit-viewport")return this._fitViewport(t);let r,n;return this._changeViewbox(function(){typeof t!="object"&&(r=this.viewbox().outer,t={x:r.width/2,y:r.height/2}),n=this._setZoom(e,t)}),or(n.a,1e3)};function gi(e,t){const r="matrix("+t.a+","+t.b+","+t.c+","+t.d+","+t.e+","+t.f+")";e.setAttribute("transform",r)}j.prototype._fitViewport=function(e){const t=this.viewbox(),r=t.outer,n=t.inner;let i,a;return n.x>=0&&n.y>=0&&n.x+n.width<=r.width&&n.y+n.height<=r.height&&!e?a={x:0,y:0,width:Math.max(n.width+n.x,r.width),height:Math.max(n.height+n.y,r.height)}:(i=Math.min(1,r.width/n.width,r.height/n.height),a={x:n.x+(e?n.width/2-r.width/i/2:0),y:n.y+(e?n.height/2-r.height/i/2:0),width:r.width/i,height:r.height/i}),this.viewbox(a),this.viewbox(!1).scale};j.prototype._setZoom=function(e,t){const r=this._svg,n=this._viewport,i=r.createSVGMatrix(),a=r.createSVGPoint();let o,l,u,h,b;u=n.getCTM();const y=u.a;return t?(o=B(a,t),l=o.matrixTransform(u.inverse()),h=i.translate(l.x,l.y).scale(1/y*e).translate(-l.x,-l.y),b=u.multiply(h)):b=i.scale(e),gi(this._viewport,b),b};j.prototype.getSize=function(){return{width:this._container.clientWidth,height:this._container.clientHeight}};j.prototype.getAbsoluteBBox=function(e){const t=this.viewbox();let r;e.waypoints?r=this.getGraphics(e).getBBox():r=e;const n=r.x*t.scale-t.x*t.scale,i=r.y*t.scale-t.y*t.scale,a=r.width*t.scale,o=r.height*t.scale;return{x:n,y:i,width:a,height:o}};j.prototype.resized=function(){delete this._cachedViewbox,this._eventBus.fire("canvas.resized")};var kt="data-element-id";function ke(e){this._elements={},this._eventBus=e}ke.$inject=["eventBus"];ke.prototype.add=function(e,t,r){var n=e.id;this._validateId(n),Z(t,kt,n),r&&Z(r,kt,n),this._elements[n]={element:e,gfx:t,secondaryGfx:r}};ke.prototype.remove=function(e){var t=this._elements,r=e.id||e,n=r&&t[r];n&&(Z(n.gfx,kt,""),n.secondaryGfx&&Z(n.secondaryGfx,kt,""),delete t[r])};ke.prototype.updateId=function(e,t){this._validateId(t),typeof e=="string"&&(e=this.get(e)),this._eventBus.fire("element.updateId",{element:e,newId:t});var r=this.getGraphics(e),n=this.getGraphics(e,!0);this.remove(e),e.id=t,this.add(e,r,n)};ke.prototype.updateGraphics=function(e,t,r){var n=e.id||e,i=this._elements[n];return r?i.secondaryGfx=t:i.gfx=t,t&&Z(t,kt,n),t};ke.prototype.get=function(e){var t;typeof e=="string"?t=e:t=e&&Z(e,kt);var r=this._elements[t];return r&&r.element};ke.prototype.filter=function(e){var t=[];return this.forEach(function(r,n){e(r,n)&&t.push(r)}),t};ke.prototype.find=function(e){for(var t=this._elements,r=Object.keys(t),n=0;n<r.length;n++){var i=r[n],a=t[i],o=a.element,l=a.gfx;if(e(o,l))return o}};ke.prototype.getAll=function(){return this.filter(function(e){return e})};ke.prototype.forEach=function(e){var t=this._elements;Object.keys(t).forEach(function(r){var n=t[r],i=n.element,a=n.gfx;return e(i,a)})};ke.prototype.getGraphics=function(e,t){var r=e.id||e,n=this._elements[r];return n&&(t?n.secondaryGfx:n.gfx)};ke.prototype._validateId=function(e){if(!e)throw new Error("element must have an id");if(this._elements[e])throw new Error("element with id "+e+" already added")};var Qr={exports:{}},br={};function il(e,t,r,n){var i=r.inverse;return Object.defineProperty(e,"remove",{value:function(a){var o=this.indexOf(a);return o!==-1&&(this.splice(o,1),t.unset(a,i,n)),a}}),Object.defineProperty(e,"contains",{value:function(a){return this.indexOf(a)!==-1}}),Object.defineProperty(e,"add",{value:function(a,o){var l=this.indexOf(a);if(typeof o>"u"){if(l!==-1)return;o=this.length}l!==-1&&this.splice(l,1),this.splice(o,0,a),l===-1&&t.set(a,i,n)}}),Object.defineProperty(e,"__refs_collection",{value:!0}),e}function al(e){return e.__refs_collection===!0}br.extend=il;br.isExtended=al;var vi=br;function sl(e,t){return Object.prototype.hasOwnProperty.call(e,t.name||t)}function bi(e,t,r){var n=vi.extend(r[t.name]||[],e,t,r);Object.defineProperty(r,t.name,{enumerable:t.enumerable,value:n}),n.length&&n.forEach(function(i){e.set(i,t.inverse,r)})}function ol(e,t,r){var n=t.inverse,i=r[t.name];Object.defineProperty(r,t.name,{configurable:t.configurable,enumerable:t.enumerable,get:function(){return i},set:function(a){if(a!==i){var o=i;i=null,o&&e.unset(o,n,r),i=a,e.set(i,n,r)}}})}function ot(e,t){if(!(this instanceof ot))return new ot(e,t);e.inverse=t,t.inverse=e,this.props={},this.props[e.name]=e,this.props[t.name]=t}ot.prototype.bind=function(e,t){if(typeof t=="string"){if(!this.props[t])throw new Error("no property <"+t+"> in ref");t=this.props[t]}t.collection?bi(this,t,e):ol(this,t,e)};ot.prototype.ensureRefsCollection=function(e,t){var r=e[t.name];return vi.isExtended(r)||bi(this,t,e),r};ot.prototype.ensureBound=function(e,t){sl(e,t)||this.bind(e,t)};ot.prototype.unset=function(e,t,r){e&&(this.ensureBound(e,t),t.collection?this.ensureRefsCollection(e,t).remove(r):e[t.name]=void 0)};ot.prototype.set=function(e,t,r){e&&(this.ensureBound(e,t),t.collection?this.ensureRefsCollection(e,t).add(r):e[t.name]=r)};var ll=ot;Qr.exports=ll;Qr.exports.Collection=br;var ul=Qr.exports;const Ht=la(ul);var Zr=new Ht({name:"children",enumerable:!0,collection:!0},{name:"parent"}),_i=new Ht({name:"labels",enumerable:!0,collection:!0},{name:"labelTarget"}),Cn=new Ht({name:"attachers",collection:!0},{name:"host"}),Ei=new Ht({name:"outgoing",collection:!0},{name:"source"}),wi=new Ht({name:"incoming",collection:!0},{name:"target"});function Kt(){Object.defineProperty(this,"businessObject",{writable:!0}),Object.defineProperty(this,"label",{get:function(){return this.labels[0]},set:function(e){var t=this.label,r=this.labels;!e&&t?r.remove(t):r.add(e,0)}}),Zr.bind(this,"parent"),_i.bind(this,"labels"),Ei.bind(this,"outgoing"),wi.bind(this,"incoming")}function qt(){Kt.call(this),Zr.bind(this,"children"),Cn.bind(this,"host"),Cn.bind(this,"attachers")}Ke(qt,Kt);function xi(){Kt.call(this),Zr.bind(this,"children")}Ke(xi,qt);function Si(){qt.call(this),_i.bind(this,"labelTarget")}Ke(Si,qt);function Pi(){Kt.call(this),Ei.bind(this,"source"),wi.bind(this,"target")}Ke(Pi,Kt);var cl={connection:Pi,shape:qt,label:Si,root:xi};function pl(e,t){var r=cl[e];if(!r)throw new Error("unknown type: <"+e+">");return B(new r,t)}function Dt(){this._uid=12}Dt.prototype.createRoot=function(e){return this.create("root",e)};Dt.prototype.createLabel=function(e){return this.create("label",e)};Dt.prototype.createShape=function(e){return this.create("shape",e)};Dt.prototype.createConnection=function(e){return this.create("connection",e)};Dt.prototype.create=function(e,t){return t=B({},t||{}),t.id||(t.id=e+"_"+this._uid++),pl(e,t)};var hr="__fn",Ai=1e3,fl=Array.prototype.slice;function Ce(){this._listeners={},this.on("diagram.destroy",1,this._destroy,this)}Ce.prototype.on=function(e,t,r,n){if(e=Fe(e)?e:[e],Ye(t)&&(n=r,r=t,t=Ai),!nt(t))throw new Error("priority must be a number");var i=r;n&&(i=it(r,n),i[hr]=r[hr]||r);var a=this;e.forEach(function(o){a._addListener(o,{priority:t,callback:i,next:null})})};Ce.prototype.once=function(e,t,r,n){var i=this;if(Ye(t)&&(n=r,r=t,t=Ai),!nt(t))throw new Error("priority must be a number");function a(){a.__isTomb=!0;var o=r.apply(n,arguments);return i.off(e,a),o}a[hr]=r,this.on(e,t,a)};Ce.prototype.off=function(e,t){e=Fe(e)?e:[e];var r=this;e.forEach(function(n){r._removeListener(n,t)})};Ce.prototype.createEvent=function(e){var t=new Yt;return t.init(e),t};Ce.prototype.fire=function(e,t){var r,n,i,a;if(a=fl.call(arguments),typeof e=="object"&&(t=e,e=t.type),!e)throw new Error("no event type specified");if(n=this._listeners[e],!!n){t instanceof Yt?r=t:r=this.createEvent(t),a[0]=r;var o=r.type;e!==o&&(r.type=e);try{i=this._invokeListeners(r,a,n)}finally{e!==o&&(r.type=o)}return i===void 0&&r.defaultPrevented&&(i=!1),i}};Ce.prototype.handleError=function(e){return this.fire("error",{error:e})===!1};Ce.prototype._destroy=function(){this._listeners={}};Ce.prototype._invokeListeners=function(e,t,r){for(var n;r&&!e.cancelBubble;)n=this._invokeListener(e,t,r),r=r.next;return n};Ce.prototype._invokeListener=function(e,t,r){var n;if(r.callback.__isTomb)return n;try{n=dl(r.callback,t),n!==void 0&&(e.returnValue=n,e.stopPropagation()),n===!1&&e.preventDefault()}catch(i){if(!this.handleError(i))throw console.error("unhandled error in event listener",i),i}return n};Ce.prototype._addListener=function(e,t){var r=this._getListeners(e),n;if(!r){this._setListeners(e,t);return}for(;r;){if(r.priority<t.priority){t.next=r,n?n.next=t:this._setListeners(e,t);return}n=r,r=r.next}n.next=t};Ce.prototype._getListeners=function(e){return this._listeners[e]};Ce.prototype._setListeners=function(e,t){this._listeners[e]=t};Ce.prototype._removeListener=function(e,t){var r=this._getListeners(e),n,i,a;if(!t){this._setListeners(e,null);return}for(;r;)n=r.next,a=r.callback,(a===t||a[hr]===t)&&(i?i.next=n:this._setListeners(e,n)),i=r,r=n};function Yt(){}Yt.prototype.stopPropagation=function(){this.cancelBubble=!0};Yt.prototype.preventDefault=function(){this.defaultPrevented=!0};Yt.prototype.init=function(e){B(this,e||{})};function dl(e,t){return e.apply(null,t)}function hl(e){return e.childNodes[0]}function ml(e){return e.parentNode.childNodes[1]}function Ne(e,t){this._eventBus=e,this._elementRegistry=t}Ne.$inject=["eventBus","elementRegistry"];Ne.prototype._getChildrenContainer=function(e){var t=this._elementRegistry.getGraphics(e),r;return e.parent?(r=ml(t),r||(r=te("g"),Te(r).add("djs-children"),ce(t.parentNode,r))):r=t,r};Ne.prototype._clear=function(e){var t=hl(e);return qn(t),t};Ne.prototype._createContainer=function(e,t,r,n){var i=te("g");Te(i).add("djs-group"),typeof r<"u"?Ri(i,t,t.childNodes[r]):ce(t,i);var a=te("g");Te(a).add("djs-element"),Te(a).add("djs-"+e),n&&Te(a).add("djs-frame"),ce(i,a);var o=te("g");return Te(o).add("djs-visual"),ce(a,o),a};Ne.prototype.create=function(e,t,r){var n=this._getChildrenContainer(t.parent);return this._createContainer(e,n,r,ii(t))};Ne.prototype.updateContainments=function(e){var t=this,r=this._elementRegistry,n;n=Ct(e,function(i,a){return a.parent&&(i[a.parent.id]=a.parent),i},{}),I(n,function(i){var a=i.children;if(a){var o=t._getChildrenContainer(i);I(a.slice().reverse(),function(l){var u=r.getGraphics(l);Ri(u.parentNode,o)})}})};Ne.prototype.drawShape=function(e,t,r={}){var n=this._eventBus;return n.fire("render.shape",{gfx:e,element:t,attrs:r})};Ne.prototype.getShapePath=function(e){var t=this._eventBus;return t.fire("render.getShapePath",e)};Ne.prototype.drawConnection=function(e,t,r={}){var n=this._eventBus;return n.fire("render.connection",{gfx:e,element:t,attrs:r})};Ne.prototype.getConnectionPath=function(e){var t=this._eventBus;return t.fire("render.getConnectionPath",e)};Ne.prototype.update=function(e,t,r){if(t.parent){var n=this._clear(r);if(e==="shape")this.drawShape(n,t),Jn(r,t.x,t.y);else if(e==="connection")this.drawConnection(n,t);else throw new Error("unknown type: "+e);t.hidden?Z(r,"display","none"):Z(r,"display","block")}};Ne.prototype.remove=function(e){var t=this._elementRegistry.getGraphics(e);zt(t.parentNode)};function Ri(e,t,r){var n=r||t.firstChild;e!==n&&t.insertBefore(e,n)}const yl={__depends__:[Xo],__init__:["canvas"],canvas:["type",j],elementRegistry:["type",ke],elementFactory:["type",Dt],eventBus:["type",Ce],graphicsFactory:["type",Ne]};function gl(e){var t=new hi(e);return t.init(),t}function vl(e){e=e||{};var t={config:["value",e]},r=[t,yl].concat(e.modules||[]);return gl(r)}function It(e,t){this._injector=t=t||vl(e),this.get=t.get,this.invoke=t.invoke,this.get("eventBus").fire("diagram.init")}It.prototype.destroy=function(){this.get("eventBus").fire("diagram.destroy")};It.prototype.clear=function(){this.get("eventBus").fire("diagram.clear")};function en(){}en.prototype.get=function(e){return this.$model.properties.get(this,e)};en.prototype.set=function(e,t){this.$model.properties.set(this,e,t)};function Ci(e,t){this.model=e,this.properties=t}Ci.prototype.createType=function(e){var t=this.model,r=this.properties,n=Object.create(en.prototype);I(e.properties,function(o){!o.isMany&&o.default!==void 0&&(n[o.name]=o.default)}),r.defineModel(n,t),r.defineDescriptor(n,e);var i=e.ns.name;function a(o){r.define(this,"$type",{value:i,enumerable:!0}),r.define(this,"$attrs",{value:{}}),r.define(this,"$parent",{writable:!0}),I(o,it(function(l,u){this.set(u,l)},this))}return a.prototype=n,a.hasType=n.$instanceOf=this.model.hasType,r.defineModel(a,t),r.defineDescriptor(a,e),a};var bl={String:!0,Boolean:!0,Integer:!0,Real:!0,Element:!0},Ti={String:function(e){return e},Boolean:function(e){return e==="true"},Integer:function(e){return parseInt(e,10)},Real:function(e){return parseFloat(e)}};function tn(e,t){var r=Ti[e];return r?r(t):t}function jr(e){return!!bl[e]}function ki(e){return!!Ti[e]}function we(e,t){var r=e.split(/:/),n,i;if(r.length===1)n=e,i=t;else if(r.length===2)n=r[1],i=r[0];else throw new Error("expected <prefix:localName> or <localName>, got "+e);return e=(i?i+":":"")+n,{name:e,prefix:i,localName:n}}function Me(e){this.ns=e,this.name=e.name,this.allTypes=[],this.allTypesByName={},this.properties=[],this.propertiesByName={}}Me.prototype.build=function(){return Aa(this,["ns","name","allTypes","allTypesByName","properties","propertiesByName","bodyProperty","idProperty"])};Me.prototype.addProperty=function(e,t,r){typeof t=="boolean"&&(r=t,t=void 0),this.addNamedProperty(e,r!==!1);var n=this.properties;t!==void 0?n.splice(t,0,e):n.push(e)};Me.prototype.replaceProperty=function(e,t,r){var n=e.ns,i=this.properties,a=this.propertiesByName,o=e.name!==t.name;if(e.isId){if(!t.isId)throw new Error("property <"+t.ns.name+"> must be id property to refine <"+e.ns.name+">");this.setIdProperty(t,!1)}if(e.isBody){if(!t.isBody)throw new Error("property <"+t.ns.name+"> must be body property to refine <"+e.ns.name+">");this.setBodyProperty(t,!1)}var l=i.indexOf(e);if(l===-1)throw new Error("property <"+n.name+"> not found in property list");i.splice(l,1),this.addProperty(t,r?void 0:l,o),a[n.name]=a[n.localName]=t};Me.prototype.redefineProperty=function(e,t,r){var n=e.ns.prefix,i=t.split("#"),a=we(i[0],n),o=we(i[1],a.prefix).name,l=this.propertiesByName[o];if(l)this.replaceProperty(l,e,r);else throw new Error("refined property <"+o+"> not found");delete e.redefines};Me.prototype.addNamedProperty=function(e,t){var r=e.ns,n=this.propertiesByName;t&&(this.assertNotDefined(e,r.name),this.assertNotDefined(e,r.localName)),n[r.name]=n[r.localName]=e};Me.prototype.removeNamedProperty=function(e){var t=e.ns,r=this.propertiesByName;delete r[t.name],delete r[t.localName]};Me.prototype.setBodyProperty=function(e,t){if(t&&this.bodyProperty)throw new Error("body property defined multiple times (<"+this.bodyProperty.ns.name+">, <"+e.ns.name+">)");this.bodyProperty=e};Me.prototype.setIdProperty=function(e,t){if(t&&this.idProperty)throw new Error("id property defined multiple times (<"+this.idProperty.ns.name+">, <"+e.ns.name+">)");this.idProperty=e};Me.prototype.assertNotTrait=function(e){if((e.extends||[]).length)throw new Error("cannot create <".concat(e.name,"> extending <").concat(e.extends,">"))};Me.prototype.assertNotDefined=function(e,t){var r=e.name,n=this.propertiesByName[r];if(n)throw new Error("property <"+r+"> already defined; override of <"+n.definedBy.ns.name+"#"+n.ns.name+"> by <"+e.definedBy.ns.name+"#"+e.ns.name+"> not allowed without redefines")};Me.prototype.hasProperty=function(e){return this.propertiesByName[e]};Me.prototype.addTrait=function(e,t){t&&this.assertNotTrait(e);var r=this.allTypesByName,n=this.allTypes,i=e.name;i in r||(I(e.properties,it(function(a){a=B({},a,{name:a.ns.localName,inherited:t}),Object.defineProperty(a,"definedBy",{value:e});var o=a.replaces,l=a.redefines;o||l?this.redefineProperty(a,o||l,o):(a.isBody&&this.setBodyProperty(a),a.isId&&this.setIdProperty(a),this.addProperty(a))},this)),n.push(e),r[i]=e)};function pt(e,t){this.packageMap={},this.typeMap={},this.packages=[],this.properties=t,I(e,it(this.registerPackage,this))}pt.prototype.getPackage=function(e){return this.packageMap[e]};pt.prototype.getPackages=function(){return this.packages};pt.prototype.registerPackage=function(e){e=B({},e);var t=this.packageMap;Tn(t,e,"prefix"),Tn(t,e,"uri"),I(e.types,it(function(r){this.registerType(r,e)},this)),t[e.uri]=t[e.prefix]=e,this.packages.push(e)};pt.prototype.registerType=function(e,t){e=B({},e,{superClass:(e.superClass||[]).slice(),extends:(e.extends||[]).slice(),properties:(e.properties||[]).slice(),meta:B(e.meta||{})});var r=we(e.name,t.prefix),n=r.name,i={};I(e.properties,it(function(a){var o=we(a.name,r.prefix),l=o.name;jr(a.type)||(a.type=we(a.type,o.prefix).name),B(a,{ns:o,name:l}),i[l]=a},this)),B(e,{ns:r,name:n,propertiesByName:i}),I(e.extends,it(function(a){var o=we(a,r.prefix),l=this.typeMap[o.name];l.traits=l.traits||[],l.traits.push(n)},this)),this.definePackage(e,t),this.typeMap[n]=e};pt.prototype.mapTypes=function(e,t,r){var n=jr(e.name)?{name:e.name}:this.typeMap[e.name],i=this;function a(u,h){var b=we(u,jr(u)?"":e.prefix);i.mapTypes(b,t,h)}function o(u){return a(u,!0)}function l(u){return a(u,!1)}if(!n)throw new Error("unknown type <"+e.name+">");I(n.superClass,r?o:l),t(n,!r),I(n.traits,o)};pt.prototype.getEffectiveDescriptor=function(e){var t=we(e),r=new Me(t);this.mapTypes(t,function(i,a){r.addTrait(i,a)});var n=r.build();return this.definePackage(n,n.allTypes[n.allTypes.length-1].$pkg),n};pt.prototype.definePackage=function(e,t){this.properties.define(e,"$pkg",{value:t})};function Tn(e,t,r){var n=t[r];if(n in e)throw new Error("package with "+r+" <"+n+"> already defined")}function Et(e){this.model=e}Et.prototype.set=function(e,t,r){if(!We(t)||!t.length)throw new TypeError("property name must be a non-empty string");var n=this.getProperty(e,t),i=n&&n.name;_l(r)?n?delete e[i]:delete e.$attrs[Vr(t)]:n?i in e?e[i]=r:Ni(e,n,r):e.$attrs[Vr(t)]=r};Et.prototype.get=function(e,t){var r=this.getProperty(e,t);if(!r)return e.$attrs[Vr(t)];var n=r.name;return!e[n]&&r.isMany&&Ni(e,r,[]),e[n]};Et.prototype.define=function(e,t,r){if(!r.writable){var n=r.value;r=B({},r,{get:function(){return n}}),delete r.value}Object.defineProperty(e,t,r)};Et.prototype.defineDescriptor=function(e,t){this.define(e,"$descriptor",{value:t})};Et.prototype.defineModel=function(e,t){this.define(e,"$model",{value:t})};Et.prototype.getProperty=function(e,t){var r=this.model,n=r.getPropertyDescriptor(e,t);if(n)return n;if(t.includes(":"))return null;const i=r.config.strict;if(typeof i<"u"){const a=new TypeError("unknown property <".concat(t,"> on <").concat(e.$type,">"));if(i)throw a;typeof console<"u"&&console.warn(a)}return null};function _l(e){return typeof e>"u"}function Ni(e,t,r){Object.defineProperty(e,t.name,{enumerable:!t.isReference,writable:!0,value:r,configurable:!0})}function Vr(e){return e.replace(/^:/,"")}function Ie(e,t={}){this.properties=new Et(this),this.factory=new Ci(this,this.properties),this.registry=new pt(e,this.properties),this.typeCache={},this.config=t}Ie.prototype.create=function(e,t){var r=this.getType(e);if(!r)throw new Error("unknown type <"+e+">");return new r(t)};Ie.prototype.getType=function(e){var t=this.typeCache,r=We(e)?e:e.ns.name,n=t[r];return n||(e=this.registry.getEffectiveDescriptor(r),n=t[r]=this.factory.createType(e)),n};Ie.prototype.createAny=function(e,t,r){var n=we(e),i={$type:e,$instanceOf:function(o){return o===this.$type},get:function(o){return this[o]},set:function(o,l){Pa(this,[o],l)}},a={name:e,isGeneric:!0,ns:{prefix:n.prefix,localName:n.localName,uri:t}};return this.properties.defineDescriptor(i,a),this.properties.defineModel(i,this),this.properties.define(i,"get",{enumerable:!1,writable:!0}),this.properties.define(i,"set",{enumerable:!1,writable:!0}),this.properties.define(i,"$parent",{enumerable:!1,writable:!0}),this.properties.define(i,"$instanceOf",{enumerable:!1,writable:!0}),I(r,function(o,l){dt(o)&&o.value!==void 0?i[o.name]=o.value:i[l]=o}),i};Ie.prototype.getPackage=function(e){return this.registry.getPackage(e)};Ie.prototype.getPackages=function(){return this.registry.getPackages()};Ie.prototype.getElementDescriptor=function(e){return e.$descriptor};Ie.prototype.hasType=function(e,t){t===void 0&&(t=e,e=this);var r=e.$model.getElementDescriptor(e);return t in r.allTypesByName};Ie.prototype.getPropertyDescriptor=function(e,t){return this.getElementDescriptor(e).propertiesByName[t]};Ie.prototype.getTypeDescriptor=function(e){return this.registry.typeMap[e]};var kn=String.fromCharCode,El=Object.prototype.hasOwnProperty,wl=/&#(\d+);|&#x([0-9a-f]+);|&(\w+);/ig,Vt={amp:"&",apos:"'",gt:">",lt:"<",quot:'"'};Object.keys(Vt).forEach(function(e){Vt[e.toUpperCase()]=Vt[e]});function xl(e,t,r,n){return n?El.call(Vt,n)?Vt[n]:"&"+n+";":kn(t||parseInt(r,16))}function ft(e){return e.length>3&&e.indexOf("&")!==-1?e.replace(wl,xl):e}var Sl="http://www.w3.org/2001/XMLSchema-instance",Pl="xsi",Nn="xsi:type",Mn="non-whitespace outside of root node";function St(e){return new Error(e)}function Dn(e){return"missing namespace for prefix <"+e+">"}function nr(e){return{get:e,enumerable:!0}}function Al(e){var t={},r;for(r in e)t[r]=e[r];return t}function $r(e){return e+"$uri"}function Rl(e){var t={},r,n;for(r in e)n=e[r],t[n]=n,t[$r(n)]=r;return t}function In(){return{line:0,column:0}}function Cl(e){throw e}function Mi(e){if(!this)return new Mi(e);var t=e&&e.proxy,r,n,i,a,o=Cl,l,u,h,b,y=In,k=!1,R=!1,V=null,$=!1,W;function H(w){w instanceof Error||(w=St(w)),V=w,o(w,y)}function v(w){l&&(w instanceof Error||(w=St(w)),l(w,y))}this.on=function(w,S){if(typeof S!="function")throw St("required args <name, cb>");switch(w){case"openTag":n=S;break;case"text":r=S;break;case"closeTag":i=S;break;case"error":o=S;break;case"warn":l=S;break;case"cdata":a=S;break;case"attention":b=S;break;case"question":h=S;break;case"comment":u=S;break;default:throw St("unsupported event: "+w)}return this},this.ns=function(w){if(typeof w>"u"&&(w={}),typeof w!="object")throw St("required args <nsMap={}>");var S={},C;for(C in w)S[C]=w[C];return S[Sl]=Pl,R=!0,W=S,this},this.parse=function(w){if(typeof w!="string")throw St("required args <xml=string>");return V=null,N(w),y=In,$=!1,V},this.stop=function(){$=!0};function N(w){var S=R?[]:null,C=R?Rl(W):null,P,_=[],g=0,x=!1,D=!1,T=0,M=0,O,se,Y,K,Ve,c,s,p,d,f="",m=0,E;function U(){if(E!==null)return E;var G,Se,ue,De=R&&C.xmlns,Oe=R&&k?[]:null,re=m,ye=f,qe=ye.length,un,et,pe,$e,ee,wt={},cn={},Le,L,Q;e:for(;re<qe;re++)if(Le=!1,L=ye.charCodeAt(re),!(L===32||L<14&&L>8)){for((L<65||L>122||L>90&&L<97)&&L!==95&&L!==58&&(v("illegal first char attribute name"),Le=!0),Q=re+1;Q<qe;Q++)if(L=ye.charCodeAt(Q),!(L>96&&L<123||L>64&&L<91||L>47&&L<59||L===46||L===45||L===95)){if(L===32||L<14&&L>8){v("missing attribute value"),re=Q;continue e}if(L===61)break;v("illegal attribute name char"),Le=!0}if(ee=ye.substring(re,Q),ee==="xmlns:xmlns"&&(v("illegal declaration of xmlns"),Le=!0),L=ye.charCodeAt(Q+1),L===34)Q=ye.indexOf('"',re=Q+2),Q===-1&&(Q=ye.indexOf("'",re),Q!==-1&&(v("attribute value quote missmatch"),Le=!0));else if(L===39)Q=ye.indexOf("'",re=Q+2),Q===-1&&(Q=ye.indexOf('"',re),Q!==-1&&(v("attribute value quote missmatch"),Le=!0));else for(v("missing attribute value quotes"),Le=!0,Q=Q+1;Q<qe&&(L=ye.charCodeAt(Q+1),!(L===32||L<14&&L>8));Q++);for(Q===-1&&(v("missing closing quotes"),Q=qe,Le=!0),Le||(pe=ye.substring(re,Q)),re=Q;Q+1<qe&&(L=ye.charCodeAt(Q+1),!(L===32||L<14&&L>8));Q++)re===Q&&(v("illegal character after attribute end"),Le=!0);if(re=Q+1,Le)continue e;if(ee in cn){v("attribute <"+ee+"> already defined");continue}if(cn[ee]=!0,!R){wt[ee]=pe;continue}if(k){if(et=ee==="xmlns"?"xmlns":ee.charCodeAt(0)===120&&ee.substr(0,6)==="xmlns:"?ee.substr(6):null,et!==null){if(G=ft(pe),Se=$r(et),$e=W[G],!$e){if(et==="xmlns"||Se in C&&C[Se]!==G)do $e="ns"+g++;while(typeof C[$e]<"u");else $e=et;W[G]=$e}C[et]!==$e&&(un||(C=Al(C),un=!0),C[et]=$e,et==="xmlns"&&(C[$r($e)]=G,De=$e),C[Se]=G),wt[ee]=pe;continue}Oe.push(ee,pe);continue}if(L=ee.indexOf(":"),L===-1){wt[ee]=pe;continue}if(!(ue=C[ee.substring(0,L)])){v(Dn(ee.substring(0,L)));continue}ee=De===ue?ee.substr(L+1):ue+ee.substr(L),ee===Nn&&(L=pe.indexOf(":"),L!==-1?(ue=pe.substring(0,L),ue=C[ue]||ue,pe=ue+pe.substring(L)):pe=De+":"+pe),wt[ee]=pe}if(k)for(re=0,qe=Oe.length;re<qe;re++){if(ee=Oe[re++],pe=Oe[re],L=ee.indexOf(":"),L!==-1){if(!(ue=C[ee.substring(0,L)])){v(Dn(ee.substring(0,L)));continue}ee=De===ue?ee.substr(L+1):ue+ee.substr(L),ee===Nn&&(L=pe.indexOf(":"),L!==-1?(ue=pe.substring(0,L),ue=C[ue]||ue,pe=ue+pe.substring(L)):pe=De+":"+pe)}wt[ee]=pe}return E=wt}function oe(){for(var G=/(\r\n|\r|\n)/g,Se=0,ue=0,De=0,Oe=M,re,ye;T>=De&&(re=G.exec(w),!(!re||(Oe=re[0].length+re.index,Oe>T)));)Se+=1,De=Oe;return T==-1?(ue=Oe,ye=w.substring(M)):M===0?ye=w.substring(M,T):(ue=T-De,ye=M==-1?w.substring(T):w.substring(T,M+1)),{data:ye,line:Se,column:ue}}for(y=oe,t&&(d=Object.create({},{name:nr(function(){return s}),originalName:nr(function(){return p}),attrs:nr(U),ns:nr(function(){return C})}));M!==-1;){if(w.charCodeAt(M)===60?T=M:T=w.indexOf("<",M),T===-1){if(_.length)return H("unexpected end of file");if(M===0)return H("missing start tag");M<w.length&&w.substring(M).trim()&&v(Mn);return}if(M!==T){if(_.length){if(r&&(r(w.substring(M,T),ft,y),$))return}else if(w.substring(M,T).trim()&&(v(Mn),$))return}if(K=w.charCodeAt(T+1),K===33){if(Y=w.charCodeAt(T+2),Y===91&&w.substr(T+3,6)==="CDATA["){if(M=w.indexOf("]]>",T),M===-1)return H("unclosed cdata");if(a&&(a(w.substring(T+9,M),y),$))return;M+=3;continue}if(Y===45&&w.charCodeAt(T+3)===45){if(M=w.indexOf("-->",T),M===-1)return H("unclosed comment");if(u&&(u(w.substring(T+4,M),ft,y),$))return;M+=3;continue}}if(K===63){if(M=w.indexOf("?>",T),M===-1)return H("unclosed question");if(h&&(h(w.substring(T,M+2),y),$))return;M+=2;continue}for(O=T+1;;O++){if(Ve=w.charCodeAt(O),isNaN(Ve))return M=-1,H("unclosed tag");if(Ve===34)Y=w.indexOf('"',O+1),O=Y!==-1?Y:O;else if(Ve===39)Y=w.indexOf("'",O+1),O=Y!==-1?Y:O;else if(Ve===62){M=O;break}}if(K===33){if(b&&(b(w.substring(T,M+1),ft,y),$))return;M+=1;continue}if(E={},K===47){if(x=!1,D=!0,!_.length)return H("missing open tag");if(O=s=_.pop(),Y=T+2+O.length,w.substring(T+2,Y)!==O)return H("closing tag mismatch");for(;Y<M;Y++)if(K=w.charCodeAt(Y),!(K===32||K>8&&K<14))return H("close tag")}else{if(w.charCodeAt(M-1)===47?(O=s=w.substring(T+1,M-1),x=!0,D=!0):(O=s=w.substring(T+1,M),x=!0,D=!1),!(K>96&&K<123||K>64&&K<91||K===95||K===58))return H("illegal first char nodeName");for(Y=1,se=O.length;Y<se;Y++)if(K=O.charCodeAt(Y),!(K>96&&K<123||K>64&&K<91||K>47&&K<59||K===45||K===95||K==46)){if(K===32||K<14&&K>8){s=O.substring(0,Y),E=null;break}return H("invalid nodeName")}D||_.push(s)}if(R){if(P=C,x&&(D||S.push(P),E===null&&(k=O.indexOf("xmlns",Y)!==-1)&&(m=Y,f=O,U(),k=!1)),p=s,K=s.indexOf(":"),K!==-1){if(c=C[s.substring(0,K)],!c)return H("missing namespace on <"+p+">");s=s.substr(K+1)}else c=C.xmlns;c&&(s=c+":"+s)}if(x&&(m=Y,f=O,n&&(t?n(d,ft,D,y):n(s,U,ft,D,y),$)))return;if(D){if(i&&(i(t?d:s,ft,x,y),$))return;R&&(x?C=P:C=S.pop())}M+=1}}}function Di(e){return e.xml&&e.xml.tagAlias==="lowerCase"}var Tl={xsi:"http://www.w3.org/2001/XMLSchema-instance",xml:"http://www.w3.org/XML/1998/namespace"},_r="xsi:type";function Ii(e){return e.xml&&e.xml.serialize}function Oi(e){return Ii(e)===_r}function kl(e){return Ii(e)==="property"}function Nl(e){return e.charAt(0).toUpperCase()+e.slice(1)}function Li(e,t){return Di(t)?e.prefix+":"+Nl(e.localName):e.name}function Ml(e,t){var r=e.name,n=e.localName,i=t.xml&&t.xml.typePrefix;return i&&n.indexOf(i)===0?e.prefix+":"+n.slice(i.length):r}function Dl(e,t){var r=we(e),n=t.getPackage(r.prefix);return Ml(r,n)}function lt(e){return new Error(e)}function Ge(e){return e.$descriptor}function Il(e){B(this,e),this.elementsById={},this.references=[],this.warnings=[],this.addReference=function(t){this.references.push(t)},this.addElement=function(t){if(!t)throw lt("expected element");var r=this.elementsById,n=Ge(t),i=n.idProperty,a;if(i&&(a=t.get(i.name),a)){if(!/^([a-z][\w-.]*:)?[a-z_][\w-.]*$/i.test(a))throw new Error("illegal ID <"+a+">");if(r[a])throw lt("duplicate ID <"+a+">");r[a]=t}},this.addWarning=function(t){this.warnings.push(t)}}function Gt(){}Gt.prototype.handleEnd=function(){};Gt.prototype.handleText=function(){};Gt.prototype.handleNode=function(){};function rn(){}rn.prototype=Object.create(Gt.prototype);rn.prototype.handleNode=function(){return this};function Ot(){}Ot.prototype=Object.create(Gt.prototype);Ot.prototype.handleText=function(e){this.body=(this.body||"")+e};function Xt(e,t){this.property=e,this.context=t}Xt.prototype=Object.create(Ot.prototype);Xt.prototype.handleNode=function(e){if(this.element)throw lt("expected no sub nodes");return this.element=this.createReference(e),this};Xt.prototype.handleEnd=function(){this.element.id=this.body};Xt.prototype.createReference=function(e){return{property:this.property.ns.name,id:""}};function nn(e,t){this.element=t,this.propertyDesc=e}nn.prototype=Object.create(Ot.prototype);nn.prototype.handleEnd=function(){var e=this.body||"",t=this.element,r=this.propertyDesc;e=tn(r.type,e),r.isMany?t.get(r.name).push(e):t.set(r.name,e)};function Er(){}Er.prototype=Object.create(Ot.prototype);Er.prototype.handleNode=function(e){var t=this,r=this.element;return r?t=this.handleChild(e):(r=this.element=this.createElement(e),this.context.addElement(r)),t};function xe(e,t,r){this.model=e,this.type=e.getType(t),this.context=r}xe.prototype=Object.create(Er.prototype);xe.prototype.addReference=function(e){this.context.addReference(e)};xe.prototype.handleText=function(e){var t=this.element,r=Ge(t),n=r.bodyProperty;if(!n)throw lt("unexpected body text <"+e+">");Ot.prototype.handleText.call(this,e)};xe.prototype.handleEnd=function(){var e=this.body,t=this.element,r=Ge(t),n=r.bodyProperty;n&&e!==void 0&&(e=tn(n.type,e),t.set(n.name,e))};xe.prototype.createElement=function(e){var t=e.attributes,r=this.type,n=Ge(r),i=this.context,a=new r({}),o=this.model,l;return I(t,function(u,h){var b=n.propertiesByName[h],y;b&&b.isReference?b.isMany?(y=u.split(" "),I(y,function(k){i.addReference({element:a,property:b.ns.name,id:k})})):i.addReference({element:a,property:b.ns.name,id:u}):(b?u=tn(b.type,u):h!=="xmlns"&&(l=we(h,n.ns.prefix),o.getPackage(l.prefix)&&i.addWarning({message:"unknown attribute <"+h+">",element:a,property:h,value:u})),a.set(h,u))}),a};xe.prototype.getPropertyForNode=function(e){var t=e.name,r=we(t),n=this.type,i=this.model,a=Ge(n),o=r.name,l=a.propertiesByName[o],u,h;if(l&&!l.isAttr)return Oi(l)&&(u=e.attributes[_r],u)?(u=Dl(u,i),h=i.getType(u),B({},l,{effectiveType:Ge(h).name})):l;var b=i.getPackage(r.prefix);if(b){if(u=Li(r,b),h=i.getType(u),l=je(a.properties,function(y){return!y.isVirtual&&!y.isReference&&!y.isAttribute&&h.hasType(y.type)}),l)return B({},l,{effectiveType:Ge(h).name})}else if(l=je(a.properties,function(y){return!y.isReference&&!y.isAttribute&&y.type==="Element"}),l)return l;throw lt("unrecognized element <"+r.name+">")};xe.prototype.toString=function(){return"ElementDescriptor["+Ge(this.type).name+"]"};xe.prototype.valueHandler=function(e,t){return new nn(e,t)};xe.prototype.referenceHandler=function(e){return new Xt(e,this.context)};xe.prototype.handler=function(e){return e==="Element"?new Nt(this.model,e,this.context):new xe(this.model,e,this.context)};xe.prototype.handleChild=function(e){var t,r,n,i;if(t=this.getPropertyForNode(e),n=this.element,r=t.effectiveType||t.type,ki(r))return this.valueHandler(t,n);t.isReference?i=this.referenceHandler(t).handleNode(e):i=this.handler(r).handleNode(e);var a=i.element;return a!==void 0&&(t.isMany?n.get(t.name).push(a):n.set(t.name,a),t.isReference?(B(a,{element:n}),this.context.addReference(a)):a.$parent=n),i};function an(e,t,r){xe.call(this,e,t,r)}an.prototype=Object.create(xe.prototype);an.prototype.createElement=function(e){var t=e.name,r=we(t),n=this.model,i=this.type,a=n.getPackage(r.prefix),o=a&&Li(r,a)||t;if(!i.hasType(o))throw lt("unexpected element <"+e.originalName+">");return xe.prototype.createElement.call(this,e)};function Nt(e,t,r){this.model=e,this.context=r}Nt.prototype=Object.create(Er.prototype);Nt.prototype.createElement=function(e){var t=e.name,r=we(t),n=r.prefix,i=e.ns[n+"$uri"],a=e.attributes;return this.model.createAny(t,i,a)};Nt.prototype.handleChild=function(e){var t=new Nt(this.model,"Element",this.context).handleNode(e),r=this.element,n=t.element,i;return n!==void 0&&(i=r.$children=r.$children||[],i.push(n),n.$parent=r),t};Nt.prototype.handleEnd=function(){this.body&&(this.element.$body=this.body)};function sn(e){e instanceof Ie&&(e={model:e}),B(this,{lax:!1},e)}sn.prototype.fromXML=function(e,t,r){var n=t.rootHandler;t instanceof xe?(n=t,t={}):typeof t=="string"?(n=this.handler(t),t={}):typeof n=="string"&&(n=this.handler(n));var i=this.model,a=this.lax,o=new Il(B({},t,{rootHandler:n})),l=new Mi({proxy:!0}),u=Ol();n.context=o,u.push(n);function h(S,C,P){var _=C(),g=_.line,x=_.column,D=_.data;D.charAt(0)==="<"&&D.indexOf(" ")!==-1&&(D=D.slice(0,D.indexOf(" "))+">");var T="unparsable content "+(D?D+" ":"")+"detected\n	line: "+g+"\n	column: "+x+"\n	nested error: "+S.message;if(P)return o.addWarning({message:T,error:S}),!0;throw lt(T)}function b(S,C){return h(S,C,!0)}function y(){var S=o.elementsById,C=o.references,P,_;for(P=0;_=C[P];P++){var g=_.element,x=S[_.id],D=Ge(g).propertiesByName[_.property];if(x||o.addWarning({message:"unresolved reference <"+_.id+">",element:_.element,property:_.property,value:_.id}),D.isMany){var T=g.get(D.name),M=T.indexOf(_);M===-1&&(M=T.length),x?T[M]=x:T.splice(M,1)}else g.set(D.name,x)}}function k(){u.pop().handleEnd()}var R=/^<\?xml /i,V=/ encoding="([^"]+)"/i,$=/^utf-8$/i;function W(S){if(R.test(S)){var C=V.exec(S),P=C&&C[1];!P||$.test(P)||o.addWarning({message:"unsupported document encoding <"+P+">, falling back to UTF-8"})}}function H(S,C){var P=u.peek();try{u.push(P.handleNode(S))}catch(_){h(_,C,a)&&u.push(new rn)}}function v(S,C){try{u.peek().handleText(S)}catch(P){b(P,C)}}function N(S,C){S.trim()&&v(S,C)}var w=i.getPackages().reduce(function(S,C){return S[C.uri]=C.prefix,S},{"http://www.w3.org/XML/1998/namespace":"xml"});return l.ns(w).on("openTag",function(S,C,P,_){var g=S.attrs||{},x=Object.keys(g).reduce(function(T,M){var O=C(g[M]);return T[M]=O,T},{}),D={name:S.name,originalName:S.originalName,attributes:x,ns:S.ns};H(D,_)}).on("question",W).on("closeTag",k).on("cdata",v).on("text",function(S,C,P){N(C(S),P)}).on("error",h).on("warn",b),new Promise(function(S,C){var P;try{l.parse(e),y()}catch(T){P=T}var _=n.element;!P&&!_&&(P=lt("failed to parse document as <"+n.type.$descriptor.name+">"));var g=o.warnings,x=o.references,D=o.elementsById;return P?(P.warnings=g,C(P)):S({rootElement:_,elementsById:D,references:x,warnings:g})})};sn.prototype.handler=function(e){return new an(this.model,e)};function Ol(){var e=[];return Object.defineProperty(e,"peek",{value:function(){return this[this.length-1]}}),e}var Ll='<?xml version="1.0" encoding="UTF-8"?>\n',Bl=/<|>|'|"|&|\n\r|\n/g,Bi=/<|>|&/g;function Fl(e){var t={},r={},n={},i=[],a=[];this.byUri=function(o){return r[o]||e&&e.byUri(o)},this.add=function(o,l){r[o.uri]=o,l?i.push(o):a.push(o),this.mapPrefix(o.prefix,o.uri)},this.uriByPrefix=function(o){return t[o||"xmlns"]},this.mapPrefix=function(o,l){t[o||"xmlns"]=l},this.getNSKey=function(o){return o.prefix!==void 0?o.uri+"|"+o.prefix:o.uri},this.logUsed=function(o){var l=o.uri,u=this.getNSKey(o);n[u]=this.byUri(l),e&&e.logUsed(o)},this.getUsed=function(o){function l(b){var y=u.getNSKey(b);return n[y]}var u=this,h=[].concat(i,a);return h.filter(l)}}function jl(e){return e.charAt(0).toLowerCase()+e.slice(1)}function Vl(e,t){return Di(t)?jl(e):e}function Fi(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}function ji(e){return We(e)?e:(e.prefix?e.prefix+":":"")+e.localName}function $l(e){return e.getUsed().filter(function(t){return t.prefix!=="xml"}).map(function(t){var r="xmlns"+(t.prefix?":"+t.prefix:"");return{name:r,value:t.uri}})}function Ul(e,t){return t.isGeneric?B({localName:t.ns.localName},e):B({localName:Vl(t.ns.localName,t.$pkg)},e)}function zl(e,t){return B({localName:t.ns.localName},e)}function Wl(e){var t=e.$descriptor;return Rt(t.properties,function(r){var n=r.name;if(r.isVirtual||!mr(e,n))return!1;var i=e[n];return i===r.default||i===null?!1:r.isMany?i.length:!0})}var Hl={"\n":"#10","\n\r":"#10",'"':"#34","'":"#39","<":"#60",">":"#62","&":"#38"},Kl={"<":"lt",">":"gt","&":"amp"};function Vi(e,t,r){return e=We(e)?e:""+e,e.replace(t,function(n){return"&"+r[n]+";"})}function ql(e){return Vi(e,Bl,Hl)}function Yl(e){return Vi(e,Bi,Kl)}function Gl(e){return Rt(e,function(t){return t.isAttr})}function Xl(e){return Rt(e,function(t){return!t.isAttr})}function on(e){this.tagName=e}on.prototype.build=function(e){return this.element=e,this};on.prototype.serializeTo=function(e){e.appendIndent().append("<"+this.tagName+">"+this.element.id+"</"+this.tagName+">").appendNewLine()};function mt(){}mt.prototype.serializeValue=mt.prototype.serializeTo=function(e){e.append(this.escape?Yl(this.value):this.value)};mt.prototype.build=function(e,t){return this.value=t,e.type==="String"&&t.search(Bi)!==-1&&(this.escape=!0),this};function ln(e){this.tagName=e}Fi(ln,mt);ln.prototype.serializeTo=function(e){e.appendIndent().append("<"+this.tagName+">"),this.serializeValue(e),e.append("</"+this.tagName+">").appendNewLine()};function ae(e,t){this.body=[],this.attrs=[],this.parent=e,this.propertyDescriptor=t}ae.prototype.build=function(e){this.element=e;var t=e.$descriptor,r=this.propertyDescriptor,n,i,a=t.isGeneric;return a?n=this.parseGeneric(e):n=this.parseNsAttributes(e),r?this.ns=this.nsPropertyTagName(r):this.ns=this.nsTagName(t),this.tagName=this.addTagName(this.ns),a||(i=Wl(e),this.parseAttributes(Gl(i)),this.parseContainments(Xl(i))),this.parseGenericAttributes(e,n),this};ae.prototype.nsTagName=function(e){var t=this.logNamespaceUsed(e.ns);return Ul(t,e)};ae.prototype.nsPropertyTagName=function(e){var t=this.logNamespaceUsed(e.ns);return zl(t,e)};ae.prototype.isLocalNs=function(e){return e.uri===this.ns.uri};ae.prototype.nsAttributeName=function(e){var t;if(We(e)?t=we(e):t=e.ns,e.inherited)return{localName:t.localName};var r=this.logNamespaceUsed(t);return this.getNamespaces().logUsed(r),this.isLocalNs(r)?{localName:t.localName}:B({localName:t.localName},r)};ae.prototype.parseGeneric=function(e){var t=this,r=this.body,n=[];return I(e,function(i,a){var o;a==="$body"?r.push(new mt().build({type:"String"},i)):a==="$children"?I(i,function(l){r.push(new ae(t).build(l))}):a.indexOf("$")!==0&&(o=t.parseNsAttribute(e,a,i),o&&n.push({name:a,value:i}))}),n};ae.prototype.parseNsAttribute=function(e,t,r){var n=e.$model,i=we(t),a;if(i.prefix==="xmlns"&&(a={prefix:i.localName,uri:r}),!i.prefix&&i.localName==="xmlns"&&(a={uri:r}),!a)return{name:t,value:r};if(n&&n.getPackage(r))this.logNamespace(a,!0,!0);else{var o=this.logNamespaceUsed(a,!0);this.getNamespaces().logUsed(o)}};ae.prototype.parseNsAttributes=function(e,t){var r=this,n=e.$attrs,i=[];return I(n,function(a,o){var l=r.parseNsAttribute(e,o,a);l&&i.push(l)}),i};ae.prototype.parseGenericAttributes=function(e,t){var r=this;I(t,function(n){if(n.name!==_r)try{r.addAttribute(r.nsAttributeName(n.name),n.value)}catch(i){console.warn("missing namespace information for ",n.name,"=",n.value,"on",e,i)}})};ae.prototype.parseContainments=function(e){var t=this,r=this.body,n=this.element;I(e,function(i){var a=n.get(i.name),o=i.isReference,l=i.isMany;if(l||(a=[a]),i.isBody)r.push(new mt().build(i,a[0]));else if(ki(i.type))I(a,function(b){r.push(new ln(t.addTagName(t.nsPropertyTagName(i))).build(i,b))});else if(o)I(a,function(b){r.push(new on(t.addTagName(t.nsPropertyTagName(i))).build(b))});else{var u=Oi(i),h=kl(i);I(a,function(b){var y;u?y=new wr(t,i):h?y=new ae(t,i):y=new ae(t),r.push(y.build(b))})}})};ae.prototype.getNamespaces=function(e){var t=this.namespaces,r=this.parent,n;return t||(n=r&&r.getNamespaces(),e||!n?this.namespaces=t=new Fl(n):t=n),t};ae.prototype.logNamespace=function(e,t,r){var n=this.getNamespaces(r),i=e.uri,a=e.prefix,o=n.byUri(i);return(!o||r)&&n.add(e,t),n.mapPrefix(a,i),e};ae.prototype.logNamespaceUsed=function(e,t){var r=this.element,n=r.$model,i=this.getNamespaces(t),a=e.prefix,o=e.uri,l,u,h;if(!a&&!o)return{localName:e.localName};if(h=Tl[a]||n&&(n.getPackage(a)||{}).uri,o=o||h||i.uriByPrefix(a),!o)throw new Error("no namespace uri given for prefix <"+a+">");if(e=i.byUri(o),!e){for(l=a,u=1;i.uriByPrefix(l);)l=a+"_"+u++;e=this.logNamespace({prefix:l,uri:o},h===o)}return a&&i.mapPrefix(a,o),e};ae.prototype.parseAttributes=function(e){var t=this,r=this.element;I(e,function(n){var i=r.get(n.name);if(n.isReference)if(!n.isMany)i=i.id;else{var a=[];I(i,function(o){a.push(o.id)}),i=a.join(" ")}t.addAttribute(t.nsAttributeName(n),i)})};ae.prototype.addTagName=function(e){var t=this.logNamespaceUsed(e);return this.getNamespaces().logUsed(t),ji(e)};ae.prototype.addAttribute=function(e,t){var r=this.attrs;We(t)&&(t=ql(t));var n=_a(r,function(a){return a.name.localName===e.localName&&a.name.uri===e.uri&&a.name.prefix===e.prefix}),i={name:e,value:t};n!==-1?r.splice(n,1,i):r.push(i)};ae.prototype.serializeAttributes=function(e){var t=this.attrs,r=this.namespaces;r&&(t=$l(r).concat(t)),I(t,function(n){e.append(" ").append(ji(n.name)).append('="').append(n.value).append('"')})};ae.prototype.serializeTo=function(e){var t=this.body[0],r=t&&t.constructor!==mt;e.appendIndent().append("<"+this.tagName),this.serializeAttributes(e),e.append(t?">":" />"),t&&(r&&e.appendNewLine().indent(),I(this.body,function(n){n.serializeTo(e)}),r&&e.unindent().appendIndent(),e.append("</"+this.tagName+">")),e.appendNewLine()};function wr(e,t){ae.call(this,e,t)}Fi(wr,ae);wr.prototype.parseNsAttributes=function(e){var t=ae.prototype.parseNsAttributes.call(this,e),r=e.$descriptor;if(r.name===this.propertyDescriptor.type)return t;var n=this.typeNs=this.nsTagName(r);this.getNamespaces().logUsed(this.typeNs);var i=e.$model.getPackage(n.uri),a=i.xml&&i.xml.typePrefix||"";return this.addAttribute(this.nsAttributeName(_r),(n.prefix?n.prefix+":":"")+a+r.ns.localName),t};wr.prototype.isLocalNs=function(e){return e.uri===(this.typeNs||this.ns).uri};function Jl(){this.value="",this.write=function(e){this.value+=e}}function Ql(e,t){var r=[""];this.append=function(n){return e.write(n),this},this.appendNewLine=function(){return t&&e.write("\n"),this},this.appendIndent=function(){return t&&e.write(r.join("  ")),this},this.indent=function(){return r.push(""),this},this.unindent=function(){return r.pop(),this}}function Zl(e){e=B({format:!1,preamble:!0},e||{});function t(r,n){var i=n||new Jl,a=new Ql(i,e.format);if(e.preamble&&a.append(Ll),new ae().build(r).serializeTo(a),!n)return i.value}return{toXML:t}}function xr(e,t){Ie.call(this,e,t)}xr.prototype=Object.create(Ie.prototype);xr.prototype.fromXML=function(e,t,r){We(t)||(r=t,t="bpmn:Definitions");var n=new sn(B({model:this,lax:!0},r)),i=n.handler(t);return n.fromXML(e,i)};xr.prototype.toXML=function(e,t){var r=new Zl(t);return new Promise(function(n,i){try{var a=r.toXML(e);return n({xml:a})}catch(o){return i(o)}})};var eu="BPMN20",tu="http://www.omg.org/spec/BPMN/20100524/MODEL",ru="bpmn",nu=[],iu=[{name:"Interface",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"operations",type:"Operation",isMany:!0},{name:"implementationRef",isAttr:!0,type:"String"}]},{name:"Operation",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"inMessageRef",type:"Message",isReference:!0},{name:"outMessageRef",type:"Message",isReference:!0},{name:"errorRef",type:"Error",isMany:!0,isReference:!0},{name:"implementationRef",isAttr:!0,type:"String"}]},{name:"EndPoint",superClass:["RootElement"]},{name:"Auditing",superClass:["BaseElement"]},{name:"GlobalTask",superClass:["CallableElement"],properties:[{name:"resources",type:"ResourceRole",isMany:!0}]},{name:"Monitoring",superClass:["BaseElement"]},{name:"Performer",superClass:["ResourceRole"]},{name:"Process",superClass:["FlowElementsContainer","CallableElement"],properties:[{name:"processType",type:"ProcessType",isAttr:!0},{name:"isClosed",isAttr:!0,type:"Boolean"},{name:"auditing",type:"Auditing"},{name:"monitoring",type:"Monitoring"},{name:"properties",type:"Property",isMany:!0},{name:"laneSets",isMany:!0,replaces:"FlowElementsContainer#laneSets",type:"LaneSet"},{name:"flowElements",isMany:!0,replaces:"FlowElementsContainer#flowElements",type:"FlowElement"},{name:"artifacts",type:"Artifact",isMany:!0},{name:"resources",type:"ResourceRole",isMany:!0},{name:"correlationSubscriptions",type:"CorrelationSubscription",isMany:!0},{name:"supports",type:"Process",isMany:!0,isReference:!0},{name:"definitionalCollaborationRef",type:"Collaboration",isAttr:!0,isReference:!0},{name:"isExecutable",isAttr:!0,type:"Boolean"}]},{name:"LaneSet",superClass:["BaseElement"],properties:[{name:"lanes",type:"Lane",isMany:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Lane",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"partitionElementRef",type:"BaseElement",isAttr:!0,isReference:!0},{name:"partitionElement",type:"BaseElement"},{name:"flowNodeRef",type:"FlowNode",isMany:!0,isReference:!0},{name:"childLaneSet",type:"LaneSet",xml:{serialize:"xsi:type"}}]},{name:"GlobalManualTask",superClass:["GlobalTask"]},{name:"ManualTask",superClass:["Task"]},{name:"UserTask",superClass:["Task"],properties:[{name:"renderings",type:"Rendering",isMany:!0},{name:"implementation",isAttr:!0,type:"String"}]},{name:"Rendering",superClass:["BaseElement"]},{name:"HumanPerformer",superClass:["Performer"]},{name:"PotentialOwner",superClass:["HumanPerformer"]},{name:"GlobalUserTask",superClass:["GlobalTask"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"renderings",type:"Rendering",isMany:!0}]},{name:"Gateway",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"gatewayDirection",type:"GatewayDirection",default:"Unspecified",isAttr:!0}]},{name:"EventBasedGateway",superClass:["Gateway"],properties:[{name:"instantiate",default:!1,isAttr:!0,type:"Boolean"},{name:"eventGatewayType",type:"EventBasedGatewayType",isAttr:!0,default:"Exclusive"}]},{name:"ComplexGateway",superClass:["Gateway"],properties:[{name:"activationCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"ExclusiveGateway",superClass:["Gateway"],properties:[{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"InclusiveGateway",superClass:["Gateway"],properties:[{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"ParallelGateway",superClass:["Gateway"]},{name:"RootElement",isAbstract:!0,superClass:["BaseElement"]},{name:"Relationship",superClass:["BaseElement"],properties:[{name:"type",isAttr:!0,type:"String"},{name:"direction",type:"RelationshipDirection",isAttr:!0},{name:"source",isMany:!0,isReference:!0,type:"Element"},{name:"target",isMany:!0,isReference:!0,type:"Element"}]},{name:"BaseElement",isAbstract:!0,properties:[{name:"id",isAttr:!0,type:"String",isId:!0},{name:"documentation",type:"Documentation",isMany:!0},{name:"extensionDefinitions",type:"ExtensionDefinition",isMany:!0,isReference:!0},{name:"extensionElements",type:"ExtensionElements"}]},{name:"Extension",properties:[{name:"mustUnderstand",default:!1,isAttr:!0,type:"Boolean"},{name:"definition",type:"ExtensionDefinition",isAttr:!0,isReference:!0}]},{name:"ExtensionDefinition",properties:[{name:"name",isAttr:!0,type:"String"},{name:"extensionAttributeDefinitions",type:"ExtensionAttributeDefinition",isMany:!0}]},{name:"ExtensionAttributeDefinition",properties:[{name:"name",isAttr:!0,type:"String"},{name:"type",isAttr:!0,type:"String"},{name:"isReference",default:!1,isAttr:!0,type:"Boolean"},{name:"extensionDefinition",type:"ExtensionDefinition",isAttr:!0,isReference:!0}]},{name:"ExtensionElements",properties:[{name:"valueRef",isAttr:!0,isReference:!0,type:"Element"},{name:"values",type:"Element",isMany:!0},{name:"extensionAttributeDefinition",type:"ExtensionAttributeDefinition",isAttr:!0,isReference:!0}]},{name:"Documentation",superClass:["BaseElement"],properties:[{name:"text",type:"String",isBody:!0},{name:"textFormat",default:"text/plain",isAttr:!0,type:"String"}]},{name:"Event",isAbstract:!0,superClass:["FlowNode","InteractionNode"],properties:[{name:"properties",type:"Property",isMany:!0}]},{name:"IntermediateCatchEvent",superClass:["CatchEvent"]},{name:"IntermediateThrowEvent",superClass:["ThrowEvent"]},{name:"EndEvent",superClass:["ThrowEvent"]},{name:"StartEvent",superClass:["CatchEvent"],properties:[{name:"isInterrupting",default:!0,isAttr:!0,type:"Boolean"}]},{name:"ThrowEvent",isAbstract:!0,superClass:["Event"],properties:[{name:"dataInputs",type:"DataInput",isMany:!0},{name:"dataInputAssociations",type:"DataInputAssociation",isMany:!0},{name:"inputSet",type:"InputSet"},{name:"eventDefinitions",type:"EventDefinition",isMany:!0},{name:"eventDefinitionRef",type:"EventDefinition",isMany:!0,isReference:!0}]},{name:"CatchEvent",isAbstract:!0,superClass:["Event"],properties:[{name:"parallelMultiple",isAttr:!0,type:"Boolean",default:!1},{name:"dataOutputs",type:"DataOutput",isMany:!0},{name:"dataOutputAssociations",type:"DataOutputAssociation",isMany:!0},{name:"outputSet",type:"OutputSet"},{name:"eventDefinitions",type:"EventDefinition",isMany:!0},{name:"eventDefinitionRef",type:"EventDefinition",isMany:!0,isReference:!0}]},{name:"BoundaryEvent",superClass:["CatchEvent"],properties:[{name:"cancelActivity",default:!0,isAttr:!0,type:"Boolean"},{name:"attachedToRef",type:"Activity",isAttr:!0,isReference:!0}]},{name:"EventDefinition",isAbstract:!0,superClass:["RootElement"]},{name:"CancelEventDefinition",superClass:["EventDefinition"]},{name:"ErrorEventDefinition",superClass:["EventDefinition"],properties:[{name:"errorRef",type:"Error",isAttr:!0,isReference:!0}]},{name:"TerminateEventDefinition",superClass:["EventDefinition"]},{name:"EscalationEventDefinition",superClass:["EventDefinition"],properties:[{name:"escalationRef",type:"Escalation",isAttr:!0,isReference:!0}]},{name:"Escalation",properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"escalationCode",isAttr:!0,type:"String"}],superClass:["RootElement"]},{name:"CompensateEventDefinition",superClass:["EventDefinition"],properties:[{name:"waitForCompletion",isAttr:!0,type:"Boolean",default:!0},{name:"activityRef",type:"Activity",isAttr:!0,isReference:!0}]},{name:"TimerEventDefinition",superClass:["EventDefinition"],properties:[{name:"timeDate",type:"Expression",xml:{serialize:"xsi:type"}},{name:"timeCycle",type:"Expression",xml:{serialize:"xsi:type"}},{name:"timeDuration",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"LinkEventDefinition",superClass:["EventDefinition"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"target",type:"LinkEventDefinition",isReference:!0},{name:"source",type:"LinkEventDefinition",isMany:!0,isReference:!0}]},{name:"MessageEventDefinition",superClass:["EventDefinition"],properties:[{name:"messageRef",type:"Message",isAttr:!0,isReference:!0},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"ConditionalEventDefinition",superClass:["EventDefinition"],properties:[{name:"condition",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"SignalEventDefinition",superClass:["EventDefinition"],properties:[{name:"signalRef",type:"Signal",isAttr:!0,isReference:!0}]},{name:"Signal",superClass:["RootElement"],properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"ImplicitThrowEvent",superClass:["ThrowEvent"]},{name:"DataState",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"}]},{name:"ItemAwareElement",superClass:["BaseElement"],properties:[{name:"itemSubjectRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"dataState",type:"DataState"}]},{name:"DataAssociation",superClass:["BaseElement"],properties:[{name:"sourceRef",type:"ItemAwareElement",isMany:!0,isReference:!0},{name:"targetRef",type:"ItemAwareElement",isReference:!0},{name:"transformation",type:"FormalExpression",xml:{serialize:"property"}},{name:"assignment",type:"Assignment",isMany:!0}]},{name:"DataInput",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isCollection",default:!1,isAttr:!0,type:"Boolean"},{name:"inputSetRef",type:"InputSet",isMany:!0,isVirtual:!0,isReference:!0},{name:"inputSetWithOptional",type:"InputSet",isMany:!0,isVirtual:!0,isReference:!0},{name:"inputSetWithWhileExecuting",type:"InputSet",isMany:!0,isVirtual:!0,isReference:!0}]},{name:"DataOutput",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isCollection",default:!1,isAttr:!0,type:"Boolean"},{name:"outputSetRef",type:"OutputSet",isMany:!0,isVirtual:!0,isReference:!0},{name:"outputSetWithOptional",type:"OutputSet",isMany:!0,isVirtual:!0,isReference:!0},{name:"outputSetWithWhileExecuting",type:"OutputSet",isMany:!0,isVirtual:!0,isReference:!0}]},{name:"InputSet",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"dataInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"optionalInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"whileExecutingInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"outputSetRefs",type:"OutputSet",isMany:!0,isReference:!0}]},{name:"OutputSet",superClass:["BaseElement"],properties:[{name:"dataOutputRefs",type:"DataOutput",isMany:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"inputSetRefs",type:"InputSet",isMany:!0,isReference:!0},{name:"optionalOutputRefs",type:"DataOutput",isMany:!0,isReference:!0},{name:"whileExecutingOutputRefs",type:"DataOutput",isMany:!0,isReference:!0}]},{name:"Property",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"}]},{name:"DataInputAssociation",superClass:["DataAssociation"]},{name:"DataOutputAssociation",superClass:["DataAssociation"]},{name:"InputOutputSpecification",superClass:["BaseElement"],properties:[{name:"dataInputs",type:"DataInput",isMany:!0},{name:"dataOutputs",type:"DataOutput",isMany:!0},{name:"inputSets",type:"InputSet",isMany:!0},{name:"outputSets",type:"OutputSet",isMany:!0}]},{name:"DataObject",superClass:["FlowElement","ItemAwareElement"],properties:[{name:"isCollection",default:!1,isAttr:!0,type:"Boolean"}]},{name:"InputOutputBinding",properties:[{name:"inputDataRef",type:"InputSet",isAttr:!0,isReference:!0},{name:"outputDataRef",type:"OutputSet",isAttr:!0,isReference:!0},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"Assignment",superClass:["BaseElement"],properties:[{name:"from",type:"Expression",xml:{serialize:"xsi:type"}},{name:"to",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"DataStore",superClass:["RootElement","ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"capacity",isAttr:!0,type:"Integer"},{name:"isUnlimited",default:!0,isAttr:!0,type:"Boolean"}]},{name:"DataStoreReference",superClass:["ItemAwareElement","FlowElement"],properties:[{name:"dataStoreRef",type:"DataStore",isAttr:!0,isReference:!0}]},{name:"DataObjectReference",superClass:["ItemAwareElement","FlowElement"],properties:[{name:"dataObjectRef",type:"DataObject",isAttr:!0,isReference:!0}]},{name:"ConversationLink",superClass:["BaseElement"],properties:[{name:"sourceRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"ConversationAssociation",superClass:["BaseElement"],properties:[{name:"innerConversationNodeRef",type:"ConversationNode",isAttr:!0,isReference:!0},{name:"outerConversationNodeRef",type:"ConversationNode",isAttr:!0,isReference:!0}]},{name:"CallConversation",superClass:["ConversationNode"],properties:[{name:"calledCollaborationRef",type:"Collaboration",isAttr:!0,isReference:!0},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0}]},{name:"Conversation",superClass:["ConversationNode"]},{name:"SubConversation",superClass:["ConversationNode"],properties:[{name:"conversationNodes",type:"ConversationNode",isMany:!0}]},{name:"ConversationNode",isAbstract:!0,superClass:["InteractionNode","BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRef",type:"Participant",isMany:!0,isReference:!0},{name:"messageFlowRefs",type:"MessageFlow",isMany:!0,isReference:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0}]},{name:"GlobalConversation",superClass:["Collaboration"]},{name:"PartnerEntity",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRef",type:"Participant",isMany:!0,isReference:!0}]},{name:"PartnerRole",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRef",type:"Participant",isMany:!0,isReference:!0}]},{name:"CorrelationProperty",superClass:["RootElement"],properties:[{name:"correlationPropertyRetrievalExpression",type:"CorrelationPropertyRetrievalExpression",isMany:!0},{name:"name",isAttr:!0,type:"String"},{name:"type",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"Error",superClass:["RootElement"],properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"errorCode",isAttr:!0,type:"String"}]},{name:"CorrelationKey",superClass:["BaseElement"],properties:[{name:"correlationPropertyRef",type:"CorrelationProperty",isMany:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Expression",superClass:["BaseElement"],isAbstract:!1,properties:[{name:"body",isBody:!0,type:"String"}]},{name:"FormalExpression",superClass:["Expression"],properties:[{name:"language",isAttr:!0,type:"String"},{name:"evaluatesToTypeRef",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"Message",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"itemRef",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"ItemDefinition",superClass:["RootElement"],properties:[{name:"itemKind",type:"ItemKind",isAttr:!0},{name:"structureRef",isAttr:!0,type:"String"},{name:"isCollection",default:!1,isAttr:!0,type:"Boolean"},{name:"import",type:"Import",isAttr:!0,isReference:!0}]},{name:"FlowElement",isAbstract:!0,superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"auditing",type:"Auditing"},{name:"monitoring",type:"Monitoring"},{name:"categoryValueRef",type:"CategoryValue",isMany:!0,isReference:!0}]},{name:"SequenceFlow",superClass:["FlowElement"],properties:[{name:"isImmediate",isAttr:!0,type:"Boolean"},{name:"conditionExpression",type:"Expression",xml:{serialize:"xsi:type"}},{name:"sourceRef",type:"FlowNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"FlowNode",isAttr:!0,isReference:!0}]},{name:"FlowElementsContainer",isAbstract:!0,superClass:["BaseElement"],properties:[{name:"laneSets",type:"LaneSet",isMany:!0},{name:"flowElements",type:"FlowElement",isMany:!0}]},{name:"CallableElement",isAbstract:!0,superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"ioSpecification",type:"InputOutputSpecification",xml:{serialize:"property"}},{name:"supportedInterfaceRef",type:"Interface",isMany:!0,isReference:!0},{name:"ioBinding",type:"InputOutputBinding",isMany:!0,xml:{serialize:"property"}}]},{name:"FlowNode",isAbstract:!0,superClass:["FlowElement"],properties:[{name:"incoming",type:"SequenceFlow",isMany:!0,isReference:!0},{name:"outgoing",type:"SequenceFlow",isMany:!0,isReference:!0},{name:"lanes",type:"Lane",isMany:!0,isVirtual:!0,isReference:!0}]},{name:"CorrelationPropertyRetrievalExpression",superClass:["BaseElement"],properties:[{name:"messagePath",type:"FormalExpression"},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"CorrelationPropertyBinding",superClass:["BaseElement"],properties:[{name:"dataPath",type:"FormalExpression"},{name:"correlationPropertyRef",type:"CorrelationProperty",isAttr:!0,isReference:!0}]},{name:"Resource",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"resourceParameters",type:"ResourceParameter",isMany:!0}]},{name:"ResourceParameter",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isRequired",isAttr:!0,type:"Boolean"},{name:"type",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"CorrelationSubscription",superClass:["BaseElement"],properties:[{name:"correlationKeyRef",type:"CorrelationKey",isAttr:!0,isReference:!0},{name:"correlationPropertyBinding",type:"CorrelationPropertyBinding",isMany:!0}]},{name:"MessageFlow",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"sourceRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"MessageFlowAssociation",superClass:["BaseElement"],properties:[{name:"innerMessageFlowRef",type:"MessageFlow",isAttr:!0,isReference:!0},{name:"outerMessageFlowRef",type:"MessageFlow",isAttr:!0,isReference:!0}]},{name:"InteractionNode",isAbstract:!0,properties:[{name:"incomingConversationLinks",type:"ConversationLink",isMany:!0,isVirtual:!0,isReference:!0},{name:"outgoingConversationLinks",type:"ConversationLink",isMany:!0,isVirtual:!0,isReference:!0}]},{name:"Participant",superClass:["InteractionNode","BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"interfaceRef",type:"Interface",isMany:!0,isReference:!0},{name:"participantMultiplicity",type:"ParticipantMultiplicity"},{name:"endPointRefs",type:"EndPoint",isMany:!0,isReference:!0},{name:"processRef",type:"Process",isAttr:!0,isReference:!0}]},{name:"ParticipantAssociation",superClass:["BaseElement"],properties:[{name:"innerParticipantRef",type:"Participant",isAttr:!0,isReference:!0},{name:"outerParticipantRef",type:"Participant",isAttr:!0,isReference:!0}]},{name:"ParticipantMultiplicity",properties:[{name:"minimum",default:0,isAttr:!0,type:"Integer"},{name:"maximum",default:1,isAttr:!0,type:"Integer"}],superClass:["BaseElement"]},{name:"Collaboration",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isClosed",isAttr:!0,type:"Boolean"},{name:"participants",type:"Participant",isMany:!0},{name:"messageFlows",type:"MessageFlow",isMany:!0},{name:"artifacts",type:"Artifact",isMany:!0},{name:"conversations",type:"ConversationNode",isMany:!0},{name:"conversationAssociations",type:"ConversationAssociation"},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0},{name:"messageFlowAssociations",type:"MessageFlowAssociation",isMany:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0},{name:"choreographyRef",type:"Choreography",isMany:!0,isReference:!0},{name:"conversationLinks",type:"ConversationLink",isMany:!0}]},{name:"ChoreographyActivity",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"participantRef",type:"Participant",isMany:!0,isReference:!0},{name:"initiatingParticipantRef",type:"Participant",isAttr:!0,isReference:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0},{name:"loopType",type:"ChoreographyLoopType",default:"None",isAttr:!0}]},{name:"CallChoreography",superClass:["ChoreographyActivity"],properties:[{name:"calledChoreographyRef",type:"Choreography",isAttr:!0,isReference:!0},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0}]},{name:"SubChoreography",superClass:["ChoreographyActivity","FlowElementsContainer"],properties:[{name:"artifacts",type:"Artifact",isMany:!0}]},{name:"ChoreographyTask",superClass:["ChoreographyActivity"],properties:[{name:"messageFlowRef",type:"MessageFlow",isMany:!0,isReference:!0}]},{name:"Choreography",superClass:["Collaboration","FlowElementsContainer"]},{name:"GlobalChoreographyTask",superClass:["Choreography"],properties:[{name:"initiatingParticipantRef",type:"Participant",isAttr:!0,isReference:!0}]},{name:"TextAnnotation",superClass:["Artifact"],properties:[{name:"text",type:"String"},{name:"textFormat",default:"text/plain",isAttr:!0,type:"String"}]},{name:"Group",superClass:["Artifact"],properties:[{name:"categoryValueRef",type:"CategoryValue",isAttr:!0,isReference:!0}]},{name:"Association",superClass:["Artifact"],properties:[{name:"associationDirection",type:"AssociationDirection",isAttr:!0},{name:"sourceRef",type:"BaseElement",isAttr:!0,isReference:!0},{name:"targetRef",type:"BaseElement",isAttr:!0,isReference:!0}]},{name:"Category",superClass:["RootElement"],properties:[{name:"categoryValue",type:"CategoryValue",isMany:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Artifact",isAbstract:!0,superClass:["BaseElement"]},{name:"CategoryValue",superClass:["BaseElement"],properties:[{name:"categorizedFlowElements",type:"FlowElement",isMany:!0,isVirtual:!0,isReference:!0},{name:"value",isAttr:!0,type:"String"}]},{name:"Activity",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"isForCompensation",default:!1,isAttr:!0,type:"Boolean"},{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0},{name:"ioSpecification",type:"InputOutputSpecification",xml:{serialize:"property"}},{name:"boundaryEventRefs",type:"BoundaryEvent",isMany:!0,isReference:!0},{name:"properties",type:"Property",isMany:!0},{name:"dataInputAssociations",type:"DataInputAssociation",isMany:!0},{name:"dataOutputAssociations",type:"DataOutputAssociation",isMany:!0},{name:"startQuantity",default:1,isAttr:!0,type:"Integer"},{name:"resources",type:"ResourceRole",isMany:!0},{name:"completionQuantity",default:1,isAttr:!0,type:"Integer"},{name:"loopCharacteristics",type:"LoopCharacteristics"}]},{name:"ServiceTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"SubProcess",superClass:["Activity","FlowElementsContainer","InteractionNode"],properties:[{name:"triggeredByEvent",default:!1,isAttr:!0,type:"Boolean"},{name:"artifacts",type:"Artifact",isMany:!0}]},{name:"LoopCharacteristics",isAbstract:!0,superClass:["BaseElement"]},{name:"MultiInstanceLoopCharacteristics",superClass:["LoopCharacteristics"],properties:[{name:"isSequential",default:!1,isAttr:!0,type:"Boolean"},{name:"behavior",type:"MultiInstanceBehavior",default:"All",isAttr:!0},{name:"loopCardinality",type:"Expression",xml:{serialize:"xsi:type"}},{name:"loopDataInputRef",type:"ItemAwareElement",isReference:!0},{name:"loopDataOutputRef",type:"ItemAwareElement",isReference:!0},{name:"inputDataItem",type:"DataInput",xml:{serialize:"property"}},{name:"outputDataItem",type:"DataOutput",xml:{serialize:"property"}},{name:"complexBehaviorDefinition",type:"ComplexBehaviorDefinition",isMany:!0},{name:"completionCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"oneBehaviorEventRef",type:"EventDefinition",isAttr:!0,isReference:!0},{name:"noneBehaviorEventRef",type:"EventDefinition",isAttr:!0,isReference:!0}]},{name:"StandardLoopCharacteristics",superClass:["LoopCharacteristics"],properties:[{name:"testBefore",default:!1,isAttr:!0,type:"Boolean"},{name:"loopCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"loopMaximum",type:"Integer",isAttr:!0}]},{name:"CallActivity",superClass:["Activity","InteractionNode"],properties:[{name:"calledElement",type:"String",isAttr:!0}]},{name:"Task",superClass:["Activity","InteractionNode"]},{name:"SendTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"ReceiveTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"instantiate",default:!1,isAttr:!0,type:"Boolean"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"ScriptTask",superClass:["Task"],properties:[{name:"scriptFormat",isAttr:!0,type:"String"},{name:"script",type:"String"}]},{name:"BusinessRuleTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"}]},{name:"AdHocSubProcess",superClass:["SubProcess"],properties:[{name:"completionCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"ordering",type:"AdHocOrdering",isAttr:!0},{name:"cancelRemainingInstances",default:!0,isAttr:!0,type:"Boolean"}]},{name:"Transaction",superClass:["SubProcess"],properties:[{name:"protocol",isAttr:!0,type:"String"},{name:"method",isAttr:!0,type:"String"}]},{name:"GlobalScriptTask",superClass:["GlobalTask"],properties:[{name:"scriptLanguage",isAttr:!0,type:"String"},{name:"script",isAttr:!0,type:"String"}]},{name:"GlobalBusinessRuleTask",superClass:["GlobalTask"],properties:[{name:"implementation",isAttr:!0,type:"String"}]},{name:"ComplexBehaviorDefinition",superClass:["BaseElement"],properties:[{name:"condition",type:"FormalExpression"},{name:"event",type:"ImplicitThrowEvent"}]},{name:"ResourceRole",superClass:["BaseElement"],properties:[{name:"resourceRef",type:"Resource",isReference:!0},{name:"resourceParameterBindings",type:"ResourceParameterBinding",isMany:!0},{name:"resourceAssignmentExpression",type:"ResourceAssignmentExpression"},{name:"name",isAttr:!0,type:"String"}]},{name:"ResourceParameterBinding",properties:[{name:"expression",type:"Expression",xml:{serialize:"xsi:type"}},{name:"parameterRef",type:"ResourceParameter",isAttr:!0,isReference:!0}],superClass:["BaseElement"]},{name:"ResourceAssignmentExpression",properties:[{name:"expression",type:"Expression",xml:{serialize:"xsi:type"}}],superClass:["BaseElement"]},{name:"Import",properties:[{name:"importType",isAttr:!0,type:"String"},{name:"location",isAttr:!0,type:"String"},{name:"namespace",isAttr:!0,type:"String"}]},{name:"Definitions",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"targetNamespace",isAttr:!0,type:"String"},{name:"expressionLanguage",default:"http://www.w3.org/1999/XPath",isAttr:!0,type:"String"},{name:"typeLanguage",default:"http://www.w3.org/2001/XMLSchema",isAttr:!0,type:"String"},{name:"imports",type:"Import",isMany:!0},{name:"extensions",type:"Extension",isMany:!0},{name:"rootElements",type:"RootElement",isMany:!0},{name:"diagrams",isMany:!0,type:"bpmndi:BPMNDiagram"},{name:"exporter",isAttr:!0,type:"String"},{name:"relationships",type:"Relationship",isMany:!0},{name:"exporterVersion",isAttr:!0,type:"String"}]}],au=[{name:"ProcessType",literalValues:[{name:"None"},{name:"Public"},{name:"Private"}]},{name:"GatewayDirection",literalValues:[{name:"Unspecified"},{name:"Converging"},{name:"Diverging"},{name:"Mixed"}]},{name:"EventBasedGatewayType",literalValues:[{name:"Parallel"},{name:"Exclusive"}]},{name:"RelationshipDirection",literalValues:[{name:"None"},{name:"Forward"},{name:"Backward"},{name:"Both"}]},{name:"ItemKind",literalValues:[{name:"Physical"},{name:"Information"}]},{name:"ChoreographyLoopType",literalValues:[{name:"None"},{name:"Standard"},{name:"MultiInstanceSequential"},{name:"MultiInstanceParallel"}]},{name:"AssociationDirection",literalValues:[{name:"None"},{name:"One"},{name:"Both"}]},{name:"MultiInstanceBehavior",literalValues:[{name:"None"},{name:"One"},{name:"All"},{name:"Complex"}]},{name:"AdHocOrdering",literalValues:[{name:"Parallel"},{name:"Sequential"}]}],su={tagAlias:"lowerCase",typePrefix:"t"},ou={name:eu,uri:tu,prefix:ru,associations:nu,types:iu,enumerations:au,xml:su},lu="BPMNDI",uu="http://www.omg.org/spec/BPMN/20100524/DI",cu="bpmndi",pu=[{name:"BPMNDiagram",properties:[{name:"plane",type:"BPMNPlane",redefines:"di:Diagram#rootElement"},{name:"labelStyle",type:"BPMNLabelStyle",isMany:!0}],superClass:["di:Diagram"]},{name:"BPMNPlane",properties:[{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"}],superClass:["di:Plane"]},{name:"BPMNShape",properties:[{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"},{name:"isHorizontal",isAttr:!0,type:"Boolean"},{name:"isExpanded",isAttr:!0,type:"Boolean"},{name:"isMarkerVisible",isAttr:!0,type:"Boolean"},{name:"label",type:"BPMNLabel"},{name:"isMessageVisible",isAttr:!0,type:"Boolean"},{name:"participantBandKind",type:"ParticipantBandKind",isAttr:!0},{name:"choreographyActivityShape",type:"BPMNShape",isAttr:!0,isReference:!0}],superClass:["di:LabeledShape"]},{name:"BPMNEdge",properties:[{name:"label",type:"BPMNLabel"},{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"},{name:"sourceElement",isAttr:!0,isReference:!0,type:"di:DiagramElement",redefines:"di:Edge#source"},{name:"targetElement",isAttr:!0,isReference:!0,type:"di:DiagramElement",redefines:"di:Edge#target"},{name:"messageVisibleKind",type:"MessageVisibleKind",isAttr:!0,default:"initiating"}],superClass:["di:LabeledEdge"]},{name:"BPMNLabel",properties:[{name:"labelStyle",type:"BPMNLabelStyle",isAttr:!0,isReference:!0,redefines:"di:DiagramElement#style"}],superClass:["di:Label"]},{name:"BPMNLabelStyle",properties:[{name:"font",type:"dc:Font"}],superClass:["di:Style"]}],fu=[{name:"ParticipantBandKind",literalValues:[{name:"top_initiating"},{name:"middle_initiating"},{name:"bottom_initiating"},{name:"top_non_initiating"},{name:"middle_non_initiating"},{name:"bottom_non_initiating"}]},{name:"MessageVisibleKind",literalValues:[{name:"initiating"},{name:"non_initiating"}]}],du=[],hu={name:lu,uri:uu,prefix:cu,types:pu,enumerations:fu,associations:du},mu="DC",yu="http://www.omg.org/spec/DD/20100524/DC",gu="dc",vu=[{name:"Boolean"},{name:"Integer"},{name:"Real"},{name:"String"},{name:"Font",properties:[{name:"name",type:"String",isAttr:!0},{name:"size",type:"Real",isAttr:!0},{name:"isBold",type:"Boolean",isAttr:!0},{name:"isItalic",type:"Boolean",isAttr:!0},{name:"isUnderline",type:"Boolean",isAttr:!0},{name:"isStrikeThrough",type:"Boolean",isAttr:!0}]},{name:"Point",properties:[{name:"x",type:"Real",default:"0",isAttr:!0},{name:"y",type:"Real",default:"0",isAttr:!0}]},{name:"Bounds",properties:[{name:"x",type:"Real",default:"0",isAttr:!0},{name:"y",type:"Real",default:"0",isAttr:!0},{name:"width",type:"Real",isAttr:!0},{name:"height",type:"Real",isAttr:!0}]}],bu=[],_u={name:mu,uri:yu,prefix:gu,types:vu,associations:bu},Eu="DI",wu="http://www.omg.org/spec/DD/20100524/DI",xu="di",Su=[{name:"DiagramElement",isAbstract:!0,properties:[{name:"id",isAttr:!0,isId:!0,type:"String"},{name:"extension",type:"Extension"},{name:"owningDiagram",type:"Diagram",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"owningElement",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"modelElement",isReadOnly:!0,isVirtual:!0,isReference:!0,type:"Element"},{name:"style",type:"Style",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"ownedElement",type:"DiagramElement",isReadOnly:!0,isMany:!0,isVirtual:!0}]},{name:"Node",isAbstract:!0,superClass:["DiagramElement"]},{name:"Edge",isAbstract:!0,superClass:["DiagramElement"],properties:[{name:"source",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"target",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"waypoint",isUnique:!1,isMany:!0,type:"dc:Point",xml:{serialize:"xsi:type"}}]},{name:"Diagram",isAbstract:!0,properties:[{name:"id",isAttr:!0,isId:!0,type:"String"},{name:"rootElement",type:"DiagramElement",isReadOnly:!0,isVirtual:!0},{name:"name",isAttr:!0,type:"String"},{name:"documentation",isAttr:!0,type:"String"},{name:"resolution",isAttr:!0,type:"Real"},{name:"ownedStyle",type:"Style",isReadOnly:!0,isMany:!0,isVirtual:!0}]},{name:"Shape",isAbstract:!0,superClass:["Node"],properties:[{name:"bounds",type:"dc:Bounds"}]},{name:"Plane",isAbstract:!0,superClass:["Node"],properties:[{name:"planeElement",type:"DiagramElement",subsettedProperty:"DiagramElement-ownedElement",isMany:!0}]},{name:"LabeledEdge",isAbstract:!0,superClass:["Edge"],properties:[{name:"ownedLabel",type:"Label",isReadOnly:!0,subsettedProperty:"DiagramElement-ownedElement",isMany:!0,isVirtual:!0}]},{name:"LabeledShape",isAbstract:!0,superClass:["Shape"],properties:[{name:"ownedLabel",type:"Label",isReadOnly:!0,subsettedProperty:"DiagramElement-ownedElement",isMany:!0,isVirtual:!0}]},{name:"Label",isAbstract:!0,superClass:["Node"],properties:[{name:"bounds",type:"dc:Bounds"}]},{name:"Style",isAbstract:!0,properties:[{name:"id",isAttr:!0,isId:!0,type:"String"}]},{name:"Extension",properties:[{name:"values",isMany:!0,type:"Element"}]}],Pu=[],Au={tagAlias:"lowerCase"},Ru={name:Eu,uri:wu,prefix:xu,types:Su,associations:Pu,xml:Au},Cu="bpmn.io colors for BPMN",Tu="http://bpmn.io/schema/bpmn/biocolor/1.0",ku="bioc",Nu=[{name:"ColoredShape",extends:["bpmndi:BPMNShape"],properties:[{name:"stroke",isAttr:!0,type:"String"},{name:"fill",isAttr:!0,type:"String"}]},{name:"ColoredEdge",extends:["bpmndi:BPMNEdge"],properties:[{name:"stroke",isAttr:!0,type:"String"},{name:"fill",isAttr:!0,type:"String"}]}],Mu=[],Du=[],Iu={name:Cu,uri:Tu,prefix:ku,types:Nu,enumerations:Mu,associations:Du},Ou="BPMN in Color",Lu="http://www.omg.org/spec/BPMN/non-normative/color/1.0",Bu="color",Fu=[{name:"ColoredLabel",extends:["bpmndi:BPMNLabel"],properties:[{name:"color",isAttr:!0,type:"String"}]},{name:"ColoredShape",extends:["bpmndi:BPMNShape"],properties:[{name:"background-color",isAttr:!0,type:"String"},{name:"border-color",isAttr:!0,type:"String"}]},{name:"ColoredEdge",extends:["bpmndi:BPMNEdge"],properties:[{name:"border-color",isAttr:!0,type:"String"}]}],ju=[],Vu=[],$u={name:Ou,uri:Lu,prefix:Bu,types:Fu,enumerations:ju,associations:Vu},Uu={bpmn:ou,bpmndi:hu,dc:_u,di:Ru,bioc:Iu,color:$u};function zu(e,t){var r=B({},Uu,e);return new xr(r,t)}function Jt(e){return function(){if(!window.Promise)throw new Error("Promises is not supported in this environment. Please polyfill Promise.");var t=arguments.length;if(t>=1&&Ye(arguments[t-1])){var r=arguments[t-1];console.warn(new Error("Passing callbacks to "+e.name+" is deprecated and will be removed in a future major release. Please switch to promises: https://bpmn.io/l/moving-to-promises.html"));var n=Array.prototype.slice.call(arguments,0,-1);e.apply(this,n).then(function(i){var a=Object.keys(i)[0];return r(null,i[a])},function(i){return r(i,i.warnings)})}else return e.apply(this,arguments)}}var Wu="Tried to access di from the businessObject. The di is available through the diagram element only. For more information, see https://github.com/bpmn-io/bpmn-js/issues/1472";function Hu(e){mr(e,"di")||Object.defineProperty(e,"di",{enumerable:!1,get:function(){throw new Error(Wu)}})}function Pe(e,t){return e.$instanceOf(t)}function Ku(e){return je(e.rootElements,function(t){return Pe(t,"bpmn:Process")||Pe(t,"bpmn:Collaboration")})}function qu(e,t){var r={},n=[],i={};function a(f,m){return function(E){f(E,m)}}function o(f){r[f.id]=f}function l(f){return r[f.id]}function u(f,m){var E=f.gfx;if(E)throw new Error(t("already rendered {element}",{element:_e(f)}));return e.element(f,i[f.id],m)}function h(f,m){return e.root(f,i[f.id],m)}function b(f,m){try{var E=i[f.id]&&u(f,m);return o(f),E}catch(U){y(U.message,{element:f,error:U}),console.error(t("failed to import {element}",{element:_e(f)})),console.error(U)}}function y(f,m){e.error(f,m)}var k=this.registerDi=function(m){var E=m.bpmnElement;E?i[E.id]?y(t("multiple DI elements defined for {element}",{element:_e(E)}),{element:E}):(i[E.id]=m,Hu(E)):y(t("no bpmnElement referenced in {element}",{element:_e(m)}),{element:m})};function R(f){V(f.plane)}function V(f){k(f),I(f.planeElement,$)}function $(f){k(f)}this.handleDefinitions=function(m,E){var U=m.diagrams;if(E&&U.indexOf(E)===-1)throw new Error(t("diagram not part of bpmn:Definitions"));if(!E&&U&&U.length&&(E=U[0]),!E)throw new Error(t("no diagram to display"));i={},R(E);var oe=E.plane;if(!oe)throw new Error(t("no plane for {element}",{element:_e(E)}));var G=oe.bpmnElement;if(!G)if(G=Ku(m),G)y(t("correcting missing bpmnElement on {plane} to {rootElement}",{plane:_e(oe),rootElement:_e(G)})),oe.bpmnElement=G,k(oe);else throw new Error(t("no process or collaboration to display"));var Se=h(G,oe);if(Pe(G,"bpmn:Process")||Pe(G,"bpmn:SubProcess"))H(G,Se);else if(Pe(G,"bpmn:Collaboration"))p(G,Se),v(m.rootElements,Se);else throw new Error(t("unsupported bpmnElement for {plane}: {rootElement}",{plane:_e(oe),rootElement:_e(G)}));W(n)};var W=this.handleDeferred=function(){for(var m;n.length;)m=n.shift(),m()};function H(f,m){Ve(f,m),x(f.ioSpecification,m),g(f.artifacts,m),o(f)}function v(f,m){var E=Rt(f,function(U){return!l(U)&&Pe(U,"bpmn:Process")&&U.laneSets});E.forEach(a(H,m))}function N(f,m){b(f,m)}function w(f,m){I(f,a(N,m))}function S(f,m){b(f,m)}function C(f,m){b(f,m)}function P(f,m){b(f,m)}function _(f,m){b(f,m)}function g(f,m){I(f,function(E){Pe(E,"bpmn:Association")?n.push(function(){_(E,m)}):_(E,m)})}function x(f,m){f&&(I(f.dataInputs,a(C,m)),I(f.dataOutputs,a(P,m)))}var D=this.handleSubProcess=function(m,E){Ve(m,E),g(m.artifacts,E)};function T(f,m){var E=b(f,m);Pe(f,"bpmn:SubProcess")&&D(f,E||m),Pe(f,"bpmn:Activity")&&x(f.ioSpecification,m),n.push(function(){I(f.dataInputAssociations,a(S,m)),I(f.dataOutputAssociations,a(S,m))})}function M(f,m){b(f,m)}function O(f,m){b(f,m)}function se(f,m){n.push(function(){var E=b(f,m);f.childLaneSet&&Y(f.childLaneSet,E||m),d(f)})}function Y(f,m){I(f.lanes,a(se,m))}function K(f,m){I(f,a(Y,m))}function Ve(f,m){c(f.flowElements,m),f.laneSets&&K(f.laneSets,m)}function c(f,m){I(f,function(E){Pe(E,"bpmn:SequenceFlow")?n.push(function(){M(E,m)}):Pe(E,"bpmn:BoundaryEvent")?n.unshift(function(){T(E,m)}):Pe(E,"bpmn:FlowNode")?T(E,m):Pe(E,"bpmn:DataObject")||(Pe(E,"bpmn:DataStoreReference")||Pe(E,"bpmn:DataObjectReference")?O(E,m):y(t("unrecognized flowElement {element} in context {context}",{element:_e(E),context:m?_e(m.businessObject):"null"}),{element:E,context:m}))})}function s(f,m){var E=b(f,m),U=f.processRef;U&&H(U,E||m)}function p(f,m){I(f.participants,a(s,m)),g(f.artifacts,m),n.push(function(){w(f.messageFlows,m)})}function d(f){I(f.flowNodeRef,function(m){var E=m.get("lanes");E&&E.push(f)})}}function Yu(e,t,r){var n,i,a,o,l,u=[];function h(b,y){var k={root:function(W,H){return n.add(W,H)},element:function(W,H,v){return n.add(W,H,v)},error:function(W,H){u.push({message:W,context:H})}},R=new qu(k,a);y=y||b.diagrams&&b.diagrams[0];var V=Gu(b,y);if(!V)throw new Error(a("no diagram to display"));I(V,function(W){R.handleDefinitions(b,W)});var $=y.plane.bpmnElement.id;o.setRootElement(o.findRoot($+"_plane")||o.findRoot($))}return new Promise(function(b,y){try{return n=e.get("bpmnImporter"),i=e.get("eventBus"),a=e.get("translate"),o=e.get("canvas"),i.fire("import.render.start",{definitions:t}),h(t,r),i.fire("import.render.complete",{error:l,warnings:u}),b({warnings:u})}catch(k){return k.warnings=u,y(k)}})}function Gu(e,t){if(t){var r=t.plane.bpmnElement,n=r;!F(r,"bpmn:Process")&&!F(r,"bpmn:Collaboration")&&(n=Xu(r));var i;F(n,"bpmn:Collaboration")?i=n:i=je(e.rootElements,function(h){if(F(h,"bpmn:Collaboration"))return je(h.participants,function(b){return b.processRef===n})});var a=[n];i&&(a=Ea(i.participants,function(h){return h.processRef}),a.push(i));var o=$i(a),l=[t],u=[r];return I(e.diagrams,function(h){var b=h.plane.bpmnElement;o.indexOf(b)!==-1&&u.indexOf(b)===-1&&(l.push(h),u.push(b))}),l}}function $i(e){var t=[];return I(e,function(r){r&&(t.push(r),t=t.concat($i(r.flowElements)))}),t}function Xu(e){for(var t=e;t;){if(F(t,"bpmn:Process"))return t;t=t.$parent}}var Ju='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14.02 5.57" width="53" height="21"><path fill="currentColor" d="M1.88.92v.14c0 .41-.13.68-.*********.46.44.46.86v.33c0 .61-.33.95-.95.95H0V0h.95c.65 0 .93.3.93.92zM.63.57v1.06h.24c.24 0 .38-.1.38-.43V.98c0-.28-.1-.4-.32-.4zm0 1.63v1.22h.36c.2 0 .32-.1.32-.39v-.35c0-.37-.12-.48-.4-.48H.63zM4.18.99v.52c0 .64-.31.98-.94.98h-.3V4h-.62V0h.92c.63 0 .94.35.94.99zM2.94.57v1.35h.3c.2 0 .3-.09.3-.37v-.6c0-.29-.1-.38-.3-.38h-.3zm2.89 2.27L6.25 0h.88v4h-.6V1.12L6.1 3.99h-.6l-.46-2.82v2.82h-.55V0h.87zM8.14 1.1V4h-.56V0h.79L9 2.4V0h.56v4h-.64zm2.49 2.29v.6h-.6v-.6zM12.12 1c0-.63.33-1 .95-1 .61 0 .95.37.95 1v2.04c0 .64-.34 1-.95 1-.62 0-.95-.37-.95-1zm.62 2.08c0 .28.13.39.33.39s.32-.1.32-.4V.98c0-.29-.12-.4-.32-.4s-.33.11-.33.4z"/><path fill="currentColor" d="M0 4.53h14.02v1.04H0zM11.08 0h.63v.62h-.63zm.63 4V1h-.63v2.98z"/></svg>',Ui=Ju,zi={verticalAlign:"middle"},Wi={color:"#404040"},Qu={zIndex:"1001",position:"fixed",top:"0",left:"0",right:"0",bottom:"0"},Zu={width:"100%",height:"100%",background:"rgba(40,40,40,0.2)"},ec={position:"absolute",left:"50%",top:"40%",transform:"translate(-50%)",width:"260px",padding:"10px",background:"white",boxShadow:"0 1px 4px rgba(0,0,0,0.3)",fontFamily:"Helvetica, Arial, sans-serif",fontSize:"14px",display:"flex",lineHeight:"1.3"},tc='<div class="bjs-powered-by-lightbox"><div class="backdrop"></div><div class="notice"><a href="https://bpmn.io" target="_blank" rel="noopener" class="link">'+Ui+'</a><span>Web-based tooling for BPMN, DMN and forms powered by <a href="https://bpmn.io" target="_blank" rel="noopener">bpmn.io</a>.</span></div></div>',ze;function rc(){ze=He(tc),Re(ze,Qu),Re(Be("svg",ze),zi),Re(Be(".backdrop",ze),Zu),Re(Be(".notice",ze),ec),Re(Be(".link",ze),Wi,{margin:"15px 20px 15px 10px",alignSelf:"center"})}function nc(){ze||(rc(),Lr.bind(ze,".backdrop","click",function(e){document.body.removeChild(ze)})),document.body.appendChild(ze)}function J(e){e=B({},ac,e),this._moddle=this._createModdle(e),this._container=this._createContainer(e),oc(this._container),this._init(this._container,this._moddle,e)}Ke(J,It);J.prototype.importXML=async function(t,r){const n=this;function i(o){const l=n.get("eventBus").createEvent(o);return Object.defineProperty(l,"context",{enumerable:!0,get:function(){return console.warn(new Error("import.parse.complete <context> is deprecated and will be removed in future library versions")),{warnings:o.warnings,references:o.references,elementsById:o.elementsById}}}),l}let a=[];try{t=this._emit("import.parse.start",{xml:t})||t;let o;try{o=await this._moddle.fromXML(t,"bpmn:Definitions")}catch(k){throw this._emit("import.parse.complete",{error:k}),k}let l=o.rootElement;const u=o.references,h=o.warnings,b=o.elementsById;a=a.concat(h),l=this._emit("import.parse.complete",i({error:null,definitions:l,elementsById:b,references:u,warnings:a}))||l;const y=await this.importDefinitions(l,r);return a=a.concat(y.warnings),this._emit("import.done",{error:null,warnings:a}),{warnings:a}}catch(o){let l=o;throw a=a.concat(l.warnings||[]),lr(l,a),l=ic(l),this._emit("import.done",{error:l,warnings:l.warnings}),l}};J.prototype.importXML=Jt(J.prototype.importXML);J.prototype.importDefinitions=async function(t,r){return this._setDefinitions(t),{warnings:(await this.open(r)).warnings}};J.prototype.importDefinitions=Jt(J.prototype.importDefinitions);J.prototype.open=async function(t){const r=this._definitions;let n=t;if(!r){const a=new Error("no XML imported");throw lr(a,[]),a}if(typeof t=="string"&&(n=sc(r,t),!n)){const a=new Error("BPMNDiagram <"+t+"> not found");throw lr(a,[]),a}try{this.clear()}catch(a){throw lr(a,[]),a}const{warnings:i}=await Yu(this,r,n);return{warnings:i}};J.prototype.open=Jt(J.prototype.open);J.prototype.saveXML=async function(t){t=t||{};let r=this._definitions,n,i;try{if(!r)throw new Error("no definitions loaded");r=this._emit("saveXML.start",{definitions:r})||r,i=(await this._moddle.toXML(r,t)).xml,i=this._emit("saveXML.serialized",{xml:i})||i}catch(o){n=o}const a=n?{error:n}:{xml:i};if(this._emit("saveXML.done",a),n)throw n;return a};J.prototype.saveXML=Jt(J.prototype.saveXML);J.prototype.saveSVG=async function(){this._emit("saveSVG.start");let t,r;try{const n=this.get("canvas"),i=n.getActiveLayer(),a=Be("defs",n._svg),o=bn(i),l=a?"<defs>"+bn(a)+"</defs>":"",u=i.getBBox();t='<?xml version="1.0" encoding="utf-8"?>\n<!-- created with bpmn-js / http://bpmn.io -->\n<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="'+u.width+'" height="'+u.height+'" viewBox="'+u.x+" "+u.y+" "+u.width+" "+u.height+'" version="1.1">'+l+o+"</svg>"}catch(n){r=n}if(this._emit("saveSVG.done",{error:r,svg:t}),r)throw r;return{svg:t}};J.prototype.saveSVG=Jt(J.prototype.saveSVG);J.prototype._setDefinitions=function(e){this._definitions=e};J.prototype.getModules=function(){return this._modules};J.prototype.clear=function(){this.getDefinitions()&&It.prototype.clear.call(this)};J.prototype.destroy=function(){It.prototype.destroy.call(this),dr(this._container)};J.prototype.on=function(e,t,r,n){return this.get("eventBus").on(e,t,r,n)};J.prototype.off=function(e,t){this.get("eventBus").off(e,t)};J.prototype.attachTo=function(e){if(!e)throw new Error("parentNode required");this.detach(),e.get&&e.constructor.prototype.jquery&&(e=e.get(0)),typeof e=="string"&&(e=Be(e)),e.appendChild(this._container),this._emit("attach",{}),this.get("canvas").resized()};J.prototype.getDefinitions=function(){return this._definitions};J.prototype.detach=function(){const e=this._container,t=e.parentNode;t&&(this._emit("detach",{}),t.removeChild(e))};J.prototype._init=function(e,t,r){const n=r.modules||this.getModules(r),i=r.additionalModules||[],a=[{bpmnjs:["value",this],moddle:["value",t]}],o=[].concat(a,n,i),l=B(Ra(r,["additionalModules"]),{canvas:B({},r.canvas,{container:e}),modules:o});It.call(this,l),r&&r.container&&this.attachTo(r.container)};J.prototype._emit=function(e,t){return this.get("eventBus").fire(e,t)};J.prototype._createContainer=function(e){const t=He('<div class="bjs-container"></div>');return Re(t,{width:On(e.width),height:On(e.height),position:e.position}),t};J.prototype._createModdle=function(e){const t=B({},this._moddleExtensions,e.moddleExtensions);return new zu(t)};J.prototype._modules=[];function lr(e,t){return e.warnings=t,e}function ic(e){const r=/unparsable content <([^>]+)> detected([\s\S]*)$/.exec(e.message);return r&&(e.message="unparsable content <"+r[1]+"> detected; this may indicate an invalid BPMN 2.0 diagram file"+r[2]),e}const ac={width:"100%",height:"100%",position:"relative"};function On(e){return e+(nt(e)?"px":"")}function sc(e,t){return t&&je(e.diagrams,function(r){return r.id===t})||null}function oc(e){const r='<a href="http://bpmn.io" target="_blank" class="bjs-powered-by" title="Powered by bpmn.io" >'+Ui+"</a>",n=He(r);Re(Be("svg",n),zi),Re(n,Wi,{position:"absolute",bottom:"15px",right:"15px",zIndex:"100"}),e.appendChild(n),Kr.bind(n,"click",function(i){nc(),i.preventDefault()})}function Sr(e){J.call(this,e)}Ke(Sr,J);Sr.prototype._modules=[lo,Zn,Eo,ui,Vo];Sr.prototype._moddleExtensions={};const lc={name:"FlowBmp",components:{},props:{modelKey:String,bizId:String,processInstanceId:String,show:Boolean},emits:[],setup(e,{attrs:t,slots:r,emit:n}){},data(){return{loading:!1,bpmnXml:"",flowInstance:{},setup:!1}},computed:{},watch:{modelKey:{immediate:!0,handler(e){e&&this.getModelXML()}},show:{immediate:!0,handler(e){e&&!this.setup&&this.setupBmp()}}},created(){},mounted(){},methods:{async getModelXML(){var e;try{this.loading=!0;const t=await Xi({modelKey:this.modelKey});if(this.bpmnXml=t.bpmnXml,((e=this.$route.query)==null?void 0:e.processState)=="-1")return;this.getNodesHistory()}catch(t){console.log(t)}finally{this.loading=!1}},async getNodesHistory(){try{this.loading=!0;const e=await Ji({bizId:this.bizId});this.flowInstance=e||{}}catch(e){console.log(e)}finally{this.loading=!1}},async setupBmp(){try{const e=this.$refs.canvas;if(!e)return;e.innerHTML="";const t=new Sr({container:e}),{warnings:r}=await t.importXML(this.bpmnXml);this.$nextTick(()=>{const n=t.get("canvas"),{currentNode:i=[],userTask:a=[],exclusiveGateway:o=[]}=this.flowInstance;i.forEach(l=>{n.addMarker(l,"current-highlight")}),a.forEach(l=>{n.addMarker(l,"highlight")}),o.forEach(l=>{n.addMarker(l,"highlight")}),n.zoom("fit-viewport")}),this.setup=!0}catch(e){console.log(e)}}}},uc={class:"bmp-container"},cc={class:"canvas",ref:"canvas"};function pc(e,t,r,n,i,a){const o=X("van-divider");return z(),ie("div",uc,[ne(o,{class:"divider"},{default:me(()=>t[0]||(t[0]=[Ft("流程图")])),_:1,__:[0]}),t[1]||(t[1]=qi('<div class="exmple-wp" data-v-ff27054e><div class="exmple-item" data-v-ff27054e><div class="exmple-icon current" data-v-ff27054e></div><span class="exmple-text" data-v-ff27054e>进行中</span></div><div class="exmple-item" data-v-ff27054e><div class="exmple-icon done" data-v-ff27054e></div><span class="exmple-text" data-v-ff27054e>已通过</span></div><div class="exmple-item" data-v-ff27054e><div class="exmple-icon process" data-v-ff27054e></div><span class="exmple-text" data-v-ff27054e>未完成</span></div></div>',1)),be("div",cc,null,512)])}const fc=yt(lc,[["render",pc],["__scopeId","data-v-ff27054e"]]);const dc={name:"BpmInitiatorInfos",componentName:"BpmInitiatorInfos",props:{info:{type:Object,default:null},modelValue:{type:Object,default:()=>({})},required:{type:Boolean,default:!0},readonly:{type:Boolean,default:!0},fieldReadonlyConfig:{type:Object,default:()=>({})},type:{type:String,default:""},format:{type:String,default:"YYYY-MM-DD"}},emits:["update:modelValue","setInitiatorInfo"],inject:{registerValidationResult:{default:null}},data(){return{taskKey:this.$route.params.taskKey||this.$route.query.taskKey,formData:{initiator:null,initiatorFullName:"",initiatorPhone:"",initiatedTime:"",initiatorDept:"",label:"",approvalTimeLabel:"",userPhoneLabel:"",userOrgLabel:""}}},computed:{currentUser(){return this.$store.USER_INFO||{}},fieldReadonly(){const e={initiatorFullName:!0,initiatorDept:!0,initiatorPhone:!0,initiatedTime:!0};return this.readonly?e:{...e,...this.fieldReadonlyConfig}}},watch:{info:{handler(e){if(e){const{userName:t,userFullName:r,userPhone:n,approvalTime:i,userOrg:a,label:o,approvalTimeLabel:l,userPhoneLabel:u,userOrgLabel:h}=e;this.formData={initiator:t,initiatorFullName:r,initiatorPhone:n,initiatedTime:i,initiatorDept:a,label:o,approvalTimeLabel:l,userPhoneLabel:u,userOrgLabel:h}}else this.taskKey==="UserTask_0"||this.taskKey==="fawkes_custom_flow_start"?this.setInitiatorInfo():this.resetForm()},immediate:!0,deep:!0},formData:{handler(e){this.$emit("update:modelValue",e)},deep:!0},modelValue:{handler(e){e&&Object.keys(e).forEach(t=>{this.formData.hasOwnProperty(t)&&(this.formData[t]=e[t])})},immediate:!0,deep:!0}},created(){this.type==="add"&&this.setInitiatorInfo()},methods:{formatDate(e){var t;return e?this.$dayjs(e).format(((t=this.info)==null?void 0:t.format)||this.format):""},setInitiatorInfo(){var l;const e=this.currentUser;if(!e)return;this.formData.initiator=e.userName,this.formData.initiatorFullName=e.userFullname,this.formData.initiatorPhone=e.phone,this.formData.initiatedTime=this.formatDate(new Date),this.formData.initiatorDept=(l=e.orgList)!=null&&l.length?e.orgList.map(u=>u.name).join(";"):"";const{initiator:t,initiatorFullName:r,initiatedTime:n,initiatorPhone:i,initiatorDept:a}=this.formData,o={label:this.formData.label||"发起人",prop:"initiator",userName:t,userFullName:r,approvalTime:n,userPhone:i,userOrg:a,type:0};this.$emit("setInitiatorInfo",o)},getFormData(){return this.formData},resetForm(){const{label:e,approvalTimeLabel:t,userPhoneLabel:r,userOrgLabel:n}=this.formData;this.formData={initiator:null,initiatorFullName:"",initiatorPhone:"",initiatedTime:"",initiatorDept:"",label:e||"",approvalTimeLabel:t||"",userPhoneLabel:r||"",userOrgLabel:n||""}},validate(){return!this.required||!!this.formData.initiatorFullName},validateFormItem(e){const{type:t,fields:r}=e,n="initiator";if(this.required&&(!r||r.includes(n))){const a=this.validate();this.registerValidationResult&&this.registerValidationResult(n,{valid:a,message:a?"":"发起人不能为空"})}},handleValidateForm(e){const{type:t,fields:r}=e,n="initiator";if(this.required&&(!r||r.includes(n))){const a=this.validate();this.$parent&&this.$parent.registerValidationResult&&this.$parent.registerValidationResult(n,{valid:a,message:a?"":"发起人不能为空"})}}}},hc={class:"bpm-initiator-infos"};function mc(e,t,r,n,i,a){const o=X("van-field"),l=X("van-cell-group");return z(),ie("div",hc,[ge(e.$slots,"before",{},void 0,!0),ne(l,{border:!1},{default:me(()=>[ne(o,{modelValue:i.formData.initiatorFullName,"onUpdate:modelValue":t[0]||(t[0]=u=>i.formData.initiatorFullName=u),label:i.formData.label||"发起人",readonly:a.fieldReadonly.initiatorFullName,required:r.required},null,8,["modelValue","label","readonly","required"]),ne(o,{modelValue:i.formData.initiatorDept,"onUpdate:modelValue":t[1]||(t[1]=u=>i.formData.initiatorDept=u),label:i.formData.userOrgLabel||"发起人部门",readonly:a.fieldReadonly.initiatorDept},null,8,["modelValue","label","readonly"]),ne(o,{modelValue:i.formData.initiatorPhone,"onUpdate:modelValue":t[2]||(t[2]=u=>i.formData.initiatorPhone=u),label:i.formData.userPhoneLabel||"联系方式",readonly:a.fieldReadonly.initiatorPhone},null,8,["modelValue","label","readonly"]),ne(o,{modelValue:i.formData.initiatedTime,"onUpdate:modelValue":t[3]||(t[3]=u=>i.formData.initiatedTime=u),formatter:a.formatDate,label:i.formData.approvalTimeLabel||"发起日期",readonly:a.fieldReadonly.initiatedTime},null,8,["modelValue","formatter","label","readonly"]),ge(e.$slots,"after",{},void 0,!0)]),_:3})])}const yc=yt(dc,[["render",mc],["__scopeId","data-v-d09c136a"]]);const gc={name:"FormItemMultiplePerson",components:{},emits:["update:userName","update:userFullname","change"],props:{userName:String,userFullname:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:Boolean,readonly:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]}},setup(e,{attrs:t,slots:r,emit:n}){},data(){return{showPicker:!1,loading:!1,finished:!1,list:[],searchParams:{pageNo:1,pageSize:200,isMobile:!0,searchValue:"",accountStatus:"1"},loaded:!1,user:{},selectUserList:[]}},computed:{portal(){return this.$store.PORTAL},selectedUser(){return this.user&&this.user.id?{...this.user}:{...this.list.find(t=>t.id==this.userId||t.userName===this.userName)}}},watch:{"searchParams.searchValue"(e){e===""&&this.onSearch(e)},list:{handler(e){this.loaded||this.setInitPerson()},deep:!0}},created(){},mounted(){},methods:{onSearch(e){this.finished=!1,this.searchParams.pageNo=1,this.$nextTick(()=>{this.onLoadList()})},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams};e.pageNo===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await ua(e),r=this.searchParams.pageNo<=1?[]:this.list||[];this.list=[...r,...t.list],this.list.length>=t.total?this.finished=!0:this.searchParams.pageNo++}catch(e){console.log(e),this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.loaded=!0,this.$closeToast()},100)}},onShowPicker(){this.readonly||(this.showPicker=!0,this.loaded||this.onSearch())},setInitPerson(){let e=[];if(this.userName){let t=this.userName.split(",");this.list.forEach(r=>{t.includes(r.userName)&&e.push(r)})}this.selectUserList=e},onClosePicker(){this.showPicker=!1},handleSelect(e){let t=this.selectUserList.findIndex(r=>r.userName==e.userName);t>-1?this.selectUserList.splice(t,1):this.selectUserList.push(e)},getDept(e=[]){return e.find(r=>{var n;return r.portalId==((n=this.portal)==null?void 0:n.id)})||e[0]},onSelectConfirm(){const e=this.selectUserList.map(r=>r.userName).join(","),t=this.selectUserList.map(r=>r.userFullname).join(",");this.$emit("update:userName",e),this.$emit("update:userFullname",t),this.$emit("change",this.selectUserList),this.onClosePicker()}}},vc={class:"pop-window"},bc={class:"pop-window-header"},_c={class:"van-picker__title van-ellipsis"},Ec={class:"pop-window-body"},wc={class:"list-content"},xc={key:0,class:"p-[10px]"},Sc={class:"pop-window-footer van-safe-area-bottom"};function Pc(e,t,r,n,i,a){const o=X("van-field"),l=X("van-search"),u=X("van-cell"),h=X("van-empty"),b=X("van-list"),y=X("van-button"),k=X("van-popup");return z(),ie(Ee,null,[ne(o,{name:r.name,"model-value":r.userFullname,label:r.label,required:r.required,rules:r.rules,"input-align":r.inputAlign,"error-message-align":r.errorMessageAlign,"label-width":r.labelWidth,readonly:"","is-link":!r.readonly,placeholder:"请选择",onClick:t[0]||(t[0]=R=>a.onShowPicker())},null,8,["name","model-value","label","required","rules","input-align","error-message-align","label-width","is-link"]),ne(k,{show:i.showPicker,"onUpdate:show":t[4]||(t[4]=R=>i.showPicker=R),position:"bottom",closeable:"",teleport:"#app"},{default:me(()=>[be("div",vc,[be("div",bc,[be("div",_c,rt(r.title),1)]),be("div",Ec,[ne(l,{modelValue:i.searchParams.searchValue,"onUpdate:modelValue":t[1]||(t[1]=R=>i.searchParams.searchValue=R),placeholder:"输入账号或姓名搜索","show-action":!1,onSearch:a.onSearch},null,8,["modelValue","onSearch"]),be("div",wc,[ne(b,{loading:i.loading,"onUpdate:loading":t[2]||(t[2]=R=>i.loading=R),finished:i.finished,"finished-text":i.list&&i.list.length?"没有更多了":"",onLoad:a.onLoadList,"immediate-check":!1},{default:me(()=>[i.list&&i.list.length?(z(!0),ie(Ee,{key:0},At(i.list,R=>{var V;return z(),he(u,{class:Ln(["person-cell",{select:i.selectUserList.findIndex($=>$.userName===R.userName)>-1}]),key:R.id,title:R.userFullname,value:(V=a.getDept(R.orgList))==null?void 0:V.name,onClick:$=>a.handleSelect(R)},null,8,["class","title","value","onClick"])}),128)):(z(),ie(Ee,{key:1},[i.loading?de("",!0):(z(),ie("div",xc,[ne(h,{description:"暂无更多数据"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])])]),be("div",Sc,[ne(y,{class:"confirm-button",round:"",block:"",type:"primary",onClick:t[3]||(t[3]=Yi(R=>a.onSelectConfirm(),["stop","prevent"]))},{default:me(()=>t[5]||(t[5]=[Ft(" 确定 ")])),_:1,__:[5]})])])]),_:1},8,["show"])],64)}const Ac=yt(gc,[["render",Pc],["__scopeId","data-v-7e388d2b"]]);const Rc={name:"BpmPersonInfos",componentName:"BpmPersonInfos",components:{FormItemPerson:fa,FormItemMultiplePerson:Ac},props:{personList:{type:Array,default:()=>[]},title:{type:String,default:""}},emits:["setBpmPerson","validate"],data(){return{tempPersonList:[],hiddenPersonList:[],formData:{},flowConfig:{},formRules:{},isCcInfo:!1}},watch:{personList:{handler(e){if(e){let t=e.filter(r=>r.isVisible!==0);this.hiddenPersonList=e.filter(r=>r.isVisible===0),this.flowConfig={},this.formRules={};for(let r=0;r<t.length;r++){let n=t[r];this.flowConfig[n.prop]=n.isEditable===0?"readonly":"false",n.isRequired!==0&&(this.formRules[n.prop]={required:!0}),n.type===2?this.isCcInfo=!0:this.isCcInfo=!1}this.tempPersonList=JSON.parse(JSON.stringify(t||[]))}else this.tempPersonList=[],this.hiddenPersonList=[]},immediate:!0,deep:!0}},inject:{registerValidationResult:{default:null}},methods:{getFieldReadonly(e){return this.flowConfig[e]==="readonly"},handlePersonChange(e,t){this.formData[t.prop]=t.userName,this.formData[t.prop+"FullName"]=t.userFullName,this.isCcInfo?this.$emit("setBpmPerson",{ccParamList:[...this.tempPersonList,...this.hiddenPersonList]}):this.$emit("setBpmPerson",{approverParamList:[...this.tempPersonList,...this.hiddenPersonList]})},isFieldRequired(e){var t;return((t=this.formRules[e])==null?void 0:t.required)===!0},validateField(e){if(!this.formRules[e])return!0;if(this.formRules[e].required){const t=this.tempPersonList.find(r=>r.prop===e);if(!(t&&t.id&&t.userFullName))return!1}return!0},validate(){let e=!0;for(const t in this.formRules)this.validateField(t)||(e=!1);return this.$emit("validate",e),e},validateFormItem(e){const{type:t,fields:r}=e;console.log("BpmPersonInfos validateFormItem 被调用:",{type:t,fields:r,personList:this.tempPersonList.length,formRules:this.formRules});for(const n of this.tempPersonList){const i=n.prop;if(console.log("处理字段:",{prop:i,hasRule:!!this.formRules[i],isRequired:!!(this.formRules[i]&&this.formRules[i].required),item:n}),this.formRules[i]&&this.formRules[i].required){const a=!r||r.includes(i);if(console.log("字段验证状态:",{prop:i,shouldValidate:a,fields:r}),a){const o=this.validateField(i);console.log("验证结果:",{prop:i,isValid:o,hasRegisterFn:!!this.registerValidationResult}),this.registerValidationResult&&this.registerValidationResult(i,{valid:o,message:o?"":"".concat(n.label||i,"不能为空，请选择")})}}}}}},Cc={class:"bpm-person-infos"};function Tc(e,t,r,n,i,a){const o=X("van-switch"),l=X("van-cell"),u=X("FormItemMultiplePerson"),h=X("FormItemPerson"),b=X("van-cell-group");return z(),ie("div",Cc,[ge(e.$slots,"before",{},void 0,!0),i.tempPersonList.length>0?(z(),he(b,{key:0,border:!1},{default:me(()=>[(z(!0),ie(Ee,null,At(i.tempPersonList,(y,k)=>(z(),ie(Ee,{key:k},[i.isCcInfo?(z(),he(l,{key:0,center:""},{title:me(()=>t[0]||(t[0]=[be("span",{class:"label"},"是否抄送",-1)])),"right-icon":me(()=>[ne(o,{modelValue:y.isCc,"onUpdate:modelValue":R=>y.isCc=R,"active-value":0,"inactive-value":-1,size:"24"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)):de("",!0),!i.isCcInfo||y.isCc===0||y.isCc==="0"?(z(),ie(Ee,{key:1},[y.isMultiple===1?(z(),he(u,{key:0,readonly:a.getFieldReadonly(y.prop),required:a.isFieldRequired(y.prop),label:y.label,title:y.label,userName:y.userName,"onUpdate:userName":R=>y.userName=R,userFullname:y.userFullName,"onUpdate:userFullname":R=>y.userFullName=R,onChange:R=>a.handlePersonChange(R,y)},null,8,["readonly","required","label","title","userName","onUpdate:userName","userFullname","onUpdate:userFullname","onChange"])):(z(),he(h,{key:1,readonly:a.getFieldReadonly(y.prop),required:a.isFieldRequired(y.prop),label:y.label,title:y.label,userName:y.userName,"onUpdate:userName":R=>y.userName=R,userFullname:y.userFullName,"onUpdate:userFullname":R=>y.userFullName=R,id:y.id,"onUpdate:id":R=>y.id=R,onChange:R=>a.handlePersonChange(R,y)},null,8,["readonly","required","label","title","userName","onUpdate:userName","userFullname","onUpdate:userFullname","id","onUpdate:id","onChange"]))],64)):de("",!0)],64))),128))]),_:1})):de("",!0),ge(e.$slots,"after",{},void 0,!0)])}const kc=yt(Rc,[["render",Tc],["__scopeId","data-v-87d93fe7"]]);const Nc={name:"BpmPersonView",componentName:"BpmPersonView",props:{personList:{type:Array,default:()=>[]},title:{type:String,default:""}},data(){return{tempPersonList:[],hiddenPersonList:[],formData:{},flowConfig:{}}},watch:{personList:{handler(e){if(e){let t=e.filter(r=>r.isVisible!==0);this.hiddenPersonList=e.filter(r=>r.isVisible===0),this.tempPersonList=JSON.parse(JSON.stringify(t||[])),console.log("this.tempPersonList===>",this.tempPersonList)}else this.tempPersonList=[],this.hiddenPersonList=[]},immediate:!0,deep:!0}},methods:{formatDate(e){return e?this.$dayjs(e).format("YYYY-MM-DD HH:mm:ss"):""}}},Mc={class:"bpm-person-view"};function Dc(e,t,r,n,i,a){const o=X("van-field"),l=X("van-cell-group");return z(),ie("div",Mc,[ge(e.$slots,"before",{},void 0,!0),i.tempPersonList.length>0?(z(),he(l,{key:0,border:!1},{default:me(()=>[(z(!0),ie(Ee,null,At(i.tempPersonList,(u,h)=>(z(),ie(Ee,{key:h},[ne(o,{label:u.label,modelValue:u.userFullName,"onUpdate:modelValue":b=>u.userFullName=b,readonly:""},null,8,["label","modelValue","onUpdate:modelValue"]),u.approvalTime?(z(),he(o,{key:0,label:u.approvalTimeLabel||"审核日期",value:a.formatDate(u.approvalTime),readonly:""},null,8,["label","value"])):de("",!0)],64))),128))]),_:1})):de("",!0),ge(e.$slots,"after",{},void 0,!0)])}const Ic=yt(Nc,[["render",Dc],["__scopeId","data-v-d92c9a21"]]);const Oc={name:"FlowForm",components:{FlowTimeline:va,FlowBmp:fc,UploadFiles:ca,BpmInitiatorInfos:yc,BpmPersonInfos:kc,BpmPersonView:Ic},props:{modelKey:String,formKey:String,entityName:String,detailParamList:{type:Array,default:()=>[]},detailEntityNameList:{type:Array,default:()=>[]},allTaskKeys:{type:Array,default:()=>[]},bpmPersonObj:{type:Object,default:()=>({approverConfigList:[],ccUserConfigList:[],approverParamList:[],ccParamList:[],initiatorInfo:null})},beforeSubmit:{type:Function,default:null},validationConfig:{type:Object,default:()=>({saveDraft:void 0,submit:void 0,reject:["comment"],abandon:["comment"]})}},emits:["afterSubmit","draftClick","submitClick","abandonClick","afterAbandon","rejectClick","afterReject","backSubmitClick","afterBackSubmit","copyCallBack","changeComment","setAllTaskKeys"],inject:["appReload2"],provide(){return{registerValidationResult:this.registerValidationResult,validateFormItem:this.validateFormItem}},setup(e,{attrs:t,slots:r,emit:n}){const[i,a]=tt(Zi),[o,l]=tt(ea),[u,h]=tt(ta),[b,y]=tt(ra),[k,R]=tt(na),[V,$]=tt(ia),[W,H]=tt(aa),[v,N]=tt(sa);return{requestFormData:i,loadingFormData:a,requestSubmitForm:o,loadingSubmitForm:l,requestProcessStart:u,loadingProcessStart:h,requestProcessUpdate:b,loadingProcessUpdate:y,requestProcessAbandon:k,loadingProcessAbandon:R,requestProcessReject:V,loadingProcessReject:$,requestUpdateExecutor:W,loadingUpdateExecutor:H,requestFormButtons:v,loadingFormButtons:N}},data(){return{comment:"通过",fileToken:"",tabs:["详情","流转"],tab:"详情",buttonList:[],redirectPaths:{SafetyHiddenDangerNew:"SafetyHiddenDanger",UseSealApply:"UseSealApply",QualityThreat:"QualityThreat",QualityThreatSupervisor:"QualityThreat",QualityThreatNew:"QualityThreat",MeetFlow:"MeetingList"},validationResults:{}}},computed:{currentUser(){return this.$store.USER_INFO||{}},portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},saveData(){var e,t;return(t=(e=this.$bizStore)==null?void 0:e.tempData)==null?void 0:t[this.formKey]},pageQuery(){return this.$route.query||{}},taskId(){var e;return(e=this.pageQuery)==null?void 0:e.taskId},type(){var e;return(e=this.pageQuery)==null?void 0:e.type},bizId(){var e;return(e=this.pageQuery)==null?void 0:e.bizId},taskKey(){var e;return((e=this.pageQuery)==null?void 0:e.taskKey)||""},formKey(){var e;return(e=this.pageQuery)==null?void 0:e.formKey},processInstanceId(){var e;return((e=this.pageQuery)==null?void 0:e.processInstanceId)||""},redirectPathName(){return this.type==="add"?this.redirectPaths[this.formKey]:""},historyPath(){return this.$router.options.history.state.back}},watch:{taskId:{immediate:!1,handler(e,t){e&&e!=t&&this.getFormButtons()}}},created(){},mounted(){this.getFormButtons()},methods:{async copyFilling(){try{await this.$confirm({title:"提示",message:"是否复制上次提交的值?"}),this.portalId==this.saveData.portalId?this.$emit("copyCallBack",this.saveData):this.$showToast("门户不同不能复制")}catch(e){console.log(e)}finally{}},async onInit(e,t){try{this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const r={id:this.bizId,entityName:this.entityName,detailEntityNameList:this.detailEntityNameList},n=await this.requestFormData(e,r);t&&typeof t=="function"&&t(n)}catch(r){}finally{this.$closeToast()}},async getFormButtons(){var e;try{if(this.type!=="execute"||!this.taskId)return;const t={taskId:this.taskId},r=await this.requestFormButtons(t),n=JSON.parse(r.buttonList);let i=[];(e=n==null?void 0:n.taskButtonList)!=null&&e.length?i=n.taskButtonList.sort((a,o)=>this.sortButtonType(o.type)-this.sortButtonType(a.type)):i=n==null?void 0:n.sort((a,o)=>this.sortButtonType(o.type)-this.sortButtonType(a.type)),i=i.filter(a=>["submit","saveDraft","reject","abandon"].includes(a.type)),this.buttonList=[...i]}catch(t){}finally{}},sortButtonType(e){switch(e){case"submit":return 1;case"saveDraft":return 2;case"reject":return 3;case"abandon":return 4;case"addCountersignee":return 5;case"edit":return 6;case"circulate":return 7;default:return 8}},buttonTypeStyle(e){switch(e){case"submit":return"primary";case"abandon":return"danger";default:return"default"}},async handleButtonClick(e){console.log("buttonInfo ==>",e);const{type:t,targetNode:r}=e;if(await this.validateForAction(e))switch(t){case"saveDraft":{this.$emit("draftClick",{buttonInfo:e});return}case"submit":{this.$emit("submitClick",{buttonInfo:e});return}case"abandon":{this.onAbandon();return}case"reject":{this.onReject(r);return}default:this.$showToast({message:"暂不支持该操作!"});return}},async handleFormAction(e){var h,b,y,k,R,V,$,W,H,v,N,w,S,C;const{url:t,formData:r,buttonInfo:n={type:e.actionType||"submit"},actionType:i="submit",confirmMsg:a="确定提交?",loadingMsg:o="正在提交...",successMsg:l="提交成功!"}=e,u=i==="submit";try{await this.$confirm({title:"提示",message:a}),this.$showToast({type:"loading",loadingType:"spinner",forbidClick:!0,message:o,duration:0});let P={approverConfigList:(h=this.bpmPersonObj)==null?void 0:h.approverConfigList,ccUserConfigList:(b=this.bpmPersonObj)==null?void 0:b.ccUserConfigList,approverParamList:(y=this.bpmPersonObj)==null?void 0:y.approverParamList,ccParamList:(k=this.bpmPersonObj)==null?void 0:k.ccParamList,initiatorInfo:(R=this.bpmPersonObj)==null?void 0:R.initiatorInfo};console.log("this.beforeSubmit",this.beforeSubmit);let _={...r};if(this.beforeSubmit){const O=await this.beforeSubmit(n,P);if(O===!1)return;O&&(_=O)}_.notifyMethod?Array.isArray(_.notifyMethod)&&(_.notifyMethod=JSON.stringify(_.notifyMethod)):_.notifyMethod=JSON.stringify([0]);let g=JSON.parse(JSON.stringify(this.bpmPersonObj.approverParamList||[]));Array.isArray(_.approverParamList)&&(g=g.map((O,se)=>{let Y=_.approverParamList.find(K=>K.prop===O.prop);return{...O,...Y}}),delete _.approverParamList),(V=this.bpmPersonObj)!=null&&V.initiatorInfo&&g.push(($=this.bpmPersonObj)==null?void 0:$.initiatorInfo);let x=JSON.parse(JSON.stringify(((W=this.bpmPersonObj)==null?void 0:W.ccParamList)||[]));Array.isArray(_.ccParamList)&&(x=x.map((O,se)=>{let Y=_.ccParamList.find(K=>K.prop===O.prop);return{...O,...Y}}),delete _.ccParamList);const D={formKey:this.formKey,bizId:this.bizId,taskId:this.taskId,taskKey:this.taskKey,entityName:this.entityName,notifyMethod:_.notifyMethod};n.code===dn._Object.saveDraft.code&&(_.processState=3);for(let O=0;O<g.length;O++){g[O]={...g[O],...D};let se=g[O];_[se.prop]=se.userName||""}for(let O=0;O<x.length;O++){x[O]={...x[O],...D};for(let Y=0;Y<((H=this.bpmPersonObj)==null?void 0:H.ccParamList.length);Y++)if(this.bpmPersonObj.ccParamList[Y].prop===x[O].prop){(v=this.bpmPersonObj)!=null&&v.ccParamList[Y].notifyMethod||(this.bpmPersonObj.ccParamList[Y].notifyMethod=x[O].notifyMethod);break}let se=x[O];_[se.prop]=se.userName||""}if(n.code===dn._Object.submit.code&&Array.isArray(this.allTaskKeys)&&this.allTaskKeys.length>0)for(let O=0;O<this.allTaskKeys.length;O++){let se=this.allTaskKeys[O];Array.isArray(se)&&se.length>0&&this.taskKey===se[se.length-1]&&(g=[])}const T={entityName:this.entityName,entityObject:_,detailParamList:this.detailParamList,formProcessParam:this.useProcess(n,_),approverParamList:g,ccParamList:x},M=await this.requestSubmitForm(t,T);if(this.$emit("afterSubmit",n,T),this.$closeToast(),u&&this.type==="add"&&this.$bizStore.saveData({[this.formKey]:{...r}}),u&&((N=this.bpmPersonObj)!=null&&N.ccParamList)&&((w=this.bpmPersonObj)==null?void 0:w.ccParamList.length)>0)for(let O=0;O<((S=this.bpmPersonObj)==null?void 0:S.ccParamList.length);O++){const se=(C=this.bpmPersonObj)==null?void 0:C.ccParamList[O];this.ccSend(se.userName,se.userFullname,se.notifyMethod)}this.$nextTick(()=>{this.$showToast({message:l,icon:"success",forbidClick:!0,duration:1500}),setTimeout(()=>{this.routerJump()},1500)})}catch(P){console.log(P),this.$closeToast()}},async onSaveDraft(e,t,r={type:"saveDraft"}){await this.validateForAction(r)&&await this.handleFormAction({url:e,formData:t,buttonInfo:r,actionType:"saveDraft",confirmMsg:"确定暂存?",loadingMsg:"正在暂存...",successMsg:"暂存成功!"})},async onSubmit(e,t,r={type:"submit"}){await this.validateForAction(r)&&await this.handleFormAction({url:e,formData:t,buttonInfo:r,actionType:"submit",confirmMsg:"确定提交?",loadingMsg:"正在提交...",successMsg:"提交成功!"})},async onAbandon(){try{await this.$confirm({title:"提示",message:"确定废弃?"}),this.$showToast({type:"loading",loadingType:"spinner",forbidClick:!0,message:"正在废弃...",duration:0});const e={taskId:this.taskId,bizId:this.bizId,formKey:this.formKey,comment:this.comment||"",fileToken:this.fileToken||""};this.$emit("abandonClick",e),await this.requestProcessAbandon(e),this.$emit("afterAbandon"),this.$closeToast(),this.$nextTick(()=>{this.$showToast({message:"废弃成功!",icon:"success",forbidClick:!0,duration:1500}),setTimeout(()=>{this.routerJump()},1500)})}catch(e){console.log(e),this.$closeToast()}finally{}},async onReject(e){try{await this.$confirm({title:"提示",message:"确定退回?"}),this.$showToast({type:"loading",loadingType:"spinner",forbidClick:!0,message:"正在退回...",duration:0});const t={taskId:this.taskId,targetKey:e[0],comment:this.comment||"",fileToken:this.fileToken||""};this.$emit("rejectClick",t);const r=await this.requestProcessReject(t);this.$emit("afterReject"),this.$closeToast(),this.$nextTick(()=>{this.$showToast({message:"退回成功!",icon:"success",forbidClick:!0,duration:1500}),setTimeout(()=>{this.routerJump()},1500)})}catch(t){console.log(t),this.$closeToast()}finally{}},async onBackSubmit(e,t){try{const r={entityName:this.entityName,detailParamList:this.detailParamList,entityObject:{...t}};this.$emit("backSubmitClick",r),await this.requestSubmitForm(e,r),this.$emit("afterBackSubmit")}catch(r){console.log(r)}},getComment(){return this.comment},setComment(e){this.comment=e},handleCommentChange(e){this.$emit("changeComment",e)},routerJump(){this.redirectPathName?this.$router.replace({name:this.redirectPathName}):this.historyPath==="/Tasks"?this.handleNextTask({page:1,size:10}):this.$router.back()},async handleNextTask({page:e=1,size:t=10}){try{const{list:r=[],nextPage:n,hasNextPage:i}=await oa({page:e,size:t});if(r.length===0){this.$router.replace({name:"Tasks"});return}let a=r.find(o=>pa.includes(o.formKey));if(a){const o=this.$showLoadingToast({duration:2e3,forbidClick:!0,message:"3秒后进入下一条待办"});let l=3;const u=setInterval(()=>{if(l--,l)o.message="".concat(l,"秒后进入下一条待办");else{clearInterval(u),this.$closeToast();const h={path:"/FormCenter/".concat(a.formKey),query:{type:"execute",taskKey:a.taskKey,formKey:a.formKey,bizId:a.formBizId,taskId:a.taskId,processInstanceId:a.processInstanceId,title:a.formName}};this.$bizStore.saveData({redirectParams:h}),this.$router.replace({name:"NextTaskRedirect"})}},1e3)}else i?this.handleNextTask({page:n,size:t}):this.$router.replace({name:"Tasks"})}catch(r){console.log("catch next",r),this.$router.replace({name:"Tasks"}),this.appReload2()}},useProcess(e={},t={}){return{modelKey:this.modelKey,bizId:this.bizId,processInstanceId:this.processInstanceId,formKey:this.formKey,stageFlag:e.type==="saveDraft"?1:0,variable:t,taskId:this.taskId,approval:e.type==="saveDraft"?"stage":"",comment:this.comment||"",targetKey:e.targetKey||""}},setInitiatorInfo(e){this.bpmPersonObj.initiatorInfo=e},setBpmPerson(e){let{approverConfigList:t,ccUserConfigList:r,approverParamList:n,ccParamList:i,initiatorInfo:a}=e;t&&(this.bpmPersonObj.approverConfigList=t||[]),r&&(this.bpmPersonObj.ccUserConfigList=r||[]),n&&(this.bpmPersonObj.approverParamList=n||[]),i&&(this.bpmPersonObj.ccParamList=i||[]),a&&(this.bpmPersonObj.initiatorInfo=a)},setPropConfig(e=[]){var t,r;if(Array.isArray(e)){let n=e.filter(i=>i.type===1);this.bpmPersonObj.approverConfigList=(t=this.bpmPersonObj)==null?void 0:t.approverConfigList.map((i,a)=>{let o=n.find(l=>l.prop===i.prop);return{...i,...o}}),this.bpmPersonObj.ccUserConfigList=(r=this.bpmPersonObj)==null?void 0:r.ccUserConfigList.map((i,a)=>{let o=e.find(l=>l.prop===i.prop);return{...i,...o}})}},async ccSend(e,t,r){var n,i,a,o;if(e)try{let l=JSON.stringify([0]);Array.isArray(r)&&(l=JSON.stringify(r));const u={formBizId:this.bizId,formKey:this.formKey,processInstanceId:this.processInstanceId,processType:"1",receiverUserName:e,receiverUserFullName:t,notify:l,senderUserName:((n=this.currentUser)==null?void 0:n.userName)||"",senderUserFullName:((i=this.currentUser)==null?void 0:i.userFullname)||"",taskId:this.taskId,taskKey:this.taskKey,title:((a=this.pageQuery)==null?void 0:a.title)||"",sourceId:this.portalId||"",sourceName:((o=this.portal)==null?void 0:o.name)||""};Qi(u).then(h=>{console.log("".concat(this.taskKey," 抄送成功！"),u)})}catch(l){console.error("抄送失败",l)}},setAllTaskKeys(e){this.allTaskKeys=e},validateForAction(e){console.log("validateForAction 方法被调用:",e);const t=e.type,r=this.validationConfig[t];this.validationResults={};const n=[this.$refs.initiatorInfos,this.$refs.personInfos].filter(Boolean);console.log("验证组件情况:",{buttonInfo:e,fieldsToValidate:r,initiatorInfos:!!this.$refs.initiatorInfos,personInfos:!!this.$refs.personInfos,componentsToValidate:n.length,componentsList:n});for(const i of n)i!=null&&i.validateFormItem&&i.validateFormItem({type:t,fields:r});return new Promise(i=>{setTimeout(()=>{const a=Object.values(this.validationResults);if(a.some(l=>!l.valid)){const l=a.find(u=>!u.valid);l&&l.message?this.$showToast({message:l.message}):this.$showToast({message:"表单验证未通过，请检查必填项"}),i(!1)}else i(!0)},100)})},registerValidationResult(e,t){this.validationResults[e]=t}}},Lc={class:"form-wp"},Bc={class:"buttons"},Fc={class:"form-wp"},jc={class:"buttons"},Vc={class:"form-wp"};function $c(e,t,r,n,i,a){const o=X("Navbar"),l=X("van-tab"),u=X("van-tabs"),h=X("BpmInitiatorInfos"),b=X("BpmPersonInfos"),y=X("van-form"),k=X("van-button"),R=X("BpmPersonView"),V=X("van-field"),$=X("van-cell-group"),W=X("FlowTimeline"),H=X("FlowBmp");return z(),ie(Ee,null,[ne(o,{back:"",title:a.pageQuery.title},{right:me(()=>[ge(e.$slots,"NavbarRight",{},()=>[a.saveData&&a.portalId==a.saveData.portalId&&a.type==="add"?(z(),ie("span",{key:0,class:"color-[#fff]",onClick:t[0]||(t[0]=(...v)=>a.copyFilling&&a.copyFilling(...v))},"复制填写")):de("",!0)],!0)]),_:3},8,["title"]),a.type!=="add"?(z(),he(u,{key:0,class:"tabs-wp",active:i.tab,"onUpdate:active":t[1]||(t[1]=v=>i.tab=v),"line-width":"4em"},{default:me(()=>[(z(!0),ie(Ee,null,At(i.tabs,v=>(z(),he(l,{class:"px-[20px]",key:v,name:v,title:v},null,8,["name","title"]))),128))]),_:1},8,["active"])):de("",!0),be("div",{class:Ln(["view-height",{"has-tab":a.type!=="add"}])},[a.type==="add"?(z(),ie(Ee,{key:0},[be("div",Lc,[ge(e.$slots,"default",{},void 0,!0),ne(y,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:me(()=>[ge(e.$slots,"initiator-infos",{},()=>[ne(h,{ref:"initiatorInfos",info:r.bpmPersonObj.initiatorInfo,onSetInitiatorInfo:a.setInitiatorInfo},null,8,["info","onSetInitiatorInfo"])],!0),ge(e.$slots,"person-section",{},()=>[ge(e.$slots,"approver-select",{},()=>[r.bpmPersonObj.approverConfigList.length>0?(z(),he(b,{key:0,ref:"personInfos",type:1,"person-list":r.bpmPersonObj.approverConfigList,onSetBpmPerson:a.setBpmPerson},null,8,["person-list","onSetBpmPerson"])):de("",!0)],!0),ge(e.$slots,"cc-select",{},()=>[r.bpmPersonObj.ccUserConfigList.length>0?(z(),he(b,{key:0,ref:"personInfosCc",type:2,"person-list":r.bpmPersonObj.ccUserConfigList,onSetBpmPerson:a.setBpmPerson},null,8,["person-list","onSetBpmPerson"])):de("",!0)],!0)],!0)]),_:3},512)]),be("div",Bc,[ne(k,{class:"button",round:"",block:"",plain:"",type:"primary",onClick:t[2]||(t[2]=v=>e.$emit("draftClick"))},{default:me(()=>t[5]||(t[5]=[Ft(" 暂存 ")])),_:1,__:[5]}),ne(k,{class:"button",round:"",block:"",type:"primary",onClick:t[3]||(t[3]=v=>e.$emit("submitClick"))},{default:me(()=>t[6]||(t[6]=[Ft(" 提交 ")])),_:1,__:[6]})])],64)):(z(),ie(Ee,{key:1},[pn(be("div",Fc,[ge(e.$slots,"default",{},void 0,!0),ne(y,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:me(()=>[ge(e.$slots,"initiator-infos",{},()=>[ne(h,{ref:"initiatorInfos",info:r.bpmPersonObj.initiatorInfo,onSetInitiatorInfo:a.setInitiatorInfo},null,8,["info","onSetInitiatorInfo"])],!0),ge(e.$slots,"person-section",{},()=>[a.type!=="view"?(z(),ie(Ee,{key:0},[ge(e.$slots,"approver-select",{},()=>[r.bpmPersonObj.approverConfigList.length>0?(z(),he(b,{key:0,ref:"personInfos",type:1,"person-list":r.bpmPersonObj.approverConfigList,onSetBpmPerson:a.setBpmPerson},null,8,["person-list","onSetBpmPerson"])):de("",!0)],!0),ge(e.$slots,"cc-select",{},()=>[r.bpmPersonObj.ccUserConfigList.length>0?(z(),he(b,{key:0,ref:"personInfosCc",type:2,"person-list":r.bpmPersonObj.ccUserConfigList,onSetBpmPerson:a.setBpmPerson},null,8,["person-list","onSetBpmPerson"])):de("",!0)],!0)],64)):de("",!0),a.type==="view"?(z(),ie(Ee,{key:1},[ge(e.$slots,"approver-view",{},()=>[r.bpmPersonObj.approverParamList.length>0?(z(),he(R,{key:0,ref:"personView",title:"审批人","person-list":r.bpmPersonObj.approverParamList},null,8,["person-list"])):de("",!0)],!0),ge(e.$slots,"cc-view",{},()=>[r.bpmPersonObj.ccParamList.length>0?(z(),he(R,{key:0,ref:"personViewCc",title:"抄送人","person-list":r.bpmPersonObj.ccParamList},null,8,["person-list"])):de("",!0)],!0)],64)):de("",!0)],!0)]),_:3},512),a.type==="execute"?(z(),ie(Ee,{key:0},[a.taskKey!=="UserTask_0"?(z(),he(y,{key:0,ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:me(()=>[ne($,{border:!1,class:"mt[10px]"},{default:me(()=>[ne(V,{label:"处理意见",modelValue:i.comment,"onUpdate:modelValue":[t[4]||(t[4]=v=>i.comment=v),a.handleCommentChange],rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入处理意见",required:!0,"input-align":"left",readonly:a.type==="view"},null,8,["modelValue","readonly","onUpdate:modelValue"])]),_:1})]),_:1},512)):de("",!0),be("div",jc,[(z(!0),ie(Ee,null,At([...i.buttonList],(v,N)=>(z(),he(k,{key:N,class:"button",round:"",block:"",plain:v.type!=="submit",type:a.buttonTypeStyle(v.type),onClick:w=>a.handleButtonClick(v)},{default:me(()=>[Ft(rt(v.text),1)]),_:2},1032,["plain","type","onClick"]))),128))])],64)):de("",!0)],512),[[fn,i.tab==="详情"]]),pn(be("div",Vc,[ne(W,{"biz-id":a.bizId,"task-id":a.taskId},null,8,["biz-id","task-id"]),ne(H,{show:i.tab==="流转","model-key":r.modelKey,"biz-id":a.bizId},null,8,["show","model-key","biz-id"])],512),[[fn,i.tab==="流转"]])],64))],2)],64)}const Gc=yt(Oc,[["render",$c],["__scopeId","data-v-e6f48463"]]);export{Gc as F,Ac as a,dn as b};
