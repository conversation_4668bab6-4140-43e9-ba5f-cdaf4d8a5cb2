import{F as S,D as y}from"./index-a831f9da.js";import{_ as B}from"./index-4829f8e2.js";import{Q as g,R as a,X as A,V as u,U as t,S as m,W as f,F as h,Y as b}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const L={name:"JL36",components:{FormTemplate:S,DocumentPart:y},emits:[],props:{},setup(s,{attrs:e,slots:i,emit:c}){},data(){return{detailTable:[{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:s,detailParamList:e}){},onBeforeSubmit({formData:s,detailParamList:e,taskStart:i},c){return new Promise((l,o)=>{try{l()}catch(r){o(r)}})}}},P={class:"jl-table"},j={class:"cell"},x={class:"cell"};function $(s,e,i,c,l,o){const r=g("FormTemplate");return a(),A(r,{ref:"FormTemplate",nature:"监发","on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":l.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:l.attachmentDesc},{default:u(({formData:k,formTable:d,baseObj:v,uploadAccept:w,taskStart:_,taskComment2:D,taskComment3:C,taskComment4:T})=>[t("table",P,[e[1]||(e[1]=t("colgroup",null,[t("col",{width:"30"}),t("col",{"min-width":"40"}),t("col",{"min-width":"40"}),t("col",{"min-width":"40"}),t("col",{"min-width":"40"}),t("col",{"min-width":"40"}),t("col",{"min-width":"40"}),t("col",{"min-width":"40"})],-1)),t("tbody",null,[e[0]||(e[0]=t("tr",null,[t("th",null,[t("div",{class:"cell"},"序号")]),t("th",null,[t("div",{class:"cell"},"文号")]),t("th",null,[t("div",{class:"cell"},"文件名称")]),t("th",null,[t("div",{class:"cell"},"发送单位")]),t("th",null,[t("div",{class:"cell"},"抄送单位")]),t("th",null,[t("div",{class:"cell"},"发文时间")]),t("th",null,[t("div",{class:"cell"},"收文时间")]),t("th",null,[t("div",{class:"cell"},"签收人")])],-1)),(a(!0),m(h,null,f(d,(F,n)=>(a(),m("tr",{key:n},[t("td",null,[t("div",j,[t("span",null,b(n+1),1)])]),(a(),m(h,null,f(7,p=>t("td",{key:"".concat(n,"_").concat(p)},[t("div",x,[t("span",null,b(F["field".concat(p)]),1)])])),64))]))),128))])])]),footer:u(({formData:k,formTable:d,baseObj:v,uploadAccept:w,taskStart:_,taskComment2:D,taskComment3:C,taskComment4:T})=>e[2]||(e[2]=[t("div",{class:"footer-input"},[t("div",null,"说明：本表应妥善保存。")],-1)])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const H=B(L,[["render",$]]);export{H as default};
