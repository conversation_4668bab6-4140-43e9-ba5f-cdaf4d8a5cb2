import{F as w,D as N}from"./index-a831f9da.js";import{_ as U}from"./index-4829f8e2.js";import{Q as r,R as c,X as F,V as p,k as a,U as s,Y as l,B as f,S as P,Z as B}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const L={name:"JL16",components:{FormTemplate:w,DocumentPart:N},emits:[],props:{},setup(t,{attrs:e,slots:b,emit:k}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:t,detailParamList:e}){},onBeforeSubmit({formData:t,detailParamList:e},b){return new Promise((k,m)=>{try{if(b==="submit"&&taskStart&&!t.check1&&!t.check2)return this.$showNotify({type:"danger",message:"请选择复工范围：全部/部分",duration:3*1e3}),m(!1),!1;k()}catch(d){m(d)}})},changeCheck1(t){t&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.field7="",this.$nextTick(()=>{var e;(e=this.$refs.FormTemplate.$refs.form)==null||e.resetValidation()}))},changeCheck2(t){t&&(this.$refs.FormTemplate.formData.check1=!1)}}},q={class:"one-line"},A={class:"form-info"},O={class:"form-info"},$={class:"check-wp",style:{"text-indent":"0"}},I={class:"form-info"},W={class:"form-info"},z={class:"form-info"},E={class:"form-info"},J={class:"form-info"},Q={class:"comment-wp"},R={class:"one-line"},X={class:"check-wp"},Y={style:{padding:"0 5px"}},Z={style:{padding:"0 5px"}},j={class:"one-line"},G={class:"check-wp"},H={style:{padding:"0 5px"}},K={style:{padding:"0 5px"}},M={key:0,class:"textarea-wp"},S={class:"footer-input"},D={class:"form-info"},ee={class:"form-info"},se={class:"form-info"},ne={class:"form-info"},le={class:"form-info"};function te(t,e,b,k,m,d){const u=r("van-checkbox"),_=r("van-field"),h=r("DocumentPart"),g=r("FormTemplate");return c(),F(g,{ref:"FormTemplate",nature:"复工","on-after-init":d.onAfterInit,"on-before-submit":d.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!1,attachmentDesc:m.attachmentDesc},{default:p(({formData:n,formTable:C,baseObj:V,uploadAccept:v,taskStart:i,taskComment2:x,taskComment3:y,taskComment4:T})=>[a(h,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:n.supervisionDeptName,deptOptions:V.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!i},{default:p(()=>[s("div",q,[e[3]||(e[3]=s("span",{style:{"padding-left":"2em"}},"鉴于暂停施工指示（ 监理 [ ",-1)),s("span",A,l(n.field1),1),e[4]||(e[4]=s("span",null," ] 停工",-1)),s("span",O,l(n.field2),1),e[5]||(e[5]=s("span",null,"号）",-1)),e[6]||(e[6]=s("span",null,"所述原因已经",-1)),s("div",$,[a(u,{modelValue:n.check1,"onUpdate:modelValue":o=>n.check1=o,shape:"square",disabled:!i,onChange:d.changeCheck1},{default:p(()=>e[0]||(e[0]=[f("全部")])),_:2,__:[0]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),e[2]||(e[2]=s("span",null," / ",-1)),a(u,{modelValue:n.check2,"onUpdate:modelValue":o=>n.check2=o,shape:"square",disabled:!i,onChange:d.changeCheck2},{default:p(()=>e[1]||(e[1]=[f("部分")])),_:2,__:[1]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[7]||(e[7]=s("span",null,"消除，",-1)),e[8]||(e[8]=s("span",null,"你方可于",-1)),s("span",I,l(n.field3),1),e[9]||(e[9]=s("span",null,"年",-1)),s("span",W,l(n.field4),1),e[10]||(e[10]=s("span",null,"月",-1)),s("span",z,l(n.field5),1),e[11]||(e[11]=s("span",null,"日",-1)),s("span",E,l(n.field6),1),e[12]||(e[12]=s("span",null,"时起对",-1)),s("span",J,l(n.projectName),1),e[13]||(e[13]=s("span",null,"工程",-1)),e[14]||(e[14]=s("span",null,"下列范围恢复施工。",-1))]),s("div",Q,[e[23]||(e[23]=s("div",null,"复工范围：",-1)),s("div",R,[s("div",X,[a(u,{modelValue:n.check1,"onUpdate:modelValue":o=>n.check1=o,shape:"square",disabled:!i,onChange:d.changeCheck2},{default:p(()=>e[15]||(e[15]=[f(" ")])),_:2,__:[15]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[16]||(e[16]=s("span",null,"监理 [ ",-1)),s("span",Y,l(n.field1),1),e[17]||(e[17]=s("span",null," ] 停工 ",-1)),s("span",Z,l(n.field2),1),e[18]||(e[18]=s("span",null," 号指示的全部暂停施工项目",-1))]),s("div",j,[s("div",G,[a(u,{modelValue:n.check2,"onUpdate:modelValue":o=>n.check2=o,shape:"square",disabled:!i,onChange:d.changeCheck2},{default:p(()=>e[19]||(e[19]=[f(" ")])),_:2,__:[19]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[20]||(e[20]=s("span",null,"监理 [ ",-1)),s("span",H,l(n.field1),1),e[21]||(e[21]=s("span",null," ] 停工 ",-1)),s("span",K,l(n.field2),1),e[22]||(e[22]=s("span",null," 号指示的下列暂停施工项目",-1))]),n.check2?(c(),P("div",M,[a(_,{modelValue:n.field7,"onUpdate:modelValue":o=>n.field7=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"请填写暂停施工项目",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])):B("",!0)])]),_:2},1032,["deptValue","deptOptions","disabled"]),a(h,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:n.epcDeptName,deptOptions:V.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!i},{default:p(()=>e[24]||(e[24]=[s("div",{class:"comment-wp"},[s("div",{style:{height:"30px"}})],-1)])),_:2,__:[24]},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:n,formTable:C,baseObj:V,uploadAccept:v,taskStart:i,taskComment2:x,taskComment3:y,taskComment4:T})=>[s("div",S,[e[25]||(e[25]=s("span",null,"说明：本表一式",-1)),s("span",D,l(n.num1),1),e[26]||(e[26]=s("span",null,"份，由监理机构填写，承包人签收后，发包人",-1)),s("span",ee,l(n.num2),1),e[27]||(e[27]=s("span",null,"份，设代机构",-1)),s("span",se,l(n.num3),1),e[28]||(e[28]=s("span",null,"份，监理机构",-1)),s("span",ne,l(n.num4),1),e[29]||(e[29]=s("span",null,"份，承包人",-1)),s("span",le,l(n.num5),1),e[30]||(e[30]=s("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ge=U(L,[["render",te]]);export{ge as default};
