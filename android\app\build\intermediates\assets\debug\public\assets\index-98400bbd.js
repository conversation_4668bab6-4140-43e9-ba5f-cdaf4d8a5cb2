import{F as P,D as T}from"./index-1be3ad72.js";import{_ as x}from"./index-4829f8e2.js";import{Q as f,R as _,X as B,V as d,k as i,U as t,Y as l,S as h,W as F,F as O}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const U={name:"CB15",components:{FormTemplate:P,DocumentPart:T},emits:[],props:{},setup(u,{attrs:e,slots:v,emit:V}){},data(){return{detailTable:[],attachmentDesc:"",tableList:[{content:"施工技术交底和安全交底情况"},{content:"主要施工设备到位情况"},{content:"施工安全、质量保证措施落实情况"},{content:"工程设备检查和验收情况"},{content:"原材料,中间产品质量及准备情况"},{content:"现场施工人员安排情况"},{content:"风、水、电等必须的辅助生产设施准备情况"},{content:"场地平整、交通、临时设施准备情况"},{content:"测量放样情况"},{content:"工艺试验情况"}]}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:u,detailParamList:e}){},onBeforeSubmit({formData:u,detailParamList:e,taskComment3:v},V){return new Promise((o,p)=>{try{o()}catch(m){p(m)}})}}},A={class:"one-line"},W={class:"form-info"},g={class:"cb-table"},z={colspan:"2"},E={class:"cell"},I={class:"cell",style:{"text-align":"center"}},S={class:"cell"},Q={class:"cell"},R={class:"attachment-desc"},X={class:"comment-wp"},Y={class:"textarea-wp"},q={class:"footer-input"},G={class:"form-info"},H={class:"form-info"},J={class:"form-info"},K={class:"form-info"};function M(u,e,v,V,o,p){const m=f("van-field"),c=f("DocumentPart"),w=f("FormTemplate");return _(),B(w,{ref:"FormTemplate",nature:"分开工","on-after-init":p.onAfterInit,"on-before-submit":p.onBeforeSubmit,"detail-table":o.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:o.attachmentDesc},{default:d(({formData:n,formTable:C,baseObj:r,uploadAccept:k,taskStart:s,taskComment2:y,taskComment3:L,taskComment4:N,taskComment5:D})=>[i(c,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:n.constructionDeptName,deptOptions:r.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!s},{default:d(()=>[t("div",A,[t("span",W,l(n.field1),1),e[0]||(e[0]=t("span",null,"已具备开工条件，",-1)),e[1]||(e[1]=t("span",null,"施工准备已就绪，",-1)),e[2]||(e[2]=t("span",null,"请贵方审批。",-1))]),t("table",g,[t("tbody",null,[t("tr",null,[e[3]||(e[3]=t("th",{colspan:"2",style:{width:"25%"}},[t("div",{class:"cell"},"申请开工分部工程名称、编码")],-1)),t("td",z,[t("div",E,l(n.field2),1)])]),e[4]||(e[4]=t("tr",null,[t("th",{rowspan:"11"},[t("div",{class:"cell"},"承包人施工准备工作自检记录")]),t("td",null,[t("div",{class:"cell"},"序号")]),t("td",null,[t("div",{class:"cell"},"检查内容")]),t("td",null,[t("div",{class:"cell"},"检查结果")])],-1)),(_(!0),h(O,null,F(o.tableList||[],(a,b)=>(_(),h("tr",{key:b},[t("td",null,[t("div",I,l(b+1),1)]),t("td",null,[t("div",S,l(a.content),1)]),t("td",null,[t("div",Q,l(n["field".concat(b+3)]),1)])]))),128))])]),t("div",R,[e[5]||(e[5]=t("div",null,"附件：",-1)),i(m,{modelValue:n.attachmentDesc,"onUpdate:modelValue":a=>n.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),i(c,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:n.epcDeptName,deptOptions:r.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!s},{default:d(()=>[t("div",X,[e[6]||(e[6]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",Y,[i(m,{modelValue:n.comment2,"onUpdate:modelValue":a=>n.comment2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!y},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),i(c,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:n.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!s},{default:d(()=>e[7]||(e[7]=[t("div",{class:"comment-wp"},[t("div",null,"审核后另行批复。")],-1)])),_:2,__:[7]},1032,["deptValue","deptOptions","disabled"])]),footer:d(({formData:n,formTable:C,baseObj:r,uploadAccept:k,taskStart:s,taskComment2:y,taskComment3:L,taskComment4:N,taskComment5:D})=>[t("div",q,[e[8]||(e[8]=t("span",null,"说明：本表一式",-1)),t("span",G,l(n.num1),1),e[9]||(e[9]=t("span",null,"份，由承包人填写，监理机构签收后，发包人",-1)),t("span",H,l(n.num2),1),e[10]||(e[10]=t("span",null,"份，监理机构",-1)),t("span",J,l(n.num3),1),e[11]||(e[11]=t("span",null,"份，承包人",-1)),t("span",K,l(n.num4),1),e[12]||(e[12]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ut=x(U,[["render",M]]);export{ut as default};
