import{h as b,_ as I}from"./index-4829f8e2.js";import{F as E}from"./index-8d635ba7.js";import{F as T}from"./FormItemPicker-d3f69283.js";import{F as q}from"./FormItemDate-ba00d9d5.js";import{F as V}from"./FormItemCalendar-905fde75.js";import{F as A}from"./FormItemCascader-c665b251.js";import{F as L}from"./FormItemPerson-bd0e3e57.js";import{F as _}from"./FormItemCoord-9e82e1bf.js";import{U as j}from"./index-fc22947f.js";import{a as K}from"./common-3f45b198.js";import{Q as m,R as s,S as c,k as n,a7 as B,V as d,B as k,U as g,Y as z,F as C,W as M,X as D,y as O,Z as f,a2 as H}from"./verder-361ae6c7.js";import"./vant-91101745.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./array-15ef8611.js";import"./validate-2249584f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";function Y(a){return b({url:"/cybereng-safety/safety/hiddenDanger/delayTask",method:"get",params:a})}function W(a){return b({url:"/cybereng-safety/safety/hiddenDanger/sendMessage",method:"get",params:a})}function Q(a){return b({url:"/cybereng-safety/yxst/safety/hiddenDanger",method:"put",params:a})}function X(a={}){return!a.isAsc&&!a.orderByColumn&&(a.isAsc="desc",a.orderByColumn="id"),b({url:"/cybereng-safety/risk/identification/data/list",method:"get",params:a})}const Z={name:"FormItemRiskSource",components:{},emits:["update:riskSource","update:riskSourceName","change"],props:{riskSource:[String,Number],riskSourceName:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:Boolean,readonly:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},searchData:{type:Object,default:()=>({})}},setup(a,{attrs:r,slots:o,emit:p}){},data(){return{showPicker:!1,loading:!1,finished:!1,list:[],searchParams:{pageNo:1,pageSize:200,isMobile:!0,searchValue:"",accountStatus:"1"},loaded:!1,user:{}}},computed:{portal(){return this.$store.PORTAL},selectedUser(){return this.user&&this.user.id?{...this.user}:{...this.list.find(r=>r.id==this.riskSource||r.dangerName===this.riskSourceName)}}},watch:{"searchParams.searchValue"(a){a===""&&this.onSearch(a)}},created(){},mounted(){},methods:{onSearch(a){this.finished=!1,this.searchParams.pageNo=1,this.$nextTick(()=>{this.onLoadList()})},async onLoadList(){try{this.loading=!0,this.finished=!1;const a={...this.searchData,...this.searchParams};a.pageNo===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const r=await X(a),o=this.searchParams.pageNo<=1?[]:this.list||[];this.list=[...o,...r.list],this.searchParams.pageNo++,this.list.length>=r.total&&(this.finished=!0)}catch(a){console.log(a),this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.loaded=!0,this.$closeToast()},100)}},onShowPicker(){this.readonly||(this.handleSelect({}),this.showPicker=!0,this.finished=!1,this.searchParams.pageNo=1,this.loaded||this.onSearch())},onClosePicker(){this.showPicker=!1},handleSelect(a){this.user=a},onClear(){this.readonly||(this.$emit("update:riskSource",""),this.$emit("update:riskSourceName",""))},onSelectConfirm(){const{id:a="",dangerName:r=""}=this.selectedUser||{};this.$emit("update:riskSource",a),this.$emit("update:riskSourceName",r),this.$emit("change",this.selectedUser),this.onClosePicker()}}},G={class:"pop-window"},J={class:"pop-window-header"},$={class:"van-picker__title van-ellipsis"},ee={class:"pop-window-body"},re={class:"list-content"},te={key:0,class:"p-[10px]"},ae={class:"pop-window-footer van-safe-area-bottom"};function ie(a,r,o,p,e,i){const l=m("van-button"),h=m("van-field"),N=m("van-cell"),v=m("van-empty"),S=m("van-list"),w=m("van-popup");return s(),c(C,null,[n(h,{name:o.name,"model-value":o.riskSourceName,label:o.label,required:o.required,rules:o.rules,"input-align":o.inputAlign,"error-message-align":o.errorMessageAlign,"label-width":o.labelWidth,center:"",clearable:"",readonly:"",placeholder:"请选择","right-icon":o.readonly?"":"close",onClickRightIcon:i.onClear},B({_:2},[o.readonly?void 0:{name:"button",fn:d(()=>[n(l,{size:"small",type:"primary",onClick:r[0]||(r[0]=u=>i.onShowPicker())},{default:d(()=>r[4]||(r[4]=[k("选择风险")])),_:1,__:[4]})]),key:"0"}]),1032,["name","model-value","label","required","rules","input-align","error-message-align","label-width","right-icon","onClickRightIcon"]),n(w,{show:e.showPicker,"onUpdate:show":r[3]||(r[3]=u=>e.showPicker=u),position:"bottom",closeable:"",teleport:"#app"},{default:d(()=>[g("div",G,[g("div",J,[g("div",$,z(o.title),1)]),g("div",ee,[g("div",re,[n(S,{loading:e.loading,"onUpdate:loading":r[1]||(r[1]=u=>e.loading=u),finished:e.finished,"finished-text":e.list&&e.list.length?"没有更多了":"",onLoad:i.onLoadList,"immediate-check":!1},{default:d(()=>[e.list&&e.list.length?(s(!0),c(C,{key:0},M(e.list,u=>(s(),D(N,{class:O(["person-cell",{select:i.selectedUser.id===u.id}]),key:u.id,title:u.dangerName,value:u.position,onClick:U=>i.handleSelect(u)},null,8,["class","title","value","onClick"]))),128)):(s(),c(C,{key:1},[e.loading?f("",!0):(s(),c("div",te,[n(v,{description:"暂无更多数据"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])])]),g("div",ae,[n(l,{class:"confirm-button",round:"",block:"",type:"primary",onClick:r[2]||(r[2]=H(u=>i.onSelectConfirm(),["stop","prevent"]))},{default:d(()=>r[5]||(r[5]=[k(" 确定 ")])),_:1,__:[5]})])])]),_:1},8,["show"])],64)}const ne=I(Z,[["render",ie],["__scopeId","data-v-a0b20229"]]),oe={name:"SafetyHiddenDangerNew",components:{FlowForm:E,FormItemPicker:T,FormItemDate:q,FormItemCalendar:V,FormItemPerson:L,FormItemCoord:_,UploadFiles:j,FormItemCascader:A,FormItemRiskSource:ne},props:{},emits:[],setup(a,{attrs:r,slots:o,emit:p}){},data(){var a,r;return{type:((a=this.$route.query)==null?void 0:a.type)||"",taskKey:((r=this.$route.query)==null?void 0:r.taskKey)||"",entityName:"SafetyHiddenDanger",formKey:"SafetyHiddenDangerNew",modelKey:"safety_hidden_danger_flow_new",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-safety/safety/form/query",submit:"/cybereng-safety/form/commit"},formData:{type:"",portalId:"",subProjectId:"",subProjectName:"",workAreaId:"",workAreaName:"",overdueState:"",hiddenDangerName:"",hiddenDangerCode:"",rectifyState:"",hiddenDangerLevel:"",hiddenDangerSource:"",rectifyDate:"",hiddenDangerCategory:"",hiddenDangerContent:"",beforeFileToken:"",beforeFileTokenImage:[],beforeFiles:1,constructionPost:"",longitude:"",latitude:"",riskSource:"",riskSourceName:"",createBy:"",hiddenDangerReportorFullname:"",prjDepName:"",prjDepCode:"",otherReportorFullname:"",initiateDate:"",isDangerConfirm:!1,isSupervision:!1,hiddenDangerConfirmer:"",hiddenDangerConfirmerFullname:"",hiddenDangerConfirmerDeptName:"",hiddenDangerConfirmerDeptCode:"",hiddenDangerRectifier:"",hiddenDangerRectifierFullname:"",hiddenDangerRectifierDeptName:"",hiddenDangerRectifierDeptCode:"",hiddenDangerChecker:"",hiddenDangerCheckerFullname:"",hiddenDangerCheckerDeptName:"",hiddenDangerCheckerDeptCode:"",hiddenDangerSupervisor:"",hiddenDangerSupervisorFullname:"",hiddenDangerSupervisorDeptName:"",hiddenDangerSupervisorDeptCode:"",hiddenDangerRectifyApprover:"",hiddenDangerRectifyApproverFullname:"",hiddenDangerRectifyApproverDeptName:"",hiddenDangerRectifyApproverDeptCode:"",rectifyComment:"",dispatchingCompany:"",rectifyMeasures:"",rectifySituation:"",afterFileToken:"",afterFileTokenImage:[],afterFiles:1,finishDate:"",safetyCheckId:""},hiddenDangerSourceList:[]}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},siteList(){let a=this.$store.SITE_LIST.find(o=>o.slots.id==this.formData.subProjectId),r=[];return a&&(r=a.children.map(o=>({text:o.title,value:o.slots.id}))),r},canEdit0(){return this.type==="add"||this.taskKey==="UserTask_0"},canEdit2(){return this.type==="execute"&&this.taskKey==="UserTask_2"},riskSourceSearch(){return{subProjectId:this.formData.subProjectId}},minDate(){const a=new Date().getFullYear();return new Date(a-1,0,1)},maxDate(){const a=new Date().getFullYear();return new Date(a+2,0,1)}},watch:{},created(){this.getHiddenLevelList()},mounted(){this.initForm()},methods:{copyCallBack(a){let r=["hiddenDangerContent","beforeFileToken","riskSource","riskSourceName","rectifyComment","dispatchingCompany","rectifyMeasures","rectifySituation","afterFileToken","latitude","longitude"];this.formData={...a},r.forEach(o=>{this.formData[o]=""})},async initForm(){if(this.type==="add"){if(this.formData.portalId=this.portalId,this.portal.type!=1){const l=this.subProjectList.find(h=>h.portalId==this.portal.id);this.formData.subProjectId=l==null?void 0:l.id,this.formData.subProjectName=l==null?void 0:l.nodeName}const{userName:a="",userFullname:r="",orgList:o=[]}=this.user||{},p=o.find(l=>{var h;return l.portalId==((h=this.portal)==null?void 0:h.id)})||o[0],e=(p==null?void 0:p.name)||"",i=(p==null?void 0:p.orgNo)||"";this.formData.createBy=a,this.formData.hiddenDangerReportorFullname=r,this.formData.prjDepName=e,this.formData.prjDepCode=i,this.formData.hiddenDangerSupervisor=a,this.formData.hiddenDangerSupervisorFullname=r,this.formData.hiddenDangerSupervisorDeptName=e,this.formData.hiddenDangerSupervisorDeptCode=i}else this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,a=>{const{detailParamList:r=[],entityObject:o}=a;if(this.detailParamList=r,this.formData={...this.formData,...o},o.constructionPost){let p=o.constructionPost.split(",");this.formData.longitude=p[0],this.formData.latitude=p[1]}}),this.canEdit2&&setTimeout(()=>{document.getElementById("hidden-rectify").scrollIntoView()},500)})},setHiddenDangerChecker(){this.formData.hiddenDangerChecker=this.formData.hiddenDangerConfirmer,this.formData.hiddenDangerCheckerFullname=this.formData.hiddenDangerConfirmerFullname,this.formData.hiddenDangerCheckerDeptName=this.formData.hiddenDangerConfirmerDeptName,this.formData.hiddenDangerCheckerDeptCode=this.formData.hiddenDangerConfirmerDeptCode},async onDraft(){try{const a={...this.formData};this.$refs.FlowForm.onSaveDraft(this.service.submit,a)}catch(a){console.log(a)}},async onSubmit(){try{await this.$refs.form.validate(),this.formData.constructionPost=!this.formData.longitude||!this.formData.latitude?"":[this.formData.longitude,this.formData.latitude].join(",");const a={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,a)}catch(a){console.log(a)}},async updateFiles(){return new Promise(async(a,r)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),a()}catch(o){r()}})},afterSubmit(a,r){this.updateFiles(),a==="submit"&&(this.taskKey=="UserTask_1"&&Y({id:this.formData.id}),(this.taskKey=="UserTask_1"||this.taskKey=="UserTask_2")&&W({id:this.formData.id}),this.formData.id&&Q({id:this.formData.id}))},handleSubProjectChange(a={}){this.formData.workAreaId="",this.formData.workAreaName="",this.formData.riskSource="",this.formData.riskSourceName=""},onDangerConfirmChange(a){a||(this.formData.hiddenDangerConfirmer="",this.formData.hiddenDangerConfirmerFullname="",this.formData.hiddenDangerConfirmerDeptName="",this.formData.hiddenDangerConfirmerDeptCode="",this.formData.hiddenDangerChecker="",this.formData.hiddenDangerCheckerFullname="",this.formData.hiddenDangerCheckerDeptName="",this.formData.hiddenDangerCheckerDeptCode="")},onSupervisionChange(a){a||(this.formData.hiddenDangerRectifyApprover="",this.formData.hiddenDangerRectifyApproverFullname="",this.formData.hiddenDangerRectifyApproverDeptName="",this.formData.hiddenDangerRectifyApproverDeptCode="")},async getHiddenLevelList(){try{let a=await K({refDirectoryTreeName:"检查表单"});this.hiddenDangerSourceList=a[0].children||[]}catch(a){this.hiddenDangerSourceList=[]}}}};function le(a,r,o,p,e,i){const l=m("van-field"),h=m("FormItemPicker"),N=m("FormItemCascader"),v=m("FormItemCalendar"),S=m("FormItemCoord"),w=m("form-item-risk-source"),u=m("van-cell-group"),U=m("UploadFiles"),y=m("FormItemPerson"),F=m("van-radio"),x=m("van-radio-group"),P=m("van-form"),R=m("FlowForm");return s(),D(R,{ref:"FlowForm","model-key":e.modelKey,"form-key":e.formKey,"entity-name":e.entityName,"detail-param-list":e.detailParamList,"detail-entity-name-list":e.detailEntityNameList,onDraftClick:i.onDraft,onSubmitClick:i.onSubmit,onAfterSubmit:i.afterSubmit,onCopyCallBack:i.copyCallBack},{default:d(()=>[n(P,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:d(()=>[n(u,{border:!1},{default:d(()=>[e.type==="view"?(s(),c(C,{key:0},[e.formData.hiddenDangerName?(s(),D(l,{key:0,modelValue:e.formData.hiddenDangerName,"onUpdate:modelValue":r[0]||(r[0]=t=>e.formData.hiddenDangerName=t),label:"隐患名称",placeholder:"自动生成(上报人+隐患闭合流程)",readonly:""},null,8,["modelValue"])):f("",!0),e.formData.hiddenDangerCode?(s(),D(l,{key:1,modelValue:e.formData.hiddenDangerCode,"onUpdate:modelValue":r[1]||(r[1]=t=>e.formData.hiddenDangerCode=t),label:"隐患编号",placeholder:"审批完成后自动生成",readonly:""},null,8,["modelValue"])):f("",!0)],64)):f("",!0),n(h,{label:"子工程",value:e.formData.subProjectId,"onUpdate:value":r[2]||(r[2]=t=>e.formData.subProjectId=t),text:e.formData.subProjectName,"onUpdate:text":r[3]||(r[3]=t=>e.formData.subProjectName=t),columns:[...i.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:i.portal.type!=1||e.type==="view"||!i.canEdit0,onChange:i.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),e.formData.workAreaId||i.canEdit0?(s(),D(h,{key:1,label:"施工工区",value:e.formData.workAreaId,"onUpdate:value":r[4]||(r[4]=t=>e.formData.workAreaId=t),text:e.formData.workAreaName,"onUpdate:text":r[5]||(r[5]=t=>e.formData.workAreaName=t),columns:[...i.siteList],"columns-field-names":{text:"text",value:"value",children:"none"},title:"选择施工工区",readonly:e.type==="view"||!i.canEdit0},null,8,["value","text","columns","readonly"])):f("",!0),n(N,{label:"隐患来源",value:e.formData.hiddenDangerSource,"onUpdate:value":r[6]||(r[6]=t=>e.formData.hiddenDangerSource=t),columns:[...e.hiddenDangerSourceList],"columns-field-names":{text:"nodeName",value:"id",children:"children"},"trigger-change-mode":!0,title:"选择隐患来源",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","columns","readonly"]),n(h,{label:"隐患级别",value:e.formData.hiddenDangerLevel,"onUpdate:value":r[7]||(r[7]=t=>e.formData.hiddenDangerLevel=t),"dict-name":"hidden_danger_level",title:"选择隐患级别",required:"",rules:[{required:!0,message:"请选择隐患级别"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),n(h,{label:"隐患分类",value:e.formData.hiddenDangerCategory,"onUpdate:value":r[8]||(r[8]=t=>e.formData.hiddenDangerCategory=t),"dict-name":"hidden_danger_category",title:"选择隐患分类",required:"",rules:[{required:!0,message:"请选择隐患分类"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),n(v,{label:"整改期限",value:e.formData.rectifyDate,"onUpdate:value":r[9]||(r[9]=t=>e.formData.rectifyDate=t),"show-confirm":!1,title:"选择整改期限",required:"","min-date":i.minDate,"max-date":i.maxDate,rules:[{required:!0,message:"请选择整改期限"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","min-date","max-date","readonly"]),e.formData.longitude||i.canEdit0?(s(),D(S,{key:2,label:"定位",longitude:e.formData.longitude,"onUpdate:longitude":r[10]||(r[10]=t=>e.formData.longitude=t),latitude:e.formData.latitude,"onUpdate:latitude":r[11]||(r[11]=t=>e.formData.latitude=t),title:"选择定位",readonly:e.type==="view"||!i.canEdit0},null,8,["longitude","latitude","readonly"])):f("",!0),e.formData.riskSource||i.canEdit0?(s(),D(w,{key:3,"risk-source":e.formData.riskSource,"onUpdate:riskSource":r[12]||(r[12]=t=>e.formData.riskSource=t),"risk-source-name":e.formData.riskSourceName,"onUpdate:riskSourceName":r[13]||(r[13]=t=>e.formData.riskSourceName=t),label:"关联危险源",title:"选择危险源","search-data":i.riskSourceSearch,readonly:e.type==="view"||!i.canEdit0},null,8,["risk-source","risk-source-name","search-data","readonly"])):f("",!0)]),_:1}),n(u,{border:!1},{default:d(()=>[n(l,{label:"隐患内容",modelValue:e.formData.hiddenDangerContent,"onUpdate:modelValue":r[14]||(r[14]=t=>e.formData.hiddenDangerContent=t),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入隐患内容",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入隐患内容"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),e.formData.beforeFileToken&&e.formData.beforeFiles||i.canEdit0?(s(),D(l,{key:0,label:"附件照片&视频","label-align":"top","input-align":"left"},{input:d(()=>[n(U,{ref:"beforeFiles",g9s:e.formData.beforeFileToken,"onUpdate:g9s":r[15]||(r[15]=t=>e.formData.beforeFileToken=t),files:e.formData.beforeFiles,"onUpdate:files":r[16]||(r[16]=t=>e.formData.beforeFiles=t),accept:"image/*,video/*",multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:e.type==="view"||!i.canEdit0},null,8,["g9s","files","readonly"])]),_:1})):f("",!0)]),_:1}),i.canEdit0?(s(),D(u,{key:0,border:!1},{default:d(()=>[n(y,{label:"上报人",userName:e.formData.createBy,"onUpdate:userName":r[17]||(r[17]=t=>e.formData.createBy=t),userFullname:e.formData.hiddenDangerReportorFullname,"onUpdate:userFullname":r[18]||(r[18]=t=>e.formData.hiddenDangerReportorFullname=t),deptName:e.formData.prjDepName,"onUpdate:deptName":r[19]||(r[19]=t=>e.formData.prjDepName=t),deptCode:e.formData.prjDepCode,"onUpdate:deptCode":r[20]||(r[20]=t=>e.formData.prjDepCode=t),title:"选择上报人",required:"",rules:[{required:!0,message:"请选择上报人"}],readonly:!0},null,8,["userName","userFullname","deptName","deptCode"]),n(l,{modelValue:e.formData.otherReportorFullname,"onUpdate:modelValue":r[21]||(r[21]=t=>e.formData.otherReportorFullname=t),label:"检查人员",placeholder:"同行人姓名请逗号隔开",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),n(v,{label:"检查日期",value:e.formData.initiateDate,"onUpdate:value":r[22]||(r[22]=t=>e.formData.initiateDate=t),"show-confirm":!1,required:!0,"min-date":i.minDate,"max-date":i.maxDate,readonly:e.type==="view"||!i.canEdit0,rules:[{required:!0,message:"请选择检查日期"}]},null,8,["value","min-date","max-date","readonly"]),e.taskKey==="UserTask_3"||e.taskKey==="UserTask_4"||e.taskKey==="UserTask_5"||e.type==="view"?(s(),D(l,{key:0,modelValue:e.formData.overdueState,"onUpdate:modelValue":r[23]||(r[23]=t=>e.formData.overdueState=t),label:"逾期情况",placeholder:"",readonly:""},null,8,["modelValue"])):f("",!0),n(y,{label:"隐患整改人",userName:e.formData.hiddenDangerRectifier,"onUpdate:userName":r[24]||(r[24]=t=>e.formData.hiddenDangerRectifier=t),userFullname:e.formData.hiddenDangerRectifierFullname,"onUpdate:userFullname":r[25]||(r[25]=t=>e.formData.hiddenDangerRectifierFullname=t),deptName:e.formData.hiddenDangerRectifierDeptName,"onUpdate:deptName":r[26]||(r[26]=t=>e.formData.hiddenDangerRectifierDeptName=t),deptCode:e.formData.hiddenDangerRectifierDeptCode,"onUpdate:deptCode":r[27]||(r[27]=t=>e.formData.hiddenDangerRectifierDeptCode=t),title:"选择隐患整改人",required:"",rules:[{required:!0,message:"请选择隐患整改人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),n(l,{name:"radio",label:"是否隐患确认"},{input:d(()=>[n(x,{modelValue:e.formData.isDangerConfirm,"onUpdate:modelValue":r[28]||(r[28]=t=>e.formData.isDangerConfirm=t),direction:"horizontal",disabled:e.type==="view"||!i.canEdit0,onChange:i.onDangerConfirmChange},{default:d(()=>[n(F,{name:!0},{default:d(()=>r[52]||(r[52]=[k("是")])),_:1,__:[52]}),n(F,{name:!1},{default:d(()=>r[53]||(r[53]=[k("否")])),_:1,__:[53]})]),_:1},8,["modelValue","disabled","onChange"])]),_:1}),e.formData.isDangerConfirm?(s(),D(y,{key:1,label:"隐患确认人",userName:e.formData.hiddenDangerConfirmer,"onUpdate:userName":r[29]||(r[29]=t=>e.formData.hiddenDangerConfirmer=t),userFullname:e.formData.hiddenDangerConfirmerFullname,"onUpdate:userFullname":r[30]||(r[30]=t=>e.formData.hiddenDangerConfirmerFullname=t),deptName:e.formData.hiddenDangerConfirmerDeptName,"onUpdate:deptName":r[31]||(r[31]=t=>e.formData.hiddenDangerConfirmerDeptName=t),deptCode:e.formData.hiddenDangerConfirmerDeptCode,"onUpdate:deptCode":r[32]||(r[32]=t=>e.formData.hiddenDangerConfirmerDeptCode=t),title:"选择隐患确认人",required:"",rules:[{required:!0,message:"请选择隐患确认人"}],readonly:e.type==="view"||!i.canEdit0,onChange:i.setHiddenDangerChecker},null,8,["userName","userFullname","deptName","deptCode","readonly","onChange"])):f("",!0),e.formData.isDangerConfirm?(s(),D(y,{key:2,label:"整改确认人",userName:e.formData.hiddenDangerChecker,"onUpdate:userName":r[33]||(r[33]=t=>e.formData.hiddenDangerChecker=t),userFullname:e.formData.hiddenDangerCheckerFullname,"onUpdate:userFullname":r[34]||(r[34]=t=>e.formData.hiddenDangerCheckerFullname=t),deptName:e.formData.hiddenDangerCheckerDeptName,"onUpdate:deptName":r[35]||(r[35]=t=>e.formData.hiddenDangerCheckerDeptName=t),deptCode:e.formData.hiddenDangerCheckerDeptCode,"onUpdate:deptCode":r[36]||(r[36]=t=>e.formData.hiddenDangerCheckerDeptCode=t),title:"选择整改确认人",required:"",rules:[{required:!0,message:"请选择整改确认人"}],readonly:""},null,8,["userName","userFullname","deptName","deptCode"])):f("",!0),n(y,{label:"隐患审核人",userName:e.formData.hiddenDangerSupervisor,"onUpdate:userName":r[37]||(r[37]=t=>e.formData.hiddenDangerSupervisor=t),userFullname:e.formData.hiddenDangerSupervisorFullname,"onUpdate:userFullname":r[38]||(r[38]=t=>e.formData.hiddenDangerSupervisorFullname=t),deptName:e.formData.hiddenDangerSupervisorDeptName,"onUpdate:deptName":r[39]||(r[39]=t=>e.formData.hiddenDangerSupervisorDeptName=t),deptCode:e.formData.hiddenDangerSupervisorDeptCode,"onUpdate:deptCode":r[40]||(r[40]=t=>e.formData.hiddenDangerSupervisorDeptCode=t),title:"选择隐患审核人",required:"",rules:[{required:!0,message:"请选择隐患审核人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),n(l,{name:"radio",label:"是否隐患复核"},{input:d(()=>[n(x,{modelValue:e.formData.isSupervision,"onUpdate:modelValue":r[41]||(r[41]=t=>e.formData.isSupervision=t),direction:"horizontal",disabled:e.type==="view"||!i.canEdit0,onChange:i.onSupervisionChange},{default:d(()=>[n(F,{name:!0},{default:d(()=>r[54]||(r[54]=[k("是")])),_:1,__:[54]}),n(F,{name:!1},{default:d(()=>r[55]||(r[55]=[k("否")])),_:1,__:[55]})]),_:1},8,["modelValue","disabled","onChange"])]),_:1}),e.formData.isSupervision?(s(),D(y,{key:3,label:"隐患复核人",userName:e.formData.hiddenDangerRectifyApprover,"onUpdate:userName":r[42]||(r[42]=t=>e.formData.hiddenDangerRectifyApprover=t),userFullname:e.formData.hiddenDangerRectifyApproverFullname,"onUpdate:userFullname":r[43]||(r[43]=t=>e.formData.hiddenDangerRectifyApproverFullname=t),deptName:e.formData.hiddenDangerRectifyApproverDeptName,"onUpdate:deptName":r[44]||(r[44]=t=>e.formData.hiddenDangerRectifyApproverDeptName=t),deptCode:e.formData.hiddenDangerRectifyApproverDeptCode,"onUpdate:deptCode":r[45]||(r[45]=t=>e.formData.hiddenDangerRectifyApproverDeptCode=t),title:"选择隐患复核人",required:"",rules:[{required:!0,message:"请选择隐患复核人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"])):f("",!0)]),_:1})):f("",!0),(e.formData.rectifyComment||e.type==="execute"&&e.taskKey==="UserTask_1")&&e.formData.isDangerConfirm?(s(),D(u,{key:1,border:!1},{default:d(()=>[n(l,{label:"整改要求及处理意见",modelValue:e.formData.rectifyComment,"onUpdate:modelValue":r[46]||(r[46]=t=>e.formData.rectifyComment=t),rows:"4",autosize:"",type:"textarea","label-width":"140","label-align":"top",placeholder:"请输入","input-align":"left",readonly:e.type==="view"||!(e.type==="execute"&&e.taskKey==="UserTask_1")},null,8,["modelValue","readonly"])]),_:1})):f("",!0),e.type==="view"&&e.formData.rectifyMeasures||e.taskKey&&e.taskKey!=="UserTask_0"&&e.taskKey!=="UserTask_1"?(s(),c(C,{key:2},[r[56]||(r[56]=g("div",{id:"hidden-rectify",class:"h-[50px] px-[18px] flex items-center bg-[#fff] my-[8px]"}," 隐患整改 ",-1)),n(u,{border:!1},{default:d(()=>[e.formData.dispatchingCompany||i.canEdit2?(s(),D(h,{key:0,label:"劳务公司",value:e.formData.dispatchingCompany,"onUpdate:value":r[47]||(r[47]=t=>e.formData.dispatchingCompany=t),"dict-name":"dispatching_company",title:"选择劳务公司",readonly:e.type==="view"||!i.canEdit2},null,8,["value","readonly"])):f("",!0),n(l,{label:"整改措施",modelValue:e.formData.rectifyMeasures,"onUpdate:modelValue":r[48]||(r[48]=t=>e.formData.rectifyMeasures=t),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改措施",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请填写整改措施"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),n(l,{label:"整改情况",modelValue:e.formData.rectifySituation,"onUpdate:modelValue":r[49]||(r[49]=t=>e.formData.rectifySituation=t),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改情况",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请填写整改情况"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),i.canEdit2||e.formData.afterFiles&&e.formData.afterFileToken?(s(),D(l,{key:1,label:"附件照片&视频","label-align":"top","input-align":"left"},{input:d(()=>[n(U,{ref:"afterFiles",g9s:e.formData.afterFileToken,"onUpdate:g9s":r[50]||(r[50]=t=>e.formData.afterFileToken=t),files:e.formData.afterFiles,"onUpdate:files":r[51]||(r[51]=t=>e.formData.afterFiles=t),accept:"image/*,video/*",multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:e.type==="view"||!i.canEdit2},null,8,["g9s","files","readonly"])]),_:1})):f("",!0)]),_:1})],64)):f("",!0)]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit","onCopyCallBack"])}const Ie=I(oe,[["render",le]]);export{Ie as default};
