import{F as y,D as T}from"./index-a831f9da.js";import{_ as x}from"./index-4829f8e2.js";import{Q as f,R as F,X as N,V as a,k as p,U as l,B as u,Y as i}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const P={name:"JL21",components:{FormTemplate:y,DocumentPart:T},emits:[],props:{},setup(o,{attrs:e,slots:c,emit:h}){},data(){return{detailTable:[],attachmentDesc:"1、完工付款/最终结清申请单。\n2、审核计算资料。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:o,detailParamList:e}){},onBeforeSubmit({formData:o,detailParamList:e,taskStart:c},h){return new Promise((r,t)=>{try{if(h==="submit"&&c&&!o.check1&&!o.check2&&!o.check3)return this.$showNotify({type:"danger",message:"请选择类型",duration:3*1e3}),t(!1),!1;r()}catch(m){t(m)}})},changeCheck1(o){o&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.check3=!1)},changeCheck2(o){o&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check3=!1)},changeCheck3(o){o&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check2=!1)}}},L={class:"one-line"},q={class:"check-wp"},B={class:"check-wp"},A={class:"check-wp"},O={class:"form-info"},z={class:"form-info"},I={class:"form-info"},W={class:"form-info"},J={class:"form-info"},Q={class:"one-line"},R={class:"check-wp"},X={class:"check-wp"},Y={class:"check-wp"},$={class:"attachment-desc"},E={class:"comment-wp"},G={class:"textarea-wp"},H={class:"footer-input"},K={class:"form-info"},M={class:"form-info"},Z={class:"form-info"},j={class:"form-info"};function S(o,e,c,h,r,t){const m=f("van-checkbox"),V=f("van-field"),k=f("DocumentPart"),g=f("FormTemplate");return F(),N(g,{ref:"FormTemplate",nature:"付结","employer-target":!0,"on-after-init":t.onAfterInit,"on-before-submit":t.onBeforeSubmit,"detail-table":r.detailTable,"is-show-confirm1":!1,attachmentDesc:r.attachmentDesc},{default:a(({formData:s,formTable:v,baseObj:b,uploadAccept:_,taskStart:d,taskComment2:U,taskComment3:w,taskComment4:C})=>[p(k,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:b.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!d},{default:a(()=>[l("div",L,[e[3]||(e[3]=l("span",{style:{"padding-left":"2em"}},"经审核承包人的",-1)),l("div",q,[p(m,{modelValue:s.check1,"onUpdate:modelValue":n=>s.check1=n,shape:"square",disabled:!d,onChange:t.changeCheck1},{default:a(()=>e[0]||(e[0]=[u("完工付款申请")])),_:2,__:[0]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[4]||(e[4]=l("span",null,"/",-1)),l("div",B,[p(m,{modelValue:s.check2,"onUpdate:modelValue":n=>s.check2=n,shape:"square",disabled:!d,onChange:t.changeCheck2},{default:a(()=>e[1]||(e[1]=[u("最终结清申请")])),_:2,__:[1]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[5]||(e[5]=l("span",null,"/",-1)),l("div",A,[p(m,{modelValue:s.check3,"onUpdate:modelValue":n=>s.check3=n,shape:"square",disabled:!d,onChange:t.changeCheck3},{default:a(()=>e[2]||(e[2]=[u("临时付款申请")])),_:2,__:[2]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[6]||(e[6]=l("span",null,"（ ",-1)),l("span",O,i(s.constructionName),1),e[7]||(e[7]=l("span",null," [ ",-1)),l("span",z,i(s.field1),1),e[8]||(e[8]=l("span",null," ] 付结",-1)),l("span",I,i(s.field2),1),e[9]||(e[9]=l("span",null,"号），",-1)),e[10]||(e[10]=l("span",null,"应支付",-1)),e[11]||(e[11]=l("span",null,"给承包人的金额共计为",-1)),e[12]||(e[12]=l("span",null,"（大写）",-1)),l("span",W,i(s.field3),1),e[13]||(e[13]=l("span",null,"（小写",-1)),l("span",J,i(s.field4),1),e[14]||(e[14]=l("span",null,"）。",-1))]),l("div",Q,[e[18]||(e[18]=l("span",{style:{"padding-left":"2em"}},"请贵方在收到",-1)),l("div",R,[p(m,{modelValue:s.check1,"onUpdate:modelValue":n=>s.check1=n,shape:"square",disabled:!d,onChange:t.changeCheck1},{default:a(()=>e[15]||(e[15]=[u("完工付款证书")])),_:2,__:[15]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[19]||(e[19]=l("span",null,"/",-1)),l("div",X,[p(m,{modelValue:s.check2,"onUpdate:modelValue":n=>s.check2=n,shape:"square",disabled:!d,onChange:t.changeCheck2},{default:a(()=>e[16]||(e[16]=[u("最终结清证书")])),_:2,__:[16]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[20]||(e[20]=l("span",null,"/",-1)),l("div",Y,[p(m,{modelValue:s.check3,"onUpdate:modelValue":n=>s.check3=n,shape:"square",disabled:!d,onChange:t.changeCheck3},{default:a(()=>e[17]||(e[17]=[u("临时付款证书")])),_:2,__:[17]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[21]||(e[21]=l("span",null,"后",-1)),e[22]||(e[22]=l("span",null,"按合同约定完成审批， ",-1)),e[23]||(e[23]=l("span",null,"并将上述工程金额支付给承包人。",-1))]),l("div",$,[e[24]||(e[24]=l("div",null,"附件：",-1)),p(V,{modelValue:s.attachmentDesc,"onUpdate:modelValue":n=>s.attachmentDesc=n,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),p(k,{deptLabel:"发包人：",deptProp:"employerName",deptValue:s.employerName,deptOptions:b.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!d},{default:a(()=>[l("div",E,[e[25]||(e[25]=l("div",null,"发包人审定意见：",-1)),l("div",G,[p(V,{modelValue:s.comment4,"onUpdate:modelValue":n=>s.comment4=n,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:a(({formData:s,formTable:v,baseObj:b,uploadAccept:_,taskStart:d,taskComment2:U,taskComment3:w,taskComment4:C})=>[l("div",H,[e[26]||(e[26]=l("span",null,"说明：本证书一式",-1)),l("span",K,i(s.num1),1),e[27]||(e[27]=l("span",null,"份，由监理机构填写，监理机构",-1)),l("span",M,i(s.num2),1),e[28]||(e[28]=l("span",null,"份，发包人",-1)),l("span",Z,i(s.num3),1),e[29]||(e[29]=l("span",null,"份，承包人",-1)),l("span",j,i(s.num4),1),e[30]||(e[30]=l("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ce=x(P,[["render",S]]);export{ce as default};
