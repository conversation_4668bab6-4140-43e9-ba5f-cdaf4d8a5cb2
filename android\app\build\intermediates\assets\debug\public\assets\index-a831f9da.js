import{h as P,_ as U}from"./index-4829f8e2.js";import{F as A}from"./index-8d635ba7.js";import{F as L}from"./FormItemPicker-d3f69283.js";import{U as B}from"./index-fc22947f.js";import{F as x}from"./FormItemPerson-bd0e3e57.js";import{Q as h,R as f,X as v,V as m,k as u,U as i,Y as c,a1 as _,S as y,F,B as g,y as I,Z as b,W as O,A as T}from"./verder-361ae6c7.js";function E(e){return P({url:"/cybereng-technology/common/getProcessBaseData",method:"get",params:e})}function H(e){return P({url:"/fawkes-ext/bpmConfig/process/getApprover/".concat(e.processKey),method:"post",data:e})}const V={name:"JLTemplate",components:{FlowForm:A,FormItemPicker:L,UploadFiles:B,FormItemPerson:x},emits:[],props:{nature:String,isShowConfirm1:{type:Boolean,default:!1},isShowConfirm2:{type:Boolean,default:!1},isShowConfirm3:{type:Boolean,default:!1},nums:{type:Array,default:()=>[]},detailTable:{type:Array,default:()=>[]},showTarget:{type:Boolean,default:!0},customerHeader:Boolean,titleFill:Boolean,employerTarget:{type:Boolean,default:!1},noApprover:Boolean,hideBorder:Boolean,attachmentDesc:String,onAfterInit:{type:Function,default:()=>()=>{}},onBeforeSubmit:{type:Function,default:()=>Promise.reject()},onAfterSubmit:{type:Function,default:()=>Promise.resolve()}},setup(e,{attrs:t,slots:r,emit:o}){},data(){return{service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/form/commit"},detailEntityNameList:["TechnologyFilesExtend1","TechnologyFilesExtend2"],detailParamList:[],uploadAccept:".jpg,.jpeg,.png,.JPG,.JPEG,.dwg,.DWG,.rar,.RAR,.zip,.ZIP,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx",formData:{menuCode:"jl",id:void 0,portalId:void 0,fileType:this.$route.query.formKey||this.$route.name,sendingType:this.$route.query.sendingType,sendingTypeCode:this.$route.query.sendingTypeCode,sendingName:"",sendingCode:"",modelType:"监理文件",prjDepCode:"",prjDepName:"",subProjectId:"",subProjectCode:"",subProjectName:"",createBy:"",userFullname:"",userTelephoneNum:"",annualDate:this.$dayjs().format("YYYY"),titleCode:"",projectName:"",projectNameCode:"",contractCode:"",constructionName:"",constructionDeptName:"",supervisionName:"",supervisionDeptName:"",supervisionSimpleName:"",informationSimpleName:"",informationDeptName:"",epcName:"",epcDeptName:"",epcDeptNameLine:"",employerName:"",comment1:"",comment2:"",comment3:"",comment4:"",comment5:"",comment6:"",comment7:"",comment8:"",comment9:"",comment10:"",approverUsername1:"",approverFullname1:"",approverUnit1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",approverUsername3:"",approverFullname3:"",approverUnit3:"",approverUsername4:"",approverFullname4:"",approverUnit4:"",approverUsername5:"",approverFullname5:"",approverUnit5:"",approverUsername6:"",approverFullname6:"",approverUnit6:"",approverUsername7:"",approverFullname7:"",approverUnit7:"",approverUsername8:"",approverFullname8:"",approverUnit8:"",approverUsername9:"",approverFullname9:"",approverUnit9:"",approverUsername10:"",approverFullname10:"",approverUnit10:"",duplicateUsername1:"",duplicateUsername2:"",duplicateUsername3:"",duplicateUsername4:"",updateDate:this.$dayjs().format("YYYY-MM-DD HH:mm:ss"),filingPath:"",notifyMethod:"[]",processState:"0",routerPath:"",num1:this.nums[0]||"",num2:this.nums[1]||"",num3:this.nums[2]||"",num4:this.nums[3]||"",num5:this.nums[4]||"",num6:this.nums[5]||"",isconfirm1:this.isShowConfirm1,isconfirm2:this.isShowConfirm2,isconfirm3:this.isShowConfirm3,contentAttachment:"",otherAttachment:"",attachmentDesc:this.attachmentDesc||"",time1:"",time2:"",time3:"",time4:"",time5:"",time6:"",time7:"",time8:"",time9:"",time10:"",field1:void 0,field2:void 0,field3:void 0,field4:void 0,field5:void 0,field6:void 0,field7:void 0,field8:void 0,field9:void 0,field10:void 0,field11:void 0,field12:void 0,field13:void 0,field14:void 0,field15:void 0,field16:void 0,field17:void 0,field18:void 0,field19:void 0,field20:void 0,check1:void 0,check2:void 0,check3:void 0,check4:void 0,check5:void 0,check6:void 0,check7:void 0,check8:void 0,check9:void 0,check10:void 0,check11:void 0,check12:void 0,check13:void 0,check14:void 0,check15:void 0,check16:void 0,check17:void 0,check18:void 0,check19:void 0,check20:void 0},formTable:[...this.detailTable],baseData:{},approverLoading:!1,approverList:[]}},computed:{formKey(){return this.$route.name||""},modelKey(){var e,t;return(e=this.formKey)!=null&&e.includes("Branch")?(t=this.formKey.replace("JL","technology_JL"))==null?void 0:t.replace("Branch","_branch_process"):"technology_".concat(this.formKey)},entityName(){var e;return(e=this.formKey)!=null&&e.includes("Branch")?"TechnologyFilesBranch":"TechnologyFiles"},type(){var e;return((e=this.$route.query)==null?void 0:e.type)||"view"},taskKey(){var e;return((e=this.$route.query)==null?void 0:e.taskKey)||""},isBranch(){return this.formKey.includes("Branch")},taskStart(){return this.type=="view"||this.isBranch?!1:this.type=="add"?!0:this.type=="execute"&&this.taskKey=="UserTask_0"},taskComment2(){return this.type=="execute"&&(this.taskKey==="UserTask_1"||this.taskKey==="UserTask_4")},taskComment3(){return this.type=="execute"&&this.taskKey==="UserTask_2"},taskComment4(){return this.type=="execute"&&this.taskKey==="UserTask_3"},portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},baseObj(){var e;return this.formData.subProjectId?((e=this.baseData)==null?void 0:e[this.formData.subProjectId])||{}:{}},canApprover(){const{id:e,subProjectId:t}=this.formData||{};return e?!0:!!(this.modelKey&&t)}},watch:{"formData.comment2"(e){this.taskComment2&&this.setComment(e)},"formData.comment3"(e){this.taskComment3&&this.setComment(e)},"formData.comment4"(e){this.taskComment4&&this.setComment(e)},"formData.annualDate":{immediate:!0,handler(e){let t=e||this.$dayjs().format("YYYY");this.formData.filingPath="\\".concat(this.formData.modelType||"监理文件","\\文件汇总\\").concat(t,"年度\\").concat(this.formData.sendingType||"监理文件")}},"formData.subProjectId":{immediate:!0,handler(e){this.canApprover?this.getProcessApprover():this.approverList=[]}}},created(){},mounted(){this.initForm()},methods:{async getBaseData(){try{const e={subProjectId:"",sendingType:this.formData.sendingType},t=await E(e);this.baseData=t||{}}catch(e){console.log(e)}},async initForm(){if(await this.getBaseData(),this.type==="add"){if(this.formData.portalId=this.portalId,this.portal.type!=1){const p=this.subProjectList.find(l=>l.portalId==this.portal.id);this.subProjectIdChange(p)}const{userName:e="",userFullname:t="",phone:r,orgList:o=[]}=this.user||{},a=o.find(p=>{var l;return p.portalId==((l=this.portal)==null?void 0:l.id)})||o[0],s=(a==null?void 0:a.name)||"",D=(a==null?void 0:a.orgNo)||"";this.formData.createBy=e,this.formData.userFullname=t,this.formData.userTelephoneNum=r,this.formData.prjDepName=s,this.formData.prjDepCode=D}else this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,e=>{var p,l;const{entityObject:t,detailParamList:r=[]}=e;this.detailParamList=r;const o=r.find(d=>d.detailEntityName==="TechnologyFilesExtend1")||{},a=(p=o==null?void 0:o.detailEntityArray)==null?void 0:p[0],s={};for(let d=1;d<=20;d++)s["check".concat(d)]=a==null?void 0:a["check".concat(d)],s["field".concat(d)]=a==null?void 0:a["field".concat(d)];this.formData={...this.formData,...s,...t};const D=(l=r.find(d=>d.detailEntityName==="TechnologyFilesExtend2"))==null?void 0:l.detailEntityArray;this.formTable=D||[],this.taskComment2&&t.comment2&&this.setComment(t.comment2),this.taskComment3&&t.comment3&&this.setComment(t.comment3),this.taskComment4&&t.comment4&&this.setComment(t.comment4),this.onAfterInit&&this.onAfterInit({formData:this.formData,detailParamList:r})})})},chengeSubProject(e){var t,r,o,a,s,D,p,l,d,k,C,N,S;this.formData.subProjectId=e.id,this.formData.subProjectName=e.nodeName,this.formData.subProjectCode=e.nodeCode,this.formData.contractCode=(t=this.baseObj.contractCode)==null?void 0:t[0],this.formData.projectName=(r=this.baseObj.projectName)==null?void 0:r[0],this.formData.constructionName=(o=this.baseObj.constructionName)==null?void 0:o[0],this.formData.constructionDeptName=(a=this.baseObj.constructionDeptName)==null?void 0:a[0],this.formData.epcName=(s=this.baseObj.epcName)==null?void 0:s[0],this.formData.epcDeptName=(D=this.baseObj.epcDeptName)==null?void 0:D[0],this.formData.epcDeptNameLine=(p=this.baseObj.epcDeptNameLine)==null?void 0:p[0],this.formData.supervisionName=(l=this.baseObj.supervisionName)==null?void 0:l[0],this.formData.supervisionDeptName=(d=this.baseObj.supervisionDeptName)==null?void 0:d[0],this.formData.supervisionSimpleName=(k=this.baseObj.supervisionSimpleName)==null?void 0:k[0],this.formData.employerName=(C=this.baseObj.employerName)==null?void 0:C[0],this.formData.informationSimpleName=(N=this.baseObj.informationSimpleName)==null?void 0:N[0],this.formData.informationDeptName=(S=this.baseObj.informationDeptName)==null?void 0:S[0]},handleSupervisionSimpleNameChange(e){this.formData.supervisionSimpleName=e;const r=(this.baseObj.supervisionSimpleName||[]).findIndex(s=>s===e)||0,o=this.baseObj.supervisionName||[],a=this.baseObj.supervisionDeptName||[];this.formData.supervisionName=o[r],this.formData.supervisionDeptName=a[r]},handleInformationSimpleNameChange(e){this.formData.informationSimpleName=e;const r=(this.baseObj.informationSimpleName||[]).findIndex(a=>a===e)||0,o=this.baseObj.informationDeptName||[];this.formData.informationDeptName=o[r]},async getProcessApprover(){if(!(this.isBranch||this.noApprover))try{this.approverLoading=!0;const e={...this.formData,formKey:this.formKey,processKey:this.modelKey},t=await H(e);this.approverList=t||[],this.taskStart&&t.forEach(r=>{this.formData[r.fieldName]=r.fieldValue,this.formData[r.fullnameFieldName]=r.fullNameFieldValue})}catch(e){console.log(e)}finally{this.approverLoading=!1}},updateDetailParamList(){var r,o;const e=[],t=((o=(r=this.detailParamList.find(a=>a.detailEntityName==="TechnologyFilesExtend1"))==null?void 0:r.detailEntityArray)==null?void 0:o[0])||{};for(let a=1;a<=20;a++)t["check".concat(a)]=this.formData["check".concat(a)],t["field".concat(a)]=this.formData["field".concat(a)];e.push({detailEntityName:"TechnologyFilesExtend1",detailEntityArray:[{...t}]}),e.push({detailEntityName:"TechnologyFilesExtend2",detailEntityArray:[...this.formTable]}),this.detailParamList=e},emitBeforeSubmit(e,t){return new Promise(async(r,o)=>{try{this.updateDetailParamList(),await this.onBeforeSubmit({formData:e,detailParamList:this.detailParamList,taskStart:this.taskStart,taskComment:this.taskComment2,taskComment3:this.taskComment3,taskComment4:this.taskComment4},t),r()}catch(a){o(a)}})},async onDraft(){try{const e={...this.formData,rectifyState:"暂存"};await this.emitBeforeSubmit(e,"saveDraft"),this.$nextTick(()=>{this.$refs.FlowForm.onSaveDraft(this.service.submit,e)})}catch(e){console.log(e)}},async onSubmit(){try{try{await this.$refs.form.validate()}catch(t){this.$showNotify({type:"primary",message:"请完善表单内容!"});return}switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":{this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_2":{this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_3":{this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_4":{this.formData.time4=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_5":{this.formData.time5=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_6":{this.formData.time6=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_7":{this.formData.time7=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_8":{this.formData.time8=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_9":{this.formData.time9=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_10":{this.formData.time10=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}}if(!(this.isBranch||this.noApprover)){if(!this.canApprover)return this.$showToast({message:"该业务流程未选择审批人!"}),reject(!1),!1;if(!this.approverList||!this.approverList.length)return this.$showToast({message:"该业务流程未配置审批人!"}),reject(!1),!1}const e={...this.formData};await this.emitBeforeSubmit(e,"submit"),this.$nextTick(()=>{this.$refs.FlowForm.onSubmit(this.service.submit,e)})}catch(e){console.log(e)}},async onReject(){try{const e={...this.formData};await this.emitBeforeSubmit(e,"reject"),this.$nextTick(()=>{this.$refs.FlowForm.onBackSubmit(this.service.submit,e)})}catch(e){console.log(e)}},afterSubmit(e,t){this.updateFiles(),this.onAfterSubmit(e,t)},async updateFiles(){return new Promise(async(e,t)=>{try{this.$refs.otherAttachment&&await this.$refs.otherAttachment.update(),this.$refs.contentAttachment&&await this.$refs.contentAttachment.update(),e()}catch(r){t()}})},getComment(){var e,t;return(t=(e=this.$refs)==null?void 0:e.FlowForm)==null?void 0:t.getComment()},setComment(e){var t,r;(r=(t=this.$refs)==null?void 0:t.FlowForm)==null||r.setComment(e)},onCommentChange(e){this.taskComment2&&(this.formData.comment2=e),this.taskComment3&&(this.formData.comment3=e),this.taskComment4&&(this.formData.comment4=e)}}},M={class:"form-papper"},K={class:"form-title"},q={class:"code"},R={class:"name"},z={class:"archive"},W={key:0},J={key:1,class:"form-header"},G={style:{"margin-top":"5px"}},Z={key:0,class:"form-target"},Q={class:"one-line"},X={key:0,class:"form-info"},$={key:1,class:"form-info"},ee={class:"form-content"},te={class:"form-footer"},ae={class:"footer-form"},se={class:"person-picker"};function re(e,t,r,o,a,s){const D=h("FormItemPicker"),p=h("van-cell-group"),l=h("van-radio"),d=h("van-radio-group"),k=h("van-field"),C=h("UploadFiles"),N=h("FormItemPerson"),S=h("van-cell"),Y=h("van-form"),w=h("FlowForm");return f(),v(w,{ref:"FlowForm","model-key":s.modelKey,"form-key":s.formKey,"entity-name":s.entityName,"detail-param-list":a.detailParamList,"detail-entity-name-list":a.detailEntityNameList,onChangeComment:s.onCommentChange,onDraftClick:s.onDraft,onSubmitClick:s.onSubmit,onAfterSubmit:s.afterSubmit,onRejectClick:s.onReject},{default:m(()=>[u(Y,{ref:"form","label-width":"7em","input-align":"left","error-message-align":"right"},{default:m(()=>[u(p,{border:!1},{default:m(()=>[u(D,{label:"子工程","input-align":"right",value:a.formData.subProjectId,"onUpdate:value":t[0]||(t[0]=n=>a.formData.subProjectId=n),text:a.formData.subProjectName,"onUpdate:text":t[1]||(t[1]=n=>a.formData.subProjectName=n),columns:[...s.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:s.portal.type!=1||!s.taskStart,onChange:s.chengeSubProject},null,8,["value","text","columns","readonly","onChange"])]),_:1}),u(p,{border:!1},{default:m(()=>[i("div",M,[i("div",K,[i("div",q,c(a.formData.fileType),1),i("div",R,[r.titleFill?_(e.$slots,"title",{key:0,formData:a.formData,formTable:a.formTable,baseObj:s.baseObj,uploadAccept:a.uploadAccept,taskStart:s.taskStart,taskComment2:s.taskComment2,taskComment3:s.taskComment3,taskComment4:s.taskComment4},void 0,!0):(f(),y(F,{key:1},[g(c(a.formData.sendingType),1)],64))]),i("div",z,[t[6]||(t[6]=i("span",null,"（ ",-1)),i("div",null,c(a.formData.supervisionSimpleName),1),t[7]||(t[7]=i("span",null,"[ ",-1)),i("div",null,c(a.formData.annualDate),1),t[8]||(t[8]=i("span",null,"]",-1)),i("span",null,c(r.nature),1),i("div",null,c(a.formData.titleCode),1),t[9]||(t[9]=i("span",null,"号 ）",-1))])]),r.customerHeader?(f(),y("div",W,[_(e.$slots,"header",{formData:a.formData,formTable:a.formTable,baseObj:s.baseObj,uploadAccept:a.uploadAccept,taskStart:s.taskStart,taskComment2:s.taskComment2,taskComment3:s.taskComment3,taskComment4:s.taskComment4},void 0,!0)])):(f(),y("div",J,[i("div",null,[t[10]||(t[10]=i("span",null,"合同名称：",-1)),i("span",null,c(a.formData.epcName),1)]),i("div",G,[t[11]||(t[11]=i("span",null,"合同编号：",-1)),i("span",null,c(a.formData.contractCode),1)])])),i("div",{class:I(["form-body",{"hide-border":r.hideBorder}])},[r.showTarget?(f(),y("div",Z,[i("div",Q,[t[12]||(t[12]=i("span",null,"致：",-1)),r.employerTarget?b("",!0):(f(),y("span",X,c(a.formData.epcDeptNameLine),1)),r.employerTarget?(f(),y("span",$,c(a.formData.employerName),1)):b("",!0)])])):b("",!0),i("div",ee,[_(e.$slots,"default",{formData:a.formData,formTable:a.formTable,baseObj:s.baseObj,uploadAccept:a.uploadAccept,taskStart:s.taskStart,taskComment2:s.taskComment2,taskComment3:s.taskComment3,taskComment4:s.taskComment4},void 0,!0)])],2),i("div",te,[_(e.$slots,"footer",{formData:a.formData,formTable:a.formTable,baseObj:s.baseObj,uploadAccept:a.uploadAccept,taskStart:s.taskStart,taskComment2:s.taskComment2,taskComment3:s.taskComment3,taskComment4:s.taskComment4},void 0,!0)])])]),_:3}),u(p,{border:!1},{default:m(()=>[i("div",ae,[r.isShowConfirm1?(f(),v(k,{key:0,name:"radio",label:"是否监理总监审批：","label-width":"14em","input-align":"right"},{input:m(()=>[u(d,{modelValue:a.formData.isconfirm1,"onUpdate:modelValue":t[2]||(t[2]=n=>a.formData.isconfirm1=n),direction:"horizontal",disabled:!s.taskStart},{default:m(()=>[u(l,{name:!0},{default:m(()=>t[13]||(t[13]=[g("是")])),_:1,__:[13]}),u(l,{name:!1},{default:m(()=>t[14]||(t[14]=[g("否")])),_:1,__:[14]})]),_:1},8,["modelValue","disabled"])]),_:1})):b("",!0),r.isShowConfirm2?(f(),v(k,{key:1,name:"radio",label:"是否总包项目经理审批：","label-width":"14em","input-align":"right"},{input:m(()=>[u(d,{modelValue:a.formData.isconfirm2,"onUpdate:modelValue":t[3]||(t[3]=n=>a.formData.isconfirm2=n),direction:"horizontal",disabled:!s.taskStart},{default:m(()=>[u(l,{name:!0},{default:m(()=>t[15]||(t[15]=[g("是")])),_:1,__:[15]}),u(l,{name:!1},{default:m(()=>t[16]||(t[16]=[g("否")])),_:1,__:[16]})]),_:1},8,["modelValue","disabled"])]),_:1})):b("",!0),r.isShowConfirm3?(f(),v(k,{key:2,name:"radio",label:"是否标段项目经理审批：","label-width":"14em","input-align":"right"},{input:m(()=>[u(d,{modelValue:a.formData.isconfirm3,"onUpdate:modelValue":t[4]||(t[4]=n=>a.formData.isconfirm3=n),direction:"horizontal",disabled:!s.taskStart},{default:m(()=>[u(l,{name:!0},{default:m(()=>t[17]||(t[17]=[g("是")])),_:1,__:[17]}),u(l,{name:!1},{default:m(()=>t[18]||(t[18]=[g("否")])),_:1,__:[18]})]),_:1},8,["modelValue","disabled"])]),_:1})):b("",!0),u(k,{label:"附件上传：","label-align":"top","input-align":"left"},{input:m(()=>[u(C,{ref:"otherAttachment",g9s:a.formData.otherAttachment,"onUpdate:g9s":t[5]||(t[5]=n=>a.formData.otherAttachment=n),accept:a.uploadAccept,multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:!s.taskStart},null,8,["g9s","accept","readonly"])]),_:1})])]),_:1}),s.isBranch||r.noApprover?b("",!0):(f(),y(F,{key:0},[s.canApprover?(f(),v(p,{key:0,border:!1},{default:m(()=>[i("div",se,[a.approverList&&a.approverList.length?(f(!0),y(F,{key:0},O(a.approverList||[],(n,pe)=>(f(),v(N,{label:"".concat(n.label),userName:a.formData[n.fieldName],"onUpdate:userName":j=>a.formData[n.fieldName]=j,userFullname:a.formData[n.fullnameFieldName],"onUpdate:userFullname":j=>a.formData[n.fullnameFieldName]=j,title:"选择执行人",labelWidth:"15em","input-align":"right",required:!!n.required,rules:[{required:!!n.required,message:"请选择执行人"}],multiple:!!n.multipleChoice,readonly:!s.taskStart},null,8,["label","userName","onUpdate:userName","userFullname","onUpdate:userFullname","required","rules","multiple","readonly"]))),256)):(f(),v(S,{key:1,title:"审批人",value:"该业务流程未配置审批人"}))])]),_:1})):b("",!0)],64))]),_:3},512)]),_:3},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onChangeComment","onDraftClick","onSubmitClick","onAfterSubmit","onRejectClick"])}const ge=U(V,[["render",re],["__scopeId","data-v-8fb09741"]]);const ie={name:"DocumentPart",components:{},emits:["update:deptValue","update:personValue","update:dateValue"],props:{deptLabel:{type:String,default:"部门机构："},deptValue:{type:String,default:""},deptOptions:{type:Array,default:()=>[]},deptProp:{type:String,default:"deptProp"},personLabel:{type:String,default:"负责人："},personValue:{type:String,default:""},dateLabel:{type:String,default:"日期："},dateValue:{type:String,default:""},labelWidth:{type:String,default:"11em"},disabled:Boolean},setup(e,{attrs:t,slots:r,emit:o}){},data(){return{}},computed:{computedDeptValue:{get(){return this.deptValue||""},set(e){this.$emit("update:deptValue",e)}}},watch:{},created(){},mounted(){},methods:{}},oe={class:"document-part"},ne={class:"part-slot"},me={class:"part-sign"},le={class:"ps-item"},de=["innerHTML"],ue={class:"ps-item"},fe={class:"ps-item"};function ce(e,t,r,o,a,s){return f(),y("div",oe,[i("div",ne,[_(e.$slots,"default",{},void 0,!0)]),i("div",me,[i("div",le,[i("span",{class:"label",style:T({width:r.labelWidth})},c(r.deptLabel),5),i("span",{class:"value",innerHTML:s.computedDeptValue.replaceAll("\n","<br/>")},null,8,de)]),i("div",ue,[i("span",{class:"label",style:T({width:r.labelWidth})},c(r.personLabel),5),t[0]||(t[0]=i("span",{class:"value"},c(""),-1))]),i("div",fe,[i("span",{class:"label",style:T({width:r.labelWidth})},c(r.dateLabel),5),t[1]||(t[1]=i("span",{class:"value"},c(""),-1))])])])}const _e=U(ie,[["render",ce],["__scopeId","data-v-35a918cc"]]);export{_e as D,ge as F};
