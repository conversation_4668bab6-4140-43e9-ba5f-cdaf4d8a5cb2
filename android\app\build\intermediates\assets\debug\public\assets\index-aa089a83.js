import{F as B,D as F}from"./index-1be3ad72.js";import{_ as T}from"./index-4829f8e2.js";import{Q as h,R as r,X as U,V as i,k as s,U as e,S as f,W as C,F as D,Y as o}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const O={name:"CB30",components:{FormTemplate:B,DocumentPart:F},emits:[],props:{},setup(b,{attrs:t,slots:v,emit:V}){},data(){return{detailTable:[{},{},{},{}],attachmentDesc:"计量测量、计算等资料"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:b,detailParamList:t}){},onBeforeSubmit({formData:b,detailParamList:t,taskComment3:v},V){return new Promise((m,u)=>{try{m()}catch(a){u(a)}})}}},A={class:"comment-wp"},W={class:"form-table"},g={class:"center"},z={class:"comment-wp"},E={class:"form-table"},I={class:"center"},S={class:"attachment-desc"},G={class:"comment-wp"},Q={class:"textarea-wp"},R={class:"comment-wp"},X={class:"textarea-wp"},Y={class:"footer-input"},q={class:"form-info"},H={class:"form-info"},J={class:"form-info"},K={class:"form-info"};function M(b,t,v,V,m,u){const a=h("van-field"),w=h("DocumentPart"),N=h("FormTemplate");return r(),U(N,{ref:"FormTemplate",nature:"计报","on-after-init":u.onAfterInit,"on-before-submit":u.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:m.attachmentDesc},{default:i(({formData:n,formTable:_,baseObj:c,uploadAccept:P,taskStart:d,taskComment2:y,taskComment3:k,taskComment4:L,taskComment5:x})=>[s(w,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:n.constructionDeptName,deptOptions:c.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!d},{default:i(()=>[t[5]||(t[5]=e("div",{class:"one-line"},[e("span",{style:{"padding-left":"2em"}},"我方按施工合同约定，完成了下列项目的施工，其工程质量经检验合格，并依据合同进行了计量。"),e("span",null,"现提交计量结果，请贵方审核。")],-1)),e("div",A,[t[1]||(t[1]=e("div",null,"一、合同分类分项项目（含变更项目）",-1)),e("div",W,[e("table",null,[t[0]||(t[0]=e("thead",null,[e("tr",null,[e("th",{colspan:"1",rowspan:"1"},"序号"),e("th",{colspan:"1",rowspan:"1"},"项目编码"),e("th",{colspan:"1",rowspan:"1"},"项目编号"),e("th",{colspan:"1",rowspan:"1"},"项目名称"),e("th",{colspan:"1",rowspan:"1"},"单位"),e("th",{colspan:"1",rowspan:"1"},"申报工程量"),e("th",{colspan:"1",rowspan:"1"},"监理核实工程量"),e("th",{colspan:"1",rowspan:"1"},"备注")])],-1)),e("tbody",null,[(r(!0),f(D,null,C(_.slice(0,2)||[],(l,p)=>(r(),f("tr",{key:p},[e("td",g,o(p+1),1),e("td",null,o(l.field1),1),e("td",null,o(l.field2),1),e("td",null,o(l.field3),1),e("td",null,o(l.field4),1),e("td",null,o(l.field5),1),e("td",null,o(l.field6),1),e("td",null,o(l.field7),1)]))),128))])])])]),e("div",z,[t[3]||(t[3]=e("div",null,"二、合同措施项目（含变更项目）",-1)),e("div",E,[e("table",null,[t[2]||(t[2]=e("thead",null,[e("tr",null,[e("th",{colspan:"1",rowspan:"1"},"序号"),e("th",{colspan:"1",rowspan:"1"},"项目编号"),e("th",{colspan:"1",rowspan:"1"},"项目名称"),e("th",{colspan:"1",rowspan:"1"},"合价"),e("th",{colspan:"1",rowspan:"1"},"本次申报"),e("th",{colspan:"1",rowspan:"1"},"监理核实"),e("th",{colspan:"1",rowspan:"1"},"备注")])],-1)),e("tbody",null,[(r(!0),f(D,null,C(_.slice(2,4)||[],(l,p)=>(r(),f("tr",{key:p},[e("td",I,o(p+1),1),e("td",null,o(l.field2),1),e("td",null,o(l.field3),1),e("td",null,o(l.field4),1),e("td",null,o(l.field5),1),e("td",null,o(l.field6),1),e("td",null,o(l.field7),1)]))),128))])])])]),e("div",S,[t[4]||(t[4]=e("div",null,"附件：",-1)),s(a,{modelValue:n.attachmentDesc,"onUpdate:modelValue":l=>n.attachmentDesc=l,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2,__:[5]},1032,["deptValue","deptOptions","disabled"]),s(w,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:n.epcDeptName,deptOptions:c.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!d},{default:i(()=>[e("div",G,[t[6]||(t[6]=e("div",null,"EPC总承包项目部意见：",-1)),e("div",Q,[s(a,{modelValue:n.comment2,"onUpdate:modelValue":l=>n.comment2=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!y},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),s(w,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:n.supervisionDeptName,deptOptions:c.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!d},{default:i(()=>[e("div",R,[t[7]||(t[7]=e("div",null,"审核意见：",-1)),e("div",X,[s(a,{modelValue:n.comment3,"onUpdate:modelValue":l=>n.comment3=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!k},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:i(({formData:n,formTable:_,baseObj:c,uploadAccept:P,taskStart:d,taskComment2:y,taskComment3:k,taskComment4:L,taskComment5:x})=>[e("div",Y,[t[8]||(t[8]=e("span",null,"说明：本表一式",-1)),e("span",q,o(n.num1),1),t[9]||(t[9]=e("span",null,"份，由承包人填写，监理机构审核后，发包人",-1)),e("span",H,o(n.num2),1),t[10]||(t[10]=e("span",null,"份，监理机构",-1)),e("span",J,o(n.num3),1),t[11]||(t[11]=e("span",null,"份，承包人",-1)),e("span",K,o(n.num4),1),t[12]||(t[12]=e("span",null,"份。",-1))]),t[13]||(t[13]=e("div",{class:"footer-input"}," 2、本表中的项目编码是指《水利工程工程量清单计价规范》（GB50501-2007）中的项目编码，项目编号是指合同工程量清单的项目编号。 ",-1))]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ue=T(O,[["render",M]]);export{ue as default};
