import{F as P,a as q}from"./index-8d635ba7.js";import{F as V}from"./FormItemPicker-d3f69283.js";import{F as I}from"./FormItemDate-ba00d9d5.js";import{F as x}from"./FormItemCalendar-905fde75.js";import{F as E}from"./FormItemCascader-c665b251.js";import{F as j}from"./FormItemPerson-bd0e3e57.js";import{F as S}from"./FormItemCoord-9e82e1bf.js";import{_ as b,h as M}from"./index-4829f8e2.js";import{Q as s,R as f,S as C,k as n,V as i,F as k,X as y,Z as c,B as v}from"./verder-361ae6c7.js";import{U as L}from"./index-fc22947f.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./array-15ef8611.js";import"./validate-2249584f.js";import"./vant-91101745.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";const K={name:"FormItemDateAndTime",components:{},emits:["update:value","update:text"],props:{value:[String,Number],text:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:Boolean,readonly:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},columnsType:{type:Array,default:()=>["year","month","day"]},minDate:{type:Date,default:()=>{const o=new Date().getFullYear();return new Date(o-10,0,1)}},maxDate:{type:Date,default:()=>{const o=new Date().getFullYear();return new Date(o+10,0,1)}},timeColumnsType:{type:Array,default:()=>["hour","minute","second"]}},setup(o,{attrs:t,slots:a,emit:d}){},data(){return{showPicker:!1,currentDate:void 0,currentTime:void 0}},computed:{},watch:{value:{immediate:!0,deep:!0,handler(o){if(o){let t=new Date(o);t instanceof Date&&!isNaN(t.getTime())||(t=new Date);const a=this.columnsType.map(e=>e==="year"?t.getFullYear():e==="month"?t.getMonth()+1:e==="day"?t.getDate():void 0),d=this.timeColumnsType.map(e=>e==="hour"?this.$dayjs(t).format("HH"):e==="minute"?this.$dayjs(t).format("mm"):e==="second"?this.$dayjs(t).format("ss"):void 0);this.currentDate=a||[],this.currentTime=d||[]}}}},created(){},mounted(){},methods:{onShowPicker(){this.readonly||(this.showPicker=!0)},onClosePicker(){this.showPicker=!1},onSelectConfirm(){const o=this.currentDate.join("-"),t=this.currentTime.join(":");this.$emit("update:value","".concat(o," ").concat(t)),this.onClosePicker()}}};function B(o,t,a,d,e,l){const g=s("van-field"),m=s("van-date-picker"),p=s("van-time-picker"),D=s("van-picker-group"),F=s("van-popup");return f(),C(k,null,[n(g,{name:a.name,"model-value":a.value,label:a.label,required:a.required,rules:a.rules,"input-align":a.inputAlign,"error-message-align":a.errorMessageAlign,"label-width":a.labelWidth,readonly:"","is-link":!a.readonly,placeholder:"请选择",onClick:t[0]||(t[0]=u=>l.onShowPicker())},null,8,["name","model-value","label","required","rules","input-align","error-message-align","label-width","is-link"]),n(F,{show:e.showPicker,"onUpdate:show":t[3]||(t[3]=u=>e.showPicker=u),position:"bottom",teleport:"#app"},{default:i(()=>[n(D,{title:a.title,tabs:["选择日期","选择时间"],onConfirm:l.onSelectConfirm,onCancel:l.onClosePicker},{default:i(()=>[n(m,{modelValue:e.currentDate,"onUpdate:modelValue":t[1]||(t[1]=u=>e.currentDate=u),"columns-type":a.columnsType,"min-date":a.minDate,"max-date":a.maxDate},null,8,["modelValue","columns-type","min-date","max-date"]),n(p,{modelValue:e.currentTime,"onUpdate:modelValue":t[2]||(t[2]=u=>e.currentTime=u),"columns-type":a.timeColumnsType},null,8,["modelValue","columns-type"])]),_:1},8,["title","onConfirm","onCancel"])]),_:1},8,["show"])],64)}const _=b(K,[["render",B]]);function Y(o){return M({method:"get",url:"/cybereng-technology/meeting/checkUniqueness",params:o})}const R={name:"MeetFlow",components:{FlowForm:P,FormItemPicker:V,FormItemDate:I,FormItemCalendar:x,FormItemPerson:j,FormItemCoord:S,UploadFiles:L,FormItemCascader:E,FormItemDateAndTime:_,FormItemMultiplePerson:q},props:{},emits:[],data(){var o,t;return{type:((o=this.$route.query)==null?void 0:o.type)||"",taskKey:((t=this.$route.query)==null?void 0:t.taskKey)||"",entityName:"TechnologyMeeting",formKey:"MeetFlow",modelKey:"meet_company_flow",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/form/commit"},formData:{portalId:"",notifyAttachmentNum:null,subProjectName:"",subProjectId:null,moduleType:"",sendingName:"",sendingType:"",sendingCode:"",sendingTypeCode:"",meetingTime:"",meetingPlace:"",meetingPlaceName:"",notifyAttachment:"",notifyAttachmentFiles:1,annualDate:this.$dayjs().format("YYYY"),createBy:"",userFullname:"",userTelephoneNum:"",prjDepName:"",prjDepCode:"",fillingDate:this.$dayjs().format("YYYY-MM-DD"),meetingNotifyUsers:"",meetingNotifyUsersFullname:"",recorderUsername:"",recorderFullname:"",recorderUnit:"",recorderCode:"",isNotifyCollate:!1,notifyCollatorUsername:"",notifyCollatorFullname:"",isRecordCollate:!1,recordCollatorFullname:"",recordCollatorUsername:"",isConfirm:!1,confirmerUsername:"",confirmerFullname:"",notifyMethod:[],contentAttachment:null,contentAttachmentFiles:1,recordAttachment:null,recordAttachmentNum:null,recordAttachmentNumFiles:1}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},canEdit0(){return this.type==="add"||this.taskKey==="UserTask_0"},canEdit2(){return this.type==="add"||this.taskKey==="UserTask_0"||this.taskKey==="UserTask_2"},canEdit3(){return this.type==="add"||this.taskKey==="UserTask_0"||this.taskKey==="UserTask_2"||this.taskKey==="UserTask_3"},yesOrNoList(){return[{text:"是",value:!0},{text:"否",value:!1}]},meetingPlaceList(){let t=this.$store.ENUM_DICT.meeting_place.find(a=>a.code==this.formData.subProjectId);return(t==null?void 0:t.child)||[]}},mounted(){this.initForm()},methods:{copyCallBack(o){},async initForm(){var o;if(this.type==="add"){if(this.formData.sendingTypeCode=this.$route.query.sendingTypeCode,this.formData.sendingType=this.$route.query.sendingType,this.formData.moduleType=(o=this.$route.query)==null?void 0:o.moduleType,this.formData.portalId=this.portalId,this.portal.type!=1){const p=this.subProjectList.find(D=>D.portalId==this.portal.id);this.formData.subProjectId=p==null?void 0:p.id,this.formData.subProjectName=p==null?void 0:p.nodeName}const{userName:t="",userFullname:a="",orgList:d=[],phone:e=""}=this.user||{},l=d.find(p=>{var D;return p.portalId==((D=this.portal)==null?void 0:D.id)})||d[0],g=(l==null?void 0:l.name)||"",m=(l==null?void 0:l.orgNo)||"";this.formData.createBy=t,this.formData.userFullname=a,this.formData.prjDepName=g,this.formData.prjDepCode=m,this.formData.userTelephoneNum=e}else this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,t=>{const{detailParamList:a=[],entityObject:d}=t;this.detailParamList=a,this.formData={...this.formData,...d},this.formData.notifyMethod=d.notifyMethod.split(",")})})},async onDraft(){try{const o={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,o)}catch(o){console.log(o)}},async onSubmit(){try{await this.$refs.form.validate(),await this.checkMeetName();const o={...this.formData,notifyMethod:this.formData.notifyMethod.join(",")};await this.$refs.FlowForm.onSubmit(this.service.submit,o)}catch(o){console.log(o)}},checkReleaseTime(){return(this.type==="add"||this.type==="execute"&&this.taskKey==="UserTask_0")&&new Date(this.formData.meetingTime)<new Date?(this.$showToast("会议时间必须晚于当前时间"),!1):!0},checkMeetName(){if(this.canEdit0){let o={moduleType:this.formData.moduleType,sendingName:this.formData.sendingName,sendingCode:this.formData.sendingCode,sendingTypeCode:this.formData.sendingTypeCode};return this.formData.id&&(o.id=this.formData.id),new Promise((t,a)=>{Y(o).then(d=>{d!="数据正常"?(this.$showToast(d),a(!1)):t(!0)})})}},getSubProjectNode(o){this.formData.portalId=o&&o.portalId||"",this.formData.meetingPlace=""},clearUserChoose(o){this.formData["".concat(o,"Username")]="",this.formData["".concat(o,"Fullname")]=""},async updateFiles(){return new Promise(async(o,t)=>{try{this.$refs.notifyAttachmentFiles&&await this.$refs.notifyAttachmentFiles.update(),this.$refs.recordAttachmentNumFiles&&await this.$refs.recordAttachmentNumFiles.update(),this.$refs.contentAttachmentFiles&&await this.$refs.contentAttachmentFiles.update(),o()}catch(a){t()}})},afterSubmit(o,t){this.updateFiles()}}};function O(o,t,a,d,e,l){const g=s("FormItemPicker"),m=s("van-field"),p=s("FormItemDateAndTime"),D=s("UploadFiles"),F=s("FormItemDate"),u=s("van-cell-group"),h=s("FormItemPerson"),N=s("FormItemMultiplePerson"),U=s("van-checkbox"),w=s("van-checkbox-group"),T=s("van-form"),A=s("FlowForm");return f(),y(A,{ref:"FlowForm","model-key":e.modelKey,"form-key":e.formKey,"entity-name":e.entityName,"detail-param-list":e.detailParamList,"detail-entity-name-list":e.detailEntityNameList,onDraftClick:l.onDraft,onSubmitClick:l.onSubmit,onAfterSubmit:l.afterSubmit,onCopyCallBack:l.copyCallBack},{default:i(()=>[n(T,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:i(()=>[n(u,{border:!1},{default:i(()=>[n(g,{label:"子工程",value:e.formData.subProjectId,"onUpdate:value":t[0]||(t[0]=r=>e.formData.subProjectId=r),text:e.formData.subProjectName,"onUpdate:text":t[1]||(t[1]=r=>e.formData.subProjectName=r),columns:[...l.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:l.portal.type!=1||e.type==="view"||!l.canEdit0,onChange:l.getSubProjectNode},null,8,["value","text","columns","readonly","onChange"]),n(m,{modelValue:e.formData.sendingName,"onUpdate:modelValue":t[2]||(t[2]=r=>e.formData.sendingName=r),label:"会议名称",placeholder:"请输入",required:"",rules:[{required:!0,message:"请输入"}],readonly:e.type==="view"||!l.canEdit0},null,8,["modelValue","readonly"]),n(p,{label:"会议时间",value:e.formData.meetingTime,"onUpdate:value":t[3]||(t[3]=r=>e.formData.meetingTime=r),required:"",readonly:e.type==="view"||!l.canEdit0},null,8,["value","readonly"]),n(m,{modelValue:e.formData.sendingType,"onUpdate:modelValue":t[4]||(t[4]=r=>e.formData.sendingType=r),label:"会议类型",placeholder:"请选择",readonly:"",required:""},null,8,["modelValue"]),e.formData.meetingPlace||l.canEdit0?(f(),y(g,{key:0,label:"会议地点",value:e.formData.meetingPlace,"onUpdate:value":t[5]||(t[5]=r=>e.formData.meetingPlace=r),text:e.formData.meetingPlaceName,"onUpdate:text":t[6]||(t[6]=r=>e.formData.meetingPlaceName=r),columns:[...l.meetingPlaceList],"columns-field-names":{text:"zh-CN",value:"code",children:"none"},title:"选择会议地点",readonly:e.type==="view"||!l.canEdit0},null,8,["value","text","columns","readonly"])):c("",!0),e.formData.meetingDescription?(f(),y(m,{key:1,modelValue:e.formData.meetingDescription,"onUpdate:modelValue":t[7]||(t[7]=r=>e.formData.meetingDescription=r),readonly:e.type==="view"||!l.canEdit0,label:"会议描述",placeholder:"顶部对齐","label-align":"top"},null,8,["modelValue","readonly"])):c("",!0),e.formData.notifyAttachment&&e.formData.notifyAttachmentFiles||l.canEdit0?(f(),y(m,{key:2,label:"通知附件","label-align":"top","input-align":"left"},{input:i(()=>[n(D,{ref:"notifyAttachmentFiles",g9s:e.formData.notifyAttachment,"onUpdate:g9s":t[8]||(t[8]=r=>e.formData.notifyAttachment=r),files:e.formData.notifyAttachmentFiles,"onUpdate:files":t[9]||(t[9]=r=>e.formData.notifyAttachmentFiles=r),name2:e.formData.sendingName,"onUpdate:name2":t[10]||(t[10]=r=>e.formData.sendingName=r),accept:"*",multiple:!1,readonly:e.type==="view"||!l.canEdit0},null,8,["g9s","files","name2","readonly"])]),_:1})):c("",!0),n(F,{label:"年度",value:e.formData.annualDate,"onUpdate:value":t[11]||(t[11]=r=>e.formData.annualDate=r),"columns-type":["year"],required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!l.canEdit0},null,8,["value","readonly"]),e.taskKey==="UserTask_2"||e.taskKey==="UserTask_3"||e.taskKey==="UserTask_4"||e.type==="view"&&e.formData.recordAttachment&&e.formData.recordAttachmentNumFiles?(f(),y(m,{key:3,label:"会议纪要","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传"}],modelValue:e.formData.recordAttachmentNumFiles,"onUpdate:modelValue":t[14]||(t[14]=r=>e.formData.recordAttachmentNumFiles=r)},{input:i(()=>[n(D,{ref:"recordAttachmentNumFiles",g9s:e.formData.recordAttachment,"onUpdate:g9s":t[12]||(t[12]=r=>e.formData.recordAttachment=r),files:e.formData.recordAttachmentNumFiles,"onUpdate:files":t[13]||(t[13]=r=>e.formData.recordAttachmentNumFiles=r),accept:"*",multiple:!0,readonly:e.type==="view"||e.taskKey!=="UserTask_2"},null,8,["g9s","files","readonly"])]),_:1},8,["modelValue"])):c("",!0)]),_:1}),e.type!=="view"?(f(),C(k,{key:0},[n(u,{border:!1},{default:i(()=>[n(h,{label:"发起人",userName:e.formData.createBy,"onUpdate:userName":t[15]||(t[15]=r=>e.formData.createBy=r),userFullname:e.formData.userFullname,"onUpdate:userFullname":t[16]||(t[16]=r=>e.formData.userFullname=r),deptName:e.formData.prjDepName,"onUpdate:deptName":t[17]||(t[17]=r=>e.formData.prjDepName=r),deptCode:e.formData.prjDepCode,"onUpdate:deptCode":t[18]||(t[18]=r=>e.formData.prjDepCode=r),title:"选择发起人",required:"",rules:[{required:!0,message:"请选择发起人"}],readonly:!0},null,8,["userName","userFullname","deptName","deptCode"]),n(m,{modelValue:e.formData.userTelephoneNum,"onUpdate:modelValue":t[19]||(t[19]=r=>e.formData.userTelephoneNum=r),label:"联系方式",placeholder:"请输入",readonly:""},null,8,["modelValue"]),n(m,{modelValue:e.formData.prjDepName,"onUpdate:modelValue":t[20]||(t[20]=r=>e.formData.prjDepName=r),label:"所属部门",placeholder:"请输入",readonly:""},null,8,["modelValue"]),n(m,{modelValue:e.formData.fillingDate,"onUpdate:modelValue":t[21]||(t[21]=r=>e.formData.fillingDate=r),label:"填写日期",placeholder:"请输入",readonly:""},null,8,["modelValue"]),n(N,{label:"会议通知对象",title:"选择会议通知对象",required:"",rules:[{required:!0,message:"请选择会议通知对象"}],readonly:e.type==="view"||!l.canEdit0,"user-name":e.formData.meetingNotifyUsers,"onUpdate:userName":t[22]||(t[22]=r=>e.formData.meetingNotifyUsers=r),"user-fullname":e.formData.meetingNotifyUsersFullname,"onUpdate:userFullname":t[23]||(t[23]=r=>e.formData.meetingNotifyUsersFullname=r)},null,8,["readonly","user-name","user-fullname"])]),_:1}),n(u,{border:!1},{default:i(()=>[n(h,{label:"会议记录人",userName:e.formData.recorderUsername,"onUpdate:userName":t[24]||(t[24]=r=>e.formData.recorderUsername=r),userFullname:e.formData.recorderFullname,"onUpdate:userFullname":t[25]||(t[25]=r=>e.formData.recorderFullname=r),deptName:e.formData.recorderUnit,"onUpdate:deptName":t[26]||(t[26]=r=>e.formData.recorderUnit=r),deptCode:e.formData.recorderCode,"onUpdate:deptCode":t[27]||(t[27]=r=>e.formData.recorderCode=r),title:"选择会议记录人",required:"",rules:[{required:!0,message:"请选择会议记录人"}],readonly:e.type==="view"||!l.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),n(m,{modelValue:e.formData.recorderUnit,"onUpdate:modelValue":t[28]||(t[28]=r=>e.formData.recorderUnit=r),label:"所属部门",placeholder:"请输入",readonly:""},null,8,["modelValue"])]),_:1}),n(u,{border:!1},{default:i(()=>[n(g,{label:"通知是否核稿","label-width":"10em",value:e.formData.isNotifyCollate,"onUpdate:value":t[29]||(t[29]=r=>e.formData.isNotifyCollate=r),columns:[...l.yesOrNoList],required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!l.canEdit0,onChange:t[30]||(t[30]=r=>l.clearUserChoose("notifyCollator"))},null,8,["value","columns","readonly"]),e.formData.isNotifyCollate?(f(),y(h,{key:0,label:"核稿人",userName:e.formData.notifyCollatorUsername,"onUpdate:userName":t[31]||(t[31]=r=>e.formData.notifyCollatorUsername=r),userFullname:e.formData.notifyCollatorFullname,"onUpdate:userFullname":t[32]||(t[32]=r=>e.formData.notifyCollatorFullname=r),title:"请选择通知核稿人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!l.canEdit0},null,8,["userName","userFullname","readonly"])):c("",!0)]),_:1}),n(u,{border:!1},{default:i(()=>[n(g,{label:"会议纪要是否核稿","label-width":"10em",value:e.formData.isRecordCollate,"onUpdate:value":t[33]||(t[33]=r=>e.formData.isRecordCollate=r),columns:[...l.yesOrNoList],required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!l.canEdit2,onChange:t[34]||(t[34]=r=>l.clearUserChoose("recordCollator"))},null,8,["value","columns","readonly"]),e.formData.isRecordCollate?(f(),y(N,{key:0,label:"核稿人",userName:e.formData.recordCollatorUsername,"onUpdate:userName":t[35]||(t[35]=r=>e.formData.recordCollatorUsername=r),userFullname:e.formData.recordCollatorFullname,"onUpdate:userFullname":t[36]||(t[36]=r=>e.formData.recordCollatorFullname=r),title:"请选择会议纪要核稿人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!l.canEdit2},null,8,["userName","userFullname","readonly"])):c("",!0)]),_:1}),n(u,{border:!1},{default:i(()=>[n(g,{label:"会议纪要是否确认","label-width":"10em",value:e.formData.isConfirm,"onUpdate:value":t[37]||(t[37]=r=>e.formData.isConfirm=r),columns:[...l.yesOrNoList],required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!l.canEdit3,onChange:t[38]||(t[38]=r=>l.clearUserChoose("confirmer"))},null,8,["value","columns","readonly"]),e.formData.isConfirm?(f(),y(h,{key:0,label:"确认人",userName:e.formData.confirmerUsername,"onUpdate:userName":t[39]||(t[39]=r=>e.formData.confirmerUsername=r),userFullname:e.formData.confirmerFullname,"onUpdate:userFullname":t[40]||(t[40]=r=>e.formData.confirmerFullname=r),title:"选择确认人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!l.canEdit3},null,8,["userName","userFullname","readonly"])):c("",!0)]),_:1})],64)):c("",!0),n(u,{border:!1},{default:i(()=>[l.canEdit0?(f(),y(m,{key:0,name:"checkboxGroup",label:"通知方式",class:"check-box-color",required:"",rules:[{required:!0,message:"请选择"}]},{input:i(()=>[n(w,{modelValue:e.formData.notifyMethod,"onUpdate:modelValue":t[41]||(t[41]=r=>e.formData.notifyMethod=r),direction:"horizontal",shape:"square",disabled:e.type==="view"||!l.canEdit0},{default:i(()=>[n(U,{name:"系统通知"},{default:i(()=>t[45]||(t[45]=[v("系统通知")])),_:1,__:[45]}),n(U,{name:"短信通知"},{default:i(()=>t[46]||(t[46]=[v("短信通知")])),_:1,__:[46]})]),_:1},8,["modelValue","disabled"])]),_:1})):c("",!0),e.formData.contentAttachment&&e.formData.contentAttachmentFiles||l.canEdit0?(f(),y(m,{key:1,label:"上传附件","label-align":"top","input-align":"left"},{input:i(()=>[n(D,{ref:"contentAttachmentFiles",g9s:e.formData.contentAttachment,"onUpdate:g9s":t[42]||(t[42]=r=>e.formData.contentAttachment=r),files:e.formData.contentAttachmentFiles,"onUpdate:files":t[43]||(t[43]=r=>e.formData.contentAttachmentFiles=r),accept:"*",multiple:!0,readonly:e.type==="view"||!l.canEdit0},null,8,["g9s","files","readonly"])]),_:1})):c("",!0)]),_:1}),e.type==="view"?(f(),y(u,{key:1,border:!1},{default:i(()=>[n(m,{"model-value":e.formData.userFullname,label:"发起人",placeholder:"",readonly:""},null,8,["model-value"]),n(m,{"model-value":e.formData.userTelephoneNum,label:"联系方式",placeholder:"",readonly:""},null,8,["model-value"]),n(m,{label:"会议通知对象",modelValue:e.formData.meetingNotifyUsersFullname,"onUpdate:modelValue":t[44]||(t[44]=r=>e.formData.meetingNotifyUsersFullname=r),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"暂无通知对象","input-align":"left",readonly:!0},null,8,["modelValue"]),n(m,{"model-value":e.formData.recorderFullname,label:"会议记录人",placeholder:"",readonly:""},null,8,["model-value"])]),_:1})):c("",!0)]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit","onCopyCallBack"])}const de=b(R,[["render",O],["__scopeId","data-v-1690fc4f"]]);export{de as default};
