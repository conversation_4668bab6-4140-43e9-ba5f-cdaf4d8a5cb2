import{F as C}from"./index-8d635ba7.js";import{_ as P}from"./FormItemSection-e3118d02.js";import{F as S}from"./FormItemPicker-d3f69283.js";import{F as V}from"./FormItemDate-ba00d9d5.js";import{F as T}from"./FormItemCalendar-905fde75.js";import{F as q}from"./FormItemCascader-c665b251.js";import{F as E}from"./FormItemPerson-bd0e3e57.js";import{F as L}from"./FormItemCoord-9e82e1bf.js";import{U as N}from"./index-fc22947f.js";import{_ as x,A as v}from"./index-4829f8e2.js";import{g as A}from"./wbsUtil-3e809cfd.js";import{f as O}from"./formMix-e81d0a85.js";import{Q as s,R as d,X as p,V as l,k as a,B as f,Z as h}from"./verder-361ae6c7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./array-15ef8611.js";import"./validate-2249584f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";const z={name:"SafeInspectionHazard",components:{FormItemSection:P,FlowForm:C,FormItemPicker:S,FormItemDate:V,FormItemCalendar:T,FormItemPerson:E,FormItemCoord:L,UploadFiles:N,FormItemCascader:q},mixins:[O],data(){var o,r;return{allTaskKeys:[["UserTask_0","UserTask_1","UserTask_2","UserTask_3","UserTask_4"],["UserTask_0","UserTask_1"]],type:((o=this.$route.query)==null?void 0:o.type)||"",taskKey:((r=this.$route.query)==null?void 0:r.taskKey)||"",entityName:"SafeInspectionHazard",formKey:"SafeInspectionHazard",modelKey:"AQJC-YHGL002",detailParamList:[],detailEntityNameList:[],service:{submit:v.VUE_APP_TCS_API_SERVICENAME+"/form/commit",query:v.VUE_APP_TCS_API_SERVICENAME+"/form/query"},formData:{sectionId:"",name:"",hazardNumber:"",projectPosition:"",constructionArea:"",source:this.$DICT_VALUE.LSJC,level:"",deadline:"",dangerSource:"",description:"",requirement:"",fileUpload:"",type:"",typeChild:"",measure:"",measureDate:"",initiator:"",processName:"",processCode:"",measureFileUpload:"",isPass:"",needSupervision:"",positionInfo:null,lng:null,lat:null,height:null},wbsList:[],bpmPersonInfo:{},approverConfigList:[]}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},canEdit0(){return this.type==="add"||this.taskKey==="UserTask_0"},canEdit1(){return this.type==="execute"&&this.taskKey==="UserTask_1"},canEdit2(){return this.type==="execute"&&this.taskKey==="UserTask_2"},typeChildOptions(){let o=this.$store.ENUM_DICT[this.$DICT_CODE.safe_hazard_type].find(r=>r.code===this.formData.type);return(o==null?void 0:o.child)||[]},currentUser(){return this.$store.USER_INFO||{}}},watch:{},created(){},mounted(){},methods:{async beforeInit(o){let r=o.entityObject;return this.type==="execute"&&(this.taskKey==="UserTask_1"&&(r.isPass=1),this.taskKey==="UserTask_2"&&(r.needSupervision=1)),r},changeTgHander(){this.formData.isPass===1?this.bpmPersonInfo.approverConfigList=this.approverConfigList:this.bpmPersonInfo.approverConfigList=[],this.setBpmPerson(this.bpmPersonInfo)},changeJlHander(){this.formData.needSupervision===1?(this.bpmPersonInfo.approverConfigList=this.approverConfigList.filter(o=>o.prop==="supervisionPerson"),this.setBpmPerson(this.bpmPersonInfo)):(this.bpmPersonInfo.approverConfigList=this.approverConfigList.filter(o=>o.prop==="reviewPerson"),this.setBpmPerson(this.bpmPersonInfo))},async beforeSetBpmPerson(o,r,m){let{approverConfigList:g}=o;return this.type==="add"&&(o.initiatorInfo={type:0,prop:"initiator",taskKey:"UserTask_0",userName:this.currentUser.userName,userFullName:this.currentUser.userFullname,approvalTime:this.$dayjs(new Date).format("YYYY-MM-DD"),userPhone:this.currentUser.phone,userOrg:this.currentUser.orgList&&this.currentUser.orgList.length>0?[...new Set(this.currentUser.orgList.map(e=>e.name))].join(";"):"",label:"上报人",userPhoneLabel:"联系方式",approvalTimeLabel:"上报日期",userOrgLabel:"上报人所在部门"},this.formData.sectionId=this.$getPortalId()),this.getWbsList(),o.initiatorInfo.format="YYYY-MM-DD",this.bpmPersonInfo=JSON.parse(JSON.stringify(o)),this.approverConfigList=JSON.parse(JSON.stringify(g)),this.taskKey==="UserTask_2"&&(o.approverConfigList=this.approverConfigList.filter(e=>e.prop==="supervisionPerson")),o},handleSection(){this.formData.projectPosition=null,this.getWbsList()},async getWbsList(){this.wbsList=await A(this.formData.sectionId,!0,this.portal),this.$nextTick(()=>{this.$refs.formItemCascaderRef.chengeLabel()})},async initForm(){},validData(){return["sectionId","constructionArea","level","deadline","name","description","requirement","fileUpload"].some(m=>!this.formData[m])},async onDraft(){try{if(this.validData())return this.$showToast({message:"请完善表单数据"});this.doData();const o={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,o)}catch(o){console.log(o)}},async onSubmit(){try{if(this.validData())return this.$showToast({message:"请完善表单数据"});this.doData();const o={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,o)}catch(o){console.log(o)}},doData(){this.formData.reporter||(this.formData.reporter=this.currentUser.userName),this.formData.reporterName||(this.formData.reporterName=this.currentUser.userFullname),this.formData.reportDate||(this.formData.reportDate=this.$dayjs(new Date).format("YYYY-MM-DD HH:mm:ss")),this.formData.phone||(this.formData.phone=this.currentUser.phone)},async afterSubmit(o,r){this.updateFiles()},async updateFiles(){return new Promise(async(o,r)=>{try{this.$refs.fileUpload&&await this.$refs.fileUpload.update(),this.$refs.measureFileUpload&&await this.$refs.measureFileUpload.update(),o()}catch(m){r()}})}}};function K(o,r,m,g,e,i){const n=s("van-field"),U=s("FormItemSection"),w=s("FormItemCascader"),F=s("FormItemPicker"),D=s("FormItemDate"),c=s("UploadFiles"),_=s("FormItemCoord"),y=s("van-cell-group"),u=s("van-radio"),b=s("van-radio-group"),k=s("van-form"),I=s("FlowForm");return d(),p(I,{ref:"FlowForm","model-key":e.modelKey,"form-key":e.formKey,"entity-name":e.entityName,"all-task-keys":e.allTaskKeys,"bpm-person-obj":o.bpmPersonObj,"onUpdate:bpmPersonObj":r[18]||(r[18]=t=>o.bpmPersonObj=t),onDraftClick:i.onDraft,onSubmitClick:i.onSubmit,onAfterSubmit:i.afterSubmit},{default:l(()=>[a(k,{ref:"form","label-width":"9em","input-align":"right","error-message-align":"right"},{default:l(()=>[a(y,{border:!1},{default:l(()=>[a(n,{modelValue:e.formData.hazardNumber,"onUpdate:modelValue":r[0]||(r[0]=t=>e.formData.hazardNumber=t),label:"隐患编号",placeholder:"自动生成",readonly:""},null,8,["modelValue"]),a(U,{label:"所属标段",placeholder:"请选择",modelValue:e.formData.sectionId,"onUpdate:modelValue":r[1]||(r[1]=t=>e.formData.sectionId=t),readonly:e.type==="view"||!i.canEdit0,required:"",rules:[{required:!0,message:"请选择所属标段"}],onSelect:i.handleSection},null,8,["modelValue","readonly","onSelect"]),a(w,{ref:"formItemCascaderRef",label:"工程部位",value:e.formData.projectPosition,"onUpdate:value":r[2]||(r[2]=t=>e.formData.projectPosition=t),columns:[...e.wbsList],"columns-field-names":{text:"nodeName",value:"id",children:"children"},"trigger-change-mode":!0,placeholder:"请选择",readonly:e.type==="view"||!i.canEdit0},null,8,["value","columns","readonly"]),a(F,{label:"隐患级别",value:e.formData.level,"onUpdate:value":r[3]||(r[3]=t=>e.formData.level=t),"dict-name":o.$DICT_CODE.safe_hazard_level,placeholder:"请选择",required:"",rules:[{required:!0,message:"请选择隐患级别"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","dict-name","readonly"]),a(D,{label:"整改期限",value:e.formData.deadline,"onUpdate:value":r[4]||(r[4]=t=>e.formData.deadline=t),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择整改期限"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),a(n,{label:"详细区域",modelValue:e.formData.constructionArea,"onUpdate:modelValue":r[5]||(r[5]=t=>e.formData.constructionArea=t),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),a(n,{label:"隐患名称",modelValue:e.formData.name,"onUpdate:modelValue":r[6]||(r[6]=t=>e.formData.name=t),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入隐患名称"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),a(n,{label:"整改内容",modelValue:e.formData.description,"onUpdate:modelValue":r[7]||(r[7]=t=>e.formData.description=t),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改内容"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),a(n,{label:"整改要求及处理意见",modelValue:e.formData.requirement,"onUpdate:modelValue":r[8]||(r[8]=t=>e.formData.requirement=t),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改要求及处理意见"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),a(n,{label:"附件上传","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传资料附件"}]},{input:l(()=>[a(c,{ref:"fileUpload",g9s:e.formData.fileUpload,"onUpdate:g9s":r[9]||(r[9]=t=>e.formData.fileUpload=t),accept:"*",multiple:!0,readonly:e.type==="view"||!i.canEdit0},null,8,["g9s","readonly"])]),_:1}),a(_,{label:"经纬度",longitude:e.formData.lng,"onUpdate:longitude":r[10]||(r[10]=t=>e.formData.lng=t),latitude:e.formData.lat,"onUpdate:latitude":r[11]||(r[11]=t=>e.formData.lat=t),title:"选择定位",readonly:e.type==="view"||!i.canEdit0},null,8,["longitude","latitude","readonly"])]),_:1}),i.canEdit1?(d(),p(y,{key:0,border:!1},{default:l(()=>[a(n,{name:"isPass",label:"是否通过",required:"",rules:[{required:!0,message:"请选择是否通过"}]},{input:l(()=>[a(b,{modelValue:e.formData.isPass,"onUpdate:modelValue":r[12]||(r[12]=t=>e.formData.isPass=t),direction:"horizontal",onChange:i.changeTgHander},{default:l(()=>[a(u,{name:1},{default:l(()=>r[19]||(r[19]=[f("通过")])),_:1,__:[19]}),a(u,{name:0},{default:l(()=>r[20]||(r[20]=[f("不通过")])),_:1,__:[20]})]),_:1},8,["modelValue","onChange"])]),_:1})]),_:1})):h("",!0),e.formData.measureDate||e.formData.taskKey==="UserTask_2"?(d(),p(y,{key:1,border:!1},{default:l(()=>[i.canEdit2?(d(),p(n,{key:0,name:"needSupervision",label:"是否需要监理审核",required:"",rules:[{required:!0,message:"请选择是否需要监理审核"}]},{input:l(()=>[a(b,{modelValue:e.formData.needSupervision,"onUpdate:modelValue":r[13]||(r[13]=t=>e.formData.needSupervision=t),direction:"horizontal",onChange:i.changeJlHander},{default:l(()=>[a(u,{name:1},{default:l(()=>r[21]||(r[21]=[f("是")])),_:1,__:[21]}),a(u,{name:0},{default:l(()=>r[22]||(r[22]=[f("否")])),_:1,__:[22]})]),_:1},8,["modelValue","onChange"])]),_:1})):h("",!0),a(n,{label:"整改措施",modelValue:e.formData.measure,"onUpdate:modelValue":r[14]||(r[14]=t=>e.formData.measure=t),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改措施"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),a(n,{label:"整改情况",modelValue:e.formData.situation,"onUpdate:modelValue":r[15]||(r[15]=t=>e.formData.situation=t),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改情况"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),a(D,{label:"整改完成日期",value:e.formData.measureDate,"onUpdate:value":r[16]||(r[16]=t=>e.formData.measureDate=t),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择整改完成日期"}],readonly:e.type==="view"||!i.canEdit2},null,8,["value","readonly"]),a(n,{label:"附件上传","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传附件"}]},{input:l(()=>[a(c,{ref:"measureFileUpload",g9s:e.formData.measureFileUpload,"onUpdate:g9s":r[17]||(r[17]=t=>e.formData.measureFileUpload=t),accept:"*",multiple:!0,readonly:e.type==="view"||!i.canEdit2},null,8,["g9s","readonly"])]),_:1})]),_:1})):h("",!0)]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","all-task-keys","bpm-person-obj","onDraftClick","onSubmitClick","onAfterSubmit"])}const ue=x(z,[["render",K]]);export{ue as default};
