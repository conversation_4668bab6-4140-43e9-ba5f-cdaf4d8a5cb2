import{F as v,D as w}from"./index-a831f9da.js";import{_ as F}from"./index-4829f8e2.js";import{Q as y,R as i,X as B,V as u,U as t,Y as e,S as f,W as S,F as j}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const x={name:"JL30",components:{FormTemplate:v,DocumentPart:w},emits:[],props:{},setup(o,{attrs:a,slots:m,emit:r}){},data(){return{detailTable:[{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:o,detailParamList:a}){},onBeforeSubmit({formData:o,detailParamList:a},m){return new Promise((r,n)=>{try{r()}catch(s){n(s)}})}}},A={class:"jl-table"},L={class:"cell"},P={class:"cell"},g={class:"form-table"},I={class:"center"};function N(o,a,m,r,n,s){const h=y("FormTemplate");return i(),B(h,{ref:"FormTemplate",nature:"见证","on-after-init":s.onAfterInit,"on-before-submit":s.onBeforeSubmit,"detail-table":n.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:n.attachmentDesc},{default:u(({formData:p,formTable:d,baseObj:b,uploadAccept:_,taskStart:k,taskComment2:D,taskComment3:C,taskComment4:T})=>[t("table",A,[t("tbody",null,[t("tr",null,[a[0]||(a[0]=t("th",null,[t("div",{class:"cell"},"单位工程名称及编号")],-1)),t("td",null,[t("div",L,e(p.projectName),1)])]),t("tr",null,[a[1]||(a[1]=t("th",null,[t("div",{class:"cell"},"承包人")],-1)),t("td",null,[t("div",P,e(p.field1),1)])])])]),t("div",g,[t("table",null,[a[2]||(a[2]=t("thead",null,[t("tr",null,[t("th",{colspan:"1",rowspan:"1"},"序号"),t("th",{colspan:"1",rowspan:"1"},"检测项目"),t("th",{colspan:"1",rowspan:"1"},"对应单元工程编号"),t("th",{colspan:"1",rowspan:"1"},"桩号"),t("th",{colspan:"1",rowspan:"1"},"高程"),t("th",{colspan:"1",rowspan:"1"},"代表数量"),t("th",{colspan:"1",rowspan:"1"},"组数"),t("th",{colspan:"1",rowspan:"1"},"取样人"),t("th",{colspan:"1",rowspan:"1"},"送样人"),t("th",{colspan:"1",rowspan:"1"},"送样时间"),t("th",{colspan:"1",rowspan:"1"},"检测机构"),t("th",{colspan:"1",rowspan:"1"},"检测结果"),t("th",{colspan:"1",rowspan:"1"},"检测报告编号"),t("th",{colspan:"1",rowspan:"1"},"跟踪（见证）监理人员")])],-1)),t("tbody",null,[(i(!0),f(j,null,S(d||[],(l,c)=>(i(),f("tr",{key:c},[t("td",I,e(c+1),1),t("td",null,e(l.field1),1),t("td",null,e(l.field2),1),t("td",null,e(l.field3),1),t("td",null,e(l.field4),1),t("td",null,e(l.field5),1),t("td",null,e(l.field6),1),t("td",null,e(l.field7),1),t("td",null,e(l.field8),1),t("td",null,e(l.field9),1),t("td",null,e(l.field10),1),t("td",null,e(l.field11),1),t("td",null,e(l.field12),1),t("td",null,e(l.field13),1)]))),128))])])])]),footer:u(({formData:p,formTable:d,baseObj:b,uploadAccept:_,taskStart:k,taskComment2:D,taskComment3:C,taskComment4:T})=>a[3]||(a[3]=[t("div",{class:"footer-input"},[t("span",null," 说明：本表按月装订成册。 ")],-1)])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const M=F(x,[["render",N]]);export{M as default};
