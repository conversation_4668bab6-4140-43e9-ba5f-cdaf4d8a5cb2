import{h as T,_ as I,A as w}from"./index-4829f8e2.js";import{a as S,g as N}from"./wbsUtil-3e809cfd.js";import{R as P}from"./constants-4e307505.js";import{d as A}from"./form-a8596e72.js";import{Q as r,R as u,S as y,k as a,V as l,U as n,Y as _,B as g,a2 as L,X as C,F as b,W as E,Z as R,y as x}from"./verder-361ae6c7.js";import{F as M}from"./FormItemPicker-d3f69283.js";import{S as F}from"./sift-bc945174.js";import{R as D}from"./constants-94a272fa.js";import"./vant-91101745.js";import"./array-15ef8611.js";const O="project-yjp-tcs";function U(s){return T({url:"".concat(O,"/quality/qualityInspection/correction/page"),method:"get",params:s})}const V={name:"ListItem",components:{},props:{item:{type:Object,default:()=>({})},wbsList:{type:Object,default:()=>({})}},emits:[],setup(s,{attrs:e,slots:i,emit:m}){},data(){return{statusMap:P}},watch:{},created(){},computed:{statusTypeMap(){var s;return((s=this.statusMap)==null?void 0:s.TYPE_MAP)||{}},statusLabelMap(){var s;return((s=this.statusMap)==null?void 0:s.LABEL_MAP)||{}},user(){return this.$store.USER_INFO}},mounted(){},methods:{getWbsLabel:S,handelDel(){let s={formKey:"QualityInspectionCorrection",entityName:"QualityInspectionCorrection",detailEntityNameList:[]};this.$confirm({title:"提示",message:"确认删除".concat(this.item.correctionNumber,"?")}).then(()=>{A(w.VUE_APP_TCS_API_SERVICENAME,this.item.id,s).then(e=>{this.$emit("delSuccess")})}).catch(()=>{})},toFormCenter(){const{id:s,formKey:e,taskId:i,taskKey:m,processInstanceId:t,formName:o}=this.item;this.$router.push({path:"/FormCenter/QualityInspectionCorrection",query:{type:this.user.userName===this.item.createBy&&this.item.processState===3?"execute":"view",taskKey:m,formKey:"QualityInspectionCorrection",bizId:s,taskId:i,processInstanceId:t,title:"质量检查问题整改流程"}})}}},j={class:"body"},Q={class:"item-info header"},B={class:"value"},W={class:"item-info"},z={class:"value"},K={class:"item-info"},Y={class:"value"},q={class:"item-info"},G={class:"value"},X={class:"item-info"},Z={class:"value"},H={class:"right"};function J(s,e,i,m,t,o){const f=r("van-tag"),v=r("van-button"),p=r("van-swipe-cell");return u(),y("div",{class:"task-item",onClick:e[0]||(e[0]=L(h=>o.toFormCenter(),["stop","prevent"]))},[a(p,null,{right:l(()=>[a(v,{square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:o.user.userName!==i.item.createBy||![t.statusMap.PENDING_REVIEW,t.statusMap.STAGE].includes(i.item.rectificationStatus),onClick:o.handelDel},null,8,["disabled","onClick"])]),default:l(()=>[n("div",j,[n("div",Q,[e[1]||(e[1]=n("span",{class:"key"},"整改单号",-1)),n("span",B,_(i.item.correctionNumber),1)]),n("div",W,[e[2]||(e[2]=n("span",{class:"key"},"所属标段",-1)),n("span",z,_(s.$formatLabel(i.item.sectionId,s.$DICT_CODE.project_section)),1)]),n("div",K,[e[3]||(e[3]=n("span",{class:"key"},"整改部位",-1)),n("span",Y,_(o.getWbsLabel(i.wbsList,i.item.projectPosition)),1)]),n("div",q,[e[4]||(e[4]=n("span",{class:"key"},"整改期限",-1)),n("span",G,_(s.$dayjs(i.item.inspectionDate).format("YYYY-MM-DD")),1)]),n("div",X,[e[5]||(e[5]=n("span",{class:"key"},"整改内容",-1)),n("span",Z,_(i.item.description),1)]),n("div",H,[a(f,{class:"tag",color:o.statusTypeMap[i.item.rectificationStatus],plain:"",size:"medium"},{default:l(()=>[g(_(o.statusLabelMap[i.item.rectificationStatus]),1)]),_:1},8,["color"])])])]),_:1})])}const $=I(V,[["render",J],["__scopeId","data-v-8780bee6"]]);const ee={name:"List",components:{ListItem:$},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(s,{attrs:e,slots:i,emit:m}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20},wbsList:[]}},computed:{},watch:{},created(){},mounted(){this.onLoadList(),this.getWbsList()},methods:{async getWbsList(){this.wbsList=await N(null,!0,this.$store.PORTAL)},onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const s={...this.searchParams,...this.search};s.page===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e=await U(s),i=this.searchParams.page<=1?[]:this.list||[];this.list.length>=e.total&&(this.finished=!0),this.list=[...i,...e.records],this.searchParams.page++}catch(s){this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}}}},te={key:0,class:"p-[10px]"};function se(s,e,i,m,t,o){const f=r("ListItem"),v=r("van-empty"),p=r("van-list"),h=r("van-pull-refresh");return u(),C(h,{modelValue:t.refreshing,"onUpdate:modelValue":e[1]||(e[1]=c=>t.refreshing=c),onRefresh:o.onRefresh},{default:l(()=>[a(p,{loading:t.loading,"onUpdate:loading":e[0]||(e[0]=c=>t.loading=c),finished:t.finished,"finished-text":t.list&&t.list.length?"没有更多了":"",onLoad:o.onLoadList,"immediate-check":!1},{default:l(()=>[t.list&&t.list.length?(u(!0),y(b,{key:0},E(t.list||[],(c,k)=>(u(),C(f,{key:c.id,item:c,wbsList:t.wbsList,onDelSuccess:o.onRefresh},null,8,["item","wbsList","onDelSuccess"]))),128)):(u(),y(b,{key:1},[t.loading?R("",!0):(u(),y("div",te,[a(v,{description:"暂无数据"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])}const ie=I(ee,[["render",se],["__scopeId","data-v-0a4e37ca"]]);const ne={name:"QualityInspectionCorrection",components:{FormItemPicker:M,List:ie},props:{},emits:[],setup(s,{attrs:e,slots:i,emit:m}){},data(){return{search:{correctionNumber:"",sectionId:"",rectificationStatus:""},Sift:F,showTop:!1,statusOptions:Object.entries(D.LABEL_MAP).map(([s,e])=>({value:s,label:e}))}},watch:{},mounted(){},methods:{handleAdd(){this.$router.push({path:"/FormCenter/QualityInspectionCorrection",query:{type:"add",title:"新增质量整改",taskKey:"UserTask_0"}})},handleQuery(){this.showTop=!1,this.$nextTick(()=>{this.$refs.List&&this.$refs.List.onRefresh(this.search)})},handleResetting(){this.search={correctionNumber:"",sectionId:"",rectificationStatus:""},this.handleQuery()}}},oe={class:"btn-group"};function ae(s,e,i,m,t,o){const f=r("van-icon"),v=r("Navbar"),p=r("FormItemPicker"),h=r("van-button"),c=r("van-popup"),k=r("List");return u(),y(b,null,[a(v,{back:""},{right:l(()=>[a(f,{name:t.Sift,size:"2em",onClick:e[0]||(e[0]=L(d=>t.showTop=!t.showTop,["stop","prevent"]))},null,8,["name"])]),_:1}),a(c,{show:t.showTop,"onUpdate:show":e[3]||(e[3]=d=>t.showTop=d),position:"top"},{default:l(()=>[a(p,{value:t.search.sectionId,"onUpdate:value":e[1]||(e[1]=d=>t.search.sectionId=d),"dict-name":s.$DICT_CODE.project_section,placeholder:"选择所属标段"},null,8,["value","dict-name"]),a(p,{value:t.search.rectificationStatus,"onUpdate:value":e[2]||(e[2]=d=>t.search.rectificationStatus=d),placeholder:"选择流转状态",columns:[...t.statusOptions],"columns-field-names":{text:"label",value:"value",children:"none"}},null,8,["value","columns"]),n("div",oe,[a(h,{round:"",type:"primary",plain:"",onClick:L(o.handleQuery,["stop","prevent"])},{default:l(()=>e[5]||(e[5]=[g("查询")])),_:1,__:[5]},8,["onClick"]),a(h,{round:"",plain:"",onClick:L(o.handleResetting,["stop","prevent"])},{default:l(()=>e[6]||(e[6]=[g("重置")])),_:1,__:[6]},8,["onClick"])])]),_:1},8,["show"]),n("div",{class:x(["view-height",{"no-tabbar":s.envFeishu}])},[a(k,{ref:"List",search:t.search},null,8,["search"])],2),a(h,{type:"primary",size:"normal",style:{width:"100%"},onClick:e[4]||(e[4]=d=>o.handleAdd())},{default:l(()=>e[7]||(e[7]=[g("新增整改")])),_:1,__:[7]})],64)}const ve=I(ne,[["render",ae],["__scopeId","data-v-633316ff"]]);export{ve as default};
