import{s as z,_ as D}from"./stationButtonGroup-08a296f6.js";import{l as H}from"./lodash-d061b41e.js";import{g as P}from"./api-d1b3ba40.js";import{_ as R}from"./index-4829f8e2.js";import{r as d,Q as c,R as s,S as r,k as e,u as $,V as o,F as v,W as x,U as u,X as w,y as S,B as _,Y as m,Z as G}from"./verder-361ae6c7.js";import"./vant-91101745.js";const Q={class:"svg-icon env-vector-item-icon mr-1","aria-hidden":"true"},W=["xlink:href"],X={class:"env-vector-item-name mb-0"},Y={class:"env-vector-item-value mb-0 text-truncate"},Z={key:0,class:"border-top pb-5"},A={key:1,class:"text-center",style:{margin:"24% 0"}},J={__name:"index",setup(K){const f=d(""),g=d(null),k=d(),b=d([]),y=d(!1),B=d([{name:"温度",iconName:"icon-wenduji",unit:"℃"},{name:"湿度",iconName:"icon-shidu",unit:"%"},{name:"风速",iconName:"icon-fengsu",unit:"m/秒"},{name:"噪音",iconName:"icon-zaosheng",unit:"分贝"},{name:"PM2.5",iconName:"icon-pm25ganyingqi",unit:""},{name:"PM10",iconName:"icon-pm10ganyingqi",unit:""}]);function h(){g.value&&window.clearInterval(g.value),g.value=window.setInterval(()=>{N()},2e4),N()}function N(){P({key:f.value,damId:k.value}).then(l=>{l.forEach(t=>{t.watchTime=t.watchTime.split(" ")[0]}),b.value=l}).finally(l=>{y.value=!1})}function E(l){k.value=l,h()}const V=H.debounce(function(l){h()},500);return(l,t)=>{const I=c("Navbar"),L=c("van-search"),i=c("van-col"),M=c("van-icon"),p=c("van-row"),C=c("van-badge"),T=c("a-icon"),j=c("van-cell"),U=c("van-list"),F=c("van-pull-refresh");return s(),r(v,null,[e(I,{back:!l.envFeishu,backEvent:()=>l.$router.replace({name:"Home"})},null,8,["back","backEvent"]),e(L,{modelValue:f.value,"onUpdate:modelValue":[t[0]||(t[0]=n=>f.value=n),$(V)],placeholder:"请输入搜索内容",autocomplete:!1,class:"border-bottom",onClear:$(V)},null,8,["modelValue","onUpdate:modelValue","onClear"]),e(z,{onChange:E,class:"border-bottom"}),e(F,{modelValue:y.value,"onUpdate:modelValue":t[1]||(t[1]=n=>y.value=n),onRefresh:h,class:"flex-grow-1 ofy-auto no-scrollbar"},{default:o(()=>[b.value.length>0?(s(),r(v,{key:0},[e(U,null,{default:o(()=>[(s(!0),r(v,null,x(b.value,(n,q)=>(s(),w(j,{key:n.id,class:S(["py-0",{"mt-3":q>0}])},{title:o(()=>[e(p,{gutter:8,type:"flex",justify:"space-between",align:"center",class:"titleBox"},{default:o(()=>[e(i,{class:"title"},{default:o(()=>[_(m(n.codeName),1)]),_:2},1024),e(i,{class:"time"},{default:o(()=>[e(M,{name:"clock-o",color:"d0d0d0",class:"mr-1"}),_(" 测值时间："+m(n.watchTime),1)]),_:2},1024)]),_:2},1024),e(p,{gutter:2,class:"mb-1"},{default:o(()=>[(s(!0),r(v,null,x(B.value,a=>(s(),w(i,{key:a.name,span:8,class:"env-vector-item"},{default:o(()=>[e(C,{dot:n["".concat(a.name,"测值状态")]===2||n["".concat(a.name,"测值状态")]===3,color:n["".concat(a.name,"测值状态")]===2?"#ff9d29":"#f4222c",class:"error-dot"},{default:o(()=>[(s(),r("svg",Q,[u("use",{"xlink:href":"#".concat(a.iconName)},null,8,W)]))]),_:2},1032,["dot","color"]),u("div",null,[u("div",X,m(a.name),1),u("div",Y,m(n[a.name]===void 0||n[a.name]===null?"-":n[a.name])+" "+m(a.unit),1)])]),_:2},1024))),128))]),_:2},1024),n.warningMessage&&n.warningMessage.length>0?(s(),r("div",Z,[e(p,{type:"flex",justify:"space-between",align:"center",class:"pt-7 pb-4"},{default:o(()=>[e(i,{class:"text-black"},{default:o(()=>t[2]||(t[2]=[_("动态预警")])),_:1,__:[2]}),e(i,{class:"text-blue cursor-pointer",onClick:a=>l.$router.push("/CircumstancesDetection/".concat(n.codeId,"/warningHistory"))},{default:o(()=>[t[3]||(t[3]=_(" 更多 ")),e(T,{type:"double-right"})]),_:2,__:[3]},1032,["onClick"])]),_:2},1024),(s(!0),r(v,null,x(n.warningMessage,a=>(s(),w(p,{key:a.tableId,gutter:8,type:"flex",justify:"space-between",align:"center",class:"flex-nowrap",style:{"line-height":"28px"}},{default:o(()=>[e(i,{class:"van-ellipsis"},{default:o(()=>[e(C,{dot:"",class:"mr-1"}),_(" "+m(a.title),1)]),_:2},1024),e(i,{class:"van-ellipsis"},{default:o(()=>[_(m(a.watchTime),1)]),_:2},1024)]),_:2},1024))),128))])):G("",!0)]),_:2},1032,["class"]))),128))]),_:1}),t[4]||(t[4]=u("div",{class:"text-gray-6-1 text-center py-3 mb-0"},"没有更多了",-1))],64)):(s(),r("div",A,t[5]||(t[5]=[u("img",{src:D,width:"87",height:"113",alt:"暂无数据",class:"mb-6"},null,-1),u("p",{class:"text-gray-6-1",style:{"margin-top":"0"}},"暂无环境测点",-1)])))]),_:1},8,["modelValue"])],64)}}},se=R(J,[["__scopeId","data-v-56b65436"]]);export{se as default};
