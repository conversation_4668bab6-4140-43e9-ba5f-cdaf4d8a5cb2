import{F as _,D as w}from"./index-a831f9da.js";import{_ as x}from"./index-4829f8e2.js";import{Q as d,R as N,X as F,V as r,k as i,U as t,Y as l}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const P={name:"JL04",components:{FormTemplate:_,DocumentPart:w},emits:[],props:{},setup(n,{attrs:e,slots:u,emit:f}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:n,detailParamList:e}){},onBeforeSubmit({formData:n,detailParamList:e,taskStart:u},f){return new Promise((p,o)=>{try{if(f==="submit"&&u){if(!n.check1&&!n.check2)return this.$showNotify({type:"danger",message:"请选择类型!",duration:3*1e3}),o(!1),!1;if(n.check2&&!n.field1)return this.$showNotify({type:"danger",message:"请完善信息!",duration:3*1e3}),o(!1),!1}p()}catch(m){o(m)}})},changeCheck1(n){n&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.field1="",this.$nextTick(()=>{var e;(e=this.$refs.FormTemplate.$refs.form)==null||e.resetValidation()}))},changeCheck2(n){n&&(this.$refs.FormTemplate.formData.check1=!1)}}},L={class:"one-line"},U={class:"check-wp"},$={class:"check-wp"},B={class:"form-info"},A={class:"form-info"},O={class:"form-info"},S={class:"form-info"},q={class:"comment-wp"},I={class:"textarea-wp"},W={class:"footer-input"},z={class:"form-info"},J={class:"form-info"},Q={class:"form-info"},R={class:"form-info"};function X(n,e,u,f,p,o){const m=d("van-checkbox"),k=d("DocumentPart"),V=d("van-field"),v=d("FormTemplate");return N(),F(v,{ref:"FormTemplate",nature:"工预付","employer-target":!0,"on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":p.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:p.attachmentDesc},{default:r(({formData:s,formTable:g,baseObj:c,uploadAccept:C,taskStart:b,taskComment2:y,taskComment3:T,taskComment4:h})=>[i(k,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:c.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!b},{default:r(()=>[t("div",L,[e[0]||(e[0]=t("span",{style:{"padding-left":"2em"}},"鉴于",-1)),t("div",U,[i(m,{modelValue:s.check1,"onUpdate:modelValue":a=>s.check1=a,shape:"square",disabled:!0,onChange:o.changeCheck1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e[1]||(e[1]=t("span",null,"工程预付款担保已获得贵方确认",-1)),e[2]||(e[2]=t("span",null," / ",-1)),t("div",$,[i(m,{modelValue:s.check2,"onUpdate:modelValue":a=>s.check2=a,shape:"square",disabled:!0,onChange:o.changeCheck2},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e[3]||(e[3]=t("span",null,"合同约定的第",-1)),t("span",B,l(s.field1),1),e[4]||(e[4]=t("span",null,"次工程预付款条件已具备。",-1)),e[5]||(e[5]=t("span",null,"根据施工合同约定，",-1)),e[6]||(e[6]=t("span",null,"贵方应向承包人支付第",-1)),t("span",A,l(s.field2),1),e[7]||(e[7]=t("span",null,"次工程预付款，",-1)),e[8]||(e[8]=t("span",null,"金额为：",-1))]),t("div",null,[e[9]||(e[9]=t("span",null,"大写：",-1)),t("span",O,l(s.field3),1),e[10]||(e[10]=t("span",null,"元",-1))]),t("div",null,[e[11]||(e[11]=t("span",null,"小写：",-1)),t("span",S,l(s.field4),1),e[12]||(e[12]=t("span",null,"元",-1))])]),_:2},1032,["deptValue","deptOptions","disabled"]),i(k,{deptLabel:"发包人：",deptProp:"employerName",deptValue:s.employerName,deptOptions:c.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!b},{default:r(()=>[t("div",q,[e[13]||(e[13]=t("div",null,"发包人审批意见：",-1)),t("div",I,[i(V,{modelValue:s.comment4,"onUpdate:modelValue":a=>s.comment4=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!h},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:r(({formData:s,formTable:g,baseObj:c,uploadAccept:C,taskStart:b,taskComment2:y,taskComment3:T,taskComment4:h})=>[t("div",W,[e[14]||(e[14]=t("span",null,"说明：本证书一式",-1)),t("span",z,l(s.num1),1),e[15]||(e[15]=t("span",null,"份，由监理机构填写，承包人",-1)),t("span",J,l(s.num2),1),e[16]||(e[16]=t("span",null,"份，监理机构",-1)),t("span",Q,l(s.num3),1),e[17]||(e[17]=t("span",null,"份，发包人",-1)),t("span",R,l(s.num4),1),e[18]||(e[18]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ae=x(P,[["render",X]]);export{ae as default};
