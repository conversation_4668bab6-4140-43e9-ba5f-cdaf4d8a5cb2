import{F as P,D as T}from"./index-1be3ad72.js";import{_ as B}from"./index-4829f8e2.js";import{Q as b,R as v,X as F,V as p,k as o,U as e,Y as n,S as w,W as U,F as D}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const O={name:"CB17",components:{FormTemplate:P,DocumentPart:T},emits:[],props:{},setup(u,{attrs:t,slots:_,emit:f}){},data(){return{detailTable:[],attachmentDesc:"自检资料",tableList:[{content:"备料情况"},{content:"施工配合比"},{content:"检测装备"},{content:"基面/施工缝处理"},{content:"钢筋制安"},{content:"模板支立"},{content:"细部结构"},{content:"预埋件（含止水安装、监测仪器安装）"},{content:"混凝土系统准备"}]}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:u,detailParamList:t}){},onBeforeSubmit({formData:u,detailParamList:t,taskComment3:_},f){return new Promise((d,m)=>{try{d()}catch(a){m(a)}})}}},g={class:"cb-table"},A={class:"cell"},W={class:"cell"},z={class:"cell"},E={class:"cell"},I={class:"cb-table"},S={class:"cell",style:{"text-align":"center"}},Q={class:"cell"},R={class:"attachment-desc"},X={class:"comment-wp"},Y={class:"textarea-wp"},q={class:"comment-wp"},G={class:"textarea-wp"},H={class:"footer-input"},J={class:"form-info"},K={class:"form-info"},M={class:"form-info"},Z={class:"form-info"};function $(u,t,_,f,d,m){const a=b("van-field"),c=b("DocumentPart"),x=b("FormTemplate");return v(),F(x,{ref:"FormTemplate",nature:"开仓","on-after-init":m.onAfterInit,"on-before-submit":m.onBeforeSubmit,"detail-table":d.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:d.attachmentDesc},{default:p(({formData:l,formTable:C,baseObj:r,uploadAccept:L,taskStart:i,taskComment2:V,taskComment3:y,taskComment4:N,taskComment5:k})=>[o(c,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:l.constructionDeptName,deptOptions:r.constructionDeptName,personLabel:"现场负责人：",labelWidth:"10em",disabled:!i},{default:p(()=>[t[6]||(t[6]=e("div",{class:"one-line"},[e("span",{style:{"padding-left":"2em"}},"我方下述工程混凝土浇筑准备工作已就绪，"),e("span",null,"请贵方审批。")],-1)),e("div",null,[e("table",g,[e("tbody",null,[e("tr",null,[t[0]||(t[0]=e("th",null,[e("div",{class:"cell"},"单位工程名称")],-1)),e("td",null,[e("div",A,n(l.field1),1)]),t[1]||(t[1]=e("th",null,[e("div",{class:"cell"},"分部工程名称")],-1)),e("td",null,[e("div",W,n(l.field2),1)])]),e("tr",null,[t[2]||(t[2]=e("th",null,[e("div",{class:"cell"},"单元工程名称")],-1)),e("td",null,[e("div",z,n(l.field3),1)]),t[3]||(t[3]=e("th",null,[e("div",{class:"cell"},"单元工程编码")],-1)),e("td",null,[e("div",E,n(l.field4),1)])])])]),e("table",I,[e("tbody",null,[t[4]||(t[4]=e("tr",null,[e("th",{rowspan:"10",style:{width:"50px"}},[e("div",{class:"cell"},"申 报 意 见")]),e("td",null,[e("div",{class:"cell"},"主要内容")]),e("td",null,[e("div",{class:"cell"},"准备情况")])],-1)),(v(!0),w(D,null,U(d.tableList||[],(s,h)=>(v(),w("tr",{key:h},[e("td",null,[e("div",S,n(s.content),1)]),e("td",null,[e("div",Q,n(l["field".concat(h+5)]),1)])]))),128))])])]),e("div",R,[t[5]||(t[5]=e("div",null,"附件：",-1)),o(a,{modelValue:l.attachmentDesc,"onUpdate:modelValue":s=>l.attachmentDesc=s,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2,__:[6]},1032,["deptValue","deptOptions","disabled"]),o(c,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:l.epcDeptName,deptOptions:r.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!i},{default:p(()=>[e("div",X,[t[7]||(t[7]=e("div",null,"EPC总承包项目部意见：",-1)),e("div",Y,[o(a,{modelValue:l.comment2,"onUpdate:modelValue":s=>l.comment2=s,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!V},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),o(c,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:l.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!i},{default:p(()=>[e("div",q,[t[8]||(t[8]=e("div",null,"审批意见：",-1)),e("div",G,[o(a,{modelValue:l.comment3,"onUpdate:modelValue":s=>l.comment3=s,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!y},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:l,formTable:C,baseObj:r,uploadAccept:L,taskStart:i,taskComment2:V,taskComment3:y,taskComment4:N,taskComment5:k})=>[e("div",H,[t[9]||(t[9]=e("span",null,"说明：本表一式",-1)),e("span",J,n(l.num1),1),t[10]||(t[10]=e("span",null,"份，由承包人填写，监理机构审批后，发包人",-1)),e("span",K,n(l.num2),1),t[11]||(t[11]=e("span",null,"份，监理机构",-1)),e("span",M,n(l.num3),1),t[12]||(t[12]=e("span",null,"份，承包人",-1)),e("span",Z,n(l.num4),1),t[13]||(t[13]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const be=B(O,[["render",$]]);export{be as default};
