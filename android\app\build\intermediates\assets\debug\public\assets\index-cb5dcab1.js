import{F as b,a as k}from"./index-8d635ba7.js";import{D as V,a as w}from"./DesignDispatchDrawingTable-897743ce.js";import{F as T}from"./FormItemPerson-bd0e3e57.js";import{_ as h}from"./index-4829f8e2.js";import{Q as n,R as i,X as s,V as d,k as o,S as Y,Z as p,F as E}from"./verder-361ae6c7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";const P={name:"TechnologyDesignProprietorAuditNew",components:{FlowForm:b,DesignDispatchCommonTopNew:V,DesignDispatchDrawingTable:w,FormItemPerson:T,FormItemMultiplePerson:k},data(){var m,r;return{type:((m=this.$route.query)==null?void 0:m.type)||"",taskKey:((r=this.$route.query)==null?void 0:r.taskKey)||"",modelKey:"technology_design_proprietor_audit_2",entityName:"TechnologyCommonFile",detailEntityName:"TechnologyDesignEPCAudit",detailParamList:[],detailEntityNameList:["TechnologyDesignEPCAudit","TechnologyDesignEPCAuditDrawing"],service:{query:"/cybereng-technology/design/EPCAudit/query",submit:"/cybereng-technology/design/form/commit"},formKey:"TechnologyDesignProprietorAuditNew",formData:{formKey:"TechnologyDesignProprietorAuditNew",portalId:"",subProjectName:"",subProjectId:null,modelType:"",sendingName:"",sendingCode:"",sendingType:"",sendingTypeCode:"",contentDescription:"",profession:"",relavancePartName:"",relavancePart:"",leadDesigner:"",leadDesignerFullname:"",userTelephoneNum:"",fileNum:"",fileName:"",notifyMethods:[],isconfirm1:!0,approverUsername1:"",approverFullname1:"",approverUnit1:"",time1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",time2:"",approverUsername3:"",approverFullname3:"",approverUnit3:"",time3:"",approverUsername4:"",approverFullname4:"",approverUnit4:"",time4:"",approverUsername5:"",approverFullname5:"",approverUnit5:"",time5:"",approverUsername6:"",approverFullname6:"",approverUnit6:"",time6:"",approverUsername7:"",approverFullname7:"",time7:"",duplicateUsername1:"",duplicateFullname1:"",duplicateUsername2:"",duplicateFullname2:"",relatedBpmId:""},formDetailTable:[],detailId:""}},computed:{canEdit1(){return this.type==="execute"&&this.taskKey==="UserTask_1"}},mounted(){this.initForm()},methods:{async initForm(){this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,m=>{const{detailParamList:r=[],entityObject:u}=m;this.detailParamList=r,this.detailId=r[0].detailEntityArray[0].id,this.formData={...this.formData,...r[0].detailEntityArray[0],...u,notifyMethods:u.notifyMethod?u.notifyMethod.split(","):[]},this.formDetailTable=r[1].detailEntityArray})})},async onDraft(){try{const m={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,m)}catch(m){console.log(m)}},async onSubmit(){try{if(["fileName","fileNum","relavancePartName","profession","leadDesignerFullname","subProjectName","approverFullname1"].some(D=>!this.formData[D]))return this.$showToast({message:"请登录数智建管系统完善表单"});if(!this.formDetailTable.length)return this.$showToast({message:"请登录数智建管系统完善表单"});await this.$refs.form.validate(),this.canEdit1&&(this.formData.approverUsername7="".concat(this.formData.approverUsername2,",").concat(this.formData.approverUsername3,",").concat(this.formData.approverUsername4),this.formData.approverFullname7="".concat(this.formData.approverFullname2,",").concat(this.formData.approverFullname3,",").concat(this.formData.approverFullname4)),this.setTime();const u={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,u)}catch(m){console.log(m)}},setTime(){switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":{this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_2":{this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_3":{this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_4":{this.formData.time4=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_5":{this.formData.time5=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_6":{this.formData.time6=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_7":{this.formData.time7=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}}},async afterSubmit(){}}};function M(m,r,u,D,e,t){const y=n("design-dispatch-common-top-new"),v=n("design-dispatch-drawing-table"),l=n("van-field"),f=n("FormItemPerson"),U=n("FormItemMultiplePerson"),F=n("van-cell-group"),g=n("van-form"),N=n("FlowForm");return i(),s(N,{ref:"FlowForm","model-key":e.modelKey,"form-key":e.formKey,"entity-name":e.entityName,"detail-param-list":e.detailParamList,"detail-entity-name-list":e.detailEntityNameList,onDraftClick:t.onDraft,onSubmitClick:t.onSubmit,onAfterSubmit:t.afterSubmit},{default:d(()=>[o(g,{ref:"form","label-width":"8em","input-align":"right","error-message-align":"right"},{default:d(()=>[o(y,{"form-data":e.formData,type:e.type,readonly:""},null,8,["form-data","type"]),o(v,{"form-detail-table":e.formDetailTable,ref:"detailForm","form-data":e.formData,type:e.type},null,8,["form-detail-table","form-data","type"]),o(F,{border:!1},{default:d(()=>[o(l,{modelValue:e.formData.userFullname,"onUpdate:modelValue":r[0]||(r[0]=a=>e.formData.userFullname=a),label:"发起人",readonly:""},null,8,["modelValue"]),o(l,{modelValue:e.formData.prjDepName,"onUpdate:modelValue":r[1]||(r[1]=a=>e.formData.prjDepName=a),label:"发起人部门",readonly:""},null,8,["modelValue"]),o(l,{modelValue:e.formData.approverFullname1,"onUpdate:modelValue":r[2]||(r[2]=a=>e.formData.approverFullname1=a),required:"",label:"业主联络人",readonly:""},null,8,["modelValue"]),o(l,{modelValue:e.formData.approverUnit1,"onUpdate:modelValue":r[3]||(r[3]=a=>e.formData.approverUnit1=a),label:"业主联络人部门",readonly:""},null,8,["modelValue"]),o(l,{modelValue:e.formData.duplicateFullname1,"onUpdate:modelValue":r[4]||(r[4]=a=>e.formData.duplicateFullname1=a),label:"抄送至",readonly:""},null,8,["modelValue"]),e.type==="view"||e.type==="execute"&&e.taskKey!=="UserTask_0"?(i(),Y(E,{key:0},[e.formData.approverUsername3||t.canEdit1?(i(),s(f,{key:0,label:"监理评审人",userName:e.formData.approverUsername3,"onUpdate:userName":r[5]||(r[5]=a=>e.formData.approverUsername3=a),userFullname:e.formData.approverFullname3,"onUpdate:userFullname":r[6]||(r[6]=a=>e.formData.approverFullname3=a),deptName:e.formData.approverUnit3,"onUpdate:deptName":r[7]||(r[7]=a=>e.formData.approverUnit3=a),title:"选择监理评审人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!t.canEdit1},null,8,["userName","userFullname","deptName","readonly"])):p("",!0),o(l,{modelValue:e.formData.approverUnit3,"onUpdate:modelValue":r[8]||(r[8]=a=>e.formData.approverUnit3=a),label:"监理评审人部门",readonly:""},null,8,["modelValue"]),e.formData.approverUsername4||t.canEdit1?(i(),s(f,{key:1,label:"施工评审人",userName:e.formData.approverUsername4,"onUpdate:userName":r[9]||(r[9]=a=>e.formData.approverUsername4=a),userFullname:e.formData.approverFullname4,"onUpdate:userFullname":r[10]||(r[10]=a=>e.formData.approverFullname4=a),deptName:e.formData.approverUnit4,"onUpdate:deptName":r[11]||(r[11]=a=>e.formData.approverUnit4=a),title:"选择施工评审人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!t.canEdit1},null,8,["userName","userFullname","deptName","readonly"])):p("",!0),o(l,{modelValue:e.formData.approverUnit4,"onUpdate:modelValue":r[12]||(r[12]=a=>e.formData.approverUnit4=a),label:"施工评审人部门",readonly:""},null,8,["modelValue"]),e.formData.approverUsername2||t.canEdit1?(i(),s(f,{key:2,label:"业主评审人",userName:e.formData.approverUsername2,"onUpdate:userName":r[13]||(r[13]=a=>e.formData.approverUsername2=a),userFullname:e.formData.approverFullname2,"onUpdate:userFullname":r[14]||(r[14]=a=>e.formData.approverFullname2=a),deptName:e.formData.approverUnit2,"onUpdate:deptName":r[15]||(r[15]=a=>e.formData.approverUnit2=a),title:"选择业主评审人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!t.canEdit1},null,8,["userName","userFullname","deptName","readonly"])):p("",!0),o(l,{modelValue:e.formData.approverUnit2,"onUpdate:modelValue":r[16]||(r[16]=a=>e.formData.approverUnit2=a),label:"业主评审人部门",readonly:""},null,8,["modelValue"]),e.formData.approverUsername5||t.canEdit1?(i(),s(f,{key:3,label:"意见汇总人",userName:e.formData.approverUsername5,"onUpdate:userName":r[17]||(r[17]=a=>e.formData.approverUsername5=a),userFullname:e.formData.approverFullname5,"onUpdate:userFullname":r[18]||(r[18]=a=>e.formData.approverFullname5=a),deptName:e.formData.approverUnit5,"onUpdate:deptName":r[19]||(r[19]=a=>e.formData.approverUnit5=a),title:"选择意见汇总人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!t.canEdit1},null,8,["userName","userFullname","deptName","readonly"])):p("",!0),o(l,{modelValue:e.formData.approverUnit5,"onUpdate:modelValue":r[20]||(r[20]=a=>e.formData.approverUnit5=a),label:"意见汇总人部门",readonly:""},null,8,["modelValue"]),e.formData.approverUsername6||t.canEdit1?(i(),s(f,{key:4,label:"南排公司质安部联络人",userName:e.formData.approverUsername6,"onUpdate:userName":r[21]||(r[21]=a=>e.formData.approverUsername6=a),userFullname:e.formData.approverFullname6,"onUpdate:userFullname":r[22]||(r[22]=a=>e.formData.approverFullname6=a),deptName:e.formData.approverUnit6,"onUpdate:deptName":r[23]||(r[23]=a=>e.formData.approverUnit6=a),title:"选择南排公司质安部联络人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!t.canEdit1},null,8,["userName","userFullname","deptName","readonly"])):p("",!0),o(l,{modelValue:e.formData.approverUnit6,"onUpdate:modelValue":r[24]||(r[24]=a=>e.formData.approverUnit6=a),label:"南排公司质安部联络人部门",readonly:""},null,8,["modelValue"]),e.formData.duplicateUsername2||t.canEdit1?(i(),s(U,{key:5,label:"抄送至",title:"选择抄送人",readonly:e.type==="view"||!t.canEdit1,"user-name":e.formData.duplicateUsername2,"onUpdate:userName":r[25]||(r[25]=a=>e.formData.duplicateUsername2=a),"user-fullname":e.formData.duplicateFullname2,"onUpdate:userFullname":r[26]||(r[26]=a=>e.formData.duplicateFullname2=a)},null,8,["readonly","user-name","user-fullname"])):p("",!0)],64)):p("",!0)]),_:1})]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}const R=h(P,[["render",M]]);export{R as default};
