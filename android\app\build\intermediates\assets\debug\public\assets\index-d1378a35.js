import{F as x,D as F}from"./index-a831f9da.js";import{_ as P}from"./index-4829f8e2.js";import{Q as u,R as _,X as $,V as p,k as a,U as s,Y as l,B as v}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const L={name:"JL17",components:{FormTemplate:x,DocumentPart:F},emits:[],props:{},setup(t,{attrs:e,slots:f,emit:b}){},data(){return{detailTable:[],attachmentDesc:"索赔审核意见。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:t,detailParamList:e}){},onBeforeSubmit({formData:t,detailParamList:e,taskStart:f},b){return new Promise((m,o)=>{try{if(b==="submit"&&f){if(!t.check1&&!t.check2)return this.$showNotify({type:"danger",message:"请勾选索赔意见",duration:3*1e3}),o(!1),!1;if(t.check2&&(!t.field6||!t.field7||!t.field8))return this.$showNotify({type:"danger",message:"请完善索赔信息",duration:3*1e3}),o(!1),!1}m()}catch(r){o(r)}})},changeCheck1(t){t&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.field6="",this.$refs.FormTemplate.formData.field7="",this.$refs.FormTemplate.formData.field8="",this.$nextTick(()=>{var e;(e=this.$refs.FormTemplate.$refs.form)==null||e.resetValidation()}))},changeCheck2(t){t&&(this.$refs.FormTemplate.formData.check1=!1)}}},U={class:"one-line"},B={class:"form-info"},c={class:"form-info"},A={class:"form-info"},O={class:"form-info"},q={class:"form-info"},I={class:"form-info"},W={class:"one-line"},z={class:"check-wp"},J={class:"one-line"},Q={class:"check-wp"},R={class:"form-info"},X={class:"form-info"},Y={class:"form-info"},E={class:"attachment-desc"},G={class:"footer-input"},H={class:"form-info"},K={class:"form-info"},M={class:"form-info"},S={class:"form-info"};function Z(t,e,f,b,m,o){const r=u("van-checkbox"),g=u("van-field"),V=u("DocumentPart"),C=u("FormTemplate");return _(),$(C,{ref:"FormTemplate",nature:"索赔审","employer-target":!0,"on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!1,attachmentDesc:m.attachmentDesc},{default:p(({formData:n,formTable:T,baseObj:k,uploadAccept:h,taskStart:i,taskComment2:y,taskComment3:N,taskComment4:w})=>[a(V,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:n.supervisionDeptName,deptOptions:k.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!i},{default:p(()=>[s("div",U,[e[0]||(e[0]=s("span",{style:{"padding-left":"2em"}},"根据有关规定和施工合同约定，你方提出的索赔申请报告",-1)),e[1]||(e[1]=s("span",null,"（ ",-1)),s("span",B,l(n.constructionName),1),e[2]||(e[2]=s("span",null," [ ",-1)),s("span",c,l(n.field1),1),e[3]||(e[3]=s("span",null," ] 赔报",-1)),s("span",A,l(n.field2),1),e[4]||(e[4]=s("span",null,"号），",-1)),e[5]||(e[5]=s("span",null,"索赔",-1)),e[6]||(e[6]=s("span",null,"金额",-1)),e[7]||(e[7]=s("span",null,"（大写）",-1)),s("span",O,l(n.field3),1),e[8]||(e[8]=s("span",null,"元",-1)),e[9]||(e[9]=s("span",null,"（小写",-1)),s("span",q,l(n.field4),1),e[10]||(e[10]=s("span",null,"元），",-1)),e[11]||(e[11]=s("span",null,"工期顺延",-1)),s("span",I,l(n.field5),1),e[12]||(e[12]=s("span",null,"天，",-1)),e[13]||(e[13]=s("span",null,"经我方审核：",-1))]),s("div",W,[s("div",z,[a(r,{modelValue:n.check1,"onUpdate:modelValue":d=>n.check1=d,shape:"square",disabled:!i,onChange:o.changeCheck1},{default:p(()=>e[14]||(e[14]=[v("不同意此项索赔")])),_:2,__:[14]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),s("div",J,[s("div",Q,[a(r,{modelValue:n.check2,"onUpdate:modelValue":d=>n.check2=d,shape:"square",disabled:!i,onChange:o.changeCheck2},{default:p(()=>e[15]||(e[15]=[v("同意此项索赔")])),_:2,__:[15]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[16]||(e[16]=s("span",null,"，",-1)),e[17]||(e[17]=s("span",null,"核准索赔金额为",-1)),e[18]||(e[18]=s("span",null,"（大写）",-1)),s("span",R,l(n.field6),1),e[19]||(e[19]=s("span",null,"元",-1)),e[20]||(e[20]=s("span",null,"（小写",-1)),s("span",X,l(n.field7),1),e[21]||(e[21]=s("span",null,"元），",-1)),e[22]||(e[22]=s("span",null,"工期顺延",-1)),s("span",Y,l(n.field8),1),e[23]||(e[23]=s("span",null,"天。",-1))]),s("div",E,[e[24]||(e[24]=s("div",null,"附件：",-1)),a(g,{modelValue:n.attachmentDesc,"onUpdate:modelValue":d=>n.attachmentDesc=d,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),a(V,{deptLabel:"发包人：",deptProp:"employerName",deptValue:n.employerName,deptOptions:k.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!i},{default:p(()=>e[25]||(e[25]=[s("div",{class:"comment-wp"},[s("div",{style:{height:"30px"}})],-1)])),_:2,__:[25]},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:n,formTable:T,baseObj:k,uploadAccept:h,taskStart:i,taskComment2:y,taskComment3:N,taskComment4:w})=>[s("div",G,[e[26]||(e[26]=s("span",null,"说明：本表一式",-1)),s("span",H,l(n.num1),1),e[27]||(e[27]=s("span",null,"份，由监理机构填写，发包人签署后，承包人",-1)),s("span",K,l(n.num2),1),e[28]||(e[28]=s("span",null,"份，监理机构",-1)),s("span",M,l(n.num3),1),e[29]||(e[29]=s("span",null,"份，发包人",-1)),s("span",S,l(n.num4),1),e[30]||(e[30]=s("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const fe=P(L,[["render",Z]]);export{fe as default};
