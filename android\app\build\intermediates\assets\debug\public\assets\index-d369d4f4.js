import{F as U,D as C}from"./index-a831f9da.js";import{_ as N}from"./index-4829f8e2.js";import{Q as c,R as P,X as L,V as m,k as d,U as e,Y as l}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const h={name:"JL15",components:{FormTemplate:U,DocumentPart:C},emits:[],props:{},setup(r,{attrs:t,slots:b,emit:u}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:r,detailParamList:t}){},onBeforeSubmit({formData:r,detailParamList:t},b){return new Promise((u,a)=>{try{u()}catch(i){a(i)}})}}},k={class:"one-line"},B={class:"form-info"},F={class:"form-info"},z={class:"form-info"},A={class:"comment-wp"},O={class:"textarea-wp"},g={class:"comment-wp"},I={class:"textarea-wp"},W={class:"comment-wp"},D={class:"textarea-wp"},J={class:"comment-wp"},Q={class:"textarea-wp"},R={class:"footer-input"},X={class:"form-info"},Y={class:"form-info"},j={class:"form-info"},q={class:"form-info"},E={class:"form-info"};function G(r,t,b,u,a,i){const p=c("van-field"),_=c("DocumentPart"),V=c("FormTemplate");return P(),L(V,{ref:"FormTemplate",nature:"停工","on-after-init":i.onAfterInit,"on-before-submit":i.onBeforeSubmit,"detail-table":a.detailTable,"is-show-confirm1":!1,attachmentDesc:a.attachmentDesc},{default:m(({formData:o,formTable:v,baseObj:f,uploadAccept:w,taskStart:s,taskComment2:y,taskComment3:x,taskComment4:T})=>[d(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:f.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!s},{default:m(()=>[e("div",k,[t[0]||(t[0]=e("span",{style:{"padding-left":"2em"}},"由于本通知所述原因，现通知你方于",-1)),e("span",B,l(o.field1),1),t[1]||(t[1]=e("span",null,"年",-1)),e("span",F,l(o.field2),1),t[2]||(t[2]=e("span",null,"月",-1)),e("span",z,l(o.field3),1),t[3]||(t[3]=e("span",null,"时对工程项目暂停施工。",-1))]),e("div",A,[t[4]||(t[4]=e("div",null,"暂停施工范围说明：",-1)),e("div",O,[d(p,{modelValue:o.field5,"onUpdate:modelValue":n=>o.field5=n,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",g,[t[5]||(t[5]=e("div",null,"暂停施工原因：",-1)),e("div",I,[d(p,{modelValue:o.field6,"onUpdate:modelValue":n=>o.field6=n,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",W,[t[6]||(t[6]=e("div",null,"引用合同条款或法规依据：",-1)),e("div",D,[d(p,{modelValue:o.field7,"onUpdate:modelValue":n=>o.field7=n,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",J,[t[7]||(t[7]=e("div",null,"暂停施工期间要求：",-1)),e("div",Q,[d(p,{modelValue:o.field8,"onUpdate:modelValue":n=>o.field8=n,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),d(_,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:f.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!s},{default:m(()=>t[8]||(t[8]=[e("div",{class:"comment-wp"},[e("div",{style:{height:"30px"}})],-1)])),_:2,__:[8]},1032,["deptValue","deptOptions","disabled"])]),footer:m(({formData:o,formTable:v,baseObj:f,uploadAccept:w,taskStart:s,taskComment2:y,taskComment3:x,taskComment4:T})=>[e("div",R,[t[9]||(t[9]=e("span",null,"说明：本表一式",-1)),e("span",X,l(o.num1),1),t[10]||(t[10]=e("span",null,"份，由监理机构填写，承包人签了后，发包人",-1)),e("span",Y,l(o.num2),1),t[11]||(t[11]=e("span",null,"份，设代机构",-1)),e("span",j,l(o.num3),1),t[12]||(t[12]=e("span",null,"份，监理机构",-1)),e("span",q,l(o.num4),1),t[13]||(t[13]=e("span",null,"份，承包人",-1)),e("span",E,l(o.num5),1),t[14]||(t[14]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const pe=N(h,[["render",G]]);export{pe as default};
