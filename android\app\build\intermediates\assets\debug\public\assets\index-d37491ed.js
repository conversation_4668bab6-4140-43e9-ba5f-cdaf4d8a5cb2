import{h,_ as c}from"./index-4829f8e2.js";import{F as k}from"./index-8d635ba7.js";import{F as R}from"./FormItemPicker-d3f69283.js";import{F as E}from"./FormItemDate-ba00d9d5.js";import{F as U}from"./FormItemCalendar-905fde75.js";import{F as S}from"./FormItemPerson-bd0e3e57.js";import{F as I}from"./FormItemCoord-9e82e1bf.js";import{U as q}from"./index-fc22947f.js";import{Q as a,R as d,X as D,V as s,k as l,Z as y}from"./verder-361ae6c7.js";import"./vant-91101745.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./validate-2249584f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";function w(o){return h({url:"/cybereng-quality/quality/problem/delayTask",method:"get",params:o})}const P={name:"QualityThreatSupervisor",components:{FlowForm:k,FormItemPicker:R,FormItemDate:E,FormItemCalendar:U,FormItemPerson:S,FormItemCoord:I,UploadFiles:q},props:{},emits:[],setup(o,{attrs:r,slots:u,emit:n}){},data(){var o,r;return{type:((o=this.$route.query)==null?void 0:o.type)||"",taskKey:((r=this.$route.query)==null?void 0:r.taskKey)||"",entityName:"QualityProblem",formKey:"QualityThreatSupervisor",modelKey:"quality_hidden_danger_flow_jl",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-quality/quality/form/query",submit:"/cybereng-quality/form/commit"},formData:{type:"2",id:void 0,portalId:void 0,subProjectId:"",subProjectName:"",unitEngineeringId:"",unitEngineeringName:"",divisionEngineeringId:"",divisionEngineeringName:"",longitude:"",latitude:"",problemCode:void 0,rectifyDate:"",overdueState:"",problemContent:"",beforeFileToken:"",problemReportor:"",problemReportorFullname:"",problemReportorDeptName:"",problemReportorDeptCode:"",problemChecker:"",problemCheckerFullname:"",problemCheckerDeptName:"",problemCheckerDeptCode:"",problemRectifier:"",problemRectifierFullname:"",problemRectifierDeptName:"",problemRectifierDeptCode:"",problemSupervisor:"",problemSupervisorFullname:"",problemSupervisorDeptName:"",problemSupervisorDeptCode:"",problemRectifyApprover:"",problemRectifyApproverFullname:"",problemRectifyApproverDeptName:"",problemRectifyApproverDeptCode:"",rectifyState:"待整改",rectifyMeasures:"",rectifySituation:"",afterFileToken:"",finishDate:""}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},unitEngineeringList(){if(this.portal.type==1){if(!this.formData.subProjectId)return[];const o=this.subProjectList.find(r=>r.id==this.formData.subProjectId);return o?o.children||[]:[]}else return this.subProjectList[0]?this.subProjectList[0].children||[]:[]},divisionEngineeringList(){if(!this.formData.unitEngineeringId)return[];const o=this.unitEngineeringList.find(r=>r.id==this.formData.unitEngineeringId);return o?o.children||[]:[]},canEdit0(){return this.type==="add"||this.taskKey==="UserTask_0"},canEdit1(){return this.type==="execute"&&this.taskKey==="UserTask_1"},canEdit2(){return this.type==="execute"&&this.taskKey==="UserTask_2"}},watch:{},created(){},mounted(){this.initForm()},methods:{copyCallBack(o){this.formData.divisionEngineeringId=o.divisionEngineeringId,this.formData.divisionEngineeringName=o.divisionEngineeringName,this.formData.portalId=o.portalId,this.formData.problemChecker=o.problemChecker,this.formData.problemCheckerDeptCode=o.problemCheckerDeptCode,this.formData.problemCheckerDeptName=o.problemCheckerDeptName,this.formData.problemCheckerFullname=o.problemCheckerFullname,this.formData.problemRectifier=o.problemRectifier,this.formData.problemRectifierDeptCode=o.problemRectifierDeptCode,this.formData.problemRectifierDeptName=o.problemRectifierDeptName,this.formData.problemRectifierFullname=o.problemRectifierFullname,this.formData.problemRectifyApprover=o.problemRectifyApprover,this.formData.problemRectifyApproverDeptCode=o.problemRectifyApproverDeptCode,this.formData.problemRectifyApproverDeptName=o.problemRectifyApproverDeptName,this.formData.problemRectifyApproverFullname=o.problemRectifyApproverFullname,this.formData.problemReportor=o.problemReportor,this.formData.problemReportorDeptCode=o.problemReportorDeptCode,this.formData.problemReportorDeptName=o.problemReportorDeptName,this.formData.problemReportorFullname=o.problemReportorFullname,this.formData.problemSupervisor=o.problemSupervisor,this.formData.problemSupervisorDeptCode=o.problemSupervisorDeptCode,this.formData.problemSupervisorDeptName=o.problemSupervisorDeptName,this.formData.problemSupervisorFullname=o.problemSupervisorFullname,this.formData.rectifyDate=o.rectifyDate,this.formData.rectifyState=o.rectifyState,this.formData.subProjectId=o.subProjectId,this.formData.subProjectName=o.subProjectName,this.formData.type=o.type,this.formData.unitEngineeringId=o.unitEngineeringId,this.formData.unitEngineeringName=o.unitEngineeringName},async initForm(){if(this.type==="add"){if(this.formData.portalId=this.portalId,this.portal.type!=1){const m=this.subProjectList.find(p=>p.portalId==this.portal.id);this.formData.subProjectId=m==null?void 0:m.id,this.formData.subProjectName=m==null?void 0:m.nodeName}const{userName:o="",userFullname:r="",orgList:u=[]}=this.user||{},n=u.find(m=>{var p;return m.portalId==((p=this.portal)==null?void 0:p.id)})||u[0],e=(n==null?void 0:n.name)||"",i=(n==null?void 0:n.orgNo)||"";this.formData.problemReportor=o,this.formData.problemReportorFullname=r,this.formData.problemReportorDeptName=e,this.formData.problemReportorDeptCode=i,this.formData.problemRectifyApprover=o,this.formData.problemRectifyApproverFullname=r,this.formData.problemRectifyApproverDeptName=e,this.formData.problemRectifyApproverDeptCode=i}else this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,o=>{const{detailParamList:r=[],entityObject:u}=o;this.detailParamList=r,this.formData={...this.formData,...u}})})},async onDraft(){try{const o={...this.formData,rectifyState:"暂存"};this.$refs.FlowForm.onSaveDraft(this.service.submit,o)}catch(o){console.log(o)}},async onSubmit(){try{await this.$refs.form.validate();let o="待确认";this.taskKey==="UserTask_0"?o="待确认":this.taskKey==="UserTask_1"?o="待整改":this.taskKey==="UserTask_2"||this.taskKey==="UserTask_3"?o="待审批":this.taskKey==="UserTask_4"&&(o="整改完成",this.formData.finishDate=this.$dayjs().format("YYYY-MM-DD HH:mm:ss"));const r={...this.formData,rectifyState:o};this.$refs.FlowForm.onSubmit(this.service.submit,r)}catch(o){console.log(o)}},async updateFiles(){return new Promise(async(o,r)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),o()}catch(u){this.$showToast({message:"文件更新失败!"}),r()}})},afterSubmit(o,r){this.updateFiles(),o==="submit"&&this.taskKey=="UserTask_1"&&w({id:this.formData.id})},handleSubProjectChange(o={}){this.formData.unitEngineeringId="",this.formData.unitEngineeringName="",this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""},handleUnitEngineeringChange(o={}){this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""}}};function A(o,r,u,n,e,i){const m=a("van-field"),p=a("FormItemPicker"),v=a("FormItemCoord"),C=a("FormItemCalendar"),b=a("van-cell-group"),g=a("UploadFiles"),f=a("FormItemPerson"),F=a("van-form"),N=a("FlowForm");return d(),D(N,{ref:"FlowForm","model-key":e.modelKey,"form-key":e.formKey,"entity-name":e.entityName,"detail-param-list":e.detailParamList,"detail-entity-name-list":e.detailEntityNameList,onDraftClick:i.onDraft,onSubmitClick:i.onSubmit,onAfterSubmit:i.afterSubmit,onCopyCallBack:i.copyCallBack},{default:s(()=>[l(F,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:s(()=>[l(b,{border:!1},{default:s(()=>[e.formData.problemCode?(d(),D(m,{key:0,modelValue:e.formData.problemCode,"onUpdate:modelValue":r[0]||(r[0]=t=>e.formData.problemCode=t),label:"质量问题单号",placeholder:"审批完成后自动生成",readonly:""},null,8,["modelValue"])):y("",!0),l(p,{label:"子工程",value:e.formData.subProjectId,"onUpdate:value":r[1]||(r[1]=t=>e.formData.subProjectId=t),text:e.formData.subProjectName,"onUpdate:text":r[2]||(r[2]=t=>e.formData.subProjectName=t),columns:[...i.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:i.portal.type!=1||e.type==="view"||!i.canEdit0,onChange:i.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),l(p,{label:"单位工程",value:e.formData.unitEngineeringId,"onUpdate:value":r[3]||(r[3]=t=>e.formData.unitEngineeringId=t),text:e.formData.unitEngineeringName,"onUpdate:text":r[4]||(r[4]=t=>e.formData.unitEngineeringName=t),columns:[...i.unitEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择单位工程",required:"",rules:[{required:!0,message:"请选择单位工程"}],readonly:e.type==="view"||!i.canEdit0,onChange:i.handleUnitEngineeringChange},null,8,["value","text","columns","readonly","onChange"]),l(p,{label:"分部工程",value:e.formData.divisionEngineeringId,"onUpdate:value":r[5]||(r[5]=t=>e.formData.divisionEngineeringId=t),text:e.formData.divisionEngineeringName,"onUpdate:text":r[6]||(r[6]=t=>e.formData.divisionEngineeringName=t),columns:[...i.divisionEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择分部工程",required:"",rules:[{required:!0,message:"请选择分部工程"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","text","columns","readonly"]),l(v,{label:"定位",longitude:e.formData.longitude,"onUpdate:longitude":r[7]||(r[7]=t=>e.formData.longitude=t),latitude:e.formData.latitude,"onUpdate:latitude":r[8]||(r[8]=t=>e.formData.latitude=t),title:"选择定位",readonly:e.type==="view"||!i.canEdit0},null,8,["longitude","latitude","readonly"]),l(C,{label:"整改期限",value:e.formData.rectifyDate,"onUpdate:value":r[9]||(r[9]=t=>e.formData.rectifyDate=t),title:"选择整改期限",required:"",rules:[{required:!0,message:"请选择整改期限"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),e.type!=="add"?(d(),D(m,{key:1,modelValue:e.formData.rectifyState,"onUpdate:modelValue":r[10]||(r[10]=t=>e.formData.rectifyState=t),label:"整改状态",placeholder:"",readonly:""},null,8,["modelValue"])):y("",!0),e.type!=="add"?(d(),D(m,{key:2,modelValue:e.formData.overdueState,"onUpdate:modelValue":r[11]||(r[11]=t=>e.formData.overdueState=t),label:"逾期情况",placeholder:"",readonly:""},null,8,["modelValue"])):y("",!0)]),_:1}),l(b,{border:!1},{default:s(()=>[l(m,{label:"问题内容",modelValue:e.formData.problemContent,"onUpdate:modelValue":r[12]||(r[12]=t=>e.formData.problemContent=t),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入问题内容",required:"",rules:[{required:!0,message:"请输入问题内容"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),l(m,{label:"附件照片","label-align":"top","input-align":"left"},{input:s(()=>[l(g,{ref:"beforeFiles",g9s:e.formData.beforeFileToken,"onUpdate:g9s":r[13]||(r[13]=t=>e.formData.beforeFileToken=t),accept:"image/*",multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:e.type==="view"||!i.canEdit0},null,8,["g9s","readonly"])]),_:1})]),_:1}),l(b,{border:!1},{default:s(()=>[l(f,{label:"监理发起人",userName:e.formData.problemReportor,"onUpdate:userName":r[14]||(r[14]=t=>e.formData.problemReportor=t),userFullname:e.formData.problemReportorFullname,"onUpdate:userFullname":r[15]||(r[15]=t=>e.formData.problemReportorFullname=t),deptName:e.formData.problemReportorDeptName,"onUpdate:deptName":r[16]||(r[16]=t=>e.formData.problemReportorDeptName=t),deptCode:e.formData.problemReportorDeptCode,"onUpdate:deptCode":r[17]||(r[17]=t=>e.formData.problemReportorDeptCode=t),title:"选择监理发起人",required:"",rules:[{required:!0,message:"请选择监理发起人"}],readonly:!0},null,8,["userName","userFullname","deptName","deptCode"]),l(m,{modelValue:e.formData.problemReportorDeptName,"onUpdate:modelValue":r[18]||(r[18]=t=>e.formData.problemReportorDeptName=t),label:"发起人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),l(f,{label:"总包确认人",userName:e.formData.problemChecker,"onUpdate:userName":r[19]||(r[19]=t=>e.formData.problemChecker=t),userFullname:e.formData.problemCheckerFullname,"onUpdate:userFullname":r[20]||(r[20]=t=>e.formData.problemCheckerFullname=t),deptName:e.formData.problemCheckerDeptName,"onUpdate:deptName":r[21]||(r[21]=t=>e.formData.problemCheckerDeptName=t),deptCode:e.formData.problemCheckerDeptCode,"onUpdate:deptCode":r[22]||(r[22]=t=>e.formData.problemCheckerDeptCode=t),title:"选择总包确认人",required:"",rules:[{required:!0,message:"请选择总包确认人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),l(m,{modelValue:e.formData.problemCheckerDeptName,"onUpdate:modelValue":r[23]||(r[23]=t=>e.formData.problemCheckerDeptName=t),label:"确认人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),l(f,{"label-width":"8em",label:"施工单位整改人",userName:e.formData.problemRectifier,"onUpdate:userName":r[24]||(r[24]=t=>e.formData.problemRectifier=t),userFullname:e.formData.problemRectifierFullname,"onUpdate:userFullname":r[25]||(r[25]=t=>e.formData.problemRectifierFullname=t),deptName:e.formData.problemRectifierDeptName,"onUpdate:deptName":r[26]||(r[26]=t=>e.formData.problemRectifierDeptName=t),deptCode:e.formData.problemRectifierDeptCode,"onUpdate:deptCode":r[27]||(r[27]=t=>e.formData.problemRectifierDeptCode=t),title:"选择施工单位整改人",required:!i.canEdit0,rules:[{required:!i.canEdit0,message:"请选择施工单位整改人"}],readonly:e.type==="view"||!i.canEdit0&&!i.canEdit1},null,8,["userName","userFullname","deptName","deptCode","required","rules","readonly"]),l(f,{label:"总包审核人",userName:e.formData.problemSupervisor,"onUpdate:userName":r[28]||(r[28]=t=>e.formData.problemSupervisor=t),userFullname:e.formData.problemSupervisorFullname,"onUpdate:userFullname":r[29]||(r[29]=t=>e.formData.problemSupervisorFullname=t),deptName:e.formData.problemSupervisorDeptName,"onUpdate:deptName":r[30]||(r[30]=t=>e.formData.problemSupervisorDeptName=t),deptCode:e.formData.problemSupervisorDeptCode,"onUpdate:deptCode":r[31]||(r[31]=t=>e.formData.problemSupervisorDeptCode=t),title:"选择总包审核人",required:"",rules:[{required:!0,message:"请选择总包审核人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),l(f,{label:"监理审核人",userName:e.formData.problemRectifyApprover,"onUpdate:userName":r[32]||(r[32]=t=>e.formData.problemRectifyApprover=t),userFullname:e.formData.problemRectifyApproverFullname,"onUpdate:userFullname":r[33]||(r[33]=t=>e.formData.problemRectifyApproverFullname=t),deptName:e.formData.problemRectifyApproverDeptName,"onUpdate:deptName":r[34]||(r[34]=t=>e.formData.problemRectifyApproverDeptName=t),deptCode:e.formData.problemRectifyApproverDeptCode,"onUpdate:deptCode":r[35]||(r[35]=t=>e.formData.problemRectifyApproverDeptCode=t),title:"选择监理审核人",required:"",rules:[{required:!0,message:"请选择监理审核人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"])]),_:1}),e.type==="view"||e.taskKey&&e.taskKey!=="UserTask_0"&&e.taskKey!=="UserTask_1"?(d(),D(b,{key:0,border:!1},{default:s(()=>[l(m,{label:"整改措施",modelValue:e.formData.rectifyMeasures,"onUpdate:modelValue":r[36]||(r[36]=t=>e.formData.rectifyMeasures=t),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改措施",required:"",rules:[{required:!0,message:"请填写整改措施"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),l(m,{label:"整改情况",modelValue:e.formData.rectifySituation,"onUpdate:modelValue":r[37]||(r[37]=t=>e.formData.rectifySituation=t),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改情况",required:"",rules:[{required:!0,message:"请填写整改情况"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),l(m,{label:"附件照片","label-align":"top","input-align":"left"},{input:s(()=>[l(g,{ref:"afterFiles",g9s:e.formData.afterFileToken,"onUpdate:g9s":r[38]||(r[38]=t=>e.formData.afterFileToken=t),accept:"image/*",multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:e.type==="view"||!i.canEdit2},null,8,["g9s","readonly"])]),_:1})]),_:1})):y("",!0)]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit","onCopyCallBack"])}const _=c(P,[["render",A]]);export{_ as default};
