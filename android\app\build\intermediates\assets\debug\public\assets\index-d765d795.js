import{F as D,D as F}from"./index-a831f9da.js";import{_ as g}from"./index-4829f8e2.js";import{Q as b,R as a,X as B,V as p,k as v,U as t,Y as s,S as _,W as h,F as k}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const A={name:"JL13",components:{FormTemplate:D,DocumentPart:F},emits:[],props:{},setup(m,{attrs:e,slots:y,emit:d}){},data(){return{detailTable:[{},{},{},{},{},{},{},{},{},{}],attachmentDesc:"1、变更项目价格申报表\n2、监理变更单价审核说明\n3、监理变更单价分析表\n4、变更项目价格变化汇总表"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:m,detailParamList:e}){},onBeforeSubmit({formData:m,detailParamList:e},y){return new Promise((d,n)=>{try{d()}catch(o){n(o)}})}}},O={class:"comment-wp"},S={style:{"text-indent":"2em"}},U={class:"form-info"},W={class:"form-info"},I={class:"form-info"},j={class:"jl-table"},z={class:"cell",style:{"text-align":"center"}},E=["data-i"],J={class:"cell"},Q={class:"attachment-desc"},R={class:"footer-input"},X={class:"form-info"},Y={class:"form-info"},$={class:"form-info"},q={class:"form-info"};function G(m,e,y,d,n,o){const V=b("van-field"),w=b("DocumentPart"),N=b("FormTemplate");return a(),B(N,{ref:"FormTemplate",nature:"变价审","employer-target":!0,"on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":n.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:n.attachmentDesc},{default:p(({formData:l,formTable:x,baseObj:r,uploadAccept:C,taskStart:i,taskComment2:L,taskComment3:P,taskComment4:T})=>[v(w,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:l.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!i},{default:p(()=>[t("div",O,[t("div",S,[e[0]||(e[0]=t("span",null,"根据有关规定和施工合同约定，承包人提出的变更项目价格申报表",-1)),e[1]||(e[1]=t("span",null,"（ ",-1)),t("span",U,s(l.constructionName),1),e[2]||(e[2]=t("span",null," [ ",-1)),t("span",W,s(l.field1),1),e[3]||(e[3]=t("span",null," ] 变价",-1)),t("span",I,s(l.field2),1),e[4]||(e[4]=t("span",null,"号）",-1)),e[5]||(e[5]=t("span",null,"经我方审核， ",-1)),e[6]||(e[6]=t("span",null,"变更项目价格如下，",-1)),e[7]||(e[7]=t("span",null,"请贵方审核。",-1))])]),t("table",j,[e[9]||(e[9]=t("colgroup",null,[t("col",{width:"30px"}),t("col",{"min-width":"40px"}),t("col",{"min-width":"40px"}),t("col",{"min-width":"40px"}),t("col",{"min-width":"40px"}),t("col",{"min-width":"40px"})],-1)),t("tbody",null,[e[8]||(e[8]=t("tr",null,[t("th",null,[t("div",{class:"cell"},"序号")]),t("th",null,[t("div",{class:"cell"},"项目名称")]),t("th",null,[t("div",{class:"cell"},"单位")]),t("th",null,[t("div",{class:"cell"},"承包人申报价格（单价或合价）")]),t("th",null,[t("div",{class:"cell"},"监理审核价格（单价或合价）")]),t("th",null,[t("div",{class:"cell"},"备注")])],-1)),(a(!0),_(k,null,h(x||[],(u,c)=>(a(),_("tr",{key:c},[t("td",null,[t("div",z,s(c+1),1)]),(a(),_(k,null,h(5,f=>t("td",{key:c+"_"+f,"data-i":f},[t("div",J,s(u["field".concat(f)]),1)],8,E)),64))]))),128))])]),t("div",Q,[e[10]||(e[10]=t("div",null,"附件：",-1)),v(V,{modelValue:l.attachmentDesc,"onUpdate:modelValue":u=>l.attachmentDesc=u,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),v(w,{deptLabel:"发包人：",deptProp:"employerName",deptValue:l.employerName,deptOptions:r.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!i},{default:p(()=>e[11]||(e[11]=[t("div",{class:"comment-wp"},[t("div",{style:{height:"30px"}})],-1)])),_:2,__:[11]},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:l,formTable:x,baseObj:r,uploadAccept:C,taskStart:i,taskComment2:L,taskComment3:P,taskComment4:T})=>[t("div",R,[e[12]||(e[12]=t("span",null,"说明：本表一式",-1)),t("span",X,s(l.num1),1),e[13]||(e[13]=t("span",null,"份，由监理机构填写，承包人",-1)),t("span",Y,s(l.num2),1),e[14]||(e[14]=t("span",null,"份，监理机构",-1)),t("span",$,s(l.num3),1),e[15]||(e[15]=t("span",null,"份，发包人",-1)),t("span",q,s(l.num4),1),e[16]||(e[16]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const rt=g(A,[["render",G]]);export{rt as default};
