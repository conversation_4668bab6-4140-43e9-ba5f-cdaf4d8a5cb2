import{F as h,D as T}from"./index-a831f9da.js";import{_ as C}from"./index-4829f8e2.js";import{Q as c,R as N,X as P,V as m,k as s,U as e,Y as d}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const L={name:"JL12",components:{FormTemplate:h,DocumentPart:T},emits:[],props:{},setup(r,{attrs:t,slots:V,emit:u}){},data(){return{detailTable:[],attachmentDesc:"1、变更项目清单（含估算工程量）及说明\n2、设计文件、施工图纸（若有）\n3、其他变更依据"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:r,detailParamList:t}){},onBeforeSubmit({formData:r,detailParamList:t},V){return new Promise((u,i)=>{try{u()}catch(p){i(p)}})}}},z={class:"comment-wp"},B={style:{"text-indent":"2em"}},F={class:"form-info"},A={class:"form-info"},O={class:"form-info"},k={class:"comment-wp"},g={class:"textarea-wp"},I={class:"comment-wp"},W={class:"textarea-wp"},J={class:"comment-wp"},Q={class:"textarea-wp"},R={class:"comment-wp"},X={class:"textarea-wp"},Y={class:"comment-wp"},j={class:"textarea-wp"},q={class:"attachment-desc"},D={class:"footer-input"},E={class:"form-info"},G={class:"form-info"},H={class:"form-info"},K={class:"form-info"};function M(r,t,V,u,i,p){const a=c("van-field"),b=c("DocumentPart"),v=c("FormTemplate");return N(),P(v,{ref:"FormTemplate",nature:"变指","on-after-init":p.onAfterInit,"on-before-submit":p.onBeforeSubmit,"detail-table":i.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:i.attachmentDesc},{default:m(({formData:l,formTable:_,baseObj:f,uploadAccept:w,taskStart:n,taskComment2:y,taskComment3:x,taskComment4:U})=>[s(b,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:l.supervisionDeptName,deptOptions:f.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!n},{default:m(()=>[e("div",z,[e("div",B,[t[0]||(t[0]=e("span",null,"现决定对本合同项目作如下变更或调整，贵方应根据本指示于",-1)),e("span",F,d(l.field1),1),t[1]||(t[1]=e("span",null,"年",-1)),e("span",A,d(l.field2),1),t[2]||(t[2]=e("span",null,"月",-1)),e("span",O,d(l.field3),1),t[3]||(t[3]=e("span",null,"日前提交相应的",-1)),t[4]||(t[4]=e("span",null,"施工措施计划和变更",-1)),t[5]||(t[5]=e("span",null,"报价。",-1))])]),e("div",k,[t[6]||(t[6]=e("div",null,"变更项目名称：",-1)),e("div",g,[s(a,{modelValue:l.field5,"onUpdate:modelValue":o=>l.field5=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!n},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",I,[t[7]||(t[7]=e("div",null,"变更内容简述：",-1)),e("div",W,[s(a,{modelValue:l.field6,"onUpdate:modelValue":o=>l.field6=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!n},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",J,[t[8]||(t[8]=e("div",null,"变更工程量估计：",-1)),e("div",Q,[s(a,{modelValue:l.field7,"onUpdate:modelValue":o=>l.field7=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!n},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",R,[t[9]||(t[9]=e("div",null,"变更技术要求：",-1)),e("div",X,[s(a,{modelValue:l.field8,"onUpdate:modelValue":o=>l.field8=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!n},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",Y,[t[10]||(t[10]=e("div",null,"变更进度要求：",-1)),e("div",j,[s(a,{modelValue:l.field9,"onUpdate:modelValue":o=>l.field9=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!n},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),e("div",q,[t[11]||(t[11]=e("div",null,"附件：",-1)),s(a,{modelValue:l.attachmentDesc,"onUpdate:modelValue":o=>l.attachmentDesc=o,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!n},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),s(b,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:l.epcDeptName,deptOptions:f.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!n},{default:m(()=>t[12]||(t[12]=[e("div",{class:"comment-wp"},[e("div",{style:{height:"30px"}})],-1)])),_:2,__:[12]},1032,["deptValue","deptOptions","disabled"])]),footer:m(({formData:l,formTable:_,baseObj:f,uploadAccept:w,taskStart:n,taskComment2:y,taskComment3:x,taskComment4:U})=>[e("div",D,[t[13]||(t[13]=e("span",null,"说明：本表一式",-1)),e("span",E,d(l.num1),1),t[14]||(t[14]=e("span",null,"份，由监理机构填写，承包人签收后，承包人",-1)),e("span",G,d(l.num2),1),t[15]||(t[15]=e("span",null,"份，监理机构",-1)),e("span",H,d(l.num3),1),t[16]||(t[16]=e("span",null,"份，发包人",-1)),e("span",K,d(l.num4),1),t[17]||(t[17]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ue=C(L,[["render",M]]);export{ue as default};
