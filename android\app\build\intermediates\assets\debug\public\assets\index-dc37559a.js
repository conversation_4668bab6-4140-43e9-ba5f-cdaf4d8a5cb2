import{F as T,D as C}from"./index-a831f9da.js";import{_ as B}from"./index-4829f8e2.js";import{Q as r,R as F,X as S,V as e,U as l,Y as n,k as d,B as v}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const A={name:"JL26",components:{FormTemplate:T,DocumentPart:C},emits:[],props:{},setup(p,{attrs:s,slots:c,emit:a}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:p,detailParamList:s}){},onBeforeSubmit({formData:p,detailParamList:s},c){return new Promise((a,i)=>{try{a()}catch(o){i(o)}})}}},P={class:"jl-table"},V={colspan:"3"},j={class:"cell"},L={class:"cell"},N={class:"cell"},g={class:"cell"},I={class:"cell"},O={colspan:"5"},J={class:"cell"},Q={class:"one-line"},R={class:"one-line"},U={class:"one-line"},X={class:"cell"},Y={class:"one-line"},q={colspan:"2"},z={class:"cell"},E={class:"one-line"},G={class:"cell"},H={class:"one-line"},K={colspan:"2"},M={class:"cell"},W={class:"one-line"},Z={class:"cell"},$={class:"one-line"},D={colspan:"2"},ll={class:"cell"},sl={class:"one-line"},tl={colspan:"5"},nl={class:"cell"},el={colspan:"5"},il={class:"cell"},ol={colspan:"5"},dl={class:"cell"},pl={colspan:"5"},al={class:"cell"},ul={colspan:"5"},rl={class:"cell"},cl={colspan:"5"},vl={class:"cell"},ml={colspan:"6"},_l={class:"cell"},fl={class:"one-line"};function hl(p,s,c,a,i,o){const u=r("van-col"),m=r("van-row"),_=r("FormTemplate");return F(),S(_,{ref:"FormTemplate",nature:"旁站","on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":i.detailTable,"is-show-confirm1":!1,"show-target":!1,"hide-border":"",attachmentDesc:i.attachmentDesc},{default:e(({formData:t,formTable:f,baseObj:h,uploadAccept:b,taskStart:w,taskComment2:x,taskComment3:y,taskComment4:k})=>[l("table",P,[l("tbody",null,[l("tr",null,[s[0]||(s[0]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"工程部位")],-1)),l("td",V,[l("div",j,n(t.field1),1)]),s[1]||(s[1]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"日期")],-1)),l("td",null,[l("div",L,n(t.field2),1)])]),l("tr",null,[s[2]||(s[2]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"时 间")],-1)),l("td",null,[l("div",N,n(t.field3),1)]),s[3]||(s[3]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"天气")],-1)),l("td",null,[l("div",g,n(t.field4),1)]),s[4]||(s[4]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"温度")],-1)),l("td",null,[l("div",I,n(t.field5),1)])]),l("tr",null,[s[8]||(s[8]=l("th",{style:{width:"80px"},rowspan:"5"},[l("div",{class:"cell"},"人员情况")],-1)),l("td",O,[l("div",J,[d(m,null,{default:e(()=>[d(u,{span:12},{default:e(()=>[l("div",Q,[s[5]||(s[5]=l("span",null,"施工技术员：",-1)),l("span",null,n(t.field6),1)])]),_:2},1024),d(u,{span:12},{default:e(()=>[l("div",R,[s[6]||(s[6]=l("span",null,"施工班组长：",-1)),l("span",null,n(t.field7),1)])]),_:2},1024),d(u,{span:12},{default:e(()=>[l("div",U,[s[7]||(s[7]=l("span",null,"质检员：",-1)),l("span",null,n(t.field8),1)])]),_:2},1024)]),_:2},1024)])])]),s[29]||(s[29]=l("tr",null,[l("th",{colspan:"5"},[l("div",{class:"cell"},"现场人员数量及分类人员数量")])],-1)),l("tr",null,[s[11]||(s[11]=l("th",null,[l("div",{class:"cell"},"管理人员")],-1)),l("td",null,[l("div",X,[l("div",Y,[v(n(t.field9)+" ",1),s[9]||(s[9]=l("span",null,"人",-1))])])]),s[12]||(s[12]=l("th",null,[l("div",{class:"cell"},"技术人员")],-1)),l("td",q,[l("div",z,[l("div",E,[v(n(t.field10)+" ",1),s[10]||(s[10]=l("span",null,"人",-1))])])])]),l("tr",null,[s[15]||(s[15]=l("th",null,[l("div",{class:"cell"},"特种作业人员")],-1)),l("td",null,[l("div",G,[l("div",H,[l("span",null,n(t.field11),1),s[13]||(s[13]=l("span",null,"人",-1))])])]),s[16]||(s[16]=l("th",null,[l("div",{class:"cell"},"普通作业人员")],-1)),l("td",K,[l("div",M,[l("div",W,[l("span",null,n(t.field12),1),s[14]||(s[14]=l("span",null,"人",-1))])])])]),l("tr",null,[s[19]||(s[19]=l("th",null,[l("div",{class:"cell"},"其他辅助人员")],-1)),l("td",null,[l("div",Z,[l("div",$,[l("span",null,n(t.field13),1),s[17]||(s[17]=l("span",null,"人",-1))])])]),s[20]||(s[20]=l("th",null,[l("div",{class:"cell"},"合计")],-1)),l("td",D,[l("div",ll,[l("div",sl,[l("span",null,n(t.field14),1),s[18]||(s[18]=l("span",null,"人",-1))])])])]),l("tr",null,[s[21]||(s[21]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"主要施工设备及运转情况")],-1)),l("td",tl,[l("div",nl,[l("span",null,n(t.field15),1)])])]),l("tr",null,[s[22]||(s[22]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"主要材料使用情况")],-1)),l("td",el,[l("div",il,[l("span",null,n(t.field16),1)])])]),l("tr",null,[s[23]||(s[23]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"施工过程描述")],-1)),l("td",ol,[l("div",dl,[l("span",null,n(t.field17),1)])])]),l("tr",null,[s[24]||(s[24]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"监理现场检查、检测情况")],-1)),l("td",pl,[l("div",al,[l("span",null,n(t.field18),1)])])]),l("tr",null,[s[25]||(s[25]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"承包人提出的问题")],-1)),l("td",ul,[l("div",rl,[l("span",null,n(t.field19),1)])])]),l("tr",null,[s[26]||(s[26]=l("th",{style:{width:"80px"}},[l("div",{class:"cell"},"监理人答复或指示")],-1)),l("td",cl,[l("div",vl,[l("span",null,n(t.field20),1)])])]),l("tr",null,[l("td",ml,[l("div",_l,[l("div",fl,[s[27]||(s[27]=l("span",null,"当班监理员：（签名）",-1)),l("span",null,n(t.field21),1),s[28]||(s[28]=l("span",null,"施工技术员：（签名）",-1)),l("span",null,n(t.field22),1)])])])])])])]),footer:e(({formData:t,formTable:f,baseObj:h,uploadAccept:b,taskStart:w,taskComment2:x,taskComment3:y,taskComment4:k})=>s[30]||(s[30]=[l("div",{class:"footer-input"},[l("span",null,"说明：本表单独汇编成册。")],-1)])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const Nl=B(A,[["render",hl]]);export{Nl as default};
