import{F as x,D as N}from"./index-1be3ad72.js";import{_ as F}from"./index-4829f8e2.js";import{Q as f,R as _,X as $,V as p,k as a,U as t,Y as l}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const U={name:"CB23",components:{FormTemplate:x,DocumentPart:N},emits:[],props:{},setup(n,{attrs:e,slots:c,emit:b}){},data(){return{detailTable:[],attachmentDesc:"1、停工因素消除情况说明。\n2、复工条件情况说明。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:n,detailParamList:e}){},onBeforeSubmit({formData:n,detailParamList:e,taskStart:c},b){return new Promise((m,o)=>{try{if(b==="submit"&&c){if(!n.check1&&!n.check2)return this.$showNotify({type:"danger",message:"请完善依据信息!",duration:3*1e3}),o(!1),!1;if(n.check1&&(!n.field1||!n.field2))return this.$showNotify({type:"danger",message:"请完善依据信息!",duration:3*1e3}),o(!1),!1;if(n.check2&&(!n.field3||!n.field4))return this.$showNotify({type:"danger",message:"请完善依据信息!",duration:3*1e3}),o(!1),!1}m()}catch(r){o(r)}})},changeCheck1(n){n&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.field3="",this.$refs.FormTemplate.formData.field4="",this.$nextTick(()=>{var e;(e=this.$refs.FormTemplate.$refs.form)==null||e.resetValidation()}))},changeCheck2(n){n&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.field1="",this.$refs.FormTemplate.formData.field2="",this.$nextTick(()=>{var e;(e=this.$refs.FormTemplate.$refs.form)==null||e.resetValidation()}))}}},P={class:"one-line"},L={class:"form-info"},B={class:"check-wp"},O={class:"form-info"},A={class:"form-info"},z={class:"check-wp"},W={class:"form-info"},q={class:"form-info"},I={class:"form-info"},E={class:"form-info"},Q={class:"form-info"},R={class:"form-info"},S={class:"attachment-desc"},X={class:"comment-wp"},Y={class:"textarea-wp"},G={class:"comment-wp"},H={class:"textarea-wp"},J={class:"footer-input"},K={class:"form-info"},M={class:"form-info"},Z={class:"form-info"},j={class:"form-info"};function D(n,e,c,b,m,o){const r=f("van-checkbox"),V=f("van-field"),h=f("DocumentPart"),y=f("FormTemplate");return _(),$(y,{ref:"FormTemplate",nature:"复工","on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:m.attachmentDesc},{default:p(({formData:s,formTable:C,baseObj:u,uploadAccept:T,taskStart:d,taskComment2:k,taskComment3:v,taskComment4:w,taskComment5:g})=>[a(h,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:s.constructionDeptName,deptOptions:u.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!d},{default:p(()=>[t("div",P,[t("span",L,l(s.projectName),1),e[0]||(e[0]=t("span",null,"工程项目",-1)),e[1]||(e[1]=t("span",null,"依据",-1)),t("div",B,[a(r,{modelValue:s.check1,"onUpdate:modelValue":i=>s.check1=i,shape:"square",disabled:!0,onChange:o.changeCheck1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e[2]||(e[2]=t("span",null,"暂停施工指示",-1)),e[3]||(e[3]=t("span",null,"监理[",-1)),t("span",O,l(s.field1),1),e[4]||(e[4]=t("span",null,"]",-1)),e[5]||(e[5]=t("span",null,"停工",-1)),t("span",A,l(s.field2),1),e[6]||(e[6]=t("span",null,"号）",-1)),e[7]||(e[7]=t("span",null,"/",-1)),t("div",z,[a(r,{modelValue:s.check2,"onUpdate:modelValue":i=>s.check2=i,shape:"square",disabled:!0,onChange:o.changeCheck2},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e[8]||(e[8]=t("span",null,"批准的暂停施工报审表",-1)),e[9]||(e[9]=t("span",null,"（承包[",-1)),t("span",W,l(s.field3),1),e[10]||(e[10]=t("span",null,"]",-1)),e[11]||(e[11]=t("span",null,"暂停",-1)),t("span",q,l(s.field4),1),e[12]||(e[12]=t("span",null,"号）",-1)),e[13]||(e[13]=t("span",null,"后，已于",-1)),t("span",I,l(s.field5),1),e[14]||(e[14]=t("span",null,"年",-1)),t("span",E,l(s.field6),1),e[15]||(e[15]=t("span",null,"月",-1)),t("span",Q,l(s.field7),1),e[16]||(e[16]=t("span",null,"日",-1)),t("span",R,l(s.field8),1),e[17]||(e[17]=t("span",null,"时",-1)),e[18]||(e[18]=t("span",null,"暂停施工。",-1))]),e[20]||(e[20]=t("div",{style:{"margin-top":"10px"}}," 鉴于致使该工程的停工因素已经消除，复工准备工作已就绪，特申请复工，请贵方审批。 ",-1)),t("div",S,[e[19]||(e[19]=t("div",null,"附件：",-1)),a(V,{modelValue:s.attachmentDesc,"onUpdate:modelValue":i=>s.attachmentDesc=i,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2,__:[20]},1032,["deptValue","deptOptions","disabled"]),a(h,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:s.epcDeptName,deptOptions:u.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!d},{default:p(()=>[t("div",X,[e[21]||(e[21]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",Y,[a(V,{modelValue:s.comment2,"onUpdate:modelValue":i=>s.comment2=i,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!k},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),a(h,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:u.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!d},{default:p(()=>[t("div",G,[e[22]||(e[22]=t("div",null,"审批意见：",-1)),t("div",H,[a(V,{modelValue:s.comment3,"onUpdate:modelValue":i=>s.comment3=i,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:s,formTable:C,baseObj:u,uploadAccept:T,taskStart:d,taskComment2:k,taskComment3:v,taskComment4:w,taskComment5:g})=>[t("div",J,[e[23]||(e[23]=t("span",null,"说明：本表一式",-1)),t("span",K,l(s.num1),1),e[24]||(e[24]=t("span",null,"份，由承包人填写，报送监理机构审批后，随同审批意见发包人",-1)),t("span",M,l(s.num2),1),e[25]||(e[25]=t("span",null,"份，监理机构",-1)),t("span",Z,l(s.num3),1),e[26]||(e[26]=t("span",null,"份，承包人",-1)),t("span",j,l(s.num4),1),e[27]||(e[27]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const be=F(U,[["render",D]]);export{be as default};
