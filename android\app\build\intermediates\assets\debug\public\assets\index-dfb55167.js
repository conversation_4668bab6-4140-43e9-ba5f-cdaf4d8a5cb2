import{F as N,D as A}from"./index-1be3ad72.js";import{U as P}from"./index-fc22947f.js";import{_ as D}from"./index-4829f8e2.js";import{Q as p,R as $,X as L,V as s,U as t,Y as r,k as o,B as V}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";const B={name:"CB18",components:{FormTemplate:N,DocumentPart:A,UploadFiles:P},emits:[],props:{},setup(n,{attrs:e,slots:c,emit:u}){},data(){return{detailTable:[],attachmentDesc:"施工质量评定表\n施工质量检查、检测记录"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:n,detailParamList:e}){},onBeforeSubmit({formData:n,detailParamList:e,taskComment3:c},u){return new Promise((f,d)=>{try{if(u=="submit"&&c&&!n.check1&&!n.check2&&!n.check3&&!n.check4)return this.$showNotify({type:"danger",message:"请选复核结果!",duration:3*1e3}),d(!1),!1;f()}catch(h){d(h)}})},onAfterSubmit(n,e){return new Promise(async(c,u)=>{try{this.$refs.contentAttachment&&await this.$refs.contentAttachment.update(),c()}catch(f){u()}})},changeCheck1(n){n&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.check3=!1,this.$refs.FormTemplate.formData.check4=!1)},changeCheck2(n){n&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check3=!1,this.$refs.FormTemplate.formData.check4=!1)},changeCheck3(n){n&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.check4=!1)},changeCheck4(n){n&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.check3=!1)}}},O={class:"one-line",style:{"justify-content":"center",padding:"0 30px"}},q={class:"one-line"},S={class:"form-info"},z={class:"attachment-desc"},I={class:"comment-wp"},W={class:"textarea-wp"},E={class:"comment-wp"},Q={class:"check-wp"},R={class:"check-wp"},X={class:"check-wp"},Y={class:"check-wp"},j={class:"footer-input"},G={class:"form-info"},H={class:"form-info"},J={class:"form-info"},K={class:"form-info"};function M(n,e,c,u,f,d){const h=p("van-field"),v=p("DocumentPart"),b=p("van-checkbox"),k=p("van-col"),F=p("van-row"),w=p("UploadFiles"),U=p("FormTemplate");return $(),L(U,{ref:"FormTemplate",nature:"质报","on-after-init":d.onAfterInit,"on-before-submit":d.onBeforeSubmit,"on-after-submit":d.onAfterSubmit,"detail-table":f.detailTable,"is-show-confirm1":!1,"title-fill":!0,nums:[6,1,1,4],attachmentDesc:f.attachmentDesc},{title:s(({formData:a,formTable:T,baseObj:_,uploadAccept:g,taskStart:m,taskComment2:C,taskComment3:i,taskComment4:y})=>[t("div",O,[t("span",null,r(a.field1),1),e[0]||(e[0]=t("span",null,"工程施工质量报验单",-1))])]),default:s(({formData:a,formTable:T,baseObj:_,uploadAccept:g,taskStart:m,taskComment2:C,taskComment3:i,taskComment4:y,taskComment5:x})=>[o(v,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:a.constructionDeptName,deptOptions:_.constructionDeptName,personLabel:"质检负责人：",labelWidth:"10em",disabled:!m},{default:s(()=>[t("div",q,[t("span",S,r(a.field1),1),e[1]||(e[1]=t("span",null,"工程已按合同要求完成施工，",-1)),e[2]||(e[2]=t("span",null,"经自检合格，",-1)),e[3]||(e[3]=t("span",null,"报请贵方核验。",-1))]),t("div",z,[e[4]||(e[4]=t("div",null,"附件：",-1)),o(h,{modelValue:a.attachmentDesc,"onUpdate:modelValue":l=>a.attachmentDesc=l,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!m},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),o(v,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:a.epcDeptName,deptOptions:_.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!m},{default:s(()=>[t("div",I,[e[5]||(e[5]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",W,[o(h,{modelValue:a.comment2,"onUpdate:modelValue":l=>a.comment2=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),o(v,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:a.supervisionDeptName,deptOptions:_.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!m},{default:s(()=>[t("div",E,[e[12]||(e[12]=t("div",null,"监理机构意见：",-1)),t("div",null,[e[10]||(e[10]=t("div",{style:{margin:"10px 0"}},"复核结果：",-1)),o(F,{gutter:0},{default:s(()=>[o(k,{span:24},{default:s(()=>[t("div",Q,[o(b,{modelValue:a.check1,"onUpdate:modelValue":l=>a.check1=l,shape:"square",disabled:!i,onChange:d.changeCheck1},{default:s(()=>e[6]||(e[6]=[V(" 同意进入下一工序 ")])),_:2,__:[6]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),_:2},1024),o(k,{span:24},{default:s(()=>[t("div",R,[o(b,{modelValue:a.check2,"onUpdate:modelValue":l=>a.check2=l,shape:"square",disabled:!i,onChange:d.changeCheck2},{default:s(()=>e[7]||(e[7]=[V(" 不同意进入下一工序 ")])),_:2,__:[7]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),_:2},1024),o(k,{span:24},{default:s(()=>[t("div",X,[o(b,{modelValue:a.check3,"onUpdate:modelValue":l=>a.check3=l,shape:"square",disabled:!i,onChange:d.changeCheck3},{default:s(()=>e[8]||(e[8]=[V(" 同意进入下一单元工程 ")])),_:2,__:[8]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),_:2},1024),o(k,{span:24},{default:s(()=>[t("div",Y,[o(b,{modelValue:a.check4,"onUpdate:modelValue":l=>a.check4=l,shape:"square",disabled:!i,onChange:d.changeCheck4},{default:s(()=>e[9]||(e[9]=[V(" 不同意进入下一单元工程 ")])),_:2,__:[9]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),_:2},1024)]),_:2},1024),e[11]||(e[11]=t("div",{style:{margin:"10px 0"}},"附件：监理复核支持材料。",-1)),o(h,{label:"","label-align":"top","input-align":"left"},{input:s(()=>[o(w,{ref:"contentAttachment",g9s:a.contentAttachment,"onUpdate:g9s":l=>a.contentAttachment=l,accept:g,multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:!i},null,8,["g9s","onUpdate:g9s","accept","readonly"])]),_:2},1024)])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:s(({formData:a,formTable:T,baseObj:_,uploadAccept:g,taskStart:m,taskComment2:C,taskComment3:i,taskComment4:y,taskComment5:x})=>[t("div",j,[e[13]||(e[13]=t("span",null,"说明：本表一式",-1)),t("span",G,r(a.num1),1),e[14]||(e[14]=t("span",null,"份，由承包人填写，监理机构审签后，发包人",-1)),t("span",H,r(a.num2),1),e[15]||(e[15]=t("span",null,"份，监理机构",-1)),t("span",J,r(a.num3),1),e[16]||(e[16]=t("span",null,"份，承包人",-1)),t("span",K,r(a.num4),1),e[17]||(e[17]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","on-after-submit","detail-table","attachmentDesc"])}const fe=D(B,[["render",M],["__scopeId","data-v-e7748b53"]]);export{fe as default};
