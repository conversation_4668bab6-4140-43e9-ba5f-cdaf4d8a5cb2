import{F as D,D as T}from"./index-a831f9da.js";import{_ as C}from"./index-4829f8e2.js";import{Q as f,R as N,X as P,V as d,k as s,U as t,Y as m}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const L={name:"JL06",components:{FormTemplate:D,DocumentPart:T},emits:[],props:{},setup(i,{attrs:e,slots:b,emit:r}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:i,detailParamList:e}){},onBeforeSubmit({formData:i,detailParamList:e},b){return new Promise((r,a)=>{try{r()}catch(p){a(p)}})}}},U={class:"comment-wp"},k={class:"textarea-wp"},B={class:"comment-wp"},F={class:"textarea-wp"},A={class:"attachment-desc"},O={class:"footer-input"},z={class:"form-info"},g={class:"form-info"},I={class:"form-info"},W={class:"form-info"};function J(i,e,b,r,a,p){const u=f("van-field"),_=f("DocumentPart"),V=f("FormTemplate");return N(),P(V,{ref:"FormTemplate",nature:"通知","on-after-init":p.onAfterInit,"on-before-submit":p.onBeforeSubmit,"detail-table":a.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:a.attachmentDesc},{default:d(({formData:o,formTable:v,baseObj:c,uploadAccept:h,taskStart:l,taskComment2:w,taskComment3:y,taskComment4:x})=>[s(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:c.supervisionDeptName,personLabel:"总监理工程师/监理工程师：",labelWidth:"10em",disabled:!l},{default:d(()=>[t("div",U,[e[0]||(e[0]=t("div",null,"事由：",-1)),t("div",k,[s(u,{modelValue:o.field1,"onUpdate:modelValue":n=>o.field1=n,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),t("div",B,[e[1]||(e[1]=t("div",null,"通知内容：",-1)),t("div",F,[s(u,{modelValue:o.field2,"onUpdate:modelValue":n=>o.field2=n,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),t("div",A,[e[2]||(e[2]=t("div",null,"附件：",-1)),s(u,{modelValue:o.attachmentDesc,"onUpdate:modelValue":n=>o.attachmentDesc=n,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),s(_,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:c.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!l},{default:d(()=>e[3]||(e[3]=[t("div",{class:"comment-wp"},[t("div",{style:{height:"30px"}})],-1)])),_:2,__:[3]},1032,["deptValue","deptOptions","disabled"])]),footer:d(({formData:o,formTable:v,baseObj:c,uploadAccept:h,taskStart:l,taskComment2:w,taskComment3:y,taskComment4:x})=>[t("div",O,[e[4]||(e[4]=t("span",null,"说明：本通知一式",-1)),t("span",z,m(o.num1),1),e[5]||(e[5]=t("span",null,"份，由监理机构填写，承包人",-1)),t("span",g,m(o.num2),1),e[6]||(e[6]=t("span",null,"份，监理机构",-1)),t("span",I,m(o.num3),1),e[7]||(e[7]=t("span",null,"份，发包人",-1)),t("span",W,m(o.num4),1),e[8]||(e[8]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const te=C(L,[["render",J]]);export{te as default};
