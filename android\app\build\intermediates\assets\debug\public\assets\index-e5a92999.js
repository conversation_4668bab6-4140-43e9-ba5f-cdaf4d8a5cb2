import{F as y,D as N}from"./index-1be3ad72.js";import{_ as x}from"./index-4829f8e2.js";import{Q as f,R as F,X as $,V as p,k as i,U as t,Y as l}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const P={name:"CB29",components:{FormTemplate:y,DocumentPart:N},emits:[],props:{},setup(n,{attrs:e,slots:c,emit:h}){},data(){return{detailTable:[],attachmentDesc:"索赔报告，主要内容包括：\n1、索赔事件简述及索赔要求。\n2、索赔依据。\n3、索赔计算。\n4、索赔证明材料。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:n,detailParamList:e}){},onBeforeSubmit({formData:n,detailParamList:e,taskStart:c},h){return new Promise((m,o)=>{try{if(h==="submit"&&c){if(!n.check1&&!n.check2)return this.$showNotify({type:"danger",message:"请完善索赔信息!",duration:3*1e3}),o(!1),!1;if(n.check1&&(!n.field2||!n.field3))return this.$showNotify({type:"danger",message:"请完善索赔信息!",duration:3*1e3}),o(!1),!1;if(n.check2&&!n.field4)return this.$showNotify({type:"danger",message:"请完善索赔信息!",duration:3*1e3}),o(!1),!1}m()}catch(r){o(r)}})},changeCheck1(n){n&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.field4="",this.$nextTick(()=>{var e;(e=this.$refs.FormTemplate.$refs.form)==null||e.resetValidation()}))},changeCheck2(n){n&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.field2="",this.$refs.FormTemplate.formData.field3="",this.$nextTick(()=>{var e;(e=this.$refs.FormTemplate.$refs.form)==null||e.resetValidation()}))}}},U={class:"one-line"},L={class:"form-info"},B={class:"check-wp"},O={class:"form-info"},A={class:"form-info"},D={class:"check-wp"},W={class:"form-info"},q={class:"attachment-desc"},z={class:"comment-wp"},I={class:"textarea-wp"},E={class:"footer-input"},Q={class:"form-info"},R={class:"form-info"},S={class:"form-info"},X={class:"form-info"};function Y(n,e,c,h,m,o){const r=f("van-checkbox"),k=f("van-field"),b=f("DocumentPart"),v=f("FormTemplate");return F(),$(v,{ref:"FormTemplate",nature:"赔报","on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:m.attachmentDesc},{default:p(({formData:s,formTable:C,baseObj:u,uploadAccept:_,taskStart:d,taskComment2:V,taskComment3:T,taskComment4:g,taskComment5:w})=>[i(b,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:s.constructionDeptName,deptOptions:u.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!d},{default:p(()=>[t("div",U,[e[0]||(e[0]=t("span",{style:{"padding-left":"2em"}},"根据有关规定和施工合同的约定，我方对",-1)),t("span",L,l(s.field1),1),e[1]||(e[1]=t("span",null,"事件，申请",-1)),t("div",B,[i(r,{modelValue:s.check1,"onUpdate:modelValue":a=>s.check1=a,shape:"square",disabled:!0,onChange:o.changeCheck1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e[2]||(e[2]=t("span",null,"赔偿金额为",-1)),e[3]||(e[3]=t("span",null,"（大写）",-1)),t("span",O,l(s.field2),1),e[4]||(e[4]=t("span",null,"元（小写",-1)),t("span",A,l(s.field3),1),e[5]||(e[5]=t("span",null,"元）",-1)),e[6]||(e[6]=t("span",null,"/",-1)),t("div",D,[i(r,{modelValue:s.check2,"onUpdate:modelValue":a=>s.check2=a,shape:"square",disabled:!0,onChange:o.changeCheck2},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e[7]||(e[7]=t("span",null,"索赔工期",-1)),t("span",W,l(s.field4||""),1),e[8]||(e[8]=t("span",null,"天，",-1)),e[9]||(e[9]=t("span",null,"请贵方审核。",-1))]),t("div",q,[e[10]||(e[10]=t("div",null,"附件：",-1)),i(k,{modelValue:s.attachmentDesc,"onUpdate:modelValue":a=>s.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),i(b,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:s.epcDeptName,deptOptions:u.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!d},{default:p(()=>[t("div",z,[e[11]||(e[11]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",I,[i(k,{modelValue:s.comment2,"onUpdate:modelValue":a=>s.comment2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!V},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),i(b,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:u.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!d},{default:p(()=>e[12]||(e[12]=[t("div",{class:"comment-wp"},[t("div",null,"监理机构将另行签发审核意见。")],-1)])),_:2,__:[12]},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:s,formTable:C,baseObj:u,uploadAccept:_,taskStart:d,taskComment2:V,taskComment3:T,taskComment4:g,taskComment5:w})=>[t("div",E,[e[13]||(e[13]=t("span",null,"注：本表一式",-1)),t("span",Q,l(s.num1),1),e[14]||(e[14]=t("span",null,"份，由承包人填写，监理机构审核后，随同批复意见发包人",-1)),t("span",R,l(s.num2),1),e[15]||(e[15]=t("span",null,"份，监理机构",-1)),t("span",S,l(s.num3),1),e[16]||(e[16]=t("span",null,"份，承包人",-1)),t("span",X,l(s.num4),1),e[17]||(e[17]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const de=x(P,[["render",Y]]);export{de as default};
