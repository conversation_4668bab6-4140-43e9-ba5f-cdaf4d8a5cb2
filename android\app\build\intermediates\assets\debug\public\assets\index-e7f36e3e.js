import{F as h,D as C}from"./index-a831f9da.js";import{_ as N}from"./index-4829f8e2.js";import{Q as f,R as P,X as D,V as p,k as n,U as t,Y as i}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const L={name:"JL10",components:{FormTemplate:h,DocumentPart:C},emits:[],props:{},setup(m,{attrs:e,slots:b,emit:r}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:m,detailParamList:e}){},onBeforeSubmit({formData:m,detailParamList:e},b){return new Promise((r,a)=>{try{r()}catch(d){a(d)}})}}},U={class:"comment-wp"},k={class:"textarea-wp"},B={class:"comment-wp"},F={class:"textarea-wp"},A={class:"comment-wp"},O={class:"textarea-wp"},z={class:"footer-input"},g={class:"form-info"},I={class:"form-info"},W={class:"form-info"},J={class:"form-info"};function Q(m,e,b,r,a,d){const u=f("van-field"),_=f("DocumentPart"),v=f("FormTemplate");return P(),D(v,{ref:"FormTemplate",nature:"警告","on-after-init":d.onAfterInit,"on-before-submit":d.onBeforeSubmit,"detail-table":a.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:a.attachmentDesc},{default:p(({formData:o,formTable:V,baseObj:c,uploadAccept:w,taskStart:l,taskComment2:y,taskComment3:x,taskComment4:T})=>[n(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:c.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!l},{default:p(()=>[e[3]||(e[3]=t("div",{class:"comment-wp"},[t("div",{style:{"text-indent":"2em"}}," 鉴于你方在履行合同时发生了下列所述的违约行为，依据合同约定，特发此警告通知。你方应立即采取措施，纠正违约行为后报我方确认。 ")],-1)),t("div",U,[e[0]||(e[0]=t("div",null,"违约行为情况描述：",-1)),t("div",k,[n(u,{modelValue:o.field1,"onUpdate:modelValue":s=>o.field1=s,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),t("div",B,[e[1]||(e[1]=t("div",null,"合同的相关规定：",-1)),t("div",F,[n(u,{modelValue:o.field2,"onUpdate:modelValue":s=>o.field2=s,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),t("div",A,[e[2]||(e[2]=t("div",null,"监理机构要求：",-1)),t("div",O,[n(u,{modelValue:o.field3,"onUpdate:modelValue":s=>o.field3=s,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2,__:[3]},1032,["deptValue","deptOptions","disabled"]),n(_,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:c.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!l},{default:p(()=>e[4]||(e[4]=[t("div",{class:"comment-wp"},[t("div",{style:{height:"30px"}})],-1)])),_:2,__:[4]},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:o,formTable:V,baseObj:c,uploadAccept:w,taskStart:l,taskComment2:y,taskComment3:x,taskComment4:T})=>[t("div",z,[e[5]||(e[5]=t("span",null,"说明：本表一式",-1)),t("span",g,i(o.num1),1),e[6]||(e[6]=t("span",null,"份，由监理机构填写，承包人签收后，承包人",-1)),t("span",I,i(o.num2),1),e[7]||(e[7]=t("span",null,"份，监理机构",-1)),t("span",W,i(o.num3),1),e[8]||(e[8]=t("span",null,"份，发包人",-1)),t("span",J,i(o.num4),1),e[9]||(e[9]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const oe=N(L,[["render",Q]]);export{oe as default};
