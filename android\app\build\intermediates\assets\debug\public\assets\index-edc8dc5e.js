import{F as T,D as F}from"./index-a831f9da.js";import{_ as B}from"./index-4829f8e2.js";import{Q as c,R as b,X as C,V as a,k as d,U as e,Y as l,S as w,W as D,F as h}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const U={name:"JL24",components:{FormTemplate:T,DocumentPart:F},emits:[],props:{},setup(i,{attrs:t,slots:_,emit:r}){},data(){return{detailTable:[{},{},{},{},{},{},{},{},{},{},{},{}],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:i,detailParamList:t}){},onBeforeSubmit({formData:i,detailParamList:t},_){return new Promise((r,o)=>{try{r()}catch(p){o(p)}})}}},g={class:"one-line"},A={class:"form-info"},O={class:"form-info"},S={class:"form-table"},I={class:"center"},W={class:"comment-wp"},E={class:"one-line2"},J={class:"footer-input"},Q={class:"form-info"},R={class:"form-info"},X={class:"form-info"},Y={class:"form-info"},j={class:"form-info"};function q(i,t,_,r,o,p){const V=c("DocumentPart"),v=c("van-field"),x=c("FormTemplate");return b(),C(x,{ref:"FormTemplate",nature:"图发","on-after-init":p.onAfterInit,"on-before-submit":p.onBeforeSubmit,"detail-table":o.detailTable,"is-show-confirm1":!1,attachmentDesc:o.attachmentDesc},{default:a(({formData:n,formTable:y,baseObj:m,uploadAccept:L,taskStart:u,taskComment2:N,taskComment3:f,taskComment4:P})=>[d(V,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:n.supervisionDeptName,deptOptions:m.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!u},{default:a(()=>[e("div",g,[t[0]||(t[0]=e("span",{style:{"padding-left":"2em"}},"本批签发下表所列施工图纸",-1)),e("span",A,l(n.field1),1),t[1]||(t[1]=e("span",null,"张，",-1)),t[2]||(t[2]=e("span",null,"其他设计文件",-1)),e("span",O,l(n.field2),1),t[3]||(t[3]=e("span",null,"份。",-1))]),e("div",S,[e("table",null,[t[4]||(t[4]=e("thead",null,[e("tr",null,[e("th",{colspan:"1",rowspan:"1"},"序号"),e("th",{colspan:"1",rowspan:"1"},"施工图纸/其他设计文件名称"),e("th",{colspan:"1",rowspan:"1"},"文图号"),e("th",{colspan:"1",rowspan:"1"},"份数"),e("th",{colspan:"1",rowspan:"1"},"备注")])],-1)),e("tbody",null,[(b(!0),w(h,null,D(y||[],(s,k)=>(b(),w("tr",{key:k},[e("td",I,l(k+1),1),e("td",null,l(s.field1),1),e("td",null,l(s.field2),1),e("td",null,l(s.field3),1),e("td",null,l(s.field4),1)]))),128))])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),d(V,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:n.epcDeptName,deptOptions:m.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!u},{default:a(()=>[e("div",W,[e("div",E,[t[5]||(t[5]=e("span",null,"今已收到经监理机构签发的施工图纸",-1)),d(v,{modelValue:n.field3,"onUpdate:modelValue":s=>n.field3=s,label:"",placeholder:"",readonly:!f,style:{width:"60px"}},null,8,["modelValue","onUpdate:modelValue","readonly"]),t[6]||(t[6]=e("span",null,"张，",-1)),t[7]||(t[7]=e("span",null,"其他设计文件",-1)),d(v,{modelValue:n.field4,"onUpdate:modelValue":s=>n.field4=s,label:"",placeholder:"",readonly:!f,style:{width:"60px"}},null,8,["modelValue","onUpdate:modelValue","readonly"]),t[8]||(t[8]=e("span",null,"份。",-1))])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:a(({formData:n,formTable:y,baseObj:m,uploadAccept:L,taskStart:u,taskComment2:N,taskComment3:f,taskComment4:P})=>[e("div",J,[t[9]||(t[9]=e("span",null,"说明：本表一式",-1)),e("span",Q,l(n.num1),1),t[10]||(t[10]=e("span",null,"份，由监理机构填写，发包人",-1)),e("span",R,l(n.num2),1),t[11]||(t[11]=e("span",null,"份，承包人",-1)),e("span",X,l(n.num3),1),t[12]||(t[12]=e("span",null,"份，设代机构",-1)),e("span",Y,l(n.num4),1),t[13]||(t[13]=e("span",null,"份，监理机构",-1)),e("span",j,l(n.num5),1),t[14]||(t[14]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const de=B(U,[["render",q],["__scopeId","data-v-41c82cc1"]]);export{de as default};
