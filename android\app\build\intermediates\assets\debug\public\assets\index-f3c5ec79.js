import{F as V}from"./index-8d635ba7.js";import{F as P}from"./FormItemPicker-d3f69283.js";import{F as w}from"./FormItemDate-ba00d9d5.js";import{F as k}from"./FormItemCalendar-905fde75.js";import{F as E}from"./FormItemPerson-bd0e3e57.js";import{F as S}from"./FormItemCoord-9e82e1bf.js";import{U as I}from"./index-fc22947f.js";import{_ as j}from"./index-4829f8e2.js";import{Q as n,R as u,X as p,V as i,k as o,Z as d,B as F}from"./verder-361ae6c7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./validate-2249584f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";const x={name:"UseSealApply",components:{FlowForm:V,FormItemPicker:P,FormItemDate:w,FormItemCalendar:k,FormItemPerson:E,FormItemCoord:S,UploadFiles:I},props:{},data(){var l,e;return{type:((l=this.$route.query)==null?void 0:l.type)||"",taskKey:((e=this.$route.query)==null?void 0:e.taskKey)||"",entityName:"TechnologySealApplication",formKey:"UseSealApply",modelKey:"seal_application",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/form/commit"},formData:{isconfirm1:!0,sealPaperName:"",sendingType:"",sealNum:"",reason:"",contentAttachment:"",createBy:"",userFullname:"",prjDepName:"",approverUsername1:"",approverFullname1:"",approverUnit1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",approverUsername3:"",approverFullname3:"",approverUnit3:""}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},canEdit0(){return this.type==="add"||this.taskKey==="UserTask_0"}},watch:{},created(){},mounted(){this.initForm()},methods:{async initForm(){this.type!=="add"&&this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,l=>{const{detailParamList:e=[],entityObject:f}=l;this.detailParamList=e,this.formData={...this.formData,...f}})})},async onSubmit(){var l;try{await this.$refs.form.validate();const e={...this.formData,taskSubject:(l=this.$route.query)==null?void 0:l.taskKey};this.$bizStore.saveData({SafetyHiddenDanger:{...e}}),this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},afterSubmit(l,e){this.updateFiles()},async updateFiles(){return new Promise(async(l,e)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),l()}catch(f){e()}})},handleSubProjectChange(l={}){this.formData.unitEngineeringId="",this.formData.unitEngineeringName="",this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""}}};function q(l,e,f,C,r,a){const m=n("van-field"),D=n("FormItemPicker"),v=n("UploadFiles"),s=n("FormItemPerson"),y=n("van-radio"),U=n("van-radio-group"),b=n("van-cell-group"),g=n("van-form"),N=n("FlowForm");return u(),p(N,{ref:"FlowForm","model-key":r.modelKey,"form-key":r.formKey,"entity-name":r.entityName,"detail-param-list":r.detailParamList,"detail-entity-name-list":r.detailEntityNameList,onSubmitClick:a.onSubmit,onAfterSubmit:a.afterSubmit},{default:i(()=>[o(g,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:i(()=>[o(b,{border:!1},{default:i(()=>[o(m,{modelValue:r.formData.sealPaperName,"onUpdate:modelValue":e[0]||(e[0]=t=>r.formData.sealPaperName=t),label:"用印文件名称",readonly:r.type==="view"||!a.canEdit0},null,8,["modelValue","readonly"]),o(m,{modelValue:r.formData.sendingType,"onUpdate:modelValue":e[1]||(e[1]=t=>r.formData.sendingType=t),label:"用印类型",readonly:""},null,8,["modelValue"]),r.formData.sealNum?(u(),p(m,{key:0,modelValue:r.formData.sealNum,"onUpdate:modelValue":e[2]||(e[2]=t=>r.formData.sealNum=t),label:"用印份数",readonly:r.type==="view"||!a.canEdit0},null,8,["modelValue","readonly"])):d("",!0),o(D,{label:"子工程",value:r.formData.subProjectId,"onUpdate:value":e[3]||(e[3]=t=>r.formData.subProjectId=t),text:r.formData.subProjectName,"onUpdate:text":e[4]||(e[4]=t=>r.formData.subProjectName=t),columns:[...a.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",rules:[{required:!0,message:"请选择子工程"}],readonly:a.portal.type!=1||r.type==="view"||!a.canEdit0,onChange:a.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),o(m,{label:"事由",modelValue:r.formData.reason,"onUpdate:modelValue":e[5]||(e[5]=t=>r.formData.reason=t),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入事由",rules:[{required:!0,message:"请输入事由"}],"input-align":"left",readonly:r.type==="view"||!a.canEdit0},null,8,["modelValue","readonly"]),o(m,{label:"附件","label-align":"top","input-align":"left"},{input:i(()=>[o(v,{ref:"beforeFiles",g9s:r.formData.contentAttachment,"onUpdate:g9s":e[6]||(e[6]=t=>r.formData.contentAttachment=t),accept:"image/*",multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:r.type==="view"||!a.canEdit0},null,8,["g9s","readonly"])]),_:1}),o(s,{label:"用印人",userFullname:r.formData.userFullname,"onUpdate:userFullname":e[7]||(e[7]=t=>r.formData.userFullname=t),userName:r.formData.createBy,"onUpdate:userName":e[8]||(e[8]=t=>r.formData.createBy=t),title:"选择用印人",rules:[{required:!0,message:"请选择用印人"}],readonly:""},null,8,["userFullname","userName"]),o(m,{modelValue:r.formData.prjDepName,"onUpdate:modelValue":e[9]||(e[9]=t=>r.formData.prjDepName=t),label:"用印人部门",readonly:""},null,8,["modelValue"]),o(m,{name:"radio",label:"是否需要部门负责人审批"},{input:i(()=>[o(U,{modelValue:r.formData.isconfirm1,"onUpdate:modelValue":e[10]||(e[10]=t=>r.formData.isconfirm1=t),direction:"horizontal",disabled:r.type==="view"||!a.canEdit0},{default:i(()=>[o(y,{name:!0},{default:i(()=>e[23]||(e[23]=[F("是")])),_:1,__:[23]}),o(y,{name:!1},{default:i(()=>e[24]||(e[24]=[F("否")])),_:1,__:[24]})]),_:1},8,["modelValue","disabled"])]),_:1}),r.formData.isconfirm1?(u(),p(s,{key:1,label:"部门负责人",userName:r.formData.approverUsername1,"onUpdate:userName":e[11]||(e[11]=t=>r.formData.approverUsername1=t),userFullname:r.formData.approverFullname1,"onUpdate:userFullname":e[12]||(e[12]=t=>r.formData.approverFullname1=t),deptName:r.formData.approverUnit1,"onUpdate:deptName":e[13]||(e[13]=t=>r.formData.approverUnit1=t),title:"选择部门负责人",rules:[{required:!0,message:"请选择部门负责人"}],readonly:r.type==="view"||!a.canEdit0},null,8,["userName","userFullname","deptName","readonly"])):d("",!0),r.formData.isconfirm1?(u(),p(m,{key:2,modelValue:r.formData.approverUnit1,"onUpdate:modelValue":e[14]||(e[14]=t=>r.formData.approverUnit1=t),label:"部门负责人部门",readonly:!0},null,8,["modelValue"])):d("",!0),o(s,{label:"项目负责人",userName:r.formData.approverUsername2,"onUpdate:userName":e[15]||(e[15]=t=>r.formData.approverUsername2=t),userFullname:r.formData.approverFullname2,"onUpdate:userFullname":e[16]||(e[16]=t=>r.formData.approverFullname2=t),deptName:r.formData.approverUnit2,"onUpdate:deptName":e[17]||(e[17]=t=>r.formData.approverUnit2=t),title:"选择项目负责人",rules:[{required:!0,message:"请选择项目负责人"}],readonly:r.type==="view"||!a.canEdit0},null,8,["userName","userFullname","deptName","readonly"]),o(m,{modelValue:r.formData.approverUnit2,"onUpdate:modelValue":e[18]||(e[18]=t=>r.formData.approverUnit2=t),label:"项目负责人部门",readonly:!0},null,8,["modelValue"]),o(s,{label:"经办人",userName:r.formData.approverFullname3,"onUpdate:userName":e[19]||(e[19]=t=>r.formData.approverFullname3=t),userFullname:r.formData.approverFullname3,"onUpdate:userFullname":e[20]||(e[20]=t=>r.formData.approverFullname3=t),deptName:r.formData.approverUnit3,"onUpdate:deptName":e[21]||(e[21]=t=>r.formData.approverUnit3=t),title:"选择经办人",rules:[{required:!0,message:"请选择经办人"}],readonly:r.type==="view"||!a.canEdit0},null,8,["userName","userFullname","deptName","readonly"]),o(m,{modelValue:r.formData.approverUnit3,"onUpdate:modelValue":e[22]||(e[22]=t=>r.formData.approverUnit3=t),label:"经办人部门",readonly:!0},null,8,["modelValue"])]),_:1})]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onSubmitClick","onAfterSubmit"])}const W=j(x,[["render",q]]);export{W as default};
