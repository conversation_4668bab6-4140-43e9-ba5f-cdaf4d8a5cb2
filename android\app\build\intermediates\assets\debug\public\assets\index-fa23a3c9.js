import{F as _,D as x}from"./index-1be3ad72.js";import{_ as y}from"./index-4829f8e2.js";import{Q as u,R as B,X as D,V as l,k as d,U as e,Y as s}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const F={name:"CB14",components:{FormTemplate:_,DocumentPart:x},emits:[],props:{},setup(r,{attrs:t,slots:f,emit:b}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:r,detailParamList:t}){},onBeforeSubmit({formData:r,detailParamList:t,taskComment3:f},b){return new Promise((p,i)=>{try{p()}catch(o){i(o)}})}}},O={class:"one-line"},A={class:"form-info"},S={class:"form-info"},U={class:"form-info"},W={class:"form-info"},g={class:"form-info"},I={class:"form-info"},z={class:"form-info"},E={class:"comment-wp"},Q={class:"textarea-wp"},R={class:"footer-input"},X={class:"form-info"},Y={class:"form-info"},c={class:"form-info"},j={class:"form-info"};function q(r,t,f,b,p,i){const o=u("DocumentPart"),N=u("van-field"),V=u("FormTemplate");return B(),D(V,{ref:"FormTemplate",nature:"合开工","on-after-init":i.onAfterInit,"on-before-submit":i.onBeforeSubmit,"detail-table":p.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:p.attachmentDesc},{default:l(({formData:n,formTable:C,baseObj:m,uploadAccept:k,taskStart:a,taskComment2:v,taskComment3:P,taskComment4:T,taskComment5:w})=>[d(o,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:n.constructionDeptName,deptOptions:m.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!a},{default:l(()=>[e("div",O,[t[0]||(t[0]=e("span",{style:{"padding-left":"2em"}},"我方承担的",-1)),e("span",A,s(n.projectName),1),t[1]||(t[1]=e("span",null,"工程，",-1)),t[2]||(t[2]=e("span",null,"于",-1)),e("span",S,s(n.field1),1),t[3]||(t[3]=e("span",null,"年",-1)),e("span",U,s(n.field2),1),t[4]||(t[4]=e("span",null,"月",-1)),e("span",W,s(n.field3),1),t[5]||(t[5]=e("span",null,"日",-1)),t[6]||(t[6]=e("span",null,"收到贵部",-1)),t[7]||(t[7]=e("span",null,"下发的合同工程开工通知",-1)),t[8]||(t[8]=e("span",null,"（",-1)),e("span",g,s(n.supervisionSimpleName),1),t[9]||(t[9]=e("span",null," [ ",-1)),e("span",I,s(n.field5),1),t[10]||(t[10]=e("span",null," ] ",-1)),t[11]||(t[11]=e("span",null,"开工",-1)),e("span",z,s(n.field6),1),t[12]||(t[12]=e("span",null,"号），",-1)),t[13]||(t[13]=e("span",null,"按照要求及时调遣了人员和施工设备、材料进场，开展了",-1)),t[14]||(t[14]=e("span",null,"各项准备工作。",-1))])]),_:2},1032,["deptValue","deptOptions","disabled"]),d(o,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:n.epcDeptName,deptOptions:m.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!a},{default:l(()=>[e("div",E,[t[15]||(t[15]=e("div",null,"EPC总承包项目部意见：",-1)),e("div",Q,[d(N,{modelValue:n.comment2,"onUpdate:modelValue":L=>n.comment2=L,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),d(o,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:n.supervisionDeptName,deptOptions:m.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!a},{default:l(()=>t[16]||(t[16]=[e("div",{class:"comment-wp"},[e("div",null,"审核后另行批复。")],-1)])),_:2,__:[16]},1032,["deptValue","deptOptions","disabled"])]),footer:l(({formData:n,formTable:C,baseObj:m,uploadAccept:k,taskStart:a,taskComment2:v,taskComment3:P,taskComment4:T,taskComment5:w})=>[e("div",R,[t[17]||(t[17]=e("span",null,"说明：本表一式",-1)),e("span",X,s(n.num1),1),t[18]||(t[18]=e("span",null,"份，由承包人填写，监理机构签收后，发包人",-1)),e("span",Y,s(n.num2),1),t[19]||(t[19]=e("span",null,"份，监理机构",-1)),e("span",c,s(n.num3),1),t[20]||(t[20]=e("span",null,"份，承包人",-1)),e("span",j,s(n.num4),1),t[21]||(t[21]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const it=y(F,[["render",q]]);export{it as default};
