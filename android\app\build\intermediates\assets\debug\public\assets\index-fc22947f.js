import{b as wi,d as Si,c as xi,e as gn,u as zt,i as pn,a as vn}from"./file-842bc27d.js";import{g as mn,a as yn}from"./file-2bef16be.js";import{m as Pi,k as _n,_ as It,o as Lt,t as Ii,v as Cn,C as kn,b as Tn,c as bn,h as En}from"./index-4829f8e2.js";import{B as Gt}from"./delegate-b245d146.js";import{Q as ne,R as U,X as be,V as Be,U as He,a4 as wn,S as K,F as Li,Z as ee,B as Sn,Y as Ye,y as xn,k as ke,W as Pn}from"./verder-361ae6c7.js";import{F as In,D as Kt,c as Ln,V as An,a as Dn,b as On}from"./index-889f88bd.js";import{c as Rn,d as Mn,e as Fn}from"./vant-91101745.js";function Yt(o,a){var r=Object.keys(o);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(o);a&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(o,e).enumerable})),r.push.apply(r,t)}return r}function V(o){for(var a=1;a<arguments.length;a++){var r=arguments[a]!=null?arguments[a]:{};a%2?Yt(Object(r),!0).forEach(function(t){y(o,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(r)):Yt(Object(r)).forEach(function(t){Object.defineProperty(o,t,Object.getOwnPropertyDescriptor(r,t))})}return o}function $(o){"@babel/helpers - typeof";return $=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},$(o)}function E(o,a){if(!(o instanceof a))throw new TypeError("Cannot call a class as a function")}function $t(o,a){for(var r=0;r<a.length;r++){var t=a[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(o,Ai(t.key),t)}}function w(o,a,r){return a&&$t(o.prototype,a),r&&$t(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}function y(o,a,r){return a=Ai(a),a in o?Object.defineProperty(o,a,{value:r,enumerable:!0,configurable:!0,writable:!0}):o[a]=r,o}function S(o,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function");o.prototype=Object.create(a&&a.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),a&&Tt(o,a)}function P(o){return P=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},P(o)}function Tt(o,a){return Tt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Tt(o,a)}function Nn(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(o){return!1}}function v(o){if(o===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return o}function At(o,a){if(a&&(typeof a=="object"||typeof a=="function"))return a;if(a!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return v(o)}function x(o){var a=Nn();return function(){var t=P(o),e;if(a){var i=P(this).constructor;e=Reflect.construct(t,arguments,i)}else e=t.apply(this,arguments);return At(this,e)}}function Bn(o,a){for(;!Object.prototype.hasOwnProperty.call(o,a)&&(o=P(o),o!==null););return o}function I(){return typeof Reflect<"u"&&Reflect.get?I=Reflect.get.bind():I=function(a,r,t){var e=Bn(a,r);if(e){var i=Object.getOwnPropertyDescriptor(e,r);return i.get?i.get.call(arguments.length<3?a:t):i.value}},I.apply(this,arguments)}function re(o){return Hn(o)||Un(o)||Vn(o)||jn()}function Hn(o){if(Array.isArray(o))return bt(o)}function Un(o){if(typeof Symbol<"u"&&o[Symbol.iterator]!=null||o["@@iterator"]!=null)return Array.from(o)}function Vn(o,a){if(o){if(typeof o=="string")return bt(o,a);var r=Object.prototype.toString.call(o).slice(8,-1);if(r==="Object"&&o.constructor&&(r=o.constructor.name),r==="Map"||r==="Set")return Array.from(o);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bt(o,a)}}function bt(o,a){(a==null||a>o.length)&&(a=o.length);for(var r=0,t=new Array(a);r<a;r++)t[r]=o[r];return t}function jn(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Wn(o,a){if(typeof o!="object"||o===null)return o;var r=o[Symbol.toPrimitive];if(r!==void 0){var t=r.call(o,a||"default");if(typeof t!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(a==="string"?String:Number)(o)}function Ai(o){var a=Wn(o,"string");return typeof a=="symbol"?a:String(a)}var Di={exports:{}};(function(o){var a=Object.prototype.hasOwnProperty,r="~";function t(){}Object.create&&(t.prototype=Object.create(null),new t().__proto__||(r=!1));function e(l,u,c){this.fn=l,this.context=u,this.once=c||!1}function i(l,u,c,h,d){if(typeof c!="function")throw new TypeError("The listener must be a function");var g=new e(c,h||l,d),p=r?r+u:u;return l._events[p]?l._events[p].fn?l._events[p]=[l._events[p],g]:l._events[p].push(g):(l._events[p]=g,l._eventsCount++),l}function n(l,u){--l._eventsCount===0?l._events=new t:delete l._events[u]}function s(){this._events=new t,this._eventsCount=0}s.prototype.eventNames=function(){var u=[],c,h;if(this._eventsCount===0)return u;for(h in c=this._events)a.call(c,h)&&u.push(r?h.slice(1):h);return Object.getOwnPropertySymbols?u.concat(Object.getOwnPropertySymbols(c)):u},s.prototype.listeners=function(u){var c=r?r+u:u,h=this._events[c];if(!h)return[];if(h.fn)return[h.fn];for(var d=0,g=h.length,p=new Array(g);d<g;d++)p[d]=h[d].fn;return p},s.prototype.listenerCount=function(u){var c=r?r+u:u,h=this._events[c];return h?h.fn?1:h.length:0},s.prototype.emit=function(u,c,h,d,g,p){var m=r?r+u:u;if(!this._events[m])return!1;var _=this._events[m],b=arguments.length,T,k;if(_.fn){switch(_.once&&this.removeListener(u,_.fn,void 0,!0),b){case 1:return _.fn.call(_.context),!0;case 2:return _.fn.call(_.context,c),!0;case 3:return _.fn.call(_.context,c,h),!0;case 4:return _.fn.call(_.context,c,h,d),!0;case 5:return _.fn.call(_.context,c,h,d,g),!0;case 6:return _.fn.call(_.context,c,h,d,g,p),!0}for(k=1,T=new Array(b-1);k<b;k++)T[k-1]=arguments[k];_.fn.apply(_.context,T)}else{var D=_.length,L;for(k=0;k<D;k++)switch(_[k].once&&this.removeListener(u,_[k].fn,void 0,!0),b){case 1:_[k].fn.call(_[k].context);break;case 2:_[k].fn.call(_[k].context,c);break;case 3:_[k].fn.call(_[k].context,c,h);break;case 4:_[k].fn.call(_[k].context,c,h,d);break;default:if(!T)for(L=1,T=new Array(b-1);L<b;L++)T[L-1]=arguments[L];_[k].fn.apply(_[k].context,T)}}return!0},s.prototype.on=function(u,c,h){return i(this,u,c,h,!1)},s.prototype.once=function(u,c,h){return i(this,u,c,h,!0)},s.prototype.removeListener=function(u,c,h,d){var g=r?r+u:u;if(!this._events[g])return this;if(!c)return n(this,g),this;var p=this._events[g];if(p.fn)p.fn===c&&(!d||p.once)&&(!h||p.context===h)&&n(this,g);else{for(var m=0,_=[],b=p.length;m<b;m++)(p[m].fn!==c||d&&!p[m].once||h&&p[m].context!==h)&&_.push(p[m]);_.length?this._events[g]=_.length===1?_[0]:_:n(this,g)}return this},s.prototype.removeAllListeners=function(u){var c;return u?(c=r?r+u:u,this._events[c]&&n(this,c)):(this._events=new t,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,o.exports=s})(Di);var Oi=Di.exports;const Ri=Pi(Oi);var zn=typeof window<"u"&&window.location&&window.location.href.indexOf("xgplayerdebugger=1")>-1,ut={info:"color: #525252; background-color: #90ee90;",error:"color: #525252; background-color: red;",warn:"color: #525252; background-color: yellow; "},ct="%c[xgplayer]",R={config:{debug:zn?3:0},logInfo:function(a){for(var r,t=arguments.length,e=new Array(t>1?t-1:0),i=1;i<t;i++)e[i-1]=arguments[i];this.config.debug>=3&&(r=console).log.apply(r,[ct,ut.info,a].concat(e))},logWarn:function(a){for(var r,t=arguments.length,e=new Array(t>1?t-1:0),i=1;i<t;i++)e[i-1]=arguments[i];this.config.debug>=1&&(r=console).warn.apply(r,[ct,ut.warn,a].concat(e))},logError:function(a){var r;if(!(this.config.debug<1)){for(var t=this.config.debug>=2?"trace":"error",e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];(r=console)[t].apply(r,[ct,ut.error,a].concat(i))}}};function Gn(o){o.logInfo=R.logInfo.bind(o),o.logWarn=R.logWarn.bind(o),o.logError=R.logError.bind(o)}var Kn=function(){function o(a){E(this,o),this.bufferedList=a}return w(o,[{key:"start",value:function(r){return this.bufferedList[r].start}},{key:"end",value:function(r){return this.bufferedList[r].end}},{key:"length",get:function(){return this.bufferedList.length}}]),o}(),f={};f.createDom=function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"div",a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},t=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"",e=document.createElement(o);return e.className=t,e.innerHTML=a,Object.keys(r).forEach(function(i){var n=i,s=r[i];o==="video"||o==="audio"||o==="live-video"?s&&e.setAttribute(n,s):e.setAttribute(n,s)}),e};f.createDomFromHtml=function(o){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"";try{var t=document.createElement("div");t.innerHTML=o;var e=t.children;return t=null,e.length>0?(e=e[0],r&&f.addClass(e,r),a&&Object.keys(a).forEach(function(i){e.setAttribute(i,a[i])}),e):null}catch(i){return R.logError("util.createDomFromHtml",i),null}};f.hasClass=function(o,a){if(!o||!a)return!1;try{return Array.prototype.some.call(o.classList,function(t){return t===a})}catch(t){var r=o.className&&$(o.className)==="object"?o.getAttribute("class"):o.className;return r&&!!r.match(new RegExp("(\\s|^)"+a+"(\\s|$)"))}};f.addClass=function(o,a){if(!(!o||!a))try{a.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach(function(r){r&&o.classList.add(r)})}catch(r){f.hasClass(o,a)||(o.className&&$(o.className)==="object"?o.setAttribute("class",o.getAttribute("class")+" "+a):o.className+=" "+a)}};f.removeClass=function(o,a){if(!(!o||!a))try{a.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach(function(r){r&&o.classList.remove(r)})}catch(r){f.hasClass(o,a)&&a.split(/\s+/g).forEach(function(t){var e=new RegExp("(\\s|^)"+t+"(\\s|$)");o.className&&$(o.className)==="object"?o.setAttribute("class",o.getAttribute("class").replace(e," ")):o.className=o.className.replace(e," ")})}};f.toggleClass=function(o,a){o&&a.split(/\s+/g).forEach(function(r){f.hasClass(o,r)?f.removeClass(o,r):f.addClass(o,r)})};f.classNames=function(){for(var o=arguments,a=[],r=function(i){f.typeOf(o[i])==="String"?a.push(o[i]):f.typeOf(o[i])==="Object"&&Object.keys(o[i]).map(function(n){o[i][n]&&a.push(n)})},t=0;t<arguments.length;t++)r(t);return a.join(" ")};f.findDom=function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:document,a=arguments.length>1?arguments[1]:void 0,r;try{r=o.querySelector(a)}catch(t){R.logError("util.findDom",t),a.indexOf("#")===0&&(r=o.getElementById(a.slice(1)))}return r};f.getCss=function(o,a){return o.currentStyle?o.currentStyle[a]:document.defaultView.getComputedStyle(o,!1)[a]};f.padStart=function(o,a,r){for(var t=String(r),e=a>>0,i=Math.ceil(e/t.length),n=[],s=String(o);i--;)n.push(t);return n.join("").substring(0,e-s.length)+s};f.format=function(o){if(window.isNaN(o))return"";o=Math.round(o);var a=f.padStart(Math.floor(o/3600),2,0),r=f.padStart(Math.floor((o-a*3600)/60),2,0),t=f.padStart(Math.floor(o-a*3600-r*60),2,0);return(a==="00"?[r,t]:[a,r,t]).join(":")};f.event=function(o){if(o.touches){var a=o.touches[0]||o.changedTouches[0];o.clientX=a.clientX||0,o.clientY=a.clientY||0,o.offsetX=a.pageX-a.target.offsetLeft,o.offsetY=a.pageY-a.target.offsetTop}o._target=o.target||o.srcElement};f.typeOf=function(o){return Object.prototype.toString.call(o).match(/([^\s.*]+)(?=]$)/g)[0]};f.deepCopy=function(o,a){if(f.typeOf(a)==="Object"&&f.typeOf(o)==="Object")return Object.keys(a).forEach(function(r){f.typeOf(a[r])==="Object"&&!(a[r]instanceof Node)?o[r]===void 0||o[r]===void 0?o[r]=a[r]:f.deepCopy(o[r],a[r]):f.typeOf(a[r])==="Array"?o[r]=f.typeOf(o[r])==="Array"?o[r].concat(a[r]):a[r]:o[r]=a[r]}),o};f.deepMerge=function(o,a){return Object.keys(a).map(function(r){if(f.typeOf(a[r])==="Array"&&f.typeOf(o[r])==="Array"){if(f.typeOf(o[r])==="Array"){var t;(t=o[r]).push.apply(t,re(a[r]))}}else f.typeOf(o[r])===f.typeOf(a[r])&&o[r]!==null&&f.typeOf(o[r])==="Object"&&!(a[r]instanceof window.Node)?f.deepMerge(o[r],a[r]):a[r]!==null&&(o[r]=a[r])}),o};f.getBgImage=function(o){var a=(o.currentStyle||window.getComputedStyle(o,null)).backgroundImage;if(!a||a==="none")return"";var r=document.createElement("a");return r.href=a.replace(/url\("|"\)/g,""),r.href};f.copyDom=function(o){if(o&&o.nodeType===1){var a=document.createElement(o.tagName);return Array.prototype.forEach.call(o.attributes,function(r){a.setAttribute(r.name,r.value)}),o.innerHTML&&(a.innerHTML=o.innerHTML),a}else return""};f.setInterval=function(o,a,r,t){o._interval[a]||(o._interval[a]=window.setInterval(r.bind(o),t))};f.clearInterval=function(o,a){clearInterval(o._interval[a]),o._interval[a]=null};f.setTimeout=function(o,a,r){o._timers||(o._timers=[]);var t=setTimeout(function(){a(),f.clearTimeout(o,t)},r);return o._timers.push(t),t};f.clearTimeout=function(o,a){var r=o._timers;if(f.typeOf(r)==="Array"){for(var t=0;t<r.length;t++)if(r[t]===a){r.splice(t,1),clearTimeout(a);break}}else clearTimeout(a)};f.clearAllTimers=function(o){var a=o._timers;f.typeOf(a)==="Array"&&(a.map(function(r){clearTimeout(r)}),o._timerIds=[])};f.createImgBtn=function(o,a,r,t){var e=f.createDom("xg-".concat(o),"",{},"xgplayer-".concat(o,"-img"));if(e.style.backgroundImage='url("'.concat(a,'")'),r&&t){var i,n,s;["px","rem","em","pt","dp","vw","vh","vm","%"].every(function(l){return r.indexOf(l)>-1&&t.indexOf(l)>-1?(i=parseFloat(r.slice(0,r.indexOf(l)).trim()),n=parseFloat(t.slice(0,t.indexOf(l)).trim()),s=l,!1):!0}),e.style.width="".concat(i).concat(s),e.style.height="".concat(n).concat(s),e.style.backgroundSize="".concat(i).concat(s," ").concat(n).concat(s),o==="start"?e.style.margin="-".concat(n/2).concat(s," auto auto -").concat(i/2).concat(s):e.style.margin="auto 5px auto 5px"}return e};f.Hex2RGBA=function(o,a){var r=[];if(/^\#[0-9A-F]{3}$/i.test(o)){var t="#";o.replace(/[0-9A-F]/ig,function(e){t+=e+e}),o=t}return/^#[0-9A-F]{6}$/i.test(o)?(o.replace(/[0-9A-F]{2}/ig,function(e){r.push(parseInt(e,16))}),"rgba(".concat(r.join(","),", ").concat(a,")")):"rgba(255, 255, 255, 0.1)"};f.getFullScreenEl=function(){return document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement};f.checkIsFunction=function(o){return o&&typeof o=="function"};f.checkIsObject=function(o){return o!==null&&$(o)==="object"};f.hide=function(o){o.style.display="none"};f.show=function(o,a){o.style.display=a||"block"};f.isUndefined=function(o){if(typeof o>"u"||o===null)return!0};f.isNotNull=function(o){return o!=null};f.setStyleFromCsstext=function(o,a){if(a)if(f.typeOf(a)==="String"){var r=a.replace(/\s+/g,"").split(";");r.map(function(t){if(t){var e=t.split(":");e.length>1&&(o.style[e[0]]=e[1])}})}else Object.keys(a).map(function(t){o.style[t]=a[t]})};function Yn(o,a){for(var r=0,t=a.length;r<t;r++)if(o.indexOf(a[r])>-1)return!0;return!1}f.filterStyleFromText=function(o){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:["width","height","top","left","bottom","right","position","z-index","padding","margin","transform"],r=o.style.cssText;if(!r)return{};var t=r.replace(/\s+/g,"").split(";"),e={},i={};return t.map(function(n){if(n){var s=n.split(":");s.length>1&&(Yn(s[0],a)?e[s[0]]=s[1]:i[s[0]]=s[1])}}),o.setAttribute("style",""),Object.keys(i).map(function(n){o.style[n]=i[n]}),e};f.getStyleFromCsstext=function(o){var a=o.style.cssText;if(!a)return{};var r=a.replace(/\s+/g,"").split(";"),t={};return r.map(function(e){if(e){var i=e.split(":");i.length>1&&(t[i[0]]=i[1])}}),t};f.preloadImg=function(o){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:function(){},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(){};if(o){var t=new window.Image;t.onload=function(e){t=null,a&&a(e)},t.onerror=function(e){t=null,r&&r(e)},t.src=o}};f.stopPropagation=function(o){o&&o.stopPropagation()};f.scrollTop=function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0};f.scrollLeft=function(){return window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0};f.checkTouchSupport=function(){return"ontouchstart"in window};f.getBuffered2=function(o){for(var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:.5,r=[],t=0;t<o.length;t++)r.push({start:o.start(t)<.5?0:o.start(t),end:o.end(t)});r.sort(function(l,u){var c=l.start-u.start;return c||u.end-l.end});var e=[];if(a)for(var i=0;i<r.length;i++){var n=e.length;if(n){var s=e[n-1].end;r[i].start-s<a?r[i].end>s&&(e[n-1].end=r[i].end):e.push(r[i])}else e.push(r[i])}else e=r;return new Kn(e)};f.getEventPos=function(o){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;return o.touches&&o.touches.length>0&&(o=o.touches[0]),{x:o.x/a,y:o.y/a,clientX:o.clientX/a,clientY:o.clientY/a,offsetX:o.offsetX/a,offsetY:o.offsetY/a,pageX:o.pageX/a,pageY:o.pageY/a}};f.requestAnimationFrame=function(o){var a=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame;if(a)return a(o)};f.getHostFromUrl=function(o){if(f.typeOf(o)!=="String")return"";var a=o.split("/"),r="";return a.length>3&&a[2]&&(r=a[2]),r};f.cancelAnimationFrame=function(o){var a=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.cancelRequestAnimationFrame;a&&a(o)};f.isMSE=function(o){return o.media&&(o=o.media),!o||!(o instanceof HTMLMediaElement)?!1:/^blob/.test(o.currentSrc)||/^blob/.test(o.src)};f.isBlob=function(o){return typeof o=="string"&&/^blob/.test(o)};f.generateSessionId=function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,a=new Date().getTime();try{o=parseInt(o)}catch(t){o=0}a+=o,window.performance&&typeof window.performance.now=="function"&&(a+=parseInt(window.performance.now()));var r="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=(a+Math.random()*16)%16|0;return a=Math.floor(a/16),(t==="x"?e:e&3|8).toString(16)});return r};f.createEvent=function(o){var a;return typeof window.Event=="function"?a=new Event(o):(a=document.createEvent("Event"),a.initEvent(o,!0,!0)),a};f.adjustTimeByDuration=function(o,a,r){return!a||!o?o:o>a||r&&o<a?a:o};f.createPositionBar=function(o,a){var r=f.createDom("xg-bar","",{"data-index":-1},o);return a.appendChild(r),r};f.getTransformStyle=function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{x:0,y:0,scale:1,rotate:0},a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",r={scale:"".concat(o.scale||1),translate:"".concat(o.x||0,"%, ").concat(o.y||0,"%"),rotate:"".concat(o.rotate||0,"deg")},t=Object.keys(r);return t.forEach(function(e){var i=new RegExp("".concat(e,"\\([^\\(]+\\)"),"g"),n="".concat(e,"(").concat(r[e],")");i.test(a)?(i.lastIndex=-1,a=a.replace(i,n)):a+="".concat(n," ")}),a};f.convertDeg=function(o){return Math.abs(o)<=1?o*360:o%360};f.getIndexByTime=function(o,a){var r=a.length,t=-1;if(r<1)return t;if(o<=a[0].end||r<2)t=0;else if(o>a[r-1].end)t=r-1;else for(var e=1;e<r;e++)if(o>a[e-1].end&&o<=a[e].end){t=e;break}return t};f.getOffsetCurrentTime=function(o,a){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:-1,t=-1;if(r>=0&&r<a.length?t=r:t=f.getIndexByTime(o,a),t<0)return-1;var e=a.length,i=a[t],n=i.start,s=i.end,l=i.cTime,u=i.offset;return o<n?l:o>=n&&o<=s?o-u:o>s&&t>=e-1?s:-1};f.getCurrentTimeByOffset=function(o,a){var r=-1;if(!a||a.length<0)return o;for(var t=0;t<a.length;t++)if(o<=a[t].duration){r=t;break}if(r!==-1){var e=a[r].start;return r-1<0?e+o:e+(o-a[r-1].duration)}return o};function Mi(o){var a=$(o);return o!==null&&(a==="object"||a==="function")}function $n(o,a,r){var t,e,i,n,s,l,u=0,c=!1,h=!1,d=!0,g=!a&&a!==0&&typeof window.requestAnimationFrame=="function";if(typeof o!="function")throw new TypeError("Expected a function");a=+a||0,Mi(r)&&(c=!!r.leading,h="maxWait"in r,i=h?Math.max(+r.maxWait||0,a):i,d="trailing"in r?!!r.trailing:d);function p(O){var q=t,te=e;return t=e=void 0,u=O,n=o.apply(te,q),n}function m(O,q){return g?(window.cancelAnimationFrame(s),window.requestAnimationFrame(O)):setTimeout(O,q)}function _(O){if(g)return window.cancelAnimationFrame(O);clearTimeout(O)}function b(O){return u=O,s=m(D,a),c?p(O):n}function T(O){var q=O-l,te=O-u,ie=a-q;return h?Math.min(ie,i-te):ie}function k(O){var q=O-l,te=O-u;return l===void 0||q>=a||q<0||h&&te>=i}function D(){var O=Date.now();if(k(O))return L(O);s=m(D,T(O))}function L(O){return s=void 0,d&&t?p(O):(t=e=void 0,n)}function B(){s!==void 0&&_(s),u=0,t=l=e=s=void 0}function M(){return s===void 0?n:L(Date.now())}function W(){return s!==void 0}function j(){for(var O=Date.now(),q=k(O),te=arguments.length,ie=new Array(te),Ce=0;Ce<te;Ce++)ie[Ce]=arguments[Ce];if(t=ie,e=this,l=O,q){if(s===void 0)return b(l);if(h)return s=m(D,a),p(l)}return s===void 0&&(s=m(D,a)),n}return j.cancel=B,j.flush=M,j.pending=W,j}function Xn(o,a,r){var t=!0,e=!0;if(typeof o!="function")throw new TypeError("Expected a function");return Mi(r)&&(t="leading"in r?!!r.leading:t,e="trailing"in r?!!r.trailing:e),$n(o,a,{leading:t,trailing:e,maxWait:a})}function qn(){var o=(document.documentElement.getAttribute("lang")||navigator.language||"zh-cn").toLocaleLowerCase();return o==="zh-cn"&&(o="zh"),o}function Zn(o,a,r){if(o){var t=o.getAttribute(r);return!!(t&&t===a&&(o.tagName==="VIDEO"||o.tagName==="AUDIO"))}}var Xt={android:/(Android)\s([\d.]+)/,ios:/(Version)\/([\d.]+)/},Jn=["avc1.42E01E, mp4a.40.2","avc1.58A01E, mp4a.40.2","avc1.4D401E, mp4a.40.2","avc1.64001E, mp4a.40.2","avc1.42E01E","mp4v.20.8","mp4v.20.8, mp4a.40.2","mp4v.20.240, mp4a.40.2"],A={get device(){var o=A.os;return o.isPc?"pc":"mobile"},get browser(){if(typeof navigator>"u")return"";var o=navigator.userAgent.toLowerCase(),a={ie:/rv:([\d.]+)\) like gecko/,firefox:/firefox\/([\d.]+)/,chrome:/chrome\/([\d.]+)/,opera:/opera.([\d.]+)/,safari:/version\/([\d.]+).*safari/};return[].concat(Object.keys(a).filter(function(r){return a[r].test(o)}))[0]},get os(){if(typeof navigator>"u")return{};var o=navigator.userAgent,a=/(?:Windows Phone)/.test(o),r=/(?:SymbianOS)/.test(o)||a,t=/(?:Tizen)/ig.test(o),e=/(?:Web0S)/ig.test(o),i=/(?:Android)/.test(o),n=/(?:Firefox)/.test(o),s=/(?:iPad|PlayBook)/.test(o)||navigator.platform==="MacIntel"&&navigator.maxTouchPoints>1,l=s||i&&!/(?:Mobile)/.test(o)||n&&/(?:Tablet)/.test(o),u=/(?:iPhone)/.test(o)&&!l,c=!u&&!i&&!r&&!l;return{isTablet:l,isPhone:u,isIpad:s,isIos:u||s,isAndroid:i,isPc:c,isSymbian:r,isWindowsPhone:a,isFireFox:n,isTizen:t,isWebOS:e}},get osVersion(){if(typeof navigator>"u")return 0;var o=navigator.userAgent,a="";/(?:iPhone)|(?:iPad|PlayBook)/.test(o)?a=Xt.ios:a=Xt.android;var r=a?a.exec(o):[];if(r&&r.length>=3){var t=r[2].split(".");return t.length>0?parseInt(t[0]):0}return 0},get isWeixin(){if(typeof navigator>"u")return!1;var o=/(micromessenger)\/([\d.]+)/,a=o.exec(navigator.userAgent.toLocaleLowerCase());return!!a},isSupportMP4:function(){var a={isSupport:!1,mime:""};if(typeof document>"u")return a;if(this.supportResult)return this.supportResult;var r=document.createElement("video");return typeof r.canPlayType=="function"&&Jn.map(function(t){r.canPlayType('video/mp4; codecs="'.concat(t,'"'))==="probably"&&(a.isSupport=!0,a.mime+="||".concat(t))}),this.supportResult=a,r=null,a},isMSESupport:function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:'video/mp4; codecs="avc1.42E01E,mp4a.40.2"';if(typeof MediaSource>"u"||!MediaSource)return!1;try{return MediaSource.isTypeSupported(a)}catch(r){return this._logger.error(a,r),!1}},isHevcSupported:function(){return typeof MediaSource>"u"||!MediaSource.isTypeSupported?!1:MediaSource.isTypeSupported('video/mp4;codecs="hev1.1.6.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.2.4.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.3.E.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.4.10.L120.90"')},probeConfigSupported:function(a){var r={supported:!1,smooth:!1,powerEfficient:!1};if(!a||typeof navigator>"u")return Promise.resolve(r);if(navigator.mediaCapabilities&&navigator.mediaCapabilities.decodingInfo)return navigator.mediaCapabilities.decodingInfo(a);var t=a.video||{},e=a.audio||{};try{var i=MediaSource.isTypeSupported(t.contentType),n=MediaSource.isTypeSupported(e.contentType);return Promise.resolve({supported:i&&n,smooth:!1,powerEfficient:!1})}catch(s){return Promise.resolve(r)}}},Et="3.0.22",qt={1:"media",2:"media",3:"media",4:"media",5:"media",6:"media"},Zt={1:5101,2:5102,3:5103,4:5104,5:5105,6:5106},$e=w(function o(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{errorType:"",errorCode:0,errorMessage:"",originError:"",ext:{},mediaError:null};E(this,o);var t=a&&a.i18n?a.i18n.ERROR_TYPES:null;if(a.media){var e=r.mediaError?r.mediaError:a.media.error||{},i=a.duration,n=a.currentTime,s=a.ended,l=a.src,u=a.currentSrc,c=a.media,h=c.readyState,d=c.networkState,g=r.errorCode||e.code;Zt[g]&&(g=Zt[g]);var p={playerVersion:Et,currentTime:n,duration:i,ended:s,readyState:h,networkState:d,src:l||u,errorType:r.errorType,errorCode:g,message:r.errorMessage||e.message,mediaError:e,originError:r.originError?r.originError.stack:"",host:f.getHostFromUrl(l||u)};return r.ext&&Object.keys(r.ext).map(function(T){p[T]=r.ext[T]}),p}else if(arguments.length>1){for(var m={playerVersion:Et,domain:document.domain},_=["errorType","currentTime","duration","networkState","readyState","src","currentSrc","ended","errd","errorCode","mediaError"],b=0;b<arguments.length;b++)m[_[b]]=arguments[b];return m.ex=t?(t[arguments[0]]||{}).msg:"",m}}),Q="play",Dt="playing",we="ended",Me="pause",Ve="error",rt="seeking",ce="seeked",J="timeupdate",je="waiting",ue="canplay",Qn="canplaythrough",he="durationchange",Fi="volumechange",fe="loadeddata",er="loadedmetadata",Ni="ratechange",Bi="progress",Ot="loadstart",Se="emptied",tr="stalled",ir="suspend",nr="abort",rr="bufferedChange",Rt="focus",Hi="blur",Mt="ready",Ui="urlNull",Ee="autoplay_started",Ft="autoplay_was_prevented",st="complete",Xe="replay",Nt="destroy",at="urlchange",wt="download_speed_change",Vi="leaveplayer",ji="enterplayer",Wi="loading",le="fullscreen_change",qe="cssFullscreen_change",Ze="mini_state_change",Bt="definition_change",sr="before_definition_change",St="after_definition_change",ar="SEI_PARSED",or="retry",me="video_resize",xt="pip_change",zi="rotate",Gi="screenShot",Je="playnext",Ki="shortcut",Yi="xglog",Qe="user_action",ot="reset",$i="source_error",Xi="source_success",lr="switch_subtitle",qi=["play","playing","ended","pause","error","seeking","seeked","timeupdate","waiting","canplay","canplaythrough","durationchange","volumechange","loadeddata","loadedmetadata","ratechange","progress","loadstart","emptied","stalled","suspend","abort","lowdecode"],Zi={STATS_INFO:"stats_info",STATS_DOWNLOAD:"stats_download",STATS_RESET:"stats_reset"},Ji="fps_stuck";const ur=Object.freeze(Object.defineProperty({__proto__:null,ABORT:nr,AFTER_DEFINITION_CHANGE:St,AUTOPLAY_PREVENTED:Ft,AUTOPLAY_STARTED:Ee,BEFORE_DEFINITION_CHANGE:sr,BUFFER_CHANGE:rr,CANPLAY:ue,CANPLAY_THROUGH:Qn,COMPLETE:st,CSS_FULLSCREEN_CHANGE:qe,DEFINITION_CHANGE:Bt,DESTROY:Nt,DOWNLOAD_SPEED_CHANGE:wt,DURATION_CHANGE:he,EMPTIED:Se,ENDED:we,ENTER_PLAYER:ji,ERROR:Ve,FPS_STUCK:Ji,FULLSCREEN_CHANGE:le,LEAVE_PLAYER:Vi,LOADED_DATA:fe,LOADED_METADATA:er,LOADING:Wi,LOAD_START:Ot,MINI_STATE_CHANGE:Ze,PAUSE:Me,PIP_CHANGE:xt,PLAY:Q,PLAYER_BLUR:Hi,PLAYER_FOCUS:Rt,PLAYING:Dt,PLAYNEXT:Je,PROGRESS:Bi,RATE_CHANGE:Ni,READY:Mt,REPLAY:Xe,RESET:ot,RETRY:or,ROTATE:zi,SCREEN_SHOT:Gi,SEEKED:ce,SEEKING:rt,SEI_PARSED:ar,SHORTCUT:Ki,SOURCE_ERROR:$i,SOURCE_SUCCESS:Xi,STALLED:tr,STATS_EVENTS:Zi,SUSPEND:ir,SWITCH_SUBTITLE:lr,TIME_UPDATE:J,URL_CHANGE:at,URL_NULL:Ui,USER_ACTION:Qe,VIDEO_EVENTS:qi,VIDEO_RESIZE:me,VOLUME_CHANGE:Fi,WAITING:je,XGLOG:Yi},Symbol.toStringTag,{value:"Module"}));function ft(o,a){!this||!this.emit||(o==="error"?this.errorHandler(o,a.error):this.emit(o,a))}function cr(o,a){return function(r,t){var e={player:a,eventName:o,originalEvent:r,detail:r.detail||{},timeStamp:r.timeStamp,currentTime:a.currentTime,duration:a.duration,paused:a.paused,ended:a.ended,isInternalOp:!!a._internalOp[r.type],muted:a.muted,volume:a.volume,host:f.getHostFromUrl(a.currentSrc),vtype:a.vtype};if(a.removeInnerOP(r.type),o==="timeupdate"&&(a._currentTime=a.media&&a.media.currentTime),o==="ratechange"){var i=a.media?a.media.playbackRate:0;if(i&&a._rate===i)return;a._rate=a.media&&a.media.playbackRate}if(o==="durationchange"&&(a._duration=a.media.duration),o==="volumechange"&&(e.isMutedChange=a._lastMuted!==a.muted,a._lastMuted=a.muted),o==="error"&&(e.error=t||a.video.error),a.mediaEventMiddleware[o]){var n=ft.bind(a,o,e);try{a.mediaEventMiddleware[o].call(a,e,n)}catch(s){throw ft.call(a,o,e),s}}else ft.call(a,o,e)}}var fr=function(o){S(r,o);var a=x(r);function r(t){var e;E(this,r),e=a.call(this,t),e._hasStart=!1,e._currentTime=0,e._duration=0,e._internalOp={},e._lastMuted=!1,e.vtype="MP4",e._rate=-1,e.mediaConfig=Object.assign({},{controls:!1,autoplay:t.autoplay,playsinline:t.playsinline,"x5-playsinline":t.playsinline,"webkit-playsinline":t.playsinline,"x5-video-player-fullscreen":t["x5-video-player-fullscreen"]||t.x5VideoPlayerFullscreen,"x5-video-orientation":t["x5-video-orientation"]||t.x5VideoOrientation,airplay:t.airplay,"webkit-airplay":t.airplay,tabindex:t.tabindex|0,mediaType:t.mediaType||"video","data-index":-1},t.videoConfig,t.videoAttributes);var i=t["x5-video-player-type"]||t.x5VideoPlayerType;return A.isWeixin&&A.os.isAndroid&&i&&(e.mediaConfig["x5-video-player-type"]=i,delete e.mediaConfig.playsinline,delete e.mediaConfig["webkit-playsinline"],delete e.mediaConfig["x5-playsinline"]),t.loop&&(e.mediaConfig.loop="loop"),t.autoplayMuted&&!Object.prototype.hasOwnProperty.call(e.mediaConfig,"muted")&&(e.mediaConfig.muted=!0),e.media=t.mediaEl instanceof HTMLMediaElement?t.mediaEl:typeof t.mediaEl=="function"?t.mediaEl(e.mediaConfig):f.createDom(e.mediaConfig.mediaType,"",e.mediaConfig,""),t.defaultPlaybackRate&&(e.media.defaultPlaybackRate=e.media.playbackRate=t.defaultPlaybackRate),f.typeOf(t.volume)==="Number"&&(e.volume=t.volume),t.autoplayMuted&&(e.media.muted=!0,e._lastMuted=!0),t.autoplay&&(e.media.autoplay=!0),t.playsinline&&(e.media.playsinline=!0),e._interval={},e.mediaEventMiddleware={},e.attachVideoEvents(),e}return w(r,[{key:"setEventsMiddleware",value:function(e){var i=this;Object.keys(e).map(function(n){i.mediaEventMiddleware[n]=e[n]})}},{key:"removeEventsMiddleware",value:function(e){var i=this;Object.keys(e).map(function(n){delete i.mediaEventMiddleware[n]})}},{key:"attachVideoEvents",value:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.media;this._evHandlers||(this._evHandlers=qi.map(function(n){var s="on".concat(n.charAt(0).toUpperCase()).concat(n.slice(1));return typeof e[s]=="function"&&e.on(n,e[s]),y({},n,cr(n,e))})),this._evHandlers.forEach(function(n){var s=Object.keys(n)[0];i.addEventListener(s,n[s],!1)})}},{key:"detachVideoEvents",value:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.media;this._evHandlers.forEach(function(n){var s=Object.keys(n)[0];i.removeEventListener(s,n[s],!1)}),this._evHandlers.forEach(function(n){var s=Object.keys(n)[0],l="on".concat(s.charAt(0).toUpperCase()).concat(s.slice(1));typeof e[l]=="function"&&e.off(s,e[l])}),this._evHandlers=null}},{key:"_attachSourceEvents",value:function(e,i){var n=this;e.removeAttribute("src"),e.load(),i.forEach(function(h,d){n.media.appendChild(f.createDom("source","",{src:"".concat(h.src),type:"".concat(h.type||""),"data-index":d+1}))});var s=e.children;if(s){this._videoSourceCount=s.length,this._videoSourceIndex=s.length,this._vLoadeddata=function(h){n.emit(Xi,{src:h.target.currentSrc,host:f.getHostFromUrl(h.target.currentSrc)})};for(var l=null,u=0;u<this._evHandlers.length;u++)if(Object.keys(this._evHandlers[u])[0]==="error"){l=this._evHandlers[u];break}!this._sourceError&&(this._sourceError=function(h){var d=parseInt(h.target.getAttribute("data-index"),10);if(n._videoSourceIndex--,n._videoSourceIndex===0||d>=n._videoSourceCount){var g={code:4,message:"sources_load_error"};l?l.error(h,g):n.errorHandler("error",g)}var p=qt[4];n.emit($i,new $e(n,{errorType:p,errorCode:4,errorMessage:"sources_load_error",mediaError:{code:4,message:"sources_load_error"},src:h.target.src}))});for(var c=0;c<s.length;c++)s[c].addEventListener("error",this._sourceError);e.addEventListener("loadeddata",this._vLoadeddata)}}},{key:"_detachSourceEvents",value:function(e){var i=e.children;if(!(!i||i.length===0||!this._sourceError)){for(var n=0;n<i.length;n++)i[n].removeEventListener("error",this._sourceError);for(;i.length>0;)e.removeChild(i[0]);this._vLoadeddata&&e.removeEventListener("loadeddata",this._vLoadeddata)}}},{key:"errorHandler",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(this.media&&(this.media.error||i)){var n=this.media.error||i,s=n.code?qt[n.code]:"other",l=n.message;this.media.currentSrc||this.media.srcObject||(l="empty_src",n={code:6,message:l}),this.emit(e,new $e(this,{errorType:s,errorCode:n.code,errorMessage:n.message||"",mediaError:n}))}}},{key:"destroy",value:function(){this.media&&(this.media.pause&&(this.media.pause(),this.media.muted=!0),this.media.removeAttribute("src"),this.media.load()),this._currentTime=0,this._duration=0,this.mediaConfig=null;for(var e in this._interval)Object.prototype.hasOwnProperty.call(this._interval,e)&&(clearInterval(this._interval[e]),this._interval[e]=null);this.detachVideoEvents(),this.media=null,this.mediaEventMiddleware={},this.removeAllListeners()}},{key:"video",get:function(){return this.media},set:function(e){this.media=e}},{key:"play",value:function(){var e=this.media?this.media.play():null;return e}},{key:"pause",value:function(){this.media&&this.media.pause()}},{key:"load",value:function(){this.media&&this.media.load()}},{key:"canPlayType",value:function(e){return this.media?this.media.canPlayType(e):!1}},{key:"getBufferedRange",value:function(e){var i=[0,0];if(!this.media)return i;e||(e=this.media.buffered);var n=this.media.currentTime;if(e)for(var s=0,l=e.length;s<l&&(i[0]=e.start(s),i[1]=e.end(s),!(i[0]<=n&&n<=i[1]));s++);return i[0]-n<=0&&n-i[1]<=0?i:[0,0]}},{key:"autoplay",get:function(){return this.media?this.media.autoplay:!1},set:function(e){this.media&&(this.media.autoplay=e)}},{key:"buffered",get:function(){return this.media?this.media.buffered:null}},{key:"buffered2",get:function(){return this.media&&this.media.buffered?f.getBuffered2(this.media.buffered):null}},{key:"bufferedPoint",get:function(){var e={start:0,end:0};if(!this.media)return e;var i=this.media.buffered;if(!i||i.length===0)return e;for(var n=0;n<i.length;n++)if((i.start(n)<=this.currentTime||i.start(n)<.1)&&i.end(n)>=this.currentTime)return{start:i.start(n),end:i.end(n)};return e}},{key:"crossOrigin",get:function(){return this.media?this.media.crossOrigin:""},set:function(e){this.media&&(this.media.crossOrigin=e)}},{key:"currentSrc",get:function(){return this.media?this.media.currentSrc:""},set:function(e){this.media&&(this.media.currentSrc=e)}},{key:"currentTime",get:function(){return this.media?this.media.currentTime!==void 0?this.media.currentTime:this._currentTime:0},set:function(e){this.media&&(this.media.currentTime=e)}},{key:"defaultMuted",get:function(){return this.media?this.media.defaultMuted:!1},set:function(e){this.media&&(this.media.defaultMuted=e)}},{key:"duration",get:function(){return this._duration}},{key:"ended",get:function(){return this.media?this.media.ended:!1}},{key:"error",get:function(){return this.media.error}},{key:"errorNote",get:function(){var e=this.media.error;if(!e)return"";var i=["MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED"];return i[this.media.error.code-1]}},{key:"loop",get:function(){return this.media?this.media.loop:!1},set:function(e){this.media&&(this.media.loop=e)}},{key:"muted",get:function(){return this.media?this.media.muted:!1},set:function(e){!this.media||this.media.muted===e||(this._lastMuted=this.media.muted,this.media.muted=e)}},{key:"networkState",get:function(){return this.media.networkState}},{key:"paused",get:function(){return this.media?this.media.paused:!0}},{key:"playbackRate",get:function(){return this.media?this.media.playbackRate:0},set:function(e){!this.media||e===1/0||(this.media.defaultPlaybackRate=e,this.media.playbackRate=e)}},{key:"played",get:function(){return this.media?this.media.played:null}},{key:"preload",get:function(){return this.media?this.media.preload:!1},set:function(e){this.media&&(this.media.preload=e)}},{key:"readyState",get:function(){return this.media.readyState}},{key:"seekable",get:function(){return this.media?this.media.seekable:!1}},{key:"seeking",get:function(){return this.media?this.media.seeking:!1}},{key:"src",get:function(){return this.media?this.media.src:""},set:function(e){if(this.media){if(this.emit(at,e),this.emit(je),this._currentTime=0,this._duration=0,f.isMSE(this.media)){this.onWaiting();return}this._detachSourceEvents(this.media),f.typeOf(e)==="Array"?this._attachSourceEvents(this.media,e):e?this.media.src=e:this.media.removeAttribute("src"),this.load()}}},{key:"volume",get:function(){return this.media?this.media.volume:0},set:function(e){e===1/0||!this.media||(this.media.volume=e)}},{key:"aspectRatio",get:function(){return this.media?this.media.videoWidth/this.media.videoHeight:0}},{key:"addInnerOP",value:function(e){this._internalOp[e]=!0}},{key:"removeInnerOP",value:function(e){delete this._internalOp[e]}},{key:"emit",value:function(e,i){for(var n,s=arguments.length,l=new Array(s>2?s-2:0),u=2;u<s;u++)l[u-2]=arguments[u];(n=I(P(r.prototype),"emit",this)).call.apply(n,[this,e,i].concat(l))}},{key:"on",value:function(e,i){for(var n,s=arguments.length,l=new Array(s>2?s-2:0),u=2;u<s;u++)l[u-2]=arguments[u];(n=I(P(r.prototype),"on",this)).call.apply(n,[this,e,i].concat(l))}},{key:"once",value:function(e,i){for(var n,s=arguments.length,l=new Array(s>2?s-2:0),u=2;u<s;u++)l[u-2]=arguments[u];(n=I(P(r.prototype),"once",this)).call.apply(n,[this,e,i].concat(l))}},{key:"off",value:function(e,i){for(var n,s=arguments.length,l=new Array(s>2?s-2:0),u=2;u<s;u++)l[u-2]=arguments[u];(n=I(P(r.prototype),"off",this)).call.apply(n,[this,e,i].concat(l))}},{key:"offAll",value:function(){I(P(r.prototype),"removeAllListeners",this).call(this)}}]),r}(Ri),hr=function(){function o(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{name:"xgplayer",version:1,db:null,ojstore:{name:"xg-m4a",keypath:"vid"}};E(this,o),this.indexedDB=window.indexedDB||window.webkitindexedDB,this.IDBKeyRange=window.IDBKeyRange||window.webkitIDBKeyRange,this.myDB=a}return w(o,[{key:"openDB",value:function(r){var t=this,e=this,i=this.myDB.version||1,n=e.indexedDB.open(e.myDB.name,i);n.onerror=function(s){},n.onsuccess=function(s){t.myDB.db=s.target.result,r.call(e)},n.onupgradeneeded=function(s){var l=s.target.result;s.target.transaction,l.objectStoreNames.contains(e.myDB.ojstore.name)||l.createObjectStore(e.myDB.ojstore.name,{keyPath:e.myDB.ojstore.keypath})}}},{key:"deletedb",value:function(){var r=this;r.indexedDB.deleteDatabase(this.myDB.name)}},{key:"closeDB",value:function(){this.myDB.db.close()}},{key:"addData",value:function(r,t){for(var e=this.myDB.db.transaction(r,"readwrite").objectStore(r),i,n=0;n<t.length;n++)i=e.add(t[n]),i.onerror=function(){},i.onsuccess=function(){}}},{key:"putData",value:function(r,t){for(var e=this.myDB.db.transaction(r,"readwrite").objectStore(r),i,n=0;n<t.length;n++)i=e.put(t[n]),i.onerror=function(){},i.onsuccess=function(){}}},{key:"getDataByKey",value:function(r,t,e){var i=this,n=this.myDB.db.transaction(r,"readwrite").objectStore(r),s=n.get(t);s.onerror=function(){e.call(i,null)},s.onsuccess=function(l){var u=l.target.result;e.call(i,u)}}},{key:"deleteData",value:function(r,t){var e=this.myDB.db.transaction(r,"readwrite").objectStore(r);e.delete(t)}},{key:"clearData",value:function(r){var t=this.myDB.db.transaction(r,"readwrite").objectStore(r);t.clear()}}]),o}(),Jt=["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],Qt=["requestFullscreen","webkitRequestFullscreen","mozRequestFullScreen","msRequestFullscreen"],ei=["exitFullscreen","webkitExitFullscreen","mozCancelFullScreen","msExitFullscreen"],ve="data-xgplayerid";function We(o,a,r){for(var t=arguments.length,e=new Array(t>3?t-3:0),i=3;i<t;i++)e[i-3]=arguments[i];var n=a.call.apply(a,[o].concat(e));!r||typeof r!="function"||(n&&n.then?n.then(function(){for(var s=arguments.length,l=new Array(s),u=0;u<s;u++)l[u]=arguments[u];r.call.apply(r,[o].concat(l))}):r.call.apply(r,[o].concat(e)))}function et(o,a){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{pre:null,next:null};return this.__hooks||(this.__hooks={}),!this.__hooks[o]&&(this.__hooks[o]=null),(function(){var t=arguments,e=this;if(r.pre)try{var i;(i=r.pre).call.apply(i,[this].concat(Array.prototype.slice.call(arguments)))}catch(s){throw s.message="[pluginName: ".concat(this.pluginName,":").concat(o,":pre error] >> ").concat(s.message),s}if(this.__hooks&&this.__hooks[o])try{var n=oe(this,o,a);n?n.then?n.then(function(s){s!==!1&&We.apply(void 0,[e,a,r.next].concat(re(t)))}).catch(function(s){throw s}):We.apply(void 0,[this,a,r.next].concat(Array.prototype.slice.call(arguments))):n===void 0&&We.apply(void 0,[this,a,r.next].concat(Array.prototype.slice.call(arguments)))}catch(s){throw s.message="[pluginName: ".concat(this.pluginName,":").concat(o,"] >> ").concat(s.message),s}else We.apply(void 0,[this,a,r.next].concat(Array.prototype.slice.call(arguments)))}).bind(this)}function Qi(o,a){var r=this.__hooks;if(!r||!Array.isArray(r[o]))return-1;for(var t=r[o],e=0;e<t.length;e++)if(t[e]===a)return e;return-1}function tt(o,a){var r=this.__hooks;if(r)return r.hasOwnProperty(o)?(Array.isArray(r[o])||(r[o]=[]),Qi.call(this,o,a)===-1&&r[o].push(a),!0):(console.warn("has no supported hook which name [".concat(o,"]")),!1)}function it(o,a){var r=this.__hooks;if(r)if(Array.isArray(r[o])){var t=r[o],e=Qi.call(this,o,a);e!==-1&&t.splice(e,1)}else r[o]&&(r[o]=null)}function ti(o){if(!(!this.plugins||!this.plugins[o.toLowerCase()])){for(var a=this.plugins[o.toLowerCase()],r=arguments.length,t=new Array(r>1?r-1:0),e=1;e<r;e++)t[e-1]=arguments[e];return a.useHooks&&a.useHooks.apply(a,t)}}function ii(o){if(!(!this.plugins||!this.plugins[o.toLowerCase()])){var a=this.plugins[o.toLowerCase()];if(a){for(var r=arguments.length,t=new Array(r>1?r-1:0),e=1;e<r;e++)t[e-1]=arguments[e];return a.removeHooks&&a.removeHooks.apply(a,t)}}}function en(o){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];o.__hooks={},a&&a.map(function(r){o.__hooks[r]=null}),Object.defineProperty(o,"hooks",{get:function(){return o.__hooks&&Object.keys(o.__hooks).map(function(t){if(o.__hooks[t])return t})}})}function tn(o){o.__hooks=null}function oe(o,a,r){for(var t=arguments.length,e=new Array(t>3?t-3:0),i=3;i<t;i++)e[i-3]=arguments[i];if(o.__hooks&&Array.isArray(o.__hooks[a])){var n=o.__hooks[a],s=-1,l=function u(c,h,d){for(var g=arguments.length,p=new Array(g>3?g-3:0),m=3;m<g;m++)p[m-3]=arguments[m];if(s++,n.length===0||s===n.length)return d.call.apply(d,[c,c].concat(p));var _=n[s],b=_.call.apply(_,[c,c].concat(p));if(b&&b.then)return b.then(function(T){return T===!1?null:u.apply(void 0,[c,h,d].concat(p))}).catch(function(T){console.warn("[runHooks]".concat(h," reject"),T.message)});if(b!==!1)return u.apply(void 0,[c,h,d].concat(p))};return l.apply(void 0,[o,a,r].concat(e))}else return r.call.apply(r,[o,o].concat(e))}function ht(o,a){R.logError("[".concat(o,"] event or callback cant be undefined or null when call ").concat(a))}var ye=function(){function o(a){E(this,o),f.checkIsFunction(this.beforeCreate)&&this.beforeCreate(a),en(this),this.__args=a,this.__events={},this.__onceEvents={},this.config=a.config||{},this.player=null,this.playerConfig={},this.pluginName="",this.__init(a)}return w(o,[{key:"beforeCreate",value:function(r){}},{key:"afterCreate",value:function(){}},{key:"beforePlayerInit",value:function(){}},{key:"onPluginsReady",value:function(){}},{key:"afterPlayerInit",value:function(){}},{key:"destroy",value:function(){}},{key:"__init",value:function(r){this.player=r.player,this.playerConfig=r.player&&r.player.config,this.pluginName=r.pluginName?r.pluginName.toLowerCase():this.constructor.pluginName.toLowerCase(),this.logger=r.player&&r.player.logger}},{key:"updateLang",value:function(r){r||(r=this.lang)}},{key:"lang",get:function(){return this.player.lang}},{key:"i18n",get:function(){return this.player.i18n}},{key:"i18nKeys",get:function(){return this.player.i18nKeys}},{key:"domEventType",get:function(){var r=f.checkTouchSupport()?"touch":"mouse";return this.playerConfig&&(this.playerConfig.domEventType==="touch"||this.playerConfig.domEventType==="mouse")&&(r=this.playerConfig.domEventType),r}},{key:"on",value:function(r,t){var e=this;if(!r||!t||!this.player){ht(this.pluginName,"plugin.on(event, callback)");return}typeof r=="string"?(this.__events[r]=t,this.player.on(r,t)):Array.isArray(r)&&r.forEach(function(i){e.__events[i]=t,e.player.on(i,t)})}},{key:"once",value:function(r,t){var e=this;if(!r||!t||!this.player){ht(this.pluginName,"plugin.once(event, callback)");return}typeof r=="string"?(this.__onceEvents[r]=t,this.player.once(r,t)):Array.isArray(r)&&r.forEach(function(i){e.__onceEvents[i]=t,e.player.once(r,t)})}},{key:"off",value:function(r,t){var e=this;if(!r||!t||!this.player){ht(this.pluginName,"plugin.off(event, callback)");return}typeof r=="string"?(delete this.__events[r],this.player.off(r,t)):Array.isArray(r)&&r.forEach(function(i){delete e.__events[r],e.player.off(i,t)})}},{key:"offAll",value:function(){var r=this;["__events","__onceEvents"].forEach(function(t){Object.keys(r[t]).forEach(function(e){r[t][e]&&r.off(e,r[t][e]),e&&delete r[t][e]})}),this.__events={},this.__onceEvents={}}},{key:"emit",value:function(r){var t;if(this.player){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];(t=this.player).emit.apply(t,[r].concat(i))}}},{key:"emitUserAction",value:function(r,t){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(this.player){var i=V(V({},e),{},{pluginName:this.pluginName});this.player.emitUserAction(r,t,i)}}},{key:"hook",value:function(r,t){return et.call.apply(et,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"useHooks",value:function(r,t){for(var e=arguments.length,i=new Array(e>2?e-2:0),n=2;n<e;n++)i[n-2]=arguments[n];return tt.call.apply(tt,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removeHooks",value:function(r,t){for(var e=arguments.length,i=new Array(e>2?e-2:0),n=2;n<e;n++)i[n-2]=arguments[n];return it.call.apply(it,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"registerPlugin",value:function(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"";if(this.player)return e&&(t.pluginName=e),this.player.registerPlugin({plugin:r,options:t})}},{key:"getPlugin",value:function(r){return this.player?this.player.getPlugin(r):null}},{key:"__destroy",value:function(){var r=this,t=this.player,e=this.pluginName;this.offAll(),f.clearAllTimers(this),f.checkIsFunction(this.destroy)&&this.destroy(),["player","playerConfig","pluginName","logger","__args","__hooks"].map(function(i){r[i]=null}),t.unRegisterPlugin(e),tn(this)}}],[{key:"defineGetterOrSetter",value:function(r,t){for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&Object.defineProperty(r,e,t[e])}},{key:"defineMethod",value:function(r,t){for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&typeof t[e]=="function"&&Object.defineProperty(r,e,{configurable:!0,value:t[e]})}},{key:"defaultConfig",get:function(){return{}}},{key:"pluginName",get:function(){return"pluginName"}}]),o}(),dr={CONTROLS:"controls",ROOT:"root"},H={ROOT:"root",ROOT_LEFT:"rootLeft",ROOT_RIGHT:"rootRight",ROOT_TOP:"rootTop",CONTROLS_LEFT:"controlsLeft",CONTROLS_RIGTH:"controlsRight",CONTROLS_RIGHT:"controlsRight",CONTROLS_CENTER:"controlsCenter",CONTROLS:"controls"},ni={ICON_DISABLE:"xg-icon-disable",ICON_HIDE:"xg-icon-hide"};function dt(o){return o?o.indexOf&&/^(?:http|data:|\/)/.test(o):!1}function gr(o,a){return $(o)==="object"&&o.class&&typeof o.class=="string"?"".concat(a," ").concat(o.class):a}function pr(o,a){return $(o)==="object"&&o.attr&&$(o.attr)==="object"&&Object.keys(o.attr).map(function(r){a[r]=o.attr[r]}),a}function ri(o,a){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"",t=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},e=arguments.length>4&&arguments[4]!==void 0?arguments[4]:"",i=null;if(o instanceof window.Element)return f.addClass(o,r),Object.keys(t).map(function(n){o.setAttribute(n,t[n])}),o;if(dt(o)||dt(o.url))return t.src=dt(o)?o:o.url||"",i=f.createDom(o.tag||"img","",t,"xg-img ".concat(r)),i;if(typeof o=="function")try{return i=o(),i instanceof window.Element?(f.addClass(i,r),Object.keys(t).map(function(n){i.setAttribute(n,t[n])}),i):(R.logWarn("warn>>icons.".concat(a," in config of plugin named [").concat(e,"] is a function mast return an Element Object")),null)}catch(n){return R.logError("Plugin named [".concat(e,"]:createIcon"),n),null}return typeof o=="string"?f.createDomFromHtml(o,t,r):(R.logWarn("warn>>icons.".concat(a," in config of plugin named [").concat(e,"] is invalid")),null)}function vr(o,a){var r=a.config.icons||a.playerConfig.icons;Object.keys(o).map(function(t){var e=o[t],i=e&&e.class?e.class:"",n=e&&e.attr?e.attr:{},s=null;r&&r[t]&&(i=gr(r[t],i),n=pr(r[t],n),s=ri(r[t],t,i,n,a.pluginName)),!s&&e&&(s=ri(e.icon?e.icon:e,n,i,{},a.pluginName)),a.icons[t]=s})}function mr(o,a){Object.keys(o).map(function(r){Object.defineProperty(a.langText,r,{get:function(){var e=a.lang,i=a.i18n;return i[r]?i[r]:o[r]&&o[r][e]||""}})})}var N=function(o){S(r,o);var a=x(r);function r(){var t,e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return E(this,r),t=a.call(this,e),t.__delegates=[],t}return w(r,[{key:"__init",value:function(e){if(I(P(r.prototype),"__init",this).call(this,e),!!e.root){var i=e.root,n=null;this.icons={},this.root=null,this.parent=null;var s=this.registerIcons()||{};vr(s,this),this.langText={};var l=this.registerLanguageTexts()||{};mr(l,this);var u="";try{u=this.render()}catch(d){throw R.logError("Plugin:".concat(this.pluginName,":render"),d),new Error("Plugin:".concat(this.pluginName,":render:").concat(d.message))}if(u)n=r.insert(u,i,e.index),n.setAttribute("data-index",e.index);else if(e.tag)n=f.createDom(e.tag,"",e.attr,e.name),n.setAttribute("data-index",e.index),i.appendChild(n);else return;this.root=n,this.parent=i;var c=this.config.attr||{},h=this.config.style||{};this.setAttr(c),this.setStyle(h),this.config.index&&this.root.setAttribute("data-index",this.config.index),this.__registerChildren()}}},{key:"__registerChildren",value:function(){var e=this;if(this.root){this._children=[];var i=this.children();i&&$(i)==="object"&&Object.keys(i).length>0&&Object.keys(i).map(function(n){var s=n,l=i[s],u={root:e.root},c,h;typeof l=="function"?(c=e.config[s]||{},h=l):$(l)==="object"&&typeof l.plugin=="function"&&(c=l.options?f.deepCopy(e.config[s]||{},l.options):e.config[s]||{},h=l.plugin),u.config=c,c.index!==void 0&&(u.index=c.index),c.root&&(u.root=c.root),e.registerPlugin(h,u,s)})}}},{key:"updateLang",value:function(e){e||(e=this.lang);function i(u,c){for(var h=0;h<u.children.length;h++)u.children[h].children.length>0?i(u.children[h],c):c(u.children[h])}var n=this.root,s=this.i18n,l=this.langText;n&&i(n,function(u){var c=u.getAttribute&&u.getAttribute("lang-key");if(c){var h=s[c.toUpperCase()]||l[c];h&&(u.innerHTML=typeof h=="function"?h(e):h)}})}},{key:"lang",get:function(){return this.player.lang}},{key:"changeLangTextKey",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",n=this.i18n||{},s=this.langText;e.setAttribute&&e.setAttribute("lang-key",i);var l=n[i]||s[i]||"";l&&(e.innerHTML=l)}},{key:"plugins",value:function(){return this._children}},{key:"disable",value:function(){this.config.disable=!0,f.addClass(this.find(".xgplayer-icon"),ni.ICON_DISABLE)}},{key:"enable",value:function(){this.config.disable=!1,f.removeClass(this.find(".xgplayer-icon"),ni.ICON_DISABLE)}},{key:"children",value:function(){return{}}},{key:"registerPlugin",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"";i.root=i.root||this.root;var s=I(P(r.prototype),"registerPlugin",this).call(this,e,i,n);return this._children.push(s),s}},{key:"registerIcons",value:function(){return{}}},{key:"registerLanguageTexts",value:function(){return{}}},{key:"find",value:function(e){if(this.root)return this.root.querySelector(e)}},{key:"bind",value:function(e,i,n){var s=this;if(arguments.length<3&&typeof i=="function")Array.isArray(e)?e.forEach(function(u){s.bindEL(u,i)}):this.bindEL(e,i);else{var l=r.delegate.call(this,this.root,e,i,n);this.__delegates=this.__delegates.concat(l)}}},{key:"unbind",value:function(e,i){var n=this;if(arguments.length<3&&typeof i=="function")Array.isArray(e)?e.forEach(function(u){n.unbindEL(u,i)}):this.unbindEL(e,i);else for(var s="".concat(e,"_").concat(i),l=0;l<this.__delegates.length;l++)if(this.__delegates[l].key===s){this.__delegates[l].destroy(),this.__delegates.splice(l,1);break}}},{key:"setStyle",value:function(e,i){var n=this;if(this.root){if(f.typeOf(e)==="String")return this.root.style[e]=i;f.typeOf(e)==="Object"&&Object.keys(e).map(function(s){n.root.style[s]=e[s]})}}},{key:"setAttr",value:function(e,i){var n=this;if(this.root){if(f.typeOf(e)==="String")return this.root.setAttribute(e,i);f.typeOf(e)==="Object"&&Object.keys(e).map(function(s){n.root.setAttribute(s,e[s])})}}},{key:"setHtml",value:function(e,i){this.root&&(this.root.innerHTML=e,typeof i=="function"&&i())}},{key:"bindEL",value:function(e,i){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;this.root&&"on".concat(e)in this.root&&typeof i=="function"&&this.root.addEventListener(e,i,n)}},{key:"unbindEL",value:function(e,i){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;this.root&&"on".concat(e)in this.root&&typeof i=="function"&&this.root.removeEventListener(e,i,n)}},{key:"show",value:function(e){if(this.root){this.root.style.display=e!==void 0?e:"block";var i=window.getComputedStyle(this.root,null),n=i.getPropertyValue("display");if(n==="none")return this.root.style.display="block"}}},{key:"hide",value:function(){this.root&&(this.root.style.display="none")}},{key:"appendChild",value:function(e,i){if(!this.root)return null;if(arguments.length<2&&arguments[0]instanceof window.Element)return this.root.appendChild(arguments[0]);if(!i||!(i instanceof window.Element))return null;try{return typeof e=="string"?this.find(e).appendChild(i):e.appendChild(i)}catch(n){return R.logError("Plugin:appendChild",n),null}}},{key:"render",value:function(){return""}},{key:"destroy",value:function(){}},{key:"__destroy",value:function(){var e=this,i=this.player;this.__delegates.map(function(n){n.destroy()}),this.__delegates=[],this._children instanceof Array&&(this._children.map(function(n){i.unRegisterPlugin(n.pluginName)}),this._children=null),this.root&&(this.root.hasOwnProperty("remove")?this.root.remove():this.root.parentNode&&this.root.parentNode.removeChild(this.root)),I(P(r.prototype),"__destroy",this).call(this),this.icons={},["root","parent"].map(function(n){e[n]=null})}}],[{key:"insert",value:function(e,i){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,s=i.children.length,l=Number(n),u=e instanceof window.Node;if(s){for(var c=0,h=null,d="";c<s;c++){h=i.children[c];var g=Number(h.getAttribute("data-index"));if(g>=l){d="beforebegin";break}else g<l&&(d="afterend")}return u?d==="afterend"?i.appendChild(e):i.insertBefore(e,h):h.insertAdjacentHTML(d,e),d==="afterend"?i.children[i.children.length-1]:i.children[c]}else return u?i.appendChild(e):i.insertAdjacentHTML("beforeend",e),i.children[i.children.length-1]}},{key:"defaultConfig",get:function(){return{}}},{key:"delegate",value:function(e,i,n,s){var l=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,u=[];if(e instanceof window.Node&&typeof s=="function")if(Array.isArray(n))n.forEach(function(h){var d=Gt(e,i,h,s,l);d.key="".concat(i,"_").concat(h),u.push(d)});else{var c=Gt(e,i,n,s,l);c.key="".concat(i,"_").concat(n),u.push(c)}return u}},{key:"ROOT_TYPES",get:function(){return dr}},{key:"POSITIONS",get:function(){return H}}]),r}(ye),yr=function(){function o(){var a=this;if(E(this,o),y(this,"__trigger",function(r){var t=new Date().getTime();a.timeStamp=t;for(var e=0;e<r.length;e++)a.__runHandler(r[e].target)}),this.__handlers=[],this.timeStamp=0,this.observer=null,!!window.ResizeObserver)try{this.observer=new window.ResizeObserver(Xn(this.__trigger,100,{trailing:!0})),this.timeStamp=new Date().getTime()}catch(r){console.error(r)}}return w(o,[{key:"addObserver",value:function(r,t){if(this.observer){this.observer.observe(r);for(var e=r.getAttribute(ve),i=this.__handlers,n=-1,s=0;s<i.length;s++)i[s]&&r===i[s].target&&(n=s);n>-1?this.__handlers[n].handler=t:this.__handlers.push({target:r,handler:t,playerId:e})}}},{key:"unObserver",value:function(r){var t=-1;this.__handlers.map(function(i,n){r===i.target&&(t=n)});try{var e;(e=this.observer)===null||e===void 0||e.unobserve(r)}catch(i){}t>-1&&this.__handlers.splice(t,1)}},{key:"destroyObserver",value:function(){var r;(r=this.observer)===null||r===void 0||r.disconnect(),this.observer=null,this.__handlers=null}},{key:"__runHandler",value:function(r){for(var t=this.__handlers,e=0;e<t.length;e++)if(t[e]&&r===t[e].target){try{t[e].handler(r)}catch(i){console.error(i)}return!0}return!1}}]),o}(),Ne=null;function _r(o,a){return Ne||(Ne=new yr),Ne.addObserver(o,a),Ne}function Cr(o,a){var r;(r=Ne)===null||r===void 0||r.unObserver(o,a)}var G={pluginGroup:{},init:function(a){var r=a._pluginInfoId;r||(r=new Date().getTime(),a._pluginInfoId=r),!a.config.closeResizeObserver&&_r(a.root,function(){a.resize()}),this.pluginGroup[r]={_originalOptions:a.config||{},_plugins:{}}},formatPluginInfo:function(a,r){var t=null,e=null;return a.plugin&&typeof a.plugin=="function"?(t=a.plugin,e=a.options):(t=a,e={}),r&&(e.config=r||{}),{PLUFGIN:t,options:e}},checkPluginIfExits:function(a,r){for(var t=0;t<r.length;t++)if(a.toLowerCase()===r[t].pluginName.toLowerCase())return!0;return!1},getRootByConfig:function(a,r){for(var t=Object.keys(r),e=null,i=0;i<t.length;i++)if(a.toLowerCase()===t[i].toLowerCase()){e=r[t[i]];break}return f.typeOf(e)==="Object"?{root:e.root,position:e.position}:{}},lazyRegister:function(a,r){var t=this,e=r.timeout||1500;return Promise.race([r.loader().then(function(i){var n;i&&i.__esModule?n=i.default:n=i,t.register(a,n,i.options)}),new Promise(function(i,n){setTimeout(function(){n(new Error("timeout"))},e)})])},register:function(a,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!(!a||!r||typeof r!="function"||r.prototype===void 0)){var e=a._pluginInfoId;if(!(!e||!this.pluginGroup[e])){this.pluginGroup[e]._plugins||(this.pluginGroup[e]._plugins={});var i=this.pluginGroup[e]._plugins,n=this.pluginGroup[e]._originalOptions;t.player=a;var s=t.pluginName||r.pluginName;if(!s)throw new Error("The property pluginName is necessary");if(r.isSupported&&!r.isSupported(a.config.mediaType,a.config.codecType)){console.warn("not supported plugin [".concat(s,"]"));return}t.config||(t.config={});for(var l=Object.keys(n),u=0;u<l.length;u++)if(s.toLowerCase()===l[u].toLowerCase()){var c=n[l[u]];f.typeOf(c)==="Object"?t.config=Object.assign({},t.config,n[l[u]]):f.typeOf(c)==="Boolean"&&(t.config.disable=!c);break}r.defaultConfig&&Object.keys(r.defaultConfig).forEach(function(d){typeof t.config[d]>"u"&&(t.config[d]=r.defaultConfig[d])}),t.root?typeof t.root=="string"&&(t.root=a[t.root]):t.root=a.root,t.index=t.config.index||0;try{i[s.toLowerCase()]&&(this.unRegister(e,s.toLowerCase()),console.warn("the is one plugin with same pluginName [".concat(s,"] exist, destroy the old instance")));var h=new r(t);return i[s.toLowerCase()]=h,i[s.toLowerCase()].func=r,h&&typeof h.afterCreate=="function"&&h.afterCreate(),h}catch(d){throw console.error(d),d}}}},unRegister:function(a,r){a._pluginInfoId&&(a=a._pluginInfoId),r=r.toLowerCase();try{var t=this.pluginGroup[a]._plugins[r];t&&(t.pluginName&&t.__destroy(),delete this.pluginGroup[a]._plugins[r])}catch(e){console.error("[unRegister:".concat(r,"] cgid:[").concat(a,"] error"),e)}},deletePlugin:function(a,r){var t=a._pluginInfoId;t&&this.pluginGroup[t]&&this.pluginGroup[t]._plugins&&delete this.pluginGroup[t]._plugins[r]},getPlugins:function(a){var r=a._pluginInfoId;return r&&this.pluginGroup[r]?this.pluginGroup[r]._plugins:{}},findPlugin:function(a,r){var t=a._pluginInfoId;if(!t||!this.pluginGroup[t])return null;var e=r.toLowerCase();return this.pluginGroup[t]._plugins[e]},beforeInit:function(a){var r=this;function t(e){return!e||!e.then?new Promise(function(i){i()}):e}return new Promise(function(e){if(r.pluginGroup){var i;return a._loadingPlugins&&a._loadingPlugins.length?i=Promise.all(a._loadingPlugins):i=Promise.resolve(),i.then(function(){var n=a._pluginInfoId;if(!r.pluginGroup[n]){e();return}var s=r.pluginGroup[n]._plugins,l=[];Object.keys(s).forEach(function(u){if(s[u]&&s[u].beforePlayerInit)try{var c=s[u].beforePlayerInit();l.push(t(c))}catch(h){throw l.push(t(null)),h}}),Promise.all([].concat(l)).then(function(){e()}).catch(function(u){console.error(u),e()})})}})},afterInit:function(a){var r=a._pluginInfoId;if(!(!r||!this.pluginGroup[r])){var t=this.pluginGroup[r]._plugins;Object.keys(t).forEach(function(e){t[e]&&t[e].afterPlayerInit&&t[e].afterPlayerInit()})}},setLang:function(a,r){var t=r._pluginInfoId;if(!(!t||!this.pluginGroup[t])){var e=this.pluginGroup[t]._plugins;Object.keys(e).forEach(function(i){if(e[i].updateLang)e[i].updateLang(a);else try{e[i].lang=a}catch(n){console.warn("".concat(i," setLang"))}})}},reRender:function(a){var r=this,t=a._pluginInfoId;if(!(!t||!this.pluginGroup[t])){var e=[],i=this.pluginGroup[t]._plugins;Object.keys(i).forEach(function(n){n!=="controls"&&i[n]&&(e.push({plugin:i[n].func,options:i[n].__args}),r.unRegister(t,n))}),e.forEach(function(n){r.register(a,n.plugin,n.options)})}},onPluginsReady:function(a){var r=a._pluginInfoId;if(!(!r||!this.pluginGroup[r])){var t=this.pluginGroup[r]._plugins||{};Object.keys(t).forEach(function(e){t[e].onPluginsReady&&typeof t[e].onPluginsReady=="function"&&t[e].onPluginsReady()})}},destroy:function(a){var r=a._pluginInfoId;if(this.pluginGroup[r]){Cr(a.root);for(var t=this.pluginGroup[r]._plugins,e=0,i=Object.keys(t);e<i.length;e++){var n=i[e];this.unRegister(r,n)}delete this.pluginGroup[r],delete a._pluginInfoId}}},C={DEFAULT:"xgplayer",DEFAULT_SKIN:"xgplayer-skin-default",ENTER:"xgplayer-is-enter",PAUSED:"xgplayer-pause",PLAYING:"xgplayer-playing",ENDED:"xgplayer-ended",CANPLAY:"xgplayer-canplay",LOADING:"xgplayer-isloading",ERROR:"xgplayer-is-error",REPLAY:"xgplayer-replay",NO_START:"xgplayer-nostart",ACTIVE:"xgplayer-active",INACTIVE:"xgplayer-inactive",FULLSCREEN:"xgplayer-is-fullscreen",CSS_FULLSCREEN:"xgplayer-is-cssfullscreen",ROTATE_FULLSCREEN:"xgplayer-rotate-fullscreen",PARENT_ROTATE_FULLSCREEN:"xgplayer-rotate-parent",PARENT_FULLSCREEN:"xgplayer-fullscreen-parent",INNER_FULLSCREEN:"xgplayer-fullscreen-inner",NO_CONTROLS:"no-controls",FLEX_CONTROLS:"flex-controls",CONTROLS_FOLLOW:"controls-follow",CONTROLS_AUTOHIDE:"controls-autohide",TOP_BAR_AUTOHIDE:"top-bar-autohide",NOT_ALLOW_AUTOPLAY:"not-allow-autoplay",SEEKING:"seeking",PC:"xgplayer-pc",MOBILE:"xgplayer-mobile",MINI:"xgplayer-mini"};function si(){return{id:"",el:null,url:"",domEventType:"default",nullUrlStart:!1,width:600,height:337.5,fluid:!1,fitVideoSize:"fixed",videoFillMode:"auto",volume:.6,autoplay:!1,autoplayMuted:!1,loop:!1,isLive:!1,zoom:1,videoInit:!0,poster:"",isMobileSimulateMode:!1,defaultPlaybackRate:1,execBeforePluginsCall:null,allowSeekAfterEnded:!0,enableContextmenu:!0,closeVideoClick:!1,closeVideoDblclick:!1,closePlayerBlur:!1,closeDelayBlur:!1,leavePlayerTime:3e3,closePlayVideoFocus:!1,closePauseVideoFocus:!1,closeFocusVideoFocus:!0,closeControlsBlur:!0,topBarAutoHide:!0,videoAttributes:{},startTime:0,seekedStatus:"play",miniprogress:!1,disableSwipeHandler:function(){},enableSwipeHandler:function(){},preProcessUrl:null,ignores:[],whitelist:[],inactive:3e3,lang:qn(),controls:!0,marginControls:!1,fullscreenTarget:null,screenShot:!1,rotate:!1,pip:!1,download:!1,mini:!1,cssFullscreen:!0,keyShortcut:!0,presets:[],plugins:[],playbackRate:1,definition:{list:[]},playsinline:!0,customDuration:0,timeOffset:0,icons:{},i18n:[],tabindex:0,thumbnail:null,videoConfig:{},isHideTips:!1,minWaitDelay:200,commonStyle:{progressColor:"",playedColor:"",cachedColor:"",sliderBtnStyle:{},volumeColor:""}}}var kr=function(a,r){var t,e,i;r.preset&&r.options?i=new r.preset(r.options,a.config):i=new r({},a.config);var n=i,s=n.plugins,l=s===void 0?[]:s,u=n.ignores,c=u===void 0?[]:u,h=n.icons,d=h===void 0?{}:h,g=n.i18n,p=g===void 0?[]:g;a.config.plugins||(a.config.plugins=[]),a.config.ignores||(a.config.ignores=[]),(t=a.config.plugins).push.apply(t,re(l)),(e=a.config.ignores).push.apply(e,re(c)),Object.keys(d).map(function(_){a.config.icons[_]||(a.config.icons[_]=d[_])});var m=a.config.i18n||[];p.push.apply(p,re(m)),a.config.i18n=p},Tr=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"onMouseEnter",function(s){var l=v(t),u=l.player,c=l.playerConfig;c.closeControlsBlur&&u.focus({autoHide:!1})}),y(v(t),"onMouseLeave",function(s){var l=v(t),u=l.player;u.focus()}),t}return w(r,[{key:"beforeCreate",value:function(e){!e.config.mode&&A.device==="mobile"&&(e.config.mode="flex"),e.player.config.marginControls&&(e.config.autoHide=!1)}},{key:"afterCreate",value:function(){var e=this,i=this.config,n=i.disable,s=i.height,l=i.mode;if(!n){l==="flex"&&this.player.addClass(C.FLEX_CONTROLS);var u={height:"".concat(s,"px")};Object.keys(u).map(function(h){e.root.style[h]=u[h]}),this.left=this.find("xg-left-grid"),this.center=this.find("xg-center-grid"),this.right=this.find("xg-right-grid"),this.innerRoot=this.find("xg-inner-controls"),this.on(Ze,function(h){h?f.addClass(e.root,"mini-controls"):f.removeClass(e.root,"mini-controls")});var c=this.playerConfig.isMobileSimulateMode;A.device!=="mobile"&&c!=="mobile"&&(this.bind("mouseenter",this.onMouseEnter),this.bind("mouseleave",this.onMouseLeave))}}},{key:"focus",value:function(){this.player.focus({autoHide:!1})}},{key:"focusAwhile",value:function(){this.player.focus({autoHide:!0})}},{key:"blur",value:function(){this.player.blur({ignorePaused:!0})}},{key:"recoverAutoHide",value:function(){this.config.autoHide&&f.addClass(this.root,C.CONTROLS_AUTOHIDE)}},{key:"pauseAutoHide",value:function(){f.removeClass(this.root,C.CONTROLS_AUTOHIDE)}},{key:"show",value:function(e){this.root.style.display="",this.player.focus()}},{key:"hide",value:function(){this.root.style.display="none"}},{key:"mode",get:function(){return this.config.mode}},{key:"registerPlugin",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;if(this.root){var s=e.defaultConfig||{};if(!i.root){var l=i.position?i.position:i.config&&i.config.position?i.config.position:s.position;switch(l){case H.CONTROLS_LEFT:i.root=this.left;break;case H.CONTROLS_RIGHT:i.root=this.right;break;case H.CONTROLS_CENTER:i.root=this.center;break;case H.CONTROLS:i.root=this.root;break;default:i.root=this.left}return I(P(r.prototype),"registerPlugin",this).call(this,e,i,n)}}}},{key:"destroy",value:function(){A.device!=="mobile"&&(this.unbind("mouseenter",this.onMouseEnter),this.unbind("mouseleave",this.onMouseLeave)),this.left=null,this.center=null,this.right=null,this.innerRoot=null}},{key:"render",value:function(){var e=this.config,i=e.mode,n=e.autoHide,s=e.initShow,l=e.disable;if(!l){var u=f.classNames({"xgplayer-controls":!0},{"flex-controls":i==="flex"},{"bottom-controls":i==="bottom"},y({},C.CONTROLS_AUTOHIDE,n),{"xgplayer-controls-initshow":s||!n});return'<xg-controls class="'.concat(u,'" unselectable="on">\n    <xg-inner-controls class="xg-inner-controls xg-pos">\n      <xg-left-grid class="xg-left-grid">\n      </xg-left-grid>\n      <xg-center-grid class="xg-center-grid"></xg-center-grid>\n      <xg-right-grid class="xg-right-grid">\n      </xg-right-grid>\n    </xg-inner-controls>\n    </xg-controls>')}}}],[{key:"pluginName",get:function(){return"controls"}},{key:"defaultConfig",get:function(){return{disable:!1,autoHide:!0,mode:"",initShow:!1}}}]),r}(N),br={LANG:"en",TEXT:{ERROR_TYPES:{network:{code:1,msg:"video download error"},mse:{code:2,msg:"stream append error"},parse:{code:3,msg:"parsing error"},format:{code:4,msg:"wrong format"},decoder:{code:5,msg:"decoding error"},runtime:{code:6,msg:"grammatical errors"},timeout:{code:7,msg:"play timeout"},other:{code:8,msg:"other errors"}},HAVE_NOTHING:"There is no information on whether audio/video is ready",HAVE_METADATA:"Audio/video metadata is ready ",HAVE_CURRENT_DATA:"Data about the current play location is available, but there is not enough data to play the next frame/millisecond",HAVE_FUTURE_DATA:"Current and at least one frame of data is available",HAVE_ENOUGH_DATA:"The available data is sufficient to start playing",NETWORK_EMPTY:"Audio/video has not been initialized",NETWORK_IDLE:"Audio/video is active and has been selected for resources, but no network is used",NETWORK_LOADING:"The browser is downloading the data",NETWORK_NO_SOURCE:"No audio/video source was found",MEDIA_ERR_ABORTED:"The fetch process is aborted by the user",MEDIA_ERR_NETWORK:"An error occurred while downloading",MEDIA_ERR_DECODE:"An error occurred while decoding",MEDIA_ERR_SRC_NOT_SUPPORTED:"Audio/video is not supported",REPLAY:"Replay",ERROR:"Network is offline",PLAY_TIPS:"Play",PAUSE_TIPS:"Pause",PLAYNEXT_TIPS:"Play next",DOWNLOAD_TIPS:"Download",ROTATE_TIPS:"Rotate",RELOAD_TIPS:"Reload",FULLSCREEN_TIPS:"Fullscreen",EXITFULLSCREEN_TIPS:"Exit fullscreen",CSSFULLSCREEN_TIPS:"Cssfullscreen",EXITCSSFULLSCREEN_TIPS:"Exit cssfullscreen",TEXTTRACK:"Caption",PIP:"PIP",SCREENSHOT:"Screenshot",LIVE:"LIVE",OFF:"Off",OPEN:"Open",MINI_DRAG:"Click and hold to drag",MINISCREEN:"Miniscreen",REFRESH_TIPS:"Please Try",REFRESH:"Refresh",FORWARD:"forward",LIVE_TIP:"Live"}},Z={lang:{},langKeys:[],textKeys:[]};function Ue(o,a){return Object.keys(a).forEach(function(r){var t=f.typeOf(a[r]),e=f.typeOf(o[r]);if(t==="Array"){var i;e!=="Array"&&(o[r]=[]),(i=o[r]).push.apply(i,re(a[r]))}else t==="Object"?(e!=="Object"&&(o[r]={}),Ue(o[r],a[r])):o[r]=a[r]}),o}function nn(){Object.keys(Z.lang.en).map(function(o){Z.textKeys[o]=o})}function Er(o,a){var r=[];if(a||(a=Z),!!a.lang){f.typeOf(o)!=="Array"?r=Object.keys(o).map(function(i){var n=i==="zh"?"zh-cn":i;return{LANG:n,TEXT:o[i]}}):r=o;var t=a,e=t.lang;r.map(function(i){i.LANG==="zh"&&(i.LANG="zh-cn"),e[i.LANG]?Ue(e[i.LANG]||{},i.TEXT||{}):Ht(i,a)}),nn()}}function Ht(o,a){var r=o.LANG;if(a||(a=Z),!!a.lang){var t=o.TEXT||{};r==="zh"&&(r="zh-cn"),a.lang[r]?Ue(a.lang[r],t):(a.langKeys.push(r),a.lang[r]=t),nn()}}function wr(o){var a,r={lang:{},langKeys:[],textKeys:{},pId:o};return Ue(r.lang,Z.lang),(a=r.langKeys).push.apply(a,re(Z.langKeys)),Ue(r.textKeys,Z.textKeys),r}Ht(br);var Re={get textKeys(){return Z.textKeys},get langKeys(){return Z.langKeys},get lang(){var o={};return Z.langKeys.map(function(a){o[a]=Z.lang[a]}),Z.lang["zh-cn"]&&(o.zh=Z.lang["zh-cn"]||{}),o},extend:Er,use:Ht,init:wr},F={INITIAL:1,READY:2,ATTACHING:3,ATTACHED:4,NOTALLOW:5,RUNNING:6,ENDED:7,DESTROYED:8},ai=["ERROR","INITIAL","READY","ATTACHING","ATTACHED","NOTALLOW","RUNNING","ENDED","DESTROYED"],X={},gt=null,rn=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"add",value:function(e){e&&(X[e.playerId]=e,Object.keys(X).length===1&&this.setActive(e.playerId,!0))}},{key:"remove",value:function(e){e&&(e.isUserActive,delete X[e.playerId])}},{key:"_iterate",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(var n in X)if(Object.prototype.hasOwnProperty.call(X,n)){var s=X[n];if(i){if(e(s))break}else e(s)}}},{key:"forEach",value:function(e){this._iterate(e)}},{key:"find",value:function(e){var i=null;return this._iterate(function(n){var s=e(n);return s&&(i=n),s},!0),i}},{key:"findAll",value:function(e){var i=[];return this._iterate(function(n){e(n)&&i.push(n)}),i}},{key:"setActive",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(X[e])return i?this.forEach(function(n){e===n.playerId?(n.isUserActive=!0,n.isInstNext=!1):n.isUserActive=!1}):X[e].isUserActive=i,e}},{key:"getActiveId",value:function(){for(var e=Object.keys(X),i=0;i<e.length;i++){var n=X[e[i]];if(n&&n.isUserActive)return e[i]}return null}},{key:"setNext",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(X[e])return i?this.forEach(function(n){e===n.playerId?(n.isUserActive=!1,n.isInstNext=!0):n.isInstNext=!1}):X[e].isInstNext=i,e}}],[{key:"getInstance",value:function(){return gt||(gt=new r),gt}}]),r}(Oi.EventEmitter);function Sr(o){for(var a=Object.keys(X),r=0;r<a.length;r++){var t=X[a[r]];if(t.root===o)return t}return null}var xr=["play","pause","replay","retry"],oi=0,li=0,de=null,lt=function(o){S(r,o);var a=x(r);function r(t){var e;E(this,r);var i=f.deepMerge(si(),t);e=a.call(this,i),y(v(e),"canPlayFunc",function(){if(e.config){var p=e.config,m=p.autoplay,_=p.defaultPlaybackRate;R.logInfo("player","canPlayFunc, startTime",e.__startTime),e._seekToStartTime(),e.playbackRate=_,(m||e._useAutoplay)&&e.mediaPlay(),e.off(ue,e.canPlayFunc),e.removeClass(C.ENTER)}}),y(v(e),"onFullscreenChange",function(p,m){var _=function(){f.setTimeout(v(e),function(){e.resize()},100)},b=f.getFullScreenEl();e._fullActionFrom?e._fullActionFrom="":e.emit(Qe,{eventType:"system",action:"switch_fullscreen",pluginName:"player",currentTime:e.currentTime,duration:e.duration,props:[{prop:"fullscreen",from:!0,to:!1}]});var T=Zn(b,e.playerId,ve);if(m||b&&(b===e._fullscreenEl||T))_(),!e.config.closeFocusVideoFocus&&e.media.focus(),e.fullscreen=!0,e.changeFullStyle(e.root,b,C.FULLSCREEN),e.emit(le,!0,e._fullScreenOffset),e.cssfullscreen&&e.exitCssFullscreen();else if(e.fullscreen){_();var k=v(e),D=k._fullScreenOffset,L=k.config;if(L.needFullscreenScroll?(window.scrollTo(D.left,D.top),f.setTimeout(v(e),function(){e.fullscreen=!1,e._fullScreenOffset=null},100)):(!e.config.closeFocusVideoFocus&&e.media.focus(),e.fullscreen=!1,e._fullScreenOffset=null),e.cssfullscreen)e.removeClass(C.FULLSCREEN);else{var B=e._fullscreenEl;!B&&(e.root.contains(p.target)||p.target===e.root)&&(B=p.target),e.recoverFullStyle(e.root,B,C.FULLSCREEN)}e._fullscreenEl=null,e.emit(le,!1)}}),y(v(e),"_onWebkitbeginfullscreen",function(p){e._fullscreenEl=e.media,e.onFullscreenChange(p,!0)}),y(v(e),"_onWebkitendfullscreen",function(p){e.onFullscreenChange(p,!1)}),en(v(e),xr),e.config=i,e._pluginInfoId=f.generateSessionId(),Gn(v(e));var n=e.constructor.defaultPreset;if(e.config.presets.length){var s=e.config.presets.indexOf("default");s>=0&&n&&(e.config.presets[s]=n)}else n&&e.config.presets.push(n);e.userTimer=null,e.waitTimer=null,e.handleSource=!0,e._state=F.INITIAL,e.isAd=!1,e.isError=!1,e._hasStart=!1,e.isSeeking=!1,e.isCanplay=!1,e._useAutoplay=!1,e.__startTime=-1,e.rotateDeg=0,e.isActive=!1,e.fullscreen=!1,e.cssfullscreen=!1,e.isRotateFullscreen=!1,e._fullscreenEl=null,e.timeSegments=[],e._cssfullscreenEl=null,e.curDefinition=null,e._orgCss="",e._fullScreenOffset=null,e._videoHeight=0,e._videoWidth=0,e.videoPos={pi:1,scale:0,rotate:-1,x:0,y:0,h:-1,w:-1,vy:0,vx:0},e.sizeInfo={width:0,height:0,left:0,top:0},e._accPlayed={t:0,acc:0,loopAcc:0},e._offsetInfo={currentTime:-1,duration:0},e.innerContainer=null,e.controls=null,e.topBar=null,e.root=null,e.__i18n=Re.init(e._pluginInfoId),A.os.isAndroid&&A.osVersion>0&&A.osVersion<6&&(e.config.autoplay=!1),e.database=new hr,e.isUserActive=!1,e._onceSeekCanplay=null,e._isPauseBeforeSeek=0,e.innerStates={isActiveLocked:!1},e.instManager=de;var l=e._initDOM();if(!l)return console.error(new Error("can't find the dom which id is ".concat(e.config.id," or this.config.el does not exist"))),At(e);var u=e.config,c=u.definition,h=c===void 0?{}:c,d=u.url;if(!d&&h.list&&h.list.length>0){var g=h.list.find(function(p){return p.definition&&p.definition===h.defaultDefinition});g||(h.defaultDefinition=h.list[0].definition,g=h.list[0]),e.config.url=g.url,e.curDefinition=g}return e._bindEvents(),e._registerPresets(),e._registerPlugins(),G.onPluginsReady(v(e)),e.getInitDefinition(),e.setState(F.READY),f.setTimeout(v(e),function(){e.emit(Mt)},0),e.onReady&&e.onReady(),(e.config.videoInit||e.config.autoplay)&&(!e.hasStart||e.state<F.ATTACHED)&&e.start(),e}return w(r,[{key:"_initDOM",value:function(){var e,i=this;if(this.root=this.config.id?document.getElementById(this.config.id):null,!this.root){var n=this.config.el;if(n&&n.nodeType===1)this.root=n;else return this.emit(Ve,new $e("use",this.config.vid,{line:32,handle:"Constructor",msg:"container id can't be empty"})),console.error("this.confg.id or this.config.el can't be empty"),!1}var s=Sr(this.root);if(s&&(R.logWarn("The is an Player instance already exists in this.root, destroy it and reinitialize"),s.destroy()),this.root.setAttribute(ve,this.playerId),this.media.setAttribute(ve,this.playerId),(e=de)===null||e===void 0||e.add(this),G.init(this),this._initBaseDoms(),this.config.controls){var l=this.config.controls.root||null,u=G.register(this,Tr,{root:l});this.controls=u}var c=this.config.isMobileSimulateMode==="mobile"?"mobile":A.device;if(this.addClass("".concat(C.DEFAULT," ").concat(C.INACTIVE," xgplayer-").concat(c," ").concat(this.config.controls?"":C.NO_CONTROLS)),this.config.autoplay?this.addClass(C.ENTER):this.addClass(C.NO_START),this.config.fluid){var h=this.config,d=h.width,g=h.height;(typeof d!="number"||typeof g!="number")&&(d=600,g=337.5);var p={width:"100%",height:"0","max-width":"100%","padding-top":"".concat(g*100/d,"%")};Object.keys(p).forEach(function(D){i.root.style[D]=p[D]})}else["width","height"].forEach(function(D){i.config[D]&&(typeof i.config[D]!="number"?i.root.style[D]=i.config[D]:i.root.style[D]="".concat(i.config[D],"px"))});var m=this.root.getBoundingClientRect(),_=m.width,b=m.height,T=m.left,k=m.top;return this.sizeInfo.width=_,this.sizeInfo.height=b,this.sizeInfo.left=T,this.sizeInfo.top=k,!0}},{key:"_initBaseDoms",value:function(){this.topBar=null,this.leftBar=null,this.rightBar=null,this.config.marginControls&&(this.innerContainer=f.createDom("xg-video-container","",{"data-index":-1},"xg-video-container"),this.root.appendChild(this.innerContainer))}},{key:"_bindEvents",value:function(){var e=this;["focus","blur"].forEach(function(i){e.on(i,e["on"+i.charAt(0).toUpperCase()+i.slice(1)])}),Jt.forEach(function(i){document&&document.addEventListener(i,e.onFullscreenChange)}),A.os.isIos&&(this.media.addEventListener("webkitbeginfullscreen",this._onWebkitbeginfullscreen),this.media.addEventListener("webkitendfullscreen",this._onWebkitendfullscreen)),this.once(fe,this.resize),this.playFunc=function(){e.config.closeFocusVideoFocus||e.media.focus()},this.once(Q,this.playFunc)}},{key:"_unbindEvents",value:function(){var e=this;this.root.removeEventListener("mousemove",this.mousemoveFunc),Jt.forEach(function(i){document.removeEventListener(i,e.onFullscreenChange)}),this.playFunc&&this.off(Q,this.playFunc),this.off(ue,this.canPlayFunc),this.media.removeEventListener("webkitbeginfullscreen",this._onWebkitbeginfullscreen),this.media.removeEventListener("webkitendfullscreen",this._onWebkitendfullscreen)}},{key:"_clearUserTimer",value:function(){this.userTimer&&(f.clearTimeout(this,this.userTimer),this.userTimer=null)}},{key:"_startInit",value:function(e){var i=this;if(this.media&&!((!e||e===""||f.typeOf(e)==="Array"&&e.length===0)&&(e="",this.emit(Ui),R.logWarn("config.url is null, please get url and run player._startInit(url)"),this.config.nullUrlStart))){this.handleSource&&(this._detachSourceEvents(this.media),f.typeOf(e)==="Array"&&e.length>0?this._attachSourceEvents(this.media,e):!this.media.src||this.media.src!==e?this.media.src=e:e||this.media.removeAttribute("src")),f.typeOf(this.config.volume)==="Number"&&(this.volume=this.config.volume);var n=this.innerContainer?this.innerContainer:this.root;this.media instanceof window.Element&&!n.contains(this.media)&&n.insertBefore(this.media,n.firstChild);var s=this.media.readyState;R.logInfo("_startInit readyState",s),this.config.autoplay&&(!f.isMSE(this.media)&&this.load(),(A.os.isIpad||A.os.isPhone)&&this.mediaPlay());var l=this.config.startTime;this.__startTime=l>0?l:-1,this.config.startTime=0,s>=2&&this.duration>0?this.canPlayFunc():this.on(ue,this.canPlayFunc),(!this.hasStart||this.state<F.ATTACHED)&&G.afterInit(this),this.hasStart=!0,this.setState(F.ATTACHED),f.setTimeout(this,function(){i.emit(st)},0)}}},{key:"_registerPlugins",value:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this._loadingPlugins=[];var n=this.config.ignores||[],s=this.config.plugins||[],l=this.config.i18n||[];i&&Re.extend(l,this.__i18n);var u=n.join("||").toLowerCase().split("||"),c=this.plugins;s.forEach(function(h){try{var d=h.plugin?h.plugin.pluginName:h.pluginName;if(d&&u.indexOf(d.toLowerCase())>-1)return null;if(!i&&c[d.toLowerCase()])return;if(h.lazy&&h.loader){var g=G.lazyRegister(e,h);h.forceBeforeInit&&(g.then(function(){e._loadingPlugins.splice(e._loadingPlugins.indexOf(g),1)}).catch(function(p){R.logError("_registerPlugins:loadingPlugin",p),e._loadingPlugins.splice(e._loadingPlugins.indexOf(g),1)}),e._loadingPlugins.push(g));return}return e.registerPlugin(h)}catch(p){R.logError("_registerPlugins:",p)}})}},{key:"_registerPresets",value:function(){var e=this;this.config.presets.forEach(function(i){kr(e,i)})}},{key:"_getRootByPosition",value:function(e){var i=null;switch(e){case H.ROOT_RIGHT:this.rightBar||(this.rightBar=f.createPositionBar("xg-right-bar",this.root)),i=this.rightBar;break;case H.ROOT_LEFT:this.leftBar||(this.leftBar=f.createPositionBar("xg-left-bar",this.root)),i=this.leftBar;break;case H.ROOT_TOP:this.topBar||(this.topBar=f.createPositionBar("xg-top-bar",this.root),this.config.topBarAutoHide&&f.addClass(this.topBar,C.TOP_BAR_AUTOHIDE)),i=this.topBar;break;default:i=this.innerContainer||this.root;break}return i}},{key:"registerPlugin",value:function(e,i){var n=G.formatPluginInfo(e,i),s=n.PLUFGIN,l=n.options,u=this.config.plugins,c=G.checkPluginIfExits(s.pluginName,u);!c&&u.push(s);var h=G.getRootByConfig(s.pluginName,this.config);h.root&&(l.root=h.root),h.position&&(l.position=h.position);var d=l.position?l.position:l.config&&l.config.position||s.defaultConfig&&s.defaultConfig.position;if(!l.root&&typeof d=="string"&&d.indexOf("controls")>-1){var g;return(g=this.controls)===null||g===void 0?void 0:g.registerPlugin(s,l,s.pluginName)}return l.root||(l.root=this._getRootByPosition(d)),G.register(this,s,l)}},{key:"deregister",value:function(e){typeof e=="string"?G.unRegister(this,e):e instanceof ye&&G.unRegister(this,e.pluginName)}},{key:"unRegisterPlugin",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;this.deregister(e),i&&this.removePluginFromConfig(e)}},{key:"removePluginFromConfig",value:function(e){var i;if(typeof e=="string"?i=e:e instanceof ye&&(i=e.pluginName),!!i)for(var n=this.config.plugins.length-1;n>-1;n--){var s=this.config.plugins[n];if(s.pluginName.toLowerCase()===i.toLowerCase()){this.config.plugins.splice(n,1);break}}}},{key:"plugins",get:function(){return G.getPlugins(this)}},{key:"getPlugin",value:function(e){var i=G.findPlugin(this,e);return i&&i.pluginName?i:null}},{key:"addClass",value:function(e){this.root&&(f.hasClass(this.root,e)||f.addClass(this.root,e))}},{key:"removeClass",value:function(e){this.root&&f.removeClass(this.root,e)}},{key:"hasClass",value:function(e){if(this.root)return f.hasClass(this.root,e)}},{key:"setAttribute",value:function(e,i){this.root&&this.root.setAttribute(e,i)}},{key:"removeAttribute",value:function(e,i){this.root&&this.root.removeAttribute(e,i)}},{key:"start",value:function(e){var i=this;if(!(this.state>F.ATTACHING))return!e&&!this.config.url&&this.getInitDefinition(),this.hasStart=!0,this.setState(F.ATTACHING),this._registerPlugins(!1),G.beforeInit(this).then(function(){if(i.config){e||(e=i.url||i.config.url);var n=i.preProcessUrl(e),s=i._startInit(n.url);return s}}).catch(function(n){throw n.fileName="player",n.lineNumber="236",R.logError("start:beforeInit:",n),n})}},{key:"switchURL",value:function(e,i){var n=this,s=e;f.typeOf(e)==="Object"&&(s=e.url),s=this.preProcessUrl(s).url;var l=this.currentTime;this.__startTime=l;var u=this.paused&&!this.isError;return this.src=s,new Promise(function(c,h){var d=function(m){n.off("timeupdate",g),n.off("canplay",g),h(m)},g=function(){n._seekToStartTime(),u&&n.pause(),n.off("error",d),c(!0)};if(n.once("error",d),!s){n.errorHandler("error",{code:6,message:"empty_src"});return}A.os.isAndroid?n.once("timeupdate",g):n.once("canplay",g),n.play()})}},{key:"videoPlay",value:function(){this.mediaPlay()}},{key:"mediaPlay",value:function(){var e=this;if(!this.hasStart&&this.state<F.ATTACHED){this.removeClass(C.NO_START),this.addClass(C.ENTER),this.start(),this._useAutoplay=!0;return}this.state<F.RUNNING&&(this.removeClass(C.NO_START),!this.isCanplay&&this.addClass(C.ENTER));var i=I(P(r.prototype),"play",this).call(this);return i!==void 0&&i&&i.then?i.then(function(){e.removeClass(C.NOT_ALLOW_AUTOPLAY),e.addClass(C.PLAYING),e.state<F.RUNNING&&(R.logInfo(">>>>playPromise.then"),e.setState(F.RUNNING),e.emit(Ee))}).catch(function(n){if(R.logWarn(">>>>playPromise.catch",n.name),e.media&&e.media.error){e.onError(),e.removeClass(C.ENTER);return}n.name==="NotAllowedError"&&(e._errorTimer=f.setTimeout(e,function(){e._errorTimer=null,e.emit(Ft),e.addClass(C.NOT_ALLOW_AUTOPLAY),e.removeClass(C.ENTER),e.pause(),e.setState(F.NOTALLOW)},0))}):(R.logWarn("video.play not return promise"),this.state<F.RUNNING&&(this.setState(F.RUNNING),this.removeClass(C.NOT_ALLOW_AUTOPLAY),this.removeClass(C.NO_START),this.removeClass(C.ENTER),this.addClass(C.PLAYING),this.emit(Ee))),i}},{key:"mediaPause",value:function(){I(P(r.prototype),"pause",this).call(this)}},{key:"videoPause",value:function(){I(P(r.prototype),"pause",this).call(this)}},{key:"play",value:function(){var e=this;return this.removeClass(C.PAUSED),oe(this,"play",function(){return e.mediaPlay()})}},{key:"pause",value:function(){var e=this;oe(this,"pause",function(){I(P(r.prototype),"pause",e).call(e)})}},{key:"seek",value:function(e,i){var n=this;if(!(!this.media||Number.isNaN(Number(e))||!this.hasStart)){var s=this.config,l=s.isSeekedPlay,u=s.seekedStatus,c=i||(l?"play":u);e=e<0?0:e>this.duration?parseInt(this.duration,10):e,!this._isPauseBeforeSeek&&(this._isPauseBeforeSeek=this.paused?2:1),this._onceSeekCanplay&&this.off(ce,this._onceSeekCanplay),this._onceSeekCanplay=function(){switch(n.removeClass(C.ENTER),n.isSeeking=!1,c){case"play":n.play();break;case"pause":n.pause();break;default:n._isPauseBeforeSeek>1||n.paused?n.pause():n.play()}n._isPauseBeforeSeek=0,n._onceSeekCanplay=null},this.once(ce,this._onceSeekCanplay),this.state<F.RUNNING?(this.removeClass(C.NO_START),this.currentTime=e,this.play()):this.currentTime=e}}},{key:"getInitDefinition",value:function(){var e=this,i=this.config,n=i.definition,s=i.url;!s&&n&&n.list&&n.list.length>0&&n.defaultDefinition&&n.list.map(function(l){l.definition===n.defaultDefinition&&(e.config.url=l.url,e.curDefinition=l)})}},{key:"changeDefinition",value:function(e,i){var n=this,s=this.config.definition;if(Array.isArray(s==null?void 0:s.list)&&s.list.forEach(function(u){(e==null?void 0:e.definition)===u.definition&&(n.curDefinition=u)}),e!=null&&e.bitrate&&typeof e.bitrate!="number"&&(e.bitrate=parseInt(e.bitrate,10)||0),this.emit(Bt,{from:i,to:e}),!this.hasStart){this.config.url=e.url;return}var l=this.switchURL(e.url,V({seamless:s.seamless!==!1&&typeof MediaSource<"u"&&typeof MediaSource.isTypeSupported=="function"},e));l&&l.then?l.then(function(){n.emit(St,{from:i,to:e})}):this.emit(St,{from:i,to:e})}},{key:"reload",value:function(){this.load(),this.reloadFunc=function(){this.play()},this.once(fe,this.reloadFunc)}},{key:"resetState",value:function(){var e=this,i=C.NOT_ALLOW_AUTOPLAY,n=C.PLAYING,s=C.NO_START,l=C.PAUSED,u=C.REPLAY,c=C.ENTER,h=C.ENDED,d=C.ERROR,g=C.LOADING,p=[i,n,s,l,u,c,h,d,g];this.hasStart=!1,this.isError=!1,this._useAutoplay=!1,this.mediaPause(),this._accPlayed.acc=0,this._accPlayed.t=0,this._accPlayed.loopAcc=0,p.forEach(function(m){e.removeClass(m)}),this.addClass(C.NO_START),this.emit(ot)}},{key:"reset",value:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0;this.resetState();var s=this.plugins;if(s&&(i.map(function(u){e.deregister(u)}),n)){var l=si();Object.keys(this.config).keys(function(u){e.config[u]!=="undefined"&&(u==="plugins"||u==="presets"||u==="el"||u==="id")&&(e.config[u]=l[u])})}}},{key:"destroy",value:function(){var e,i=this,n=this.innerContainer,s=this.root,l=this.media;if(!(!s||!l)){if(this.hasStart=!1,this._useAutoplay=!1,s.removeAttribute(ve),l.removeAttribute(ve),this.updateAcc("destroy"),this._unbindEvents(),this._detachSourceEvents(this.media),f.clearAllTimers(this),this.emit(Nt),(e=de)===null||e===void 0||e.remove(this),G.destroy(this),tn(this),I(P(r.prototype),"destroy",this).call(this),this.fullscreen&&this._fullscreenEl===this.root&&this.exitFullscreen(),n)for(var u=n.children,c=0;c<u.length;c++)n.removeChild(u[c]);!n&&l instanceof window.Node&&s.contains(l)&&!this.config.remainMediaAfterDestroy&&s.removeChild(l),["topBar","leftBar","rightBar","innerContainer"].map(function(d){i[d]&&s.removeChild(i[d]),i[d]=null});var h=s.className.split(" ");h.length>0?s.className=h.filter(function(d){return d.indexOf("xgplayer")<0}).join(" "):s.className="",this.removeAttribute("data-xgfill"),["isSeeking","isCanplay","isActive","cssfullscreen","fullscreen"].forEach(function(d){i[d]=!1})}}},{key:"replay",value:function(){var e=this;this.removeClass(C.ENDED),this.currentTime=0,this.isSeeking=!1,oe(this,"replay",function(){e.once(ce,function(){var i=e.mediaPlay();i&&i.catch&&i.catch(function(n){console.log(n)})}),e.emit(Xe),e.onPlay()})}},{key:"retry",value:function(){var e=this;this.removeClass(C.ERROR),this.addClass(C.LOADING),oe(this,"retry",function(){var i=e.currentTime,n=e.config.url,s=f.isMSE(e.media)?{url:n}:e.preProcessUrl(n);e.src=s.url,!e.config.isLive&&(e.currentTime=i),e.once(ue,function(){e.mediaPlay()})})}},{key:"changeFullStyle",value:function(e,i,n,s){e&&(s||(s=C.PARENT_FULLSCREEN),this._orgCss||(this._orgCss=f.filterStyleFromText(e)),f.addClass(e,n),i&&i!==e&&!this._orgPCss&&(this._orgPCss=f.filterStyleFromText(i),f.addClass(i,s),i.setAttribute(ve,this.playerId)))}},{key:"recoverFullStyle",value:function(e,i,n,s){s||(s=C.PARENT_FULLSCREEN),this._orgCss&&(f.setStyleFromCsstext(e,this._orgCss),this._orgCss=""),f.removeClass(e,n),i&&i!==e&&this._orgPCss&&(f.setStyleFromCsstext(i,this._orgPCss),this._orgPCss="",f.removeClass(i,s),i.removeAttribute(ve))}},{key:"getFullscreen",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.config.fullscreenTarget,i=this.root,n=this.media;(e==="video"||e==="media")&&(e=this[e]),e||(e=i),this._fullScreenOffset={top:f.scrollTop(),left:f.scrollLeft()},this._fullscreenEl=e,this._fullActionFrom="get";var s=f.getFullScreenEl();if(s===this._fullscreenEl)return this.onFullscreenChange(),Promise.resolve();try{for(var l=0;l<Qt.length;l++){var u=Qt[l];if(e[u]){var c=u==="webkitRequestFullscreen"?e.webkitRequestFullscreen(window.Element.ALLOW_KEYBOARD_INPUT):e[u]();return c&&c.then?c:Promise.resolve()}}return n.fullscreenEnabled||n.webkitSupportsFullscreen?(n.webkitEnterFullscreen(),Promise.resolve()):Promise.reject(new Error("call getFullscreen fail"))}catch(h){return Promise.reject(new Error("call getFullscreen fail"))}}},{key:"exitFullscreen",value:function(e){if(this.isRotateFullscreen&&this.exitRotateFullscreen(),!(!this._fullscreenEl&&!f.getFullScreenEl())){this.root;var i=this.media;this._fullActionFrom="exit";try{for(var n=0;n<ei.length;n++){var s=ei[n];if(document[s]){var l=document[s]();return l&&l.then?l:Promise.resolve()}}return i&&i.webkitSupportsFullscreen?(i.webkitExitFullScreen(),Promise.resolve()):Promise.reject(new Error("call exitFullscreen fail"))}catch(u){return Promise.reject(new Error("call exitFullscreen fail"))}}}},{key:"getCssFullscreen",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.config.fullscreenTarget;this.isRotateFullscreen?this.exitRotateFullscreen():this.fullscreen&&this.exitFullscreen();var i=e?"".concat(C.INNER_FULLSCREEN," ").concat(C.CSS_FULLSCREEN):C.CSS_FULLSCREEN;this.changeFullStyle(this.root,e,i);var n=this.config.fullscreen,s=n===void 0?{}:n,l=s.useCssFullscreen===!0||typeof s.useCssFullscreen=="function"&&s.useCssFullscreen();l&&(this.fullscreen=!0,this.emit(le,!0)),this._cssfullscreenEl=e,this.cssfullscreen=!0,this.emit(qe,!0)}},{key:"exitCssFullscreen",value:function(){var e=this._cssfullscreenEl?"".concat(C.INNER_FULLSCREEN," ").concat(C.CSS_FULLSCREEN):C.CSS_FULLSCREEN;if(!this.fullscreen)this.recoverFullStyle(this.root,this._cssfullscreenEl,e);else{var i=this.config.fullscreen,n=i===void 0?{}:i,s=n.useCssFullscreen===!0||typeof n.useCssFullscreen=="function"&&n.useCssFullscreen();s?(this.recoverFullStyle(this.root,this._cssfullscreenEl,e),this.fullscreen=!1,this.emit(le,!1)):this.removeClass(e)}this._cssfullscreenEl=null,this.cssfullscreen=!1,this.emit(qe,!1)}},{key:"getRotateFullscreen",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.config.fullscreenTarget;this.cssfullscreen&&this.exitCssFullscreen(e);var i=e?"".concat(C.INNER_FULLSCREEN," ").concat(C.ROTATE_FULLSCREEN):C.ROTATE_FULLSCREEN;this._fullscreenEl=e||this.root,this.changeFullStyle(this.root,e,i,C.PARENT_ROTATE_FULLSCREEN),this.isRotateFullscreen=!0,this.fullscreen=!0,this.setRotateDeg(90),this._rootStyle=this.root.getAttribute("style"),this.root.style.width="".concat(window.innerHeight,"px"),this.emit(le,!0)}},{key:"exitRotateFullscreen",value:function(e){var i=this._fullscreenEl!==this.root?"".concat(C.INNER_FULLSCREEN," ").concat(C.ROTATE_FULLSCREEN):C.ROTATE_FULLSCREEN;this.recoverFullStyle(this.root,this._fullscreenEl,i,C.PARENT_ROTATE_FULLSCREEN),this.isRotateFullscreen=!1,this.fullscreen=!1,this.setRotateDeg(0),this.emit(le,!1),this._rootStyle&&(this.root.style.style=this._rootStyle,this._rootStyle=!1)}},{key:"setRotateDeg",value:function(e){window.orientation===90||window.orientation===-90?this.rotateDeg=0:this.rotateDeg=e}},{key:"focus",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{autoHide:!this.config.closeDelayBlur,delay:this.config.inactive};if(this.isActive){this.onFocus(e);return}this.emit(Rt,V({paused:this.paused,ended:this.ended},e))}},{key:"blur",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{ignorePaused:!1};if(!this.isActive){this.onBlur(e);return}this._clearUserTimer(),this.emit(Hi,V({paused:this.paused,ended:this.ended},e))}},{key:"onFocus",value:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{autoHide:!0,delay:3e3},n=this.innerStates;if(this.isActive=!0,this.removeClass(C.INACTIVE),this._clearUserTimer(),i.isLock!==void 0&&(n.isActiveLocked=i.isLock),i.autoHide===!1||i.isLock===!0||n.isActiveLocked){this._clearUserTimer();return}var s=i&&i.delay?i.delay:this.config.inactive;this.userTimer=f.setTimeout(this,function(){e.userTimer=null,e.blur()},s)}},{key:"onBlur",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=e.ignorePaused,n=i===void 0?!1:i;if(!this.innerStates.isActiveLocked){var s=this.config.closePauseVideoFocus;this.isActive=!1,(n||s||!this.paused&&!this.ended)&&this.addClass(C.INACTIVE)}}},{key:"onEmptied",value:function(){this.updateAcc("emptied")}},{key:"onCanplay",value:function(){this.removeClass(C.ENTER),this.removeClass(C.ERROR),this.removeClass(C.LOADING),this.isCanplay=!0,this.waitTimer&&f.clearTimeout(this,this.waitTimer)}},{key:"onLoadeddata",value:function(){var e=this;this.isError=!1,this.isSeeking=!1,this.__startTime>0&&(this.duration>0?this._seekToStartTime():this.once(he,function(){e._seekToStartTime()}))}},{key:"onLoadstart",value:function(){this.removeClass(C.ERROR),this.isCanplay=!1}},{key:"onPlay",value:function(){this.state===F.ENDED&&this.setState(F.RUNNING),this.removeClass(C.PAUSED),this.ended&&this.removeClass(C.ENDED),!this.config.closePlayVideoFocus&&this.focus()}},{key:"onPause",value:function(){this.addClass(C.PAUSED),this.updateAcc("pause"),this.config.closePauseVideoFocus||(this._clearUserTimer(),this.focus())}},{key:"onEnded",value:function(){this.updateAcc("ended"),this.addClass(C.ENDED),this.setState(F.ENDED)}},{key:"onError",value:function(){this.isError=!0,this.updateAcc("error"),this.removeClass(C.NOT_ALLOW_AUTOPLAY),this.removeClass(C.NO_START),this.removeClass(C.ENTER),this.removeClass(C.LOADING),this.addClass(C.ERROR)}},{key:"onSeeking",value:function(){this.isSeeking||this.updateAcc("seeking"),this.isSeeking=!0,this.addClass(C.SEEKING)}},{key:"onSeeked",value:function(){this.isSeeking=!1,this.waitTimer&&f.clearTimeout(this,this.waitTimer),this.removeClass(C.LOADING),this.removeClass(C.SEEKING)}},{key:"onWaiting",value:function(){var e=this;this.waitTimer&&f.clearTimeout(this,this.waitTimer),this.updateAcc("waiting"),this.waitTimer=f.setTimeout(this,function(){e.addClass(C.LOADING),e.emit(Wi),f.clearTimeout(e,e.waitTimer),e.waitTimer=null},this.config.minWaitDelay)}},{key:"onPlaying",value:function(){var e=this;this.isError=!1;var i=C.NO_START,n=C.PAUSED,s=C.ENDED,l=C.ERROR,u=C.REPLAY,c=C.LOADING,h=[i,n,s,l,u,c];h.forEach(function(d){e.removeClass(d)}),!this._accPlayed.t&&!this.paused&&!this.ended&&(this._accPlayed.t=new Date().getTime())}},{key:"onTimeupdate",value:function(){!this._videoHeight&&this.media.videoHeight&&this.resize(),(this.waitTimer||this.hasClass(C.LOADING))&&this.media.readyState>2&&(this.removeClass(C.LOADING),f.clearTimeout(this,this.waitTimer),this.waitTimer=null),!this.paused&&this.state===F.NOTALLOW&&this.duration&&(this.setState(F.RUNNING),this.emit(Ee)),!this._accPlayed.t&&!this.paused&&!this.ended&&(this._accPlayed.t=new Date().getTime())}},{key:"onVolumechange",value:function(){f.typeOf(this.config.volume)==="Number"&&(this.config.volume=this.volume)}},{key:"onRatechange",value:function(){this.config.defaultPlaybackRate=this.playbackRate}},{key:"emitUserAction",value:function(e,i,n){if(!(!this.media||!i||!e)){var s=f.typeOf(e)==="String"?e:e.type||"";n.props&&f.typeOf(n.props)!=="Array"&&(n.props=[n.props]),this.emit(Qe,V({eventType:s,action:i,currentTime:this.currentTime,duration:this.duration,ended:this.ended,event:e},n))}}},{key:"updateAcc",value:function(e){if(this._accPlayed.t){var i=new Date().getTime()-this._accPlayed.t;this._accPlayed.acc+=i,this._accPlayed.t=0,(e==="ended"||this.ended)&&(this._accPlayed.loopAcc=this._accPlayed.acc)}}},{key:"checkBuffer",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{startDiff:0,endDiff:0},n=i||{},s=n.startDiff,l=s===void 0?0:s,u=n.endDiff,c=u===void 0?0:u,h=this.media.buffered;if(!h||h.length===0||!this.duration)return!0;for(var d=e||this.media.currentTime||.2,g=h.length,p=0;p<g;p++)if(h.start(p)+l<=d&&h.end(p)-c>d)return!0;return!1}},{key:"resizePosition",value:function(){var e=this,i=this.videoPos,n=i.vy,s=i.vx,l=i.h,u=i.w,c=this.videoPos.rotate;if(!(c<0&&l<0&&u<0)){var h=this.videoPos._pi;if(!h&&this.media.videoHeight&&(h=this.media.videoWidth/this.media.videoHeight*100),!!h){this.videoPos.pi=h,c=c<0?0:c;var d={rotate:c},g=0,p=0,m=1,_=Math.abs(c/90),b=this.root,T=this.innerContainer,k=b.offsetWidth,D=T?T.offsetHeight:b.offsetHeight,L={},B=D,M=k;if(_%2===0)m=l>0?100/l:u>0?100/u:1,d.scale=m,g=n>0?(100-l)/2-n:0,d.y=_===2?0-g:g,p=s>0?(100-u)/2-s:0,d.x=_===2?0-p:p,L.width="".concat(M,"px"),L.height="".concat(B,"px"),L.maxWidth="",L.maxHeight="";else if(_%2===1){M=D,B=k;var W=D-k;p=-W/2/M*100,d.x=_===3?p+n/2:p-n/2,g=W/2/B*100,d.y=_===3?g+s/2:g-s/2,d.scale=m,L.width="".concat(M,"px"),L.maxWidth="".concat(M,"px"),L.height="".concat(B,"px"),L.maxHeight="".concat(B,"px")}var j=f.getTransformStyle(d,this.media.style.transform||this.media.style.webkitTransform);L.transform=j,L.webkitTransform=j,Object.keys(L).map(function(O){e.media.style[O]=L[O]})}}}},{key:"position",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{h:0,y:0,x:0,w:0};if(!(!this.media||!e||!e.h)){var i=this.videoPos;i.h=e.h*100||0,i.w=e.w*100||0,i.vx=e.x*100||0,i.vy=e.y*100||0,this.resizePosition()}}},{key:"setConfig",value:function(e){var i=this;e&&Object.keys(e).map(function(n){if(n!=="plugins"){i.config[n]=e[n];var s=i.plugins[n.toLowerCase()];s&&f.typeOf(s.setConfig)==="Function"&&s.setConfig(e[n])}})}},{key:"playNext",value:function(e){var i=this;this.resetState(),this.setConfig(e),this._currentTime=0,this._duration=0,oe(this,"playnext",function(){i.start(),i.emit(Je,e)})}},{key:"resize",value:function(){var e=this;if(this.media){var i=this.root.getBoundingClientRect();this.sizeInfo.width=i.width,this.sizeInfo.height=i.height,this.sizeInfo.left=i.left,this.sizeInfo.top=i.top;var n=this.media,s=n.videoWidth,l=n.videoHeight,u=this.config,c=u.fitVideoSize,h=u.videoFillMode;if((h==="fill"||h==="cover"||h==="contain")&&this.setAttribute("data-xgfill",h),!(!l||!s)){this._videoHeight=l,this._videoWidth=s;var d=this.controls&&this.innerContainer?this.controls.root.getBoundingClientRect().height:0,g=i.width,p=i.height-d,m=parseInt(s/l*1e3,10),_=parseInt(g/p*1e3,10),b=g,T=p,k={};c==="auto"&&_>m||c==="fixWidth"?(T=g/m*1e3,this.config.fluid?k.paddingTop="".concat(T*100/b,"%"):k.height="".concat(T+d,"px")):(c==="auto"&&_<m||c==="fixHeight")&&(b=m*p/1e3,k.width="".concat(b,"px")),!this.fullscreen&&!this.cssfullscreen&&Object.keys(k).forEach(function(L){e.root.style[L]=k[L]}),(h==="fillHeight"&&_<m||h==="fillWidth"&&_>m)&&this.setAttribute("data-xgfill","cover");var D={videoScale:m,vWidth:b,vHeight:T,cWidth:b,cHeight:T+d};this.resizePosition(),this.emit(me,D)}}}},{key:"updateObjectPosition",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;if(this.media.updateObjectPosition){this.media.updateObjectPosition(e,i);return}this.media.style.objectPosition="".concat(e*100,"% ").concat(i*100,"%")}},{key:"setState",value:function(e){R.logInfo("setState","state from:".concat(ai[this.state]," to:").concat(ai[e])),this._state=e}},{key:"preProcessUrl",value:function(e,i){var n=this.config,s=n.preProcessUrl,l=n.preProcessUrlOptions,u=Object.assign({},l,i);return!f.isBlob(e)&&typeof s=="function"?s(e,u):{url:e}}},{key:"_seekToStartTime",value:function(){this.__startTime>0&&this.duration>0&&(this.currentTime=this.__startTime>this.duration?this.duration:this.__startTime,this.__startTime=-1)}},{key:"state",get:function(){return this._state}},{key:"isFullscreen",get:function(){return this.fullscreen}},{key:"isCssfullScreen",get:function(){return this.cssfullscreen}},{key:"hasStart",get:function(){return this._hasStart},set:function(e){typeof e=="boolean"&&(this._hasStart=e,e===!1&&this.setState(F.READY),this.emit("hasstart"))}},{key:"isPlaying",get:function(){return this._state===F.RUNNING||this._state===F.ENDED},set:function(e){e?this.setState(F.RUNNING):this._state>=F.RUNNING&&this.setState(F.ATTACHED)}},{key:"definitionList",get:function(){return!this.config||!this.config.definition?[]:this.config.definition.list||[]},set:function(e){var i=this,n=this.config.definition,s=null,l=null;n.list=e,this.emit("resourceReady",e),e.forEach(function(u){var c;((c=i.curDefinition)===null||c===void 0?void 0:c.definition)===u.definition&&(s=u),n.defaultDefinition===u.definition&&(l=u)}),!l&&e.length>0&&(l=e[0]),s?this.changeDefinition(s):l&&this.changeDefinition(l)}},{key:"videoFrameInfo",get:function(){var e={total:0,dropped:0,corrupted:0,droppedRate:0,droppedDuration:0};if(!this.media||!this.media.getVideoPlaybackQuality)return e;var i=this.media.getVideoPlaybackQuality();return e.dropped=i.droppedVideoFrames||0,e.total=i.totalVideoFrames||0,e.corrupted=i.corruptedVideoFrames||0,e.total>0&&(e.droppedRate=e.dropped/e.total*100,e.droppedDuration=parseInt(this.cumulateTime/e.total*e.dropped,0)),e}},{key:"lang",get:function(){return this.config.lang},set:function(e){var i=Re.langKeys.filter(function(n){return n===e});if(i.length===0&&e!=="zh"){console.error("Sorry, set lang fail, because the language [".concat(e,"] is not supported now, list of all supported languages is [").concat(Re.langKeys.join(),"] "));return}this.config.lang=e,G.setLang(e,this)}},{key:"i18n",get:function(){var e=this.config.lang;return e==="zh"&&(e="zh-cn"),this.__i18n.lang[e]||this.__i18n.lang.en}},{key:"i18nKeys",get:function(){return this.__i18n.textKeys||{}}},{key:"version",get:function(){return Et}},{key:"playerId",get:function(){return this._pluginInfoId}},{key:"url",get:function(){return this.__url||this.config.url},set:function(e){this.__url=e}},{key:"poster",get:function(){return this.plugins.poster?this.plugins.poster.config.poster:this.config.poster},set:function(e){this.plugins.poster&&this.plugins.poster.update(e)}},{key:"readyState",get:function(){return I(P(r.prototype),"readyState",this)}},{key:"error",get:function(){var e=I(P(r.prototype),"error",this);return this.i18n[e]||e}},{key:"networkState",get:function(){return I(P(r.prototype),"networkState",this)}},{key:"fullscreenChanging",get:function(){return this._fullScreenOffset!==null}},{key:"cumulateTime",get:function(){var e=this._accPlayed,i=e.acc,n=e.t;return n?new Date().getTime()-n+i:i}},{key:"zoom",get:function(){return this.config.zoom},set:function(e){this.config.zoom=e}},{key:"videoRotateDeg",get:function(){return this.videoPos.rotate},set:function(e){e=f.convertDeg(e),!(e%90!==0||e===this.videoPos.rotate)&&(this.videoPos.rotate=e,this.resizePosition())}},{key:"avgSpeed",get:function(){return li},set:function(e){li=e}},{key:"realTimeSpeed",get:function(){return oi},set:function(e){oi=e}},{key:"offsetCurrentTime",get:function(){return this._offsetInfo.currentTime||0},set:function(e){this._offsetInfo.currentTime=e}},{key:"offsetDuration",get:function(){return this._offsetInfo.duration||0},set:function(e){this._offsetInfo.duration=e||0}},{key:"hook",value:function(e,i){return et.call.apply(et,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"useHooks",value:function(e,i){return tt.call.apply(tt,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removeHooks",value:function(e,i){return it.call.apply(it,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"usePluginHooks",value:function(e,i,n){for(var s=arguments.length,l=new Array(s>3?s-3:0),u=3;u<s;u++)l[u-3]=arguments[u];return ti.call.apply(ti,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removePluginHooks",value:function(e,i,n){for(var s=arguments.length,l=new Array(s>3?s-3:0),u=3;u<s;u++)l[u-3]=arguments[u];return ii.call.apply(ii,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"setUserActive",value:function(e,i){var n;typeof i=="boolean"&&i!==this.muted&&(this.addInnerOP("volumechange"),f.typeOf(i)===Boolean&&(this.muted=i)),(n=de)===null||n===void 0||n.setActive(this.playerId,e)}}],[{key:"debugger",get:function(){return R.config.debug},set:function(e){R.config.debug=e}},{key:"instManager",get:function(){return de},set:function(e){de=e}},{key:"getCurrentUserActivePlayerId",value:function(){var e;return(e=de)===null||e===void 0?void 0:e.getActiveId()}},{key:"setCurrentUserActive",value:function(e,i){var n;(n=de)===null||n===void 0||n.setActive(e,i)}},{key:"isHevcSupported",value:function(){return A.isHevcSupported()}},{key:"probeConfigSupported",value:function(e){return A.probeConfigSupported(e)}},{key:"install",value:function(e,i){r.plugins||(r.plugins={}),r.plugins[e]||(r.plugins[e]=i)}},{key:"use",value:function(e,i){r.plugins||(r.plugins={}),r.plugins[e]=i}}]),r}(fr);y(lt,"defaultPreset",null);y(lt,"XgVideoProxy",null);lt.instManager=rn.getInstance();function _e(o,a,r){try{return' <div class="xg-tips '.concat(r?"hide":" ",'" lang-key="').concat(o.i18nKeys[a],'">\n    ').concat(o.i18n[a],"\n    </div>")}catch(t){return'<div class="xg-tips hide"></div>'}}var xe=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"_onMouseenter",function(s){t.emit("icon_mouseenter",{pluginName:t.pluginName})}),y(v(t),"_onMouseLeave",function(s){t.emit("icon_mouseleave",{pluginName:t.pluginName})}),t}return w(r,[{key:"afterCreate",value:function(){this.bind("mouseenter",this._onMouseenter),this.bind("mouseleave",this._onMouseLeave),this.config.disable&&this.disable()}},{key:"destroy",value:function(){this.unbind("mouseenter",this._onMouseenter),this.unbind("mouseleave",this._onMouseLeave)}}]),r}(N);function Pr(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 -5 31 40">\n  <path fill="#fff" transform="scale(1.3, 1.3)" class=\'path_full\' d="M9,10v1a.9.9,0,0,1-1,1,.9.9,0,0,1-1-1V9A.9.9,0,0,1,8,8h2a.9.9,0,0,1,1,1,.9.9,0,0,1-1,1Zm6,4V13a1,1,0,0,1,2,0v2a.9.9,0,0,1-1,1H14a1,1,0,0,1,0-2Zm3-7H6V17H18Zm2,0V17a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V7A2,2,0,0,1,6,5H18A2,2,0,0,1,20,7Z"></path>\n</svg>\n',"image/svg+xml").firstChild}function Ir(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 -5 31 40">\n  <path fill="#fff" transform="scale(1.3, 1.3)" d="M9,10V9a.9.9,0,0,1,1-1,.9.9,0,0,1,1,1v2a.9.9,0,0,1-1,1H8a.9.9,0,0,1-1-1,.9.9,0,0,1,1-1Zm6,4v1a1,1,0,0,1-2,0V13a.9.9,0,0,1,1-1h2a1,1,0,0,1,0,2Zm3-7H6V17H18Zm2,0V17a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V7A2,2,0,0,1,6,5H18A2,2,0,0,1,20,7Z"></path>\n</svg>\n',"image/svg+xml").firstChild}var Pt=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"beforeCreate",value:function(e){typeof e.player.config.cssFullscreen=="boolean"&&(e.config.disable=!e.player.config.cssFullscreen)}},{key:"afterCreate",value:function(){var e=this;I(P(r.prototype),"afterCreate",this).call(this),!this.config.disable&&(this.config.target&&(this.playerConfig.fullscreenTarget=this.config.target),this.initIcons(),this.on(qe,function(i){e.animate(i)}),this.btnClick=this.btnClick.bind(this),this.handleCssFullscreen=this.hook("cssFullscreen_change",this.btnClick,{pre:function(n){n.preventDefault(),n.stopPropagation()}}),this.bind(["click","touchend"],this.handleCssFullscreen))}},{key:"initIcons",value:function(){var e=this.icons,i=this.find(".xgplayer-icon");i.appendChild(e.cssFullscreen),i.appendChild(e.exitCssFullscreen)}},{key:"btnClick",value:function(e){e.preventDefault(),e.stopPropagation();var i=this.player.isCssfullScreen;this.emitUserAction(e,"switch_cssfullscreen",{cssfullscreen:i}),i?this.player.exitCssFullscreen():this.player.getCssFullscreen()}},{key:"animate",value:function(e){this.root&&(e?this.setAttr("data-state","full"):this.setAttr("data-state","normal"),this.switchTips(e))}},{key:"switchTips",value:function(e){var i=this.i18nKeys,n=this.find(".xg-tips");n&&this.changeLangTextKey(n,e?i.EXITCSSFULLSCREEN_TIPS:i.CSSFULLSCREEN_TIPS)}},{key:"registerIcons",value:function(){return{cssFullscreen:{icon:Pr,class:"xg-get-cssfull"},exitCssFullscreen:{icon:Ir,class:"xg-exit-cssfull"}}}},{key:"destroy",value:function(){I(P(r.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.btnClick)}},{key:"render",value:function(){if(!this.config.disable)return"<xg-icon class='xgplayer-cssfullscreen'>\n    <div class=\"xgplayer-icon\">\n    </div>\n    ".concat(_e(this,"CSSFULLSCREEN_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"cssFullscreen"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_RIGHT,index:1,disable:!1,target:null}}}]),r}(xe);function Lr(){return new DOMParser().parseFromString('<svg width="32px" height="40px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <path d="M11.2374369,14 L17.6187184,7.61871843 C17.9604272,7.27700968 17.9604272,6.72299032 17.6187184,6.38128157 C17.2770097,6.03957281 16.7229903,6.03957281 16.3812816,6.38128157 L9.38128157,13.3812816 C9.03957281,13.7229903 9.03957281,14.2770097 9.38128157,14.6187184 L16.3812816,21.6187184 C16.7229903,21.9604272 17.2770097,21.9604272 17.6187184,21.6187184 C17.9604272,21.2770097 17.9604272,20.7229903 17.6187184,20.3812816 L11.2374369,14 L11.2374369,14 Z" fill="#FFFFFF"></path>\n    </g>\n</svg>',"image/svg+xml").firstChild}var Ar=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"afterCreate",value:function(){var e=this;this.initIcons(),this.onClick=function(i){i.preventDefault(),i.stopPropagation(),e.config.onClick(i)},this.bind(["click","touchend"],this.onClick)}},{key:"registerIcons",value:function(){return{screenBack:{icon:Lr,class:"xg-fullscreen-back"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(this.root,e.screenBack)}},{key:"show",value:function(){f.addClass(this.root,"show")}},{key:"hide",value:function(){f.removeClass(this.root,"show")}},{key:"render",value:function(){return'<xg-icon class="xgplayer-back">\n    </xg-icon>'}}],[{key:"pluginName",get:function(){return"topbackicon"}},{key:"defaultConfig",get:function(){return{position:H.ROOT_TOP,index:0}}}]),r}(N);function Dr(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="2 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M598 214h212v212h-84v-128h-128v-84zM726 726v-128h84v212h-212v-84h128zM214 426v-212h212v84h-128v128h-84zM298 598v128h128v84h-212v-212h84z"></path>\n</svg>\n',"image/svg+xml").firstChild}function Or(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="2 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M682 342h128v84h-212v-212h84v128zM598 810v-212h212v84h-128v128h-84zM342 342v-128h84v212h-212v-84h128zM214 682v-84h212v212h-84v-128h-128z"></path>\n</svg>\n',"image/svg+xml").firstChild}var sn=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"_onOrientationChange",function(s){t.player.fullscreen&&t.config.rotateFullscreen&&(window.orientation===90||window.orientation===-90?t.player.setRotateDeg(0):t.player.setRotateDeg(90))}),t}return w(r,[{key:"afterCreate",value:function(){var e=this;I(P(r.prototype),"afterCreate",this).call(this);var i=this.config,n=this.playerConfig;if(!i.disable){i.target&&(this.playerConfig.fullscreenTarget=this.config.target);var s=f.getFullScreenEl();n.fullscreenTarget===s&&this.player.getFullscreen().catch(function(l){}),this.initIcons(),this.handleFullscreen=this.hook("fullscreenChange",this.toggleFullScreen,{pre:function(u){var c=e.player.fullscreen;e.emitUserAction(u,"switch_fullscreen",{prop:"fullscreen",from:c,to:!c})}}),this.bind(".xgplayer-fullscreen",["touchend","click"],this.handleFullscreen),this.on(le,function(l){var u=e.find(".xg-tips");u&&e.changeLangTextKey(u,l?e.i18nKeys.EXITFULLSCREEN_TIPS:e.i18nKeys.FULLSCREEN_TIPS),e.animate(l)}),this.config.needBackIcon&&(this.topBackIcon=this.player.registerPlugin({plugin:Ar,options:{config:{onClick:function(u){e.handleFullscreen(u)}}}})),A.device==="mobile"&&window.addEventListener("orientationchange",this._onOrientationChange)}}},{key:"registerIcons",value:function(){return{fullscreen:{icon:Dr,class:"xg-get-fullscreen"},exitFullscreen:{icon:Or,class:"xg-exit-fullscreen"}}}},{key:"destroy",value:function(){I(P(r.prototype),"destroy",this).call(this),this.unbind(".xgplayer-icon",A.device==="mobile"?"touchend":"click",this.handleFullscreen),A.device==="mobile"&&window.removeEventListener("orientationchange",this._onOrientationChange)}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.fullscreen),this.appendChild(".xgplayer-icon",e.exitFullscreen)}},{key:"toggleFullScreen",value:function(e){e instanceof Event&&(e.preventDefault(),e.stopPropagation());var i=this.player,n=this.config,s=n.useCssFullscreen===!0||typeof n.useCssFullscreen=="function"&&n.useCssFullscreen();s?(i.fullscreen?i.exitCssFullscreen():i.getCssFullscreen(),this.animate(i.fullscreen)):n.rotateFullscreen?(i.fullscreen?i.exitRotateFullscreen():i.getRotateFullscreen(),this.animate(i.fullscreen)):n.switchCallback&&typeof n.switchCallback=="function"?n.switchCallback(i.fullscreen):i.fullscreen?(i.exitFullscreen(),n.useScreenOrientation&&this.unlockScreen()):(i.getFullscreen().catch(function(l){}),n.useScreenOrientation&&i.aspectRatio>1&&this.lockScreen(n.lockOrientationType))}},{key:"animate",value:function(e){e?this.setAttr("data-state","full"):this.setAttr("data-state","normal"),this.topBackIcon&&(e?(this.topBackIcon.show(),this.hide()):(this.topBackIcon.hide(),this.show()))}},{key:"render",value:function(){if(!this.config.disable){var e="FULLSCREEN_TIPS";return'<xg-icon class="xgplayer-fullscreen">\n    <div class="xgplayer-icon">\n    </div>\n    '.concat(_e(this,e,this.playerConfig.isHideTips),"\n    </xg-icon>")}}},{key:"lockScreen",value:function(e){try{screen.orientation.lock(e).catch(function(i){})}catch(i){}}},{key:"unlockScreen",value:function(){try{screen.orientation.unlock().catch(function(e){})}catch(e){}}}],[{key:"pluginName",get:function(){return"fullscreen"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_RIGHT,index:0,useCssFullscreen:!1,rotateFullscreen:!1,useScreenOrientation:!1,lockOrientationType:"landscape",switchCallback:null,target:null,disable:!1,needBackIcon:!1}}}]),r}(xe);function Ut(){return new DOMParser().parseFromString('<svg class="play" xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="3 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"></path>\n</svg>\n',"image/svg+xml").firstChild}function Vt(){return new DOMParser().parseFromString('<svg class="pause" xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="3 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M598,214h170v596h-170v-596zM256 810v-596h170v596h-170z"></path>\n</svg>\n',"image/svg+xml").firstChild}var an=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"btnClick",function(s){s.preventDefault(),s.stopPropagation();var l=v(t),u=l.player;return t.emitUserAction(s,"switch_play_pause",{prop:"paused",from:u.paused,to:!u.paused}),u.ended?u.replay():u.paused?(u.play(),t.animate(!1)):(u.pause(),t.animate(!0)),!1}),t}return w(r,[{key:"afterCreate",value:function(){I(P(r.prototype),"afterCreate",this).call(this);var e=this.config;e.disable||(this.initIcons(),this.bind(["touchend","click"],this.btnClick),this.listenEvents(),this.animate(!0))}},{key:"listenEvents",value:function(){var e=this,i=this.player;this.on([Q,Me,Ve,Se],function(){e.animate(i.paused)})}},{key:"registerIcons",value:function(){return{play:{icon:Ut,class:"xg-icon-play"},pause:{icon:Vt,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.play),this.appendChild(".xgplayer-icon",e.pause)}},{key:"animate",value:function(e){if(this.player){var i=this.i18nKeys,n=this.find(".xg-tips");e?(this.setAttr("data-state","pause"),n&&this.changeLangTextKey(n,i.PLAY_TIPS)):(this.setAttr("data-state","play"),n&&this.changeLangTextKey(n,i.PAUSE_TIPS))}}},{key:"destroy",value:function(){I(P(r.prototype),"destroy",this).call(this),this.unbind(["touchend","click"],this.btnClick)}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-play">\n    <div class="xgplayer-icon">\n    </div>\n    '.concat(_e(this,"PLAY_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"play"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_LEFT,index:0,disable:!1}}}]),r}(xe),Rr=[{tag:"xg-cache",className:"xgplayer-progress-cache",styleKey:"cachedColor"},{tag:"xg-played",className:"xgplayer-progress-played",styleKey:"playedColor"}],Mr=function(){function o(a){E(this,o),this.fragments=a.fragments||[],this.fragments.length===0&&this.fragments.push({percent:1}),this._callBack=a.actionCallback,this.fragConfig={fragFocusClass:a.fragFocusClass||"inner-focus-point",fragAutoFocus:!!a.fragAutoFocus,fragClass:a.fragClass||""},this.style=a.style||{playedColor:"",cachedColor:"",progressColor:""},this.duration=0,this.cachedIndex=0,this.playedIndex=0,this.focusIndex=-1}return w(o,[{key:"updateDuration",value:function(r){var t=this;this.duration=r;var e=0,i=this.fragments;this.fragments=i.map(function(n){return n.start=parseInt(e,10),n.end=parseInt(e+n.percent*t.duration,10),n.duration=parseInt(n.percent*t.duration,10),e+=n.percent*t.duration,n})}},{key:"updateProgress",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"played",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{newIndex:0,curIndex:0,millisecond:0},e=this.progressList,i=this.fragments;if(!(e.length<1)){var n=t.newIndex,s=t.curIndex,l=t.millisecond;n!==s&&e.map(function(h,d){d<n?h[r].style.width="100%":d>n&&(h[r].style.width=0)});var u=i[n],c=l===0?0:(l-u.start)/u.duration;e[n][r].style.width=c<0?0:"".concat(c*100,"%")}}},{key:"updateFocus",value:function(r){if(!(!this.fragConfig.fragAutoFocus||this.fragments.length<2)){if(!r){if(this.focusIndex>-1){this.unHightLight(this.focusIndex);var t={index:-1,preIndex:this.focusIndex,fragment:null};this._callBack&&this._callBack(t),this.focusIndex=-1}return}var e=this.findIndex(r.currentTime*1e3,this.focusIndex);if(e>=0&&e!==this.focusIndex){this.focusIndex>-1&&this.unHightLight(this.focusIndex),this.setHightLight(e);var i={index:e,preIndex:this.focusIndex,fragment:this.fragments[this.focusIndex]};this.focusIndex=e,this._callBack&&this._callBack(i)}}}},{key:"update",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{cached:0,played:0},t=arguments.length>1?arguments[1]:void 0;if(!this.duration||parseInt(t*1e3,10)!==this.duration){if(!t&&t!==0)return;this.updateDuration(parseInt(t*1e3,10))}var e=this.playedIndex,i=this.cachedIndex;if(f.typeOf(r.played)!=="Undefined"){var n=this.findIndex(r.played*1e3,e);if(n<0)return;this.updateProgress("played",{newIndex:n,curIndex:e,millisecond:parseInt(r.played*1e3,10)}),this.playedIndex=n}if(f.typeOf(r.cached)!=="Undefined"){var s=this.findIndex(r.cached*1e3,i);if(s<0)return;this.updateProgress("cached",{newIndex:s,curIndex:i,millisecond:parseInt(r.cached*1e3,10)}),this.cachedIndex=s}}},{key:"findIndex",value:function(r,t){var e=this.fragments;if(!e||e.length===0)return-1;if(e.length===1)return 0;if(t>-1&&t<e.length&&r>e[t].start&&r<e[t].end)return t;if(r>e[e.length-1].start)return e.length-1;for(var i=0;i<e.length;i++)if(r>e[i].start&&r<=e[i].end){t=i;break}return t}},{key:"findHightLight",value:function(){for(var r=this.root.children,t=0;t<r.length;t++)if(f.hasClass(r[t],this.fragConfig.fragFocusClass))return{dom:r[t],pos:r[t].getBoundingClientRect()}}},{key:"findFragment",value:function(r){var t=this.root.children;return r<0||r>=t.length?null:{dom:t[r],pos:t[r].getBoundingClientRect()}}},{key:"unHightLight",value:function(){for(var r=this.root.children,t=0;t<r.length;t++)f.removeClass(r[t],this.fragConfig.fragFocusClass)}},{key:"setHightLight",value:function(r){var t=this.root.children;if(r<t.length)return f.addClass(t[r],this.fragConfig.fragFocusClass),{dom:t[r],pos:t[r].getBoundingClientRect()}}},{key:"destroy",value:function(){this.progressList=null,this.fragments=null,this.root.innerHTML=""}},{key:"reset",value:function(r){var t=this;if(Object.keys(this.fragConfig).forEach(function(i){r[i]!==void 0&&(t.fragConfig[i]=r[i])}),r.fragments){if(this.fragments=r.fragments.length===0?[{percent:1}]:r.fragments,this.updateDuration(this.duration),this.playedIndex=0,this.cachedIndex=0,this.root)for(var e=this.root.children;e.length>0;)this.root.removeChild(e[0]);this.render()}}},{key:"render",value:function(){var r=this,t=this.style.progressColor;if(this.root||(this.root=f.createDom("xg-inners","",{},"progress-list")),this.fragments){var e=this.fragConfig,i=e.fragClass,n=e.fragFocusClass;this.progressList=this.fragments.map(function(s){var l=f.createDom("xg-inner","",{style:t?"background:".concat(t,"; flex: ").concat(s.percent):"flex: ".concat(s.percent)},"".concat(s.isFocus?n:""," xgplayer-progress-inner ").concat(i));return r.root.appendChild(l),Rr.forEach(function(u){l.appendChild(f.createDom(u.tag,"",{style:u.styleKey?"background: ".concat(r.style[u.styleKey],"; width:0;"):"width:0;"},u.className))}),{cached:l.children[0],played:l.children[1]}})}return this.root}}]),o}(),ui={POINT:"inner-focus-point",HIGHLIGHT:"inner-focus-highlight"},on=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),y(v(e),"onMoveOnly",function(i,n){var s=v(e),l=s.pos,u=s.config,c=s.player,h=n;if(i){f.event(i);var d=f.getEventPos(i,c.zoom),g=c.rotateDeg===90?d.clientY:d.clientX;if(l.moving&&Math.abs(l.x-g)<u.miniMoveStep)return;l.moving=!0,l.x=g,h=e.computeTime(i,g)}e.triggerCallbacks("dragmove",h,i),e._updateInnerFocus(h)}),y(v(e),"onBodyClick",function(i){e.pos.isLocked&&(e.pos.isLocked=!1,i.preventDefault(),i.stopPropagation())}),y(v(e),"_mouseDownHandler",function(i,n){e._state.time=n.currentTime,e.updateWidth(n.currentTime,n.seekTime,n.percent,0),e._updateInnerFocus(n)}),y(v(e),"_mouseUpHandler",function(i,n){var s=v(e),l=s.pos;l.moving&&e.updateWidth(n.currentTime,n.seekTime,n.percent,2)}),y(v(e),"_mouseMoveHandler",function(i,n){var s=v(e),l=s._state,u=s.pos,c=s.config,h=s.player;l.time<n.currentTime?n.forward=!0:n.forward=!1,l.time=n.currentTime,u.isDown&&!u.moving&&(u.moving=!0,c.isPauseMoving&&h.pause(),e.triggerCallbacks("dragstart",n,i),e.emitUserAction("drag","dragstart",n)),e.updateWidth(n.currentTime,n.seekTime,n.percent,1),e.triggerCallbacks("dragmove",n,i),e._updateInnerFocus(n)}),y(v(e),"onMouseDown",function(i){var n=v(e),s=n._state,l=n.player,u=n.pos,c=n.config,h=n.playerConfig,d=f.getEventPos(i,l.zoom),g=l.rotateDeg===90?d.clientY:d.clientX;if(!(l.isMini||c.closeMoveSeek||!h.allowSeekAfterEnded&&l.ended)){if(!l.duration&&!l.isPlaying){l.play();return}i.stopPropagation(),e.focus(),f.checkIsFunction(h.disableSwipeHandler)&&h.disableSwipeHandler(),f.checkIsFunction(c.onMoveStart)&&c.onMoveStart(),f.event(i),u.x=g,u.isDown=!0,u.moving=!1,s.prePlayTime=l.currentTime,l.focus({autoHide:!1}),e.isProgressMoving=!0,f.addClass(e.progressBtn,"active");var p=e.computeTime(i,g);p.prePlayTime=s.prePlayTime,e._mouseDownHandlerHook(i,p);var m=i.type;return m==="touchstart"?(e.root.addEventListener("touchmove",e.onMouseMove),e.root.addEventListener("touchend",e.onMouseUp),e.root.addEventListener("touchcancel",e.onMouseUp)):(e.unbind("mousemove",e.onMoveOnly),document.addEventListener("mousemove",e.onMouseMove,!1),document.addEventListener("mouseup",e.onMouseUp,!1)),!0}}),y(v(e),"onMouseUp",function(i){var n=v(e),s=n.player,l=n.config,u=n.pos,c=n.playerConfig,h=n._state;if(u){i.stopPropagation(),i.preventDefault(),f.checkIsFunction(c.enableSwipeHandler)&&c.enableSwipeHandler(),f.checkIsFunction(l.onMoveEnd)&&l.onMoveEnd(),f.event(i),f.removeClass(e.progressBtn,"active");var d=e.computeTime(i,u.x);d.prePlayTime=h.prePlayTime,u.moving?(e.triggerCallbacks("dragend",d,i),e.emitUserAction("drag","dragend",d)):(e.triggerCallbacks("click",d,i),e.emitUserAction("click","click",d)),e._mouseUpHandlerHook(i,d),u.moving=!1,u.isDown=!1,u.x=0,u.y=0,u.isLocked=!0,h.prePlayTime=0,h.time=0;var g=i.type;g==="touchend"||g==="touchcancel"?(e.root.removeEventListener("touchmove",e.onMouseMove),e.root.removeEventListener("touchend",e.onMouseUp),e.root.removeEventListener("touchcancel",e.onMouseUp),e.blur()):(document.removeEventListener("mousemove",e.onMouseMove,!1),document.removeEventListener("mouseup",e.onMouseUp,!1),u.isEnter?c.isMobileSimulateMode!=="mobile"&&e.bind("mousemove",e.onMoveOnly):e.onMouseLeave(i)),f.setTimeout(v(e),function(){e.resetSeekState()},1),s.focus()}}),y(v(e),"onMouseMove",function(i){var n=v(e),s=n._state,l=n.pos,u=n.player,c=n.config;f.checkTouchSupport()&&i.preventDefault(),f.event(i);var h=f.getEventPos(i,u.zoom),d=u.rotateDeg===90?h.clientY:h.clientX,g=Math.abs(l.x-d);if(!(l.moving&&g<c.miniMoveStep||!l.moving&&g<c.miniStartStep)){l.x=d;var p=e.computeTime(i,d);p.prePlayTime=s.prePlayTime,e._mouseMoveHandlerHook(i,p)}}),y(v(e),"onMouseOut",function(i){e.triggerCallbacks("mouseout",null,i)}),y(v(e),"onMouseOver",function(i){e.triggerCallbacks("mouseover",null,i)}),y(v(e),"onMouseEnter",function(i){var n=v(e),s=n.player,l=n.pos;if(!(l.isDown||l.isEnter||s.isMini||!s.config.allowSeekAfterEnded&&s.ended)){l.isEnter=!0,e.bind("mousemove",e.onMoveOnly),e.bind("mouseleave",e.onMouseLeave),f.event(i);var u=f.getEventPos(i,s.zoom),c=s.rotateDeg===90?u.clientY:u.clientX,h=e.computeTime(i,c);e.triggerCallbacks("mouseenter",h,i),e.focus()}}),y(v(e),"onMouseLeave",function(i){e.triggerCallbacks("mouseleave",null,i),e.unlock(),e._updateInnerFocus(null)}),y(v(e),"onVideoResize",function(){var i=e.pos,n=i.x,s=i.isDown,l=i.isEnter;if(l&&!s){var u=e.computeTime(null,n);e.onMoveOnly(null,u)}}),e.useable=!1,e.isProgressMoving=!1,e.__dragCallBacks=[],e._state={now:-1,direc:0,time:0,prePlayTime:-1},e._disableBlur=!1,typeof e.config.isDragingSeek=="boolean"&&(console.warn("[XGPLAYER] 'isDragingSeek' is deprecated, please use 'isDraggingSeek' instead"),e.config.isDraggingSeek=e.config.isDragingSeek),e}return w(r,[{key:"offsetDuration",get:function(){return this.playerConfig.customDuration||this.player.offsetDuration||this.player.duration}},{key:"duration",get:function(){return this.playerConfig.customDuration||this.player.duration}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"currentTime",get:function(){var e=this.player,i=e.offsetCurrentTime,n=e.currentTime;return i>=0?i:n+this.timeOffset}},{key:"changeState",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.useable=e}},{key:"show",value:function(e){this.root&&(this.root.style.display="flex")}},{key:"_initInner",value:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};(!i||i.length===0)&&(i=[{percent:1}]);var s=V(V({fragments:i},n),{},{actionCallback:function(u){e.emitUserAction("fragment_focus","fragment_focus",u)}});this.innerList?this.innerList.reset(s):(this.innerList=new Mr(s),this.outer.insertBefore(this.innerList.render(),this.outer.children[0]),["findHightLight","unHightLight","setHightLight","findFragment"].map(function(l){e[l]=e.innerList[l].bind(e.innerList)}))}},{key:"_updateInnerFocus",value:function(e){this.innerList&&this.innerList.updateFocus(e)}},{key:"afterCreate",value:function(){if(!(this.config.disable||this.playerConfig.isLive)){this.pos={x:0,y:0,moving:!1,isDown:!1,isEnter:!1,isLocked:!1},this.outer=this.find("xg-outer");var e=this.config,i=e.fragFocusClass,n=e.fragAutoFocus,s=e.fragClass;this._initInner(this.config.fragments,{fragFocusClass:i,fragAutoFocus:n,fragClass:s,style:this.playerConfig.commonStyle||{}}),A.device==="mobile"&&(this.config.isDraggingSeek=!1,this.isMobile=!0),this.progressBtn=this.find(".xgplayer-progress-btn"),this.listenEvents(),this.bindDomEvents(),this.initCustomStyle()}}},{key:"listenEvents",value:function(){var e=this;this.on(he,function(){e.onMouseLeave()}),this.on(J,function(){e.onTimeupdate()}),this.on(ce,function(){e.onTimeupdate(),e.onCacheUpdate()}),this.on(Bi,function(){e.onCacheUpdate()}),this.on(we,function(){e.onCacheUpdate(!0),e.onTimeupdate(!0),e._state.now=0}),this.on(Se,function(){e.onReset()}),this.on(me,function(){e.onVideoResize()})}},{key:"setConfig",value:function(e){var i=this,n=null;Object.keys(e).forEach(function(s){i.config[s]=e[s],s==="fragments"&&(n=e[s])}),n&&this._initInner(n,e)}},{key:"initCustomStyle",value:function(){var e=this.playerConfig||{},i=e.commonStyle,n=i.sliderBtnStyle,s=this.progressBtn;n&&(typeof n=="string"?s.style.boxShadow=n:$(n)==="object"&&Object.keys(n).map(function(l){s.style[l]=n[l]}))}},{key:"triggerCallbacks",value:function(e,i,n){this.__dragCallBacks.length>0&&this.__dragCallBacks.map(function(s){if(s&&s.handler&&s.type===e)try{s.handler(i,n)}catch(l){console.error("[XGPLAYER][triggerCallbacks] ".concat(s," error"),l)}})}},{key:"addCallBack",value:function(e,i){i&&typeof i=="function"&&this.__dragCallBacks.push({type:e,handler:i})}},{key:"removeCallBack",value:function(e,i){var n=this.__dragCallBacks,s=-1;n.map(function(l,u){l&&l.type===e&&l.handler===i&&(s=u)}),s>-1&&n.splice(s,1)}},{key:"unlock",value:function(){var e=this.player,i=this.pos;if(i.isEnter=!1,i.isLocked=!1,!e.isMini){if(this.unbind("mousemove",this.onMoveOnly),i.isDown){this.unbind("mouseleave",this.onMouseLeave);return}this.blur()}}},{key:"bindDomEvents",value:function(){var e=this.player.config;this._mouseDownHandlerHook=this.hook("dragstart",this._mouseDownHandler),this._mouseUpHandlerHook=this.hook("dragend",this._mouseUpHandler),this._mouseMoveHandlerHook=this.hook("drag",this._mouseMoveHandler),(this.domEventType==="touch"||this.domEventType==="compatible")&&this.root.addEventListener("touchstart",this.onMouseDown),(this.domEventType==="mouse"||this.domEventType==="compatible")&&(this.bind("mousedown",this.onMouseDown),e.isMobileSimulateMode!=="mobile"&&this.bind("mouseenter",this.onMouseEnter),this.bind("mouseover",this.onMouseOver),this.bind("mouseout",this.onMouseOut),this.player.root.addEventListener("click",this.onBodyClick,!0))}},{key:"focus",value:function(){this.player.controls.pauseAutoHide(),f.addClass(this.root,"active")}},{key:"blur",value:function(){this._disableBlur||(this.player.controls.recoverAutoHide(),f.removeClass(this.root,"active"))}},{key:"disableBlur",value:function(){this._disableBlur=!0}},{key:"enableBlur",value:function(){this._disableBlur=!1}},{key:"updateWidth",value:function(e,i,n,s){var l=this.config,u=this.player;if(!(l.isCloseClickSeek&&s===0)){var c=i=i>=u.duration?u.duration-l.endedDiff:Number(i).toFixed(1);this.updatePercent(n),this.updateTime(e),!(s===1&&(!l.isDraggingSeek||u.config.mediaType==="audio"))&&(this._state.now=c,this._state.direc=c>u.currentTime?0:1,u.seek(c))}}},{key:"computeTime",value:function(e,i){var n=this.player,s=this.root.getBoundingClientRect(),l=s.width,u=s.height,c=s.top,h=s.left,d,g,p=i;n.rotateDeg===90?(d=u,g=c):(d=l,g=h);var m=p-g;m=m>d?d:m<0?0:m;var _=m/d;_=_<0?0:_>1?1:_,Number.isNaN(_)&&(_=this.player.currentTime/this.offsetDuration);var b=parseInt(_*this.offsetDuration*1e3,10)/1e3,T=f.getCurrentTimeByOffset(b,n.timeSegments);return{percent:_,currentTime:b,seekTime:T,offset:m,width:d,left:g,e}}},{key:"updateTime",value:function(e){var i=this.player,n=this.duration;e>n?e=n:e<0&&(e=0);var s=i.plugins.time;s&&s.updateTime(e)}},{key:"resetSeekState",value:function(){this.isProgressMoving=!1;var e=this.player.plugins.time;e&&e.resetActive()}},{key:"updatePercent",value:function(e,i){if(this.isProgressMoving=!0,!this.config.disable){e=e>1?1:e<0?0:e,this.progressBtn.style.left="".concat(e*100,"%"),this.innerList.update({played:e*this.offsetDuration},this.offsetDuration);var n=this.player.plugins.miniprogress;n&&n.update({played:e*this.offsetDuration},this.offsetDuration)}}},{key:"onTimeupdate",value:function(e){var i=this.player,n=this._state,s=this.offsetDuration;if(!(i.isSeeking&&i.media.seeking||this.isProgressMoving||!i.hasStart)){if(n.now>-1){var l=parseInt(n.now*1e3,10)-parseInt(i.currentTime*1e3,10);if(n.direc===0&&l>300||n.direc===1&&l>-300){n.now=-1;return}else n.now=-1}var u=this.currentTime;u=f.adjustTimeByDuration(u,s,e),this.innerList.update({played:u},s),this.progressBtn.style.left="".concat(u/s*100,"%")}}},{key:"onCacheUpdate",value:function(e){var i=this.player,n=this.duration;if(i){var s=i.bufferedPoint.end;s=f.adjustTimeByDuration(s,n,e),this.innerList.update({cached:s},n)}}},{key:"onReset",value:function(){this.innerList.update({played:0,cached:0},0),this.progressBtn.style.left="0%"}},{key:"destroy",value:function(){var e=this.player;this.thumbnailPlugin=null,this.innerList.destroy(),this.innerList=null;var i=this.domEventType;(i==="touch"||i==="compatible")&&(this.root.removeEventListener("touchstart",this.onMouseDown),this.root.removeEventListener("touchmove",this.onMouseMove),this.root.removeEventListener("touchend",this.onMouseUp),this.root.removeEventListener("touchcancel",this.onMouseUp)),(i==="mouse"||i==="compatible")&&(this.unbind("mousedown",this.onMouseDown),this.unbind("mouseenter",this.onMouseEnter),this.unbind("mousemove",this.onMoveOnly),this.unbind("mouseleave",this.onMouseLeave),document.removeEventListener("mousemove",this.onMouseMove,!1),document.removeEventListener("mouseup",this.onMouseUp,!1),e.root.removeEventListener("click",this.onBodyClick,!0))}},{key:"render",value:function(){if(!(this.config.disable||this.playerConfig.isLive)){var e=this.player.controls?this.player.controls.config.mode:"",i=e==="bottom"?"xgplayer-progress-bottom":"";return'\n    <xg-progress class="xgplayer-progress '.concat(i,'">\n      <xg-outer class="xgplayer-progress-outer">\n        <xg-progress-btn class="xgplayer-progress-btn"></xg-progress-btn>\n      </xg-outer>\n    </xg-progress>\n    ')}}}],[{key:"pluginName",get:function(){return"progress"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_CENTER,index:0,disable:!1,isDraggingSeek:!0,closeMoveSeek:!1,isPauseMoving:!1,isCloseClickSeek:!1,fragments:[{percent:1}],fragFocusClass:ui.POINT,fragClass:"",fragAutoFocus:!1,miniMoveStep:5,miniStartStep:2,onMoveStart:function(){},onMoveEnd:function(){},endedDiff:.2}}},{key:"FRAGMENT_FOCUS_CLASS",get:function(){return ui}}]),r}(N),ln=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),e.isActiving=!1,e}return w(r,[{key:"duration",get:function(){var e=this.player,i=e.offsetDuration,n=e.duration;return this.playerConfig.customDuration||i||n}},{key:"currentTime",get:function(){var e=this.player,i=e.offsetCurrentTime,n=e.currentTime;return i>=0?i:n}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"afterCreate",value:function(){var e=this.player.controls.config.mode;this.mode=e==="flex"?"flex":"normal",!this.config.disable&&(this.mode==="flex"&&(this.createCenterTime(),this.root.style.display="none"),this.durationDom=this.find(".time-duration"),this.timeDom=this.find(".time-current"),this.listenEvents())}},{key:"listenEvents",value:function(){var e=this;this.on([he,ce,J],function(i){i.eventName==="durationchange"&&(e.isActiving=!1),e.onTimeUpdate()}),this.on(we,function(){e.onTimeUpdate(!0)}),this.on(Se,function(){e.onReset()})}},{key:"show",value:function(e){if(this.mode==="flex"){this.centerCurDom&&(this.centerCurDom.style.display="block"),this.centerDurDom&&(this.centerDurDom.style.display="block");return}this.root.style.display="block"}},{key:"hide",value:function(){if(this.mode==="flex"){this.centerCurDom&&(this.centerCurDom.style.display="none"),this.centerDurDom&&(this.centerDurDom.style.display="none");return}this.root.style.display="none"}},{key:"onTimeUpdate",value:function(e){var i=this.player,n=this.config,s=this.duration;if(!(n.disable||this.isActiving||!i.hasStart)){var l=this.currentTime+this.timeOffset;l=f.adjustTimeByDuration(l,s,e),this.mode==="flex"?(this.centerCurDom.innerHTML=this.minWidthTime(f.format(l)),s!==1/0&&s>0&&(this.centerDurDom.innerHTML=f.format(s))):(this.timeDom.innerHTML=this.minWidthTime(f.format(l)),s!==1/0&&s>0&&(this.durationDom.innerHTML=f.format(s)))}}},{key:"onReset",value:function(){this.mode==="flex"?(this.centerCurDom.innerHTML=this.minWidthTime(f.format(0)),this.centerDurDom.innerHTML=f.format(0)):(this.timeDom.innerHTML=this.minWidthTime(f.format(0)),this.durationDom.innerHTML=f.format(0))}},{key:"createCenterTime",value:function(){var e=this.player;if(!(!e.controls||!e.controls.center)){var i=e.controls.center;this.centerCurDom=f.createDom("xg-icon","00:00",{},"xgplayer-time xg-time-left"),this.centerDurDom=f.createDom("xg-icon","00:00",{},"xgplayer-time xg-time-right"),i.children.length>0?i.insertBefore(this.centerCurDom,i.children[0]):i.appendChild(this.centerCurDom),i.appendChild(this.centerDurDom)}}},{key:"afterPlayerInit",value:function(){var e=this.config;if(this.duration===1/0||this.playerConfig.isLive?(f.hide(this.durationDom),f.hide(this.timeDom),f.hide(this.find(".time-separator")),f.show(this.find(".time-live-tag"))):f.hide(this.find(".time-live-tag")),e.hide){this.hide();return}this.show()}},{key:"changeLiveState",value:function(e){e?(f.hide(this.durationDom),f.hide(this.timeDom),f.hide(this.find(".time-separator")),f.show(this.find(".time-live-tag"))):(f.hide(this.find(".time-live-tag")),f.show(this.find(".time-separator")),f.show(this.durationDom),f.show(this.timeDom))}},{key:"updateTime",value:function(e){if(this.isActiving=!0,!(!e&&e!==0||e>this.duration)){if(this.mode==="flex"){this.centerCurDom.innerHTML=this.minWidthTime(f.format(e));return}this.timeDom.innerHTML=this.minWidthTime(f.format(e))}}},{key:"minWidthTime",value:function(e){return e.split(":").map(function(i){return'<span class="time-min-width">'.concat(i,"</span>")}).join(":")}},{key:"resetActive",value:function(){var e=this,i=this.player,n=function(){e.isActiving=!1};this.off(ce,n),i.isSeeking&&i.media.seeking?this.once(ce,n):this.isActiving=!1}},{key:"destroy",value:function(){var e=this.player.controls.center;this.centerCurDom&&e.removeChild(this.centerCurDom),this.centerCurDom=null,this.centerDurDom&&e.removeChild(this.centerDurDom),this.centerDurDom=null}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-time">\n    <span class="time-current">00:00</span>\n    <span class="time-separator">/</span>\n    <span class="time-duration">00:00</span>\n    <span class="time-live-tag">'.concat(this.i18n.LIVE_TIP,"</span>\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"time"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_LEFT,index:2,disable:!1}}}]),r}(N);function Fr(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M940.632 837.632l-72.192-72.192c65.114-64.745 105.412-154.386 105.412-253.44s-40.299-188.695-105.396-253.424l-0.016-0.016 72.192-72.192c83.639 83.197 135.401 198.37 135.401 325.632s-51.762 242.434-135.381 325.612l-0.020 0.020zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n',"image/svg+xml").firstChild}function Nr(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n',"image/svg+xml").firstChild}function Br(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M920.4 439.808l-108.544-109.056-72.704 72.704 109.568 108.544-109.056 108.544 72.704 72.704 108.032-109.568 108.544 109.056 72.704-72.704-109.568-108.032 109.056-108.544-72.704-72.704-108.032 109.568z"></path>\n</svg>\n',"image/svg+xml").firstChild}var un=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"onBarMousedown",function(s){var l=v(t),u=l.player,c=t.find(".xgplayer-bar");f.event(s);var h=c.getBoundingClientRect(),d=f.getEventPos(s,u.zoom),g=h.height-(d.clientY-h.top);if(d.h=g,d.barH=h.height,t.pos=d,!(g<-2))return t.updateVolumePos(g,s),document.addEventListener("mouseup",t.onBarMouseUp),t._d.isStart=!0,!1}),y(v(t),"onBarMouseMove",function(s){var l=v(t),u=l._d;if(u.isStart){var c=v(t),h=c.pos,d=c.player;s.preventDefault(),s.stopPropagation(),f.event(s);var g=f.getEventPos(s,d.zoom);u.isMoving=!0;var p=h.h-g.clientY+h.clientY;p>h.barH||t.updateVolumePos(p,s)}}),y(v(t),"onBarMouseUp",function(s){f.event(s),document.removeEventListener("mouseup",t.onBarMouseUp);var l=v(t),u=l._d;u.isStart=!1,u.isMoving=!1}),y(v(t),"onMouseenter",function(s){t._d.isActive=!0,t.focus(),t.emit("icon_mouseenter",{pluginName:t.pluginName})}),y(v(t),"onMouseleave",function(s){t._d.isActive=!1,t.unFocus(100,!1,s),t.emit("icon_mouseleave",{pluginName:t.pluginName})}),y(v(t),"onVolumeChange",function(s){if(t.player){var l=t.player,u=l.muted,c=l.volume;t._d.isMoving||(t.find(".xgplayer-drag").style.height=u||c===0?"4px":"".concat(c*100,"%"),t.config.showValueLabel&&t.updateVolumeValue()),t.animate(u,c)}}),t}return w(r,[{key:"registerIcons",value:function(){return{volumeSmall:{icon:Nr,class:"xg-volume-small"},volumeLarge:{icon:Fr,class:"xg-volume"},volumeMuted:{icon:Br,class:"xg-volume-mute"}}}},{key:"afterCreate",value:function(){var e=this;if(this._timerId=null,this._d={isStart:!1,isMoving:!1,isActive:!1},!this.config.disable){this.initIcons();var i=this.playerConfig,n=i.commonStyle,s=i.volume;n.volumeColor&&(this.find(".xgplayer-drag").style.backgroundColor=n.volumeColor),this.changeMutedHandler=this.hook("mutedChange",function(l){e.changeMuted(l)},{pre:function(u){u.preventDefault(),u.stopPropagation()}}),this._onMouseenterHandler=this.hook("mouseenter",this.onMouseenter),this._onMouseleaveHandler=this.hook("mouseleave",this.onMouseleave),A.device!=="mobile"&&this.playerConfig.isMobileSimulateMode!=="mobile"&&(this.bind("mouseenter",this._onMouseenterHandler),this.bind(["blur","mouseleave"],this._onMouseleaveHandler),this.bind(".xgplayer-slider","mousedown",this.onBarMousedown),this.bind(".xgplayer-slider","mousemove",this.onBarMouseMove),this.bind(".xgplayer-slider","mouseup",this.onBarMouseUp)),this.bind(".xgplayer-icon",["touchend","click"],this.changeMutedHandler),this.on(Fi,this.onVolumeChange),this.once(fe,this.onVolumeChange),f.typeOf(s)!=="Number"&&(this.player.volume=this.config.default),this.onVolumeChange()}}},{key:"updateVolumePos",value:function(e,i){var n=this.player,s=this.find(".xgplayer-drag"),l=this.find(".xgplayer-bar");if(!(!l||!s)){var u=parseInt(e/l.getBoundingClientRect().height*1e3,10);s.style.height="".concat(e,"px");var c=Math.max(Math.min(u/1e3,1),0),h={volume:{from:n.volume,to:c}};n.muted&&(h.muted={from:!0,to:!1}),this.emitUserAction(i,"change_volume",{muted:n.muted,volume:n.volume,props:h}),n.volume=Math.max(Math.min(u/1e3,1),0),n.muted&&(n.muted=!1),this.config.showValueLabel&&this.updateVolumeValue()}}},{key:"updateVolumeValue",value:function(){var e=this.player,i=e.volume,n=e.muted,s=this.find(".xgplayer-value-label"),l=Math.max(Math.min(i,1),0);s.innerText=n?0:Math.round(l*100)}},{key:"focus",value:function(){var e=this.player;e.focus({autoHide:!1}),this._timerId&&(f.clearTimeout(this,this._timerId),this._timerId=null),f.addClass(this.root,"slide-show")}},{key:"unFocus",value:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:100,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,s=arguments.length>2?arguments[2]:void 0,l=this._d,u=this.player;l.isActive||(this._timerId&&(f.clearTimeout(this,this._timerId),this._timerId=null),this._timerId=f.setTimeout(this,function(){l.isActive||(n?u.blur():u.focus(),f.removeClass(e.root,"slide-show"),l.isStart&&e.onBarMouseUp(s)),e._timerId=null},i))}},{key:"changeMuted",value:function(e){e&&e.stopPropagation();var i=this.player,n=this._d;n.isStart&&this.onBarMouseUp(e),this.emitUserAction(e,"change_muted",{muted:i.muted,volume:i.volume,props:{muted:{from:i.muted,to:!i.muted}}}),i.volume>0&&(i.muted=!i.muted),i.volume<.01&&(i.volume=this.config.miniVolume)}},{key:"animate",value:function(e,i){e||i===0?this.setAttr("data-state","mute"):i<.5&&this.icons.volumeSmall?this.setAttr("data-state","small"):this.setAttr("data-state","normal")}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.volumeSmall),this.appendChild(".xgplayer-icon",e.volumeLarge),this.appendChild(".xgplayer-icon",e.volumeMuted)}},{key:"destroy",value:function(){this._timerId&&(f.clearTimeout(this,this._timerId),this._timerId=null),this.unbind("mouseenter",this.onMouseenter),this.unbind(["blur","mouseleave"],this.onMouseleave),this.unbind(".xgplayer-slider","mousedown",this.onBarMousedown),this.unbind(".xgplayer-slider","mousemove",this.onBarMouseMove),this.unbind(".xgplayer-slider","mouseup",this.onBarMouseUp),document.removeEventListener("mouseup",this.onBarMouseUp),this.unbind(".xgplayer-icon",A.device==="mobile"?"touchend":"click",this.changeMutedHandler)}},{key:"render",value:function(){if(!this.config.disable){var e=this.config.default||this.player.volume,i=this.config.showValueLabel;return'\n    <xg-icon class="xgplayer-volume" data-state="normal">\n      <div class="xgplayer-icon">\n      </div>\n      <xg-slider class="xgplayer-slider">\n        '.concat(i?'<div class="xgplayer-value-label">'.concat(e*100,"</div>"):"",'\n        <div class="xgplayer-bar">\n          <xg-drag class="xgplayer-drag" style="height: ').concat(e*100,'%"></xg-drag>\n        </div>\n      </xg-slider>\n    </xg-icon>')}}}],[{key:"pluginName",get:function(){return"volume"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_RIGHT,index:1,disable:!1,showValueLabel:!1,default:.6,miniVolume:.2}}}]),r}(N);function Y(){return new Date().getTime()}var ge={LOAD_START:"loadstart",LOADED_DATA:"loadeddata",FIRST_FRAME:"firstFrame",WAIT_START:"waitingStart",WAIT_END:"waitingEnd",SEEK_START:"seekStart",SEEK_END:"seekEnd"},Hr=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"_onTimeupdate",function(){t._state.isTimeUpdate=!0,t._state.autoplayStart&&(R.logInfo("[xgLogger]".concat(t.player.playerId," _onTimeupdate")),t._sendFF("onTimeupdate"))}),y(v(t),"_onAutoplayStart",function(){R.logInfo("[xgLogger]".concat(t.player.playerId," _onAutoplayStart")),t._state.autoplayStart=!0,t.vt&&t._sendFF("onAutoplayStart")}),y(v(t),"_onReset",function(){t._state={autoplayStart:!1,isFFLoading:!1,isTimeUpdate:!1,isFFSend:!1,isLs:!1},t.vt=0,t.pt=0,t.fvt=0,t.newPointTime=Y(),t.loadedCostTime=0,t.startCostTime=0,t._isSeeking=!1,t.seekingStart=0,t.waitingStart=0,t.fixedWaitingStart=0,t._isWaiting=!1,t._waitTimer&&f.clearTimeout(v(t),t._waitTimer),t._waittTimer&&f.clearTimeout(v(t),t._waittTimer),t._waitTimer=null,t._waittTimer=null,t._waitType=0}),y(v(t),"_onSeeking",function(){t.seekingStart||(t.suspendWaitingStatus("seek"),t.seekingStart=Y(),t.emitLog(ge.SEEK_START,{start:Y()}))}),y(v(t),"_onSeeked",function(){t.suspendSeekingStatus("seeked")}),y(v(t),"_onWaitingLoadStart",function(){t._isWaiting||t.vt||(t._isWaiting=!0,t.waitingStart=Y(),t.fixedWaitingStart=Y(),t._waitType=1,t.emitLog(ge.WAIT_START,{fixedStart:t.fixedWaitingStart,start:t.waitingStart,type:1,endType:"loadstart"}))}),y(v(t),"_onWaiting",function(){t._isWaiting||!t.vt||(t._isWaiting=!0,t.vt?t.seekingStart?t._waitType=2:t._waitType=0:t._waitType=1,t.fixedWaitingStart=Y(),t._waitTimer=f.setTimeout(v(t),function(){t._isWaiting&&(t.waitingStart=Y(),f.clearTimeout(v(t),t._waitTimer),t._waitTimer=null,t._startWaitTimeout(),t.emitLog(ge.WAIT_START,{fixedStart:t.fixedWaitingStart,start:t.waitingStart,type:t._waitType,endType:t._waitType===2?"seek":"playing"}))},200))}),y(v(t),"_onError",function(){t.suspendSeekingStatus("error"),t.suspendWaitingStatus("error")}),y(v(t),"_onPlaying",function(){t._isWaiting&&t.suspendWaitingStatus("playing")}),t}return w(r,[{key:"afterCreate",value:function(){var e=this;this._onReset(),this._waitType="firstFrame",this._initOnceEvents(),this.newPointTime=Y(),this.loadedCostTime=0,this.startCostTime=0,this.on(Ot,function(){var i=e._state,n=i.autoplayStart,s=i.isFFSend;e.startCostTime=Y()-e.newPointTime,R.logInfo("[xgLogger]".concat(e.player.playerId," LOAD_START"),"autoplayStart:".concat(n," isFFSend:").concat(s," startCostTime:").concat(e.startCostTime," newPointTime").concat(e.newPointTime)),!s&&(!i.isLs&&e.emitLog(ge.LOAD_START,{}),i.isLs=!0,i.isTimeUpdate=!1,i.isFFLoading=!0,e.pt=Y(),e.vt=0,e.fvt=0,e._initOnceEvents(),e._onWaitingLoadStart())}),this.on(fe,function(){e.vt=Y(),e.fvt=e.vt-e.pt,e.loadedCostTime=e.vt-e.newPointTime;var i=e._state,n=i.isTimeUpdate,s=i.isFFSend,l=i.autoplayStart;R.logInfo("[xgLogger]".concat(e.player.playerId," LOADED_DATA"),"fvt:".concat(e.fvt," isTimeUpdate:").concat(e._state.isTimeUpdate," loadedCostTime:").concat(e.loadedCostTime)),(n||l)&&e._sendFF("loadedData"),s||e.emitLog(ge.LOADED_DATA,{}),e.suspendWaitingStatus("loadeddata")}),this.on(rt,this._onSeeking),this.on(ce,this._onSeeked),this.on(Nt,function(){e.endState("destroy")}),this.on(at,function(){e.endState("urlChange"),R.logInfo("[xgLogger]".concat(e.player.playerId," URL_CHANGE")),e._state.isFFSend&&e._onReset()}),this.on([Dt,ue],this._onPlaying),this.on(je,this._onWaiting),this.on(Ve,this._onError),this.on(ot,function(){R.logInfo("[xgLogger]".concat(e.player.playerId," RESET")),e.endState("reset"),e._initOnceEvents(),e._onReset()})}},{key:"_initOnceEvents",value:function(){this.off(Ee,this._onAutoplayStart),this.off(J,this._onTimeupdate),this.once(Ee,this._onAutoplayStart),this.once(J,this._onTimeupdate)}},{key:"_sendFF",value:function(e){this.s=Y();var i=this._state,n=i.isFFLoading,s=i.isFFSend;R.logInfo("[xgLogger]".concat(this.player.playerId," _sendFF"),"".concat(e," fvt:").concat(this.fvt," isFFLoading:").concat(n," !isFFSend:").concat(!s)),this.vt>0&&n&&!s&&(R.logInfo("[xgLogger]".concat(this.player.playerId," emitLog_firstFrame"),e),this._state.isFFLoading=!1,this._state.isFFSend=!0,this.emitLog(ge.FIRST_FRAME,{fvt:this.fvt,costTime:this.fvt,vt:this.vt,startCostTime:this.startCostTime,loadedCostTime:this.loadedCostTime}))}},{key:"_startWaitTimeout",value:function(){var e=this;this._waittTimer&&f.clearTimeout(this,this._waittTimer),this._waittTimer=f.setTimeout(this,function(){e.suspendWaitingStatus("timeout"),f.clearTimeout(e,e._waittTimer),e._waittTimer=null},this.config.waitTimeout)}},{key:"endState",value:function(e){this.suspendWaitingStatus(e),this.suspendSeekingStatus(e)}},{key:"suspendSeekingStatus",value:function(e){if(this.seekingStart){var i=Y(),n=i-this.seekingStart;this.seekingStart=0,this.emitLog(ge.SEEK_END,{end:i,costTime:n,endType:e})}}},{key:"suspendWaitingStatus",value:function(e){if(this._waitTimer&&(f.clearTimeout(this,this._waitTimer),this._waitTimer=null),this._waittTimer&&(f.clearTimeout(this,this._waittTimer),this._waittTimer=null),this._isWaiting=!1,!!this.waitingStart){var i=Y(),n=i-this.waitingStart,s=i-this.fixedWaitingStart,l=this.config.waitTimeout;this._isWaiting=!1,this.waitingStart=0,this.fixedWaitingStart=0,this.emitLog(ge.WAIT_END,{fixedCostTime:s>l?l:s,costTime:n>l?l:n,type:e==="loadeddata"?1:this._waitType,endType:this._waitType===2?"seek":e})}}},{key:"emitLog",value:function(e,i){var n=this.player;this.emit(Yi,V({t:Y(),host:f.getHostFromUrl(n.currentSrc),vtype:n.vtype,eventType:e,currentTime:this.player.currentTime,readyState:n.video.readyState,networkState:n.video.networkState},i))}}],[{key:"pluginName",get:function(){return"xgLogger"}},{key:"defaultConfig",get:function(){return{waitTimeout:1e4}}}]),r}(N);function Ur(){return new DOMParser().parseFromString('<svg class="xgplayer-replay-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 78 78" width="78" height="78">\n  <path fill="#fff" transform="translate(20, 20)" d="M8.22708362,13.8757234 L11.2677371,12.6472196 C11.7798067,12.4403301 12.3626381,12.6877273 12.5695276,13.1997969 L12.9441342,14.1269807 C13.1510237,14.6390502 12.9036264,15.2218816 12.3915569,15.4287712 L6.8284538,17.6764107 L5.90126995,18.0510173 C5.38920044,18.2579068 4.80636901,18.0105096 4.5994795,17.49844 L1.97723335,11.0081531 C1.77034384,10.4960836 2.0177411,9.91325213 2.52981061,9.70636262 L3.45699446,9.33175602 C3.96906396,9.12486652 4.5518954,9.37226378 4.75878491,9.88433329 L5.67885163,12.1615783 C7.99551726,6.6766934 13.3983951,3 19.5,3 C27.7842712,3 34.5,9.71572875 34.5,18 C34.5,26.2842712 27.7842712,33 19.5,33 C15.4573596,33 11.6658607,31.3912946 8.87004692,28.5831991 C8.28554571,27.9961303 8.28762719,27.0463851 8.87469603,26.4618839 C9.46176488,25.8773827 10.4115101,25.8794641 10.9960113,26.466533 C13.2344327,28.7147875 16.263503,30 19.5,30 C26.127417,30 31.5,24.627417 31.5,18 C31.5,11.372583 26.127417,6 19.5,6 C14.4183772,6 9.94214483,9.18783811 8.22708362,13.8757234 Z"></path>\n</svg>\n',"image/svg+xml").firstChild}var Vr=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"registerIcons",value:function(){return{replay:Ur}}},{key:"afterCreate",value:function(){var e=this;N.insert(this.icons.replay,this.root,0),this.__handleReplay=this.hook("replayClick",function(){e.player.replay()},{pre:function(n){n.preventDefault(),n.stopPropagation()}}),this.bind(".xgplayer-replay",["click","touchend"],this.__handleReplay),this.on(we,function(){if(e.playerConfig.loop||f.addClass(e.player.root,"replay"),!e.config.disable){e.show();var i=e.root.querySelector("path");if(i){var n=window.getComputedStyle(i).getPropertyValue("transform");if(typeof n=="string"&&n.indexOf("none")>-1)return null;i.setAttribute("transform",n)}}}),this.on(Q,function(){e.hide()})}},{key:"handleReplay",value:function(e){e.preventDefault(),e.stopPropagation(),this.player.replay(),f.removeClass(this.player.root,"replay")}},{key:"show",value:function(e){this.config.disable||(this.root.style.display="flex")}},{key:"enable",value:function(){this.config.disable=!1}},{key:"disable",value:function(){this.config.disable=!0,this.hide()}},{key:"destroy",value:function(){this.unbind(".xgplayer-replay",["click","touchend"],this.__handleReplay)}},{key:"render",value:function(){return'<xg-replay class="xgplayer-replay">\n      <xg-replay-txt class="xgplayer-replay-txt" lang-key="'.concat(this.i18nKeys.REPLAY,'">').concat(this.i18n.REPLAY,"</xg-replay-txt>\n    </xg-replay>")}}],[{key:"pluginName",get:function(){return"replay"}},{key:"defaultConfig",get:function(){return{disable:!1}}}]),r}(N),jr=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"isEndedShow",get:function(){return this.config.isEndedShow},set:function(e){this.config.isEndedShow=e}},{key:"hide",value:function(){f.addClass(this.root,"hide")}},{key:"show",value:function(e){f.removeClass(this.root,"hide")}},{key:"beforeCreate",value:function(e){typeof e.player.config.poster=="string"&&(e.config.poster=e.player.config.poster)}},{key:"afterCreate",value:function(){var e=this;this.on(we,function(){e.isEndedShow&&f.removeClass(e.root,"hide")}),this.config.hideCanplay?(this.once(J,function(){e.onTimeUpdate()}),this.on(at,function(){f.removeClass(e.root,"hide"),f.addClass(e.root,"xg-showplay"),e.once(J,function(){e.onTimeUpdate()})})):this.on(Q,function(){f.addClass(e.root,"hide")})}},{key:"setConfig",value:function(e){var i=this;Object.keys(e).forEach(function(s){i.config[s]=e[s]});var n=this.config.poster;this.update(n)}},{key:"onTimeUpdate",value:function(){var e=this;this.player.currentTime?f.removeClass(this.root,"xg-showplay"):this.once(J,function(){e.onTimeUpdate()})}},{key:"update",value:function(e){e&&(this.config.poster=e,this.root.style.backgroundImage="url(".concat(e,")"))}},{key:"getBgSize",value:function(e){var i="";switch(e){case"cover":i="cover";break;case"contain":i="contain";break;case"fixHeight":i="auto 100%";break;default:i=""}return i?"background-size: ".concat(i,";"):""}},{key:"render",value:function(){var e=this.config,i=e.poster,n=e.hideCanplay,s=e.fillMode,l=e.notHidden,u=this.getBgSize(s),c=i?"background-image:url(".concat(i,");").concat(u):u,h=l?"xg-not-hidden":n?"xg-showplay":"";return'<xg-poster class="xgplayer-poster '.concat(h,'" style="').concat(c,'">\n    </xg-poster>')}}],[{key:"pluginName",get:function(){return"poster"}},{key:"defaultConfig",get:function(){return{isEndedShow:!0,hideCanplay:!1,notHidden:!1,poster:"",fillMode:"fixWidth"}}}]),r}(N),ae={};function Wr(o,a){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{start:null,end:null};return ae[o]&&window.clearTimeout(ae[o].id),ae[o]={},r.start&&r.start(),ae[o].id=window.setTimeout(function(){r.end&&r.end(),window.clearTimeout(ae[o].id),delete ae[o]},a),ae[o].id}function ci(o){if(o){window.clearTimeout(o);return}Object.keys(ae).map(function(a){window.clearTimeout(ae[a].id),delete ae[a]})}var zr=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),y(v(e),"onPlayerReset",function(){e.autoPlayStart=!1;var i=e.config.mode==="auto"?"auto-hide":"hide";e.setAttr("data-state","play"),f.removeClass(e.root,i),e.show()}),y(v(e),"onAutoplayStart",function(){if(!e.autoPlayStart){var i=e.config.mode==="auto"?"auto-hide":"hide";f.addClass(e.root,i),e.autoPlayStart=!0,e.toggleTo("play")}}),e.autoPlayStart=!1,e}return w(r,[{key:"afterCreate",value:function(){var e=this.playerConfig;this.initIcons(),this.listenEvents(),this.bindClickEvents(),e.autoplay||this.show()}},{key:"listenEvents",value:function(){var e=this,i=this.player,n=this.playerConfig;this.once(Mt,function(){n&&(n.lang&&n.lang==="en"?f.addClass(i.root,"lang-is-en"):n.lang==="jp"&&f.addClass(i.root,"lang-is-jp"))}),this.on(Ee,this.onAutoplayStart),this.on(Ft,function(){var s=e.config.mode==="auto"?"auto-hide":"hide";e.setAttr("data-state","play"),f.removeClass(e.root,s),e.show()}),this.on(Q,function(){e.toggleTo("play")}),this.on(Me,function(){e.toggleTo("pause")}),this.on(ot,function(){e.onPlayerReset()})}},{key:"bindClickEvents",value:function(){var e=this;this.clickHandler=this.hook("startClick",this.switchPausePlay,{pre:function(n){n.cancelable&&n.preventDefault(),n.stopPropagation();var s=e.player.paused;e.emitUserAction(n,"switch_play_pause",{props:"paused",from:s,to:!s})}}),this.bind(["click","touchend"],this.clickHandler)}},{key:"registerIcons",value:function(){return{startPlay:{icon:Ut,class:"xg-icon-play"},startPause:{icon:Vt,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild("xg-start-inner",e.startPlay),this.appendChild("xg-start-inner",e.startPause)}},{key:"hide",value:function(){f.addClass(this.root,"hide")}},{key:"show",value:function(e){f.removeClass(this.root,"hide")}},{key:"focusHide",value:function(){f.addClass(this.root,"focus-hide")}},{key:"recover",value:function(){f.removeClass(this.root,"focus-hide")}},{key:"switchStatus",value:function(e){e?this.setAttr("data-state",this.player.paused?"pause":"play"):this.setAttr("data-state",this.player.paused?"play":"pause")}},{key:"animate",value:function(e){var i=this;this._animateId=Wr("pauseplay",400,{start:function(){f.addClass(i.root,"interact"),i.show(),i.switchStatus(!0)},end:function(){f.removeClass(i.root,"interact"),!e&&i.hide(),i._animateId=null}})}},{key:"endAnimate",value:function(){f.removeClass(this.root,"interact"),ci(this._animateId),this._animateId=null}},{key:"switchPausePlay",value:function(e){var i=this.player;if(e.cancelable&&e.preventDefault(),e.stopPropagation(),!(i.state<F.READY)){var n=this.player.paused;!n&&i.state===F.RUNNING?i.pause():i.play()}}},{key:"onPlayPause",value:function(e){this.toggleTo(e)}},{key:"toggleTo",value:function(e){var i=this.config,n=this.player;if(!(!n||n.state<F.RUNNING||!this.autoPlayStart)){if(i.mode==="show"){this.switchStatus(),this.show();return}if(i.mode==="auto"){this.switchStatus();return}if(i.isShowPause&&n.paused&&!n.ended||i.isShowEnd&&n.ended){this.switchStatus(),this.show(),this.endAnimate();return}if(i.disableAnimate){this.switchStatus(),this.hide();return}if(e==="play")this.autoPlayStart?this.animate():this.hide();else{if(!this.autoPlayStart||n.ended)return;this.animate()}}}},{key:"destroy",value:function(){this.unbind(["click","touchend"],this.clickHandler),ci(this._animateId)}},{key:"render",value:function(){var e=this.playerConfig.autoplay?this.config.mode==="auto"?"auto-hide":"hide":"";return'\n    <xg-start class="xgplayer-start '.concat(e,'">\n    <xg-start-inner></xg-start-inner>\n    </xg-start>')}}],[{key:"pluginName",get:function(){return"start"}},{key:"defaultConfig",get:function(){return{isShowPause:!1,isShowEnd:!1,disableAnimate:!1,mode:"hide"}}}]),r}(N),Gr=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"render",value:function(){var e=this.config.innerHtml,i=f.createDom("xg-enter","",{},"xgplayer-enter");if(e&&e instanceof window.HTMLElement)i.appendChild(e);else if(e&&typeof e=="string")i.innerHTML=e;else{for(var n="",s=1;s<=12;s++)n+='<div class="xgplayer-enter-bar'.concat(s,'"></div>');i.innerHTML='<div class="xgplayer-enter-spinner">'.concat(n,"</div>")}return i}}],[{key:"pluginName",get:function(){return"enter"}},{key:"defaultConfig",get:function(){return{innerHtml:"",logo:""}}}]),r}(N),fi=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"afterCreate",value:function(){this.getMini=this.getMini.bind(this),this.exitMini=this.exitMini.bind(this),this.bind("click",this.getMini)}},{key:"getMini",value:function(){this.config.onClick&&this.config.onClick()}},{key:"exitMini",value:function(){this.config.onClick&&this.config.onClick()}},{key:"destroy",value:function(){this.unbind(["click","touchend"],this.getMini)}},{key:"render",value:function(){var e="MINISCREEN";return'\n      <xg-icon class="xgplayer-miniicon">\n      <div class="xgplayer-icon btn-text"><span class="icon-text" lang-key="'.concat(this.i18nKeys[e],'">').concat(this.i18n[e],"</span></div>\n      </xg-icon>")}}],[{key:"pluginName",get:function(){return"miniscreenIcon"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_RIGHT,index:10}}}]),r}(N);function hi(o){var a=parseFloat(o),r=o.indexOf("%")===-1&&!Number.isNaN(a);return r&&a}var jt=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],cn=jt.length;function Kr(){for(var o={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},a=0;a<cn;a++){var r=jt[a];o[r]=0}return o}function Yr(o){var a=window.getComputedStyle(o);return a}function di(o){if(typeof o=="string"&&(o=document.querySelector(o)),!(!o||$(o)!=="object"||!o.nodeType)){var a=Yr(o);if(a.display==="none")return Kr();var r={};r.width=o.offsetWidth,r.height=o.offsetHeight;for(var t=r.isBorderBox=a.boxSizing==="border-box",e=0;e<cn;e++){var i=jt[e],n=a[i],s=parseFloat(n);r[i]=Number.isNaN(s)?0:s}var l=r.paddingLeft+r.paddingRight,u=r.paddingTop+r.paddingBottom,c=r.marginLeft+r.marginRight,h=r.marginTop+r.marginBottom,d=r.borderLeftWidth+r.borderRightWidth,g=r.borderTopWidth+r.borderBottomWidth,p=t,m=hi(a.width);m!==!1&&(r.width=m+(p?0:l+d));var _=hi(a.height);return _!==!1&&(r.height=_+(p?0:u+g)),r.innerWidth=r.width-(l+d),r.innerHeight=r.height-(u+g),r.outerWidth=r.width+c,r.outerHeight=r.height+h,r}}function pt(o,a){for(var r=0;r<o.length;r++){var t=o[r];if(t.identifier===a)return t}}var vt={START:"dragStart",MOVE:"dragMove",ENDED:"dragEnded"},gi={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]},$r=function(o){S(r,o);var a=x(r);function r(t){var e,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return E(this,r),e=a.call(this),e.isEnabled=!0,e.isDragging=!1,e.isDown=!1,e.position={},e.downPoint={},e.dragPoint={x:0,y:0},e.startPos={x:0,y:0},e._root=t instanceof Element?t:document.querySelector(t),e._handlerDom=i.handle instanceof Element?i.handle:document.querySelector(i.handle),!e._root||!e._handlerDom?At(e):(e._bindStartEvent(),e)}return w(r,[{key:"_bindStartEvent",value:function(){var e=this;"ontouchstart"in window?this._startKey="touchstart":this._startKey="mousedown",this["on".concat(this._startKey)]=this["on".concat(this._startKey)].bind(this),this._handlerDom.addEventListener(this._startKey,this["on".concat(this._startKey)]),gi[this._startKey].map(function(i){e["on".concat(i)]=e["on".concat(i)].bind(e)})}},{key:"_unbindStartEvent",value:function(){this._handlerDom.removeEventListener(this._startKey,this["on".concat(this._startKey)])}},{key:"_bindPostStartEvents",value:function(e){var i=this;if(e){var n=gi[this._startKey];n.map(function(s){window.addEventListener(s,i["on".concat(s)])}),this._boundPointerEvents=n}}},{key:"_unbindPostStartEvents",value:function(){var e=this;this._boundPointerEvents&&(this._boundPointerEvents.map(function(i){window.removeEventListener(i,e["on".concat(i)])}),delete this._boundPointerEvents)}},{key:"enable",value:function(){this.isEnabled=!0}},{key:"disable",value:function(){this.isEnabled=!1,this.isDragging&&this.onUp()}},{key:"onDocUp",value:function(e){this.onUp()}},{key:"animate",value:function(){var e=this;this.isDragging&&(this.positionDrag(),window.requestAnimationFrame(function(){e.animate()}))}},{key:"positionDrag",value:function(){var e="translate3d(".concat(this.dragPoint.x,"px, ").concat(this.dragPoint.y,"px, 0)");this._root.style.transform=e,this._root.style.webKitTransform=e}},{key:"setLeftTop",value:function(){this._root.style.left=this.position.x+"px",this._root.style.top=this.position.y+"px"}},{key:"onmousedown",value:function(e){this.dragStart(e,e)}},{key:"onmousemove",value:function(e){this.dragMove(e,e)}},{key:"onmouseup",value:function(e){this.dragEnd(e,e)}},{key:"ontouchstart",value:function(e){var i=e.changedTouches[0];this.dragStart(e,i),this.touchIdentifier=i.pointerId!==void 0?i.pointerId:i.identifier,e.preventDefault()}},{key:"ontouchmove",value:function(e){var i=pt(e.changedTouches,this.touchIdentifier);i&&this.dragMove(e,i)}},{key:"ontouchend",value:function(e){var i=pt(e.changedTouches,this.touchIdentifier);i&&this.dragEnd(e,i),e.preventDefault()}},{key:"ontouchcancel",value:function(e){var i=pt(e.changedTouches,this.touchIdentifier);i&&this.dragCancel(e,i)}},{key:"dragStart",value:function(e,i){if(!(!this._root||this.isDown||!this.isEnabled)){this.downPoint=i,this.dragPoint.x=0,this.dragPoint.y=0,this._getPosition();var n=di(this._root);this.startPos.x=this.position.x,this.startPos.y=this.position.y,this.startPos.maxY=window.innerHeight-n.height,this.startPos.maxX=window.innerWidth-n.width,this.setLeftTop(),this.isDown=!0,this._bindPostStartEvents(e)}}},{key:"dragRealStart",value:function(e,i){this.isDragging=!0,this.animate(),this.emit(vt.START,this.startPos)}},{key:"dragEnd",value:function(e,i){this._root&&(this._unbindPostStartEvents(),this.isDragging&&(this._root.style.transform="",this.setLeftTop(),this.emit(vt.ENDED)),this.presetInfo())}},{key:"_dragPointerMove",value:function(e,i){var n={x:i.pageX-this.downPoint.pageX,y:i.pageY-this.downPoint.pageY};return!this.isDragging&&this.hasDragStarted(n)&&this.dragRealStart(e,i),n}},{key:"dragMove",value:function(e,i){if(e=e||window.event,!!this.isDown){var n=this.startPos,s=n.x,l=n.y,u=this._dragPointerMove(e,i),c=u.x,h=u.y;c=this.checkContain("x",c,s),h=this.checkContain("y",h,l),this.position.x=s+c,this.position.y=l+h,this.dragPoint.x=c,this.dragPoint.y=h,this.emit(vt.MOVE,this.position)}}},{key:"dragCancel",value:function(e,i){this.dragEnd(e,i)}},{key:"presetInfo",value:function(){this.isDragging=!1,this.startPos={x:0,y:0},this.dragPoint={x:0,y:0},this.isDown=!1}},{key:"destroy",value:function(){this._unbindStartEvent(),this._unbindPostStartEvents(),this.isDragging&&this.dragEnd(),this.removeAllListeners(),this._handlerDom=null}},{key:"hasDragStarted",value:function(e){return Math.abs(e.x)>3||Math.abs(e.y)>3}},{key:"checkContain",value:function(e,i,n){return i+n<0?0-n:e==="x"&&i+n>this.startPos.maxX?this.startPos.maxX-n:e==="y"&&i+n>this.startPos.maxY?this.startPos.maxY-n:i}},{key:"_getPosition",value:function(){var e=window.getComputedStyle(this._root),i=this._getPositionCoord(e.left,"width"),n=this._getPositionCoord(e.top,"height");this.position.x=Number.isNaN(i)?0:i,this.position.y=Number.isNaN(n)?0:n,this._addTransformPosition(e)}},{key:"_addTransformPosition",value:function(e){var i=e.transform;if(i.indexOf("matrix")===0){var n=i.split(","),s=i.indexOf("matrix3d")===0?12:4,l=parseInt(n[s],10),u=parseInt(n[s+1],10);this.position.x+=l,this.position.y+=u}}},{key:"_getPositionCoord",value:function(e,i){if(e.indexOf("%")!==-1){var n=di(this._root.parentNode);return n?parseFloat(e)/100*n[i]:0}return parseInt(e,10)}}]),r}(Ri),Xr=function(o){S(r,o);var a=x(r);function r(t){var e;E(this,r),e=a.call(this,t),y(v(e),"onCancelClick",function(s){e.exitMini(),e.isClose=!0}),y(v(e),"onCenterClick",function(s){var l=v(e),u=l.player;u.paused?u.play():u.pause()}),y(v(e),"onScroll",function(s){if(!(!window.scrollY&&window.scrollY!==0||Math.abs(window.scrollY-e.pos.scrollY)<50)){var l=parseInt(f.getCss(e.player.root,"height"));l+=e.config.scrollTop,e.pos.scrollY=window.scrollY,window.scrollY>l+5?!e.isMini&&!e.isClose&&e.getMini():window.scrollY<=l&&(e.isMini&&e.exitMini(),e.isClose=!1)}}),e.isMini=!1,e.isClose=!1;var i=v(e),n=i.config;return e.pos={left:n.left<0?window.innerWidth-n.width-20:n.left,top:n.top<0?window.innerHeight-n.height-20:n.top,height:e.config.height,width:e.config.width,scrollY:window.scrollY||0},e.lastStyle=null,e}return w(r,[{key:"beforeCreate",value:function(e){typeof e.player.config.mini=="boolean"&&(e.config.isShowIcon=e.player.config.mini)}},{key:"afterCreate",value:function(){var e=this;this.initIcons(),this.on(Me,function(){e.setAttr("data-state","pause")}),this.on(Q,function(){e.setAttr("data-state","play")})}},{key:"onPluginsReady",value:function(){var e=this,i=this.player,n=this.config;if(!n.disable){if(this.config.isShowIcon){var s={config:{onClick:function(){e.getMini()}}};i.controls.registerPlugin(fi,s,fi.pluginName)}var l=f.checkTouchSupport()?"touchend":"click";this.bind(".mini-cancel-btn",l,this.onCancelClick),this.bind(".play-icon",l,this.onCenterClick),this.config.disableDrag||(this._draggabilly=new $r(this.player.root,{handle:this.root})),this.config.isScrollSwitch&&window.addEventListener("scroll",this.onScroll)}}},{key:"registerIcons",value:function(){return{play:{icon:Ut,class:"xg-icon-play"},pause:{icon:Vt,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".play-icon",e.play),this.appendChild(".play-icon",e.pause)}},{key:"getMini",value:function(){var e=this;if(!this.isMini){var i=this.player,n=this.playerConfig,s=this.config.target||this.player.root;this.lastStyle={},f.addClass(i.root,"xgplayer-mini"),["width","height","top","left"].map(function(l){e.lastStyle[l]=s.style[l],s.style[l]="".concat(e.pos[l],"px")}),n.fluid&&(s.style["padding-top"]=""),this.emit(Ze,!0),i.isMini=this.isMini=!0}}},{key:"exitMini",value:function(){var e=this;if(!this.isMini)return!1;var i=this.player,n=this.playerConfig,s=this.config.target||this.player.root;f.removeClass(i.root,"xgplayer-mini"),this.lastStyle&&Object.keys(this.lastStyle).map(function(l){s.style[l]=e.lastStyle[l]}),this.lastStyle=null,n.fluid&&(i.root.style.width="100%",i.root.style.height="0",i.root.style["padding-top"]="".concat(n.height*100/n.width,"%")),this.emit(Ze,!1),this.isMini=i.isMini=!1}},{key:"updatePos",value:function(e){this.pos=Object.assign(this.pos,e),this.isMini&&(this.player.root.style.left="".concat(this.pos.left,"px"),this.player.root.style.top="".concat(this.pos.top,"px"),this.player.root.style.width="".concat(this.pos.width,"px"),this.player.root.style.height="".concat(this.pos.height,"px"))}},{key:"destroy",value:function(){window.removeEventListener("scroll",this.onScroll);var e=f.checkTouchSupport()?"touchend":"click";this.unbind(".mini-cancel-btn",e,this.onCancelClick),this.unbind(".play-icon",e,this.onCenterClick),this._draggabilly&&this._draggabilly.destroy(),this._draggabilly=null,this.exitMini()}},{key:"render",value:function(){if(!this.config.disable)return'\n      <xg-mini-layer class="xg-mini-layer">\n      <xg-mini-header class="xgplayer-mini-header">\n      '.concat(_e(this,"MINI_DRAG",this.playerConfig.isHideTips),'\n      </xg-mini-header>\n      <div class="mini-cancel-btn">\n        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">\n          <path fill="#fff" fill-rule="evenodd" d="M3.99 3.49a1 1 0 0 1 1.414 0L10 8.085l4.596-4.595a1 1 0 1 1 1.414 1.414L11.414 9.5l4.596 4.596a1 1 0 0 1 .084 1.32l-.084.094a1 1 0 0 1-1.414 0L10 10.914 5.404 15.51a1 1 0 0 1-1.414-1.414L8.585 9.5 3.99 4.904a1 1 0 0 1-.084-1.32z"></path>\n        </svg>\n      </div>\n      <div class="play-icon">\n      </div>\n      </xg-mini-layer>')}}],[{key:"pluginName",get:function(){return"miniscreen"}},{key:"defaultConfig",get:function(){return{index:10,disable:!1,width:320,height:180,left:-1,top:-1,isShowIcon:!1,isScrollSwitch:!1,scrollTop:0,disableDrag:!1}}}]),r}(N),ze={mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",mousemove:"onMouseMove"},mt=["videoClick","videoDbClick"],yt=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"onMouseMove",function(s){var l=v(t),u=l.player,c=l.playerConfig;u.isActive||(u.focus({autoHide:!c.closeDelayBlur}),!c.closeFocusVideoFocus&&u.media.focus())}),y(v(t),"onMouseEnter",function(s){var l=v(t),u=l.playerConfig,c=l.player;!u.closeFocusVideoFocus&&c.media.focus(),u.closeDelayBlur?c.focus({autoHide:!1}):c.focus(),t.emit(ji)}),y(v(t),"onMouseLeave",function(s){var l=t.playerConfig,u=l.closePlayerBlur,c=l.leavePlayerTime,h=l.closeDelayBlur;!u&&!h&&(c?t.player.focus({autoHide:!0,delay:c}):t.player.blur({ignorePaused:!0})),t.emit(Vi)}),y(v(t),"onVideoClick",function(s){var l=v(t),u=l.player,c=l.playerConfig;s.target&&c.closeVideoClick||(s.target===u.root||s.target===u.media||s.target===u.innerContainer||s.target===u.media.__canvas)&&(s.preventDefault(),c.closeVideoStopPropagation||s.stopPropagation(),t._clickCount++,t.clickTimer&&(clearTimeout(t.clickTimer),t.clickTimer=null),t.clickTimer=setTimeout(function(){t._clickCount&&(t._clickCount--,oe(v(t),mt[0],function(h,d){t.switchPlayPause(d.e)},{e:s,paused:u.paused}),clearTimeout(t.clickTimer),t.clickTimer=null)},300))}),y(v(t),"onVideoDblClick",function(s){var l=v(t),u=l.player,c=l.playerConfig;if(!(c.closeVideoDblclick||!s.target||s.target!==u.media&&s.target!==u.media.__canvas)){if(!c.closeVideoClick&&t._clickCount<2){t._clickCount=0;return}t._clickCount=0,t.clickTimer&&(clearTimeout(t.clickTimer),t.clickTimer=null),s.preventDefault(),s.stopPropagation(),oe(v(t),mt[1],function(h,d){t.emitUserAction(d.e,"switch_fullscreen",{props:"fullscreen",from:u.fullscreen,to:!u.fullscreen}),u.fullscreen?u.exitFullscreen():u.getFullscreen()},{e:s,fullscreen:u.fullscreen})}}),t}return w(r,[{key:"afterCreate",value:function(){var e=this;this._clickCount=0,mt.map(function(n){e.__hooks[n]=null});var i=this.playerConfig.isMobileSimulateMode;i==="mobile"||A.device==="mobile"&&!A.os.isIpad||this.initEvents()}},{key:"initEvents",value:function(){var e=this,i=this.player,n=i.media,s=i.root,l=this.playerConfig.enableContextmenu;s&&s.addEventListener("click",this.onVideoClick,!1),s&&s.addEventListener("dblclick",this.onVideoDblClick,!1),Object.keys(ze).map(function(u){s.addEventListener(u,e[ze[u]],!1)}),!l&&n&&n.addEventListener("contextmenu",this.onContextmenu,!1)}},{key:"switchPlayPause",value:function(e){var i=this.player;this.emitUserAction(e,"switch_play_pause",{props:"paused",from:i.paused,to:!i.paused}),i.ended?i.duration!==1/0&&i.duration>0&&i.replay():i.paused?i.play():i.pause()}},{key:"onContextmenu",value:function(e){e=e||window.event,e.preventDefault&&e.preventDefault(),e.stopPropagation?e.stopPropagation():(e.returnValue=!1,e.cancelBubble=!0)}},{key:"destroy",value:function(){var e=this,i=this.player,n=i.video,s=i.root;this.clickTimer&&clearTimeout(this.clickTimer),s.removeEventListener("click",this.onVideoClick,!1),s.removeEventListener("dblclick",this.onVideoDblClick,!1),n.removeEventListener("contextmenu",this.onContextmenu,!1),Object.keys(ze).map(function(l){s.removeEventListener(l,e[ze[l]],!1)})}}],[{key:"pluginName",get:function(){return"pc"}},{key:"defaultConfig",get:function(){return{}}}]),r}(ye),pe={PRESS:"press",PRESS_END:"pressend",DOUBlE_CLICK:"doubleclick",CLICK:"click",TOUCH_MOVE:"touchmove",TOUCH_START:"touchstart",TOUCH_END:"touchend"},qr={start:"touchstart",end:"touchend",move:"touchmove",cancel:"touchcancel"},Zr={start:"mousedown",end:"mouseup",move:"mousemove",cancel:"mouseleave"};function pi(o){return o&&o.length>0?o[o.length-1]:null}function Jr(){return{pressDelay:600,dbClickDelay:200,disablePress:!1,disableDbClick:!1,miniStep:2,needPreventDefault:!0}}var Qr=function(){function o(a){var r=this,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{eventType:"touch"};E(this,o),y(this,"onTouchStart",function(e){var i=r._pos,n=r.root,s=pi(e.touches);i.x=s?parseInt(s.pageX,10):e.pageX,i.y=s?parseInt(s.pageX,10):e.pageX,i.start=!0,r.__setPress(e),n.addEventListener(r.events.end,r.onTouchEnd),n.addEventListener(r.events.cancel,r.onTouchCancel),n.addEventListener(r.events.move,r.onTouchMove),r.trigger(pe.TOUCH_START,e)}),y(this,"onTouchCancel",function(e){r.onTouchEnd(e)}),y(this,"onTouchEnd",function(e){var i=r._pos,n=r.root;r.__clearPress(),n.removeEventListener(r.events.cancel,r.onTouchCancel),n.removeEventListener(r.events.end,r.onTouchEnd),n.removeEventListener(r.events.move,r.onTouchMove),e.moving=i.moving,e.press=i.press,i.press&&r.trigger(pe.PRESS_END,e),r.trigger(pe.TOUCH_END,e),!i.press&&!i.moving&&r.__setDb(e),i.press=!1,i.start=!1,i.moving=!1}),y(this,"onTouchMove",function(e){var i=r._pos,n=r.config,s=pi(e.touches),l=s?parseInt(s.pageX,10):e.pageX,u=s?parseInt(s.pageY,10):e.pageX,c=l-i.x,h=u-i.y;Math.abs(h)<n.miniStep&&Math.abs(c)<n.miniStep||(r.__clearPress(),i.press&&r.trigger(pe.PRESS_END,e),i.press=!1,i.moving=!0,r.trigger(pe.TOUCH_MOVE,e))}),this._pos={moving:!1,start:!1,x:0,y:0},this.config=Jr(),Object.keys(t).map(function(e){r.config[e]=t[e]}),this.root=a,this.events=t.eventType==="mouse"?Zr:qr,this.pressIntrvalId=null,this.dbIntrvalId=null,this.__handlers={},this._initEvent()}return w(o,[{key:"_initEvent",value:function(){this.root.addEventListener(this.events.start,this.onTouchStart)}},{key:"__setPress",value:function(r){var t=this,e=this.config;this.pressIntrvalId&&this.__clearPress(),this.pressIntrvalId=setTimeout(function(){t.trigger(pe.PRESS,r),t._pos.press=!0,t.__clearPress()},e.pressDelay)}},{key:"__clearPress",value:function(){window.clearTimeout(this.pressIntrvalId),this.pressIntrvalId=null}},{key:"__setDb",value:function(r){var t=this,e=this.config;if(this.dbIntrvalId){this.__clearDb(),this.trigger(pe.DOUBlE_CLICK,r);return}this.dbIntrvalId=setTimeout(function(){t.__clearDb(),!t._pos.start&&!t._pos.press&&!t._pos.moving&&t.trigger(pe.CLICK,r)},e.dbClickDelay)}},{key:"__clearDb",value:function(){clearTimeout(this.dbIntrvalId),this.dbIntrvalId=null}},{key:"on",value:function(r,t){this.__handlers[r]||(this.__handlers[r]=[]),this.__handlers[r].push(t)}},{key:"off",value:function(r,t){if(this.__handlers[r]){for(var e=this.__handlers[r],i=-1,n=0;n<e.length;n++)if(e[n]===t){i=n;break}i>=0&&this.__handlers[r].splice(i,1)}}},{key:"trigger",value:function(r,t){this.__handlers[r]&&this.__handlers[r].map(function(e){try{e(t)}catch(i){console.error("trigger>>:".concat(r),i)}})}},{key:"destroy",value:function(){var r=this,t={touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"};Object.keys(t).forEach(function(e){r.root.removeEventListener(e,r[t[e]])})}}]),o}();function es(){return new DOMParser().parseFromString('<svg width="20" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg"\n  xmlns:xlink="http://www.w3.org/1999/xlink">\n  <path opacity="0.54"\n    d="M7.5 3.63397C8.16667 4.01887 8.16667 4.98113 7.5 5.36603L1.5 8.83013C0.833334 9.21503 0 8.7339 0 7.9641L0 1.0359C0 0.266098 0.833333 -0.215027 1.5 0.169873L7.5 3.63397Z"\n    fill="white" />\n  <path transform="translate(5 0)" d="M7.5 3.63397C8.16667 4.01887 8.16667 4.98113 7.5 5.36603L1.5 8.83013C0.833334 9.21503 0 8.7339 0 7.9641L0 1.0359C0 0.266098 0.833333 -0.215027 1.5 0.169873L7.5 3.63397Z" fill="white"/>\n</svg>',"image/svg+xml").firstChild}var Pe={AUTO:"auto",SEEKING:"seeking",PLAYBACK:"playbackrate",LIGHT:""},_t=["videoClick","videoDbClick"],ts=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),y(v(e),"onTouchStart",function(i){var n=v(e),s=n.player,l=n.config,u=n.pos,c=n.playerConfig,h=e.getTouche(i);if(h&&!l.disableGesture&&e.duration>0&&!s.ended){u.isStart=!0,e.timer&&clearTimeout(e.timer),f.checkIsFunction(c.disableSwipeHandler)&&c.disableSwipeHandler(),e.find(".xg-dur").innerHTML=f.format(e.duration);var d=e.root.getBoundingClientRect();s.rotateDeg===90?(u.top=d.left,u.left=d.top,u.width=d.height,u.height=d.width):(u.top=d.top,u.left=d.left,u.width=d.width,u.height=d.height);var g=parseInt(h.pageX-u.left,10),p=parseInt(h.pageY-u.top,10);u.x=s.rotateDeg===90?p:g,u.y=s.rotateDeg===90?g:p,u.scopeL=l.scopeL*u.width,u.scopeR=(1-l.scopeR)*u.width,u.scopeM1=u.width*(1-l.scopeM)/2,u.scopeM2=u.width-u.scopeM1}}),y(v(e),"onTouchMove",function(i){var n=e.getTouche(i),s=v(e),l=s.pos,u=s.config,c=s.player;if(!(!n||u.disableGesture||!e.duration||!l.isStart)){var h=u.miniMoveStep,d=u.hideControlsActive,g=parseInt(n.pageX-l.left,10),p=parseInt(n.pageY-l.top,10),m=c.rotateDeg===90?p:g,_=c.rotateDeg===90?g:p;if(Math.abs(m-l.x)>h||Math.abs(_-l.y)>h){var b=m-l.x,T=_-l.y,k=l.scope;if(k===-1&&(k=e.checkScope(m,_,b,T,l),k===0&&(d?c.blur():c.focus({autoHide:!1}),!l.time&&(l.time=parseInt(c.currentTime*1e3,10)+e.timeOffset*1e3)),l.scope=k),k===-1||k>0&&!u.gestureY||k===0&&!u.gestureX)return;e.executeMove(b,T,k,l.width,l.height),l.x=m,l.y=_}}}),y(v(e),"onTouchEnd",function(i){var n=v(e),s=n.player,l=n.pos,u=n.playerConfig;if(setTimeout(function(){s.getPlugin("progress")&&s.getPlugin("progress").resetSeekState()},10),!!l.isStart){l.scope>-1&&i.cancelable&&i.preventDefault();var c=e.config,h=c.disableGesture,d=c.gestureX;!h&&d?e.endLastMove(l.scope):l.time=0,l.scope=-1,e.resetPos(),f.checkIsFunction(u.enableSwipeHandler)&&u.enableSwipeHandler(),e.changeAction(Pe.AUTO)}}),y(v(e),"onRootTouchMove",function(i){e.config.disableGesture||!e.config.gestureX||e.checkIsRootTarget(i)&&(i.stopPropagation(),e.pos.isStart?e.onTouchMove(i):e.onTouchStart(i))}),y(v(e),"onRootTouchEnd",function(i){e.pos.scope>-1&&e.onTouchEnd(i)}),e.pos={isStart:!1,x:0,y:0,time:0,volume:0,rate:1,light:0,width:0,height:0,scopeL:0,scopeR:0,scopeM1:0,scopeM2:0,scope:-1},e.timer=null,e}return w(r,[{key:"duration",get:function(){return this.playerConfig.customDuration||this.player.duration}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"registerIcons",value:function(){return{seekTipIcon:{icon:es,class:"xg-seek-pre"}}}},{key:"afterCreate",value:function(){var e=this;_t.map(function(d){e.__hooks[d]=null});var i=this.playerConfig,n=this.config,s=this.player;i.closeVideoDblclick===!0&&(n.closedbClick=!0),this.resetPos(),f.isUndefined(i.disableGesture)||(n.disableGesture=!!i.disableGesture),this.appendChild(".xg-seek-icon",this.icons.seekTipIcon),this.xgMask=f.createDom("xg-mask","",{},"xgmask"),s.root.appendChild(this.xgMask),this.initCustomStyle(),this.registerThumbnail();var l=this.domEventType==="mouse"?"mouse":"touch";this.touch=new Qr(this.root,{eventType:l,needPreventDefault:!this.config.disableGesture}),this.root.addEventListener("contextmenu",function(d){d.preventDefault()}),s.root.addEventListener("touchmove",this.onRootTouchMove,!0),s.root.addEventListener("touchend",this.onRootTouchEnd,!0),s.root.addEventListener("touchcancel",this.onRootTouchEnd,!0);var u=this.player.controls;u&&u.center&&(u.center.addEventListener("touchmove",this.onRootTouchMove,!0),u.center.addEventListener("touchend",this.onRootTouchEnd,!0),u.center.addEventListener("touchcancel",this.onRootTouchEnd,!0)),this.on(he,function(){var d=e.player,g=e.config;d.duration>0&&d.duration*1e3<g.moveDuration&&(g.moveDuration=d.duration*1e3)}),this.on([ue,we],function(){var d=e.pos,g=d.time,p=d.isStart;!p&&g>0&&(e.pos.time=0)});var c={touchstart:"onTouchStart",touchmove:"onTouchMove",touchend:"onTouchEnd",press:"onPress",pressend:"onPressEnd",click:"onClick",doubleclick:"onDbClick"};if(Object.keys(c).map(function(d){e.touch.on(d,function(g){e[c[d]](g)})}),!n.disableActive){var h=s.plugins.progress;h&&(h.addCallBack("dragmove",function(d){e.activeSeekNote(d.currentTime,d.forward)}),["dragend","click"].forEach(function(d){h.addCallBack(d,function(){e.changeAction(Pe.AUTO)})}))}}},{key:"registerThumbnail",value:function(){var e=this.player,i=e.plugins.thumbnail;if(i&&i.usable){this.thumbnail=i.createThumbnail(null,"mobile-thumbnail");var n=this.find(".time-preview");n.insertBefore(this.thumbnail,n.children[0])}}},{key:"initCustomStyle",value:function(){var e=this.playerConfig||{},i=e.commonStyle,n=i.playedColor,s=i.progressColor,l=i.timePreviewStyle,u=i.curTimeColor,c=i.durationColor;if(n&&(this.find(".xg-curbar").style.backgroundColor=n),s&&(this.find(".xg-bar").style.backgroundColor=s),l){var h=this.find(".time-preview");Object.keys(l).forEach(function(p){h.style[p]=l[p]})}var d=u||n,g=c;d&&(this.find(".xg-cur").style.color=d),g&&(this.find(".xg-dur").style.color=g),this.config.disableTimeProgress&&f.addClass(this.find(".xg-timebar"),"hide")}},{key:"resetPos",value:function(){var e=this;this.pos?(this.pos.isStart=!1,this.pos.scope=-1,["x","y","width","height","scopeL","scopeR","scopeM1","scopeM2"].map(function(i){e.pos[i]=0})):this.pos={isStart:!1,x:0,y:0,volume:0,rate:1,light:0,width:0,height:0,scopeL:0,scopeR:0,scopeM1:0,scopeM2:0,scope:-1,time:0}}},{key:"changeAction",value:function(e){var i=this.player,n=this.root;n.setAttribute("data-xg-action",e);var s=i.plugins.start;s&&s.recover()}},{key:"getTouche",value:function(e){var i=this.player.rotateDeg,n=e.touches&&e.touches.length>0?e.touches[e.touches.length-1]:e;return i===0?{pageX:n.pageX,pageY:n.pageY}:{pageX:n.pageX,pageY:n.pageY}}},{key:"checkScope",value:function(e,i,n,s,l){var u=l.width,c=-1;if(e<0||e>u)return c;var h=Math.abs(s===0?n:n/s);return Math.abs(n)>0&&h>=1.73&&e>l.scopeM1&&e<l.scopeM2?c=0:(Math.abs(n)===0||h<=.57)&&(c=e<l.scopeL?1:e>l.scopeR?2:3),c}},{key:"executeMove",value:function(e,i,n,s,l){switch(n){case 0:this.updateTime(e/s*this.config.scopeM);break;case 1:this.updateBrightness(i/l);break;case 2:A.os.isIos||this.updateVolume(i/l);break}}},{key:"endLastMove",value:function(e){var i=this,n=this.pos,s=this.player,l=this.config,u=(n.time-this.timeOffset)/1e3;switch(e){case 0:s.seek(Number(u).toFixed(1)),l.hideControlsEnd?s.blur():s.focus(),this.timer=setTimeout(function(){i.pos.time=0},500);break}this.changeAction(Pe.AUTO)}},{key:"checkIsRootTarget",value:function(e){var i=this.player.plugins||{};return i.progress&&i.progress.root.contains(e.target)?!1:i.start&&i.start.root.contains(e.target)||i.controls&&i.controls.root.contains(e.target)}},{key:"sendUseAction",value:function(e){var i=this.player.paused;this.emitUserAction(e,"switch_play_pause",{prop:"paused",from:i,to:!i})}},{key:"clickHandler",value:function(e){var i=this.player,n=this.config,s=this.playerConfig;if(i.state<F.RUNNING){s.closeVideoClick||(this.sendUseAction(f.createEvent("click")),i.play());return}!n.closedbClick||s.closeVideoClick?i.isActive?i.blur():i.focus():s.closeVideoClick||((i.isActive||n.focusVideoClick)&&(this.sendUseAction(f.createEvent("click")),this.switchPlayPause()),i.focus())}},{key:"dbClickHandler",value:function(e){var i=this.config,n=this.player;!i.closedbClick&&n.state>=F.RUNNING&&(this.sendUseAction(f.createEvent("dblclick")),this.switchPlayPause())}},{key:"onClick",value:function(e){var i=this,n=this.player;oe(this,_t[0],function(s,l){i.clickHandler(l.e)},{e,paused:n.paused})}},{key:"onDbClick",value:function(e){var i=this,n=this.player;oe(this,_t[1],function(s,l){i.dbClickHandler(l.e)},{e,paused:n.paused})}},{key:"onPress",value:function(e){var i=this.pos,n=this.config,s=this.player;n.disablePress||(i.rate=this.player.playbackRate,this.emitUserAction("press","change_rate",{prop:"playbackRate",from:s.playbackRate,to:n.pressRate}),s.playbackRate=n.pressRate,this.changeAction(Pe.PLAYBACK))}},{key:"onPressEnd",value:function(e){var i=this.pos,n=this.config,s=this.player;n.disablePress||(this.emitUserAction("pressend","change_rate",{prop:"playbackRate",from:s.playbackRate,to:i.rate}),s.playbackRate=i.rate,i.rate=1,this.changeAction(Pe.AUTO))}},{key:"updateTime",value:function(e){var i=this.player,n=this.config,s=this.player.duration;e=Number(e.toFixed(4));var l=parseInt(e*n.moveDuration,10)+this.timeOffset;l+=this.pos.time,l=l<0?0:l>s*1e3?s*1e3-200:l,i.getPlugin("time")&&i.getPlugin("time").updateTime(l/1e3),i.getPlugin("progress")&&i.getPlugin("progress").updatePercent(l/1e3/this.duration,!0),this.activeSeekNote(l/1e3,e>0),n.isTouchingSeek&&i.seek(Number((l-this.timeOffset)/1e3).toFixed(1)),this.pos.time=l}},{key:"updateVolume",value:function(e){this.player.rotateDeg&&(e=-e);var i=this.player,n=this.pos;if(e=parseInt(e*100,10),n.volume+=e,!(Math.abs(n.volume)<10)){var s=parseInt(i.volume*10,10)-parseInt(n.volume/10,10);s=s>10?10:s<1?0:s,i.volume=s/10,n.volume=0}}},{key:"updateBrightness",value:function(e){var i=this.pos,n=this.config,s=this.xgMask;if(n.darkness){this.player.rotateDeg&&(e=-e);var l=i.light+.8*e;l=l>n.maxDarkness?n.maxDarkness:l<0?0:l,s&&(s.style.backgroundColor="rgba(0,0,0,".concat(l,")")),i.light=l}}},{key:"activeSeekNote",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,n=this.player,s=this.config,l=!(this.duration!==1/0&&this.duration>0);if(!(!e||typeof e!="number"||l||s.disableActive)){e<0?e=0:e>n.duration&&(e=n.duration-.2),this.changeAction(Pe.SEEKING);var u=n.plugins.start;u&&u.focusHide(),this.find(".xg-dur").innerHTML=f.format(this.duration),this.find(".xg-cur").innerHTML=f.format(e),this.find(".xg-curbar").style.width="".concat(e/this.duration*100,"%"),i?f.removeClass(this.find(".xg-seek-show"),"xg-back"):f.addClass(this.find(".xg-seek-show"),"xg-back"),this.updateThumbnails(e)}}},{key:"updateThumbnails",value:function(e){var i=this.player,n=i.plugins.thumbnail;n&&n.usable&&this.thumbnail&&n.update(this.thumbnail,e,160,90)}},{key:"switchPlayPause",value:function(){var e=this.player;if(e.state<F.ATTACHED)return!1;e.ended||(e.paused?e.play():e.pause())}},{key:"disableGesture",value:function(){this.config.disableGesture=!0}},{key:"enableGesture",value:function(){this.config.disableGesture=!1}},{key:"destroy",value:function(){var e=this.player;this.timer&&clearTimeout(this.timer),this.thumbnail=null,e.root.removeChild(this.xgMask),this.xgMask=null,this.touch&&this.touch.destroy(),this.touch=null,e.root.removeEventListener("touchmove",this.onRootTouchMove,!0),e.root.removeEventListener("touchend",this.onRootTouchEnd,!0),e.root.removeEventListener("touchcancel",this.onRootTouchEnd,!0);var i=this.player.controls;i&&i.center&&(i.center.removeEventListener("touchmove",this.onRootTouchMove,!0),i.center.removeEventListener("touchend",this.onRootTouchEnd,!0),i.center.removeEventListener("touchcancel",this.onRootTouchEnd,!0))}},{key:"render",value:function(){var e=this.config.gradient!=="normal"?"gradient ".concat(this.config.gradient):"gradient";return'\n     <xg-trigger class="trigger">\n     <div class="'.concat(e,'"></div>\n        <div class="time-preview">\n            <div class="xg-seek-show ').concat(this.config.disableSeekIcon?" hide-seek-icon":"",'">\n              <i class="xg-seek-icon"></i>\n              <span class="xg-cur">00:00</span>\n              <span class="xg-separator">/</span>\n              <span class="xg-dur">00:00</span>\n            </div>\n              <div class="xg-bar xg-timebar">\n                <div class="xg-curbar"></div>\n              </div>\n        </div>\n        <div class="xg-playbackrate xg-top-note">\n            <span><i>').concat(this.config.pressRate,"X</i>").concat(this.i18n.FORWARD,"</span>\n        </div>\n     </xg-trigger>\n    ")}}],[{key:"pluginName",get:function(){return"mobile"}},{key:"defaultConfig",get:function(){return{index:0,disableGesture:!1,gestureX:!0,gestureY:!0,gradient:"normal",isTouchingSeek:!1,miniMoveStep:5,miniYPer:5,scopeL:.25,scopeR:.25,scopeM:.9,pressRate:2,darkness:!0,maxDarkness:.8,disableActive:!1,disableTimeProgress:!1,hideControlsActive:!1,hideControlsEnd:!1,moveDuration:60*6*1e3,closedbClick:!1,disablePress:!0,disableSeekIcon:!1,focusVideoClick:!1}}}]),r}(N);function is(o){o.preventDefault(),o.returnValue=!1}function vi(o){var a=o.tagName;return!!(a==="INPUT"||a==="TEXTAREA"||o.isContentEditable)}var mi=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"onBodyKeyDown",function(s){if(t.player){var l=s||window.event,u=l.keyCode,c=v(t),h=c._keyState,d=c.player,g=t.config,p=g.disable,m=g.disableBodyTrigger,_=g.isIgnoreUserActive;if(p||m||!(d.isUserActive||_)||vi(l.target)||!t.checkIsVisible()||l.metaKey||l.altKey||l.ctrlKey){h.isBodyKeyDown=!1;return}!s.repeat&&!h.isKeyDown&&((l.target===document.body||t.config.isGlobalTrigger&&!vi(l.target))&&t.checkCode(u,!0)&&(h.isBodyKeyDown=!0),document.addEventListener("keyup",t.onBodyKeyUp)),h.isBodyKeyDown&&t.handleKeyDown(l)}}),y(v(t),"onBodyKeyUp",function(s){t.player&&(document.removeEventListener("keyup",t.onBodyKeyUp),t.handleKeyUp(s))}),y(v(t),"onKeydown",function(s){if(t.player){var l=s||window.event,u=v(t),c=u._keyState;if(!l.repeat){if(t.config.disable||t.config.disableRootTrigger||l.metaKey||l.altKey||l.ctrlKey)return;l&&(l.keyCode===37||t.checkCode(l.keyCode))&&(l.target===t.player.root||l.target===t.player.video||l.target===t.player.controls.el)&&(c.isKeyDown=!0),t.player.root.addEventListener("keyup",t.onKeyup)}c.isKeyDown&&t.handleKeyDown(l)}}),y(v(t),"onKeyup",function(s){t.player&&(t.player.root.removeEventListener("keyup",t.onKeyup),t.handleKeyUp(s))}),t}return w(r,[{key:"mergekeyCodeMap",value:function(){var e=this,i=this.config.keyCodeMap;i&&Object.keys(i).map(function(n){e.keyCodeMap[n]?["keyCode","action","disable","pressAction","disablePress","isBodyTarget"].map(function(s){i[n][s]&&(e.keyCodeMap[n][s]=i[n][s])}):e.keyCodeMap[n]=i[n]})}},{key:"afterCreate",value:function(){this.config.disable=!this.playerConfig.keyShortcut;var e=typeof this.config.seekStep=="function"?this.config.seekStep(this.player):this.config.seekStep;!e||typeof e!="number"||(this.seekStep=e),this.keyCodeMap={space:{keyCode:32,action:"playPause",disable:!1,disablePress:!1,noBodyTarget:!1},up:{keyCode:38,action:"upVolume",disable:!1,disablePress:!1,noBodyTarget:!0},down:{keyCode:40,action:"downVolume",disable:!1,disablePress:!1,noBodyTarget:!0},left:{keyCode:37,action:"seekBack",disablePress:!1,disable:!1},right:{keyCode:39,action:"seek",pressAction:"changePlaybackRate",disablePress:!1,disable:!1},esc:{keyCode:27,action:"exitFullscreen",disablePress:!0,disable:!1}},this.mergekeyCodeMap(),this._keyState={isKeyDown:!1,isBodyKeyDown:!1,isPress:!1,tt:0,playbackRate:0},this.player.root.addEventListener("keydown",this.onKeydown),document.addEventListener("keydown",this.onBodyKeyDown)}},{key:"setConfig",value:function(e){var i=this;Object.keys(e).forEach(function(n){i.config[n]=e[n]})}},{key:"checkIsVisible",value:function(){if(!this.config.checkVisible)return!0;var e=this.player.root.getBoundingClientRect(),i=e.height,n=e.top,s=e.bottom,l=window.innerHeight;return!(n<0&&n<0-i*.9||s>0&&s-l>i*.9)}},{key:"checkCode",value:function(e,i){var n=this,s=!1;return Object.keys(this.keyCodeMap).map(function(l){n.keyCodeMap[l]&&e===n.keyCodeMap[l].keyCode&&!n.keyCodeMap[l].disable&&(s=!i||i&&!n.keyCodeMap[l].noBodyTarget)}),s}},{key:"downVolume",value:function(e){var i=this.player;if(!(i.volume<=0)){var n=parseFloat((i.volume-.1).toFixed(1)),s={volume:{from:i.volume,to:n}};this.emitUserAction(e,"change_volume",{props:s}),n>=0?i.volume=n:i.volume=0}}},{key:"upVolume",value:function(e){var i=this.player;if(!(i.volume>=1)){var n=parseFloat((i.volume+.1).toFixed(1)),s={volume:{from:i.volume,to:n}};this.emitUserAction(e,"change_volume",{props:s}),n<=1?i.volume=n:i.volume=1}}},{key:"seek",value:function(e){var i=this.player,n=i.currentTime,s=i.offsetCurrentTime,l=i.duration,u=i.offsetDuration,c=i.timeSegments,h=s>-1?s:n,d=u||l,g=e.repeat&&this.seekStep>=4?parseInt(this.seekStep/2,10):this.seekStep;h+g<=d?h=h+g:h=d;var p=f.getCurrentTimeByOffset(h,c),m={currentTime:{from:n,to:p}};this.emitUserAction(e,"seek",{props:m}),this.player.currentTime=p}},{key:"seekBack",value:function(e){var i=this.player,n=i.currentTime,s=i.offsetCurrentTime,l=i.timeSegments,u=e.repeat?parseInt(this.seekStep/2,10):this.seekStep,c=s>-1?s:n,h=c-u;h<0&&(h=0),h=f.getCurrentTimeByOffset(h,l);var d={currentTime:{from:n,to:h}};this.emitUserAction(e,"seek",{props:d}),this.player.currentTime=h}},{key:"changePlaybackRate",value:function(e){var i=this._keyState,n=this.config,s=this.player;i.playbackRate===0&&(i.playbackRate=s.playbackRate,s.playbackRate=n.playbackRate)}},{key:"playPause",value:function(e){var i=this.player;i&&(this.emitUserAction(e,"switch_play_pause"),i.paused?i.play():i.pause())}},{key:"exitFullscreen",value:function(e){var i=this.player,n=i.fullscreen,s=i.cssfullscreen;n&&(this.emitUserAction("keyup","switch_fullscreen",{prop:"fullscreen",from:n,to:!n}),i.exitFullscreen()),s&&(this.emitUserAction("keyup","switch_css_fullscreen",{prop:"cssfullscreen",from:s,to:!s}),i.exitCssFullscreen())}},{key:"handleKeyDown",value:function(e){var i=this._keyState;if(e.repeat){i.isPress=!0;var n=Date.now();if(n-i.tt<200)return;i.tt=n}this.handleKeyCode(e.keyCode,e,i.isPress)}},{key:"handleKeyUp",value:function(e){var i=this._keyState;i.playbackRate>0&&(this.player.playbackRate=i.playbackRate,i.playbackRate=0),i.isKeyDown=!1,i.isPress=!1,i.tt=0}},{key:"handleKeyCode",value:function(e,i,n){for(var s=Object.keys(this.keyCodeMap),l=0;l<s.length;l++){var u=this.keyCodeMap[s[l]],c=u.action,h=u.keyCode,d=u.disable,g=u.pressAction,p=u.disablePress;if(h===e){if(!d&&!(n&&p)){var m=n&&g||c;typeof m=="function"?c(i,this.player,n):typeof m=="string"&&typeof this[m]=="function"&&this[m](i,this.player,n),this.emit(Ki,V({key:s[l],target:i.target,isPress:n},this.keyCodeMap[s[l]]))}is(i),i.stopPropagation();break}}}},{key:"destroy",value:function(){this.player.root.removeEventListener("keydown",this.onKeydown),document.removeEventListener("keydown",this.onBodyKeyDown),this.player.root.removeEventListener("keyup",this.onKeyup),document.removeEventListener("keyup",this.onBodyKeyUp)}},{key:"disable",value:function(){this.config.disable=!0}},{key:"enable",value:function(){this.config.disable=!1}}],[{key:"pluginName",get:function(){return"keyboard"}},{key:"defaultConfig",get:function(){return{seekStep:10,checkVisible:!1,disableBodyTrigger:!1,disableRootTrigger:!1,isGlobalTrigger:!0,keyCodeMap:{},disable:!1,playbackRate:2,isIgnoreUserActive:!0}}}]),r}(ye);function ns(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="-5 -5 110 110">\n  <path d="M100,50A50,50,0,1,1,50,0" stroke-width="5" stroke="#ddd" stroke-dasharray="236" fill="none"></path>\n</svg>\n',"image/svg+xml").firstChild}var rs=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"registerIcons",value:function(){return{loadingIcon:ns}}},{key:"afterCreate",value:function(){this.appendChild("xg-loading-inner",this.icons.loadingIcon)}},{key:"render",value:function(){return'\n    <xg-loading class="xgplayer-loading">\n      <xg-loading-inner></xg-loading-inner>\n    </xg-loading>'}}],[{key:"pluginName",get:function(){return"loading"}},{key:"defaultConfig",get:function(){return{position:H.ROOT}}}]),r}(N),ss=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"_onDurationChange",function(){t.updateSegments();var s=t.player,l=s.currentTime,u=s.timeSegments;if(t._checkIfEnabled(u)){var c=f.getIndexByTime(l,u),h=f.getOffsetCurrentTime(l,u,c);t.player.offsetCurrentTime=h,t.changeIndex(c,u)}}),y(v(t),"_onLoadedData",function(){var s=t.player.timeSegments;if(t._checkIfEnabled(s)){var l=f.getOffsetCurrentTime(0,s);t.player.offsetCurrentTime=l,t.changeIndex(0,s),t.curPos.start>0&&(t.player.currentTime=t.curPos.start)}}),y(v(t),"_onTimeupdate",function(){var s=t.player,l=s.currentTime,u=s.timeSegments;if(t._checkIfEnabled(u)){var c=u.length;t.lastCurrentTime=l;var h=f.getIndexByTime(l,u);h!==t.curIndex&&t.changeIndex(h,u);var d=f.getOffsetCurrentTime(l,u,h);if(t.player.offsetCurrentTime=d,!!t.curPos){var g=t.curPos,p=g.start,m=g.end;l<p?t.player.currentTime=p:l>m&&h>=c-1&&t.player.pause()}}}),y(v(t),"_onSeeking",function(){var s=t.player,l=s.currentTime,u=s.timeSegments;if(t._checkIfEnabled(u))if(l<u[0].start)t.player.currentTime=u[0].start;else if(l>u[u.length-1].end)t.player.currentTime=u[u.length-1].end;else{var c=f.getIndexByTime(l,u);if(c>=0){var h=t.getSeekTime(l,t.lastCurrentTime,c,u);h>=0&&(t.player.currentTime=h)}}}),y(v(t),"_onPlay",function(){var s=t.player,l=s.currentTime,u=s.timeSegments;t._checkIfEnabled(u)&&l>=u[u.length-1].end&&(t.player.currentTime=u[0].start)}),t}return w(r,[{key:"afterCreate",value:function(){this.curIndex=-1,this.curPos=null,this.lastCurrentTime=0,this.updateSegments(),this.on(he,this._onDurationChange),this.on(fe,this._onLoadedData),this.on(J,this._onTimeupdate),this.on(rt,this._onSeeking),this.on(Q,this._onPlay)}},{key:"setConfig",value:function(e){var i=this;if(e){var n=Object.keys(e);n.length<1||(n.forEach(function(s){i.config[s]=e[s]}),this.updateSegments())}}},{key:"updateSegments",value:function(){var e=this.config,i=e.disable,n=e.segments,s=this.player;if(i||!n||n.length===0)s.timeSegments=[],s.offsetDuration=0,s.offsetCurrentTime=-1;else{var l=this.formatTimeSegments(n,s.duration);s.timeSegments=l,s.offsetDuration=l.length>0?l[l.length-1].duration:0}}},{key:"formatTimeSegments",value:function(e,i){var n=[];return e?(e.sort(function(s,l){return s.start-l.start}),e.forEach(function(s,l){var u={};if(u.start=s.start<0?0:s.start,u.end=i>0&&s.end>i?i:s.end,!(i>0&&u.start>i)){n.push(u);var c=u.end-u.start;if(l===0)u.offset=s.start,u.cTime=0,u.segDuration=c,u.duration=c;else{var h=n[l-1];u.offset=h.offset+(u.start-h.end),u.cTime=h.duration+h.cTime,u.segDuration=c,u.duration=h.duration+c}}}),n):[]}},{key:"getSeekTime",value:function(e,i,n,s){var l=-1,u=s[n],c=u.start,h=u.end;if(e>=c&&e<=h)return l;var d=e-i;if(d<0&&e<c){var g=i>c?i-c:0;return l=n-1>=0?s[n-1].end+d+g:0,l}return-1}},{key:"_checkIfEnabled",value:function(e){return!(!e||e.length<1)}},{key:"changeIndex",value:function(e,i){this.curIndex=e,e>=0&&i.length>0?this.curPos=i[e]:this.curPos=null}}],[{key:"pluginName",get:function(){return"TimeSegmentsControls"}},{key:"defaultConfig",get:function(){return{disable:!0,segments:[]}}}]),r}(ye);function as(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="-4 -6 40 40" fill="none">\n  <g>\n    <path transform="scale(1.5 1.5)" d="M11.6665 9.16663H4.1665C2.78579 9.16663 1.6665 10.2859 1.6665 11.6666V15.8333C1.6665 17.214 2.78579 18.3333 4.1665 18.3333H11.6665C13.0472 18.3333 14.1665 17.214 14.1665 15.8333V11.6666C14.1665 10.2859 13.0472 9.16663 11.6665 9.16663Z" fill="white"/>\n    <path transform="scale(1.5 1.5)" fill-rule="evenodd" clip-rule="evenodd" d="M3.88148 4.06298C3.75371 4.21005 3.67667 4.40231 3.67749 4.61242C3.67847 4.87253 3.79852 5.10435 3.98581 5.25646L6.99111 8.05895C7.32771 8.37283 7.85502 8.35443 8.16891 8.01782C8.48279 7.68122 8.46437 7.15391 8.12778 6.84003L6.62061 5.43457L9.8198 5.4224C9.82848 5.42239 9.8372 5.42221 9.84591 5.4219C10.9714 5.38233 12.0885 5.6285 13.0931 6.13744C14.0976 6.64635 14.957 7.40148 15.5908 8.33234C16.2246 9.2632 16.6122 10.3394 16.7177 11.4606C16.823 12.5819 16.6427 13.7115 16.1934 14.7442C16.0098 15.1661 16.203 15.6571 16.6251 15.8408C17.0471 16.0243 17.5381 15.8311 17.7216 15.4091C18.2833 14.1183 18.5087 12.7063 18.3771 11.3047C18.2453 9.90318 17.7607 8.55792 16.9684 7.39433C16.1761 6.23073 15.1021 5.28683 13.8463 4.65065C12.5946 4.01651 11.203 3.70872 9.80072 3.75583L6.43415 3.76862L7.96326 2.12885C8.27715 1.79225 8.25872 1.26494 7.92213 0.951061C7.58553 0.63718 7.05822 0.655585 6.74433 0.99219L3.90268 4.0395C3.89545 4.04724 3.88841 4.05509 3.88154 4.06303L3.88148 4.06298Z" fill="white"/>\n  </g>\n  <defs>\n    <clipPath>\n      <rect width="40" height="40" fill="white"/>\n    </clipPath>\n  </defs>\n</svg>\n',"image/svg+xml").firstChild}var os=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),e.rotateDeg=e.config.rotateDeg||0,e}return w(r,[{key:"afterCreate",value:function(){var e=this;if(!this.config.disable){I(P(r.prototype),"afterCreate",this).call(this),this.appendChild(".xgplayer-icon",this.icons.rotate),this.onBtnClick=this.onBtnClick.bind(this),this.bind(".xgplayer-icon",["click","touchend"],this.onBtnClick),this.on(me,function(){e.rotateDeg&&e.config.innerRotate&&f.setTimeout(e,function(){e.updateRotateDeg(e.rotateDeg,e.config.innerRotate)},100)});var i=this.player.root;this.rootWidth=i.style.width||i.offsetWidth||i.clientWidth,this.rootHeight=i.style.height||i.offsetHeight||i.clientHeight,this.rotateDeg&&this.updateRotateDeg(this.rotateDeg,this.config.innerRotate)}}},{key:"destroy",value:function(){I(P(r.prototype),"destroy",this).call(this),this.unbind(".xgplayer-icon",["click","touchend"],this.onBtnClick)}},{key:"onBtnClick",value:function(e){e.preventDefault(),e.stopPropagation(),this.emitUserAction(e,"rotate"),this.rotate(this.config.clockwise,this.config.innerRotate,1)}},{key:"updateRotateDeg",value:function(e,i){if(e||(e=0),i){this.player.videoRotateDeg=e;return}var n=this.player,s=this.rootWidth,l=this.rootHeight,u=n.root,c=n.innerContainer,h=n.media,d=u.offsetWidth,g=c&&i?c.offsetHeight:u.offsetHeight,p=s,m=l,_=0,b=0;(e===.75||e===.25)&&(p="".concat(g,"px"),m="".concat(d,"px"),_=-(g-d)/2,b=-(d-g)/2);var T="translate(".concat(_,"px,").concat(b,"px) rotate(").concat(e,"turn)"),k={transformOrigin:"center center",transform:T,webKitTransform:T,height:m,width:p},D=i?h:u,L=i?n.getPlugin("poster"):null;Object.keys(k).map(function(B){D.style[B]=k[B],L&&L.root&&(L.root.style[B]=k[B])})}},{key:"rotate",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,s=this.player;this.rotateDeg||(this.rotateDeg=0);var l=e?1:-1;this.rotateDeg=(this.rotateDeg+1+l*.25*n)%1,this.updateRotateDeg(this.rotateDeg,i),s.emit(zi,this.rotateDeg*360)}},{key:"registerIcons",value:function(){return{rotate:as}}},{key:"render",value:function(){if(!this.config.disable)return'\n    <xg-icon class="xgplayer-rotate">\n      <div class="xgplayer-icon">\n      </div>\n      '.concat(_e(this,"ROTATE_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"rotate"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_RIGHT,index:6,innerRotate:!0,clockwise:!1,rotateDeg:0,disable:!1}}}]),r}(xe);function ls(){return new DOMParser().parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M16.5 4.3H3.5C3.38954 4.3 3.3 4.38954 3.3 4.5V15.5C3.3 15.6105 3.38954 15.7 3.5 15.7H8.50005L8.50006 17.5H3.5C2.39543 17.5 1.5 16.6046 1.5 15.5V4.5C1.5 3.39543 2.39543 2.5 3.5 2.5H16.5C17.6046 2.5 18.5 3.39543 18.5 4.5V8.5H16.7V4.5C16.7 4.38954 16.6105 4.3 16.5 4.3ZM12 11.5C11.4477 11.5 11 11.9477 11 12.5L11 16.5C11 17.0523 11.4478 17.5 12 17.5H17.5C18.0523 17.5 18.5 17.0523 18.5 16.5L18.5 12.5C18.5 11.9477 18.0523 11.5 17.5 11.5H12Z"\n    fill="white" />\n</svg>',"image/svg+xml").firstChild}function us(){return new DOMParser().parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M16.5 4.3H3.5C3.38954 4.3 3.3 4.38954 3.3 4.5V15.5C3.3 15.6105 3.38954 15.7 3.5 15.7H8.50005L8.50006 17.5H3.5C2.39543 17.5 1.5 16.6046 1.5 15.5V4.5C1.5 3.39543 2.39543 2.5 3.5 2.5H16.5C17.6046 2.5 18.5 3.39543 18.5 4.5V8.5H16.7V4.5C16.7 4.38954 16.6105 4.3 16.5 4.3ZM12 11.5C11.4477 11.5 11 11.9477 11 12.5L11 16.5C11 17.0523 11.4478 17.5 12 17.5H17.5C18.0523 17.5 18.5 17.0523 18.5 16.5L18.5 12.5C18.5 11.9477 18.0523 11.5 17.5 11.5H12Z"\n    fill="white" />\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M9.4998 7.7C9.77595 7.7 9.9998 7.47614 9.9998 7.2V6.5C9.9998 6.22386 9.77595 6 9.4998 6H5.5402L5.52754 6.00016H5.5C5.22386 6.00016 5 6.22401 5 6.50016V10.4598C5 10.7359 5.22386 10.9598 5.5 10.9598H6.2C6.47614 10.9598 6.7 10.7359 6.7 10.4598V8.83005L8.76983 10.9386C8.96327 11.1357 9.27984 11.1386 9.47691 10.9451L9.97645 10.4548C10.1735 10.2613 10.1764 9.94476 9.983 9.7477L7.97289 7.7H9.4998Z"\n    fill="white" />\n</svg>',"image/svg+xml").firstChild}var Ie={PIP:"picture-in-picture",INLINE:"inline",FULLSCREEN:"fullscreen"},cs=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"switchPIP",function(s){if(!t.isPIPAvailable())return!1;s.stopPropagation&&s.stopPropagation(),t.isPip?(t.exitPIP(),t.emitUserAction(s,"change_pip",{props:"pip",from:!0,to:!1}),t.setAttr("data-state","normal")):t.player.media.readyState===4&&(t.requestPIP(),t.emitUserAction(s,"change_pip",{props:"pip",from:!1,to:!0}),t.setAttr("data-state","pip"))}),t}return w(r,[{key:"beforeCreate",value:function(e){typeof e.player.config.pip=="boolean"&&(e.config.showIcon=e.player.config.pip)}},{key:"afterCreate",value:function(){var e=this;this.isPIPAvailable()&&(I(P(r.prototype),"afterCreate",this).call(this),this.pMode=Ie.INLINE,this.initPipEvents(),this.config.showIcon&&this.initIcons(),this.once(st,function(){e.config.showIcon&&(f.removeClass(e.find(".xgplayer-icon"),"xg-icon-disable"),e.bind("click",e.switchPIP))}))}},{key:"registerIcons",value:function(){return{pipIcon:{icon:ls,class:"xg-get-pip"},pipIconExit:{icon:us,class:"xg-exit-pip"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.pipIcon),this.appendChild(".xgplayer-icon",e.pipIconExit)}},{key:"initPipEvents",value:function(){var e=this,i=this.player;this.leavePIPCallback=function(){var n=i.paused;f.setTimeout(e,function(){!n&&i.mediaPlay()},0),!n&&i.mediaPlay(),e.setAttr("data-state","normal"),e.pipWindow=null,i.emit(xt,!1)},this.enterPIPCallback=function(n){i.emit(xt,!0),n!=null&&n.pictureInPictureWindow&&(e.pipWindow=n.pictureInPictureWindow),e.setAttr("data-state","pip")},this.onWebkitpresentationmodechanged=function(n){var s=i.media.webkitPresentationMode;e.pMode===Ie.FULLSCREEN&&s!==Ie.FULLSCREEN&&i.onFullscreenChange(null,!1),e.pMode=s,s===Ie.PIP?e.enterPIPCallback(n):s===Ie.INLINE&&e.leavePIPCallback(n)},i.media&&(i.media.addEventListener("enterpictureinpicture",this.enterPIPCallback),i.media.addEventListener("leavepictureinpicture",this.leavePIPCallback),r.checkWebkitSetPresentationMode(i.media)&&i.media.addEventListener("webkitpresentationmodechanged",this.onWebkitpresentationmodechanged))}},{key:"copyStyleIntoPiPWindow",value:function(e){var i=re(document.styleSheets).map(function(s){try{return re(s.cssRules).map(function(u){return u.cssText}).join("")}catch(u){var l=document.createElement("link");l.rel="stylesheet",l.type=s.type,l.media=s.media,l.href=s.href,e.document.head.appendChild(l)}return""}).filter(Boolean).join("\n"),n=document.createElement("style");n.textContent=i,e.document.head.appendChild(n)}},{key:"requestPIP",value:function(){var e=this,i=this.player,n=this.playerConfig,s=this.config;if(!(!this.isPIPAvailable()||this.isPip))try{var l=n.poster;if(l&&(i.media.poster=f.typeOf(l)==="String"?l:l.poster),s.preferDocument&&this.isDocPIPAvailable()){var u={};if(s.width&&s.height)u.width=s.width,u.height=s.height;else{var c=i.root.getBoundingClientRect();u.width=c.width,u.height=c.height}documentPictureInPicture.requestWindow(u).then(function(h){var d=s.docPiPNode,g=s.docPiPStyle;e.enterPIPCallback();var p=d||i.root,m=p.parentElement,_=p.previousSibling,b=p.nextSibling;e.copyStyleIntoPiPWindow(h);var T=document.createElement("style");if(T.append("body{padding:0; margin:0;}"),g){var k="";typeof g=="string"?k=g:typeof g=="function"&&(k=g.call(s)),k&&T.append(k)}else p===i.root&&T.append("\n              .xgplayer{width: 100%!important; height: 100%!important;}\n            ");h.document.head.append(T),h.document.body.append(p),h.addEventListener("pagehide",function(D){m&&(b?m.insertBefore(p,b):_?m.insertBefore(p,_.nextSibling):m.appendChild(p)),e.leavePIPCallback()},{once:!0})})}else r.checkWebkitSetPresentationMode(i.media)?i.media.webkitSetPresentationMode("picture-in-picture"):i.media.requestPictureInPicture();return!0}catch(h){return console.error("requestPiP",h),!1}}},{key:"exitPIP",value:function(){var e=this.player;try{if(this.isPIPAvailable()&&this.isPip){var i;this.isDocPIPAvailable()&&(i=documentPictureInPicture)!==null&&i!==void 0&&i.window?documentPictureInPicture.window.close():r.checkWebkitSetPresentationMode(e.media)?e.media.webkitSetPresentationMode("inline"):document.exitPictureInPicture()}return!0}catch(n){return console.error("exitPIP",n),!1}}},{key:"isPip",get:function(){var e,i=this.player;return!!(this.isDocPIPAvailable()&&(e=documentPictureInPicture)!==null&&e!==void 0&&e.window)||document.pictureInPictureElement&&document.pictureInPictureElement===i.media||i.media.webkitPresentationMode===Ie.PIP}},{key:"isPIPAvailable",value:function(){var e=this.player.media,i=f.typeOf(document.pictureInPictureEnabled)==="Boolean"?document.pictureInPictureEnabled:!1;return i&&(f.typeOf(e.disablePictureInPicture)==="Boolean"&&!e.disablePictureInPicture||e.webkitSupportsPresentationMode&&f.typeOf(e.webkitSetPresentationMode)==="Function")||this.isDocPIPAvailable()}},{key:"isDocPIPAvailable",value:function(){return"documentPictureInPicture"in window&&/^(https|file)/.test(location.protocol)}},{key:"destroy",value:function(){I(P(r.prototype),"destroy",this).call(this);var e=this.player;e.media.removeEventListener("enterpictureinpicture",this.enterPIPCallback),e.media.removeEventListener("leavepictureinpicture",this.leavePIPCallback),r.checkWebkitSetPresentationMode(e.media)&&e.media.removeEventListener("webkitpresentationmodechanged",this.onWebkitpresentationmodechanged),this.exitPIP(),this.unbind("click",this.btnClick)}},{key:"render",value:function(){if(!(!this.config.showIcon||!this.isPIPAvailable()))return'<xg-icon class="xgplayer-pip">\n      <div class="xgplayer-icon xg-icon-disable">\n      </div>\n      '.concat(_e(this,"PIP",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"pip"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_RIGHT,index:6,showIcon:!1,preferDocument:!1,width:void 0,height:void 0,docPiPNode:void 0,docPiPStyle:void 0}}},{key:"checkWebkitSetPresentationMode",value:function(e){return typeof e.webkitSetPresentationMode=="function"}}]),r}(xe);function fs(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="40" viewBox="10 0 24 40">\n  <path transform="scale(0.038 0.028)" d="M800 380v768h-128v-352l-320 320v-704l320 320v-352z"></path>\n</svg>\n',"image/svg+xml").firstChild}var hs=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),y(v(e),"playNext",function(i){var n=v(e),s=n.player;i.preventDefault(),i.stopPropagation(),e.idx+1<e.config.urlList.length?(e.idx++,e.nextHandler(e.config.urlList[e.idx],e.idx),s.emit(Je,e.idx+1)):(e.nextHandler(),s.emit(Je))}),e.idx=-1,e}return w(r,[{key:"afterCreate",value:function(){!this.config.urlList||this.config.urlList.length===0||(this.appendChild(".xgplayer-icon",this.icons.playNext),this.initEvents())}},{key:"registerIcons",value:function(){return{playNext:fs}}},{key:"initEvents",value:function(){this.nextHandler=this.hook("nextClick",this.changeSrc);var e=A.device==="mobile"?"touchend":"click";this.bind(e,this.playNext),this.show()}},{key:"changeSrc",value:function(e){var i=this.player;e&&(i.pause(),i.currentTime=0,i.switchURL?i.switchURL(e):i.src=e,i.config.url=e,i.play())}},{key:"destroy",value:function(){this.unbind(["touchend","click"],this.playNext)}},{key:"render",value:function(){if(!(!this.config.urlList||this.config.urlList.length===0))return'\n     <xg-icon class="xgplayer-playnext">\n      <div class="xgplayer-icon">\n      </div>\n      '.concat(_e(this,"PLAYNEXT_TIPS",this.playerConfig.isHideTips),"\n     </xg-icon>\n    ")}}],[{key:"pluginName",get:function(){return"playNext"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_LEFT,index:1,url:null,urlList:[]}}}]),r}(N),fn={exports:{}};(function(o,a){(function(r,t){o.exports=t()})(_n,function(){return function r(t,e,i){var n=window,s="application/octet-stream",l=i||s,u=t,c=!e&&!i&&u,h=document.createElement("a"),d=function(M){return String(M)},g=n.Blob||n.MozBlob||n.WebKitBlob||d,p=e||"download",m,_;if(g=g.call?g.bind(n):Blob,String(this)==="true"&&(u=[u,l],l=u[0],u=u[1]),c&&c.length<2048&&(p=c.split("/").pop().split("?")[0],h.href=c,h.href.indexOf(c)!==-1)){var b=new XMLHttpRequest;return b.open("GET",c,!0),b.responseType="blob",b.onload=function(M){r(M.target.response,p,s)},setTimeout(function(){b.send()},0),b}if(/^data:([\w+-]+\/[\w+.-]+)?[,;]/.test(u))if(u.length>1024*1024*1.999&&g!==d)u=L(u),l=u.type||s;else return navigator.msSaveBlob?navigator.msSaveBlob(L(u),p):B(u);else if(/([\x80-\xff])/.test(u)){var T=0,k=new Uint8Array(u.length),D=k.length;for(T;T<D;++T)k[T]=u.charCodeAt(T);u=new g([k],{type:l})}m=u instanceof g?u:new g([u],{type:l});function L(M){var W=M.split(/[:;,]/),j=W[1],O=W[2]=="base64"?atob:decodeURIComponent,q=O(W.pop()),te=q.length,ie=0,Ce=new Uint8Array(te);for(ie;ie<te;++ie)Ce[ie]=q.charCodeAt(ie);return new g([Ce],{type:j})}function B(M,W){if("download"in h)return h.href=M,h.setAttribute("download",p),h.className="download-js-link",h.innerHTML="downloading...",h.style.display="none",document.body.appendChild(h),setTimeout(function(){h.click(),document.body.removeChild(h),W===!0&&setTimeout(function(){n.URL.revokeObjectURL(h.href)},250)},66),!0;if(/(Version)\/(\d+)\.(\d+)(?:\.(\d+))?.*Safari\//.test(navigator.userAgent))return/^data:/.test(M)&&(M="data:"+M.replace(/^data:([\w\/\-\+]+)/,s)),window.open(M)||confirm("Displaying New Document\n\nUse Save As... to download, then click back to return to this page.")&&(location.href=M),!0;var j=document.createElement("iframe");document.body.appendChild(j),!W&&/^data:/.test(M)&&(M="data:"+M.replace(/^data:([\w\/\-\+]+)/,s)),j.src=M,setTimeout(function(){document.body.removeChild(j)},333)}if(navigator.msSaveBlob)return navigator.msSaveBlob(m,p);if(n.URL)B(n.URL.createObjectURL(m),!0);else{if(typeof m=="string"||m.constructor===d)try{return B("data:"+l+";base64,"+n.btoa(m))}catch(M){return B("data:"+l+","+encodeURIComponent(m))}_=new FileReader,_.onload=function(M){B(this.result)},_.readAsDataURL(m)}return!0}})})(fn);var ds=fn.exports;const gs=Pi(ds);function ps(){return new DOMParser().parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24">\n  <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n    <g transform="translate(-488.000000, -340.000000)" fill="#FFFFFF">\n      <g id="Group-2">\n        <g id="volme_big-copy" transform="translate(488.000000, 340.000000)">\n          <rect id="Rectangle-18" x="11" y="4" width="2" height="12" rx="1"></rect>\n          <rect id="Rectangle-2" x="3" y="18" width="18" height="2" rx="1"></rect>\n          <rect id="Rectangle-2" transform="translate(4.000000, 17.500000) rotate(90.000000) translate(-4.000000, -17.500000) " x="1.5" y="16.5" width="5" height="2" rx="1"></rect><rect id="Rectangle-2-Copy-3" transform="translate(20.000000, 17.500000) rotate(90.000000) translate(-20.000000, -17.500000) " x="17.5" y="16.5" width="5" height="2" rx="1"></rect>\n          <path d="M9.48791171,8.26502656 L9.48791171,14.2650266 C9.48791171,14.8173113 9.04019646,15.2650266 8.48791171,15.2650266 C7.93562696,15.2650266 7.48791171,14.8173113 7.48791171,14.2650266 L7.48791171,7.26502656 C7.48791171,6.71274181 7.93562696,6.26502656 8.48791171,6.26502656 L15.4879117,6.26502656 C16.0401965,6.26502656 16.4879117,6.71274181 16.4879117,7.26502656 C16.4879117,7.81731131 16.0401965,8.26502656 15.4879117,8.26502656 L9.48791171,8.26502656 Z" id="Combined-Shape" transform="translate(11.987912, 10.765027) scale(1, -1) rotate(45.000000) translate(-11.987912, -10.765027) "></path>\n        </g>\n      </g>\n    </g>\n  </g>\n</svg>\n',"image/svg+xml").firstChild}var vs=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),y(v(e),"download",function(i){if(!e.isLock){e.emitUserAction(i,"download");var n=e.playerConfig.url,s="";f.typeOf(n)==="String"?s=n:f.typeOf(n)==="Array"&&n.length>0&&(s=n[0].src);var l=e.getAbsoluteURL(s);gs(l),e.isLock=!0,e.timer=window.setTimeout(function(){e.isLock=!1,window.clearTimeout(e.timer),e.timer=null},300)}}),e.timer=null,e.isLock=!1,e}return w(r,[{key:"afterCreate",value:function(){I(P(r.prototype),"afterCreate",this).call(this),!this.config.disable&&(this.appendChild(".xgplayer-icon",this.icons.download),this._handler=this.hook("click",this.download,{pre:function(i){i.preventDefault(),i.stopPropagation()}}),this.bind(["click","touchend"],this._handler))}},{key:"registerIcons",value:function(){return{download:ps}}},{key:"getAbsoluteURL",value:function(e){if(!e.match(/^https?:\/\//)){var i=document.createElement("div");i.innerHTML='<a href="'.concat(e,'">x</a>'),e=i.firstChild.href}return e}},{key:"destroy",value:function(){I(P(r.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.download),window.clearTimeout(this.timer),this.timer=null}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-download">\n   <div class="xgplayer-icon">\n   </div>\n   '.concat(_e(this,"DOWNLOAD_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"download"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_RIGHT,index:3,disable:!0}}}]),r}(xe),ms=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"beforeCreate",value:function(e){typeof e.player.config.screenShot=="boolean"&&(e.config.disable=!e.player.config.screenShot)}},{key:"afterCreate",value:function(){I(P(r.prototype),"afterCreate",this).call(this),this.appendChild(".xgplayer-icon",this.icons.screenshotIcon);var e=this.config;this.initSize=function(i){e.fitVideo&&(e.width=i.vWidth,e.height=i.vHeight)},this.once(me,this.initSize)}},{key:"onPluginsReady",value:function(){this.show(),this.onClickBtn=this.onClickBtn.bind(this),this.bind(["click","touchend"],this.onClickBtn)}},{key:"saveScreenShot",value:function(e,i){var n=document.createElement("a");n.href=e,n.download=i;var s;try{typeof MouseEvent<"u"?s=new MouseEvent("click",{bubbles:!0,cancelable:!0,view:window}):(s=document.createEvent("MouseEvents"),s.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null))}catch(l){console.error("MouseEvent unsupported",l)}s&&n.dispatchEvent(s)}},{key:"createCanvas",value:function(e,i){var n=document.createElement("canvas"),s=n.getContext("2d");this.canvasCtx=s,this.canvas=n,n.width=e||this.config.width,n.height=i||this.config.height,s.imageSmoothingEnabled=!0,s.imageSmoothingEnabled&&(s.imageSmoothingQuality="high")}},{key:"onClickBtn",value:function(e){var i=this;e.preventDefault(),e.stopPropagation(),this.emitUserAction(e,"shot");var n=this.config;this.shot(n.width,n.height).then(function(s){i.emit(Gi,s),n.saveImg&&i.saveScreenShot(s,n.name+n.format)})}},{key:"shot",value:function(e,i){var n=this,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{quality:.92,type:"image/png"},l=this.config,u=this.player,c=s.quality||l.quality,h=s.type||l.type;return new Promise(function(d,g){var p=null,m;if(u.media.canvas)p=u.media.canvas;else{n.canvas?(n.canvas.width=e||l.width,n.canvas.height=i||l.height):n.createCanvas(e,i),p=n.canvas,m=n.canvasCtx;var _=u.media.videoWidth/u.media.videoHeight,b=p.width/p.height,T=0,k=0,D=u.media.videoWidth,L=u.media.videoHeight,B,M,W,j;_>b?(W=p.width,j=p.width/_,B=0,M=Math.round((p.height-j)/2)):_===b?(W=p.width,j=p.height,B=0,M=0):_<b&&(W=p.height*_,j=p.height,B=Math.round((p.width-W)/2),M=0),m.drawImage(u.media,T,k,D,L,B,M,W,j)}var O=p.toDataURL(h,c).replace(h,"image/octet-stream");O=O.replace(/^data:image\/[^;]+/,"data:application/octet-stream"),d(O)})}},{key:"registerIcons",value:function(){return{screenshotIcon:null}}},{key:"destroy",value:function(){I(P(r.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.onClickBtn),this.off(me,this.initSize)}},{key:"render",value:function(){if(!this.config.disable){var e=this.icons.screenshotIcon?"xgplayer-icon":"xgplayer-icon btn-text",i="SCREENSHOT";return'\n      <xg-icon class="xgplayer-shot">\n      <div class="'.concat(e,'">\n      ').concat(this.icons.screenshotIcon?"":'<span lang-key="'.concat(this.i18nKeys[i],'">').concat(this.i18n[i],"</span>"),"\n      </div>\n    </xg-icon>")}}}],[{key:"pluginName",get:function(){return"screenShot"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_RIGHT,index:5,quality:.92,type:"image/png",format:".png",width:600,height:337,saveImg:!0,fitVideo:!0,disable:!1,name:"screenshot"}}}]),r}(xe),ys=function(){function o(a){E(this,o),this.config=a.config,this.parent=a.root,this.root=f.createDom("ul","",{},"xg-options-list xg-list-slide-scroll ".concat(this.config.className)),a.root.appendChild(this.root);var r=this.config.maxHeight;r&&this.setStyle({maxHeight:r}),this.onItemClick=this.onItemClick.bind(this),this.renderItemList();var t=this.config.domEventType==="touch"?"touchend":"click";this._delegates=N.delegate.call(this,this.root,"li",t,this.onItemClick)}return w(o,[{key:"renderItemList",value:function(r){var t=this,e=this.config,i=this.root;r?e.data=r:r=e.data,e.style&&Object.keys(e.style).map(function(n){i.style[n]=e[n]}),r.length>0&&(this.attrKeys=Object.keys(r[0])),this.root.innerHTML="",r.map(function(n,s){var l=n.selected?"option-item selected":"option-item";n["data-index"]=s,t.root.appendChild(f.createDom("li","<span>".concat(n.showText,"</span>"),n,l))})}},{key:"onItemClick",value:function(r){r.delegateTarget||(r.delegateTarget=r.target);var t=r.delegateTarget;if(t&&f.hasClass(t,"selected"))return!1;var e=typeof this.config.onItemClick=="function"?this.config.onItemClick:null,i=this.root.querySelector(".selected");f.addClass(t,"selected"),i&&f.removeClass(i,"selected"),e(r,{from:i?this.getAttrObj(i,this.attrKeys):null,to:this.getAttrObj(t,this.attrKeys)})}},{key:"getAttrObj",value:function(r,t){if(!r||!t)return{};var e={};t.map(function(n){e[n]=r.getAttribute(n)});var i=r.getAttribute("data-index");return i&&(e.index=Number(i)),e}},{key:"show",value:function(){f.removeClass(this.root,"hide"),f.addClass(this.root,"active")}},{key:"hide",value:function(){f.removeClass(this.root,"active"),f.addClass(this.root,"hide")}},{key:"setStyle",value:function(r){var t=this;Object.keys(r).forEach(function(e){t.root.style[e]=r[e]})}},{key:"destroy",value:function(){this._delegates&&(this._delegates.map(function(r){r.destroy&&r.destroy()}),this._delegates=null),this.root.innerHTML=null,this.parent.removeChild(this.root),this.root=null}}]),o}(),se={SIDE:"side",MIDDLE:"middle",DEFAULT:"default"},Le={CLICK:"click",HOVER:"hover"};function _s(o,a){return o===se.SIDE?a===H.CONTROLS_LEFT?"xg-side-list xg-left-side":"xg-side-list xg-right-side":""}var Te=A.device==="mobile",nt=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),y(v(e),"onEnter",function(i){i.stopPropagation(),e.emit("icon_mouseenter",{pluginName:e.pluginName}),e.switchActiveState(i)}),y(v(e),"switchActiveState",function(i){i.stopPropagation();var n=e.config.toggleMode;n===Le.CLICK?e.toggle(!e.isActive):e.toggle(!0)}),y(v(e),"onLeave",function(i){i.stopPropagation(),e.emit("icon_mouseleave",{pluginName:e.pluginName}),e.config.listType!==se.SIDE&&e.isActive&&e.toggle(!1)}),y(v(e),"onListEnter",function(i){e.enterType=2}),y(v(e),"onListLeave",function(i){e.enterType=0,e.isActive&&e.toggle(!1)}),e.isIcons=!1,e.isActive=!1,e.curValue=null,e.curIndex=0,e}return w(r,[{key:"updateLang",value:function(e){this.renderItemList(this.config.list,this.curIndex)}},{key:"afterCreate",value:function(){var e=this,i=this.config;this.initIcons(),Te=Te||this.domEventType==="touch",Te&&A.device==="mobile"&&i.listType===se.DEFAULT&&(i.listType=se.SIDE),i.hidePortrait&&f.addClass(this.root,"portrait"),this.on([me,le],function(){e._resizeList()}),this.once(ue,function(){i.list&&i.list.length>0&&(e.renderItemList(i.list),e.show())}),Te&&this.on(Rt,function(){e.isActive&&(e.optionsList&&e.optionsList.hide(),e.isActive=!1)}),Te?(i.toggleMode=Le.CLICK,this.activeEvent="touchend"):this.activeEvent=i.toggleMode===Le.CLICK?"click":"mouseenter",i.toggleMode===Le.CLICK?this.bind(this.activeEvent,this.switchActiveState):(this.bind(this.activeEvent,this.onEnter),this.bind("mouseleave",this.onLeave)),this.isIcons&&this.bind("click",this.onIconClick)}},{key:"initIcons",value:function(){var e=this,i=this.icons,n=Object.keys(i),s=!1;n.length>0&&(n.forEach(function(l){e.appendChild(".xgplayer-icon",i[l]),!s&&(s=i[l])}),this.isIcons=s),!s&&(this.appendChild(".xgplayer-icon",f.createDom("span","",{},"icon-text")),f.addClass(this.find(".xgplayer-icon"),"btn-text"))}},{key:"show",value:function(e){!this.config.list||this.config.list.length<2||f.addClass(this.root,"show")}},{key:"hide",value:function(){f.removeClass(this.root,"show")}},{key:"getTextByLang",value:function(e,i,n){if(e===void 0)return"";var s=this.config.list;!n&&(n=this.player.lang),i=!i||f.isUndefined(e[i])?"text":i,typeof e=="number"&&(e=s[e]);try{return $(e[i])==="object"?e[i][n]||e[i].en:e[i]}catch(l){return console.warn(l),""}}},{key:"toggle",value:function(e){if(!(e===this.isActive||this.config.disable)){var i=this.player.controls,n=this.config.listType;e?(n===se.SIDE?i.blur():i.focus(),this.optionsList&&this.optionsList.show()):(n===se.SIDE?i.focus():i.focusAwhile(),this.optionsList&&this.optionsList.hide()),this.isActive=e}}},{key:"onItemClick",value:function(e,i){e.stopPropagation();var n=this.config,s=n.listType,l=n.list;this.curIndex=i.to.index,this.curItem=l[this.curIndex],this.changeCurrentText();var u=this.config.isItemClickHide;(u||Te||s===se.SIDE)&&this.toggle(!1)}},{key:"onIconClick",value:function(e){}},{key:"changeCurrentText",value:function(){if(!this.isIcons){var e=this.config.list,i=this.curIndex<e.length?this.curIndex:0,n=e[i];n&&(this.find(".icon-text").innerHTML=this.getTextByLang(n,"iconText"))}}},{key:"renderItemList",value:function(e,i){var n=this,s=this.config,l=this.optionsList,u=this.player;if(typeof i=="number"&&(this.curIndex=i,this.curItem=s.list[i]),l){l.renderItemList(e),this.changeCurrentText();return}var c={config:{data:e||[],className:_s(s.listType,s.position),onItemClick:function(m,_){n.onItemClick(m,_)},domEventType:Te?"touch":"mouse"},root:s.listType===se.SIDE?u.innerContainer||u.root:this.root};if(this.config.isShowIcon){var h=this.player.root.getBoundingClientRect(),d=h.height,g=s.listType===se.MIDDLE?d-50:d;g&&s.heightLimit&&(c.config.maxHeight="".concat(g,"px")),this.optionsList=new ys(c),this.changeCurrentText(),this.show()}this._resizeList()}},{key:"_resizeList",value:function(){if(this.config.heightLimit){var e=this.player.root.getBoundingClientRect(),i=e.height,n=this.config.listType===se.MIDDLE?i-50:i;this.optionsList&&this.optionsList.setStyle({maxHeight:"".concat(n,"px")})}}},{key:"destroy",value:function(){var e=this.config;e.toggleMode===Le.CLICK?this.unbind(this.activeEvent,this.switchActiveState):(this.unbind(this.activeEvent,this.onEnter),this.unbind("mouseleave",this.onLeave)),this.isIcons&&this.unbind("click",this.onIconClick),this.optionsList&&(this.optionsList.destroy(),this.optionsList=null)}},{key:"render",value:function(){if(this.config.isShowIcon)return'<xg-icon class="xg-options-icon '.concat(this.config.className||"",'">\n    <div class="xgplayer-icon">\n    </div>\n   </xg-icon>')}}],[{key:"pluginName",get:function(){return"optionsIcon"}},{key:"defaultConfig",get:function(){return{position:H.CONTROLS_RIGHT,index:100,list:[],listType:"default",listStyle:{},hidePortrait:!0,isShowIcon:!1,isItemClickHide:!0,toggleMode:Le.HOVER,heightLimit:!0}}}]),r}(N),Cs=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),e.curTime=0,e.isPaused=!0,e}return w(r,[{key:"beforeCreate",value:function(e){var i=e.config.list;Array.isArray(i)&&i.length>0&&(e.config.list=i.map(function(n){return!n.text&&n.name&&(n.text=n.name),n.text||(n.text=n.definition),n}))}},{key:"afterCreate",value:function(){var e=this;I(P(r.prototype),"afterCreate",this).call(this),this.on("resourceReady",function(i){e.changeDefinitionList(i)}),this.on(Bt,function(i){e.renderItemList(e.config.list,i.to)}),this.player.definitionList.length<2&&this.hide()}},{key:"show",value:function(e){!this.config.list||this.config.list.length<2||f.addClass(this.root,"show")}},{key:"initDefinition",value:function(){var e=this.config,i=e.list,n=e.defaultDefinition;if(i.length>0){var s=null;i.map(function(l){l.definition===n&&(s=l)}),s||(s=i[0]),this.changeDefinition(s)}}},{key:"renderItemList",value:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.config.list||[],n=arguments.length>1?arguments[1]:void 0,s=n&&n.definition?n.definition:this.config.defaultDefinition;n&&i.forEach(function(c){c.selected=!1});var l=0,u=i.map(function(c,h){var d=V(V({},c),{},{showText:e.getTextByLang(c)||c.definition,selected:!1});return(c.selected||c.definition&&c.definition==s)&&(d.selected=!0,l=h),d});I(P(r.prototype),"renderItemList",this).call(this,u,l)}},{key:"changeDefinitionList",value:function(e){Array.isArray(e)&&(this.config.list=e.map(function(i){return!i.text&&i.name&&(i.text=i.name),i.text||(i.text=i.definition),i}),this.renderItemList(),this.config.list.length<2?this.hide():this.show())}},{key:"changeDefinition",value:function(e,i){this.player.changeDefinition(e,i)}},{key:"onItemClick",value:function(e,i){var n=this.player.definitionList;I(P(r.prototype),"onItemClick",this).apply(this,arguments),this.emitUserAction(e,"change_definition",{from:i.from,to:i.to});for(var s=0;s<n.length;s++)i.to&&n[s].definition===i.to.definition&&(i.to.url=n[s].url),i.from&&n[s].definition===i.from.definition&&(i.from.url=n[s].url);this.player.changeDefinition(i.to,i.from)}}],[{key:"pluginName",get:function(){return"definition"}},{key:"defaultConfig",get:function(){return V(V({},nt.defaultConfig),{},{position:H.CONTROLS_RIGHT,index:3,list:[],defaultDefinition:"",disable:!1,hidePortrait:!1,className:"xgplayer-definition",isShowIcon:!0})}}]),r}(nt),ks=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),e.curRate=1,e}return w(r,[{key:"beforeCreate",value:function(e){var i=e.player.config.playbackRate,n=i?Array.isArray(i)?i:e.config.list:[];Array.isArray(n)&&(e.config.list=n.map(function(s){return typeof s=="number"?s={rate:s,text:"".concat(s,"x")}:!s.text&&s.rate&&(s.text="".concat(s.rate,"x")),s}))}},{key:"afterCreate",value:function(){var e=this;I(P(r.prototype),"afterCreate",this).call(this),this.on(Ni,function(){e.curValue!==e.player.playbackRate&&e.renderItemList()}),this.renderItemList()}},{key:"show",value:function(e){!this.config.list||this.config.list.length===0||I(P(r.prototype),"show",this).call(this)}},{key:"onItemClick",value:function(e,i){I(P(r.prototype),"onItemClick",this).call(this,e,i);var n=e.delegateTarget,s=Number(n.getAttribute("rate"));if(!s||s===this.curValue)return!1;var l={playbackRate:{from:this.player.playbackRate,to:s}};this.emitUserAction(e,"change_rate",{props:l}),this.curValue=s,this.player.playbackRate=s}},{key:"renderItemList",value:function(){var e=this,i=this.player.playbackRate||1;this.curValue=i;var n=-1,s=this.config.list.map(function(l,u){var c={rate:l.rate};return c.rate===i&&(c.selected=!0,n=u),c.showText=e.getTextByLang(l),c});I(P(r.prototype),"renderItemList",this).call(this,s,n)}},{key:"changeCurrentText",value:function(){if(!this.isIcons){var e=this.config.list,i=this.curIndex<e.length?this.curIndex:0,n=e[i],s="";!n||this.curIndex<0?s="".concat(this.player.playbackRate,"x"):s=this.getTextByLang(n,"iconText"),this.find(".icon-text").innerHTML=s}}},{key:"destroy",value:function(){I(P(r.prototype),"destroy",this).call(this)}}],[{key:"pluginName",get:function(){return"playbackRate"}},{key:"defaultConfig",get:function(){return V(V({},nt.defaultConfig),{},{position:H.CONTROLS_RIGHT,index:4,list:[2,1.5,1,.75,.5],className:"xgplayer-playbackrate",isShowIcon:!0,hidePortrait:!1})}}]),r}(nt),Ts=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"afterCreate",value:function(){var e=this;this.clickHandler=this.hook("errorRetry",this.errorRetry,{pre:function(n){n.preventDefault(),n.stopPropagation()}}),this.onError=this.hook("showError",this.handleError),this.bind(".xgplayer-error-refresh","click",this.clickHandler),this.on(Ve,function(i){e.onError(i)})}},{key:"errorRetry",value:function(e){this.emitUserAction(e,"error_retry",{}),this.player.retry()}},{key:"handleError",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=this.player,n=e.errorType,s=i.errorNote?this.i18n[i.errorNote]:"";if(!s)switch(n){case"decoder":s=this.i18n.MEDIA_ERR_DECODE;break;case"network":s=this.i18n.MEDIA_ERR_NETWORK;break;default:s=this.i18n.MEDIA_ERR_SRC_NOT_SUPPORTED}this.find(".xgplayer-error-text").innerHTML=s,this.find(".xgplayer-error-tips").innerHTML="".concat(this.i18n.REFRESH_TIPS,'<span class="xgplayer-error-refresh">').concat(this.i18n.REFRESH,"</span>")}},{key:"destroy",value:function(){this.unbind(".xgplayer-error-refresh","click",this.clickHandler)}},{key:"render",value:function(){return'<xg-error class="xgplayer-error">\n      <div class="xgplayer-errornote">\n       <span class="xgplayer-error-text"></span>\n       <span class="xgplayer-error-tips"><em class="xgplayer-error-refresh"></em></span>\n      </div>\n    </xg-error>'}}],[{key:"pluginName",get:function(){return"error"}}]),r}(N),bs=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"afterCreate",value:function(){var e=this;this.intervalId=0,this.customConfig=null,this.bind(".highlight",["click","touchend"],function(i){(e.config.onClick||e.customOnClick)&&(i.preventDefault(),i.stopPropagation(),e.customOnClick?e.customOnClick(i):e.config.onClick(i))}),this.player.showPrompt=function(){e.showPrompt.apply(e,arguments)},this.player.hidePrompt=function(){e.hide()}}},{key:"setStyle",value:function(e){var i=this;Object.keys(e).map(function(n){i.root.style[n]=e[n]})}},{key:"showPrompt",value:function(e){var i=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(){};if(e){this.customOnClick=s;var l=this.config.interval;this.intervalId&&(clearTimeout(this.intervalId),this.intervalId=null),f.addClass(this.root,"show"),n.mode==="arrow"&&f.addClass(this.root,"arrow"),typeof e=="string"?this.find(".xgplayer-prompt-detail").innerHTML=e:this.find(".xgplayer-prompt-detail").innerHTML="".concat(e.text||"")+"".concat(e.highlight?'<i class="highlight">'.concat(e.highlight,"</i>"):""),n.style&&this.setStyle(n.style);var u=typeof n.autoHide=="boolean"?n.autoHide:this.config.autoHide;if(u){var c=n.interval||l;this.intervalId=setTimeout(function(){i.hide()},c)}}}},{key:"hide",value:function(){f.removeClass(this.root,"show"),f.removeClass(this.root,"arrow"),this.root.removeAttribute("style"),this.customOnClick=null}},{key:"render",value:function(){return'<xg-prompt class="xgplayer-prompt '.concat(C.CONTROLS_FOLLOW,'">\n    <span class="xgplayer-prompt-detail"></span>\n    </xg-prompt>')}}],[{key:"pluginName",get:function(){return"prompt"}},{key:"defaultConfig",get:function(){return{interval:3e3,style:{},mode:"arrow",autoHide:!0,detail:{text:"",highlight:""},onClick:function(){}}}}]),r}(N),yi={time:0,text:"",id:1,duration:1,color:"#fff",style:{},width:6,height:6};function hn(o){Object.keys(yi).map(function(a){o[a]===void 0&&(o[a]=yi[a])})}var _i={_updateDotDom:function(a,r){if(r){var t=this.calcuPosition(a.time,a.duration),e=a.style||{};e.left="".concat(t.left,"%"),e.width="".concat(t.width,"%"),r.setAttribute("data-text",a.text),r.setAttribute("data-time",a.time),t.isMini?f.addClass(r,"mini"):f.removeClass(r,"mini"),Object.keys(e).map(function(i){r.style[i]=e[i]})}},initDots:function(){var a=this;this._ispots.map(function(r){a.createDot(r,!1)}),this.ispotsInit=!0},createDot:function(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,t=this.player.plugins.progress;if(t&&(r&&(hn(a),this._ispots.push(a)),!(!this.ispotsInit&&r))){var e=this.calcuPosition(a.time,a.duration),i=a.style||{};i.left="".concat(e.left,"%"),i.width="".concat(e.width,"%");var n="xgspot_".concat(a.id," xgplayer-spot");e.isMini&&(n+=" mini");var s=a.template?'<div class="xgplayer-spot-pop">'.concat(a.template,"</div>"):"",l=f.createDom("xg-spot",s,{"data-text":a.text,"data-time":a.time,"data-id":a.id},n);Object.keys(i).map(function(u){l.style[u]=i[u]}),t.outer&&t.outer.appendChild(l),this.positionDot(l,a.id)}},findDot:function(a){if(this.player.plugins.progress){var r=this._ispots.filter(function(t,e){return t.id===a});return r.length>0?r[0]:null}},updateDot:function(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,t=this.player.plugins.progress;if(t){var e=this.findDot(a.id);if(e&&Object.keys(a).map(function(n){e[n]=a[n]}),!!this.ispotsInit){var i=t.find('xg-spot[data-id="'.concat(a.id,'"]'));i&&(this._updateDotDom(a,i),r&&this.showDot(a.id))}}},deleteDot:function(a){var r=this._ispots,t=this.player.plugins.progress;if(t){for(var e=[],i=0;i<r.length;i++)r[i].id===a&&e.push(i);for(var n=e.length,s=n-1;s>=0;s--)if(r.splice(e[s],1),this.ispotsInit){var l=t.find('xg-spot[data-id="'.concat(a,'"]'));l&&l.parentElement.removeChild(l)}}},deleteAllDots:function(){var a=this.player.plugins.progress;if(a){if(!this.ispotsInit){this._ispots=[];return}for(var r=a.root.getElementsByTagName("xg-spot"),t=r.length-1;t>=0;t--)a.outer.removeChild(r[t]);this._ispots=[]}},updateAllDots:function(){var a=this,r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=this.player.plugins.progress;if(t){if(!this.ispotsInit){this._ispots=r;return}this._ispots=[];var e=t.root.getElementsByTagName("xg-spot"),i=e.length;if(i>r.length)for(var n=i-1;n>r.length-1;n--)t.outer.removeChild(e[n]);r.forEach(function(s,l){l<i?(e[l].setAttribute("data-id","".concat(s.id)),a._ispots.push(s),a.updateDot(s)):a.createDot(s)})}},positionDots:function(){var a=this,r=this._ispots,t=this.playerSize,e=this.player.sizeInfo,i=this.player.plugins.progress;!i||e.width===t.width||(t.width=e.width,t.left=e.left,r.forEach(function(n){var s=i.find('xg-spot[data-id="'.concat(n.id,'"]'));s&&a.positionDot(s,n.id)}))},positionDot:function(a,r){var t=f.findDom(a,".xgplayer-spot-pop");if(t){var e=this.playerSize,i=a.getBoundingClientRect(),n=t.getBoundingClientRect(),s=i.left-e.left,l=e.width-s-i.width/2;if(s<n.width/2||e.width<n.width){var u=n.width/2-s;t.style.left="".concat(u,"px")}else if(l<n.width/2){var c=l-n.width/2+i.width/2;t.style.left="".concat(c,"px")}else t.style.left="50%"}},updateDuration:function(){var a=this,r=this.player.plugins.progress;if(r){var t=this._ispots;t.forEach(function(e){var i=r.find('xg-spot[data-id="'.concat(e.id,'"]'));a._updateDotDom(e,i)})}},getAllDotsDom:function(){var a=this.player.plugins.progress;if(!a)return[];var r=a.root.getElementsByTagName("xg-spot");return r},getDotDom:function(a){var r=this.player.plugins.progress;if(r)return r.find('xg-spot[data-id="'.concat(a,'"]'))}};function Es(o){var a=o.config,r=o.player;Object.keys(_i).map(function(e){o[e]=_i[e].bind(o)});var t=r.config.progressDot||a.ispots||[];o._ispots=t.map(function(e){return hn(e),e}),o.ispotsInit=!1,o.playerSize={left:r.sizeInfo.left,width:r.sizeInfo.width},o.on(he,function(){o.ispotsInit?o.updateDuration():o.initDots()}),o.on(me,function(){o.positionDots()})}var Ae={dragmove:"onProgressMove",dragstart:"onProgressDragStart",dragend:"onProgressDragEnd",click:"onProgressClick",mouseover:"onProgressMouseOver",mouseenter:"onProgressMouseOver"},ws=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),y(v(e),"onMousemove",function(i){if(!e.config.disable){if(f.hasClass(i.target,"xg-spot-content")&&e.config.isHideThumbnailHover){e.player.plugins.progress.onMouseLeave(i);return}(e._state.f||f.hasClass(i.target,"xg-spot-content"))&&(f.event(i),i.stopPropagation())}}),y(v(e),"onMousedown",function(i){e.config.disable||(e._state.f||f.hasClass(i.target,"xg-spot-content"))&&(f.event(i),i.stopPropagation())}),y(v(e),"onMouseup",function(i){if(e.isDrag){var n=e.player.plugins.progress;n&&n.pos&&(n.onMouseUp(i),!n.pos.isEnter&&n.onMouseLeave(i))}}),y(v(e),"onDotMouseLeave",function(i){if(!e.config.disable){e._curDot.removeEventListener("mouseleave",e.onDotMouseLeave),e.blurDot(i.target),e._curDot=null;var n=e.player.plugins.progress;n&&n.enableBlur(),e.show()}}),y(v(e),"onProgressMouseOver",function(i,n){if(!e.config.disable&&f.hasClass(n.target,"xgplayer-spot")&&!e._curDot){e._curDot=n.target,e.focusDot(n.target),e._curDot.children.length>0&&e.hide();var s=e.player.plugins.progress;s&&s.disableBlur(),e._curDot.addEventListener("mouseleave",e.onDotMouseLeave)}}),e._ispots=[],e.videoPreview=null,e.videothumbnail=null,e.thumbnail=null,e.timeStr="",e._state={now:0,f:!1},e}return w(r,[{key:"beforeCreate",value:function(e){var i=e.player.plugins.progress;i&&(e.root=i.root)}},{key:"afterCreate",value:function(){var e=this;this._curDot=null,this.handlerSpotClick=this.hook("spotClick",function(i,n){n.seekTime&&e.player.seek(n.seekTime)}),this.transformTimeHook=this.hook("transformTime",function(i){e.setTimeContent(f.format(i))}),Es(this),this.on(he,function(){e.show()}),this.config.disable&&this.disable(),this.extTextRoot=this.find(".xg-spot-ext-text")}},{key:"setConfig",value:function(e){var i=this;e&&Object.keys(e).map(function(n){i.config[n]=e[n]})}},{key:"onPluginsReady",value:function(){var e=this.player;e.plugins.progress&&(this.previewLine=this.find(".xg-spot-line"),this.timePoint=this.find(".xgplayer-progress-point"),this.timeText=this.find(".xg-spot-time"),this.tipText=this.find(".spot-inner-text"),this._hasThumnail=!1,this.registerThumbnail(),this.bindEvents())}},{key:"bindEvents",value:function(){var e=this,i=this.player.plugins.progress;if(i&&(Object.keys(Ae).map(function(s){e[Ae[s]]=e[Ae[s]].bind(e),i.addCallBack(s,e[Ae[s]])}),A.device!=="mobile")){this.bind(".xg-spot-info","mousemove",this.onMousemove),this.bind(".xg-spot-info","mousedown",this.onMousedown),this.bind(".xg-spot-info","mouseup",this.onMouseup);var n=this.hook("previewClick",function(){});this.handlerPreviewClick=function(s){s.stopPropagation(),n(parseInt(e._state.now*1e3,10)/1e3,s),i&&i.onMouseUp(s)},this.bind(".xg-spot-content","mouseup",this.handlerPreviewClick)}}},{key:"onProgressMove",value:function(e,i){this.config.disable||!this.player.duration||this.updatePosition(e.offset,e.width,e.currentTime,e.e)}},{key:"onProgressDragStart",value:function(e){this.config.disable||!this.player.duration||(this.isDrag=!0,this.videoPreview&&f.addClass(this.videoPreview,"show"))}},{key:"onProgressDragEnd",value:function(e){this.config.disable||!this.player.duration||(this.isDrag=!1,this.videoPreview&&f.removeClass(this.videoPreview,"show"))}},{key:"onProgressClick",value:function(e,i){this.config.disable||f.hasClass(i.target,"xgplayer-spot")&&(i.stopPropagation(),i.preventDefault(),["time","id","text"].map(function(n){e[n]=i.target.getAttribute("data-".concat(n))}),e.time&&(e.time=Number(e.time)),this.handlerSpotClick(i,e))}},{key:"updateLinePos",value:function(e,i){var n=this.root,s=this.previewLine,l=this.player,u=this.config,c=l.controls.mode,h=c==="flex",d=n.getBoundingClientRect().width;if(!(!d&&this._hasThumnail)){d=this._hasThumnail&&d<u.width?u.width:d;var g=e-d/2,p;g<0&&!h?(g=0,p=e-d/2):g>i-d&&!h?(p=g-(i-d),g=i-d):p=0,p!==void 0&&(s.style.transform="translateX(".concat(p.toFixed(2),"px)")),n.style.transform="translateX(".concat(g.toFixed(2),"px) translateZ(0)")}}},{key:"updateTimeText",value:function(e){var i=this.timeText,n=this.timePoint;i.innerHTML=e,!this.thumbnail&&(n.innerHTML=e)}},{key:"updatePosition",value:function(e,i,n,s){var l=this.root,u=this.config,c=this._state;if(l){c.now=n,this.transformTimeHook(n);var h=this.timeStr;s&&s.target&&f.hasClass(s.target,"xgplayer-spot")?(this.showTips(s.target.getAttribute("data-text"),!1,h),this.focusDot(s.target),c.f=!0,u.isFocusDots&&c.f&&(c.now=parseInt(s.target.getAttribute("data-time"),10))):u.defaultText?(c.f=!1,this.showTips(u.defaultText,!0,h)):(c.f=!1,this.hideTips("")),this.updateTimeText(h),this.updateThumbnails(c.now),this.updateLinePos(e,i)}}},{key:"setTimeContent",value:function(e){this.timeStr=e}},{key:"updateThumbnails",value:function(e){var i=this.player,n=this.videoPreview,s=this.config,l=i.plugins.thumbnail;if(l&&l.usable){this.thumbnail&&l.update(this.thumbnail,e,s.width,s.height);var u=n&&n.getBoundingClientRect();this.videothumbnail&&l.update(this.videothumbnail,e,u.width,u.height)}}},{key:"registerThumbnail",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(A.device!=="mobile"){var i=this.player,n=this.config,s=i.getPlugin("thumbnail");if(s&&s.setConfig(e),!s||!s.usable||!n.isShowThumbnail){f.addClass(this.root,"short-line no-thumbnail");return}else f.removeClass(this.root,"short-line no-thumbnail");n.mode==="short"&&f.addClass(this.root,"short-line"),this._hasThumnail=!0;var l=this.find(".xg-spot-thumbnail");this.thumbnail=s.createThumbnail(l,"progress-thumbnail"),n.isShowCoverPreview&&(this.videoPreview=f.createDom("xg-video-preview","",{},"xgvideo-preview"),i.root.appendChild(this.videoPreview),this.videothumbnail=s.createThumbnail(this.videoPreview,"xgvideo-thumbnail")),this.updateThumbnails(0)}}},{key:"calcuPosition",value:function(e,i){var n=this.player.plugins.progress,s=this.player,l=n.root.getBoundingClientRect().width,u=s.duration/l*6;return e+i>s.duration&&(i=s.duration-e),e/s.duration*100,i/s.duration,{left:e/s.duration*100,width:i/s.duration*100,isMini:i<u}}},{key:"showDot",value:function(e){var i=this.findDot(e);if(i){var n=this.root.getBoundingClientRect(),s=n.width,l=i.time/this.player.duration*s;this.updatePosition(l,s,i.time)}}},{key:"focusDot",value:function(e,i){e&&(i||(i=e.getAttribute("data-id")),f.addClass(e,"active"),this._activeDotId=i)}},{key:"blurDot",value:function(e){if(!e){var i=this._activeDotId;e=this.getDotDom(i)}e&&(f.removeClass(e,"active"),this._activeDotId=null)}},{key:"showTips",value:function(e,i){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"";f.addClass(this.root,"no-timepoint"),e&&(f.addClass(this.find(".xg-spot-content"),"show-text"),i&&this.config.mode==="production"?(f.addClass(this.root,"product"),this.tipText.textContent=e):(f.removeClass(this.root,"product"),this.tipText.textContent=this._hasThumnail?e:"".concat(n," ").concat(e)))}},{key:"hideTips",value:function(){f.removeClass(this.root,"no-timepoint"),this.tipText.textContent="",f.removeClass(this.find(".xg-spot-content"),"show-text"),f.removeClass(this.root,"product")}},{key:"hide",value:function(){f.addClass(this.root,"hide")}},{key:"show",value:function(e){f.removeClass(this.root,"hide")}},{key:"enable",value:function(){var e=this.config,i=this.playerConfig;this.config.disable=!1,this.show(),!this.thumbnail&&e.isShowThumbnail&&this.registerThumbnail(i.thumbnail||{})}},{key:"disable",value:function(){this.config.disable=!0,this.hide()}},{key:"destroy",value:function(){var e=this,i=this.player.plugins.progress;i&&Object.keys(Ae).map(function(n){i.removeCallBack(n,e[Ae[n]])}),this.videothumbnail=null,this.thumbnail=null,this.videoPreview&&this.player.root.removeChild(this.videoPreview),this.unbind(".xg-spot-info","mousemove",this.onMousemove),this.unbind(".xg-spot-info","mousedown",this.onMousedown),this.unbind(".xg-spot-info","mouseup",this.onMouseup),this.unbind(".xg-spot-content","mouseup",this.handlerPreviewClick)}},{key:"render",value:function(){return A.device==="mobile"||this.playerConfig.isMobileSimulateMode==="mobile"?"":'<div class="xg-spot-info hide '.concat(this.config.mode==="short"?"short-line":"",'">\n      <div class="xg-spot-content">\n        <div class="xg-spot-thumbnail">\n          <span class="xg-spot-time"></span>\n        </div>\n        <div class="xg-spot-text"><span class="spot-inner-text"></span></div>\n      </div>\n      <div class="xgplayer-progress-point">00:00</div>\n      <div class="xg-spot-ext-text"></div>\n      <div class="xg-spot-line"></div>\n    </div>')}}],[{key:"pluginName",get:function(){return"progresspreview"}},{key:"defaultConfig",get:function(){return{index:1,miniWidth:6,ispots:[],defaultText:"",isFocusDots:!0,isHideThumbnailHover:!0,isShowThumbnail:!0,isShowCoverPreview:!1,mode:"",disable:!1,width:160,height:90}}}]),r}(N),Ss=function(o){S(r,o);var a=x(r);function r(t){var e;return E(this,r),e=a.call(this,t),e.ratio=1,e.interval=null,e._preloadMark={},e}return w(r,[{key:"afterCreate",value:function(){var e=this;this.usable&&this.initThumbnail(),this.on([he],function(){var i=e.config,n=i.pic_num,s=i.interval;e.usable&&(e.interval=s>0?s:Math.round(e.player.duration*1e3/n)/1e3)})}},{key:"setConfig",value:function(e){var i=this;if(e){var n=Object.keys(e);n.length<1||(n.forEach(function(s){i.config[s]=e[s]}),this.usable&&this.initThumbnail())}}},{key:"usable",get:function(){var e=this.config,i=e.urls,n=e.pic_num;return i&&i.length>0&&n>0}},{key:"initThumbnail",value:function(){var e=this.config,i=e.width,n=e.height,s=e.pic_num,l=e.interval;this.ratio=i/n*100,this.interval=l||Math.round(this.player.duration/s),this._preloadMark={}}},{key:"getUrlByIndex",value:function(e){return e>=0&&e<this.config.urls.length?this.config.urls[e]:""}},{key:"preload",value:function(e){var i=this;if(!this._preloadMark[e]){var n=this.config.urls,s=n.length,l=[];e>0&&l.push(e-1),l.push(e),e>0&&e<s-1&&l.push(e+1),l.map(function(u){!i._preloadMark[u]&&u>=0&&u<s&&(i._preloadMark[u]=1,f.preloadImg(n[u],function(){i._preloadMark[u]=2}))})}}},{key:"getPosition",value:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,s=this.config,l=s.pic_num,u=s.row,c=s.col,h=s.width,d=s.height;this.interval=Math.round(this.player.duration/l);var g=Math.ceil(e/this.interval);g=g>l?l:g;var p=g<u*c?0:Math.ceil(g/(u*c))-1,m=g-p*(c*u),_=m>0?Math.ceil(m/c)-1:0,b=m>0?m-_*c-1:0,T=0,k=0;if(i&&n){var D=i/n;D<h/d?(k=n,T=k*(h/d)):(T=i,k=T/(h/d))}else n?i||(k=n||d,T=k*(h/d)):(T=i||h,k=T/(h/d));var L=this.getUrlByIndex(p);return{urlIndex:p,rowIndex:_,colIndex:b,url:L,height:k,width:T,style:{backgroundImage:"url(".concat(L,")"),backgroundSize:"".concat(T*c,"px auto"),backgroundPosition:"-".concat(b*T,"px -").concat(_*k,"px"),width:"".concat(T,"px"),height:"".concat(k,"px")}}}},{key:"update",value:function(e,i){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,l=arguments.length>4&&arguments[4]!==void 0?arguments[4]:"",u=this.config,c=u.pic_num,h=u.urls;if(!(c<=0||!h||h.length===0)){var d=this.getPosition(i,n,s);this.preload(d.urlIndex),Object.keys(d.style).map(function(g){e.style[g]=d.style[g]}),Object.keys(l).map(function(g){e.style[g]=l[g]})}}},{key:"changeConfig",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.setConfig(e)}},{key:"createThumbnail",value:function(e,i){var n=f.createDom("xg-thumbnail","",{},"thumbnail ".concat(i));return e&&e.appendChild(n),n}}],[{key:"pluginName",get:function(){return"thumbnail"}},{key:"defaultConfig",get:function(){return{isShow:!1,urls:[],pic_num:0,col:0,row:0,height:90,width:160,scale:1,className:"",hidePortrait:!1}}}]),r}(N);function Ct(o){return o?"background:".concat(o,";"):""}var xs=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"onTimeupdate",function(){var s=t.player.ended,l=v(t),u=l.offsetDuration,c=t.currentTime;c=f.adjustTimeByDuration(c,u,s),t.update({played:c},u)}),t}return w(r,[{key:"offsetDuration",get:function(){return this.playerConfig.customDuration||this.player.offsetDuration||this.player.duration}},{key:"currentTime",get:function(){var e=this.player,i=e.offsetCurrentTime,n=e.currentTime;return i>=0?i:n}},{key:"afterCreate",value:function(){var e=this;this.root&&(this.on(J,this.onTimeupdate),this.on(Se,function(){e.reset()}))}},{key:"reset",value:function(){this.update({played:0,cached:0},0)}},{key:"update",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{cached:0,played:0},i=arguments.length>1?arguments[1]:void 0;!i||!this.root||(e.cached&&(this.find("xg-mini-progress-cache").style.width="".concat(e.cached/i*100,"%")),e.played&&(this.find("xg-mini-progress-played").style.width="".concat(e.played/i*100,"%")))}},{key:"render",value:function(){var e=this.playerConfig,i=e.commonStyle,n=e.miniprogress;if(n){var s=this.config,l=s.mode,u=s.height,c={cached:Ct(i.cachedColor),played:Ct(i.playedColor),progress:Ct(i.progressColor),height:u>0&&u!==2?"height: ".concat(u,"px;"):""},h=l==="show"?"xg-mini-progress-show":"";return'<xg-mini-progress class="xg-mini-progress '.concat(h,'" style="').concat(c.progress," ").concat(c.height,'">\n    <xg-mini-progress-cache class="xg-mini-progress-cache" style="').concat(c.cached,'"></xg-mini-progress-cache>\n    <xg-mini-progress-played class="xg-mini-progress-played" style="').concat(c.played,'"></xg-mini-progress-played>\n    </xg-mini-progress>')}}}],[{key:"pluginName",get:function(){return"MiniProgress"}},{key:"defaultConfig",get:function(){return{mode:"auto",height:2}}}]),r}(N),De={REAL_TIME:"realtime",FIRST_FRAME:"firstframe",FRAME_RATE:"framerate",POSTER:"poster"};function Ps(){try{return parseInt(window.performance.now(),10)}catch(o){return new Date().getTime()}}function Is(){try{var o=document.createElement("canvas").getContext;return!!o}catch(a){return!1}}var Ge=null,Ls=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"onLoadedData",function(s){t.player&&(t._frameCount=t.config.startFrameCount,t.stop(),t.renderOnTimeupdate(s),t.off(J,t.renderOnTimeupdate),t.on(J,t.renderOnTimeupdate))}),y(v(t),"onVisibilitychange",function(s){document.visibilityState==="visible"?t._checkIfCanStart()&&t.start():document.visibilityState==="hidden"&&t.stop()}),y(v(t),"renderOnTimeupdate",function(s){if(t._frameCount>0)t.renderOnce(),t._frameCount--;else{t._isLoaded=!0,t.off(J,t.renderOnTimeupdate);var l=t.config.startInterval;!t.player.paused&&t._checkIfCanStart()&&t.start(0,l)}}),y(v(t),"start",function(s,l){var u=t.player.video,c=Ps(),h=t.checkVideoIsSupport(u);!h||!t.canvasCtx||(l||(l=t.interval),t.stop(),u.videoWidth&&u.videoHeight&&(t.videoPI=u.videoHeight>0?parseInt(u.videoWidth/u.videoHeight*100,10):0,(t.config.mode===De.REAL_TIME||c-t.preTime>=l)&&(u&&u.videoWidth&&t.update(h,t.videoPI),t.preTime=c)),t.frameId=t._loopType==="timer"?f.setTimeout(v(t),t.start,l):f.requestAnimationFrame(t.start))}),y(v(t),"stop",function(){t.frameId&&(t._loopType==="timer"?f.clearTimeout(v(t),t.frameId):f.cancelAnimationFrame(t.frameId),t.frameId=null)}),t}return w(r,[{key:"afterCreate",value:function(){var e=this;this.playerConfig.dynamicBg===!0&&(this.config.disable=!1),r.isSupport||(this.config.disable=!0);var i=this.config,n=i.disable,s=i.mode,l=i.frameRate;n||(this._pos={width:0,height:0,rwidth:0,rheight:0,x:0,y:0,pi:0},this.isStart=!1,this._isLoaded=!1,this.videoPI=0,this.preTime=0,this.interval=parseInt(1e3/l,10),this.canvas=null,this.canvasCtx=null,this._frameCount=0,this._loopType=this.config.mode!==De.REAL_TIME&&this.interval>=1e3?"timer":"animation",this.once(st,function(){e.player&&(e.init(),e.renderByPoster(),e.player.paused||e.start())}),s!==De.POSTER&&(s!==De.FIRST_FRAME&&(this.on(Se,function(){e.stop()}),this.on(Q,function(){var u=e.config.startInterval;e._checkIfCanStart()&&e.start(0,u)}),this.on(Me,function(){e.stop()})),this.on(fe,this.onLoadedData),this.on(Ot,function(){e._isLoaded=!1,e.stop()}),document.addEventListener("visibilitychange",this.onVisibilitychange)))}},{key:"setConfig",value:function(e){var i=this;Object.keys(e).forEach(function(n){n==="root"&&e[n]!==i.config[n]?i.reRender(e[n]):n==="frameRate"?i.interval=parseInt(1e3/e[n],10):n==="disable"&&e[n]&&i.stop(),i.config[n]=e[n]})}},{key:"init",value:function(e){var i=this.player,n=this.config;this.canvasFilter=r.supportCanvasFilter();try{var s=e||n.root;s||(s=n.isInnerRender&&i.innerContainer||i.root),s.insertAdjacentHTML("afterbegin",'<div class="xgplayer-dynamic-bg" data-index="'.concat(n.index,'"><canvas>\n        </canvas><xgmask></xgmask></div>')),this.root=s.children[0],this.canvas=this.find("canvas"),this.canvasFilter||(this.canvas.style.filter=n.filter,this.canvas.style.webkitFilter=n.filter),this.mask=this.find("xgmask"),n.addMask&&(this.mask.style.background=n.maskBg),this.canvasCtx=this.canvas.getContext("2d")}catch(l){R.logError("plugin:DynamicBg",l)}}},{key:"reRender",value:function(e){var i=this.config.disable;if(!(!i&&!this.root)){this.stop();var n=this.root?this.root.parentElement:null;if(n!==e&&n.removeChild(this.root),!e){this.root=null;return}this.init(e),this.renderOnce();var s=this.config.startInterval;this._checkIfCanStart()&&this.start(0,s)}}},{key:"checkVideoIsSupport",value:function(e){if(!e)return null;var i=e&&e instanceof window.HTMLVideoElement?e:e.canvas?e.canvas:e.flyVideo?e.flyVideo:null;if(i&&!(A.browser==="safari"&&f.isMSE(i)))return i;var n=i?i.tagName.toLowerCase():"";return n==="canvas"||n==="img"?i:null}},{key:"renderByPoster",value:function(){var e=this.playerConfig.poster;if(e){var i=f.typeOf(e)==="String"?e:f.typeOf(e.poster)==="String"?e.poster:null;this.updateImg(i)}}},{key:"_checkIfCanStart",value:function(){var e=this.config.mode;return this._isLoaded&&!this.player.paused&&e!==De.FIRST_FRAME&&e!==De.POSTER}},{key:"renderOnce",value:function(){var e=this.player.video;if(!(!e.videoWidth||!e.videoHeight)){this.videoPI=parseInt(e.videoWidth/e.videoHeight*100,10);var i=this.checkVideoIsSupport(e);i&&this.update(i,this.videoPI)}}},{key:"updateImg",value:function(e){var i=this;if(e){var n=this.canvas.getBoundingClientRect(),s=n.width,l=n.height,u=new window.Image;u.onload=function(){if(!(!i.canvas||i.frameId||i.isStart)){i.canvas.height=l,i.canvas.width=s;var c=parseInt(s/l*100,10);i.update(u,c),u=null}},u.src=e}}},{key:"update",value:function(e,i){if(!(!this.canvas||!this.canvasCtx||!i))try{var n=this._pos,s=this.config,l=this.canvas.getBoundingClientRect(),u=l.width,c=l.height;if(u!==n.width||c!==n.height||n.pi!==i){var h=parseInt(u/c*100,10);n.pi=i,n.width!==u&&(n.width=this.canvas.width=u),n.height!==c&&(n.height=this.canvas.height=c);var d=c,g=u;h<i?g=parseInt(c*i/100,10):h>i&&(d=parseInt(u*100/i,10)),n.rwidth=g*s.multiple,n.rheight=d*s.multiple,n.x=(u-n.rwidth)/2,n.y=(c-n.rheight)/2}this.canvasFilter&&(this.canvasCtx.filter=s.filter),this.canvasCtx.drawImage(e,n.x,n.y,n.rwidth,n.rheight)}catch(p){R.logError("plugin:DynamicBg",p)}}},{key:"destroy",value:function(){this.stop(),document.removeEventListener("visibilitychange",this.onVisibilitychange),this.canvasCtx=null,this.canvas=null}},{key:"render",value:function(){return""}}],[{key:"pluginName",get:function(){return"dynamicBg"}},{key:"defaultConfig",get:function(){return{isInnerRender:!1,disable:!0,index:-1,mode:"framerate",frameRate:10,filter:"blur(50px)",startFrameCount:2,startInterval:0,addMask:!0,multiple:1.2,maskBg:"rgba(0,0,0,0.7)"}}},{key:"isSupport",get:function(){return typeof Ge=="boolean"||(Ge=Is()),Ge}},{key:"supportCanvasFilter",value:function(){return!(A.browser==="safari"||A.browser==="firefox")}}]),r}(N),As={LANG:"zh-cn",TEXT:{ERROR_TYPES:{network:{code:1,msg:"视频下载错误"},mse:{code:2,msg:"流追加错误"},parse:{code:3,msg:"解析错误"},format:{code:4,msg:"格式错误"},decoder:{code:5,msg:"解码错误"},runtime:{code:6,msg:"语法错误"},timeout:{code:7,msg:"播放超时"},other:{code:8,msg:"其他错误"}},HAVE_NOTHING:"没有关于音频/视频是否就绪的信息",HAVE_METADATA:"音频/视频的元数据已就绪",HAVE_CURRENT_DATA:"关于当前播放位置的数据是可用的，但没有足够的数据来播放下一帧/毫秒",HAVE_FUTURE_DATA:"当前及至少下一帧的数据是可用的",HAVE_ENOUGH_DATA:"可用数据足以开始播放",NETWORK_EMPTY:"音频/视频尚未初始化",NETWORK_IDLE:"音频/视频是活动的且已选取资源，但并未使用网络",NETWORK_LOADING:"浏览器正在下载数据",NETWORK_NO_SOURCE:"未找到音频/视频来源",MEDIA_ERR_ABORTED:"取回过程被用户中止",MEDIA_ERR_NETWORK:"网络错误",MEDIA_ERR_DECODE:"解码错误",MEDIA_ERR_SRC_NOT_SUPPORTED:"不支持的音频/视频格式",REPLAY:"重播",ERROR:"网络连接似乎出现了问题",PLAY_TIPS:"播放",PAUSE_TIPS:"暂停",PLAYNEXT_TIPS:"下一集",DOWNLOAD_TIPS:"下载",ROTATE_TIPS:"旋转",RELOAD_TIPS:"重新载入",FULLSCREEN_TIPS:"进入全屏",EXITFULLSCREEN_TIPS:"退出全屏",CSSFULLSCREEN_TIPS:"进入样式全屏",EXITCSSFULLSCREEN_TIPS:"退出样式全屏",TEXTTRACK:"字幕",PIP:"画中画",SCREENSHOT:"截图",LIVE:"正在直播",OFF:"关闭",OPEN:"开启",MINI_DRAG:"点击按住可拖动视频",MINISCREEN:"小屏幕",REFRESH_TIPS:"请试试",REFRESH:"刷新",FORWARD:"快进中",LIVE_TIP:"直播"}},Fe="info",kt=Zi,Ds=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"_recordUserActions",function(s){var l=t._getTime(),u=Object.assign({},s,{msg:s.msg||s.action});t._stats[Fe].push(V(V({type:"userAction"},l),{},{payload:u}))}),y(v(t),"_onReset",function(){t.reset()}),y(v(t),"_recordInfo",function(s){t.info(s)}),y(v(t),"_downloadStats",function(){var s=t.getStats(),l=new Blob([JSON.stringify(s)],{type:"application/json"}),u=window.URL.createObjectURL(l),c=document.createElement("a");c.style.display="none",c.href=u,c.download="player.txt",c.disabled=!1,c.click()}),t}return w(r,[{key:"_getTime",value:function(){return{timestamp:Date.now(),timeFormat:new Date().toISOString()}}},{key:"afterCreate",value:function(){this.reset(),this.on(Qe,this._recordUserActions),this.on(kt.STATS_INFO,this._recordInfo),this.on(kt.STATS_DOWNLOAD,this._downloadStats),this.on(kt.STATS_RESET,this._onReset)}},{key:"destroy",value:function(){this.offAll()}},{key:"downloadStats",value:function(){this._downloadStats()}},{key:"info",value:function(e){e.profile?this._infoProfile(e):this._info(e)}},{key:"_info",value:function(e){var i=this._getTime();this._stats[Fe].push(V(V({},i),{},{payload:e}))}},{key:"_infoProfile",value:function(e){if(e&&e.startMs){var i=Date.now(),n=i-e.startMs,s=V({cat:"function",dur:n,name:e.name||e.msg,ph:"X",pid:0,tid:0,ts:e.startMs,profile:!0},e);this._info(s)}else console.warn("infoProfile need object data, include startMs")}},{key:"reset",value:function(){var e;this._stats=(e={},y(e,Fe,[]),y(e,"media",{}),e)}},{key:"getStats",value:function(){for(var e=this.player,i=e.media,n=[],s=0;s<i.buffered.length;s++)n.push({start:i.buffered.start(s),end:i.buffered.end(s)});var l={currentTime:i.currentTime,readyState:i.readyState,buffered:n,paused:i.paused,ended:i.ended};return this._stats.media=l,{raw:this._stats,timestat:this._getTimeStats(),profile:this._getProfile()}}},{key:"_getTimeStats",value:function(){var e=this._stats[Fe],i=e.map(function(n){var s=n.payload.data,l="";try{s instanceof Error?l=s.msg:s!==void 0&&(l=JSON.stringify(s))}catch(u){console.log("err",u)}return"[".concat(n.timeFormat,"] : ").concat(n.payload.msg," ").concat(l," ")});return i}},{key:"_getProfile",value:function(){var e={traceEvents:[]},i=this._stats[Fe];return i.forEach(function(n){n.payload.profile&&e.traceEvents.push(n.payload)}),e}}],[{key:"pluginName",get:function(){return"stats"}},{key:"defaultConfig",get:function(){return{}}}]),r}(ye),dn=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"onGapJump",function(){var s=v(t),l=s.player,u=s.config;if(l.media.readyState!==HTMLMediaElement.HAVE_NOTHING){if(l.media.seeking){if(!t.seekingEventReceived)return}else t.seekingEventReceived=!1;if(!(l.media.paused&&l.media.currentTime!==0&&t.hasPlayed)){var c=l.media.buffered,h=u.smallGapLimit||.5,d=u.gapDetectionThreshold||.3,g=l.media.currentTime,p=t._getIndex(c,g,d);if(!(p===null||p===0)){console.log("GapJump  bufferRange ",c.start(p),c.end(p));var m=c.start(p)+.1,_=l.media.duration;if(!(m>_)){var b=m-g,T=b<=h;b<r.BROWSER_GAP_TOLERANCE||T&&(u.useGapJump!==!1&&(l.media.currentTime=t.isSafari?m+.1:m),t.player&&t.player.emit("detectGap"),console.log("gapJump gapIndex",p," isGapSamll:",T," currentTime:",l.media.currentTime," jumpSize:",g-l.media.currentTime),m!==.08&&l&&l.emit("log",{type:"oneevent",end_type:"gap",vid:l.config.vid,ext:{video_postion:Math.floor(m*1e3)}}))}}}}}),t}return w(r,[{key:"afterCreate",value:function(){var e=this,i=this.config.useGapJump;i!==!1&&(this.hasPlayed=!1,this.seekingEventReceived=!1,this.isSafari=/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),this.on(je,this.onGapJump),this.on(Q,function(){e.hasPlayed=!0}),this.on(rt,function(){e.seekingEventReceived=!0}))}},{key:"_getIndex",value:function(e,i,n){if(!e||!e.length||e.length===1&&e.end(0)-e.start(0)<1e-6)return null;for(var s=this._getBuffered(e),l=null,u=0;u<s.length;u++){var c=s[u];if(c.start>i&&(u===0||s[u-1].end-i<=n)){l=u;break}}return l}},{key:"_getBuffered",value:function(e){if(!e)return[];for(var i=[],n=0;n<e.length;n++)i.push({start:e.start(n),end:e.end(n)});return i}}],[{key:"pluginName",get:function(){return"gapJump"}},{key:"defaultConfig",get:function(){return{useGapJump:!1,smallGapLimit:.5,gapDetectionThreshold:.3}}}]),r}(N);dn.BROWSER_GAP_TOLERANCE=.001;var Os=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"onWaiting",function(){var s=v(t),l=s.config;t.jumpCnt>l.jumpCntMax||t.timer||l.useWaitingTimeoutJump===!1||(t.timer=setTimeout(t.onJump,l.waitingTime*1e3))}),y(v(t),"onJump",function(){var s=v(t),l=s.player,u=s.config;if(clearTimeout(t.timer),t.timer=null,!(t.jumpCnt>u.jumpCntMax||u.useWaitingTimeoutJump===!1)&&!(l.media.paused&&l.media.currentTime!==0&&t.hasPlayed)){t.jumpSize=u.jumpSize*(t.jumpCnt+1),t.jumpCnt===u.jumpSize&&t.jumpSize<6&&(t.jumpSize=6);var c=l.currentTime+t.jumpSize,h=l.media.duration;c>h||(t.jumpCnt++,l.currentTime=c)}}),t}return w(r,[{key:"afterCreate",value:function(){var e=this,i=this.config,n=i.useWaitingTimeoutJump,s=i.jumpSize;n!==!1&&(this.hasPlayed=!1,this.jumpCnt=0,this.timer=null,this.jumpSize=s,this.on(je,this.onWaiting),this.on([Dt,ue],function(){clearTimeout(e.timer),e.timer=null,e.jumpSize=e.config.jumpSize}),this.on(Q,function(){e.hasPlayed=!0}))}}],[{key:"pluginName",get:function(){return"waitingTimeoutJump"}},{key:"defaultConfig",get:function(){return{useWaitingTimeoutJump:!1,waitingTime:15,jumpSize:2,jumpCntMax:4}}}]),r}(N),Oe="cdn",Ke=["cdn"],Rs=function(o){S(r,o);var a=x(r);function r(){var t;E(this,r);for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];return t=a.call.apply(a,[this].concat(i)),y(v(t),"getSpeed",function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Oe;if(!t.speedListCache||!t.speedListCache[s]||t.speedListCache[s].length<=0)return 0;var l=0;return t.speedListCache[s].map(function(u){l+=u}),Math.floor(l/t.speedListCache[s].length)}),y(v(t),"startTimer",function(){f.isMSE(t.player.video)||(t.initSpeedList(),t.cnt=0,t.timer=setTimeout(t.testSpeed,t.config.testTimeStep))}),y(v(t),"initSpeedList",function(){t.speedListCache={},Ke.forEach(function(s){t.speedListCache[s]=[]})}),y(v(t),"_onRealSpeedChange",function(s){s.speed&&t.appendList(s.speed,s.type||Oe)}),y(v(t),"testSpeed",function(){if(clearTimeout(t.timer),t.timer=null,!(!t.player||!t.config.openSpeed)){var s=t.config,l=s.url,u=s.loadSize,c=s.testCnt,h=s.testTimeStep,d=l+(l.indexOf("?")<0?"?testst=":"&testst=")+Date.now();if(!(t.cnt>=c)){t.cnt++;try{var g=new Date().getTime(),p=null,m=new XMLHttpRequest;t.xhr=m,m.open("GET",d);var _={},b=Math.floor(Math.random()*10);_.Range="bytes="+b+"-"+(u+b),_&&Object.keys(_).forEach(function(T){m.setRequestHeader(T,_[T])}),m.onreadystatechange=function(){if(m.readyState===4){t.xhr=null,p=new Date().getTime();var T=m.getResponseHeader("Content-Length")/1024*8,k=Math.round(T*1e3/(p-g));t.appendList(k),t.timer=setTimeout(t.testSpeed,h)}},m.send()}catch(T){console.error(T)}}}}),y(v(t),"appendList",function(s){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Oe;if(!(!t.speedListCache||!t.speedListCache[l])){var u=t.config.saveSpeedMax;t.speedListCache[l].length>=u&&t.speedListCache[l].shift(),t.speedListCache[l].push(s);var c=v(t),h=c.player;h&&(l===Oe?h.realTimeSpeed=s:h[t.getSpeedName("realTime",l)]=s),t.updateSpeed(l)}}),y(v(t),"updateSpeed",function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Oe,l=t.getSpeed(s),u=v(t),c=u.player;if(c)if(s===Oe)(!c.avgSpeed||l!==c.avgSpeed)&&(c.avgSpeed=l,c.emit(wt,{speed:l,realTimeSpeed:c.realTimeSpeed}));else{var h=t.getSpeedName("avg",s);(!c[h]||l!==c[h])&&(c[h]=l,c.emit(wt,{speed:l,realTimeSpeed:c.realTimeSpeed}))}}),t}return w(r,[{key:"afterCreate",value:function(){var e=this.config,i=e.openSpeed,n=e.addSpeedTypeList;(n==null?void 0:n.length)>0&&Ke.push.apply(Ke,re(n)),this.initSpeedList(),this.on("real_time_speed",this._onRealSpeedChange),this.timer=null,this.cnt=0,this.xhr=null,i&&this.on([fe,Xe],this.startTimer)}},{key:"getSpeedName",value:function(e,i){return e+"Speed"+i.toUpperCase()}},{key:"openSpeed",get:function(){return this.config.openSpeed},set:function(e){if(this.config.openSpeed=e,!e&&this.timer){clearTimeout(this.timer),this.timer=null;return}if(this.config.openSpeed){if(this.timer)return;this.timer=setTimeout(this.testSpeed,this.config.testTimeStep)}}},{key:"destroy",value:function(){var e=this;this.off("real_time_speed",this._onRealSpeedChange),this.off([fe,Xe],this.startTimer),Ke.forEach(function(i){e.speedListCache&&e.speedListCache[i]&&(e.speedListCache[i]=[])}),this.speedListCache&&(this.speedListCache={}),clearTimeout(this.timer),this.timer=null,this.xhr&&this.xhr.readyState!==4&&(this.xhr.cancel&&this.xhr.cancel(),this.xhr=null)}}],[{key:"pluginName",get:function(){return"testspeed"}},{key:"defaultConfig",get:function(){return{openSpeed:!1,testCnt:3,loadSize:200*1024,testTimeStep:3e3,url:"",saveSpeedMax:5,addSpeedTypeList:[]}}}]),r}(N),Ms=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r,[{key:"afterCreate",value:function(){var e=this,i=this.player,n=this.config,s=i.media||i.video;if(this.timer=null,this._lastDecodedFrames=0,this._currentStuckCount=0,this._lastCheckPoint=null,this._payload=[],!n.disabled){var l=s.getVideoPlaybackQuality;l&&(this.on(Q,function(){e._startTick()}),this.on(Me,function(){e._stopTick()}),this.on(we,function(){e._stopTick()}),this.on(Se,function(){e._stopTick()}))}}},{key:"_startTick",value:function(){var e=this;this._stopTick(),this._timer=setTimeout(function(){e._checkDecodeFPS(),e._startTick()},this.config.tick)}},{key:"_stopTick",value:function(){clearTimeout(this._timer),this._timer=null}},{key:"_checkBuffer",value:function(e,i){for(var n=!1,s=[],l=0;l<i.length;l++){var u=i.start(l),c=i.end(l);if(s.push({start:u,end:c}),u<=e&&e<=c-1){n=!0;break}}return{enoughBuffer:n,buffers:s}}},{key:"_checkStuck",value:function(e,i,n,s){var l=this.player.media||this.player.video,u=document.hidden,c=l.paused,h=l.readyState,d=l.currentTime,g=l.buffered;if(!(u||c||h<4)){var p=this._checkBuffer(d,g),m=p.enoughBuffer,_=p.buffers;m&&(e<=this.config.reportFrame?(this._currentStuckCount++,this._payload.push({currentTime:d,buffers:_,curDecodedFrames:e,totalVideoFrames:i,droppedVideoFrames:n,checkInterval:s}),this._currentStuckCount>=this.config.stuckCount&&(this.emit(Ji,this._payload),this._reset())):this._reset())}}},{key:"_reset",value:function(){this._payload=[],this._currentStuckCount=0}},{key:"_checkDecodeFPS",value:function(){var e=this.player.media||this.player.video;if(e){var i=e.getVideoPlaybackQuality(),n=i.totalVideoFrames,s=i.droppedVideoFrames,l=performance.now();if(n&&this._lastCheckPoint){var u=n-this._lastDecodedFrames,c=l-this._lastCheckPoint;this._checkStuck(u,n,s,c)}this._lastDecodedFrames=n,this._lastCheckPoint=l}}},{key:"destroy",value:function(){this._stopTick()}}],[{key:"pluginName",get:function(){return"FpsDetect"}},{key:"defaultConfig",get:function(){return{disabled:!1,tick:1e3,stuckCount:3,reportFrame:0}}}]),r}(N);Re.use(As);var Fs=w(function o(a,r){var t,e,i;E(this,o);var n=r&&r.isMobileSimulateMode==="mobile",s=r.isLive,l=s?[]:[ss,on,xs,ws,ln],u=[].concat(l,[an,sn,os,hs,Cs,ks,vs,ms,un,cs]),c=[Vr,jr,zr,rs,Gr,Ts,bs,Ss,Xr];this.plugins=[Ds,Hr].concat(re(u),c,[dn,Os]);var h=n?"mobile":A.device;switch(h){case"pc":(t=this.plugins).push.apply(t,[mi,yt,Pt,Rs,Ms]);break;case"mobile":(e=this.plugins).push.apply(e,[ts]);break;default:(i=this.plugins).push.apply(i,[mi,yt,Pt])}(A.os.isIpad||h==="pc")&&this.plugins.push(Ls),A.os.isIpad&&this.plugins.push(yt),this.ignores=[],this.i18n=[]}),z=function(o){S(r,o);var a=x(r);function r(){return E(this,r),a.apply(this,arguments)}return w(r)}(lt);y(z,"defaultPreset",Fs);y(z,"Util",f);y(z,"Sniffer",A);y(z,"Errors",$e);y(z,"Events",ur);y(z,"Plugin",N);y(z,"BasePlugin",ye);y(z,"I18N",Re);y(z,"STATE_CLASS",C);y(z,"InstManager",rn);y(z,"PlayIcon",an);y(z,"TimeIcon",ln);y(z,"Progress",on);y(z,"FullscreenIcon",sn);y(z,"CssFullscreenIcon",Pt);y(z,"VolumeIcon",un);const Ns={name:"VideoPreview",components:{},emits:[],props:{},setup(o,{attrs:a,slots:r,emit:t}){},data(){return{file:{},showDialog:!1,player:null}},computed:{TOKEN(){return this.$store.TOKEN}},watch:{},created(){},mounted(){},beforeUnmount(){this.showDialog=!1,this.destory()},methods:{show(o){if(!o){this.$showToast({message:"未获取到视频文件"});return}this.file=o,this.showDialog=!0,this.$nextTick(()=>{this.init()})},close(){this.showDialog=!1,this.$nextTick(()=>{this.player&&this.player.pause()})},destory(){this.player&&(this.player.destroy(),this.player=null,this.file={})},init(){var n;if(!((n=this.file)!=null&&n.fileToken)||!this.$refs.player)return;const{name:o,fileToken:a}=this.file,r="video/mp4",t=Lt({f8s:a}),e="/api/sys-storage/download",i="".concat(e,"?").concat(t);if(this.player)this.player.switchURL(i),this.player.reload();else{const s=new z({el:this.$refs.player,url:[{type:r,src:i}],lang:"zh-cn",height:"100%",width:"100%",fluid:!0,autoplayMuted:!0});this.player=s}}}},Bs={class:"video-container"},Hs={class:"player-dom",ref:"player"};function Us(o,a,r,t,e,i){const n=ne("van-dialog");return U(),be(n,{show:e.showDialog,"onUpdate:show":a[0]||(a[0]=s=>e.showDialog=s),title:"",confirmButtonText:"关闭",onClosed:i.close},{default:Be(()=>[He("div",Bs,[He("div",Hs,null,512)])]),_:1},8,["show","onClosed"])}const Vs=It(Ns,[["render",Us],["__scopeId","data-v-06905e90"]]);function js(o){return new Promise(async(a,r)=>{const{name:t,fileToken:e}=o,i=Lt({f8s:e}),s=window.location.origin+"/api/sys-storage/download",l="".concat(s,"?").concat(i),u=Rn({duration:0,forbidClick:!0,message:"下载中..."});try{const c=await Ii.getInfo(),h=await fetch(l);if(!h.ok)throw new Error("下载文件失败");const d=await h.blob(),g=new FileReader;g.onload=async()=>{try{const p=g.result.split(",")[1],m=c.platform==="android"?Kt.External:Kt.Documents,_=await Ln.writeFile({path:t,data:p,directory:m,recursive:!0});a({path:_.uri,fileName:t,mimeType:Ws(t)})}catch(p){console.error("保存文件失败",p),r(p)}},g.onerror=()=>r(new Error("读取文件失败")),g.readAsDataURL(d)}catch(c){console.log(c),r(c)}finally{Mn(u)}})}function Ws(o){const a=o.split(".").pop().toLowerCase();return{pdf:"application/pdf",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",gif:"image/gif",txt:"text/plain",zip:"application/zip",rar:"application/x-rar-compressed"}[a]||"application/octet-stream"}function zs(o){return new Promise(async(a,r)=>{try{const t=await js(o);if(!t||!t.path)throw new Error("文件路径无效");await In.open({filePath:t.path}),a()}catch(t){console.log(t),t&&t.code==8&&alert("未找到支持的应用"),r(t)}})}const Wt={name:"FilePreview",components:{VuePdfEmbed:An,VueOfficeDocx:Dn,VueOfficeExcel:On},emits:["preview-closed"],props:{},setup(o,{attrs:a,slots:r,emit:t}){return{isPdf:wi,isXls:Si,isDoc:xi}},data(){return{file:{},url:"",showDialog:!1,loadingMsg:"加载中..."}},computed:{TOKEN(){return this.$store.TOKEN},hasTabbar(){var a;const o=((a=this.$route)==null?void 0:a.name)||"";return Cn.find(r=>r.name===o)},containerHeight(){const o="calc(90vh - var(--van-dialog-header-line-height) - var(--van-dialog-button-height) - var(--sat) - var(--sab))";return this.hasTabbar?"calc(".concat(o," - var(--van-tabbar-height) - 50px)"):o}},watch:{},created(){},mounted(){},beforeUnmount(){this.showDialog=!1},methods:{show(o){if(!o){this.$showToast({message:"暂不支持该文件预览"});return}this.file=o,kn.isNativePlatform()?zs(this.file).then(()=>{}).catch(a=>{console.log(a),this.loading=!0,this.$nextTick(()=>{this.init(),this.showDialog=!0,this.loading=!1})}):window.ENV_FEISHU&&window.tt?(this.init(),window.open(this.url),this.$showToast({message:"正在打开文件"})):(this.loading=!0,this.$nextTick(()=>{this.init(),this.showDialog=!0,this.loading=!1}))},close(){this.showDialog=!1,this.url="",this.file={},this.loadingMsg="加载中...",this.$emit("preview-closed")},init(){var i;if(!((i=this.file)!=null&&i.fileToken))return;const{name:o,fileToken:a}=this.file,r=Lt({f8s:a}),t="/api/sys-storage/download",e="".concat(t,"?").concat(r);this.url=e},pdfLoaded(o){},pdfLoadingFailed(o){this.$showToast("pdf打开失败")},pdfProgress({loaded:o,total:a}){let r=a==0?"":(Number(o)/Number(a)*100).toFixed(0)+"%";this.loadingMsg="加载中...".concat(r)},rendered(){}}},Ci=()=>{wn(o=>({"115ab941":o.containerHeight}))},ki=Wt.setup;Wt.setup=ki?(o,a)=>(Ci(),ki(o,a)):Ci;const Gs={class:"file-container"};function Ks(o,a,r,t,e,i){const n=ne("vue-pdf-embed"),s=ne("vue-office-docx"),l=ne("vue-office-excel"),u=ne("van-loading"),c=ne("van-dialog");return U(),be(c,{class:xn(["preview-dialog",i.hasTabbar?"with-tabbar":""]),show:e.showDialog,"onUpdate:show":a[0]||(a[0]=h=>e.showDialog=h),title:e.file.name||"预览","message-align":"center",confirmButtonText:"关闭",onClosed:i.close},{default:Be(()=>[He("div",Gs,[e.url?(U(),K(Li,{key:0},[t.isPdf(e.file.name)?(U(),be(n,{key:0,source:e.url,onLoadingFailed:i.pdfLoadingFailed,onProgress:i.pdfProgress},null,8,["source","onLoadingFailed","onProgress"])):ee("",!0),t.isDoc(e.file.name)?(U(),be(s,{key:1,src:e.url,style:{height:"100%",width:"100%"},onRendered:i.rendered},null,8,["src","onRendered"])):ee("",!0),t.isXls(e.file.name)?(U(),be(l,{key:2,src:e.url,style:{height:"100%",width:"100%"},onRendered:i.rendered},null,8,["src","onRendered"])):ee("",!0)],64)):ee("",!0)]),o.loading?(U(),be(u,{key:0,type:"spinner",vertical:"",class:"loading"},{default:Be(()=>[Sn(Ye(e.loadingMsg),1)]),_:1})):ee("",!0)]),_:1},8,["class","show","title","onClosed"])}const Ys=It(Wt,[["render",Ks],["__scopeId","data-v-891397a1"]]);var Ti;(function(o){o.Prompt="PROMPT",o.Camera="CAMERA",o.Photos="PHOTOS"})(Ti||(Ti={}));var bi;(function(o){o.Rear="REAR",o.Front="FRONT"})(bi||(bi={}));var Ei;(function(o){o.Uri="uri",o.Base64="base64",o.DataUrl="dataUrl"})(Ei||(Ei={}));const $s=Tn("Camera",{web:()=>bn(()=>import("./web-3011cfe2.js"),["assets/web-3011cfe2.js","assets/index-4829f8e2.js","assets/verder-361ae6c7.js","assets/vant-91101745.js","assets/index-a9b3f457.css","assets/file-842bc27d.js","assets/file-2bef16be.js","assets/delegate-b245d146.js","assets/index-889f88bd.js","assets/index-71c30edb.css"]).then(o=>new o.CameraWeb)});const Xs={name:"UploadFiles",components:{VideoPreview:Vs,FilePreview:Ys},emits:["update:g9s","update:files","update:name1","update:name2","update:extName"],props:{g9s:{type:String,default:""},accept:{type:String,default:"image/*"},multiple:Boolean,maxCount:{type:Number,default:9},maxSize:{type:Number,default:1*1024*1024*50},readonly:Boolean,capture:{type:String,default:void 0},previewSize:{type:[Number,String,Array],default:void 0},previewFullImage:{type:Boolean,default:!0},noText:{type:String,default:"暂无附件"},simplePreview:Boolean,files:{}},setup(o,{attrs:a,slots:r,emit:t}){return{calcMaxSize:gn(o.maxSize,0),DOWNLOAD_IMAGE_API:"/api/sys-storage/download_image"}},data(){return{fileList:[],deleteList:[],ignoreInit:!1}},computed:{},watch:{g9s:{immediate:!0,handler(o){this.ignoreInit||this.initFiles()}},fileList:{handler(o){this.$emit("update:files",o.length)}}},created(){},mounted(){},unmounted(){},methods:{async handleLongPress1(o){const t=o.target.parentNode.querySelector("img").getAttribute("src"),{platform:e}=await Ii.getInfo();console.log(e),e==="android"&&(o.preventDefault(),Fn({title:"提示",message:"是否保存到相册"}).then(()=>{this.saveImageToGallery(t)}).catch(()=>{}))},async saveImageToGallery(o){try{await $s.saveToGallery({data:o}),console.log("图片保存成功并添加到相册！")}catch(a){console.error("保存图片到相册出错：",a)}},async initFiles(){try{this.loading=!0;const o=[];this.g9s&&[...await mn({g9s:[this.g9s]})].forEach(async({fileToken:r,size:t,fileName:e})=>{const i={fileToken:r,size:t,fileName:e},n=this.processFileMetadata({deletable:!this.readonly},i);o.push(n)}),this.fileList=o}catch(o){console.log(o)}finally{this.loading=!1}},beforeRead(o,{name:a,index:r}={}){return this.g9s?this.ignoreInit=!1:(this.ignoreInit=!0,this.$emit("update:g9s",zt(16,32))),Array.isArray(o)?this.compressorFile(o):this.compressorFile(o)},compressorFile(o){return!0},afterRead(o,{name:a,index:r}={}){const t=Array.isArray(o)?o:[o];if(t.forEach(e=>this.uploadFile(e)),t&&t[0]&&t[0].file&&t[0].file.name){const e=t[0].file.name;this.$emit("update:name1",e),this.$emit("update:extName",e.lastIndexOf(".")),this.$emit("update:name2",e.substring(0,e.lastIndexOf(".")))}},async uploadFile(o){try{o.status="uploading";const a=new FormData;a.append("g9s",this.g9s),a.append("file",o.file);const r=zt();o.fileId=r;const t=await En({url:"/sys-storage/upload",method:"post",headers:{"Content-Type":"multipart/form-data"},data:a,timeout:0,onUploadProgress:e=>{let i=e.loaded/e.total*100|0;o.message="上传中 ".concat(i,"%")}});o.status="success",o.fileToken=t==null?void 0:t.fileToken,t&&this.processFileMetadata(o,t)}catch(a){console.log(a),this.$showToast({message:"上传失败"}),this.fileList.splice(this.fileList.findIndex(r=>r.fileId===fileId),1)}},onOversize(o){this.$showToast({message:"文件大小不能超出".concat(this.calcMaxSize,"!")})},beforeDelete(o,{name:a,index:r}){return new Promise(async(t,e)=>{try{await this.$confirm({title:"提示",message:"确认删除?"}),o.fileToken&&this.deleteList.push(o.fileToken),t(!0)}catch(i){e(!1)}})},handleClickPreview(o){o.isImage||(o.isVideo?this.$refs.videoPreview&&this.$refs.videoPreview.show(o):this.$refs.filePreview&&this.$refs.filePreview.show(o))},processFileMetadata(o,a){const r=a.fileName||o.name;return o.name=r,o.size=a.size||o.size,o.extName=a.extName||"",o.fileToken=a.fileToken||o.fileToken,o.isImage=pn(r),o.isVideo=vn(r),o.isPdf=wi(r),o.isDoc=xi(r),o.isXls=Si(r),o.isImage&&o.fileToken&&(o.url="".concat(this.DOWNLOAD_IMAGE_API,"?f8s=").concat(o.fileToken)),o},update(){return new Promise(async(o,a)=>{try{const r=this.deleteList;r&&r.length&&(await yn({f8s:r}),this.deleteList=[]),o()}catch(r){a()}})}}},qs={key:0,class:"no-files"},Zs={key:1,class:"simple-preview-list"},Js={key:1,class:"van-uploader__preview-image preview-video"},Qs={key:2,class:"van-uploader__preview-image preview-file"},ea={key:0,class:"preview-remain"},ta={key:2,class:"van-uploader__upload"},ia={key:0},na={key:1,class:"preview-cover-video"},ra={key:2,class:"preview-cover"},sa={class:"file-type"},aa={key:0},oa={key:1},la={key:2},ua={class:"file-name van-ellipsis"};function ca(o,a,r,t,e,i){const n=ne("van-image"),s=ne("van-icon"),l=ne("van-uploader"),u=ne("VideoPreview"),c=ne("FilePreview");return U(),K("div",null,[ke(l,{modelValue:e.fileList,"onUpdate:modelValue":a[0]||(a[0]=h=>e.fileList=h),accept:r.accept,multiple:r.multiple,"max-count":r.maxCount,"max-size":r.maxSize,onOversize:i.onOversize,"before-read":i.beforeRead,"after-read":i.afterRead,"before-delete":i.beforeDelete,"preview-size":r.previewSize,"preview-full-image":r.previewFullImage,readonly:r.readonly,"preview-image":!r.simplePreview,onClickPreview:i.handleClickPreview,onContextmenu:i.handleLongPress1},{default:Be(()=>[!e.fileList.length&&r.readonly?(U(),K("div",qs,Ye(r.noText),1)):ee("",!0),r.simplePreview&&r.readonly?(U(),K("div",Zs,[(U(!0),K(Li,null,Pn([...e.fileList].slice(0,2),(h,d)=>(U(),K("div",{key:d,class:"simple-preview van-uploader__preview"},[h.isImage?(U(),be(n,{key:0,class:"van-uploader__preview-image",src:h.url},null,8,["src"])):h.isVideo?(U(),K("div",Js,[ke(s,{color:"#fff",size:"28",name:"play-circle-o"})])):(U(),K("div",Qs,[ke(s,{color:"#333",size:"28",name:"description"})]))]))),128)),[...e.fileList].slice(2).length?(U(),K("div",ea," + "+Ye([...e.fileList].slice(2).length),1)):ee("",!0)])):ee("",!0),r.readonly?ee("",!0):(U(),K("div",ta,[ke(s,{class:"van-uploader__upload-icon",name:"plus"})]))]),"preview-cover":Be(h=>[h.isImage?(U(),K("div",ia)):h.isVideo?(U(),K("div",na,[ke(s,{color:"#fff",size:"28",name:"play-circle-o"})])):(U(),K("div",ra,[He("div",sa,[h.isPdf?(U(),K("span",aa,"PDF")):ee("",!0),h.isDoc?(U(),K("span",oa,"DOC")):ee("",!0),h.isXls?(U(),K("span",la,"XLS")):ee("",!0)]),He("div",ua,Ye(h.name),1)]))]),_:1},8,["modelValue","accept","multiple","max-count","max-size","onOversize","before-read","after-read","before-delete","preview-size","preview-full-image","readonly","preview-image","onClickPreview","onContextmenu"]),ke(u,{ref:"videoPreview"},null,512),ke(c,{ref:"filePreview"},null,512)])}const ya=It(Xs,[["render",ca],["__scopeId","data-v-7d2476c6"]]);export{Ti as C,ya as U,bi as a};
