System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var l,t,n,s,o,m,r,i,d,c;return{setters:[e=>{l=e.F,t=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,m=e.X,r=e.V,i=e.k,d=e.U,c=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"one-line"},u={class:"check-wp"},p={class:"check-wp"},f={class:"form-info"},h={class:"form-info"},g={class:"form-info"},b={class:"form-info"},y={class:"comment-wp"},k={class:"textarea-wp"},v={class:"footer-input"},V={class:"form-info"},j={class:"form-info"},D={class:"form-info"},C={class:"form-info"};e("default",n({name:"JL04",components:{FormTemplate:l,DocumentPart:t},emits:[],props:{},setup(e,{attrs:a,slots:l,emit:t}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit({formData:e,detailParamList:a,taskStart:l},t){return new Promise(((a,n)=>{try{if("submit"===t&&l){if(!e.check1&&!e.check2)return this.$showNotify({type:"danger",message:"请选择类型!",duration:3e3}),n(!1),!1;if(e.check2&&!e.field1)return this.$showNotify({type:"danger",message:"请完善信息!",duration:3e3}),n(!1),!1}a()}catch(s){n(s)}}))},changeCheck1(e){e&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.field1="",this.$nextTick((()=>{var e;null===(e=this.$refs.FormTemplate.$refs.form)||void 0===e||e.resetValidation()})))},changeCheck2(e){e&&(this.$refs.FormTemplate.formData.check1=!1)}}},[["render",function(e,l,t,n,T,w){const x=s("van-checkbox"),F=s("DocumentPart"),P=s("van-field"),N=s("FormTemplate");return o(),m(N,{ref:"FormTemplate",nature:"工预付","employer-target":!0,"on-after-init":w.onAfterInit,"on-before-submit":w.onBeforeSubmit,"detail-table":T.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:T.attachmentDesc},{default:r((({formData:e,formTable:t,baseObj:n,uploadAccept:s,taskStart:o,taskComment2:m,taskComment3:v,taskComment4:V})=>[i(F,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!o},{default:r((()=>[d("div",a,[l[0]||(l[0]=d("span",{style:{"padding-left":"2em"}},"鉴于",-1)),d("div",u,[i(x,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!0,onChange:w.changeCheck1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),l[1]||(l[1]=d("span",null,"工程预付款担保已获得贵方确认",-1)),l[2]||(l[2]=d("span",null," / ",-1)),d("div",p,[i(x,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!0,onChange:w.changeCheck2},null,8,["modelValue","onUpdate:modelValue","onChange"])]),l[3]||(l[3]=d("span",null,"合同约定的第",-1)),d("span",f,c(e.field1),1),l[4]||(l[4]=d("span",null,"次工程预付款条件已具备。",-1)),l[5]||(l[5]=d("span",null,"根据施工合同约定，",-1)),l[6]||(l[6]=d("span",null,"贵方应向承包人支付第",-1)),d("span",h,c(e.field2),1),l[7]||(l[7]=d("span",null,"次工程预付款，",-1)),l[8]||(l[8]=d("span",null,"金额为：",-1))]),d("div",null,[l[9]||(l[9]=d("span",null,"大写：",-1)),d("span",g,c(e.field3),1),l[10]||(l[10]=d("span",null,"元",-1))]),d("div",null,[l[11]||(l[11]=d("span",null,"小写：",-1)),d("span",b,c(e.field4),1),l[12]||(l[12]=d("span",null,"元",-1))])])),_:2},1032,["deptValue","deptOptions","disabled"]),i(F,{deptLabel:"发包人：",deptProp:"employerName",deptValue:e.employerName,deptOptions:n.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!o},{default:r((()=>[d("div",y,[l[13]||(l[13]=d("div",null,"发包人审批意见：",-1)),d("div",k,[i(P,{modelValue:e.comment4,"onUpdate:modelValue":a=>e.comment4=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!V},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:r((({formData:e,formTable:a,baseObj:t,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:m,taskComment4:r})=>[d("div",v,[l[14]||(l[14]=d("span",null,"说明：本证书一式",-1)),d("span",V,c(e.num1),1),l[15]||(l[15]=d("span",null,"份，由监理机构填写，承包人",-1)),d("span",j,c(e.num2),1),l[16]||(l[16]=d("span",null,"份，监理机构",-1)),d("span",D,c(e.num3),1),l[17]||(l[17]=d("span",null,"份，发包人",-1)),d("span",C,c(e.num4),1),l[18]||(l[18]=d("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
