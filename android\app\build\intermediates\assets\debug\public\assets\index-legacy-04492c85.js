System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(l,e){"use strict";var t,a,s,n,c,i,d,u,o;return{setters:[l=>{t=l.F,a=l.D},l=>{s=l._},l=>{n=l.Q,c=l.R,i=l.X,d=l.V,u=l.U,o=l.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const e={class:"jl-table"},r={class:"cell"},m={class:"cell"},p={class:"cell"},f={class:"cell"},v={colspan:"7"},h={class:"cell"},g={colspan:"7"},y={class:"cell"},b={colspan:"7"},j={class:"cell"},x={colspan:"7"},w={class:"cell"},k={colspan:"7"},D={class:"cell"},T={colspan:"7"},C={class:"cell"};l("default",s({name:"JL33",components:{FormTemplate:t,DocumentPart:a},emits:[],props:{},setup(l,{attrs:e,slots:t,emit:a}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:l,detailParamList:e}){},onBeforeSubmit:({formData:l,detailParamList:e,taskStart:t},a)=>new Promise(((l,e)=>{try{l()}catch(t){e(t)}}))}},[["render",function(l,t,a,s,F,P){const S=n("FormTemplate");return c(),i(S,{ref:"FormTemplate",nature:"日记","on-after-init":P.onAfterInit,"on-before-submit":P.onBeforeSubmit,"detail-table":F.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:F.attachmentDesc},{default:d((({formData:l,formTable:a,baseObj:s,uploadAccept:n,taskStart:c,taskComment2:i,taskComment3:d,taskComment4:F})=>[u("table",e,[t[11]||(t[11]=u("colgroup",null,[u("col",{width:"55px"}),u("col",{"min-width":"40px"}),u("col",{"min-width":"40px"}),u("col",{"min-width":"40px"}),u("col",{"min-width":"40px"}),u("col",{"min-width":"40px"})],-1)),u("tbody",null,[u("tr",null,[t[0]||(t[0]=u("th",null,[u("div",{class:"cell"},"天气")],-1)),u("td",null,[u("div",r,[u("span",null,o(l.field1),1)])]),t[1]||(t[1]=u("th",null,[u("div",{class:"cell"},"气温")],-1)),u("td",null,[u("div",m,[u("span",null,o(l.field2),1)])]),t[2]||(t[2]=u("th",null,[u("div",{class:"cell"},"风力")],-1)),u("td",null,[u("div",p,[u("span",null,o(l.field3),1)])]),t[3]||(t[3]=u("th",null,[u("div",{class:"cell"},"风向")],-1)),u("td",null,[u("div",f,[u("span",null,o(l.field4),1)])])]),u("tr",null,[t[4]||(t[4]=u("th",null,[u("div",{class:"cell"}," 施工部位、施工内容（包括隐蔽部位施工时的地质编录情况）、施工形象及资源投入情况 ")],-1)),u("td",v,[u("div",h,[u("span",null,o(l.field5),1)])])]),u("tr",null,[t[5]||(t[5]=u("th",null,[u("div",{class:"cell"},"承包人质量检验和安全作业情况")],-1)),u("td",g,[u("div",y,[u("span",null,o(l.field6),1)])])]),u("tr",null,[t[6]||(t[6]=u("th",null,[u("div",{class:"cell"},"监理机构的检查、巡视、检验情况")],-1)),u("td",b,[u("div",j,[u("span",null,o(l.field7),1)])])]),u("tr",null,[t[7]||(t[7]=u("th",null,[u("div",{class:"cell"}," 施工作业存在的问题，现场监理人员提出的处理意见以及承包人对处理意见的落实情况 ")],-1)),u("td",x,[u("div",w,[u("span",null,o(l.field8),1)])])]),u("tr",null,[t[8]||(t[8]=u("th",null,[u("div",{class:"cell"},"汇报事项和监理机构指示")],-1)),u("td",k,[u("div",D,[u("span",null,o(l.field9),1)])])]),u("tr",null,[t[9]||(t[9]=u("th",null,[u("div",{class:"cell"},"其他事项")],-1)),u("td",T,[u("div",C,[u("span",null,o(l.field10),1)])])]),t[10]||(t[10]=u("tr",null,[u("td",{colspan:"8"},[u("div",{class:"cell"},[u("div",{style:{height:"30px"}}),u("div",{class:"part-div",style:{"margin-left":"100px"}},[u("div",{class:"part-sign"},[u("div",null,[u("span",null,"监理人员：")]),u("div",null,[u("span",null,"日期："),u("span",{style:{"padding-left":"2em"}},"年"),u("span",{style:{"padding-left":"2em"}},"月"),u("span",{style:{"padding-left":"2em"}},"日")])])])])])],-1))])])])),footer:d((({formData:l,formTable:e,baseObj:a,uploadAccept:s,taskStart:n,taskComment2:c,taskComment3:i,taskComment4:d})=>t[12]||(t[12]=[u("div",{class:"footer-input"},[u("div",null,"说明：本表由现场监理人员填写，按月装订成册。")],-1)]))),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
