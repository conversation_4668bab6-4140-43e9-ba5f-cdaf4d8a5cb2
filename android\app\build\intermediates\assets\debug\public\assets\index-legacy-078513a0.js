System.register(["./index-legacy-0aa9ecd4.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var l,t,n,s,o,d,m,p,i,c,r,u,f,h,b;return{setters:[e=>{l=e.F,t=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,d=e.X,m=e.V,p=e.k,i=e.U,c=e.Y,r=e.S,u=e.W,f=e.F,h=e.B,b=e.Z},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"one-line"},y={class:"form-info"},k={class:"form-info"},g={class:"form-info"},w={class:"input-line"},V={class:"form-info"},v={class:"form-info"},D={class:"form-info"},C={class:"form-table"},j={class:"center"},x={class:"attachment-desc"},_={class:"comment-wp"},N={class:"textarea-wp"},P={class:"comment-wp"},U={class:"check-wp"},F={class:"check-wp"},T={key:0,class:"textarea-wp"},L={class:"footer-input"},O={class:"form-info"},S={class:"form-info"},z={class:"form-info"},A={class:"form-info"};e("default",n({name:"CB07Xxh",components:{FormTemplate:l,DocumentPart:t},emits:[],props:{},setup(e,{attrs:a,slots:l,emit:t}){},data:()=>({detailTable:[{},{},{},{}],attachmentDesc:"1、质量证明文件。\n2、进场原材料/中间产品外观验收检查记录。\n3、检测报告。"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit({formData:e,detailParamList:a,taskComment3:l},t){return new Promise(((a,n)=>{try{if("submit"===t&&l){if(!e.check1&&!e.check2)return this.$showNotify({type:"danger",message:"请选择审查意见!",duration:3e3}),n(!1),!1;if(e.check2&&!e.comment3)return this.$showNotify({type:"danger",message:"请填写审查意见!",duration:3e3}),n(!1),!1}a()}catch(s){n(s)}}))},changeCheck1(e){e&&(this.$refs.FormTemplate.formData.check2=!1)},changeCheck2(e){e&&(this.$refs.FormTemplate.formData.check1=!1)}}},[["render",function(e,l,t,n,B,I){const W=s("van-field"),$=s("DocumentPart"),q=s("van-checkbox"),X=s("FormTemplate");return o(),d(X,{ref:"FormTemplate",nature:"报验","on-after-init":I.onAfterInit,"on-before-submit":I.onBeforeSubmit,"detail-table":B.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:B.attachmentDesc},{default:m((({formData:e,formTable:t,baseObj:n,uploadAccept:s,taskStart:d,taskComment2:L,taskComment3:O,taskComment4:S,taskComment5:z})=>[p($,{deptLabel:"信息化项目部：",deptProp:"informationDeptName",deptValue:e.informationDeptName,deptOptions:n.informationDeptName,personLabel:"信息化负责人：",labelWidth:"10em",disabled:!d},{default:m((()=>[i("div",a,[l[0]||(l[0]=i("span",{style:{"padding-left":"2em"}},"我方于",-1)),i("span",y,c(e.field1),1),l[1]||(l[1]=i("span",null,"年",-1)),i("span",k,c(e.field2),1),l[2]||(l[2]=i("span",null,"月",-1)),i("span",g,c(e.field3),1),l[3]||(l[3]=i("span",null,"日进场的原材料/中间产品如下表。",-1)),l[4]||(l[4]=i("span",null,"拟用于下述部位：",-1))]),i("div",w,[l[5]||(l[5]=i("span",null,"1、",-1)),i("span",V,c(e.field4),1),l[6]||(l[6]=i("span",null,"2、",-1)),i("span",v,c(e.field5),1),l[7]||(l[7]=i("span",null,"3、",-1)),i("span",D,c(e.field6),1)]),l[10]||(l[10]=i("div",null,"经自检，符合合同要求，请贵方审核。",-1)),i("div",C,[i("table",null,[l[8]||(l[8]=i("thead",null,[i("tr",null,[i("th",{colspan:"1",rowspan:"1"},"序号"),i("th",{colspan:"1",rowspan:"1"},"原材料/中间产品名称"),i("th",{colspan:"1",rowspan:"1"},"原材料/中间产品来源、产地"),i("th",{colspan:"1",rowspan:"1"},"原材料/中间产品规格"),i("th",{colspan:"1",rowspan:"1"},"用途"),i("th",{colspan:"1",rowspan:"1"},"本批原材料/中间产品数量"),i("th",{colspan:"1",rowspan:"1"},"试样来源"),i("th",{colspan:"1",rowspan:"1"},"取样地点"),i("th",{colspan:"1",rowspan:"1"},"取样日期"),i("th",{colspan:"1",rowspan:"1"},"试验日期"),i("th",{colspan:"1",rowspan:"1"},"试验结果"),i("th",{colspan:"1",rowspan:"1"},"质检负责人")])],-1)),i("tbody",null,[(o(!0),r(f,null,u(t||[],((e,a)=>(o(),r("tr",{key:a},[i("td",j,c(a+1),1),i("td",null,c(e.field1),1),i("td",null,c(e.field2),1),i("td",null,c(e.field3),1),i("td",null,c(e.field4),1),i("td",null,c(e.field5),1),i("td",null,c(e.field6),1),i("td",null,c(e.field7),1),i("td",null,c(e.field10),1),i("td",null,c(e.field8),1),i("td",null,c(e.field9),1),i("td",null,c(e.field11),1)])))),128))])])]),i("div",x,[l[9]||(l[9]=i("div",null,"附件：",-1)),p(W,{modelValue:e.attachmentDesc,"onUpdate:modelValue":a=>e.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2,__:[10]},1032,["deptValue","deptOptions","disabled"]),p($,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!d},{default:m((()=>[i("div",_,[l[11]||(l[11]=i("div",null,"EPC总承包项目部意见：",-1)),i("div",N,[p(W,{modelValue:e.comment2,"onUpdate:modelValue":a=>e.comment2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!L},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),p($,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!d},{default:m((()=>[i("div",P,[l[14]||(l[14]=i("div",null,"审查意见：",-1)),i("div",null,[i("div",U,[p(q,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!O,onChange:I.changeCheck1},{default:m((()=>l[12]||(l[12]=[h(" 同意进场使用 ")]))),_:2,__:[12]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),i("div",null,[i("div",F,[p(q,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!O,onChange:I.changeCheck2},{default:m((()=>l[13]||(l[13]=[h(" 不同意进场使用；理由： ")]))),_:2,__:[13]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),e.check2?(o(),r("div",T,[p(W,{modelValue:e.comment3,"onUpdate:modelValue":a=>e.comment3=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!O},null,8,["modelValue","onUpdate:modelValue","readonly"])])):b("",!0)])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:m((({formData:e,formTable:a,baseObj:t,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:d,taskComment4:m,taskComment5:p})=>[i("div",L,[l[15]||(l[15]=i("span",null,"说明：本表一式",-1)),i("span",O,c(e.num1),1),l[16]||(l[16]=i("span",null,"份，由承包人填写，监理机构审核后，发包人",-1)),i("span",S,c(e.num2),1),l[17]||(l[17]=i("span",null,"份，监理机构",-1)),i("span",z,c(e.num3),1),l[18]||(l[18]=i("span",null,"份，承包人",-1)),i("span",A,c(e.num4),1),l[19]||(l[19]=i("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
