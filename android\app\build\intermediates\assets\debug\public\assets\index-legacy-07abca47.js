System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var l,n,s,t,o,d,i,p,c,m,u,r,f;return{setters:[e=>{l=e.F,n=e.D},e=>{s=e._},e=>{t=e.Q,o=e.R,d=e.X,i=e.V,p=e.k,c=e.U,m=e.Y,u=e.B,r=e.S,f=e.Z},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"one-line"},h={class:"form-info"},g={class:"form-info"},b={class:"check-wp",style:{"text-indent":"0"}},k={class:"form-info"},y={class:"form-info"},V={class:"form-info"},v={class:"form-info"},C={class:"form-info"},D={class:"comment-wp"},j={class:"one-line"},_={class:"check-wp"},x={style:{padding:"0 5px"}},w={style:{padding:"0 5px"}},T={class:"one-line"},U={class:"check-wp"},F={style:{padding:"0 5px"}},P={style:{padding:"0 5px"}},N={key:0,class:"textarea-wp"},L={class:"footer-input"},S={class:"form-info"},$={class:"form-info"},O={class:"form-info"},q={class:"form-info"},A={class:"form-info"};e("default",s({name:"JL16",components:{FormTemplate:l,DocumentPart:n},emits:[],props:{},setup(e,{attrs:a,slots:l,emit:n}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit({formData:e,detailParamList:a},l){return new Promise(((a,n)=>{try{if("submit"===l&&taskStart&&!e.check1&&!e.check2)return this.$showNotify({type:"danger",message:"请选择复工范围：全部/部分",duration:3e3}),n(!1),!1;a()}catch(s){n(s)}}))},changeCheck1(e){e&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.field7="",this.$nextTick((()=>{var e;null===(e=this.$refs.FormTemplate.$refs.form)||void 0===e||e.resetValidation()})))},changeCheck2(e){e&&(this.$refs.FormTemplate.formData.check1=!1)}}},[["render",function(e,l,n,s,I,B){const z=t("van-checkbox"),R=t("van-field"),W=t("DocumentPart"),J=t("FormTemplate");return o(),d(J,{ref:"FormTemplate",nature:"复工","on-after-init":B.onAfterInit,"on-before-submit":B.onBeforeSubmit,"detail-table":I.detailTable,"is-show-confirm1":!1,attachmentDesc:I.attachmentDesc},{default:i((({formData:e,formTable:n,baseObj:s,uploadAccept:t,taskStart:d,taskComment2:L,taskComment3:S,taskComment4:$})=>[p(W,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:s.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!d},{default:i((()=>[c("div",a,[l[3]||(l[3]=c("span",{style:{"padding-left":"2em"}},"鉴于暂停施工指示（ 监理 [ ",-1)),c("span",h,m(e.field1),1),l[4]||(l[4]=c("span",null," ] 停工",-1)),c("span",g,m(e.field2),1),l[5]||(l[5]=c("span",null,"号）",-1)),l[6]||(l[6]=c("span",null,"所述原因已经",-1)),c("div",b,[p(z,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!d,onChange:B.changeCheck1},{default:i((()=>l[0]||(l[0]=[u("全部")]))),_:2,__:[0]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),l[2]||(l[2]=c("span",null," / ",-1)),p(z,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!d,onChange:B.changeCheck2},{default:i((()=>l[1]||(l[1]=[u("部分")]))),_:2,__:[1]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[7]||(l[7]=c("span",null,"消除，",-1)),l[8]||(l[8]=c("span",null,"你方可于",-1)),c("span",k,m(e.field3),1),l[9]||(l[9]=c("span",null,"年",-1)),c("span",y,m(e.field4),1),l[10]||(l[10]=c("span",null,"月",-1)),c("span",V,m(e.field5),1),l[11]||(l[11]=c("span",null,"日",-1)),c("span",v,m(e.field6),1),l[12]||(l[12]=c("span",null,"时起对",-1)),c("span",C,m(e.projectName),1),l[13]||(l[13]=c("span",null,"工程",-1)),l[14]||(l[14]=c("span",null,"下列范围恢复施工。",-1))]),c("div",D,[l[23]||(l[23]=c("div",null,"复工范围：",-1)),c("div",j,[c("div",_,[p(z,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!d,onChange:B.changeCheck2},{default:i((()=>l[15]||(l[15]=[u(" ")]))),_:2,__:[15]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[16]||(l[16]=c("span",null,"监理 [ ",-1)),c("span",x,m(e.field1),1),l[17]||(l[17]=c("span",null," ] 停工 ",-1)),c("span",w,m(e.field2),1),l[18]||(l[18]=c("span",null," 号指示的全部暂停施工项目",-1))]),c("div",T,[c("div",U,[p(z,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!d,onChange:B.changeCheck2},{default:i((()=>l[19]||(l[19]=[u(" ")]))),_:2,__:[19]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[20]||(l[20]=c("span",null,"监理 [ ",-1)),c("span",F,m(e.field1),1),l[21]||(l[21]=c("span",null," ] 停工 ",-1)),c("span",P,m(e.field2),1),l[22]||(l[22]=c("span",null," 号指示的下列暂停施工项目",-1))]),e.check2?(o(),r("div",N,[p(R,{modelValue:e.field7,"onUpdate:modelValue":a=>e.field7=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"请填写暂停施工项目",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])):f("",!0)])])),_:2},1032,["deptValue","deptOptions","disabled"]),p(W,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:s.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!d},{default:i((()=>l[24]||(l[24]=[c("div",{class:"comment-wp"},[c("div",{style:{height:"30px"}})],-1)]))),_:2,__:[24]},1032,["deptValue","deptOptions","disabled"])])),footer:i((({formData:e,formTable:a,baseObj:n,uploadAccept:s,taskStart:t,taskComment2:o,taskComment3:d,taskComment4:i})=>[c("div",L,[l[25]||(l[25]=c("span",null,"说明：本表一式",-1)),c("span",S,m(e.num1),1),l[26]||(l[26]=c("span",null,"份，由监理机构填写，承包人签收后，发包人",-1)),c("span",$,m(e.num2),1),l[27]||(l[27]=c("span",null,"份，设代机构",-1)),c("span",O,m(e.num3),1),l[28]||(l[28]=c("span",null,"份，监理机构",-1)),c("span",q,m(e.num4),1),l[29]||(l[29]=c("span",null,"份，承包人",-1)),c("span",A,m(e.num5),1),l[30]||(l[30]=c("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
