System.register(["./index-legacy-b580af71.js","./FormItemPicker-legacy-fd45c24d.js","./FormItemDate-legacy-c4422d42.js","./FormItemCalendar-legacy-ea787ea1.js","./FormItemPerson-legacy-e6e57748.js","./FormItemCoord-legacy-e6ddc9b7.js","./index-legacy-645a3645.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./validate-legacy-4e8f0db9.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js"],(function(e,a){"use strict";var t,r,l,o,n,m,i,s,u,d,p,f,y,c,v;return{setters:[e=>{t=e.F},e=>{r=e.F},e=>{l=e.F},e=>{o=e.F},e=>{n=e.F},e=>{m=e.F},e=>{i=e.U},e=>{s=e._},e=>{u=e.Q,d=e.R,p=e.X,f=e.V,y=e.k,c=e.Z,v=e.B},null,null,null,null,null,null,null,null,null],execute:function(){e("default",s({name:"UseSealApply",components:{FlowForm:t,FormItemPicker:r,FormItemDate:l,FormItemCalendar:o,FormItemPerson:n,FormItemCoord:m,UploadFiles:i},props:{},data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",entityName:"TechnologySealApplication",formKey:"UseSealApply",modelKey:"seal_application",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/form/commit"},formData:{isconfirm1:!0,sealPaperName:"",sendingType:"",sealNum:"",reason:"",contentAttachment:"",createBy:"",userFullname:"",prjDepName:"",approverUsername1:"",approverFullname1:"",approverUnit1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",approverUsername3:"",approverFullname3:"",approverUnit3:""}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},canEdit0(){return"add"===this.type||"UserTask_0"===this.taskKey}},watch:{},created(){},mounted(){this.initForm()},methods:{async initForm(){"add"!==this.type&&this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:a=[],entityObject:t}=e;this.detailParamList=a,this.formData={...this.formData,...t}}))}))},async onSubmit(){try{var e;await this.$refs.form.validate();const a={...this.formData,taskSubject:null===(e=this.$route.query)||void 0===e?void 0:e.taskKey};this.$bizStore.saveData({SafetyHiddenDanger:{...a}}),this.$refs.FlowForm.onSubmit(this.service.submit,a)}catch(a){console.log(a)}},afterSubmit(e,a){this.updateFiles()},async updateFiles(){return new Promise((async(e,a)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),e()}catch(t){a()}}))},handleSubProjectChange(e={}){this.formData.unitEngineeringId="",this.formData.unitEngineeringName="",this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""}}},[["render",function(e,a,t,r,l,o){const n=u("van-field"),m=u("FormItemPicker"),i=u("UploadFiles"),s=u("FormItemPerson"),D=u("van-radio"),F=u("van-radio-group"),g=u("van-cell-group"),h=u("van-form"),U=u("FlowForm");return d(),p(U,{ref:"FlowForm","model-key":l.modelKey,"form-key":l.formKey,"entity-name":l.entityName,"detail-param-list":l.detailParamList,"detail-entity-name-list":l.detailEntityNameList,onSubmitClick:o.onSubmit,onAfterSubmit:o.afterSubmit},{default:f((()=>[y(h,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:f((()=>[y(g,{border:!1},{default:f((()=>[y(n,{modelValue:l.formData.sealPaperName,"onUpdate:modelValue":a[0]||(a[0]=e=>l.formData.sealPaperName=e),label:"用印文件名称",readonly:"view"===l.type||!o.canEdit0},null,8,["modelValue","readonly"]),y(n,{modelValue:l.formData.sendingType,"onUpdate:modelValue":a[1]||(a[1]=e=>l.formData.sendingType=e),label:"用印类型",readonly:""},null,8,["modelValue"]),l.formData.sealNum?(d(),p(n,{key:0,modelValue:l.formData.sealNum,"onUpdate:modelValue":a[2]||(a[2]=e=>l.formData.sealNum=e),label:"用印份数",readonly:"view"===l.type||!o.canEdit0},null,8,["modelValue","readonly"])):c("",!0),y(m,{label:"子工程",value:l.formData.subProjectId,"onUpdate:value":a[3]||(a[3]=e=>l.formData.subProjectId=e),text:l.formData.subProjectName,"onUpdate:text":a[4]||(a[4]=e=>l.formData.subProjectName=e),columns:[...o.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",rules:[{required:!0,message:"请选择子工程"}],readonly:1!=o.portal.type||"view"===l.type||!o.canEdit0,onChange:o.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),y(n,{label:"事由",modelValue:l.formData.reason,"onUpdate:modelValue":a[5]||(a[5]=e=>l.formData.reason=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入事由",rules:[{required:!0,message:"请输入事由"}],"input-align":"left",readonly:"view"===l.type||!o.canEdit0},null,8,["modelValue","readonly"]),y(n,{label:"附件","label-align":"top","input-align":"left"},{input:f((()=>[y(i,{ref:"beforeFiles",g9s:l.formData.contentAttachment,"onUpdate:g9s":a[6]||(a[6]=e=>l.formData.contentAttachment=e),accept:"image/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===l.type||!o.canEdit0},null,8,["g9s","readonly"])])),_:1}),y(s,{label:"用印人",userFullname:l.formData.userFullname,"onUpdate:userFullname":a[7]||(a[7]=e=>l.formData.userFullname=e),userName:l.formData.createBy,"onUpdate:userName":a[8]||(a[8]=e=>l.formData.createBy=e),title:"选择用印人",rules:[{required:!0,message:"请选择用印人"}],readonly:""},null,8,["userFullname","userName"]),y(n,{modelValue:l.formData.prjDepName,"onUpdate:modelValue":a[9]||(a[9]=e=>l.formData.prjDepName=e),label:"用印人部门",readonly:""},null,8,["modelValue"]),y(n,{name:"radio",label:"是否需要部门负责人审批"},{input:f((()=>[y(F,{modelValue:l.formData.isconfirm1,"onUpdate:modelValue":a[10]||(a[10]=e=>l.formData.isconfirm1=e),direction:"horizontal",disabled:"view"===l.type||!o.canEdit0},{default:f((()=>[y(D,{name:!0},{default:f((()=>a[23]||(a[23]=[v("是")]))),_:1,__:[23]}),y(D,{name:!1},{default:f((()=>a[24]||(a[24]=[v("否")]))),_:1,__:[24]})])),_:1},8,["modelValue","disabled"])])),_:1}),l.formData.isconfirm1?(d(),p(s,{key:1,label:"部门负责人",userName:l.formData.approverUsername1,"onUpdate:userName":a[11]||(a[11]=e=>l.formData.approverUsername1=e),userFullname:l.formData.approverFullname1,"onUpdate:userFullname":a[12]||(a[12]=e=>l.formData.approverFullname1=e),deptName:l.formData.approverUnit1,"onUpdate:deptName":a[13]||(a[13]=e=>l.formData.approverUnit1=e),title:"选择部门负责人",rules:[{required:!0,message:"请选择部门负责人"}],readonly:"view"===l.type||!o.canEdit0},null,8,["userName","userFullname","deptName","readonly"])):c("",!0),l.formData.isconfirm1?(d(),p(n,{key:2,modelValue:l.formData.approverUnit1,"onUpdate:modelValue":a[14]||(a[14]=e=>l.formData.approverUnit1=e),label:"部门负责人部门",readonly:!0},null,8,["modelValue"])):c("",!0),y(s,{label:"项目负责人",userName:l.formData.approverUsername2,"onUpdate:userName":a[15]||(a[15]=e=>l.formData.approverUsername2=e),userFullname:l.formData.approverFullname2,"onUpdate:userFullname":a[16]||(a[16]=e=>l.formData.approverFullname2=e),deptName:l.formData.approverUnit2,"onUpdate:deptName":a[17]||(a[17]=e=>l.formData.approverUnit2=e),title:"选择项目负责人",rules:[{required:!0,message:"请选择项目负责人"}],readonly:"view"===l.type||!o.canEdit0},null,8,["userName","userFullname","deptName","readonly"]),y(n,{modelValue:l.formData.approverUnit2,"onUpdate:modelValue":a[18]||(a[18]=e=>l.formData.approverUnit2=e),label:"项目负责人部门",readonly:!0},null,8,["modelValue"]),y(s,{label:"经办人",userName:l.formData.approverFullname3,"onUpdate:userName":a[19]||(a[19]=e=>l.formData.approverFullname3=e),userFullname:l.formData.approverFullname3,"onUpdate:userFullname":a[20]||(a[20]=e=>l.formData.approverFullname3=e),deptName:l.formData.approverUnit3,"onUpdate:deptName":a[21]||(a[21]=e=>l.formData.approverUnit3=e),title:"选择经办人",rules:[{required:!0,message:"请选择经办人"}],readonly:"view"===l.type||!o.canEdit0},null,8,["userName","userFullname","deptName","readonly"]),y(n,{modelValue:l.formData.approverUnit3,"onUpdate:modelValue":a[22]||(a[22]=e=>l.formData.approverUnit3=e),label:"经办人部门",readonly:!0},null,8,["modelValue"])])),_:1})])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onSubmitClick","onAfterSubmit"])}]]))}}}));
