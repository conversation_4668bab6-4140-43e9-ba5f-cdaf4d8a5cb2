System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var t,l,n,o,s,d,m,r,i,p;return{setters:[e=>{t=e.F,l=e.D},e=>{n=e._},e=>{o=e.Q,s=e.R,d=e.X,m=e.V,r=e.k,i=e.U,p=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"comment-wp"},u={class:"textarea-wp"},c={class:"comment-wp"},f={class:"textarea-wp"},b={class:"comment-wp"},y={class:"textarea-wp"},g={class:"footer-input"},v={class:"form-info"},j={class:"form-info"},D={class:"form-info"};e("default",n({name:"JL09",components:{FormTemplate:t,DocumentPart:l},emits:[],props:{},setup(e,{attrs:a,slots:t,emit:l}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit:({formData:e,detailParamList:a},t)=>new Promise(((e,a)=>{try{e()}catch(t){a(t)}}))}},[["render",function(e,t,l,n,V,h){const w=o("van-field"),x=o("DocumentPart"),k=o("FormTemplate");return s(),d(k,{ref:"FormTemplate",nature:"现指","on-after-init":h.onAfterInit,"on-before-submit":h.onBeforeSubmit,"detail-table":V.detailTable,"is-show-confirm1":!1,attachmentDesc:V.attachmentDesc},{default:m((({formData:e,formTable:l,baseObj:n,uploadAccept:o,taskStart:s,taskComment2:d,taskComment3:p,taskComment4:g})=>[r(x,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"监理工程师/监理员：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",a,[t[0]||(t[0]=i("div",null,"事由：",-1)),i("div",u,[r(w,{modelValue:e.field1,"onUpdate:modelValue":a=>e.field1=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),i("div",c,[t[1]||(t[1]=i("div",null,"通知内容：",-1)),i("div",f,[r(w,{modelValue:e.field2,"onUpdate:modelValue":a=>e.field2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),r(x,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"现场负责人：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",b,[t[2]||(t[2]=i("div",null,"承包人意见：",-1)),i("div",y,[r(w,{modelValue:e.comment3,"onUpdate:modelValue":a=>e.comment3=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:m((({formData:e,formTable:a,baseObj:l,uploadAccept:n,taskStart:o,taskComment2:s,taskComment3:d,taskComment4:m})=>[i("div",g,[t[3]||(t[3]=i("span",null,"说明：1、本通知一式",-1)),i("span",v,p(e.num1),1),t[4]||(t[4]=i("span",null,"份，由监理机构填写，承包人签署意见后，承包人",-1)),i("span",j,p(e.num2),1),t[5]||(t[5]=i("span",null,"份，监理机构",-1)),i("span",D,p(e.num3),1),t[6]||(t[6]=i("span",null,"份。",-1))]),t[7]||(t[7]=i("div",{class:"footer-input"},[i("span",{style:{"text-indent":"3em"}},"2、本表一般情况下应由监理工程师签发；对现场发现的施工人员违反操作规程的行为，监理员可以签发。")],-1))])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
