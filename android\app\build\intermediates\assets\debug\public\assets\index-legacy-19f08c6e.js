System.register(["./api-legacy-39a4c48a.js","./ListItem-legacy-3ca9eb8f.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./sift-legacy-1dc85988.js","./FormItemPicker-legacy-fd45c24d.js","./vant-legacy-b51a9379.js"],(function(e,t){"use strict";var a,s,i,n,o,l,r,h,c,d,p,u,m,v,g,f,y,b,_;return{setters:[e=>{a=e.g},e=>{s=e.L},e=>{i=e._,n=e.u},e=>{o=e.Q,l=e.R,r=e.X,h=e.V,c=e.k,d=e.S,p=e.F,u=e.W,m=e.Z,v=e.a2,g=e.U,f=e.B,y=e.y},e=>{b=e.S},e=>{_=e.F},null],execute:function(){var t=document.createElement("style");t.textContent=".page[data-v-6e43433e]{height:100%}.view-height[data-v-a37c72ca]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height) - var(--sab));padding-top:2.66667vw;overflow-x:hidden;overflow-y:scroll}.view-height.no-tabbar[data-v-a37c72ca]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--sab) - 12vw)}.tabs-wp[data-v-a37c72ca]{border-bottom:1px solid #f8f8f8}.btn-group[data-v-a37c72ca]{width:100%;padding:0 4vw;box-sizing:border-box;display:flex;justify-content:space-between;gap:0 4vw;margin:2.66667vw 0}.btn-group>button[data-v-a37c72ca]{flex:1}\n",document.head.appendChild(t);const w={key:0,class:"p-[10px]"},L=i({name:"List",components:{ListItem:s},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}),computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams,...this.search};1===e.page&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await a(e),s=this.searchParams.page<=1?[]:this.list||[];this.list.length>=t.total&&(this.finished=!0),this.list=[...s,...t.records],this.searchParams.page++}catch(e){this.finished=!0}finally{setTimeout((()=>{this.loading=!1,this.$closeToast()}),100)}}}},[["render",function(e,t,a,s,i,n){const v=o("ListItem"),g=o("van-empty"),f=o("van-list"),y=o("van-pull-refresh");return l(),r(y,{modelValue:i.refreshing,"onUpdate:modelValue":t[1]||(t[1]=e=>i.refreshing=e),onRefresh:n.onRefresh},{default:h((()=>[c(f,{loading:i.loading,"onUpdate:loading":t[0]||(t[0]=e=>i.loading=e),finished:i.finished,"finished-text":i.list&&i.list.length?"没有更多了":"",onLoad:n.onLoadList,"immediate-check":!1},{default:h((()=>[i.list&&i.list.length?(l(!0),d(p,{key:0},u(i.list||[],((e,t)=>(l(),r(v,{key:e.id,item:e,tabName:"质量检查",onDelSuccess:n.onRefresh},null,8,["item","onDelSuccess"])))),128)):(l(),d(p,{key:1},[i.loading?m("",!0):(l(),d("div",w,[c(g,{description:"暂无数据"})]))],64))])),_:1},8,["loading","finished","finished-text","onLoad"])])),_:1},8,["modelValue","onRefresh"])}],["__scopeId","data-v-6e43433e"]]),I=n(),T={class:"btn-group"};e("default",i({name:"QualityInspection",components:{FormItemPicker:_,List:L},props:{},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({search:{hazardNumber:"",sectionId:"",inspectionType:"",inspectionResult:""},Sift:b,showTop:!1}),mounted(){},methods:{handleAdd(){I.QUALITY_INSPECTION={},this.$router.push({path:"/QualityInspectionDetail",query:{type:"add",title:"新增质量检查"}})},handleQuery(){this.showTop=!1,this.$nextTick((()=>{this.$refs.List&&this.$refs.List.onRefresh(this.search)}))},handleResetting(){this.search={inspectionNumber:"",sectionId:"",inspectionType:"",inspectionResult:""},this.handleQuery()}}},[["render",function(e,t,a,s,i,n){const r=o("van-icon"),u=o("Navbar"),m=o("FormItemPicker"),b=o("van-button"),_=o("van-popup"),w=o("List");return l(),d(p,null,[c(u,{back:""},{right:h((()=>[c(r,{name:i.Sift,size:"2em",onClick:t[0]||(t[0]=v((e=>i.showTop=!i.showTop),["stop","prevent"]))},null,8,["name"])])),_:1}),c(_,{show:i.showTop,"onUpdate:show":t[4]||(t[4]=e=>i.showTop=e),position:"top"},{default:h((()=>[c(m,{value:i.search.sectionId,"onUpdate:value":t[1]||(t[1]=e=>i.search.sectionId=e),"dict-name":e.$DICT_CODE.project_section,placeholder:"选择所属标段"},null,8,["value","dict-name"]),c(m,{value:i.search.inspectionType,"onUpdate:value":t[2]||(t[2]=e=>i.search.inspectionType=e),"dict-name":e.$DICT_CODE.safe_inspection_type,placeholder:"选择检查类型"},null,8,["value","dict-name"]),c(m,{value:i.search.inspectionResult,"onUpdate:value":t[3]||(t[3]=e=>i.search.inspectionResult=e),"dict-name":e.$DICT_CODE.safe_inspection_result,placeholder:"选择检查结果"},null,8,["value","dict-name"]),g("div",T,[c(b,{round:"",type:"primary",plain:"",onClick:v(n.handleQuery,["stop","prevent"])},{default:h((()=>t[6]||(t[6]=[f("查询")]))),_:1,__:[6]},8,["onClick"]),c(b,{round:"",plain:"",onClick:v(n.handleResetting,["stop","prevent"])},{default:h((()=>t[7]||(t[7]=[f("重置")]))),_:1,__:[7]},8,["onClick"])])])),_:1},8,["show"]),g("div",{class:y(["view-height",{"no-tabbar":e.envFeishu}])},[c(w,{ref:"List",search:i.search},null,8,["search"])],2),c(b,{type:"primary",size:"normal",style:{width:"100%"},onClick:t[5]||(t[5]=e=>n.handleAdd())},{default:h((()=>t[8]||(t[8]=[f("新增检查")]))),_:1,__:[8]})],64)}],["__scopeId","data-v-a37c72ca"]]))}}}));
