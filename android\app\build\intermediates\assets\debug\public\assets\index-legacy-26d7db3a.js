System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(l,e){"use strict";var s,t,a,n,c,d,i,u,o,p,r;return{setters:[l=>{s=l.F,t=l.D},l=>{a=l._},l=>{n=l.Q,c=l.R,d=l.X,i=l.V,u=l.U,o=l.Y,p=l.k,r=l.B},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const e={class:"jl-table"},f={colspan:"3"},v={class:"cell"},m={class:"cell"},h={class:"cell"},y={class:"cell"},b={class:"cell"},g={colspan:"5"},w={class:"cell"},j={class:"one-line"},x={class:"one-line"},k={class:"one-line"},D={class:"cell"},T={class:"one-line"},C={colspan:"2"},F={class:"cell"},P={class:"one-line"},_={class:"cell"},S={class:"one-line"},A={colspan:"2"},I={class:"cell"},B={class:"one-line"},L={class:"cell"},O={class:"one-line"},R={colspan:"2"},z={class:"cell"},J={class:"one-line"},K={colspan:"5"},Q={class:"cell"},U={colspan:"5"},V={class:"cell"},X={colspan:"5"},Y={class:"cell"},q={colspan:"5"},E={class:"cell"},G={colspan:"5"},H={class:"cell"},M={colspan:"5"},N={class:"cell"},W={colspan:"6"},Z={class:"cell"},$={class:"one-line"};l("default",a({name:"JL26",components:{FormTemplate:s,DocumentPart:t},emits:[],props:{},setup(l,{attrs:e,slots:s,emit:t}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:l,detailParamList:e}){},onBeforeSubmit:({formData:l,detailParamList:e},s)=>new Promise(((l,e)=>{try{l()}catch(s){e(s)}}))}},[["render",function(l,s,t,a,ll,el){const sl=n("van-col"),tl=n("van-row"),al=n("FormTemplate");return c(),d(al,{ref:"FormTemplate",nature:"旁站","on-after-init":el.onAfterInit,"on-before-submit":el.onBeforeSubmit,"detail-table":ll.detailTable,"is-show-confirm1":!1,"show-target":!1,"hide-border":"",attachmentDesc:ll.attachmentDesc},{default:i((({formData:l,formTable:t,baseObj:a,uploadAccept:n,taskStart:c,taskComment2:d,taskComment3:ll,taskComment4:el})=>[u("table",e,[u("tbody",null,[u("tr",null,[s[0]||(s[0]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"工程部位")],-1)),u("td",f,[u("div",v,o(l.field1),1)]),s[1]||(s[1]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"日期")],-1)),u("td",null,[u("div",m,o(l.field2),1)])]),u("tr",null,[s[2]||(s[2]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"时 间")],-1)),u("td",null,[u("div",h,o(l.field3),1)]),s[3]||(s[3]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"天气")],-1)),u("td",null,[u("div",y,o(l.field4),1)]),s[4]||(s[4]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"温度")],-1)),u("td",null,[u("div",b,o(l.field5),1)])]),u("tr",null,[s[8]||(s[8]=u("th",{style:{width:"80px"},rowspan:"5"},[u("div",{class:"cell"},"人员情况")],-1)),u("td",g,[u("div",w,[p(tl,null,{default:i((()=>[p(sl,{span:12},{default:i((()=>[u("div",j,[s[5]||(s[5]=u("span",null,"施工技术员：",-1)),u("span",null,o(l.field6),1)])])),_:2},1024),p(sl,{span:12},{default:i((()=>[u("div",x,[s[6]||(s[6]=u("span",null,"施工班组长：",-1)),u("span",null,o(l.field7),1)])])),_:2},1024),p(sl,{span:12},{default:i((()=>[u("div",k,[s[7]||(s[7]=u("span",null,"质检员：",-1)),u("span",null,o(l.field8),1)])])),_:2},1024)])),_:2},1024)])])]),s[29]||(s[29]=u("tr",null,[u("th",{colspan:"5"},[u("div",{class:"cell"},"现场人员数量及分类人员数量")])],-1)),u("tr",null,[s[11]||(s[11]=u("th",null,[u("div",{class:"cell"},"管理人员")],-1)),u("td",null,[u("div",D,[u("div",T,[r(o(l.field9)+" ",1),s[9]||(s[9]=u("span",null,"人",-1))])])]),s[12]||(s[12]=u("th",null,[u("div",{class:"cell"},"技术人员")],-1)),u("td",C,[u("div",F,[u("div",P,[r(o(l.field10)+" ",1),s[10]||(s[10]=u("span",null,"人",-1))])])])]),u("tr",null,[s[15]||(s[15]=u("th",null,[u("div",{class:"cell"},"特种作业人员")],-1)),u("td",null,[u("div",_,[u("div",S,[u("span",null,o(l.field11),1),s[13]||(s[13]=u("span",null,"人",-1))])])]),s[16]||(s[16]=u("th",null,[u("div",{class:"cell"},"普通作业人员")],-1)),u("td",A,[u("div",I,[u("div",B,[u("span",null,o(l.field12),1),s[14]||(s[14]=u("span",null,"人",-1))])])])]),u("tr",null,[s[19]||(s[19]=u("th",null,[u("div",{class:"cell"},"其他辅助人员")],-1)),u("td",null,[u("div",L,[u("div",O,[u("span",null,o(l.field13),1),s[17]||(s[17]=u("span",null,"人",-1))])])]),s[20]||(s[20]=u("th",null,[u("div",{class:"cell"},"合计")],-1)),u("td",R,[u("div",z,[u("div",J,[u("span",null,o(l.field14),1),s[18]||(s[18]=u("span",null,"人",-1))])])])]),u("tr",null,[s[21]||(s[21]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"主要施工设备及运转情况")],-1)),u("td",K,[u("div",Q,[u("span",null,o(l.field15),1)])])]),u("tr",null,[s[22]||(s[22]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"主要材料使用情况")],-1)),u("td",U,[u("div",V,[u("span",null,o(l.field16),1)])])]),u("tr",null,[s[23]||(s[23]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"施工过程描述")],-1)),u("td",X,[u("div",Y,[u("span",null,o(l.field17),1)])])]),u("tr",null,[s[24]||(s[24]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"监理现场检查、检测情况")],-1)),u("td",q,[u("div",E,[u("span",null,o(l.field18),1)])])]),u("tr",null,[s[25]||(s[25]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"承包人提出的问题")],-1)),u("td",G,[u("div",H,[u("span",null,o(l.field19),1)])])]),u("tr",null,[s[26]||(s[26]=u("th",{style:{width:"80px"}},[u("div",{class:"cell"},"监理人答复或指示")],-1)),u("td",M,[u("div",N,[u("span",null,o(l.field20),1)])])]),u("tr",null,[u("td",W,[u("div",Z,[u("div",$,[s[27]||(s[27]=u("span",null,"当班监理员：（签名）",-1)),u("span",null,o(l.field21),1),s[28]||(s[28]=u("span",null,"施工技术员：（签名）",-1)),u("span",null,o(l.field22),1)])])])])])])])),footer:i((({formData:l,formTable:e,baseObj:t,uploadAccept:a,taskStart:n,taskComment2:c,taskComment3:d,taskComment4:i})=>s[30]||(s[30]=[u("div",{class:"footer-input"},[u("span",null,"说明：本表单独汇编成册。")],-1)]))),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
