System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(l,e){"use strict";var t,a,n,s,c,d,o,i,r,u,m,p,f;return{setters:[l=>{t=l.F,a=l.D},l=>{n=l._},l=>{s=l.Q,c=l.R,d=l.X,o=l.V,i=l.U,r=l.Y,u=l.S,m=l.W,p=l.F,f=l.k},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const e={class:"jl-table"},y={colspan:"3"},g={class:"cell"},v={colspan:"3"},h={class:"cell"},b={colspan:"3"},j={class:"cell"},k={class:"cell"},w={colspan:"4"},D={class:"cell"};l("default",n({name:"JL35",components:{FormTemplate:t,DocumentPart:a},emits:[],props:{},setup(l,{attrs:e,slots:t,emit:a}){},data:()=>({detailTable:[{},{},{},{},{},{},{}],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:l,detailParamList:e}){},onBeforeSubmit:({formData:l,detailParamList:e,taskStart:t},a)=>new Promise(((l,e)=>{try{l()}catch(t){e(t)}}))}},[["render",function(l,t,a,n,x,F){const S=s("van-field"),T=s("FormTemplate");return c(),d(T,{ref:"FormTemplate",nature:"内签","on-after-init":F.onAfterInit,"on-before-submit":F.onBeforeSubmit,"detail-table":x.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:x.attachmentDesc},{default:o((({formData:l,formTable:a,baseObj:n,uploadAccept:s,taskStart:d,taskComment2:o,taskComment3:x,taskComment4:F})=>[i("table",e,[t[6]||(t[6]=i("colgroup",null,[i("col",{width:"50"}),i("col",{"min-width":"50"}),i("col",{width:"50"}),i("col",{width:"50"})],-1)),i("tbody",null,[i("tr",null,[t[0]||(t[0]=i("th",null,[i("div",{class:"cell"},"事由")],-1)),i("td",y,[i("div",g,[i("span",null,r(l.field1),1)])])]),i("tr",null,[t[1]||(t[1]=i("th",null,[i("div",{class:"cell"},"会签内容")],-1)),i("td",v,[i("div",h,[i("span",null,r(l.field2),1)])])]),i("tr",null,[t[2]||(t[2]=i("th",null,[i("div",{class:"cell"},"依据、参考文件")],-1)),i("td",b,[i("div",j,[i("span",null,r(l.field3),1)])])]),t[5]||(t[5]=i("tr",null,[i("th",null,[i("div",{class:"cell"},"会签部门")]),i("th",null,[i("div",{class:"cell"},"部门意见")]),i("th",null,[i("div",{class:"cell"},"负责人签名")]),i("th",null,[i("div",{class:"cell"},"日期")])],-1)),(c(!0),u(p,null,m(a,((l,e)=>(c(),u("tr",{key:e},[(c(),u(p,null,m(4,(t=>i("td",{key:`${e}_${t}`},[i("div",k,[i("span",null,r(l[`field${t}`]),1)])]))),64))])))),128)),i("tr",null,[i("td",w,[i("div",D,[t[3]||(t[3]=i("div",{style:{"padding-bottom":"10px"}},"会签意见：",-1)),f(S,{modelValue:l.field4,"onUpdate:modelValue":e=>l.field4=e,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"]),t[4]||(t[4]=i("div",{class:"part-div"},[i("div",{class:"part-sign",style:{"padding-left":"100px"}},[i("div",null,[i("span",null,"总监理工程师："),i("span",null,"（签名）")]),i("div",null,[i("span",null,"日期："),i("span",{style:{"padding-left":"2em"}},"年"),i("span",{style:{"padding-left":"2em"}},"月"),i("span",{style:{"padding-left":"2em"}},"日")])])],-1))])])])])])])),footer:o((({formData:l,formTable:e,baseObj:a,uploadAccept:n,taskStart:s,taskComment2:c,taskComment3:d,taskComment4:o})=>t[7]||(t[7]=[i("div",{class:"footer-input"},[i("div",null,"说明：在监理机构作出决定之前需内部会签时，可用此表。")],-1)]))),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
