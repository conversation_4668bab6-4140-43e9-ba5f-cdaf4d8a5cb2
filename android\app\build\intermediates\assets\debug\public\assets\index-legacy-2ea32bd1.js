System.register(["./index-legacy-09188690.js","./index-legacy-b580af71.js","./FormItemPicker-legacy-fd45c24d.js","./FormItemDate-legacy-c4422d42.js","./FormItemCalendar-legacy-ea787ea1.js","./FormItemCascader-legacy-b386877e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemCoord-legacy-e6ddc9b7.js","./index-legacy-645a3645.js","./common-legacy-e7aae0fd.js","./verder-legacy-e6127216.js","./vant-legacy-b51a9379.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./array-legacy-2920c097.js","./validate-legacy-4e8f0db9.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js"],(function(e,a){"use strict";var t,r,i,o,n,d,l,s,m,u,c,f,h,p,D,g,y,v,k,C,b,F,w,N,S,U;return{setters:[e=>{t=e.h,r=e._},e=>{i=e.F},e=>{o=e.F},e=>{n=e.F},e=>{d=e.F},e=>{l=e.F},e=>{s=e.F},e=>{m=e.F},e=>{u=e.U},e=>{c=e.a},e=>{f=e.Q,h=e.R,p=e.S,D=e.k,g=e.a7,y=e.V,v=e.B,k=e.U,C=e.Y,b=e.F,F=e.W,w=e.X,N=e.y,S=e.Z,U=e.a2},null,null,null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".pop-window[data-v-a0b20229]{position:relative;box-sizing:border-box}.pop-window-header[data-v-a0b20229]{position:relative;display:flex;align-items:center;justify-content:space-between;height:var(--van-picker-toolbar-height)}.pop-window-body .list-content[data-v-a0b20229]{padding:2.66667vw 0;min-height:77.86667vw;max-height:50vh;box-sizing:border-box;overflow-y:auto}.person-cell.select[data-v-a0b20229]{background-color:var(--primary-color);color:#fff}.person-cell.select[data-v-a0b20229] .van-cell__value{color:#f7f7f7}.person-cell[data-v-a0b20229] .van-cell__title{flex:initial}.person-cell[data-v-a0b20229] .van-cell__value{flex:1;text-align:right}.pop-window-footer[data-v-a0b20229]{padding-left:var(--van-padding-md);padding-right:var(--van-padding-md);background-color:#fff}.pop-window-footer .confirm-button[data-v-a0b20229]{margin:2.66667vw 0}\n",document.head.appendChild(a);const _={name:"FormItemRiskSource",components:{},emits:["update:riskSource","update:riskSourceName","change"],props:{riskSource:[String,Number],riskSourceName:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:Boolean,readonly:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},searchData:{type:Object,default:()=>({})}},setup(e,{attrs:a,slots:t,emit:r}){},data:()=>({showPicker:!1,loading:!1,finished:!1,list:[],searchParams:{pageNo:1,pageSize:200,isMobile:!0,searchValue:"",accountStatus:"1"},loaded:!1,user:{}}),computed:{portal(){return this.$store.PORTAL},selectedUser(){return this.user&&this.user.id?{...this.user}:{...this.list.find((e=>e.id==this.riskSource||e.dangerName===this.riskSourceName))}}},watch:{"searchParams.searchValue"(e){""===e&&this.onSearch(e)}},created(){},mounted(){},methods:{onSearch(e){this.finished=!1,this.searchParams.pageNo=1,this.$nextTick((()=>{this.onLoadList()}))},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchData,...this.searchParams};1===e.pageNo&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const a=await function(e={}){return e.isAsc||e.orderByColumn||(e.isAsc="desc",e.orderByColumn="id"),t({url:"/cybereng-safety/risk/identification/data/list",method:"get",params:e})}(e),r=this.searchParams.pageNo<=1?[]:this.list||[];this.list=[...r,...a.list],this.searchParams.pageNo++,this.list.length>=a.total&&(this.finished=!0)}catch(e){console.log(e),this.finished=!0}finally{setTimeout((()=>{this.loading=!1,this.loaded=!0,this.$closeToast()}),100)}},onShowPicker(){this.readonly||(this.handleSelect({}),this.showPicker=!0,this.finished=!1,this.searchParams.pageNo=1,this.loaded||this.onSearch())},onClosePicker(){this.showPicker=!1},handleSelect(e){this.user=e},onClear(){this.readonly||(this.$emit("update:riskSource",""),this.$emit("update:riskSourceName",""))},onSelectConfirm(){const{id:e="",dangerName:a=""}=this.selectedUser||{};this.$emit("update:riskSource",e),this.$emit("update:riskSourceName",a),this.$emit("change",this.selectedUser),this.onClosePicker()}}},x={class:"pop-window"},j={class:"pop-window-header"},I={class:"van-picker__title van-ellipsis"},P={class:"pop-window-body"},R={class:"list-content"},E={key:0,class:"p-[10px]"},q={class:"pop-window-footer van-safe-area-bottom"};e("default",r({name:"SafetyHiddenDangerNew",components:{FlowForm:i,FormItemPicker:o,FormItemDate:n,FormItemCalendar:d,FormItemPerson:s,FormItemCoord:m,UploadFiles:u,FormItemCascader:l,FormItemRiskSource:r(_,[["render",function(e,a,t,r,i,o){const n=f("van-button"),d=f("van-field"),l=f("van-cell"),s=f("van-empty"),m=f("van-list"),u=f("van-popup");return h(),p(b,null,[D(d,{name:t.name,"model-value":t.riskSourceName,label:t.label,required:t.required,rules:t.rules,"input-align":t.inputAlign,"error-message-align":t.errorMessageAlign,"label-width":t.labelWidth,center:"",clearable:"",readonly:"",placeholder:"请选择","right-icon":t.readonly?"":"close",onClickRightIcon:o.onClear},g({_:2},[t.readonly?void 0:{name:"button",fn:y((()=>[D(n,{size:"small",type:"primary",onClick:a[0]||(a[0]=e=>o.onShowPicker())},{default:y((()=>a[4]||(a[4]=[v("选择风险")]))),_:1,__:[4]})])),key:"0"}]),1032,["name","model-value","label","required","rules","input-align","error-message-align","label-width","right-icon","onClickRightIcon"]),D(u,{show:i.showPicker,"onUpdate:show":a[3]||(a[3]=e=>i.showPicker=e),position:"bottom",closeable:"",teleport:"#app"},{default:y((()=>[k("div",x,[k("div",j,[k("div",I,C(t.title),1)]),k("div",P,[k("div",R,[D(m,{loading:i.loading,"onUpdate:loading":a[1]||(a[1]=e=>i.loading=e),finished:i.finished,"finished-text":i.list&&i.list.length?"没有更多了":"",onLoad:o.onLoadList,"immediate-check":!1},{default:y((()=>[i.list&&i.list.length?(h(!0),p(b,{key:0},F(i.list,(e=>(h(),w(l,{class:N(["person-cell",{select:o.selectedUser.id===e.id}]),key:e.id,title:e.dangerName,value:e.position,onClick:a=>o.handleSelect(e)},null,8,["class","title","value","onClick"])))),128)):(h(),p(b,{key:1},[i.loading?S("",!0):(h(),p("div",E,[D(s,{description:"暂无更多数据"})]))],64))])),_:1},8,["loading","finished","finished-text","onLoad"])])]),k("div",q,[D(n,{class:"confirm-button",round:"",block:"",type:"primary",onClick:a[2]||(a[2]=U((e=>o.onSelectConfirm()),["stop","prevent"]))},{default:y((()=>a[5]||(a[5]=[v(" 确定 ")]))),_:1,__:[5]})])])])),_:1},8,["show"])],64)}],["__scopeId","data-v-a0b20229"]])},props:{},emits:[],setup(e,{attrs:a,slots:t,emit:r}){},data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",entityName:"SafetyHiddenDanger",formKey:"SafetyHiddenDangerNew",modelKey:"safety_hidden_danger_flow_new",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-safety/safety/form/query",submit:"/cybereng-safety/form/commit"},formData:{type:"",portalId:"",subProjectId:"",subProjectName:"",workAreaId:"",workAreaName:"",overdueState:"",hiddenDangerName:"",hiddenDangerCode:"",rectifyState:"",hiddenDangerLevel:"",hiddenDangerSource:"",rectifyDate:"",hiddenDangerCategory:"",hiddenDangerContent:"",beforeFileToken:"",beforeFileTokenImage:[],beforeFiles:1,constructionPost:"",longitude:"",latitude:"",riskSource:"",riskSourceName:"",createBy:"",hiddenDangerReportorFullname:"",prjDepName:"",prjDepCode:"",otherReportorFullname:"",initiateDate:"",isDangerConfirm:!1,isSupervision:!1,hiddenDangerConfirmer:"",hiddenDangerConfirmerFullname:"",hiddenDangerConfirmerDeptName:"",hiddenDangerConfirmerDeptCode:"",hiddenDangerRectifier:"",hiddenDangerRectifierFullname:"",hiddenDangerRectifierDeptName:"",hiddenDangerRectifierDeptCode:"",hiddenDangerChecker:"",hiddenDangerCheckerFullname:"",hiddenDangerCheckerDeptName:"",hiddenDangerCheckerDeptCode:"",hiddenDangerSupervisor:"",hiddenDangerSupervisorFullname:"",hiddenDangerSupervisorDeptName:"",hiddenDangerSupervisorDeptCode:"",hiddenDangerRectifyApprover:"",hiddenDangerRectifyApproverFullname:"",hiddenDangerRectifyApproverDeptName:"",hiddenDangerRectifyApproverDeptCode:"",rectifyComment:"",dispatchingCompany:"",rectifyMeasures:"",rectifySituation:"",afterFileToken:"",afterFileTokenImage:[],afterFiles:1,finishDate:"",safetyCheckId:""},hiddenDangerSourceList:[]}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},siteList(){let e=this.$store.SITE_LIST.find((e=>e.slots.id==this.formData.subProjectId)),a=[];return e&&(a=e.children.map((e=>({text:e.title,value:e.slots.id})))),a},canEdit0(){return"add"===this.type||"UserTask_0"===this.taskKey},canEdit2(){return"execute"===this.type&&"UserTask_2"===this.taskKey},riskSourceSearch(){return{subProjectId:this.formData.subProjectId}},minDate(){const e=(new Date).getFullYear();return new Date(e-1,0,1)},maxDate(){const e=(new Date).getFullYear();return new Date(e+2,0,1)}},watch:{},created(){this.getHiddenLevelList()},mounted(){this.initForm()},methods:{copyCallBack(e){this.formData={...e},["hiddenDangerContent","beforeFileToken","riskSource","riskSourceName","rectifyComment","dispatchingCompany","rectifyMeasures","rectifySituation","afterFileToken","latitude","longitude"].forEach((e=>{this.formData[e]=""}))},async initForm(){if("add"===this.type){if(this.formData.portalId=this.portalId,1!=this.portal.type){const e=this.subProjectList.find((e=>e.portalId==this.portal.id));this.formData.subProjectId=null==e?void 0:e.id,this.formData.subProjectName=null==e?void 0:e.nodeName}const{userName:e="",userFullname:a="",orgList:t=[]}=this.user||{},r=t.find((e=>{var a;return e.portalId==(null===(a=this.portal)||void 0===a?void 0:a.id)}))||t[0],i=(null==r?void 0:r.name)||"",o=(null==r?void 0:r.orgNo)||"";this.formData.createBy=e,this.formData.hiddenDangerReportorFullname=a,this.formData.prjDepName=i,this.formData.prjDepCode=o,this.formData.hiddenDangerSupervisor=e,this.formData.hiddenDangerSupervisorFullname=a,this.formData.hiddenDangerSupervisorDeptName=i,this.formData.hiddenDangerSupervisorDeptCode=o}else this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:a=[],entityObject:t}=e;if(this.detailParamList=a,this.formData={...this.formData,...t},t.constructionPost){let e=t.constructionPost.split(",");this.formData.longitude=e[0],this.formData.latitude=e[1]}})),this.canEdit2&&setTimeout((()=>{document.getElementById("hidden-rectify").scrollIntoView()}),500)}))},setHiddenDangerChecker(){this.formData.hiddenDangerChecker=this.formData.hiddenDangerConfirmer,this.formData.hiddenDangerCheckerFullname=this.formData.hiddenDangerConfirmerFullname,this.formData.hiddenDangerCheckerDeptName=this.formData.hiddenDangerConfirmerDeptName,this.formData.hiddenDangerCheckerDeptCode=this.formData.hiddenDangerConfirmerDeptCode},async onDraft(){try{const e={...this.formData};this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{await this.$refs.form.validate(),this.formData.constructionPost=this.formData.longitude&&this.formData.latitude?[this.formData.longitude,this.formData.latitude].join(","):"";const e={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},async updateFiles(){return new Promise((async(e,a)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),e()}catch(t){a()}}))},afterSubmit(e,a){this.updateFiles(),"submit"===e&&("UserTask_1"==this.taskKey&&function(e){t({url:"/cybereng-safety/safety/hiddenDanger/delayTask",method:"get",params:e})}({id:this.formData.id}),"UserTask_1"!=this.taskKey&&"UserTask_2"!=this.taskKey||function(e){t({url:"/cybereng-safety/safety/hiddenDanger/sendMessage",method:"get",params:e})}({id:this.formData.id}),this.formData.id&&function(e){t({url:"/cybereng-safety/yxst/safety/hiddenDanger",method:"put",params:e})}({id:this.formData.id}))},handleSubProjectChange(e={}){this.formData.workAreaId="",this.formData.workAreaName="",this.formData.riskSource="",this.formData.riskSourceName=""},onDangerConfirmChange(e){e||(this.formData.hiddenDangerConfirmer="",this.formData.hiddenDangerConfirmerFullname="",this.formData.hiddenDangerConfirmerDeptName="",this.formData.hiddenDangerConfirmerDeptCode="",this.formData.hiddenDangerChecker="",this.formData.hiddenDangerCheckerFullname="",this.formData.hiddenDangerCheckerDeptName="",this.formData.hiddenDangerCheckerDeptCode="")},onSupervisionChange(e){e||(this.formData.hiddenDangerRectifyApprover="",this.formData.hiddenDangerRectifyApproverFullname="",this.formData.hiddenDangerRectifyApproverDeptName="",this.formData.hiddenDangerRectifyApproverDeptCode="")},async getHiddenLevelList(){try{let e=await c({refDirectoryTreeName:"检查表单"});this.hiddenDangerSourceList=e[0].children||[]}catch(e){this.hiddenDangerSourceList=[]}}}},[["render",function(e,a,t,r,i,o){const n=f("van-field"),d=f("FormItemPicker"),l=f("FormItemCascader"),s=f("FormItemCalendar"),m=f("FormItemCoord"),u=f("form-item-risk-source"),c=f("van-cell-group"),g=f("UploadFiles"),C=f("FormItemPerson"),F=f("van-radio"),N=f("van-radio-group"),U=f("van-form"),_=f("FlowForm");return h(),w(_,{ref:"FlowForm","model-key":i.modelKey,"form-key":i.formKey,"entity-name":i.entityName,"detail-param-list":i.detailParamList,"detail-entity-name-list":i.detailEntityNameList,onDraftClick:o.onDraft,onSubmitClick:o.onSubmit,onAfterSubmit:o.afterSubmit,onCopyCallBack:o.copyCallBack},{default:y((()=>[D(U,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:y((()=>[D(c,{border:!1},{default:y((()=>["view"===i.type?(h(),p(b,{key:0},[i.formData.hiddenDangerName?(h(),w(n,{key:0,modelValue:i.formData.hiddenDangerName,"onUpdate:modelValue":a[0]||(a[0]=e=>i.formData.hiddenDangerName=e),label:"隐患名称",placeholder:"自动生成(上报人+隐患闭合流程)",readonly:""},null,8,["modelValue"])):S("",!0),i.formData.hiddenDangerCode?(h(),w(n,{key:1,modelValue:i.formData.hiddenDangerCode,"onUpdate:modelValue":a[1]||(a[1]=e=>i.formData.hiddenDangerCode=e),label:"隐患编号",placeholder:"审批完成后自动生成",readonly:""},null,8,["modelValue"])):S("",!0)],64)):S("",!0),D(d,{label:"子工程",value:i.formData.subProjectId,"onUpdate:value":a[2]||(a[2]=e=>i.formData.subProjectId=e),text:i.formData.subProjectName,"onUpdate:text":a[3]||(a[3]=e=>i.formData.subProjectName=e),columns:[...o.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:1!=o.portal.type||"view"===i.type||!o.canEdit0,onChange:o.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),i.formData.workAreaId||o.canEdit0?(h(),w(d,{key:1,label:"施工工区",value:i.formData.workAreaId,"onUpdate:value":a[4]||(a[4]=e=>i.formData.workAreaId=e),text:i.formData.workAreaName,"onUpdate:text":a[5]||(a[5]=e=>i.formData.workAreaName=e),columns:[...o.siteList],"columns-field-names":{text:"text",value:"value",children:"none"},title:"选择施工工区",readonly:"view"===i.type||!o.canEdit0},null,8,["value","text","columns","readonly"])):S("",!0),D(l,{label:"隐患来源",value:i.formData.hiddenDangerSource,"onUpdate:value":a[6]||(a[6]=e=>i.formData.hiddenDangerSource=e),columns:[...i.hiddenDangerSourceList],"columns-field-names":{text:"nodeName",value:"id",children:"children"},"trigger-change-mode":!0,title:"选择隐患来源",required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===i.type||!o.canEdit0},null,8,["value","columns","readonly"]),D(d,{label:"隐患级别",value:i.formData.hiddenDangerLevel,"onUpdate:value":a[7]||(a[7]=e=>i.formData.hiddenDangerLevel=e),"dict-name":"hidden_danger_level",title:"选择隐患级别",required:"",rules:[{required:!0,message:"请选择隐患级别"}],readonly:"view"===i.type||!o.canEdit0},null,8,["value","readonly"]),D(d,{label:"隐患分类",value:i.formData.hiddenDangerCategory,"onUpdate:value":a[8]||(a[8]=e=>i.formData.hiddenDangerCategory=e),"dict-name":"hidden_danger_category",title:"选择隐患分类",required:"",rules:[{required:!0,message:"请选择隐患分类"}],readonly:"view"===i.type||!o.canEdit0},null,8,["value","readonly"]),D(s,{label:"整改期限",value:i.formData.rectifyDate,"onUpdate:value":a[9]||(a[9]=e=>i.formData.rectifyDate=e),"show-confirm":!1,title:"选择整改期限",required:"","min-date":o.minDate,"max-date":o.maxDate,rules:[{required:!0,message:"请选择整改期限"}],readonly:"view"===i.type||!o.canEdit0},null,8,["value","min-date","max-date","readonly"]),i.formData.longitude||o.canEdit0?(h(),w(m,{key:2,label:"定位",longitude:i.formData.longitude,"onUpdate:longitude":a[10]||(a[10]=e=>i.formData.longitude=e),latitude:i.formData.latitude,"onUpdate:latitude":a[11]||(a[11]=e=>i.formData.latitude=e),title:"选择定位",readonly:"view"===i.type||!o.canEdit0},null,8,["longitude","latitude","readonly"])):S("",!0),i.formData.riskSource||o.canEdit0?(h(),w(u,{key:3,"risk-source":i.formData.riskSource,"onUpdate:riskSource":a[12]||(a[12]=e=>i.formData.riskSource=e),"risk-source-name":i.formData.riskSourceName,"onUpdate:riskSourceName":a[13]||(a[13]=e=>i.formData.riskSourceName=e),label:"关联危险源",title:"选择危险源","search-data":o.riskSourceSearch,readonly:"view"===i.type||!o.canEdit0},null,8,["risk-source","risk-source-name","search-data","readonly"])):S("",!0)])),_:1}),D(c,{border:!1},{default:y((()=>[D(n,{label:"隐患内容",modelValue:i.formData.hiddenDangerContent,"onUpdate:modelValue":a[14]||(a[14]=e=>i.formData.hiddenDangerContent=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入隐患内容",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入隐患内容"}],"input-align":"left",readonly:"view"===i.type||!o.canEdit0},null,8,["modelValue","readonly"]),i.formData.beforeFileToken&&i.formData.beforeFiles||o.canEdit0?(h(),w(n,{key:0,label:"附件照片&视频","label-align":"top","input-align":"left"},{input:y((()=>[D(g,{ref:"beforeFiles",g9s:i.formData.beforeFileToken,"onUpdate:g9s":a[15]||(a[15]=e=>i.formData.beforeFileToken=e),files:i.formData.beforeFiles,"onUpdate:files":a[16]||(a[16]=e=>i.formData.beforeFiles=e),accept:"image/*,video/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===i.type||!o.canEdit0},null,8,["g9s","files","readonly"])])),_:1})):S("",!0)])),_:1}),o.canEdit0?(h(),w(c,{key:0,border:!1},{default:y((()=>[D(C,{label:"上报人",userName:i.formData.createBy,"onUpdate:userName":a[17]||(a[17]=e=>i.formData.createBy=e),userFullname:i.formData.hiddenDangerReportorFullname,"onUpdate:userFullname":a[18]||(a[18]=e=>i.formData.hiddenDangerReportorFullname=e),deptName:i.formData.prjDepName,"onUpdate:deptName":a[19]||(a[19]=e=>i.formData.prjDepName=e),deptCode:i.formData.prjDepCode,"onUpdate:deptCode":a[20]||(a[20]=e=>i.formData.prjDepCode=e),title:"选择上报人",required:"",rules:[{required:!0,message:"请选择上报人"}],readonly:!0},null,8,["userName","userFullname","deptName","deptCode"]),D(n,{modelValue:i.formData.otherReportorFullname,"onUpdate:modelValue":a[21]||(a[21]=e=>i.formData.otherReportorFullname=e),label:"检查人员",placeholder:"同行人姓名请逗号隔开",readonly:"view"===i.type||!o.canEdit0},null,8,["modelValue","readonly"]),D(s,{label:"检查日期",value:i.formData.initiateDate,"onUpdate:value":a[22]||(a[22]=e=>i.formData.initiateDate=e),"show-confirm":!1,required:!0,"min-date":o.minDate,"max-date":o.maxDate,readonly:"view"===i.type||!o.canEdit0,rules:[{required:!0,message:"请选择检查日期"}]},null,8,["value","min-date","max-date","readonly"]),"UserTask_3"===i.taskKey||"UserTask_4"===i.taskKey||"UserTask_5"===i.taskKey||"view"===i.type?(h(),w(n,{key:0,modelValue:i.formData.overdueState,"onUpdate:modelValue":a[23]||(a[23]=e=>i.formData.overdueState=e),label:"逾期情况",placeholder:"",readonly:""},null,8,["modelValue"])):S("",!0),D(C,{label:"隐患整改人",userName:i.formData.hiddenDangerRectifier,"onUpdate:userName":a[24]||(a[24]=e=>i.formData.hiddenDangerRectifier=e),userFullname:i.formData.hiddenDangerRectifierFullname,"onUpdate:userFullname":a[25]||(a[25]=e=>i.formData.hiddenDangerRectifierFullname=e),deptName:i.formData.hiddenDangerRectifierDeptName,"onUpdate:deptName":a[26]||(a[26]=e=>i.formData.hiddenDangerRectifierDeptName=e),deptCode:i.formData.hiddenDangerRectifierDeptCode,"onUpdate:deptCode":a[27]||(a[27]=e=>i.formData.hiddenDangerRectifierDeptCode=e),title:"选择隐患整改人",required:"",rules:[{required:!0,message:"请选择隐患整改人"}],readonly:"view"===i.type||!o.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),D(n,{name:"radio",label:"是否隐患确认"},{input:y((()=>[D(N,{modelValue:i.formData.isDangerConfirm,"onUpdate:modelValue":a[28]||(a[28]=e=>i.formData.isDangerConfirm=e),direction:"horizontal",disabled:"view"===i.type||!o.canEdit0,onChange:o.onDangerConfirmChange},{default:y((()=>[D(F,{name:!0},{default:y((()=>a[52]||(a[52]=[v("是")]))),_:1,__:[52]}),D(F,{name:!1},{default:y((()=>a[53]||(a[53]=[v("否")]))),_:1,__:[53]})])),_:1},8,["modelValue","disabled","onChange"])])),_:1}),i.formData.isDangerConfirm?(h(),w(C,{key:1,label:"隐患确认人",userName:i.formData.hiddenDangerConfirmer,"onUpdate:userName":a[29]||(a[29]=e=>i.formData.hiddenDangerConfirmer=e),userFullname:i.formData.hiddenDangerConfirmerFullname,"onUpdate:userFullname":a[30]||(a[30]=e=>i.formData.hiddenDangerConfirmerFullname=e),deptName:i.formData.hiddenDangerConfirmerDeptName,"onUpdate:deptName":a[31]||(a[31]=e=>i.formData.hiddenDangerConfirmerDeptName=e),deptCode:i.formData.hiddenDangerConfirmerDeptCode,"onUpdate:deptCode":a[32]||(a[32]=e=>i.formData.hiddenDangerConfirmerDeptCode=e),title:"选择隐患确认人",required:"",rules:[{required:!0,message:"请选择隐患确认人"}],readonly:"view"===i.type||!o.canEdit0,onChange:o.setHiddenDangerChecker},null,8,["userName","userFullname","deptName","deptCode","readonly","onChange"])):S("",!0),i.formData.isDangerConfirm?(h(),w(C,{key:2,label:"整改确认人",userName:i.formData.hiddenDangerChecker,"onUpdate:userName":a[33]||(a[33]=e=>i.formData.hiddenDangerChecker=e),userFullname:i.formData.hiddenDangerCheckerFullname,"onUpdate:userFullname":a[34]||(a[34]=e=>i.formData.hiddenDangerCheckerFullname=e),deptName:i.formData.hiddenDangerCheckerDeptName,"onUpdate:deptName":a[35]||(a[35]=e=>i.formData.hiddenDangerCheckerDeptName=e),deptCode:i.formData.hiddenDangerCheckerDeptCode,"onUpdate:deptCode":a[36]||(a[36]=e=>i.formData.hiddenDangerCheckerDeptCode=e),title:"选择整改确认人",required:"",rules:[{required:!0,message:"请选择整改确认人"}],readonly:""},null,8,["userName","userFullname","deptName","deptCode"])):S("",!0),D(C,{label:"隐患审核人",userName:i.formData.hiddenDangerSupervisor,"onUpdate:userName":a[37]||(a[37]=e=>i.formData.hiddenDangerSupervisor=e),userFullname:i.formData.hiddenDangerSupervisorFullname,"onUpdate:userFullname":a[38]||(a[38]=e=>i.formData.hiddenDangerSupervisorFullname=e),deptName:i.formData.hiddenDangerSupervisorDeptName,"onUpdate:deptName":a[39]||(a[39]=e=>i.formData.hiddenDangerSupervisorDeptName=e),deptCode:i.formData.hiddenDangerSupervisorDeptCode,"onUpdate:deptCode":a[40]||(a[40]=e=>i.formData.hiddenDangerSupervisorDeptCode=e),title:"选择隐患审核人",required:"",rules:[{required:!0,message:"请选择隐患审核人"}],readonly:"view"===i.type||!o.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),D(n,{name:"radio",label:"是否隐患复核"},{input:y((()=>[D(N,{modelValue:i.formData.isSupervision,"onUpdate:modelValue":a[41]||(a[41]=e=>i.formData.isSupervision=e),direction:"horizontal",disabled:"view"===i.type||!o.canEdit0,onChange:o.onSupervisionChange},{default:y((()=>[D(F,{name:!0},{default:y((()=>a[54]||(a[54]=[v("是")]))),_:1,__:[54]}),D(F,{name:!1},{default:y((()=>a[55]||(a[55]=[v("否")]))),_:1,__:[55]})])),_:1},8,["modelValue","disabled","onChange"])])),_:1}),i.formData.isSupervision?(h(),w(C,{key:3,label:"隐患复核人",userName:i.formData.hiddenDangerRectifyApprover,"onUpdate:userName":a[42]||(a[42]=e=>i.formData.hiddenDangerRectifyApprover=e),userFullname:i.formData.hiddenDangerRectifyApproverFullname,"onUpdate:userFullname":a[43]||(a[43]=e=>i.formData.hiddenDangerRectifyApproverFullname=e),deptName:i.formData.hiddenDangerRectifyApproverDeptName,"onUpdate:deptName":a[44]||(a[44]=e=>i.formData.hiddenDangerRectifyApproverDeptName=e),deptCode:i.formData.hiddenDangerRectifyApproverDeptCode,"onUpdate:deptCode":a[45]||(a[45]=e=>i.formData.hiddenDangerRectifyApproverDeptCode=e),title:"选择隐患复核人",required:"",rules:[{required:!0,message:"请选择隐患复核人"}],readonly:"view"===i.type||!o.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"])):S("",!0)])),_:1})):S("",!0),(i.formData.rectifyComment||"execute"===i.type&&"UserTask_1"===i.taskKey)&&i.formData.isDangerConfirm?(h(),w(c,{key:1,border:!1},{default:y((()=>[D(n,{label:"整改要求及处理意见",modelValue:i.formData.rectifyComment,"onUpdate:modelValue":a[46]||(a[46]=e=>i.formData.rectifyComment=e),rows:"4",autosize:"",type:"textarea","label-width":"140","label-align":"top",placeholder:"请输入","input-align":"left",readonly:"view"===i.type||!("execute"===i.type&&"UserTask_1"===i.taskKey)},null,8,["modelValue","readonly"])])),_:1})):S("",!0),"view"===i.type&&i.formData.rectifyMeasures||i.taskKey&&"UserTask_0"!==i.taskKey&&"UserTask_1"!==i.taskKey?(h(),p(b,{key:2},[a[56]||(a[56]=k("div",{id:"hidden-rectify",class:"h-[50px] px-[18px] flex items-center bg-[#fff] my-[8px]"}," 隐患整改 ",-1)),D(c,{border:!1},{default:y((()=>[i.formData.dispatchingCompany||o.canEdit2?(h(),w(d,{key:0,label:"劳务公司",value:i.formData.dispatchingCompany,"onUpdate:value":a[47]||(a[47]=e=>i.formData.dispatchingCompany=e),"dict-name":"dispatching_company",title:"选择劳务公司",readonly:"view"===i.type||!o.canEdit2},null,8,["value","readonly"])):S("",!0),D(n,{label:"整改措施",modelValue:i.formData.rectifyMeasures,"onUpdate:modelValue":a[48]||(a[48]=e=>i.formData.rectifyMeasures=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改措施",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请填写整改措施"}],"input-align":"left",readonly:"view"===i.type||!o.canEdit2},null,8,["modelValue","readonly"]),D(n,{label:"整改情况",modelValue:i.formData.rectifySituation,"onUpdate:modelValue":a[49]||(a[49]=e=>i.formData.rectifySituation=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改情况",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请填写整改情况"}],"input-align":"left",readonly:"view"===i.type||!o.canEdit2},null,8,["modelValue","readonly"]),o.canEdit2||i.formData.afterFiles&&i.formData.afterFileToken?(h(),w(n,{key:1,label:"附件照片&视频","label-align":"top","input-align":"left"},{input:y((()=>[D(g,{ref:"afterFiles",g9s:i.formData.afterFileToken,"onUpdate:g9s":a[50]||(a[50]=e=>i.formData.afterFileToken=e),files:i.formData.afterFiles,"onUpdate:files":a[51]||(a[51]=e=>i.formData.afterFiles=e),accept:"image/*,video/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===i.type||!o.canEdit2},null,8,["g9s","files","readonly"])])),_:1})):S("",!0)])),_:1})],64)):S("",!0)])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit","onCopyCallBack"])}]]))}}}));
