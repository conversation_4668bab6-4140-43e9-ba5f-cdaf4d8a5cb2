System.register(["./index-legacy-f817ae93.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var l,t,n,s,o,d,i,m,r,p,c;return{setters:[e=>{l=e.F,t=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,d=e.X,i=e.V,m=e.k,r=e.U,p=e.Y,c=e.B},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".van-checkbox[data-v-80593692]{display:flex;align-items:flex-start}\n",document.head.appendChild(a);const u={class:"comment-wp"},f={class:"textarea-wp"},h={class:"comment-wp"},y={class:"textarea-wp"},b={class:"comment-wp"},v={class:"textarea-wp"},g={class:"comment-wp"},C={class:"one-line"},k={class:"form-info"},D={class:"form-info"},V={class:"form-info"},w={class:"form-info"},x={class:"form-info"},j={class:"form-info"},T={class:"comment-wp"},$={class:"textarea-wp"},F={class:"comment-wp"},U={class:"check-wp"},_={class:"one-line"},N={class:"input-field",style:{padding:"0 4px"}},P={class:"input-field",style:{padding:"0 4px"}},Y={class:"input-field",style:{padding:"0 4px"}},L={class:"check-wp"},O={class:"footer-input"},z={class:"form-info"},I={class:"form-info"},S={class:"form-info"},A={class:"form-info"};e("default",n({name:"CB12",components:{FormTemplate:l,DocumentPart:t},emits:[],props:{},setup(e,{attrs:a,slots:l,emit:t}){},data:()=>({detailTable:[],showCalendar:!1,attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit({formData:e,detailParamList:a,taskComment3:l},t){return new Promise(((a,n)=>{try{if("submit"===t&&l){if(!e.check1&&!e.check2)return this.$showNotify({type:"danger",message:"请选择测量意见!",duration:3e3}),n(!1),!1;if(e.check1&&(!e.field10||!e.field11||!e.field12))return this.$showNotify({type:"danger",message:"请填写测量时间!",duration:3e3}),n(!1),!1}a()}catch(s){n(s)}}))},changeCheck1(e){e&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.taskComment3&&(this.showCalendar=!0))},changeCheck2(e){e&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.field10="",this.$refs.FormTemplate.formData.field11="",this.$refs.FormTemplate.formData.field12="",this.$nextTick((()=>{var e;null===(e=this.$refs.FormTemplate.$refs.form)||void 0===e||e.resetValidation()})))},onConfirmCalendar(e){const a=this.$dayjs(e).format("YYYY-MM-DD");this.$refs.FormTemplate.formData.field10=this.$dayjs(a).format("YYYY"),this.$refs.FormTemplate.formData.field11=this.$dayjs(a).format("MM"),this.$refs.FormTemplate.formData.field12=this.$dayjs(a).format("DD"),this.showCalendar=!1}}},[["render",function(e,a,l,t,n,B){const M=s("van-field"),W=s("DocumentPart"),q=s("van-checkbox"),E=s("van-calendar"),Q=s("FormTemplate");return o(),d(Q,{ref:"FormTemplate",nature:"联测","on-after-init":B.onAfterInit,"on-before-submit":B.onBeforeSubmit,"detail-table":n.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:n.attachmentDesc},{default:i((({formData:e,formTable:l,baseObj:t,uploadAccept:s,taskStart:o,taskComment2:d,taskComment3:O,taskComment4:z,taskComment5:I})=>[m(W,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:t.constructionDeptName,personLabel:"技术负责人：",labelWidth:"10em",disabled:!o},{default:i((()=>[a[11]||(a[11]=r("div",{class:"comment-wp"},[r("div",{class:"one-line"},[r("span",{style:{"margin-left":"2em"}}," 根据合同约定和工程进度，我方拟进行工程测量工作，请贵方派员参加。 ")])],-1)),r("div",u,[a[1]||(a[1]=r("div",null,"施测工程部位：",-1)),r("div",f,[m(M,{modelValue:e.field1,"onUpdate:modelValue":a=>e.field1=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),r("div",h,[a[2]||(a[2]=r("div",null,"项目工作内容：",-1)),r("div",y,[m(M,{modelValue:e.field2,"onUpdate:modelValue":a=>e.field2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),r("div",b,[a[3]||(a[3]=r("div",null,"任务要点：",-1)),r("div",v,[m(M,{modelValue:e.field3,"onUpdate:modelValue":a=>e.field3=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),r("div",g,[r("div",C,[a[4]||(a[4]=r("span",null,"施测计划时间：",-1)),r("span",k,p(e.field4),1),a[5]||(a[5]=r("span",null,"年",-1)),r("span",D,p(e.field5),1),a[6]||(a[6]=r("span",null,"月",-1)),r("span",V,p(e.field6),1),a[7]||(a[7]=r("span",null,"日至",-1)),r("span",w,p(e.field7),1),a[8]||(a[8]=r("span",null,"年",-1)),r("span",x,p(e.field8),1),a[9]||(a[9]=r("span",null,"月",-1)),r("span",j,p(e.field9),1),a[10]||(a[10]=r("span",null,"日",-1))])])])),_:2,__:[11]},1032,["deptValue","deptOptions","disabled"]),m(W,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:t.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!o},{default:i((()=>[r("div",T,[a[12]||(a[12]=r("div",null,"EPC总承包项目部意见：",-1)),r("div",$,[m(M,{modelValue:e.comment2,"onUpdate:modelValue":a=>e.comment2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),m(W,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:t.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!o},{default:i((()=>[r("div",F,[r("div",null,[r("div",U,[m(q,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!O,onChange:B.changeCheck1},{default:i((()=>[r("div",_,[a[13]||(a[13]=r("span",null,"拟于",-1)),r("span",N,p(e.field10),1),a[14]||(a[14]=r("span",null,"年",-1)),r("span",P,p(e.field11),1),a[15]||(a[15]=r("span",null,"月",-1)),r("span",Y,p(e.field12),1),a[16]||(a[16]=r("span",null,"日派监理人员参加测量",-1))])])),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),r("div",null,[r("div",L,[m(q,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!O,onChange:B.changeCheck2},{default:i((()=>a[17]||(a[17]=[c(" 不派人参加联合测量，你方测量后将测量结果报给我方审核。 ")]))),_:2,__:[17]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),m(E,{show:n.showCalendar,"onUpdate:show":a[0]||(a[0]=e=>n.showCalendar=e),onConfirm:B.onConfirmCalendar},null,8,["show","onConfirm"])])),footer:i((({formData:e,formTable:l,baseObj:t,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:d,taskComment4:i,taskComment5:m})=>[r("div",O,[a[18]||(a[18]=r("span",null,"说明：本表一式",-1)),r("span",z,p(e.num1),1),a[19]||(a[19]=r("span",null,"份，由承包人填写，监理机构签署后，发包人",-1)),r("span",I,p(e.num2),1),a[20]||(a[20]=r("span",null,"份，监理机构",-1)),r("span",S,p(e.num3),1),a[21]||(a[21]=r("span",null,"份，承包人",-1)),r("span",A,p(e.num4),1),a[22]||(a[22]=r("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}],["__scopeId","data-v-80593692"]]))}}}));
