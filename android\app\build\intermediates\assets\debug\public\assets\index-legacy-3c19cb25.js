System.register(["./index-legacy-b580af71.js","./DesignDispatchDrawingTable-legacy-b7054d89.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js"],(function(e,a){"use strict";var t,l,i,o,r,n,m,s,d;return{setters:[e=>{t=e.F},e=>{l=e.D,i=e.a},e=>{o=e._},e=>{r=e.Q,n=e.R,m=e.X,s=e.V,d=e.k},null,null,null,null,null,null,null,null,null,null],execute:function(){e("default",o({name:"TechnologyDesignOuterAuditNew",components:{FlowForm:t,DesignDispatchCommonTopNew:l,DesignDispatchDrawingTable:i},data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",modelKey:"technology_design_outer_audit",entityName:"TechnologyCommonFile",detailEntityName:"TechnologyDesignEPCAudit",detailParamList:[],detailEntityNameList:["TechnologyDesignEPCAudit","TechnologyDesignEPCAuditDrawing"],service:{query:"/cybereng-technology/design/EPCAudit/query",submit:"/cybereng-technology/design/form/commit"},formKey:"TechnologyDesignOuterAuditNew",formData:{formKey:"TechnologyDesignOuterAuditNew",portalId:"",subProjectName:"",subProjectId:null,modelType:"",sendingName:"",sendingCode:"",sendingType:"",sendingTypeCode:"",contentDescription:"",profession:"",relavancePartName:"",relavancePart:"",leadDesigner:"",leadDesignerFullname:"",userTelephoneNum:"",fileNum:"",fileName:"",isconfirm1:!0,approverUsername1:"",approverFullname1:"",approverUnit1:"",time1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",time2:"",duplicateUsername1:"",duplicateFullname1:"",duplicateUsername2:"",duplicateFullname2:"",relatedBpmId:""},formDetailTable:[],detailId:""}},computed:{},mounted(){this.initForm()},methods:{async initForm(){this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:a=[],entityObject:t}=e;this.detailParamList=a,this.detailId=a[0].detailEntityArray[0].id,this.formData={...this.formData,...a[0].detailEntityArray[0],...t,notifyMethods:t.notifyMethod?t.notifyMethod.split(","):[]},this.formDetailTable=a[1].detailEntityArray}))}))},async onDraft(){try{const e={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{if(["fileName","relavancePartName","profession","leadDesignerFullname","subProjectName","approverFullname1"].some((e=>!this.formData[e])))return this.$showToast({message:"请登录数智建管系统完善表单"});if(!this.formDetailTable.length)return this.$showToast({message:"请登录数智建管系统完善表单"});await this.$refs.form.validate(),this.setTime();const e={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},setTime(){switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_2":this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss")}},async afterSubmit(){}}},[["render",function(e,a,t,l,i,o){const u=r("design-dispatch-common-top-new"),y=r("design-dispatch-drawing-table"),c=r("van-field"),f=r("van-cell-group"),p=r("van-form"),g=r("FlowForm");return n(),m(g,{ref:"FlowForm","model-key":i.modelKey,"form-key":i.formKey,"entity-name":i.entityName,"detail-param-list":i.detailParamList,"detail-entity-name-list":i.detailEntityNameList,onDraftClick:o.onDraft,onSubmitClick:o.onSubmit,onAfterSubmit:o.afterSubmit},{default:s((()=>[d(p,{ref:"form","label-width":"8em","input-align":"right","error-message-align":"right"},{default:s((()=>[d(u,{"form-data":i.formData,type:i.type,readonly:""},null,8,["form-data","type"]),d(y,{"form-detail-table":i.formDetailTable,ref:"detailForm","form-data":i.formData,type:i.type},null,8,["form-detail-table","form-data","type"]),d(f,{border:!1},{default:s((()=>[d(c,{modelValue:i.formData.userFullname,"onUpdate:modelValue":a[0]||(a[0]=e=>i.formData.userFullname=e),label:"发起人",readonly:""},null,8,["modelValue"]),d(c,{modelValue:i.formData.prjDepName,"onUpdate:modelValue":a[1]||(a[1]=e=>i.formData.prjDepName=e),label:"发起人部门",readonly:""},null,8,["modelValue"]),d(c,{modelValue:i.formData.approverFullname1,"onUpdate:modelValue":a[2]||(a[2]=e=>i.formData.approverFullname1=e),label:"审查单位接收人",readonly:"",required:""},null,8,["modelValue"]),d(c,{modelValue:i.formData.approverUnit1,"onUpdate:modelValue":a[3]||(a[3]=e=>i.formData.approverUnit1=e),label:"审查单位接收人部门","label-width":"9em",readonly:""},null,8,["modelValue"]),d(c,{modelValue:i.formData.duplicateFullname1,"onUpdate:modelValue":a[4]||(a[4]=e=>i.formData.duplicateFullname1=e),label:"抄送至",readonly:""},null,8,["modelValue"])])),_:1})])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}]]))}}}));
