System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var t,l,n,o,s,m,d,r,i,u;return{setters:[e=>{t=e.F,l=e.D},e=>{n=e._},e=>{o=e.Q,s=e.R,m=e.X,d=e.V,r=e.k,i=e.U,u=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"comment-wp"},p={class:"textarea-wp"},c={class:"comment-wp"},f={class:"textarea-wp"},y={class:"comment-wp"},b={class:"textarea-wp"},g={class:"footer-input"},v={class:"form-info"},j={class:"form-info"},V={class:"form-info"};e("default",n({name:"JL07",components:{FormTemplate:t,DocumentPart:l},emits:[],props:{},setup(e,{attrs:a,slots:t,emit:l}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit:({formData:e,detailParamList:a},t)=>new Promise(((e,a)=>{try{e()}catch(t){a(t)}}))}},[["render",function(e,t,l,n,h,D){const w=o("van-field"),x=o("DocumentPart"),k=o("FormTemplate");return s(),m(k,{ref:"FormTemplate",nature:"报告","employer-target":!0,"on-after-init":D.onAfterInit,"on-before-submit":D.onBeforeSubmit,"detail-table":h.detailTable,"is-show-confirm1":!1,attachmentDesc:h.attachmentDesc},{default:d((({formData:e,formTable:l,baseObj:n,uploadAccept:o,taskStart:s,taskComment2:m,taskComment3:u,taskComment4:g})=>[r(x,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"总监理工程师/监理工程师：",labelWidth:"10em",disabled:!s},{default:d((()=>[i("div",a,[t[0]||(t[0]=i("div",null,"事由：",-1)),i("div",p,[r(w,{modelValue:e.field1,"onUpdate:modelValue":a=>e.field1=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),i("div",c,[t[1]||(t[1]=i("div",null,"报告内容：",-1)),i("div",f,[r(w,{modelValue:e.field2,"onUpdate:modelValue":a=>e.field2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),r(x,{deptLabel:"发包人：",deptProp:"employerName",deptValue:e.employerName,deptOptions:n.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!s},{default:d((()=>[i("div",y,[t[2]||(t[2]=i("div",null,"就贵方报告事宜答复如下：",-1)),i("div",b,[r(w,{modelValue:e.comment4,"onUpdate:modelValue":a=>e.comment4=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!g},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:d((({formData:e,formTable:a,baseObj:l,uploadAccept:n,taskStart:o,taskComment2:s,taskComment3:m,taskComment4:d})=>[i("div",g,[t[3]||(t[3]=i("span",null,"说明：1、本通知一式",-1)),i("span",v,u(e.num1),1),t[4]||(t[4]=i("span",null,"份，由监理机构填写，发包人批复后留",-1)),i("span",j,u(e.num2),1),t[5]||(t[5]=i("span",null,"份，退回监理机构",-1)),i("span",V,u(e.num3),1),t[6]||(t[6]=i("span",null,"份。",-1))]),t[7]||(t[7]=i("div",{class:"footer-input"},[i("span",{style:{"text-indent":"3em"}},"2、本表可用于监理机构认为需报请发包人批示的各项事宜。")],-1))])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
