System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var t,l,n,s,o,d,m,i,r,u;return{setters:[e=>{t=e.F,l=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,d=e.X,m=e.V,i=e.k,r=e.U,u=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"comment-wp"},p={class:"textarea-wp"},c={class:"comment-wp"},f={class:"textarea-wp"},b={class:"attachment-desc"},y={class:"footer-input"},h={class:"form-info"},g={class:"form-info"},D={class:"form-info"},v={class:"form-info"};e("default",n({name:"JL06",components:{FormTemplate:t,DocumentPart:l},emits:[],props:{},setup(e,{attrs:a,slots:t,emit:l}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit:({formData:e,detailParamList:a},t)=>new Promise(((e,a)=>{try{e()}catch(t){a(t)}}))}},[["render",function(e,t,l,n,j,V){const w=s("van-field"),x=s("DocumentPart"),k=s("FormTemplate");return o(),d(k,{ref:"FormTemplate",nature:"通知","on-after-init":V.onAfterInit,"on-before-submit":V.onBeforeSubmit,"detail-table":j.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:j.attachmentDesc},{default:m((({formData:e,formTable:l,baseObj:n,uploadAccept:s,taskStart:o,taskComment2:d,taskComment3:u,taskComment4:y})=>[i(x,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"总监理工程师/监理工程师：",labelWidth:"10em",disabled:!o},{default:m((()=>[r("div",a,[t[0]||(t[0]=r("div",null,"事由：",-1)),r("div",p,[i(w,{modelValue:e.field1,"onUpdate:modelValue":a=>e.field1=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),r("div",c,[t[1]||(t[1]=r("div",null,"通知内容：",-1)),r("div",f,[i(w,{modelValue:e.field2,"onUpdate:modelValue":a=>e.field2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),r("div",b,[t[2]||(t[2]=r("div",null,"附件：",-1)),i(w,{modelValue:e.attachmentDesc,"onUpdate:modelValue":a=>e.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),i(x,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!o},{default:m((()=>t[3]||(t[3]=[r("div",{class:"comment-wp"},[r("div",{style:{height:"30px"}})],-1)]))),_:2,__:[3]},1032,["deptValue","deptOptions","disabled"])])),footer:m((({formData:e,formTable:a,baseObj:l,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:d,taskComment4:m})=>[r("div",y,[t[4]||(t[4]=r("span",null,"说明：本通知一式",-1)),r("span",h,u(e.num1),1),t[5]||(t[5]=r("span",null,"份，由监理机构填写，承包人",-1)),r("span",g,u(e.num2),1),t[6]||(t[6]=r("span",null,"份，监理机构",-1)),r("span",D,u(e.num3),1),t[7]||(t[7]=r("span",null,"份，发包人",-1)),r("span",v,u(e.num4),1),t[8]||(t[8]=r("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
