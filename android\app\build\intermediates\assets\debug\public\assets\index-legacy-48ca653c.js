System.register(["./index-legacy-b580af71.js","./DesignDispatchDrawingTable-legacy-b7054d89.js","./FormItemPerson-legacy-e6e57748.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js"],(function(e,a){"use strict";var r,t,l,o,m,n,i,s,p,u,d,f,y,D;return{setters:[e=>{r=e.F,t=e.a},e=>{l=e.D,o=e.a},e=>{m=e.F},e=>{n=e._},e=>{i=e.Q,s=e.R,p=e.X,u=e.V,d=e.k,f=e.S,y=e.Z,D=e.F},null,null,null,null,null,null,null,null,null],execute:function(){e("default",n({name:"TechnologyDesignProprietorAuditNew",components:{FlowForm:r,DesignDispatchCommonTopNew:l,DesignDispatchDrawingTable:o,FormItemPerson:m,FormItemMultiplePerson:t},data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",modelKey:"technology_design_proprietor_audit_2",entityName:"TechnologyCommonFile",detailEntityName:"TechnologyDesignEPCAudit",detailParamList:[],detailEntityNameList:["TechnologyDesignEPCAudit","TechnologyDesignEPCAuditDrawing"],service:{query:"/cybereng-technology/design/EPCAudit/query",submit:"/cybereng-technology/design/form/commit"},formKey:"TechnologyDesignProprietorAuditNew",formData:{formKey:"TechnologyDesignProprietorAuditNew",portalId:"",subProjectName:"",subProjectId:null,modelType:"",sendingName:"",sendingCode:"",sendingType:"",sendingTypeCode:"",contentDescription:"",profession:"",relavancePartName:"",relavancePart:"",leadDesigner:"",leadDesignerFullname:"",userTelephoneNum:"",fileNum:"",fileName:"",notifyMethods:[],isconfirm1:!0,approverUsername1:"",approverFullname1:"",approverUnit1:"",time1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",time2:"",approverUsername3:"",approverFullname3:"",approverUnit3:"",time3:"",approverUsername4:"",approverFullname4:"",approverUnit4:"",time4:"",approverUsername5:"",approverFullname5:"",approverUnit5:"",time5:"",approverUsername6:"",approverFullname6:"",approverUnit6:"",time6:"",approverUsername7:"",approverFullname7:"",time7:"",duplicateUsername1:"",duplicateFullname1:"",duplicateUsername2:"",duplicateFullname2:"",relatedBpmId:""},formDetailTable:[],detailId:""}},computed:{canEdit1(){return"execute"===this.type&&"UserTask_1"===this.taskKey}},mounted(){this.initForm()},methods:{async initForm(){this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:a=[],entityObject:r}=e;this.detailParamList=a,this.detailId=a[0].detailEntityArray[0].id,this.formData={...this.formData,...a[0].detailEntityArray[0],...r,notifyMethods:r.notifyMethod?r.notifyMethod.split(","):[]},this.formDetailTable=a[1].detailEntityArray}))}))},async onDraft(){try{const e={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{if(["fileName","fileNum","relavancePartName","profession","leadDesignerFullname","subProjectName","approverFullname1"].some((e=>!this.formData[e])))return this.$showToast({message:"请登录数智建管系统完善表单"});if(!this.formDetailTable.length)return this.$showToast({message:"请登录数智建管系统完善表单"});await this.$refs.form.validate(),this.canEdit1&&(this.formData.approverUsername7=`${this.formData.approverUsername2},${this.formData.approverUsername3},${this.formData.approverUsername4}`,this.formData.approverFullname7=`${this.formData.approverFullname2},${this.formData.approverFullname3},${this.formData.approverFullname4}`),this.setTime();const e={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},setTime(){switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_2":this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_3":this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_4":this.formData.time4=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_5":this.formData.time5=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_6":this.formData.time6=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_7":this.formData.time7=this.$dayjs().format("YYYY-MM-DD HH:mm:ss")}},async afterSubmit(){}}},[["render",function(e,a,r,t,l,o){const m=i("design-dispatch-common-top-new"),n=i("design-dispatch-drawing-table"),c=i("van-field"),v=i("FormItemPerson"),U=i("FormItemMultiplePerson"),h=i("van-cell-group"),F=i("van-form"),g=i("FlowForm");return s(),p(g,{ref:"FlowForm","model-key":l.modelKey,"form-key":l.formKey,"entity-name":l.entityName,"detail-param-list":l.detailParamList,"detail-entity-name-list":l.detailEntityNameList,onDraftClick:o.onDraft,onSubmitClick:o.onSubmit,onAfterSubmit:o.afterSubmit},{default:u((()=>[d(F,{ref:"form","label-width":"8em","input-align":"right","error-message-align":"right"},{default:u((()=>[d(m,{"form-data":l.formData,type:l.type,readonly:""},null,8,["form-data","type"]),d(n,{"form-detail-table":l.formDetailTable,ref:"detailForm","form-data":l.formData,type:l.type},null,8,["form-detail-table","form-data","type"]),d(h,{border:!1},{default:u((()=>[d(c,{modelValue:l.formData.userFullname,"onUpdate:modelValue":a[0]||(a[0]=e=>l.formData.userFullname=e),label:"发起人",readonly:""},null,8,["modelValue"]),d(c,{modelValue:l.formData.prjDepName,"onUpdate:modelValue":a[1]||(a[1]=e=>l.formData.prjDepName=e),label:"发起人部门",readonly:""},null,8,["modelValue"]),d(c,{modelValue:l.formData.approverFullname1,"onUpdate:modelValue":a[2]||(a[2]=e=>l.formData.approverFullname1=e),required:"",label:"业主联络人",readonly:""},null,8,["modelValue"]),d(c,{modelValue:l.formData.approverUnit1,"onUpdate:modelValue":a[3]||(a[3]=e=>l.formData.approverUnit1=e),label:"业主联络人部门",readonly:""},null,8,["modelValue"]),d(c,{modelValue:l.formData.duplicateFullname1,"onUpdate:modelValue":a[4]||(a[4]=e=>l.formData.duplicateFullname1=e),label:"抄送至",readonly:""},null,8,["modelValue"]),"view"===l.type||"execute"===l.type&&"UserTask_0"!==l.taskKey?(s(),f(D,{key:0},[l.formData.approverUsername3||o.canEdit1?(s(),p(v,{key:0,label:"监理评审人",userName:l.formData.approverUsername3,"onUpdate:userName":a[5]||(a[5]=e=>l.formData.approverUsername3=e),userFullname:l.formData.approverFullname3,"onUpdate:userFullname":a[6]||(a[6]=e=>l.formData.approverFullname3=e),deptName:l.formData.approverUnit3,"onUpdate:deptName":a[7]||(a[7]=e=>l.formData.approverUnit3=e),title:"选择监理评审人",required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit1},null,8,["userName","userFullname","deptName","readonly"])):y("",!0),d(c,{modelValue:l.formData.approverUnit3,"onUpdate:modelValue":a[8]||(a[8]=e=>l.formData.approverUnit3=e),label:"监理评审人部门",readonly:""},null,8,["modelValue"]),l.formData.approverUsername4||o.canEdit1?(s(),p(v,{key:1,label:"施工评审人",userName:l.formData.approverUsername4,"onUpdate:userName":a[9]||(a[9]=e=>l.formData.approverUsername4=e),userFullname:l.formData.approverFullname4,"onUpdate:userFullname":a[10]||(a[10]=e=>l.formData.approverFullname4=e),deptName:l.formData.approverUnit4,"onUpdate:deptName":a[11]||(a[11]=e=>l.formData.approverUnit4=e),title:"选择施工评审人",required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit1},null,8,["userName","userFullname","deptName","readonly"])):y("",!0),d(c,{modelValue:l.formData.approverUnit4,"onUpdate:modelValue":a[12]||(a[12]=e=>l.formData.approverUnit4=e),label:"施工评审人部门",readonly:""},null,8,["modelValue"]),l.formData.approverUsername2||o.canEdit1?(s(),p(v,{key:2,label:"业主评审人",userName:l.formData.approverUsername2,"onUpdate:userName":a[13]||(a[13]=e=>l.formData.approverUsername2=e),userFullname:l.formData.approverFullname2,"onUpdate:userFullname":a[14]||(a[14]=e=>l.formData.approverFullname2=e),deptName:l.formData.approverUnit2,"onUpdate:deptName":a[15]||(a[15]=e=>l.formData.approverUnit2=e),title:"选择业主评审人",required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit1},null,8,["userName","userFullname","deptName","readonly"])):y("",!0),d(c,{modelValue:l.formData.approverUnit2,"onUpdate:modelValue":a[16]||(a[16]=e=>l.formData.approverUnit2=e),label:"业主评审人部门",readonly:""},null,8,["modelValue"]),l.formData.approverUsername5||o.canEdit1?(s(),p(v,{key:3,label:"意见汇总人",userName:l.formData.approverUsername5,"onUpdate:userName":a[17]||(a[17]=e=>l.formData.approverUsername5=e),userFullname:l.formData.approverFullname5,"onUpdate:userFullname":a[18]||(a[18]=e=>l.formData.approverFullname5=e),deptName:l.formData.approverUnit5,"onUpdate:deptName":a[19]||(a[19]=e=>l.formData.approverUnit5=e),title:"选择意见汇总人",required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit1},null,8,["userName","userFullname","deptName","readonly"])):y("",!0),d(c,{modelValue:l.formData.approverUnit5,"onUpdate:modelValue":a[20]||(a[20]=e=>l.formData.approverUnit5=e),label:"意见汇总人部门",readonly:""},null,8,["modelValue"]),l.formData.approverUsername6||o.canEdit1?(s(),p(v,{key:4,label:"南排公司质安部联络人",userName:l.formData.approverUsername6,"onUpdate:userName":a[21]||(a[21]=e=>l.formData.approverUsername6=e),userFullname:l.formData.approverFullname6,"onUpdate:userFullname":a[22]||(a[22]=e=>l.formData.approverFullname6=e),deptName:l.formData.approverUnit6,"onUpdate:deptName":a[23]||(a[23]=e=>l.formData.approverUnit6=e),title:"选择南排公司质安部联络人",required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit1},null,8,["userName","userFullname","deptName","readonly"])):y("",!0),d(c,{modelValue:l.formData.approverUnit6,"onUpdate:modelValue":a[24]||(a[24]=e=>l.formData.approverUnit6=e),label:"南排公司质安部联络人部门",readonly:""},null,8,["modelValue"]),l.formData.duplicateUsername2||o.canEdit1?(s(),p(U,{key:5,label:"抄送至",title:"选择抄送人",readonly:"view"===l.type||!o.canEdit1,"user-name":l.formData.duplicateUsername2,"onUpdate:userName":a[25]||(a[25]=e=>l.formData.duplicateUsername2=e),"user-fullname":l.formData.duplicateFullname2,"onUpdate:userFullname":a[26]||(a[26]=e=>l.formData.duplicateFullname2=e)},null,8,["readonly","user-name","user-fullname"])):y("",!0)],64)):y("",!0)])),_:1})])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}]]))}}}));
