System.register(["./index-legacy-b580af71.js","./index-legacy-645a3645.js","./FormItemSection-legacy-66423754.js","./FormItemDate-legacy-c4422d42.js","./formMix-legacy-ca767df5.js","./index-legacy-09188690.js","./validate-legacy-4e8f0db9.js","./verder-legacy-e6127216.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js"],(function(e,a){"use strict";var o,t,l,r,s,i,n,d,c,m,u,p,f,h,b,y;return{setters:[e=>{o=e.F,t=e.b},e=>{l=e.U},e=>{r=e._},e=>{s=e.F},e=>{i=e.f},e=>{n=e._,d=e.A},e=>{c=e.c},e=>{m=e.Q,u=e.R,p=e.X,f=e.V,h=e.k,b=e.B,y=e.Z},null,null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".van-checkbox--horizontal[data-v-26452589]{--van-checkbox-label-color: #333;--van-checkbox-size: 3.73333vw;--van-checkbox-disabled-label-color: #333;--van-checkbox-disabled-icon-color: #333;--van-checkbox-disabled-background: #fff;padding:2.66667vw}.van-checkbox--horizontal .checkbox-group[data-v-26452589]{display:flex;flex-direction:row;flex-wrap:wrap}.van-checkbox--horizontal .checkbox-group .checkbox[data-v-26452589]{width:calc(100% - 3.73333vw);margin-right:3.73333vw;margin-bottom:1.33333vw;align-items:start}.van-checkbox--horizontal .checkbox-group .checkbox.col-1[data-v-26452589]{width:calc(100% - 3.73333vw)}.van-checkbox--horizontal .checkbox-group .checkbox.col-2[data-v-26452589]{width:calc(50% - 3.73333vw)}.van-checkbox--horizontal .checkbox-group .checkbox.col-3[data-v-26452589]{width:calc(33% - 3.73333vw)}.van-checkbox--horizontal .checkbox-group .checkbox.col-4[data-v-26452589]{width:calc(25% - 3.73333vw)}.van-checkbox--horizontal .action-wp[data-v-26452589]{padding-top:1.33333vw}.van-checkbox--horizontal .add-input-wp[data-v-26452589],.van-checkbox--horizontal .add-input-wp .add-input-action[data-v-26452589]{display:flex;flex-direction:row;align-items:center}.van-checkbox--horizontal .add-input-wp .add-input-action .btn[data-v-26452589]{margin-left:1.33333vw}\n",document.head.appendChild(a),e("default",n({name:"ProgressMilepost",components:{FlowForm:o,UploadFiles:l,FormItemSection:r,FormItemDate:s},mixins:[i],data(){var e,a;return{allTaskKeys:[["UserTask_0","UserTask_1","UserTask_2","UserTask_3","UserTask_4","UserTask_5","UserTask_6","UserTask_7","UserTask_8","UserTask_9","UserTask_10"],["UserTask_0","UserTask_1","UserTask_2_1","UserTask_3_1"]],type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",modelKey:"milepost",entityName:"ProgressMilepost",service:{query:d.VUE_APP_BASE_API_SERVICENAME+"/form/query",submit:d.VUE_APP_BASE_API_SERVICENAME+"/form/commit"},formKey:"ProgressMilepost",formData:{formKey:"ProgressMilepost",portalId:"",subProjectName:"",subProjectId:null,modelType:"",sendingName:"",sendingCode:"",sendingType:"",sendingTypeCode:"",processName:"",processCode:"",sectionId:"",milepostName:"",realDate:"",notifyMethod:[0],fileUpload:null,remark:""}}},computed:{canEdit0(){return"add"===this.type||"UserTask_0"===this.taskKey}},methods:{async beforeSubmit(e,a){let o={tableName:"progress_milepost",fieldName:"process_code",fieldVal:this.formData.processCode,dataId:this.formData.id};if(0===await this.$bizStore.duplicateCheck(o))return this.$showToast({message:"流程编号已存在！"}),!1;let{initiatorInfo:l}=a,r={...this.formData};return"add"!==this.type&&e.code!==t._Object.saveDraft.code||(r.startTime=l.approvalTime,r.submitterName=l.userFullName),r},async onDraft(){try{const e={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{if(["sectionId","processName","processCode","milepostName","realDate","notifyMethod","fileUpload"].some((e=>c(this.formData[e]))))return this.$showToast({message:"请完善表单数据"});this.setTime();const e={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},setTime(){switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_2":this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_3":this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss")}},async updateFiles(){return new Promise((async(e,a)=>{try{this.$refs.fileUpload&&await this.$refs.fileUpload.update(),e()}catch(o){a()}}))},async afterSubmit(e,a){this.updateFiles()}}},[["render",function(e,a,o,t,l,r){const s=m("van-field"),i=m("FormItemSection"),n=m("FormItemDate"),d=m("van-checkbox"),c=m("van-checkbox-group"),k=m("UploadFiles"),v=m("van-cell-group"),g=m("van-form"),_=m("FlowForm");return u(),p(_,{ref:"FlowForm","model-key":l.modelKey,"form-key":l.formKey,"entity-name":l.entityName,"all-task-keys":l.allTaskKeys,"before-submit":r.beforeSubmit,"bpm-person-obj":e.bpmPersonObj,"onUpdate:bpmPersonObj":a[8]||(a[8]=a=>e.bpmPersonObj=a),onDraftClick:r.onDraft,onSubmitClick:r.onSubmit,onAfterSubmit:r.afterSubmit},{default:f((()=>[h(g,{ref:"form","label-width":"8em","input-align":"right","error-message-align":"right"},{default:f((()=>[h(v,{border:!1},{default:f((()=>[h(s,{modelValue:l.formData.processName,"onUpdate:modelValue":a[0]||(a[0]=e=>l.formData.processName=e),label:"流程名称",readonly:"view"===l.type||!r.canEdit0,required:""},null,8,["modelValue","readonly"]),h(s,{modelValue:l.formData.processCode,"onUpdate:modelValue":a[1]||(a[1]=e=>l.formData.processCode=e),label:"流程编号",readonly:"view"===l.type||!r.canEdit0,required:""},null,8,["modelValue","readonly"]),h(i,{label:"所属标段",modelValue:l.formData.sectionId,"onUpdate:modelValue":a[2]||(a[2]=e=>l.formData.sectionId=e),readonly:"",required:""},null,8,["modelValue"]),h(s,{modelValue:l.formData.milepostName,"onUpdate:modelValue":a[3]||(a[3]=e=>l.formData.milepostName=e),label:"里程碑名称",readonly:"view"===l.type||!r.canEdit0,required:""},null,8,["modelValue","readonly"]),h(n,{label:"实际完成时间",value:l.formData.realDate,"onUpdate:value":a[4]||(a[4]=e=>l.formData.realDate=e),submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!r.canEdit0},null,8,["value","readonly"]),r.canEdit0?(u(),p(s,{key:0,name:"checkboxGroup",label:"通知方式",class:"check-box-color",required:"",rules:[{required:!0,message:"请选择"}]},{input:f((()=>[h(c,{modelValue:l.formData.notifyMethod,"onUpdate:modelValue":a[5]||(a[5]=e=>l.formData.notifyMethod=e),direction:"horizontal",shape:"square",disabled:"view"===l.type||!r.canEdit0},{default:f((()=>[h(d,{name:0},{default:f((()=>a[9]||(a[9]=[b("系统通知")]))),_:1,__:[9]}),h(d,{name:1},{default:f((()=>a[10]||(a[10]=[b("短信通知")]))),_:1,__:[10]})])),_:1},8,["modelValue","disabled"])])),_:1})):y("",!0),l.formData.fileUpload||r.canEdit0?(u(),p(s,{key:1,label:"上传附件","label-align":"top","input-align":"left",required:""},{input:f((()=>[h(k,{ref:"fileUpload",g9s:l.formData.fileUpload,"onUpdate:g9s":a[6]||(a[6]=e=>l.formData.fileUpload=e),accept:"*",multiple:!0,readonly:"view"===l.type||!r.canEdit0},null,8,["g9s","readonly"])])),_:1})):y("",!0),h(s,{label:"备注",modelValue:l.formData.remark,"onUpdate:modelValue":a[7]||(a[7]=e=>l.formData.remark=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入备注",required:!1,"input-align":"left",readonly:"view"===l.type||!r.canEdit0},null,8,["modelValue","readonly"])])),_:1})])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","all-task-keys","before-submit","bpm-person-obj","onDraftClick","onSubmitClick","onAfterSubmit"])}],["__scopeId","data-v-26452589"]]))}}}));
