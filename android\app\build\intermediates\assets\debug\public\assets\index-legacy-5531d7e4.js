System.register(["./index-legacy-f817ae93.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var l,t,n,o,d,s,m,c,i,u,r,p,f,h;return{setters:[e=>{l=e.F,t=e.D},e=>{n=e._},e=>{o=e.Q,d=e.R,s=e.X,m=e.V,c=e.k,i=e.U,u=e.Y,r=e.S,p=e.W,f=e.F,h=e.B},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"comment-wp"},b={class:"one-line"},y={class:"form-info"},k={class:"comment-wp"},V={class:"textarea-wp"},N={class:"comment-wp"},v={class:"textarea-wp"},g={class:"comment-wp"},D={class:"form-table"},j={class:"check-input"},w={class:"comment-wp"},C={class:"textarea-wp"},x={class:"comment-wp"},T={class:"textarea-wp"},U={class:"footer-input"},P={class:"form-info"},F={class:"form-info"},L={class:"form-info"},O={class:"form-info"};e("default",n({name:"CB13",components:{FormTemplate:l,DocumentPart:t},emits:[],props:{},setup(e,{attrs:a,slots:l,emit:t}){},data:()=>({detailTable:[],attachmentDesc:"",defaultChecks:[{checkName:"check1",fieldName:"field3",label:"施工控制测量",child:["1、测量数据","2、数据分析及平差成果"]},{checkName:"check2",fieldName:"field4",label:"工程计量测量",child:["1、工程量计算表","2、断面图","3、其他"]},{checkName:"check3",fieldName:"field5",label:"地形测量",child:["1、测量数据","2、数据分析及成果（数据处理方法、断面图或地形图）"]},{checkName:"check4",fieldName:"field6",label:"施工期变形监测",child:["1、观测数据","2、数据分析及评价"]}]}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){setTimeout((()=>{this.setDefaultAttachText()}))},onBeforeSubmit({formData:e,detailParamList:a},l){return new Promise(((a,t)=>{try{if("submit"===l&&!(e.check1||e.check2||e.check3||e.check4))return this.$showNotify({type:"danger",message:"请选择附件类型!",duration:3e3}),t(!1),!1;a()}catch(n){t(n)}}))},setDefaultAttachText(){this.defaultChecks.forEach((e=>{"add"!==this.type&&this.$refs.FormTemplate.formData[e.fieldName]||(this.$refs.FormTemplate.formData[e.fieldName]=e.child.join("\n"))}))},onCheckChange(e,{checkName:a,fieldName:l,child:t}){e||(this.$refs.FormTemplate.formData[l]=t.join("\n"))}}},[["render",function(e,l,t,n,z,A){const S=o("van-field"),_=o("van-checkbox"),B=o("DocumentPart"),I=o("FormTemplate");return d(),s(I,{ref:"FormTemplate",nature:"测量","on-after-init":A.onAfterInit,"on-before-submit":A.onBeforeSubmit,"detail-table":z.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:z.attachmentDesc},{default:m((({formData:e,formTable:t,baseObj:n,uploadAccept:o,taskStart:s,taskComment2:U,taskComment3:P,taskComment4:F,taskComment5:L})=>[c(B,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:n.constructionDeptName,personLabel:"技术负责人：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",a,[i("div",b,[l[0]||(l[0]=i("span",{style:{"margin-left":"2em"}},"我方已完成",-1)),i("span",y,u(e.projectName),1),l[1]||(l[1]=i("span",null,"工程的施工测量工作，",-1)),l[2]||(l[2]=i("span",null,"经自检合格，请贵方审核。",-1))])]),i("div",k,[l[3]||(l[3]=i("div",null,"施测部位：",-1)),i("div",V,[c(S,{modelValue:e.field1,"onUpdate:modelValue":a=>e.field1=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),i("div",N,[l[4]||(l[4]=i("div",null,"施测说明：",-1)),i("div",v,[c(S,{modelValue:e.field2,"onUpdate:modelValue":a=>e.field2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),i("div",g,[l[5]||(l[5]=i("div",null,"附件：",-1)),i("div",D,[i("table",null,[i("thead",null,[i("tr",null,[(d(!0),r(f,null,p(z.defaultChecks||[],((a,l)=>(d(),r("th",{key:l},[i("div",j,[c(_,{modelValue:e[a.checkName],"onUpdate:modelValue":l=>e[a.checkName]=l,shape:"square",onChange:e=>A.onCheckChange(e,a),disabled:!s},{default:m((()=>[h(u(a.label),1)])),_:2},1032,["modelValue","onUpdate:modelValue","onChange","disabled"])])])))),128))])]),i("tbody",null,[i("tr",null,[(d(!0),r(f,null,p(z.defaultChecks||[],((a,l)=>(d(),r("td",{key:l},[c(S,{modelValue:e[a.fieldName],"onUpdate:modelValue":l=>e[a.fieldName]=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!(e[a.checkName]&&s)},null,8,["modelValue","onUpdate:modelValue","readonly"])])))),128))])])])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),c(B,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",w,[l[6]||(l[6]=i("div",null,"EPC总承包项目部意见：",-1)),i("div",C,[c(S,{modelValue:e.comment2,"onUpdate:modelValue":a=>e.comment2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!U},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),c(B,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",x,[l[7]||(l[7]=i("div",null,"监理机构审核意见：",-1)),i("div",T,[c(S,{modelValue:e.comment3,"onUpdate:modelValue":a=>e.comment3=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!P},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:m((({formData:e,formTable:a,baseObj:t,uploadAccept:n,taskStart:o,taskComment2:d,taskComment3:s,taskComment4:m,taskComment5:c})=>[i("div",U,[l[8]||(l[8]=i("span",null,"说明：本表一式",-1)),i("span",P,u(e.num1),1),l[9]||(l[9]=i("span",null,"份，由承包人填写，监理机构审核后，发包人",-1)),i("span",F,u(e.num2),1),l[10]||(l[10]=i("span",null,"份，监理机构",-1)),i("span",L,u(e.num3),1),l[11]||(l[11]=i("span",null,"份，承包人",-1)),i("span",O,u(e.num4),1),l[12]||(l[12]=i("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
