System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,t){"use strict";var a,l,n,s,o,d,m,i,r,c,p,u,f;return{setters:[e=>{a=e.F,l=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,d=e.X,m=e.V,i=e.k,r=e.U,c=e.S,p=e.W,u=e.F,f=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const t={class:"form-table"},b={class:"center"},y={class:"attachment-desc"},h={class:"footer-input"},D={class:"form-info"},g={class:"form-info"},j={class:"form-info"},v={class:"form-info"};e("default",n({name:"JL08",components:{FormTemplate:a,DocumentPart:l},emits:[],props:{},setup(e,{attrs:t,slots:a,emit:l}){},data:()=>({detailTable:[{},{},{},{},{}],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:t}){},onBeforeSubmit:({formData:e,detailParamList:t},a)=>new Promise(((e,t)=>{try{e()}catch(a){t(a)}}))}},[["render",function(e,a,l,n,w,k){const x=s("van-field"),P=s("DocumentPart"),V=s("FormTemplate");return o(),d(V,{ref:"FormTemplate",nature:"计通","on-after-init":k.onAfterInit,"on-before-submit":k.onBeforeSubmit,"detail-table":w.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:w.attachmentDesc},{default:m((({formData:e,formTable:l,baseObj:n,uploadAccept:s,taskStart:d,taskComment2:h,taskComment3:D,taskComment4:g})=>[i(P,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"总监理工程师/监理工程师：",labelWidth:"10em",disabled:!d},{default:m((()=>[a[2]||(a[2]=r("div",{style:{"text-indent":"2em"}}," 依据合同约定，经发包人批准，现决定对下列工作按计日工予以安排，请据以执行。 ",-1)),r("div",t,[r("table",null,[a[0]||(a[0]=r("thead",null,[r("tr",null,[r("th",{colspan:"1",rowspan:"1"},"序号"),r("th",{colspan:"1",rowspan:"1"},"工作项目或内容"),r("th",{colspan:"1",rowspan:"1"},"计划工作时间"),r("th",{colspan:"1",rowspan:"1"},"计价及付款方式"),r("th",{colspan:"1",rowspan:"1"},"备注")])],-1)),r("tbody",null,[(o(!0),c(u,null,p(l||[],((e,t)=>(o(),c("tr",{key:t},[r("td",b,f(t+1),1),r("td",null,f(e.field1),1),r("td",null,f(e.field2),1),r("td",null,f(e.field3),1),r("td",null,f(e.field4),1)])))),128))])])]),r("div",y,[a[1]||(a[1]=r("div",null,"附件：",-1)),i(x,{modelValue:e.attachmentDesc,"onUpdate:modelValue":t=>e.attachmentDesc=t,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2,__:[2]},1032,["deptValue","deptOptions","disabled"]),i(P,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"项目负责人：",labelWidth:"10em",disabled:!d},{default:m((()=>a[3]||(a[3]=[r("div",{class:"comment-wp"},[r("div",null,"我方将按通知执行。")],-1)]))),_:2,__:[3]},1032,["deptValue","deptOptions","disabled"])])),footer:m((({formData:e,formTable:t,baseObj:l,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:d,taskComment4:m})=>[r("div",h,[a[4]||(a[4]=r("span",null,"说明：1、本表一式",-1)),r("span",D,f(e.num1),1),a[5]||(a[5]=r("span",null,"份，由监理机构填写，承包人签署后，承包人",-1)),r("span",g,f(e.num2),1),a[6]||(a[6]=r("span",null,"份，监理机构",-1)),r("span",j,f(e.num3),1),a[7]||(a[7]=r("span",null,"份，发包人",-1)),r("span",v,f(e.num4),1)]),a[8]||(a[8]=r("div",{class:"footer-input"},[r("div",{style:{"text-indent":"3em"}}," 2、本表计价及付款方式填写“按合同计日工单价支付”或“双方协商” ")],-1))])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
