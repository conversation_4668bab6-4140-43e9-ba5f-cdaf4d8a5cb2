System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var l,n,t,s,o,d,c,m,u,p,r;return{setters:[e=>{l=e.F,n=e.D},e=>{t=e._},e=>{s=e.Q,o=e.R,d=e.X,c=e.V,m=e.k,u=e.U,p=e.B,r=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"one-line"},i={class:"check-wp"},h={class:"check-wp"},f={class:"check-wp"},k={class:"form-info"},g={class:"form-info"},b={class:"form-info"},V={class:"form-info"},y={class:"form-info"},C={class:"one-line"},v={class:"check-wp"},D={class:"check-wp"},_={class:"check-wp"},j={class:"attachment-desc"},U={class:"comment-wp"},w={class:"textarea-wp"},T={class:"footer-input"},F={class:"form-info"},x={class:"form-info"},P={class:"form-info"},N={class:"form-info"};e("default",t({name:"JL21",components:{FormTemplate:l,DocumentPart:n},emits:[],props:{},setup(e,{attrs:a,slots:l,emit:n}){},data:()=>({detailTable:[],attachmentDesc:"1、完工付款/最终结清申请单。\n2、审核计算资料。"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit({formData:e,detailParamList:a,taskStart:l},n){return new Promise(((a,t)=>{try{if("submit"===n&&l&&!e.check1&&!e.check2&&!e.check3)return this.$showNotify({type:"danger",message:"请选择类型",duration:3e3}),t(!1),!1;a()}catch(s){t(s)}}))},changeCheck1(e){e&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.check3=!1)},changeCheck2(e){e&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check3=!1)},changeCheck3(e){e&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check2=!1)}}},[["render",function(e,l,n,t,L,$){const q=s("van-checkbox"),O=s("van-field"),S=s("DocumentPart"),A=s("FormTemplate");return o(),d(A,{ref:"FormTemplate",nature:"付结","employer-target":!0,"on-after-init":$.onAfterInit,"on-before-submit":$.onBeforeSubmit,"detail-table":L.detailTable,"is-show-confirm1":!1,attachmentDesc:L.attachmentDesc},{default:c((({formData:e,formTable:n,baseObj:t,uploadAccept:s,taskStart:o,taskComment2:d,taskComment3:T,taskComment4:F})=>[m(S,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:t.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!o},{default:c((()=>[u("div",a,[l[3]||(l[3]=u("span",{style:{"padding-left":"2em"}},"经审核承包人的",-1)),u("div",i,[m(q,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!o,onChange:$.changeCheck1},{default:c((()=>l[0]||(l[0]=[p("完工付款申请")]))),_:2,__:[0]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[4]||(l[4]=u("span",null,"/",-1)),u("div",h,[m(q,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!o,onChange:$.changeCheck2},{default:c((()=>l[1]||(l[1]=[p("最终结清申请")]))),_:2,__:[1]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[5]||(l[5]=u("span",null,"/",-1)),u("div",f,[m(q,{modelValue:e.check3,"onUpdate:modelValue":a=>e.check3=a,shape:"square",disabled:!o,onChange:$.changeCheck3},{default:c((()=>l[2]||(l[2]=[p("临时付款申请")]))),_:2,__:[2]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[6]||(l[6]=u("span",null,"（ ",-1)),u("span",k,r(e.constructionName),1),l[7]||(l[7]=u("span",null," [ ",-1)),u("span",g,r(e.field1),1),l[8]||(l[8]=u("span",null," ] 付结",-1)),u("span",b,r(e.field2),1),l[9]||(l[9]=u("span",null,"号），",-1)),l[10]||(l[10]=u("span",null,"应支付",-1)),l[11]||(l[11]=u("span",null,"给承包人的金额共计为",-1)),l[12]||(l[12]=u("span",null,"（大写）",-1)),u("span",V,r(e.field3),1),l[13]||(l[13]=u("span",null,"（小写",-1)),u("span",y,r(e.field4),1),l[14]||(l[14]=u("span",null,"）。",-1))]),u("div",C,[l[18]||(l[18]=u("span",{style:{"padding-left":"2em"}},"请贵方在收到",-1)),u("div",v,[m(q,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!o,onChange:$.changeCheck1},{default:c((()=>l[15]||(l[15]=[p("完工付款证书")]))),_:2,__:[15]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[19]||(l[19]=u("span",null,"/",-1)),u("div",D,[m(q,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!o,onChange:$.changeCheck2},{default:c((()=>l[16]||(l[16]=[p("最终结清证书")]))),_:2,__:[16]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[20]||(l[20]=u("span",null,"/",-1)),u("div",_,[m(q,{modelValue:e.check3,"onUpdate:modelValue":a=>e.check3=a,shape:"square",disabled:!o,onChange:$.changeCheck3},{default:c((()=>l[17]||(l[17]=[p("临时付款证书")]))),_:2,__:[17]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[21]||(l[21]=u("span",null,"后",-1)),l[22]||(l[22]=u("span",null,"按合同约定完成审批， ",-1)),l[23]||(l[23]=u("span",null,"并将上述工程金额支付给承包人。",-1))]),u("div",j,[l[24]||(l[24]=u("div",null,"附件：",-1)),m(O,{modelValue:e.attachmentDesc,"onUpdate:modelValue":a=>e.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),m(S,{deptLabel:"发包人：",deptProp:"employerName",deptValue:e.employerName,deptOptions:t.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!o},{default:c((()=>[u("div",U,[l[25]||(l[25]=u("div",null,"发包人审定意见：",-1)),u("div",w,[m(O,{modelValue:e.comment4,"onUpdate:modelValue":a=>e.comment4=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!F},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:c((({formData:e,formTable:a,baseObj:n,uploadAccept:t,taskStart:s,taskComment2:o,taskComment3:d,taskComment4:c})=>[u("div",T,[l[26]||(l[26]=u("span",null,"说明：本证书一式",-1)),u("span",F,r(e.num1),1),l[27]||(l[27]=u("span",null,"份，由监理机构填写，监理机构",-1)),u("span",x,r(e.num2),1),l[28]||(l[28]=u("span",null,"份，发包人",-1)),u("span",P,r(e.num3),1),l[29]||(l[29]=u("span",null,"份，承包人",-1)),u("span",N,r(e.num4),1),l[30]||(l[30]=u("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
