System.register(["./index-legacy-f817ae93.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var t,l,n,s,o,m,i,d,r,c;return{setters:[e=>{t=e.F,l=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,m=e.X,i=e.V,d=e.k,r=e.U,c=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"one-line"},p={class:"form-info"},u={class:"check-wp"},f={class:"form-info"},h={class:"form-info"},b={class:"check-wp"},g={class:"form-info"},k={class:"attachment-desc"},y={class:"comment-wp"},D={class:"textarea-wp"},V={class:"footer-input"},v={class:"form-info"},C={class:"form-info"},j={class:"form-info"},T={class:"form-info"};e("default",n({name:"CB29",components:{FormTemplate:t,DocumentPart:l},emits:[],props:{},setup(e,{attrs:a,slots:t,emit:l}){},data:()=>({detailTable:[],attachmentDesc:"索赔报告，主要内容包括：\n1、索赔事件简述及索赔要求。\n2、索赔依据。\n3、索赔计算。\n4、索赔证明材料。"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit({formData:e,detailParamList:a,taskStart:t},l){return new Promise(((a,n)=>{try{if("submit"===l&&t){if(!e.check1&&!e.check2)return this.$showNotify({type:"danger",message:"请完善索赔信息!",duration:3e3}),n(!1),!1;if(e.check1&&(!e.field2||!e.field3))return this.$showNotify({type:"danger",message:"请完善索赔信息!",duration:3e3}),n(!1),!1;if(e.check2&&!e.field4)return this.$showNotify({type:"danger",message:"请完善索赔信息!",duration:3e3}),n(!1),!1}a()}catch(s){n(s)}}))},changeCheck1(e){e&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.field4="",this.$nextTick((()=>{var e;null===(e=this.$refs.FormTemplate.$refs.form)||void 0===e||e.resetValidation()})))},changeCheck2(e){e&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.field2="",this.$refs.FormTemplate.formData.field3="",this.$nextTick((()=>{var e;null===(e=this.$refs.FormTemplate.$refs.form)||void 0===e||e.resetValidation()})))}}},[["render",function(e,t,l,n,w,$){const F=s("van-checkbox"),x=s("van-field"),N=s("DocumentPart"),P=s("FormTemplate");return o(),m(P,{ref:"FormTemplate",nature:"赔报","on-after-init":$.onAfterInit,"on-before-submit":$.onBeforeSubmit,"detail-table":w.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:w.attachmentDesc},{default:i((({formData:e,formTable:l,baseObj:n,uploadAccept:s,taskStart:o,taskComment2:m,taskComment3:V,taskComment4:v,taskComment5:C})=>[d(N,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:n.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!o},{default:i((()=>[r("div",a,[t[0]||(t[0]=r("span",{style:{"padding-left":"2em"}},"根据有关规定和施工合同的约定，我方对",-1)),r("span",p,c(e.field1),1),t[1]||(t[1]=r("span",null,"事件，申请",-1)),r("div",u,[d(F,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!0,onChange:$.changeCheck1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),t[2]||(t[2]=r("span",null,"赔偿金额为",-1)),t[3]||(t[3]=r("span",null,"（大写）",-1)),r("span",f,c(e.field2),1),t[4]||(t[4]=r("span",null,"元（小写",-1)),r("span",h,c(e.field3),1),t[5]||(t[5]=r("span",null,"元）",-1)),t[6]||(t[6]=r("span",null,"/",-1)),r("div",b,[d(F,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!0,onChange:$.changeCheck2},null,8,["modelValue","onUpdate:modelValue","onChange"])]),t[7]||(t[7]=r("span",null,"索赔工期",-1)),r("span",g,c(e.field4||""),1),t[8]||(t[8]=r("span",null,"天，",-1)),t[9]||(t[9]=r("span",null,"请贵方审核。",-1))]),r("div",k,[t[10]||(t[10]=r("div",null,"附件：",-1)),d(x,{modelValue:e.attachmentDesc,"onUpdate:modelValue":a=>e.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),d(N,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!o},{default:i((()=>[r("div",y,[t[11]||(t[11]=r("div",null,"EPC总承包项目部意见：",-1)),r("div",D,[d(x,{modelValue:e.comment2,"onUpdate:modelValue":a=>e.comment2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!m},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),d(N,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!o},{default:i((()=>t[12]||(t[12]=[r("div",{class:"comment-wp"},[r("div",null,"监理机构将另行签发审核意见。")],-1)]))),_:2,__:[12]},1032,["deptValue","deptOptions","disabled"])])),footer:i((({formData:e,formTable:a,baseObj:l,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:m,taskComment4:i,taskComment5:d})=>[r("div",V,[t[13]||(t[13]=r("span",null,"注：本表一式",-1)),r("span",v,c(e.num1),1),t[14]||(t[14]=r("span",null,"份，由承包人填写，监理机构审核后，随同批复意见发包人",-1)),r("span",C,c(e.num2),1),t[15]||(t[15]=r("span",null,"份，监理机构",-1)),r("span",j,c(e.num3),1),t[16]||(t[16]=r("span",null,"份，承包人",-1)),r("span",T,c(e.num4),1),t[17]||(t[17]=r("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
