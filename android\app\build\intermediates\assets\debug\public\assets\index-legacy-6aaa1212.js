System.register(["./index-legacy-09188690.js","./index-legacy-b580af71.js","./FormItemPicker-legacy-fd45c24d.js","./FormItemDate-legacy-c4422d42.js","./FormItemCalendar-legacy-ea787ea1.js","./FormItemCascader-legacy-b386877e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemCoord-legacy-e6ddc9b7.js","./index-legacy-645a3645.js","./common-legacy-e7aae0fd.js","./verder-legacy-e6127216.js","./vant-legacy-b51a9379.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./array-legacy-2920c097.js","./validate-legacy-4e8f0db9.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js"],(function(e,a){"use strict";var r,t,o,l,m,i,n,d,s,p,u,f,c,y,D,b,h,C,v,g,F;return{setters:[e=>{r=e.h,t=e._},e=>{o=e.F},e=>{l=e.F},e=>{m=e.F},e=>{i=e.F},e=>{n=e.F},e=>{d=e.F},e=>{s=e.F},e=>{p=e.U},e=>{u=e.a},e=>{f=e.Q,c=e.R,y=e.X,D=e.V,b=e.k,h=e.S,C=e.Z,v=e.F,g=e.B,F=e.U},null,null,null,null,null,null,null,null,null,null],execute:function(){e("default",t({name:"QualityThreatNew",components:{FlowForm:o,FormItemPicker:l,FormItemDate:m,FormItemCalendar:i,FormItemPerson:d,FormItemCoord:s,UploadFiles:p,FormItemCascader:n},props:{},emits:[],setup(e,{attrs:a,slots:r,emit:t}){},data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",entityName:"QualityProblem",formKey:"QualityThreatNew",modelKey:"quality_hidden_danger_flow_new",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-quality/quality/form/query",submit:"/cybereng-quality/form/commit"},formData:{type:"",portalId:"",subProjectId:"",subProjectName:"",workAreaId:"",workAreaName:"",overdueState:"",problemName:"",problemCode:"",rectifyState:"",problemLevel:"",problemSource:"",rectifyDate:"",problemCategory:"",problemContent:"",beforeFileToken:"",beforeFileTokenImage:[],beforeFiles:1,constructionPost:"",longitude:"",latitude:"",problemReportor:"",problemReportorFullname:"",problemReportorDeptName:"",problemReportorDeptCode:"",otherProblemReportorFullname:"",initiateDate:"",isProblemConfirm:!1,isSupervision:!1,problemChecker:"",problemCheckerFullname:"",problemCheckerDeptName:"",problemCheckerDeptCode:"",problemRectifier:"",problemRectifierFullname:"",problemRectifierDeptName:"",problemRectifierDeptCode:"",problemRectifyConfirmer:"",problemRectifyConfirmerFullname:"",problemRectifyConfirmerDeptName:"",problemRectifyConfirmerDeptCode:"",problemSupervisor:"",problemSupervisorFullname:"",problemSupervisorDeptName:"",problemSupervisorDeptCode:"",problemRectifyApprover:"",problemRectifyApproverFullname:"",problemRectifyApproverDeptName:"",problemRectifyApproverDeptCode:"",rectifyComment:"",rectifyMeasures:"",rectifySituation:"",afterFileToken:"",afterFileTokenImage:[],afterFiles:1,finishDate:"",safetyCheckId:""},hiddenDangerSourceList:[]}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},siteList(){let e=this.$store.SITE_LIST.find((e=>e.slots.id==this.formData.subProjectId)),a=[];return e&&(a=e.children.map((e=>({text:e.title,value:e.slots.id})))),a},canEdit0(){return"add"===this.type||"UserTask_0"===this.taskKey},canEdit2(){return"execute"===this.type&&"UserTask_2"===this.taskKey},riskSourceSearch(){return{subProjectId:this.formData.subProjectId}},minDate(){const e=(new Date).getFullYear();return new Date(e-1,0,1)}},watch:{},async created(){await this.getHiddenLevelList()},mounted(){this.initForm()},methods:{copyCallBack(e){this.formData={...e},["problemContent","beforeFileToken","riskSource","riskSourceName","rectifyComment","dispatchingCompany","rectifyMeasures","rectifySituation","afterFileToken","latitude","longitude"].forEach((e=>{this.formData[e]=""}))},async initForm(){if("add"===this.type){if(this.formData.portalId=this.portalId,1!=this.portal.type){const e=this.subProjectList.find((e=>e.portalId==this.portal.id));this.formData.subProjectId=null==e?void 0:e.id,this.formData.subProjectName=null==e?void 0:e.nodeName}const{userName:e="",userFullname:a="",orgList:r=[]}=this.user||{},t=r.find((e=>{var a;return e.portalId==(null===(a=this.portal)||void 0===a?void 0:a.id)}))||r[0],o=(null==t?void 0:t.name)||"",l=(null==t?void 0:t.orgNo)||"";this.formData.problemReportor=e,this.formData.problemReportorFullname=a,this.formData.problemReportorDeptName=o,this.formData.problemReportorDeptCode=l,this.formData.problemSupervisor=e,this.formData.problemSupervisorFullname=a,this.formData.problemSupervisorDeptName=o,this.formData.problemSupervisorDeptCode=l}else this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:a=[],entityObject:r}=e;this.detailParamList=a,this.formData={...this.formData,...r}})),this.canEdit2&&setTimeout((()=>{document.getElementById("hidden-rectify").scrollIntoView()}),500)}))},setHiddenDangerChecker(){this.formData.problemRectifyConfirmer=this.formData.problemChecker,this.formData.problemRectifyConfirmerFullname=this.formData.problemCheckerFullname,this.formData.problemRectifyConfirmerDeptName=this.formData.problemCheckerDeptName,this.formData.problemRectifyConfirmerDeptCode=this.formData.problemCheckerDeptCode},async onDraft(){try{const e={...this.formData};this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{await this.$refs.form.validate();const e={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},async updateFiles(){return new Promise((async(e,a)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),e()}catch(r){a()}}))},afterSubmit(e,a){this.updateFiles(),"submit"===e&&"UserTask_1"==this.taskKey&&function(e){r({url:"/cybereng-quality/quality/problem/delayTask",method:"get",params:e})}({id:this.formData.id})},handleSubProjectChange(e={}){this.formData.workAreaId="",this.formData.workAreaName="",this.formData.riskSource="",this.formData.riskSourceName=""},onDangerConfirmChange(e){e||(this.formData.problemChecker="",this.formData.problemCheckerFullname="",this.formData.problemCheckerDeptName="",this.formData.problemCheckerDeptCode="",this.formData.problemRectifyConfirmer="",this.formData.problemRectifyConfirmerFullname="",this.formData.problemRectifyConfirmerDeptName="",this.formData.problemRectifyConfirmerDeptCode="")},onSupervisionChange(e){e||(this.formData.problemRectifyApprover="",this.formData.problemRectifyApproverFullname="",this.formData.problemRectifyApproverDeptName="",this.formData.problemRectifyApproverDeptCode="")},async getHiddenLevelList(){try{let e=await u({refDirectoryTreeName:"检查表单"});this.hiddenDangerSourceList=e[0].children||[]}catch(e){this.hiddenDangerSourceList=[]}}}},[["render",function(e,a,r,t,o,l){const m=f("van-field"),i=f("FormItemPicker"),n=f("FormItemCascader"),d=f("FormItemCalendar"),s=f("FormItemCoord"),p=f("van-cell-group"),u=f("UploadFiles"),k=f("FormItemPerson"),N=f("van-radio"),R=f("van-radio-group"),w=f("van-form"),U=f("FlowForm");return c(),y(U,{ref:"FlowForm","model-key":o.modelKey,"form-key":o.formKey,"entity-name":o.entityName,"detail-param-list":o.detailParamList,"detail-entity-name-list":o.detailEntityNameList,onDraftClick:l.onDraft,onSubmitClick:l.onSubmit,onAfterSubmit:l.afterSubmit,onCopyCallBack:l.copyCallBack},{default:D((()=>[b(w,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:D((()=>[b(p,{border:!1},{default:D((()=>["view"===o.type?(c(),h(v,{key:0},[o.formData.problemName?(c(),y(m,{key:0,modelValue:o.formData.problemName,"onUpdate:modelValue":a[0]||(a[0]=e=>o.formData.problemName=e),label:"问题名称",placeholder:"自动生成",readonly:""},null,8,["modelValue"])):C("",!0),o.formData.problemCode?(c(),y(m,{key:1,modelValue:o.formData.problemCode,"onUpdate:modelValue":a[1]||(a[1]=e=>o.formData.problemCode=e),label:"问题编号",placeholder:"审批完成后自动生成",readonly:""},null,8,["modelValue"])):C("",!0)],64)):C("",!0),b(i,{label:"子工程",value:o.formData.subProjectId,"onUpdate:value":a[2]||(a[2]=e=>o.formData.subProjectId=e),text:o.formData.subProjectName,"onUpdate:text":a[3]||(a[3]=e=>o.formData.subProjectName=e),columns:[...l.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:1!=l.portal.type||"view"===o.type||!l.canEdit0,onChange:l.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),o.formData.workAreaId||l.canEdit0?(c(),y(i,{key:1,label:"施工工区",value:o.formData.workAreaId,"onUpdate:value":a[4]||(a[4]=e=>o.formData.workAreaId=e),text:o.formData.workAreaName,"onUpdate:text":a[5]||(a[5]=e=>o.formData.workAreaName=e),columns:[...l.siteList],"columns-field-names":{text:"text",value:"value",children:"none"},required:"",rules:[{required:!0,message:"请选择施工工区"}],title:"选择施工工区",readonly:"view"===o.type||!l.canEdit0},null,8,["value","text","columns","readonly"])):C("",!0),o.formData.problemSource||l.canEdit0?(c(),y(n,{key:2,label:"检查来源",value:o.formData.problemSource,"onUpdate:value":a[6]||(a[6]=e=>o.formData.problemSource=e),columns:[...o.hiddenDangerSourceList],"columns-field-names":{text:"nodeName",value:"id",children:"children"},"trigger-change-mode":!0,title:"选择检查来源",readonly:"view"===o.type||!l.canEdit0},null,8,["value","columns","readonly"])):C("",!0),o.formData.problemLevel||l.canEdit0?(c(),y(i,{key:3,label:"问题等级",value:o.formData.problemLevel,"onUpdate:value":a[7]||(a[7]=e=>o.formData.problemLevel=e),"dict-name":"hidden_danger_level",title:"选择问题等级",readonly:"view"===o.type||!l.canEdit0},null,8,["value","readonly"])):C("",!0),b(i,{label:"问题分类",value:o.formData.problemCategory,"onUpdate:value":a[8]||(a[8]=e=>o.formData.problemCategory=e),"dict-name":"hidden_danger_category",title:"选择问题分类",required:"",rules:[{required:!0,message:"请选择问题分类"}],readonly:"view"===o.type||!l.canEdit0},null,8,["value","readonly"]),b(d,{label:"整改期限",value:o.formData.rectifyDate,"onUpdate:value":a[9]||(a[9]=e=>o.formData.rectifyDate=e),"show-confirm":!1,title:"选择整改期限",required:"",rules:[{required:!0,message:"请选择整改期限"}],readonly:"view"===o.type||!l.canEdit0},null,8,["value","readonly"]),o.formData.longitude||l.canEdit0?(c(),y(s,{key:4,label:"定位",longitude:o.formData.longitude,"onUpdate:longitude":a[10]||(a[10]=e=>o.formData.longitude=e),latitude:o.formData.latitude,"onUpdate:latitude":a[11]||(a[11]=e=>o.formData.latitude=e),title:"选择定位",readonly:"view"===o.type||!l.canEdit0},null,8,["longitude","latitude","readonly"])):C("",!0)])),_:1}),b(p,{border:!1},{default:D((()=>[b(m,{label:"描述内容",modelValue:o.formData.problemContent,"onUpdate:modelValue":a[12]||(a[12]=e=>o.formData.problemContent=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入描述内容",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入描述内容"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit0},null,8,["modelValue","readonly"]),o.formData.beforeFileToken&&o.formData.beforeFiles||l.canEdit0?(c(),y(m,{key:0,label:"附件照片&视频","label-align":"top","input-align":"left"},{input:D((()=>[b(u,{ref:"beforeFiles",g9s:o.formData.beforeFileToken,"onUpdate:g9s":a[13]||(a[13]=e=>o.formData.beforeFileToken=e),files:o.formData.beforeFiles,"onUpdate:files":a[14]||(a[14]=e=>o.formData.beforeFiles=e),accept:"image/*,video/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===o.type||!l.canEdit0},null,8,["g9s","files","readonly"])])),_:1})):C("",!0)])),_:1}),l.canEdit0?(c(),y(p,{key:0,border:!1},{default:D((()=>[b(k,{label:"上报人",userName:o.formData.problemReportor,"onUpdate:userName":a[15]||(a[15]=e=>o.formData.problemReportor=e),userFullname:o.formData.problemReportorFullname,"onUpdate:userFullname":a[16]||(a[16]=e=>o.formData.problemReportorFullname=e),deptName:o.formData.problemReportorDeptName,"onUpdate:deptName":a[17]||(a[17]=e=>o.formData.problemReportorDeptName=e),deptCode:o.formData.problemReportorDeptCode,"onUpdate:deptCode":a[18]||(a[18]=e=>o.formData.problemReportorDeptCode=e),title:"选择上报人",required:"",rules:[{required:!0,message:"请选择上报人"}],readonly:!0},null,8,["userName","userFullname","deptName","deptCode"]),b(m,{modelValue:o.formData.otherProblemReportorFullname,"onUpdate:modelValue":a[19]||(a[19]=e=>o.formData.otherProblemReportorFullname=e),label:"检查人员",placeholder:"同行人姓名请逗号隔开",readonly:"view"===o.type||!l.canEdit0},null,8,["modelValue","readonly"]),b(d,{label:"检查日期",value:o.formData.initiateDate,"onUpdate:value":a[20]||(a[20]=e=>o.formData.initiateDate=e),"show-confirm":!1,required:!0,"min-date":l.minDate,readonly:"view"===o.type||!l.canEdit0,rules:[{required:!0,message:"请选择检查日期"}]},null,8,["value","min-date","readonly"]),"UserTask_3"===o.taskKey||"UserTask_4"===o.taskKey||"UserTask_5"===o.taskKey||"view"===o.type?(c(),y(m,{key:0,modelValue:o.formData.overdueState,"onUpdate:modelValue":a[21]||(a[21]=e=>o.formData.overdueState=e),label:"逾期情况",placeholder:"",readonly:""},null,8,["modelValue"])):C("",!0),b(k,{label:"问题整改人",userName:o.formData.problemRectifier,"onUpdate:userName":a[22]||(a[22]=e=>o.formData.problemRectifier=e),userFullname:o.formData.problemRectifierFullname,"onUpdate:userFullname":a[23]||(a[23]=e=>o.formData.problemRectifierFullname=e),deptName:o.formData.problemRectifierDeptName,"onUpdate:deptName":a[24]||(a[24]=e=>o.formData.problemRectifierDeptName=e),deptCode:o.formData.problemRectifierDeptCode,"onUpdate:deptCode":a[25]||(a[25]=e=>o.formData.problemRectifierDeptCode=e),title:"选择问题整改人",required:"",rules:[{required:!0,message:"请选择问题整改人"}],readonly:"view"===o.type||!l.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),b(m,{name:"radio",label:"是否问题确认"},{input:D((()=>[b(R,{modelValue:o.formData.isProblemConfirm,"onUpdate:modelValue":a[26]||(a[26]=e=>o.formData.isProblemConfirm=e),direction:"horizontal",disabled:"view"===o.type||!l.canEdit0,onChange:l.onDangerConfirmChange},{default:D((()=>[b(N,{name:!0},{default:D((()=>a[49]||(a[49]=[g("是")]))),_:1,__:[49]}),b(N,{name:!1},{default:D((()=>a[50]||(a[50]=[g("否")]))),_:1,__:[50]})])),_:1},8,["modelValue","disabled","onChange"])])),_:1}),o.formData.isProblemConfirm?(c(),y(k,{key:1,label:"问题确认人",userName:o.formData.problemChecker,"onUpdate:userName":a[27]||(a[27]=e=>o.formData.problemChecker=e),userFullname:o.formData.problemCheckerFullname,"onUpdate:userFullname":a[28]||(a[28]=e=>o.formData.problemCheckerFullname=e),deptName:o.formData.problemCheckerDeptName,"onUpdate:deptName":a[29]||(a[29]=e=>o.formData.problemCheckerDeptName=e),deptCode:o.formData.problemCheckerDeptCode,"onUpdate:deptCode":a[30]||(a[30]=e=>o.formData.problemCheckerDeptCode=e),title:"选择问题确认人",required:"",rules:[{required:!0,message:"请选择问题确认人"}],readonly:"view"===o.type||!l.canEdit0,onChange:l.setHiddenDangerChecker},null,8,["userName","userFullname","deptName","deptCode","readonly","onChange"])):C("",!0),o.formData.isProblemConfirm?(c(),y(k,{key:2,label:"整改确认人",userName:o.formData.problemRectifyConfirmer,"onUpdate:userName":a[31]||(a[31]=e=>o.formData.problemRectifyConfirmer=e),userFullname:o.formData.problemRectifyConfirmerFullname,"onUpdate:userFullname":a[32]||(a[32]=e=>o.formData.problemRectifyConfirmerFullname=e),deptName:o.formData.problemRectifyConfirmerDeptName,"onUpdate:deptName":a[33]||(a[33]=e=>o.formData.problemRectifyConfirmerDeptName=e),deptCode:o.formData.problemRectifyConfirmerDeptCode,"onUpdate:deptCode":a[34]||(a[34]=e=>o.formData.problemRectifyConfirmerDeptCode=e),title:"选择整改确认人",placeholder:"自动填充(同问题确认人)",required:"",rules:[{required:!0,message:"请选择整改确认人"}],readonly:""},null,8,["userName","userFullname","deptName","deptCode"])):C("",!0),b(k,{label:"问题审核人",userName:o.formData.problemSupervisor,"onUpdate:userName":a[35]||(a[35]=e=>o.formData.problemSupervisor=e),userFullname:o.formData.problemSupervisorFullname,"onUpdate:userFullname":a[36]||(a[36]=e=>o.formData.problemSupervisorFullname=e),deptName:o.formData.problemSupervisorDeptName,"onUpdate:deptName":a[37]||(a[37]=e=>o.formData.problemSupervisorDeptName=e),deptCode:o.formData.problemSupervisorDeptCode,"onUpdate:deptCode":a[38]||(a[38]=e=>o.formData.problemSupervisorDeptCode=e),title:"选择问题审核人",required:"",rules:[{required:!0,message:"请选择问题审核人"}],readonly:"view"===o.type||!l.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),b(m,{name:"radio",label:"是否监理复核"},{input:D((()=>[b(R,{modelValue:o.formData.isSupervision,"onUpdate:modelValue":a[39]||(a[39]=e=>o.formData.isSupervision=e),direction:"horizontal",disabled:"view"===o.type||!l.canEdit0,onChange:l.onSupervisionChange},{default:D((()=>[b(N,{name:!0},{default:D((()=>a[51]||(a[51]=[g("是")]))),_:1,__:[51]}),b(N,{name:!1},{default:D((()=>a[52]||(a[52]=[g("否")]))),_:1,__:[52]})])),_:1},8,["modelValue","disabled","onChange"])])),_:1}),o.formData.isSupervision?(c(),y(k,{key:3,label:"问题复核人",userName:o.formData.problemRectifyApprover,"onUpdate:userName":a[40]||(a[40]=e=>o.formData.problemRectifyApprover=e),userFullname:o.formData.problemRectifyApproverFullname,"onUpdate:userFullname":a[41]||(a[41]=e=>o.formData.problemRectifyApproverFullname=e),deptName:o.formData.problemRectifyApproverDeptName,"onUpdate:deptName":a[42]||(a[42]=e=>o.formData.problemRectifyApproverDeptName=e),deptCode:o.formData.problemRectifyApproverDeptCode,"onUpdate:deptCode":a[43]||(a[43]=e=>o.formData.problemRectifyApproverDeptCode=e),title:"选择隐问题核人",required:"",rules:[{required:!0,message:"请选择问题复核人"}],readonly:"view"===o.type||!l.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"])):C("",!0)])),_:1})):C("",!0),(o.formData.rectifyComment||"execute"===o.type&&"UserTask_1"===o.taskKey)&&o.formData.isProblemConfirm?(c(),y(p,{key:1,border:!1},{default:D((()=>[b(m,{label:"整改要求及处理意见",modelValue:o.formData.rectifyComment,"onUpdate:modelValue":a[44]||(a[44]=e=>o.formData.rectifyComment=e),rows:"4",autosize:"",type:"textarea","label-width":"140","label-align":"top",placeholder:"请输入","input-align":"left",readonly:"view"===o.type||!("execute"===o.type&&"UserTask_1"===o.taskKey)},null,8,["modelValue","readonly"])])),_:1})):C("",!0),"view"===o.type&&o.formData.rectifyMeasures||o.taskKey&&"UserTask_0"!==o.taskKey&&"UserTask_1"!==o.taskKey?(c(),h(v,{key:2},[a[53]||(a[53]=F("div",{id:"hidden-rectify",class:"h-[50px] px-[18px] flex items-center bg-[#fff] my-[8px]"}," 问题整改 ",-1)),b(p,{border:!1},{default:D((()=>[b(m,{label:"整改措施",modelValue:o.formData.rectifyMeasures,"onUpdate:modelValue":a[45]||(a[45]=e=>o.formData.rectifyMeasures=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改措施",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请填写整改措施"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit2},null,8,["modelValue","readonly"]),b(m,{label:"整改情况",modelValue:o.formData.rectifySituation,"onUpdate:modelValue":a[46]||(a[46]=e=>o.formData.rectifySituation=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改情况",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请填写整改情况"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit2},null,8,["modelValue","readonly"]),l.canEdit2||o.formData.afterFiles&&o.formData.afterFileToken?(c(),y(m,{key:0,label:"附件照片&视频","label-align":"top","input-align":"left"},{input:D((()=>[b(u,{ref:"afterFiles",g9s:o.formData.afterFileToken,"onUpdate:g9s":a[47]||(a[47]=e=>o.formData.afterFileToken=e),files:o.formData.afterFiles,"onUpdate:files":a[48]||(a[48]=e=>o.formData.afterFiles=e),accept:"image/*,video/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===o.type||!l.canEdit2},null,8,["g9s","files","readonly"])])),_:1})):C("",!0)])),_:1})],64)):C("",!0)])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit","onCopyCallBack"])}]]))}}}));
