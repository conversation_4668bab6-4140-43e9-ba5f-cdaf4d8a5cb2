System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,l){"use strict";var a,t,n,o,s,d,m,i,p,r;return{setters:[e=>{a=e.F,t=e.D},e=>{n=e._},e=>{o=e.Q,s=e.R,d=e.X,m=e.V,i=e.k,p=e.U,r=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const l={class:"comment-wp"},u={style:{"text-indent":"2em"}},c={class:"form-info"},f={class:"form-info"},y={class:"form-info"},b={class:"comment-wp"},v={class:"textarea-wp"},V={class:"comment-wp"},h={class:"textarea-wp"},w={class:"comment-wp"},g={class:"textarea-wp"},x={class:"comment-wp"},D={class:"textarea-wp"},j={class:"comment-wp"},U={class:"textarea-wp"},k={class:"attachment-desc"},P={class:"footer-input"},z={class:"form-info"},L={class:"form-info"},T={class:"form-info"},C={class:"form-info"};e("default",n({name:"JL12",components:{FormTemplate:a,DocumentPart:t},emits:[],props:{},setup(e,{attrs:l,slots:a,emit:t}){},data:()=>({detailTable:[],attachmentDesc:"1、变更项目清单（含估算工程量）及说明\n2、设计文件、施工图纸（若有）\n3、其他变更依据"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:l}){},onBeforeSubmit:({formData:e,detailParamList:l},a)=>new Promise(((e,l)=>{try{e()}catch(a){l(a)}}))}},[["render",function(e,a,t,n,F,N){const O=o("van-field"),_=o("DocumentPart"),S=o("FormTemplate");return s(),d(S,{ref:"FormTemplate",nature:"变指","on-after-init":N.onAfterInit,"on-before-submit":N.onBeforeSubmit,"detail-table":F.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:F.attachmentDesc},{default:m((({formData:e,formTable:t,baseObj:n,uploadAccept:o,taskStart:s,taskComment2:d,taskComment3:P,taskComment4:z})=>[i(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!s},{default:m((()=>[p("div",l,[p("div",u,[a[0]||(a[0]=p("span",null,"现决定对本合同项目作如下变更或调整，贵方应根据本指示于",-1)),p("span",c,r(e.field1),1),a[1]||(a[1]=p("span",null,"年",-1)),p("span",f,r(e.field2),1),a[2]||(a[2]=p("span",null,"月",-1)),p("span",y,r(e.field3),1),a[3]||(a[3]=p("span",null,"日前提交相应的",-1)),a[4]||(a[4]=p("span",null,"施工措施计划和变更",-1)),a[5]||(a[5]=p("span",null,"报价。",-1))])]),p("div",b,[a[6]||(a[6]=p("div",null,"变更项目名称：",-1)),p("div",v,[i(O,{modelValue:e.field5,"onUpdate:modelValue":l=>e.field5=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),p("div",V,[a[7]||(a[7]=p("div",null,"变更内容简述：",-1)),p("div",h,[i(O,{modelValue:e.field6,"onUpdate:modelValue":l=>e.field6=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),p("div",w,[a[8]||(a[8]=p("div",null,"变更工程量估计：",-1)),p("div",g,[i(O,{modelValue:e.field7,"onUpdate:modelValue":l=>e.field7=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),p("div",x,[a[9]||(a[9]=p("div",null,"变更技术要求：",-1)),p("div",D,[i(O,{modelValue:e.field8,"onUpdate:modelValue":l=>e.field8=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),p("div",j,[a[10]||(a[10]=p("div",null,"变更进度要求：",-1)),p("div",U,[i(O,{modelValue:e.field9,"onUpdate:modelValue":l=>e.field9=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),p("div",k,[a[11]||(a[11]=p("div",null,"附件：",-1)),i(O,{modelValue:e.attachmentDesc,"onUpdate:modelValue":l=>e.attachmentDesc=l,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),i(_,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!s},{default:m((()=>a[12]||(a[12]=[p("div",{class:"comment-wp"},[p("div",{style:{height:"30px"}})],-1)]))),_:2,__:[12]},1032,["deptValue","deptOptions","disabled"])])),footer:m((({formData:e,formTable:l,baseObj:t,uploadAccept:n,taskStart:o,taskComment2:s,taskComment3:d,taskComment4:m})=>[p("div",P,[a[13]||(a[13]=p("span",null,"说明：本表一式",-1)),p("span",z,r(e.num1),1),a[14]||(a[14]=p("span",null,"份，由监理机构填写，承包人签收后，承包人",-1)),p("span",L,r(e.num2),1),a[15]||(a[15]=p("span",null,"份，监理机构",-1)),p("span",T,r(e.num3),1),a[16]||(a[16]=p("span",null,"份，发包人",-1)),p("span",C,r(e.num4),1),a[17]||(a[17]=p("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
