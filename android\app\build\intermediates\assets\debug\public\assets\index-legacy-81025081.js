System.register(["./index-legacy-b580af71.js","./FormItemSection-legacy-66423754.js","./FormItemPicker-legacy-fd45c24d.js","./FormItemDate-legacy-c4422d42.js","./FormItemCalendar-legacy-ea787ea1.js","./FormItemCascader-legacy-b386877e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemCoord-legacy-e6ddc9b7.js","./index-legacy-645a3645.js","./index-legacy-09188690.js","./wbsUtil-legacy-542349ac.js","./formMix-legacy-ca767df5.js","./verder-legacy-e6127216.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./array-legacy-2920c097.js","./validate-legacy-4e8f0db9.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js"],(function(e,a){"use strict";var t,r,o,l,s,i,n,d,m,u,p,c,f,h,y,g,D,b,v,U;return{setters:[e=>{t=e.F},e=>{r=e._},e=>{o=e.F},e=>{l=e.F},e=>{s=e.F},e=>{i=e.F},e=>{n=e.F},e=>{d=e.F},e=>{m=e.U},e=>{u=e._,p=e.A},e=>{c=e.g},e=>{f=e.f},e=>{h=e.Q,y=e.R,g=e.X,D=e.V,b=e.k,v=e.B,U=e.Z},null,null,null,null,null,null,null,null,null,null],execute:function(){e("default",u({name:"SafeInspectionHazard",components:{FormItemSection:r,FlowForm:t,FormItemPicker:o,FormItemDate:l,FormItemCalendar:s,FormItemPerson:n,FormItemCoord:d,UploadFiles:m,FormItemCascader:i},mixins:[f],data(){var e,a;return{allTaskKeys:[["UserTask_0","UserTask_1","UserTask_2","UserTask_3","UserTask_4"],["UserTask_0","UserTask_1"]],type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",entityName:"SafeInspectionHazard",formKey:"SafeInspectionHazard",modelKey:"AQJC-YHGL002",detailParamList:[],detailEntityNameList:[],service:{submit:p.VUE_APP_TCS_API_SERVICENAME+"/form/commit",query:p.VUE_APP_TCS_API_SERVICENAME+"/form/query"},formData:{sectionId:"",name:"",hazardNumber:"",projectPosition:"",constructionArea:"",source:this.$DICT_VALUE.LSJC,level:"",deadline:"",dangerSource:"",description:"",requirement:"",fileUpload:"",type:"",typeChild:"",measure:"",measureDate:"",initiator:"",processName:"",processCode:"",measureFileUpload:"",isPass:"",needSupervision:"",positionInfo:null,lng:null,lat:null,height:null},wbsList:[],bpmPersonInfo:{},approverConfigList:[]}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},canEdit0(){return"add"===this.type||"UserTask_0"===this.taskKey},canEdit1(){return"execute"===this.type&&"UserTask_1"===this.taskKey},canEdit2(){return"execute"===this.type&&"UserTask_2"===this.taskKey},typeChildOptions(){let e=this.$store.ENUM_DICT[this.$DICT_CODE.safe_hazard_type].find((e=>e.code===this.formData.type));return(null==e?void 0:e.child)||[]},currentUser(){return this.$store.USER_INFO||{}}},watch:{},created(){},mounted(){},methods:{async beforeInit(e){let a=e.entityObject;return"execute"===this.type&&("UserTask_1"===this.taskKey&&(a.isPass=1),"UserTask_2"===this.taskKey&&(a.needSupervision=1)),a},changeTgHander(){1===this.formData.isPass?this.bpmPersonInfo.approverConfigList=this.approverConfigList:this.bpmPersonInfo.approverConfigList=[],this.setBpmPerson(this.bpmPersonInfo)},changeJlHander(){1===this.formData.needSupervision?(this.bpmPersonInfo.approverConfigList=this.approverConfigList.filter((e=>"supervisionPerson"===e.prop)),this.setBpmPerson(this.bpmPersonInfo)):(this.bpmPersonInfo.approverConfigList=this.approverConfigList.filter((e=>"reviewPerson"===e.prop)),this.setBpmPerson(this.bpmPersonInfo))},async beforeSetBpmPerson(e,a,t){let{approverConfigList:r}=e;return"add"===this.type&&(e.initiatorInfo={type:0,prop:"initiator",taskKey:"UserTask_0",userName:this.currentUser.userName,userFullName:this.currentUser.userFullname,approvalTime:this.$dayjs(new Date).format("YYYY-MM-DD"),userPhone:this.currentUser.phone,userOrg:this.currentUser.orgList&&this.currentUser.orgList.length>0?[...new Set(this.currentUser.orgList.map((e=>e.name)))].join(";"):"",label:"上报人",userPhoneLabel:"联系方式",approvalTimeLabel:"上报日期",userOrgLabel:"上报人所在部门"},this.formData.sectionId=this.$getPortalId()),this.getWbsList(),e.initiatorInfo.format="YYYY-MM-DD",this.bpmPersonInfo=JSON.parse(JSON.stringify(e)),this.approverConfigList=JSON.parse(JSON.stringify(r)),"UserTask_2"===this.taskKey&&(e.approverConfigList=this.approverConfigList.filter((e=>"supervisionPerson"===e.prop))),e},handleSection(){this.formData.projectPosition=null,this.getWbsList()},async getWbsList(){this.wbsList=await c(this.formData.sectionId,!0,this.portal),this.$nextTick((()=>{this.$refs.formItemCascaderRef.chengeLabel()}))},async initForm(){},validData(){return["sectionId","constructionArea","level","deadline","name","description","requirement","fileUpload"].some((e=>!this.formData[e]))},async onDraft(){try{if(this.validData())return this.$showToast({message:"请完善表单数据"});this.doData();const e={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{if(this.validData())return this.$showToast({message:"请完善表单数据"});this.doData();const e={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},doData(){this.formData.reporter||(this.formData.reporter=this.currentUser.userName),this.formData.reporterName||(this.formData.reporterName=this.currentUser.userFullname),this.formData.reportDate||(this.formData.reportDate=this.$dayjs(new Date).format("YYYY-MM-DD HH:mm:ss")),this.formData.phone||(this.formData.phone=this.currentUser.phone)},async afterSubmit(e,a){this.updateFiles()},async updateFiles(){return new Promise((async(e,a)=>{try{this.$refs.fileUpload&&await this.$refs.fileUpload.update(),this.$refs.measureFileUpload&&await this.$refs.measureFileUpload.update(),e()}catch(t){a()}}))}}},[["render",function(e,a,t,r,o,l){const s=h("van-field"),i=h("FormItemSection"),n=h("FormItemCascader"),d=h("FormItemPicker"),m=h("FormItemDate"),u=h("UploadFiles"),p=h("FormItemCoord"),c=h("van-cell-group"),f=h("van-radio"),_=h("van-radio-group"),w=h("van-form"),I=h("FlowForm");return y(),g(I,{ref:"FlowForm","model-key":o.modelKey,"form-key":o.formKey,"entity-name":o.entityName,"all-task-keys":o.allTaskKeys,"bpm-person-obj":e.bpmPersonObj,"onUpdate:bpmPersonObj":a[18]||(a[18]=a=>e.bpmPersonObj=a),onDraftClick:l.onDraft,onSubmitClick:l.onSubmit,onAfterSubmit:l.afterSubmit},{default:D((()=>[b(w,{ref:"form","label-width":"9em","input-align":"right","error-message-align":"right"},{default:D((()=>[b(c,{border:!1},{default:D((()=>[b(s,{modelValue:o.formData.hazardNumber,"onUpdate:modelValue":a[0]||(a[0]=e=>o.formData.hazardNumber=e),label:"隐患编号",placeholder:"自动生成",readonly:""},null,8,["modelValue"]),b(i,{label:"所属标段",placeholder:"请选择",modelValue:o.formData.sectionId,"onUpdate:modelValue":a[1]||(a[1]=e=>o.formData.sectionId=e),readonly:"view"===o.type||!l.canEdit0,required:"",rules:[{required:!0,message:"请选择所属标段"}],onSelect:l.handleSection},null,8,["modelValue","readonly","onSelect"]),b(n,{ref:"formItemCascaderRef",label:"工程部位",value:o.formData.projectPosition,"onUpdate:value":a[2]||(a[2]=e=>o.formData.projectPosition=e),columns:[...o.wbsList],"columns-field-names":{text:"nodeName",value:"id",children:"children"},"trigger-change-mode":!0,placeholder:"请选择",readonly:"view"===o.type||!l.canEdit0},null,8,["value","columns","readonly"]),b(d,{label:"隐患级别",value:o.formData.level,"onUpdate:value":a[3]||(a[3]=e=>o.formData.level=e),"dict-name":e.$DICT_CODE.safe_hazard_level,placeholder:"请选择",required:"",rules:[{required:!0,message:"请选择隐患级别"}],readonly:"view"===o.type||!l.canEdit0},null,8,["value","dict-name","readonly"]),b(m,{label:"整改期限",value:o.formData.deadline,"onUpdate:value":a[4]||(a[4]=e=>o.formData.deadline=e),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择整改期限"}],readonly:"view"===o.type||!l.canEdit0},null,8,["value","readonly"]),b(s,{label:"详细区域",modelValue:o.formData.constructionArea,"onUpdate:modelValue":a[5]||(a[5]=e=>o.formData.constructionArea=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left",readonly:"view"===o.type||!l.canEdit0},null,8,["modelValue","readonly"]),b(s,{label:"隐患名称",modelValue:o.formData.name,"onUpdate:modelValue":a[6]||(a[6]=e=>o.formData.name=e),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入隐患名称"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit0},null,8,["modelValue","readonly"]),b(s,{label:"整改内容",modelValue:o.formData.description,"onUpdate:modelValue":a[7]||(a[7]=e=>o.formData.description=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改内容"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit0},null,8,["modelValue","readonly"]),b(s,{label:"整改要求及处理意见",modelValue:o.formData.requirement,"onUpdate:modelValue":a[8]||(a[8]=e=>o.formData.requirement=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改要求及处理意见"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit0},null,8,["modelValue","readonly"]),b(s,{label:"附件上传","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传资料附件"}]},{input:D((()=>[b(u,{ref:"fileUpload",g9s:o.formData.fileUpload,"onUpdate:g9s":a[9]||(a[9]=e=>o.formData.fileUpload=e),accept:"*",multiple:!0,readonly:"view"===o.type||!l.canEdit0},null,8,["g9s","readonly"])])),_:1}),b(p,{label:"经纬度",longitude:o.formData.lng,"onUpdate:longitude":a[10]||(a[10]=e=>o.formData.lng=e),latitude:o.formData.lat,"onUpdate:latitude":a[11]||(a[11]=e=>o.formData.lat=e),title:"选择定位",readonly:"view"===o.type||!l.canEdit0},null,8,["longitude","latitude","readonly"])])),_:1}),l.canEdit1?(y(),g(c,{key:0,border:!1},{default:D((()=>[b(s,{name:"isPass",label:"是否通过",required:"",rules:[{required:!0,message:"请选择是否通过"}]},{input:D((()=>[b(_,{modelValue:o.formData.isPass,"onUpdate:modelValue":a[12]||(a[12]=e=>o.formData.isPass=e),direction:"horizontal",onChange:l.changeTgHander},{default:D((()=>[b(f,{name:1},{default:D((()=>a[19]||(a[19]=[v("通过")]))),_:1,__:[19]}),b(f,{name:0},{default:D((()=>a[20]||(a[20]=[v("不通过")]))),_:1,__:[20]})])),_:1},8,["modelValue","onChange"])])),_:1})])),_:1})):U("",!0),o.formData.measureDate||"UserTask_2"===o.formData.taskKey?(y(),g(c,{key:1,border:!1},{default:D((()=>[l.canEdit2?(y(),g(s,{key:0,name:"needSupervision",label:"是否需要监理审核",required:"",rules:[{required:!0,message:"请选择是否需要监理审核"}]},{input:D((()=>[b(_,{modelValue:o.formData.needSupervision,"onUpdate:modelValue":a[13]||(a[13]=e=>o.formData.needSupervision=e),direction:"horizontal",onChange:l.changeJlHander},{default:D((()=>[b(f,{name:1},{default:D((()=>a[21]||(a[21]=[v("是")]))),_:1,__:[21]}),b(f,{name:0},{default:D((()=>a[22]||(a[22]=[v("否")]))),_:1,__:[22]})])),_:1},8,["modelValue","onChange"])])),_:1})):U("",!0),b(s,{label:"整改措施",modelValue:o.formData.measure,"onUpdate:modelValue":a[14]||(a[14]=e=>o.formData.measure=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改措施"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit2},null,8,["modelValue","readonly"]),b(s,{label:"整改情况",modelValue:o.formData.situation,"onUpdate:modelValue":a[15]||(a[15]=e=>o.formData.situation=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改情况"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit2},null,8,["modelValue","readonly"]),b(m,{label:"整改完成日期",value:o.formData.measureDate,"onUpdate:value":a[16]||(a[16]=e=>o.formData.measureDate=e),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择整改完成日期"}],readonly:"view"===o.type||!l.canEdit2},null,8,["value","readonly"]),b(s,{label:"附件上传","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传附件"}]},{input:D((()=>[b(u,{ref:"measureFileUpload",g9s:o.formData.measureFileUpload,"onUpdate:g9s":a[17]||(a[17]=e=>o.formData.measureFileUpload=e),accept:"*",multiple:!0,readonly:"view"===o.type||!l.canEdit2},null,8,["g9s","readonly"])])),_:1})])),_:1})):U("",!0)])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","all-task-keys","bpm-person-obj","onDraftClick","onSubmitClick","onAfterSubmit"])}]]))}}}));
