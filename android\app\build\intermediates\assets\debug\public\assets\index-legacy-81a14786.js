System.register(["./index-legacy-f817ae93.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var l,t,n,s,o,d,m,c,i,p,r;return{setters:[e=>{l=e.F,t=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,d=e.X,m=e.V,c=e.k,i=e.U,p=e.Y,r=e.B},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"one-line"},u={class:"form-info"},f={class:"form-info"},h={class:"form-info"},b={class:"form-info"},y={class:"form-info"},g={class:"check-wp",style:{"text-indent":"0"}},V={class:"attachment-desc"},k={class:"comment-wp"},D={class:"textarea-wp"},v={class:"comment-wp"},C={class:"textarea-wp"},j={class:"footer-input"},w={class:"form-info"},x={class:"form-info"},P={class:"form-info"};e("default",n({name:"CB37",components:{FormTemplate:l,DocumentPart:t},emits:[],props:{},setup(e,{attrs:a,slots:l,emit:t}){},data:()=>({detailTable:[],attachmentDesc:"1、\n2、"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit({formData:e,detailParamList:a},l){return new Promise(((a,t)=>{try{if("submit"==l&&!e.check1&&!e.check2)return this.$showNotify({type:"danger",message:"请选择：通知/指示",duration:3e3}),t(!1),!1;a()}catch(n){t(n)}}))},changeCheck1(e){e&&(this.$refs.FormTemplate.formData.check2=!1)},changeCheck2(e){e&&(this.$refs.FormTemplate.formData.check1=!1)}}},[["render",function(e,l,t,n,U,_){const N=s("van-checkbox"),T=s("van-field"),F=s("DocumentPart"),L=s("FormTemplate");return o(),d(L,{ref:"FormTemplate",nature:"回复","on-after-init":_.onAfterInit,"on-before-submit":_.onBeforeSubmit,"detail-table":U.detailTable,"is-show-confirm1":!1,attachmentDesc:U.attachmentDesc},{default:m((({formData:e,formTable:t,baseObj:n,uploadAccept:s,taskStart:o,taskComment2:d,taskComment3:j,taskComment4:w,taskComment5:x})=>[c(F,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:n.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!o},{default:m((()=>[i("div",a,[l[3]||(l[3]=i("span",{style:{"margin-left":"2em"}},"我方于",-1)),i("span",u,p(e.field1),1),l[4]||(l[4]=i("span",null,"年",-1)),i("span",f,p(e.field2),1),l[5]||(l[5]=i("span",null,"月",-1)),i("span",h,p(e.field3),1),l[6]||(l[6]=i("span",null,"日收到",-1)),i("span",b,p(e.field4),1),l[7]||(l[7]=i("span",null,"(监理文件文号)关于",-1)),i("span",y,p(e.field5),1),l[8]||(l[8]=i("span",null,"的",-1)),i("div",g,[c(N,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!o,onChange:_.changeCheck1},{default:m((()=>l[0]||(l[0]=[r("通知")]))),_:2,__:[0]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),l[2]||(l[2]=i("span",null," / ",-1)),c(N,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!o,onChange:_.changeCheck2},{default:m((()=>l[1]||(l[1]=[r("指示")]))),_:2,__:[1]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[9]||(l[9]=i("span",null,"，回复如下：",-1))]),i("div",V,[l[10]||(l[10]=i("div",null,"附件：",-1)),c(T,{modelValue:e.attachmentDesc,"onUpdate:modelValue":a=>e.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),c(F,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!o},{default:m((()=>[i("div",k,[l[11]||(l[11]=i("div",null,"EPC总承包项目部意见：",-1)),i("div",D,[c(T,{modelValue:e.comment2,"onUpdate:modelValue":a=>e.comment2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),c(F,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!o},{default:m((()=>[i("div",v,[l[12]||(l[12]=i("div",null,"审核意见：",-1)),i("div",C,[c(T,{modelValue:e.comment3,"onUpdate:modelValue":a=>e.comment3=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!j},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:m((({formData:e,formTable:a,baseObj:t,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:d,taskComment4:m,taskComment5:c})=>[i("div",j,[l[13]||(l[13]=i("span",null,"说明：1、本表一式",-1)),i("span",w,p(e.num1),1),l[14]||(l[14]=i("span",null,"份，由承包人填写，监理机构审核后，承包人",-1)),i("span",x,p(e.num2),1),l[15]||(l[15]=i("span",null,"份，监理机构",-1)),i("span",P,p(e.num3),1),l[16]||(l[16]=i("span",null,"份。",-1))]),l[17]||(l[17]=i("div",{class:"footer-input"},[i("span",null,"2、本表主要用于承包人对监理机构发出的监理通知、指示的回复。")],-1))])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
