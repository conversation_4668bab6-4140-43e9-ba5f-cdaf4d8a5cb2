System.register(["./index-legacy-f817ae93.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,l){"use strict";var a,t,n,s,o,m,d,i,p,c;return{setters:[e=>{a=e.F,t=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,m=e.X,d=e.V,i=e.k,p=e.U,c=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const l={class:"one-line"},r={class:"form-info",style:{"padding-left":"2em"}},u={class:"form-info"},f={class:"form-info"},b={class:"form-info"},y={style:{margin:"20px 0"}},v={class:"cb-table"},D={rowspan:"2"},g={class:"cell"},h={class:"one-line"},j={class:"form-info"},N={class:"one-line"},V={class:"form-info"},k={class:"one-line"},x={class:"form-info"},w={class:"cell"},C={class:"form-info"},P={class:"cell"},F={class:"form-info"},L={class:"attachment-desc"},O={class:"comment-wp"},T={class:"textarea-wp"},_={class:"footer-input"},S={class:"form-info"},U={class:"form-info"},A={class:"form-info"},I={class:"form-info"};e("default",n({name:"CB35",components:{FormTemplate:a,DocumentPart:t},emits:[],props:{},setup(e,{attrs:l,slots:a,emit:t}){},data:()=>({detailTable:[],attachmentDesc:"1、前期验收遗留问题处理情况。\n2、未处理遗留问题的处理措施计划。\n3、验收报告、资料。"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:l}){},onBeforeSubmit:({formData:e,detailParamList:l,taskComment3:a},t)=>new Promise(((e,l)=>{try{e()}catch(a){l(a)}})),getSummaries:(e=[],l)=>e.reduce(((e,a)=>{const t=Number(a[l]);return isNaN(t)?Number(Number(e).toFixed(2)):Number(Number(e+t).toFixed(2))}),0)}},[["render",function(e,a,t,n,z,B){const W=s("van-field"),Q=s("DocumentPart"),E=s("FormTemplate");return o(),m(E,{ref:"FormTemplate",nature:"验报","on-after-init":B.onAfterInit,"on-before-submit":B.onBeforeSubmit,"detail-table":z.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:z.attachmentDesc},{default:d((({formData:e,formTable:t,baseObj:n,uploadAccept:s,taskStart:o,taskComment2:m,taskComment3:_,taskComment4:S,taskComment5:U})=>[i(Q,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:n.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!o},{default:d((()=>[p("div",l,[p("span",r,c(e.projectName),1),a[0]||(a[0]=p("span",null,"工程项目已于",-1)),p("span",u,c(e.field1),1),a[1]||(a[1]=p("span",null,"年",-1)),p("span",f,c(e.field2),1),a[2]||(a[2]=p("span",null,"月",-1)),p("span",b,c(e.field3),1),a[3]||(a[3]=p("span",null,"日完工，",-1)),a[4]||(a[4]=p("span",null,"未处理的遗留问题不影响本次验收评定",-1)),a[5]||(a[5]=p("span",null,"并编制了处理措施计划，",-1)),a[6]||(a[6]=p("span",null,"验收报告，资料已准备就绪，",-1)),a[7]||(a[7]=p("span",null," 现申请验收。",-1))]),p("div",y,[p("table",v,[p("tbody",null,[p("tr",null,[p("td",D,[p("div",g,[p("div",h,[p("span",j,c(e.field4),1),a[8]||(a[8]=p("span",null,"完工验收",-1))]),p("div",N,[p("span",V,c(e.field5),1),a[9]||(a[9]=p("span",null,"验收",-1))]),p("div",k,[p("span",x,c(e.field6),1),a[10]||(a[10]=p("span",null,"验收",-1))])])]),a[11]||(a[11]=p("th",null,[p("div",{class:"cell"},"验收工程名称、编码")],-1)),a[12]||(a[12]=p("th",null,[p("div",{class:"cell"},"申请验收时间")],-1))]),p("tr",null,[p("td",null,[p("div",w,[p("span",C,c(e.field7),1)])]),p("td",null,[p("div",P,[p("span",F,c(e.field8),1)])])])])])]),p("div",L,[a[13]||(a[13]=p("div",null,"附件：",-1)),i(W,{modelValue:e.attachmentDesc,"onUpdate:modelValue":l=>e.attachmentDesc=l,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),i(Q,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!o},{default:d((()=>[p("div",O,[a[14]||(a[14]=p("div",null,"EPC总承包项目部意见：",-1)),p("div",T,[i(W,{modelValue:e.comment2,"onUpdate:modelValue":l=>e.comment2=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!m},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),i(Q,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!o},{default:d((()=>a[15]||(a[15]=[p("div",{class:"comment-wp"},[p("div",null,"监理机构将另行签发审核意见。")],-1)]))),_:2,__:[15]},1032,["deptValue","deptOptions","disabled"])])),footer:d((({formData:e,formTable:l,baseObj:t,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:m,taskComment4:d,taskComment5:i})=>[p("div",_,[a[16]||(a[16]=p("span",null,"说明：本表一式",-1)),p("span",S,c(e.num1),1),a[17]||(a[17]=p("span",null,"份，由承包人填写，监理机构签收后，发包人",-1)),p("span",U,c(e.num2),1),a[18]||(a[18]=p("span",null,"份，监理机构",-1)),p("span",A,c(e.num3),1),a[19]||(a[19]=p("span",null,"份，承包人",-1)),p("span",I,c(e.num4),1),a[20]||(a[20]=p("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
