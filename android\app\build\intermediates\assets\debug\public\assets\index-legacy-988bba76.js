System.register(["./index-legacy-09188690.js","./index-legacy-b580af71.js","./FormItemPicker-legacy-fd45c24d.js","./FormItemDate-legacy-c4422d42.js","./FormItemCalendar-legacy-ea787ea1.js","./FormItemPerson-legacy-e6e57748.js","./FormItemCoord-legacy-e6ddc9b7.js","./index-legacy-645a3645.js","./verder-legacy-e6127216.js","./vant-legacy-b51a9379.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./validate-legacy-4e8f0db9.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js"],(function(e,a){"use strict";var t,r,n,i,d,o,l,s,m,u,p,D,f,g,h;return{setters:[e=>{t=e.h,r=e._},e=>{n=e.F},e=>{i=e.F},e=>{d=e.F},e=>{o=e.F},e=>{l=e.F},e=>{s=e.F},e=>{m=e.U},e=>{u=e.Q,p=e.R,D=e.X,f=e.V,g=e.k,h=e.Z},null,null,null,null,null,null,null,null,null],execute:function(){e("default",r({name:"SafetyHiddenDangerSupervisor",components:{FlowForm:n,FormItemPicker:i,FormItemDate:d,FormItemCalendar:o,FormItemPerson:l,FormItemCoord:s,UploadFiles:m},props:{},emits:[],setup(e,{attrs:a,slots:t,emit:r}){},data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",entityName:"SafetyHiddenDanger",formKey:"SafetyHiddenDangerSupervisor",modelKey:"safety_hidden_danger_flow_jl",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-safety/form/query",submit:"/cybereng-safety/form/commit"},formData:{type:"2",id:void 0,portalId:void 0,subProjectId:"",subProjectName:"",unitEngineeringId:"",unitEngineeringName:"",divisionEngineeringId:"",divisionEngineeringName:"",longitude:"",latitude:"",hiddenDangerCode:void 0,hiddenDangerLevel:"",hiddenDangerCategory:"",rectifyDate:"",overdueState:"",hiddenDangerContent:"",beforeFileToken:"",hiddenDangerReportor:"",hiddenDangerReportorFullname:"",hiddenDangerReportorDeptName:"",hiddenDangerReportorDeptCode:"",hiddenDangerChecker:"",hiddenDangerCheckerFullname:"",hiddenDangerCheckerDeptName:"",hiddenDangerCheckerDeptCode:"",hiddenDangerRectifier:"",hiddenDangerRectifierFullname:"",hiddenDangerRectifierDeptName:"",hiddenDangerRectifierDeptCode:"",hiddenDangerSupervisor:"",hiddenDangerSupervisorFullname:"",hiddenDangerSupervisorDeptName:"",hiddenDangerSupervisorDeptCode:"",hiddenDangerRectifyApprover:"",hiddenDangerRectifyApproverFullname:"",hiddenDangerRectifyApproverDeptName:"",hiddenDangerRectifyApproverDeptCode:"",rectifyState:"待整改",rectifyMeasures:"",rectifySituation:"",afterFileToken:"",finishDate:""}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},unitEngineeringList(){if(1==this.portal.type){if(!this.formData.subProjectId)return[];const e=this.subProjectList.find((e=>e.id==this.formData.subProjectId));return e&&e.children||[]}return this.subProjectList[0]&&this.subProjectList[0].children||[]},divisionEngineeringList(){if(!this.formData.unitEngineeringId)return[];const e=this.unitEngineeringList.find((e=>e.id==this.formData.unitEngineeringId));return e&&e.children||[]},canEdit0(){return"add"===this.type||"UserTask_0"===this.taskKey},canEdit1(){return"execute"===this.type&&"UserTask_1"===this.taskKey},canEdit2(){return"execute"===this.type&&"UserTask_2"===this.taskKey}},watch:{},created(){},mounted(){this.initForm()},methods:{copyCallBack(e){this.formData={...e},["hiddenDangerContent","latitude","longitude","beforeFileToken"].forEach((e=>{this.formData[e]=""}))},async initForm(){if("add"===this.type){if(this.formData.portalId=this.portalId,1!=this.portal.type){const e=this.subProjectList.find((e=>e.portalId==this.portal.id));this.formData.subProjectId=null==e?void 0:e.id,this.formData.subProjectName=null==e?void 0:e.nodeName}const{userName:e="",userFullname:a="",orgList:t=[]}=this.user||{},r=t.find((e=>{var a;return e.portalId==(null===(a=this.portal)||void 0===a?void 0:a.id)}))||t[0],n=(null==r?void 0:r.name)||"",i=(null==r?void 0:r.orgNo)||"";this.formData.hiddenDangerReportor=e,this.formData.hiddenDangerReportorFullname=a,this.formData.hiddenDangerReportorDeptName=n,this.formData.hiddenDangerReportorDeptCode=i,this.formData.hiddenDangerRectifyApprover=e,this.formData.hiddenDangerRectifyApproverFullname=a,this.formData.hiddenDangerRectifyApproverDeptName=n,this.formData.hiddenDangerRectifyApproverDeptCode=i}else this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:a=[],entityObject:t}=e;this.detailParamList=a,this.formData={...this.formData,...t}}))}))},async onDraft(){try{const e={...this.formData,rectifyState:"暂存"};this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{await this.$refs.form.validate();let e="待整改";"UserTask_0"===this.taskKey||"UserTask_1"===this.taskKey?e="待整改":"UserTask_2"===this.taskKey||"UserTask_3"===this.taskKey?e="待审核":"UserTask_4"===this.taskKey&&(e="整改完成",this.formData.finishDate=this.$dayjs().format("YYYY-MM-DD HH:mm:ss"));const a={...this.formData,rectifyState:e};this.$refs.FlowForm.onSubmit(this.service.submit,a)}catch(e){console.log(e)}},async updateFiles(){return new Promise((async(e,a)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),e()}catch(t){a()}}))},afterSubmit(e,a){this.updateFiles(),"submit"===e&&("UserTask_1"==this.taskKey&&function(e){t({url:"/cybereng-safety/safety/hiddenDanger/delayTask",method:"get",params:e})}({id:this.formData.id}),"UserTask_1"!=this.taskKey&&"UserTask_2"!=this.taskKey||function(e){t({url:"/cybereng-safety/safety/hiddenDanger/sendMessage",method:"get",params:e})}({id:this.formData.id}),this.formData.id&&function(e){t({url:"/cybereng-safety/yxst/safety/hiddenDanger",method:"put",params:e})}({id:this.formData.id}))},handleSubProjectChange(e={}){this.formData.unitEngineeringId="",this.formData.unitEngineeringName="",this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""},handleUnitEngineeringChange(e={}){this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""}}},[["render",function(e,a,t,r,n,i){const d=u("van-field"),o=u("FormItemPicker"),l=u("FormItemCoord"),s=u("FormItemCalendar"),m=u("van-cell-group"),c=u("UploadFiles"),y=u("FormItemPerson"),v=u("van-form"),F=u("FlowForm");return p(),D(F,{ref:"FlowForm","model-key":n.modelKey,"form-key":n.formKey,"entity-name":n.entityName,"detail-param-list":n.detailParamList,"detail-entity-name-list":n.detailEntityNameList,onDraftClick:i.onDraft,onSubmitClick:i.onSubmit,onAfterSubmit:i.afterSubmit,onCopyCallBack:i.copyCallBack},{default:f((()=>[g(v,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:f((()=>[g(m,{border:!1},{default:f((()=>[n.formData.hiddenDangerCode?(p(),D(d,{key:0,modelValue:n.formData.hiddenDangerCode,"onUpdate:modelValue":a[0]||(a[0]=e=>n.formData.hiddenDangerCode=e),label:"隐患整改单号",placeholder:"审批完成后自动生成",readonly:""},null,8,["modelValue"])):h("",!0),g(o,{label:"子工程",value:n.formData.subProjectId,"onUpdate:value":a[1]||(a[1]=e=>n.formData.subProjectId=e),text:n.formData.subProjectName,"onUpdate:text":a[2]||(a[2]=e=>n.formData.subProjectName=e),columns:[...i.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:1!=i.portal.type||"view"===n.type||!i.canEdit0,onChange:i.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),g(o,{label:"单位工程",value:n.formData.unitEngineeringId,"onUpdate:value":a[3]||(a[3]=e=>n.formData.unitEngineeringId=e),text:n.formData.unitEngineeringName,"onUpdate:text":a[4]||(a[4]=e=>n.formData.unitEngineeringName=e),columns:[...i.unitEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择单位工程",required:"",rules:[{required:!0,message:"请选择单位工程"}],readonly:"view"===n.type||!i.canEdit0,onChange:i.handleUnitEngineeringChange},null,8,["value","text","columns","readonly","onChange"]),g(o,{label:"分部工程",value:n.formData.divisionEngineeringId,"onUpdate:value":a[5]||(a[5]=e=>n.formData.divisionEngineeringId=e),text:n.formData.divisionEngineeringName,"onUpdate:text":a[6]||(a[6]=e=>n.formData.divisionEngineeringName=e),columns:[...i.divisionEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择分部工程",required:"",rules:[{required:!0,message:"请选择分部工程"}],readonly:"view"===n.type||!i.canEdit0},null,8,["value","text","columns","readonly"]),g(l,{label:"定位",longitude:n.formData.longitude,"onUpdate:longitude":a[7]||(a[7]=e=>n.formData.longitude=e),latitude:n.formData.latitude,"onUpdate:latitude":a[8]||(a[8]=e=>n.formData.latitude=e),title:"选择定位",readonly:"view"===n.type||!i.canEdit0},null,8,["longitude","latitude","readonly"]),g(o,{label:"隐患级别",value:n.formData.hiddenDangerLevel,"onUpdate:value":a[9]||(a[9]=e=>n.formData.hiddenDangerLevel=e),"dict-name":"hidden_danger_level",title:"选择隐患级别",required:"",rules:[{required:!0,message:"请选择隐患级别"}],readonly:"view"===n.type||!i.canEdit0},null,8,["value","readonly"]),g(o,{label:"隐患分类",value:n.formData.hiddenDangerCategory,"onUpdate:value":a[10]||(a[10]=e=>n.formData.hiddenDangerCategory=e),"dict-name":"hidden_danger_category",title:"选择隐患分类",required:"",rules:[{required:!0,message:"请选择隐患分类"}],readonly:"view"===n.type||!i.canEdit0},null,8,["value","readonly"]),g(s,{label:"整改期限",value:n.formData.rectifyDate,"onUpdate:value":a[11]||(a[11]=e=>n.formData.rectifyDate=e),title:"选择整改期限",required:"",rules:[{required:!0,message:"请选择整改期限"}],readonly:"view"===n.type||!i.canEdit0},null,8,["value","readonly"]),"add"!==n.type?(p(),D(d,{key:1,modelValue:n.formData.rectifyState,"onUpdate:modelValue":a[12]||(a[12]=e=>n.formData.rectifyState=e),label:"整改状态",placeholder:"",readonly:""},null,8,["modelValue"])):h("",!0),"add"!==n.type?(p(),D(d,{key:2,modelValue:n.formData.overdueState,"onUpdate:modelValue":a[13]||(a[13]=e=>n.formData.overdueState=e),label:"逾期情况",placeholder:"",readonly:""},null,8,["modelValue"])):h("",!0)])),_:1}),g(m,{border:!1},{default:f((()=>[g(d,{label:"隐患内容",modelValue:n.formData.hiddenDangerContent,"onUpdate:modelValue":a[14]||(a[14]=e=>n.formData.hiddenDangerContent=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入隐患内容",required:"",rules:[{required:!0,message:"请输入隐患内容"}],"input-align":"left",readonly:"view"===n.type||!i.canEdit0},null,8,["modelValue","readonly"]),g(d,{label:"附件照片","label-align":"top","input-align":"left"},{input:f((()=>[g(c,{ref:"beforeFiles",g9s:n.formData.beforeFileToken,"onUpdate:g9s":a[15]||(a[15]=e=>n.formData.beforeFileToken=e),accept:"image/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===n.type||!i.canEdit0},null,8,["g9s","readonly"])])),_:1})])),_:1}),g(m,{border:!1},{default:f((()=>[g(y,{label:"监理发起人",userName:n.formData.hiddenDangerReportor,"onUpdate:userName":a[16]||(a[16]=e=>n.formData.hiddenDangerReportor=e),userFullname:n.formData.hiddenDangerReportorFullname,"onUpdate:userFullname":a[17]||(a[17]=e=>n.formData.hiddenDangerReportorFullname=e),deptName:n.formData.hiddenDangerReportorDeptName,"onUpdate:deptName":a[18]||(a[18]=e=>n.formData.hiddenDangerReportorDeptName=e),deptCode:n.formData.hiddenDangerReportorDeptCode,"onUpdate:deptCode":a[19]||(a[19]=e=>n.formData.hiddenDangerReportorDeptCode=e),title:"选择监理发起人",required:"",rules:[{required:!0,message:"请选择监理发起人"}],readonly:!0},null,8,["userName","userFullname","deptName","deptCode"]),g(d,{modelValue:n.formData.hiddenDangerReportorDeptName,"onUpdate:modelValue":a[20]||(a[20]=e=>n.formData.hiddenDangerReportorDeptName=e),label:"上报人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),g(y,{label:"总包确认人",userName:n.formData.hiddenDangerChecker,"onUpdate:userName":a[21]||(a[21]=e=>n.formData.hiddenDangerChecker=e),userFullname:n.formData.hiddenDangerCheckerFullname,"onUpdate:userFullname":a[22]||(a[22]=e=>n.formData.hiddenDangerCheckerFullname=e),deptName:n.formData.hiddenDangerCheckerDeptName,"onUpdate:deptName":a[23]||(a[23]=e=>n.formData.hiddenDangerCheckerDeptName=e),deptCode:n.formData.hiddenDangerCheckerDeptCode,"onUpdate:deptCode":a[24]||(a[24]=e=>n.formData.hiddenDangerCheckerDeptCode=e),title:"选择总包确认人",required:"",rules:[{required:!0,message:"请选择总包确认人"}],readonly:"view"===n.type||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),g(d,{modelValue:n.formData.hiddenDangerCheckerDeptName,"onUpdate:modelValue":a[25]||(a[25]=e=>n.formData.hiddenDangerCheckerDeptName=e),label:"确认人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),g(y,{"label-width":"8em",label:"施工单位整改人",userName:n.formData.hiddenDangerRectifier,"onUpdate:userName":a[26]||(a[26]=e=>n.formData.hiddenDangerRectifier=e),userFullname:n.formData.hiddenDangerRectifierFullname,"onUpdate:userFullname":a[27]||(a[27]=e=>n.formData.hiddenDangerRectifierFullname=e),deptName:n.formData.hiddenDangerRectifierDeptName,"onUpdate:deptName":a[28]||(a[28]=e=>n.formData.hiddenDangerRectifierDeptName=e),deptCode:n.formData.hiddenDangerRectifierDeptCode,"onUpdate:deptCode":a[29]||(a[29]=e=>n.formData.hiddenDangerRectifierDeptCode=e),title:"选择施工单位整改人",required:!i.canEdit0,rules:[{required:!i.canEdit0,message:"请选择施工单位整改人"}],readonly:"view"===n.type||!i.canEdit0&&!i.canEdit1},null,8,["userName","userFullname","deptName","deptCode","required","rules","readonly"]),g(y,{label:"总包审核人",userName:n.formData.hiddenDangerSupervisor,"onUpdate:userName":a[30]||(a[30]=e=>n.formData.hiddenDangerSupervisor=e),userFullname:n.formData.hiddenDangerSupervisorFullname,"onUpdate:userFullname":a[31]||(a[31]=e=>n.formData.hiddenDangerSupervisorFullname=e),deptName:n.formData.hiddenDangerSupervisorDeptName,"onUpdate:deptName":a[32]||(a[32]=e=>n.formData.hiddenDangerSupervisorDeptName=e),deptCode:n.formData.hiddenDangerSupervisorDeptCode,"onUpdate:deptCode":a[33]||(a[33]=e=>n.formData.hiddenDangerSupervisorDeptCode=e),title:"选择总包审核人",required:"",rules:[{required:!0,message:"请选择总包审核人"}],readonly:"view"===n.type||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),g(y,{label:"监理审核人",userName:n.formData.hiddenDangerRectifyApprover,"onUpdate:userName":a[34]||(a[34]=e=>n.formData.hiddenDangerRectifyApprover=e),userFullname:n.formData.hiddenDangerRectifyApproverFullname,"onUpdate:userFullname":a[35]||(a[35]=e=>n.formData.hiddenDangerRectifyApproverFullname=e),deptName:n.formData.hiddenDangerRectifyApproverDeptName,"onUpdate:deptName":a[36]||(a[36]=e=>n.formData.hiddenDangerRectifyApproverDeptName=e),deptCode:n.formData.hiddenDangerRectifyApproverDeptCode,"onUpdate:deptCode":a[37]||(a[37]=e=>n.formData.hiddenDangerRectifyApproverDeptCode=e),title:"选择监理审核人",required:"",rules:[{required:!0,message:"请选择监理审核人"}],readonly:"view"===n.type||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"])])),_:1}),"view"===n.type||n.taskKey&&"UserTask_0"!==n.taskKey&&"UserTask_1"!==n.taskKey?(p(),D(m,{key:0,border:!1},{default:f((()=>[g(d,{label:"整改措施",modelValue:n.formData.rectifyMeasures,"onUpdate:modelValue":a[38]||(a[38]=e=>n.formData.rectifyMeasures=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改措施",required:"",rules:[{required:!0,message:"请填写整改措施"}],"input-align":"left",readonly:"view"===n.type||!i.canEdit2},null,8,["modelValue","readonly"]),g(d,{label:"整改情况",modelValue:n.formData.rectifySituation,"onUpdate:modelValue":a[39]||(a[39]=e=>n.formData.rectifySituation=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改情况",required:"",rules:[{required:!0,message:"请填写整改情况"}],"input-align":"left",readonly:"view"===n.type||!i.canEdit2},null,8,["modelValue","readonly"]),g(d,{label:"附件照片","label-align":"top","input-align":"left"},{input:f((()=>[g(c,{ref:"afterFiles",g9s:n.formData.afterFileToken,"onUpdate:g9s":a[40]||(a[40]=e=>n.formData.afterFileToken=e),accept:"image/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===n.type||!i.canEdit2},null,8,["g9s","readonly"])])),_:1})])),_:1})):h("",!0)])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit","onCopyCallBack"])}]]))}}}));
