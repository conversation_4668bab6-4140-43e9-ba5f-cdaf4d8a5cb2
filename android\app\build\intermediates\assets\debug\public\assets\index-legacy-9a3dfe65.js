System.register(["./api-legacy-53a10b6c.js","./verder-legacy-e6127216.js","./index-legacy-09188690.js","./ListItem-legacy-3ca9eb8f.js","./sift-legacy-1dc85988.js","./FormItemPicker-legacy-fd45c24d.js","./vant-legacy-b51a9379.js","./api-legacy-39a4c48a.js"],(function(e,t){"use strict";var a,s,i,n,o,l,d,r,c,h,p,m,u,v,f,g,y,b,k,w,C;return{setters:[e=>{a=e.d,s=e.g},e=>{i=e.Q,n=e.R,o=e.S,l=e.k,d=e.V,r=e.U,c=e.Y,h=e.B,p=e.a2,m=e.X,u=e.F,v=e.W,f=e.Z,g=e.y},e=>{y=e._,b=e.u},e=>{k=e.L},e=>{w=e.S},e=>{C=e.F},null,null],execute:function(){var t=document.createElement("style");t.textContent=".task-item[data-v-1772d1c4]{background-color:#fff;border-radius:1.33333vw;padding:2.66667vw;margin-bottom:2.66667vw}.task-item .body[data-v-1772d1c4]{padding:2.66667vw 1.33333vw 1.33333vw;position:relative}.task-item .body .item-info[data-v-1772d1c4]{display:flex;flex-direction:row;align-items:center;font-size:3.73333vw;color:#9a9a9a;line-height:1;padding:1.33333vw 0}.task-item .body .item-info>.key[data-v-1772d1c4]{min-width:5em}.task-item .body .item-info>.value[data-v-1772d1c4]{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#333}.task-item .body .right[data-v-1772d1c4]{position:absolute;top:1.33333vw;right:1.06667vw}.task-item .body .right .tag[data-v-1772d1c4]{padding:1.6vw 3.2vw;text-align:center}.van-button--disabled[data-v-1772d1c4]{background-color:#1d2129;border:none}.page[data-v-9fbf9be2]{height:100%}.view-height[data-v-57f36d55]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height) - var(--sab));padding-top:2.66667vw;overflow-x:hidden;overflow-y:scroll}.view-height.no-tabbar[data-v-57f36d55]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--sab) - 12vw)}.tabs-wp[data-v-57f36d55]{border-bottom:1px solid #f8f8f8}.btn-group[data-v-57f36d55]{width:100%;padding:0 4vw;box-sizing:border-box;display:flex;justify-content:space-between;gap:0 4vw;margin:2.66667vw 0}.btn-group>button[data-v-57f36d55]{flex:1}\n",document.head.appendChild(t);const _={class:"body"},S={class:"item-info header"},I={class:"value"},T={class:"item-info"},x={class:"value"},R={class:"item-info"},D={class:"value"},$={class:"item-info"},L={class:"value"},N={class:"item-info"},j={class:"value"},F={class:"item-info"},O={class:"value"},E={class:"right"},P={key:0,class:"p-[10px]"},U=y({name:"SafetyCheck",components:{ListItem:k,SafetyCheckItem:y({name:"TaskItem",components:{},props:{item:{type:Object,default:()=>({})}},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({}),computed:{taskStateText(){return"0"===String(this.item.inspectionResult)?"正常":"有隐患"},tagColor(){var e;return"0"===String(null===(e=this.item)||void 0===e?void 0:e.inspectionResult)?"#07c160":"#ff4d4f"},user(){return this.$store.USER_INFO}},watch:{},mounted(){},methods:{handelDel(){this.$confirm({title:"提示",message:`确认删除${this.item.inspectionNumber}?`}).then((()=>{a(this.item).then((()=>{this.$emit("delSuccess")}))})).catch((()=>{}))},toFormCenter(){this.$store.SAFE_INSPECTION_FORM={},this.$router.push({path:"/SafetyCheckDetail",query:{id:this.item.id,type:this.user.userName===this.item.createBy?"update":"detail",title:this.user.userName===this.item.createBy?"编辑安全检查":"安全检查详情"}})}}},[["render",function(e,t,a,s,m,u){const v=i("van-tag"),f=i("van-button"),g=i("van-swipe-cell");return n(),o("div",{class:"task-item",onClick:t[0]||(t[0]=p((e=>u.toFormCenter()),["stop","prevent"]))},[l(g,null,{right:d((()=>[l(f,{square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:u.user.userName!==a.item.createBy||!a.item.showDel,onClick:u.handelDel},null,8,["disabled","onClick"])])),default:d((()=>[r("div",_,[r("div",S,[t[1]||(t[1]=r("span",{class:"key"},"检查编号",-1)),r("span",I,c(a.item.inspectionNumber),1)]),r("div",T,[t[2]||(t[2]=r("span",{class:"key"},"检查类型",-1)),r("span",x,c(e.$formatLabel(a.item.inspectionType,e.$DICT_CODE.safe_inspection_type)),1)]),r("div",R,[t[3]||(t[3]=r("span",{class:"key"},"检查单位",-1)),r("span",D,c(a.item.inspectionUnitName),1)]),r("div",$,[t[4]||(t[4]=r("span",{class:"key"},"所属标段",-1)),r("span",L,c(e.$formatLabel(a.item.sectionId,e.$DICT_CODE.project_section)),1)]),r("div",N,[t[5]||(t[5]=r("span",{class:"key"},"详细区域",-1)),r("span",j,c(a.item.inspectionArea),1)]),r("div",F,[t[6]||(t[6]=r("span",{class:"key"},"检查日期",-1)),r("span",O,c(e.$dayjs(a.item.inspectionDate).format("YYYY-MM-DD")),1)]),r("div",E,[l(v,{class:"tag",color:u.tagColor,plain:"",size:"medium"},{default:d((()=>[h(c(u.taskStateText),1)])),_:1},8,["color"])])])])),_:1})])}],["__scopeId","data-v-1772d1c4"]])},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}),computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams,...this.search};1===e.page&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await s(e),a=this.searchParams.page<=1?[]:this.list||[];this.list.length>=t.total&&(this.finished=!0),this.list=[...a,...t.records],this.searchParams.page++}catch(e){this.finished=!0}finally{setTimeout((()=>{this.loading=!1,this.$closeToast()}),100)}}}},[["render",function(e,t,a,s,r,c){const h=i("SafetyCheckItem"),p=i("van-empty"),g=i("van-list"),y=i("van-pull-refresh");return n(),m(y,{modelValue:r.refreshing,"onUpdate:modelValue":t[1]||(t[1]=e=>r.refreshing=e),onRefresh:c.onRefresh},{default:d((()=>[l(g,{loading:r.loading,"onUpdate:loading":t[0]||(t[0]=e=>r.loading=e),finished:r.finished,"finished-text":r.list&&r.list.length?"没有更多了":"",onLoad:c.onLoadList,"immediate-check":!1},{default:d((()=>[r.list&&r.list.length?(n(!0),o(u,{key:0},v(r.list||[],((e,t)=>(n(),m(h,{key:e.id,item:e,tabName:"安全检查",onDelSuccess:c.onRefresh},null,8,["item","onDelSuccess"])))),128)):(n(),o(u,{key:1},[r.loading?f("",!0):(n(),o("div",P,[l(p,{description:"暂无安全检查"})]))],64))])),_:1},8,["loading","finished","finished-text","onLoad"])])),_:1},8,["modelValue","onRefresh"])}],["__scopeId","data-v-9fbf9be2"]]),z=b(),A={class:"btn-group"};e("default",y({name:"SafetyCheckIndex",components:{FormItemPicker:C,SafetyCheck:U},props:{},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({search:{inspectionNumber:"",sectionId:"",inspectionType:"",inspectionResult:""},Sift:w,showTop:!1}),mounted(){},methods:{handleAdd(){z.SAFE_INSPECTION_FORM={},this.$router.push({path:"/SafetyCheckDetail",query:{type:"add",title:"新增安全检查"}})},onCancel(){},handleQuery(){this.showTop=!1,this.$nextTick((()=>{this.$refs.SafetyCheckRef&&this.$refs.SafetyCheckRef.onRefresh(this.search)}))},handleResetting(){this.search={inspectionNumber:"",sectionId:"",inspectionType:"",inspectionResult:""},this.handleQuery()}}},[["render",function(e,t,a,s,c,m){const v=i("van-icon"),f=i("Navbar"),y=i("FormItemPicker"),b=i("van-button"),k=i("van-popup"),w=i("SafetyCheck");return n(),o(u,null,[l(f,{back:""},{right:d((()=>[l(v,{name:c.Sift,size:"2em",onClick:t[0]||(t[0]=p((e=>c.showTop=!c.showTop),["stop","prevent"]))},null,8,["name"])])),_:1}),l(k,{show:c.showTop,"onUpdate:show":t[4]||(t[4]=e=>c.showTop=e),position:"top"},{default:d((()=>[l(y,{value:c.search.sectionId,"onUpdate:value":t[1]||(t[1]=e=>c.search.sectionId=e),"dict-name":e.$DICT_CODE.project_section,placeholder:"选择所属标段"},null,8,["value","dict-name"]),l(y,{value:c.search.inspectionType,"onUpdate:value":t[2]||(t[2]=e=>c.search.inspectionType=e),"dict-name":e.$DICT_CODE.safe_inspection_type,placeholder:"选择检查类型"},null,8,["value","dict-name"]),l(y,{value:c.search.inspectionResult,"onUpdate:value":t[3]||(t[3]=e=>c.search.inspectionResult=e),"dict-name":e.$DICT_CODE.safe_inspection_result,placeholder:"选择检查结果"},null,8,["value","dict-name"]),r("div",A,[l(b,{round:"",type:"primary",plain:"",onClick:p(m.handleQuery,["stop","prevent"])},{default:d((()=>t[6]||(t[6]=[h("查询")]))),_:1,__:[6]},8,["onClick"]),l(b,{round:"",plain:"",onClick:p(m.handleResetting,["stop","prevent"])},{default:d((()=>t[7]||(t[7]=[h("重置")]))),_:1,__:[7]},8,["onClick"])])])),_:1},8,["show"]),r("div",{class:g(["view-height",{"no-tabbar":e.envFeishu}])},[l(w,{ref:"SafetyCheckRef",search:c.search},null,8,["search"])],2),l(b,{type:"primary",size:"normal",style:{width:"100%"},onClick:t[5]||(t[5]=e=>m.handleAdd())},{default:d((()=>t[8]||(t[8]=[h("新增检查")]))),_:1,__:[8]})],64)}],["__scopeId","data-v-57f36d55"]]))}}}));
