System.register(["./index-legacy-f817ae93.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,l){"use strict";var a,t,n,o,s,d,m,p,i,r;return{setters:[e=>{a=e.F,t=e.D},e=>{n=e._},e=>{o=e.Q,s=e.R,d=e.X,m=e.V,p=e.k,i=e.U,r=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const l={class:"one-line"},u={class:"form-info"},c={class:"form-info"},f={class:"form-info"},b={class:"form-info"},y={class:"form-info"},V={class:"form-info"},v={class:"comment-wp"},D={class:"textarea-wp"},w={class:"comment-wp"},g={class:"textarea-wp"},h={class:"comment-wp"},j={class:"textarea-wp"},x={class:"comment-wp"},k={class:"textarea-wp"},C={class:"comment-wp"},P={class:"textarea-wp"},U={class:"footer-input"},N={class:"form-info"},L={class:"form-info"},O={class:"form-info"},T={class:"form-info"};e("default",n({name:"CB21",components:{FormTemplate:a,DocumentPart:t},emits:[],props:{},setup(e,{attrs:l,slots:a,emit:t}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:l}){},onBeforeSubmit:({formData:e,detailParamList:l,taskComment3:a},t)=>new Promise(((e,l)=>{try{e()}catch(a){l(a)}}))}},[["render",function(e,a,t,n,z,F){const S=o("van-field"),_=o("DocumentPart"),A=o("FormTemplate");return s(),d(A,{ref:"FormTemplate",nature:"事故","on-after-init":F.onAfterInit,"on-before-submit":F.onBeforeSubmit,"detail-table":z.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:z.attachmentDesc},{default:m((({formData:e,formTable:t,baseObj:n,uploadAccept:o,taskStart:s,taskComment2:d,taskComment3:U,taskComment4:N,taskComment5:L})=>[p(_,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:n.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",l,[i("span",u,r(e.field1),1),a[0]||(a[0]=i("span",null,"年",-1)),i("span",c,r(e.field2),1),a[1]||(a[1]=i("span",null,"月",-1)),i("span",f,r(e.field3),1),a[2]||(a[2]=i("span",null,"日",-1)),i("span",b,r(e.field4),1),a[3]||(a[3]=i("span",null,"时，",-1)),a[4]||(a[4]=i("span",null,"在",-1)),i("span",y,r(e.field5),1),a[5]||(a[5]=i("span",null,"发生",-1)),i("span",V,r(e.field6),1),a[6]||(a[6]=i("span",null,"事故，",-1)),a[7]||(a[7]=i("span",null,"现将事故发生情况报告如下：",-1))]),i("div",v,[a[8]||(a[8]=i("div",null,"1、事故简述：",-1)),i("div",D,[p(S,{modelValue:e.field7,"onUpdate:modelValue":l=>e.field7=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),i("div",w,[a[9]||(a[9]=i("div",null,"2、已经采取的应急措施：",-1)),i("div",g,[p(S,{modelValue:e.field8,"onUpdate:modelValue":l=>e.field8=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),i("div",h,[a[10]||(a[10]=i("div",null,"3、初步处理意见：",-1)),i("div",j,[p(S,{modelValue:e.field9,"onUpdate:modelValue":l=>e.field9=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),p(_,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",x,[a[11]||(a[11]=i("div",null,"EPC总承包项目部意见：",-1)),i("div",k,[p(S,{modelValue:e.comment2,"onUpdate:modelValue":l=>e.comment2=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),p(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",C,[a[12]||(a[12]=i("div",null,"审核意见：",-1)),i("div",P,[p(S,{modelValue:e.comment3,"onUpdate:modelValue":l=>e.comment3=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!U},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:m((({formData:e,formTable:l,baseObj:t,uploadAccept:n,taskStart:o,taskComment2:s,taskComment3:d,taskComment4:m,taskComment5:p})=>[i("div",U,[a[13]||(a[13]=i("span",null,"说明：本表一式",-1)),i("span",N,r(e.num1),1),a[14]||(a[14]=i("span",null,"份，由承包人填写，监理机构签署意见后，发包人",-1)),i("span",L,r(e.num2),1),a[15]||(a[15]=i("span",null,"份，监理机构",-1)),i("span",O,r(e.num3),1),a[16]||(a[16]=i("span",null,"份，承包人",-1)),i("span",T,r(e.num4),1),a[17]||(a[17]=i("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
