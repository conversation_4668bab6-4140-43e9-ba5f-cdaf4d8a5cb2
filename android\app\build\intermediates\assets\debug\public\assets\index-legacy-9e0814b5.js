System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var t,n,l,s,o,m,i,p,r,c;return{setters:[e=>{t=e.F,n=e.D},e=>{l=e._},e=>{s=e.Q,o=e.R,m=e.X,i=e.V,p=e.k,r=e.U,c=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"one-line"},u={class:"form-info"},d={class:"one-line"},f={class:"form-info"},b={class:"form-info"},y={class:"form-info"},g={class:"footer-input"},j={class:"form-info"},D={class:"form-info"},h={class:"form-info"},v={class:"form-info"};e("default",l({name:"JL01",components:{FormTemplate:t,DocumentPart:n},emits:[],props:{},setup(e,{attrs:a,slots:t,emit:n}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit:({formData:e,detailParamList:a},t)=>new Promise(((e,a)=>{try{e()}catch(t){a(t)}}))}},[["render",function(e,t,n,l,k,P){const L=s("DocumentPart"),N=s("FormTemplate");return o(),m(N,{ref:"FormTemplate",nature:"开工","on-after-init":P.onAfterInit,"on-before-submit":P.onBeforeSubmit,"detail-table":k.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:k.attachmentDesc},{default:i((({formData:e,formTable:n,baseObj:l,uploadAccept:s,taskStart:o,taskComment2:m,taskComment3:g,taskComment4:j})=>[p(L,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:l.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!o},{default:i((()=>[r("div",a,[t[0]||(t[0]=r("span",{style:{"padding-left":"2em"}},"根据施工合同约定，现签发",-1)),r("span",u,c(e.projectName),1),t[1]||(t[1]=r("span",null,"合同工程开工通知。",-1)),t[2]||(t[2]=r("span",null,"贵方在接到该通知后，",-1)),t[3]||(t[3]=r("span",null,"及时调遣人员和施工设备、材料进场，",-1)),t[4]||(t[4]=r("span",null,"完成各项施工准备工作，尽快提交《合同工程开工申请表》。",-1))]),r("div",d,[t[5]||(t[5]=r("span",{style:{"padding-left":"2em"}},"该合同工程开工日期为",-1)),r("span",f,c(e.field2),1),t[6]||(t[6]=r("span",null,"年",-1)),r("span",b,c(e.field3),1),t[7]||(t[7]=r("span",null,"月",-1)),r("span",y,c(e.field4),1),t[8]||(t[8]=r("span",null,"日。",-1))])])),_:2},1032,["deptValue","deptOptions","disabled"]),p(L,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:l.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!0},{default:i((()=>t[9]||(t[9]=[r("div",{class:"comment-wp"},[r("div",null,"今已收到合同工程开工通知。")],-1)]))),_:2,__:[9]},1032,["deptValue","deptOptions"])])),footer:i((({formData:e,formTable:a,baseObj:n,uploadAccept:l,taskStart:s,taskComment2:o,taskComment3:m,taskComment4:i})=>[r("div",g,[t[10]||(t[10]=r("span",null,"说明：本表一式",-1)),r("span",j,c(e.num1),1),t[11]||(t[11]=r("span",null,"份，由监理机构填写，承包人签收后，承包人",-1)),r("span",D,c(e.num2),1),t[12]||(t[12]=r("span",null,"份，监理机构",-1)),r("span",h,c(e.num3),1),t[13]||(t[13]=r("span",null,"份，发包人",-1)),r("span",v,c(e.num4),1),t[14]||(t[14]=r("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
