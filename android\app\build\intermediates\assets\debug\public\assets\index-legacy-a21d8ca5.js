System.register(["./index-legacy-b580af71.js","./FormItemPicker-legacy-fd45c24d.js","./FormItemPerson-legacy-e6e57748.js","./index-legacy-645a3645.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js"],(function(e,a){"use strict";var t,r,l,o,m,n,s,i,u,d,f,c,p;return{setters:[e=>{t=e.F,r=e.a},e=>{l=e.F},e=>{o=e.F},e=>{m=e.U},e=>{n=e._},e=>{s=e.Q,i=e.R,u=e.X,d=e.V,f=e.k,c=e.Z,p=e.B},null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".check-box-color[data-v-302a56f6] .van-checkbox__label{color:var(--van-text-color)}\n",document.head.appendChild(a);const y={name:"MeetSummaryFlow",components:{FlowForm:t,FormItemPicker:l,FormItemPerson:o,UploadFiles:m,FormItemMultiplePerson:r},props:{},emits:[],data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",entityName:"TechnologyMeetingSummary",modelKey:"meet_summary_flow",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/form/commit"},formData:{formKey:"MeetSummaryFlow",fkId:"",portalId:"",subProjectName:"",subProjectId:null,projectDeptCode:"",projectDeptName:"",moduleType:"",sendingName:"",sendingType:"",sendingCode:"",sendingTypeCode:"",createBy:"",userFullname:"",userTelephoneNum:"",prjDepName:"",prjDepCode:"",fillingDate:this.$dayjs().format("YYYY-MM-DD"),isConfirm1:!1,approverUsername1:"",approverFullname1:"",isConfirm2:!1,approverUsername2:"",approverFullname2:"",isConfirm3:!1,approverUsername3:"",approverFullname3:"",notifyMethod:[],attachment:null,attachmentFiles:1,summaryAttachment:null,summaryAttachmentNum:1}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},canEdit0(){return"add"===this.type||"UserTask_0"===this.taskKey},yesOrNoList:()=>[{text:"是",value:!0},{text:"否",value:!1}]},mounted(){this.initForm()},methods:{async initForm(){if("add"===this.type){this.formData={...this.formData,...this.$route.query};const{userName:e="",userFullname:a="",orgList:t=[],phone:r=""}=this.user||{},l=t.find((e=>{var a;return e.portalId==(null===(a=this.portal)||void 0===a?void 0:a.id)}))||t[0],o=(null==l?void 0:l.name)||"",m=(null==l?void 0:l.orgNo)||"";this.formData.createBy=e,this.formData.userFullname=a,this.formData.prjDepName=o,this.formData.prjDepCode=m,this.formData.userTelephoneNum=r}else this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:a=[],entityObject:t}=e;this.detailParamList=a||[],this.formData={...this.formData,...t},this.formData.notifyMethod=t.notifyMethod?t.notifyMethod.split(","):[],console.log("formData",this.formData,this.detailParamList)}))}))},async onDraft(){try{const e={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{await this.$refs.form.validate();const e={...this.formData,notifyMethod:this.formData.notifyMethod.join(",")};await this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},getSubProjectNode(e){this.formData.portalId=e&&e.portalId||""},clearUserChoose(e){this.formData[`approverUsername${e}`]="",this.formData[`approverFullname${e}`]=""},async updateFiles(){return new Promise((async(e,a)=>{try{this.$refs.notifyAttachmentFiles&&await this.$refs.notifyAttachmentFiles.update(),this.$refs.summaryAttachmentNum&&await this.$refs.summaryAttachmentNum.update(),this.$refs.attachmentFiles&&await this.$refs.attachmentFiles.update(),e()}catch(t){a()}}))},afterSubmit(e,a){this.updateFiles()}}};e("default",n(y,[["render",function(e,a,t,r,l,o){const m=s("van-field"),n=s("UploadFiles"),y=s("van-cell-group"),h=s("FormItemPicker"),D=s("FormItemMultiplePerson"),v=s("FormItemPerson"),g=s("van-checkbox"),F=s("van-checkbox-group"),b=s("van-form"),N=s("FlowForm");return i(),u(N,{ref:"FlowForm","model-key":l.modelKey,"form-key":e.formKey,"entity-name":l.entityName,"detail-param-list":l.detailParamList,"detail-entity-name-list":l.detailEntityNameList,onDraftClick:o.onDraft,onSubmitClick:o.onSubmit,onAfterSubmit:o.afterSubmit},{default:d((()=>[f(b,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:d((()=>[f(y,{border:!1},{default:d((()=>[f(m,{modelValue:l.formData.subProjectName,"onUpdate:modelValue":a[0]||(a[0]=e=>l.formData.subProjectName=e),label:"子工程",placeholder:"请选择",readonly:"",required:""},null,8,["modelValue"]),f(m,{modelValue:l.formData.projectDeptName,"onUpdate:modelValue":a[1]||(a[1]=e=>l.formData.projectDeptName=e),label:"项目部",placeholder:"请选择",readonly:"",required:""},null,8,["modelValue"]),f(m,{modelValue:l.formData.sendingType,"onUpdate:modelValue":a[2]||(a[2]=e=>l.formData.sendingType=e),label:"会议类型",placeholder:"请选择",readonly:"",required:""},null,8,["modelValue"]),f(m,{modelValue:l.formData.sendingName,"onUpdate:modelValue":a[3]||(a[3]=e=>l.formData.sendingName=e),label:"会议名称",placeholder:"请输入",required:"",readonly:""},null,8,["modelValue"]),f(m,{label:"会议纪要","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传"}],modelValue:l.formData.summaryAttachmentNum,"onUpdate:modelValue":a[6]||(a[6]=e=>l.formData.summaryAttachmentNum=e)},{input:d((()=>[f(n,{ref:"summaryAttachmentNum",g9s:l.formData.summaryAttachment,"onUpdate:g9s":a[4]||(a[4]=e=>l.formData.summaryAttachment=e),files:l.formData.summaryAttachmentNum,"onUpdate:files":a[5]||(a[5]=e=>l.formData.summaryAttachmentNum=e),accept:"*",multiple:!0,readonly:"view"===l.type||!o.canEdit0},null,8,["g9s","files","readonly"])])),_:1},8,["modelValue"])])),_:1}),f(y,{border:!1},{default:d((()=>[f(h,{label:"会议纪要是否核稿","label-width":"10em",value:l.formData.isConfirm1,"onUpdate:value":a[7]||(a[7]=e=>l.formData.isConfirm1=e),columns:[...o.yesOrNoList],required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit0,onChange:a[8]||(a[8]=e=>o.clearUserChoose("1"))},null,8,["value","columns","readonly"]),l.formData.isConfirm1?(i(),u(D,{key:0,label:"核稿人",userName:l.formData.approverUsername1,"onUpdate:userName":a[9]||(a[9]=e=>l.formData.approverUsername1=e),userFullname:l.formData.approverFullname1,"onUpdate:userFullname":a[10]||(a[10]=e=>l.formData.approverFullname1=e),title:"请选择核稿人",required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit0},null,8,["userName","userFullname","readonly"])):c("",!0)])),_:1}),f(y,{border:!1},{default:d((()=>[f(h,{label:"会议纪要是否签发","label-width":"10em",value:l.formData.isConfirm2,"onUpdate:value":a[11]||(a[11]=e=>l.formData.isConfirm2=e),columns:[...o.yesOrNoList],required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit0,onChange:a[12]||(a[12]=e=>o.clearUserChoose("2"))},null,8,["value","columns","readonly"]),l.formData.isConfirm2?(i(),u(v,{key:0,label:"签发人",userName:l.formData.approverUsername2,"onUpdate:userName":a[13]||(a[13]=e=>l.formData.approverUsername2=e),userFullname:l.formData.approverFullname2,"onUpdate:userFullname":a[14]||(a[14]=e=>l.formData.approverFullname2=e),title:"请选择签发人",required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit0},null,8,["userName","userFullname","readonly"])):c("",!0)])),_:1}),f(y,{border:!1},{default:d((()=>[f(h,{label:"会议纪要是否传阅","label-width":"10em",value:l.formData.isConfirm3,"onUpdate:value":a[15]||(a[15]=e=>l.formData.isConfirm3=e),columns:[...o.yesOrNoList],required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit0,onChange:a[16]||(a[16]=e=>o.clearUserChoose("3"))},null,8,["value","columns","readonly"]),l.formData.isConfirm3?(i(),u(D,{key:0,label:"传阅人",userName:l.formData.approverUsername3,"onUpdate:userName":a[17]||(a[17]=e=>l.formData.approverUsername3=e),userFullname:l.formData.approverFullname3,"onUpdate:userFullname":a[18]||(a[18]=e=>l.formData.approverFullname3=e),title:"请选择传阅人",required:"",rules:[{required:!0,message:"请选择"}],readonly:"view"===l.type||!o.canEdit0},null,8,["userName","userFullname","readonly"])):c("",!0)])),_:1}),f(y,{border:!1},{default:d((()=>[o.canEdit0?(i(),u(m,{key:0,name:"checkboxGroup",label:"通知方式",class:"check-box-color",required:"",rules:[{required:!0,message:"请选择"}]},{input:d((()=>[f(F,{modelValue:l.formData.notifyMethod,"onUpdate:modelValue":a[19]||(a[19]=e=>l.formData.notifyMethod=e),direction:"horizontal",shape:"square",disabled:"view"===l.type||!o.canEdit0},{default:d((()=>[f(g,{name:"系统通知"},{default:d((()=>a[22]||(a[22]=[p("系统通知")]))),_:1,__:[22]}),f(g,{name:"短信通知"},{default:d((()=>a[23]||(a[23]=[p("短信通知")]))),_:1,__:[23]})])),_:1},8,["modelValue","disabled"])])),_:1})):c("",!0),l.formData.attachment&&l.formData.attachmentFiles||o.canEdit0?(i(),u(m,{key:1,label:"上传附件","label-align":"top","input-align":"left"},{input:d((()=>[f(n,{ref:"attachmentFiles",g9s:l.formData.attachment,"onUpdate:g9s":a[20]||(a[20]=e=>l.formData.attachment=e),files:l.formData.attachmentFiles,"onUpdate:files":a[21]||(a[21]=e=>l.formData.attachmentFiles=e),accept:"*",multiple:!0,readonly:"view"===l.type||!o.canEdit0},null,8,["g9s","files","readonly"])])),_:1})):c("",!0)])),_:1})])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}],["__scopeId","data-v-302a56f6"]]))}}}));
