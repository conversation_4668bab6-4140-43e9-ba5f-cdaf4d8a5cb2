System.register(["./index-legacy-09188690.js","./index-legacy-b580af71.js","./FormItemPicker-legacy-fd45c24d.js","./verder-legacy-e6127216.js","./index-legacy-645a3645.js","./vant-legacy-b51a9379.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js"],(function(e,a){"use strict";var t,o,s,n,i,l,r,c,m,p,d,u,h,f,v,b,y,D;return{setters:[e=>{t=e.h,o=e._},e=>{s=e.F},e=>{n=e.F},e=>{i=e.Q,l=e.R,r=e.S,c=e.k,m=e.V,p=e.F,d=e.W,u=e.X,h=e.B,f=e.Y,v=e.a2,b=e.U,y=e.Z},e=>{D=e.U},null,null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".check-input[data-v-b303c28c]{--van-checkbox-label-color: #333;--van-checkbox-size: 3.73333vw;--van-checkbox-disabled-label-color: #333;--van-checkbox-disabled-icon-color: #999;padding:2.66667vw}.check-input .checkbox-group[data-v-b303c28c]{display:flex;flex-direction:row;flex-wrap:wrap}.check-input .checkbox-group .checkbox[data-v-b303c28c]{width:calc(50% - 3.73333vw);margin-right:3.73333vw;margin-bottom:1.33333vw;align-items:start}.check-input .action-wp[data-v-b303c28c]{padding-top:1.33333vw}.check-input .add-input-wp[data-v-b303c28c],.check-input .add-input-wp .add-input-action[data-v-b303c28c]{display:flex;flex-direction:row;align-items:center}.check-input .add-input-wp .add-input-action .btn[data-v-b303c28c]{margin-left:1.33333vw}.form-papper[data-v-7fc69b09]{font-size:3.73333vw;padding:1em}.form-papper .form-title[data-v-7fc69b09]{position:relative;overflow:hidden;text-align:center;margin-bottom:1em}.form-papper .form-title .code[data-v-7fc69b09]{position:absolute;left:0;top:0}.form-papper .form-title .name[data-v-7fc69b09]{font-size:4.26667vw;font-weight:600;padding-bottom:1.33333vw}.form-papper .form-title .archive[data-v-7fc69b09]{display:flex;align-items:center;justify-content:center}.form-papper .form-title .archive>span[data-v-7fc69b09]{padding:0 1.33333vw}.form-papper .form-body[data-v-7fc69b09]{margin:2.66667vw 0;border:1px solid #111}.form-papper .form-part[data-v-7fc69b09]{padding:2.66667vw}.form-papper .form-part+.form-part[data-v-7fc69b09]{border-top:1px solid #111}.form-papper .one-line[data-v-7fc69b09]{padding-bottom:1.33333vw}.form-papper .form-info[data-v-7fc69b09]{font-weight:700;text-decoration:underline;padding:0 1.33333vw}.form-papper .luokuan-wp[data-v-7fc69b09]{padding:2.66667vw 0 2.66667vw 2.66667vw}.form-papper .luokuan-wp .lk-item[data-v-7fc69b09]{display:flex;flex-direction:row;width:100%}.form-papper .luokuan-wp .lk-item .label[data-v-7fc69b09]{width:11em;text-align:right;flex-shrink:0}.form-papper .luokuan-wp .lk-item .value[data-v-7fc69b09]{flex:1}.form-papper .textarea-wp[data-v-7fc69b09]{border:1px solid var(--van-border-color);font-size:3.73333vw}.form-papper .textarea-wp .van-cell[data-v-7fc69b09]{padding:.53333vw 1.33333vw;font-size:3.73333vw;line-height:1.2}.form-papper .form-footer[data-v-7fc69b09]{margin-bottom:2.66667vw}.form-papper .form-footer .footer-input[data-v-7fc69b09]{margin-bottom:1.33333vw}.form-papper .footer-form[data-v-7fc69b09]{margin:2.66667vw -3.73333vw}\n",document.head.appendChild(a);const g={name:"CheckInput",components:{},emits:["update:value"],model:{prop:"value",event:"update:value"},props:{value:{type:Array,default:()=>[]},options:{type:Array,default:()=>[]},readonly:{type:Boolean,default:!1},clearUnchecked:{type:Boolean,default:!0}},setup(e,{attrs:a,slots:t,emit:o}){},data:()=>({showInput:!1,inputValue:"",extOptions:[]}),computed:{checked:{get(){return this.value||[]},set(e){this.$emit("update:value",e)}},checkedOptions(){return[...this.options,...this.extOptions].filter((e=>this.checked.includes(e)))}},watch:{},created(){},mounted(){},methods:{openInput(){this.inputValue="",this.showInput=!0},addOption(){if(this.readonly)return void this.onCancel();const e=this.inputValue.trim();""!=e&&(this.extOptions.includes(e)||this.options.includes(e)?this.$showToast("已存在该选项"):(this.extOptions.push(e),this.checked.push(e),this.onCancel()))},onCancel(){this.showInput=!1,this.inputValue=""}}},k={class:"check-input"},w={key:0,class:"action-wp"},j={class:"add-input-action"},x={class:"form-papper"},C={class:"form-title"},N={class:"archive"},_={class:"form-header"},T={class:"form-body"},P={class:"form-part"},F={class:"one-line"},Y={class:"form-info"},U={class:"one-line indent"},I={class:"form-info"},$={class:"luokuan-wp"},S={class:"lk-item"},O=["innerHTML"],A={class:"form-part"},L={class:"comment-wp"},M={class:"textarea-wp"},V={class:"luokuan-wp"},H={class:"lk-item"},B=["innerHTML"],z={class:"form-part"},E={class:"comment-wp"},K={class:"textarea-wp"},q={class:"luokuan-wp"},R={class:"lk-item"},G=["innerHTML"],J={class:"form-footer"},W={class:"footer-input"},Z={class:"footer-form"};e("default",o({name:"ConstructionTechnicalScheme",components:{FlowForm:s,FormItemPicker:n,CheckInput:o(g,[["render",function(e,a,t,o,s,n){const D=i("van-checkbox"),g=i("van-checkbox-group"),x=i("van-button"),C=i("van-search");return l(),r("div",k,[c(g,{modelValue:n.checked,"onUpdate:modelValue":a[0]||(a[0]=e=>n.checked=e),class:"checkbox-group",shape:"square",disabled:t.readonly},{default:m((()=>[t.readonly&&t.clearUnchecked?(l(!0),r(p,{key:0},d([...n.checkedOptions]||[],(e=>(l(),u(D,{key:e,name:e,value:e,class:"checkbox"},{default:m((()=>[h(f(e),1)])),_:2},1032,["name","value"])))),128)):(l(!0),r(p,{key:1},d([...t.options,...s.extOptions]||[],(e=>(l(),u(D,{key:e,name:e,value:e,class:"checkbox"},{default:m((()=>[h(f(e),1)])),_:2},1032,["name","value"])))),128))])),_:1},8,["modelValue","disabled"]),t.readonly?y("",!0):(l(),r("div",w,[s.showInput?(l(),u(C,{key:1,modelValue:s.inputValue,"onUpdate:modelValue":a[4]||(a[4]=e=>s.inputValue=e),placeholder:"请新增选项","input-align":"left","left-icon":"","show-action":"",onSearch:n.addOption,onCancel:n.onCancel,class:"add-input-wp"},{action:m((()=>[b("div",j,[c(x,{class:"btn",size:"small",onClick:a[2]||(a[2]=v((e=>n.onCancel()),["stop","prevent"]))},{default:m((()=>a[6]||(a[6]=[h("取消")]))),_:1,__:[6]}),c(x,{class:"btn",type:"primary",size:"small",onClick:a[3]||(a[3]=v((e=>n.addOption()),["stop","prevent"]))},{default:m((()=>a[7]||(a[7]=[h("新增")]))),_:1,__:[7]})])])),_:1},8,["modelValue","onSearch","onCancel"])):(l(),u(x,{key:0,class:"btn",size:"small",onClick:a[1]||(a[1]=v((e=>n.openInput()),["stop","prevent"]))},{default:m((()=>a[5]||(a[5]=[h("新增")]))),_:1,__:[5]}))]))])}],["__scopeId","data-v-b303c28c"]]),UploadFiles:D},props:{},emits:[],setup(e,{attrs:a,slots:t,emit:o}){},data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",entityName:"ConstructionTechnicalSchemeBranch"===this.$route.name?"TechnologyFilesBranch":"TechnologyFiles",formKey:"ConstructionTechnicalSchemeBranch"===this.$route.name?"ConstructionTechnicalSchemeBranch":"ConstructionTechnicalScheme",modelKey:"ConstructionTechnicalSchemeBranch"===this.$route.name?"technology_CB01_branch_process":"technology_CB01",service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/form/commit"},detailEntityNameList:["TechnologyConstructionSubmit"],detailParamList:[],formData:{id:void 0,portalId:void 0,sendingName:"",sendingCode:"",sendingType:"施工技术方案申报表",sendingTypeCode:"00_02_01",modelType:"施工文件",prjDepCode:"",prjDepName:"",subProjectId:"",subProjectCode:"",subProjectName:"",createBy:"",userFullname:"",userTelephoneNum:"",annualDate:this.$dayjs().format("YYYY"),epcName:"",titleCode:"",constructionName:"",contractCode:"",supervisionName:"",projectName:"",schemeType:[],constructionDeptName:"",comment2:"",epcDeptName:"",comment3:"",supervisionDeptName:"",approverUsername1:"",approverFullname1:"",approverUnit1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",approverUsername3:"",approverFullname3:"",approverUnit3:"",approverUsername4:"",approverFullname4:"",approverUnit4:"",approverUsername5:"",approverFullname5:"",approverUnit5:"",approverUsername6:"",approverFullname6:"",approverUnit6:"",updateDate:this.$dayjs().format("YYYY-MM-DD HH:mm:ss"),filingPath:"",projectNameCode:"",projectName:"",notifyMethod:"[]",processState:"0",routerPath:"ConstructionTechnicalScheme",num1:4,num2:1,num3:1,num4:1,num5:1,isconfirm1:!0,otherAttachment:"",time1:"",time2:"",time3:"",time4:"",time5:"",time6:""},checkOptions:["施工组织设计","施工措施计划","专项施工方案","度汛方案","灾害应急预案","施工工艺试验方案","专项检测试验方案","工程测量施测计划和方案","制度体系","变更实施方案"],accept:".jpg,.jpeg,.png,.JPG,.JPEG,.dwg,.DWG,.rar,.RAR,.zip,.ZIP,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx",baseData:{}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},baseObj(){var e;return this.formData.subProjectId&&(null===(e=this.baseData)||void 0===e?void 0:e[this.formData.subProjectId])||{}},noAdd(){return"view"==this.type||"add"!=this.type&&"UserTask_0"!=this.taskKey}},watch:{"formData.annualDate":{immediate:!0,handler(e){let a=e||this.$dayjs().format("YYYY");this.formData.filingPath=`\\施工文件\\文件汇总\\${a}年度\\${this.formData.sendingType||"施工技术方案申报表"}`}}},created(){},mounted(){this.initForm(),this.getBaseData()},methods:{async getBaseData(){try{const a={subProjectId:"",sendingType:this.formData.sendingType},o=await(e=a,t({url:"/cybereng-technology/common/getProcessBaseData",method:"get",params:e}));this.baseData=o||{}}catch(a){console.log(a)}var e},async initForm(){if("add"===this.type){if(this.formData.portalId=this.portalId,1!=this.portal.type){const e=this.subProjectList.find((e=>e.portalId==this.portal.id));this.formData.subProjectId=null==e?void 0:e.id,this.formData.subProjectName=null==e?void 0:e.nodeName,this.formData.subProjectCode=null==e?void 0:e.nodeCode}const{userName:e="",userFullname:a="",phone:t,orgList:o=[]}=this.user||{},s=o.find((e=>{var a;return e.portalId==(null===(a=this.portal)||void 0===a?void 0:a.id)}))||o[0],n=(null==s?void 0:s.name)||"",i=(null==s?void 0:s.orgNo)||"";this.formData.createBy=e,this.formData.userFullname=a,this.formData.userTelephoneNum=t,this.formData.prjDepName=n,this.formData.prjDepCode=i}else this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{var a;const{detailParamList:t=[],entityObject:o}=e;this.detailParamList=t;let s=(null===(a=t[0])||void 0===a||null===(a=a.detailEntityArray)||void 0===a||null===(a=a[0])||void 0===a||null===(a=a.schemeType)||void 0===a?void 0:a.split(","))||[];s=s.filter((e=>null!=e&&""!==e)),this.formData={...this.formData,...o,schemeType:s},this.checkOptions=this.unique(this.checkOptions.concat(this.formData.schemeType))}))}))},unique(e){const a=new Map;return e.filter((e=>!a.has(e)&&a.set(e,1)))},updateDetailParamList(){var e;const a=(null===(e=this.detailParamList)||void 0===e||null===(e=e[0])||void 0===e||null===(e=e.detailEntityArray)||void 0===e?void 0:e[0])||{};a.schemeType=this.formData.schemeType.join(","),this.detailParamList=[{detailEntityName:"TechnologyConstructionSubmit",detailEntityArray:[{...a}]}]},async onDraft(){try{const e={...this.formData,rectifyState:"暂存"};this.updateDetailParamList(),this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{switch(await this.$refs.form.validate(),this.taskKey){case"UserTask_0":break;case"UserTask_1":this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_2":this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_3":this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_4":this.formData.time4=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_5":this.formData.time5=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_6":this.formData.time6=this.$dayjs().format("YYYY-MM-DD HH:mm:ss")}if("add"===this.type&&"ConstructionTechnicalScheme"===this.$route.name){const e={...this.formData,processKey:this.modelKey},a=await function(e){return t({url:`/fawkes-ext/bpmConfig/process/getApprover/${e.processKey}`,method:"post",data:e})}(e);this.formData={...this.formData,...a}}this.updateDetailParamList();const e={...this.formData};this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},async updateFiles(){return new Promise((async(e,a)=>{try{this.$refs.otherAttachment&&await this.$refs.otherAttachment.update(),e()}catch(t){a()}}))},afterSubmit(e,a){this.updateFiles()},chengeSubProject(e){this.formData.subProjectCode=e.nodeCode,this.formData.constructionName=this.baseObj.constructionName[0],this.formData.epcName=this.baseObj.epcName[0],this.formData.contractCode=this.baseObj.contractCode[0],this.formData.supervisionName=this.baseObj.supervisionName[0],this.formData.projectName=this.baseObj.projectName[0],this.formData.constructionDeptName=this.baseObj.constructionDeptName[0],this.formData.epcDeptName=this.baseObj.epcDeptName[0],this.formData.supervisionDeptName=this.baseObj.supervisionDeptName[0]}}},[["render",function(e,a,t,o,s,n){const r=i("FormItemPicker"),p=i("van-cell-group"),d=i("CheckInput"),v=i("van-field"),y=i("van-radio"),D=i("van-radio-group"),g=i("UploadFiles"),k=i("van-form"),w=i("FlowForm");return l(),u(w,{ref:"FlowForm","model-key":s.modelKey,"form-key":s.formKey,"entity-name":s.entityName,"detail-param-list":s.detailParamList,"detail-entity-name-list":s.detailEntityNameList,onDraftClick:n.onDraft,onSubmitClick:n.onSubmit,onAfterSubmit:n.afterSubmit},{default:m((()=>[c(k,{ref:"form","label-width":"7em","input-align":"left","error-message-align":"right"},{default:m((()=>[c(p,{border:!1},{default:m((()=>[c(r,{label:"子工程",value:s.formData.subProjectId,"onUpdate:value":a[0]||(a[0]=e=>s.formData.subProjectId=e),text:s.formData.subProjectName,"onUpdate:text":a[1]||(a[1]=e=>s.formData.subProjectName=e),columns:[...n.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:1!=n.portal.type||n.noAdd,onChange:n.chengeSubProject},null,8,["value","text","columns","readonly","onChange"])])),_:1}),c(p,{border:!1},{default:m((()=>[b("div",x,[b("div",C,[a[12]||(a[12]=b("div",{class:"code"},"CB01",-1)),a[13]||(a[13]=b("div",{class:"name"},"施工技术方案报审表",-1)),b("div",N,[a[7]||(a[7]=b("span",null,"（ ",-1)),b("div",null,f(s.formData.constructionName),1),a[8]||(a[8]=b("span",null,"[ ",-1)),b("div",null,f(s.formData.annualDate),1),a[9]||(a[9]=b("span",null,"]",-1)),a[10]||(a[10]=b("span",null,"技案",-1)),b("div",null,f(s.formData.titleCode),1),a[11]||(a[11]=b("span",null,"号 ）",-1))])]),b("div",_,[b("div",null,[a[14]||(a[14]=b("span",null,"合同名称：",-1)),b("span",null,f(s.formData.epcName),1)]),b("div",null,[a[15]||(a[15]=b("span",null,"合同编号：",-1)),b("span",null,f(s.formData.contractCode),1)])]),b("div",T,[b("div",P,[b("div",F,[a[16]||(a[16]=b("span",null,"致：",-1)),b("span",Y,f(s.formData.supervisionName),1)]),b("div",U,[a[17]||(a[17]=b("span",null,"我方今提交",-1)),b("span",I,f(s.formData.projectName),1),a[18]||(a[18]=b("span",null,"工程的：",-1))]),b("div",null,[c(d,{value:s.formData.schemeType,"onUpdate:value":a[2]||(a[2]=e=>s.formData.schemeType=e),options:s.checkOptions,readonly:n.noAdd},null,8,["value","options","readonly"])]),a[22]||(a[22]=b("div",{class:"one-line"},"请贵方审批。",-1)),b("div",$,[b("div",S,[a[19]||(a[19]=b("span",{class:"label"},"施工项目部：",-1)),b("span",{class:"value",innerHTML:s.formData.constructionDeptName.replaceAll("\n","<br/>")},null,8,O)]),a[20]||(a[20]=b("div",{class:"lk-item"},[b("span",{class:"label"}," 施工负责人："),b("span",{class:"value"},f(""))],-1)),a[21]||(a[21]=b("div",{class:"lk-item"},[b("span",{class:"label"}," 日期："),b("span",{class:"value"},f(""))],-1))])]),b("div",A,[b("div",L,[a[23]||(a[23]=b("div",null,"EPC总承包项目部意见：",-1)),b("div",M,[c(v,{modelValue:s.formData.comment2,"onUpdate:modelValue":a[3]||(a[3]=e=>s.formData.comment2=e),rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!0},null,8,["modelValue"])])]),b("div",V,[b("div",H,[a[24]||(a[24]=b("span",{class:"label"},"EPC总承包项目部：",-1)),b("span",{class:"value",innerHTML:s.formData.epcDeptName.replaceAll("\n","<br/>")},null,8,B)]),a[25]||(a[25]=b("div",{class:"lk-item"},[b("span",{class:"label"}," 总承包项目负责人："),b("span",{class:"value"},f(""))],-1)),a[26]||(a[26]=b("div",{class:"lk-item"},[b("span",{class:"label"}," 日期："),b("span",{class:"value"},f(""))],-1))])]),b("div",z,[b("div",E,[a[27]||(a[27]=b("div",null,"监理机构将另行签发审批意见：",-1)),b("div",K,[c(v,{modelValue:s.formData.comment3,"onUpdate:modelValue":a[4]||(a[4]=e=>s.formData.comment3=e),rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!0},null,8,["modelValue"])])]),b("div",q,[b("div",R,[a[28]||(a[28]=b("span",{class:"label"},"监理机构：",-1)),b("span",{class:"value",innerHTML:s.formData.supervisionDeptName.replaceAll("\n","<br/>")},null,8,G)]),a[29]||(a[29]=b("div",{class:"lk-item"},[b("span",{class:"label"}," 签收人："),b("span",{class:"value"},f(""))],-1)),a[30]||(a[30]=b("div",{class:"lk-item"},[b("span",{class:"label"}," 日期："),b("span",{class:"value"},f(""))],-1))])])]),b("div",J,[b("div",W,[a[31]||(a[31]=b("span",null,"说明：本表一式",-1)),b("span",null,f(s.formData.num1),1),a[32]||(a[32]=b("span",null,"份，由承包人填写，监理机构签收后，承包人",-1)),b("span",null,f(s.formData.num2),1),a[33]||(a[33]=b("span",null,"份，监理机构",-1)),b("span",null,f(s.formData.num3),1),a[34]||(a[34]=b("span",null,"份，发包人",-1)),b("span",null,f(s.formData.num4),1),a[35]||(a[35]=b("span",null,"份，设代机构",-1)),b("span",null,f(s.formData.num5),1),a[36]||(a[36]=b("span",null,"份",-1))])])])])),_:1}),c(p,{border:!1},{default:m((()=>[b("div",Z,[c(v,{name:"radio",label:"是否施工负责人确认：","label-width":"11em"},{input:m((()=>[c(D,{modelValue:s.formData.isconfirm1,"onUpdate:modelValue":a[5]||(a[5]=e=>s.formData.isconfirm1=e),direction:"horizontal",disabled:n.noAdd},{default:m((()=>[c(y,{name:!0},{default:m((()=>a[37]||(a[37]=[h("是")]))),_:1,__:[37]}),c(y,{name:!1},{default:m((()=>a[38]||(a[38]=[h("否")]))),_:1,__:[38]})])),_:1},8,["modelValue","disabled"])])),_:1}),c(v,{label:"附件上传：","label-align":"top","input-align":"left"},{input:m((()=>[c(g,{ref:"otherAttachment",g9s:s.formData.otherAttachment,"onUpdate:g9s":a[6]||(a[6]=e=>s.formData.otherAttachment=e),accept:s.accept,multiple:!0,"max-count":9,"max-size":52428800,readonly:n.noAdd},null,8,["g9s","accept","readonly"])])),_:1})])])),_:1})])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}],["__scopeId","data-v-7fc69b09"]]))}}}));
