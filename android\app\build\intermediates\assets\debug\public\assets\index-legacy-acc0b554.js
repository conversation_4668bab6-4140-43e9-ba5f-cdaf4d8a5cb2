System.register(["./index-legacy-09188690.js","./index-legacy-b580af71.js","./FormItemPicker-legacy-fd45c24d.js","./FormItemDate-legacy-c4422d42.js","./FormItemCalendar-legacy-ea787ea1.js","./FormItemPerson-legacy-e6e57748.js","./FormItemCoord-legacy-e6ddc9b7.js","./index-legacy-645a3645.js","./verder-legacy-e6127216.js","./vant-legacy-b51a9379.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./validate-legacy-4e8f0db9.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js"],(function(e,a){"use strict";var t,r,i,n,o,d,l,s,m,u,f,p,D,g,c;return{setters:[e=>{t=e.h,r=e._},e=>{i=e.F},e=>{n=e.F},e=>{o=e.F},e=>{d=e.F},e=>{l=e.F},e=>{s=e.F},e=>{m=e.U},e=>{u=e.Q,f=e.R,p=e.X,D=e.V,g=e.k,c=e.Z},null,null,null,null,null,null,null,null,null],execute:function(){e("default",r({name:"SafetyHiddenDanger",components:{FlowForm:i,FormItemPicker:n,FormItemDate:o,FormItemCalendar:d,FormItemPerson:l,FormItemCoord:s,UploadFiles:m},props:{},emits:[],setup(e,{attrs:a,slots:t,emit:r}){},data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",entityName:"SafetyHiddenDanger",formKey:"SafetyHiddenDanger",modelKey:"safety_hidden_danger_flow",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-safety/form/query",submit:"/cybereng-safety/form/commit"},formData:{type:"1",id:void 0,portalId:void 0,subProjectId:"",subProjectName:"",unitEngineeringId:"",unitEngineeringName:"",divisionEngineeringId:"",divisionEngineeringName:"",longitude:"",latitude:"",hiddenDangerCode:void 0,hiddenDangerLevel:"",hiddenDangerCategory:"",rectifyDate:"",overdueState:"",hiddenDangerContent:"",beforeFileToken:"",hiddenDangerReportor:"",hiddenDangerReportorFullname:"",hiddenDangerReportorDeptName:"",hiddenDangerReportorDeptCode:"",hiddenDangerRectifier:"",hiddenDangerRectifierFullname:"",hiddenDangerRectifierDeptName:"",hiddenDangerRectifierDeptCode:"",hiddenDangerRectifyApprover:"",hiddenDangerRectifyApproverFullname:"",hiddenDangerRectifyApproverDeptName:"",hiddenDangerRectifyApproverDeptCode:"",rectifyState:"待整改",rectifyMeasures:"",rectifySituation:"",afterFileToken:"",finishDate:""}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},unitEngineeringList(){if(1==this.portal.type){if(!this.formData.subProjectId)return[];const e=this.subProjectList.find((e=>e.id==this.formData.subProjectId));return e&&e.children||[]}return this.subProjectList[0]&&this.subProjectList[0].children||[]},divisionEngineeringList(){if(!this.formData.unitEngineeringId)return[];const e=this.unitEngineeringList.find((e=>e.id==this.formData.unitEngineeringId));return e&&e.children||[]},canEdit0(){return"add"===this.type||"UserTask_0"===this.taskKey},canEdit1(){return"execute"===this.type&&"UserTask_1"===this.taskKey},canEdit2(){return"execute"===this.type&&"UserTask_2"===this.taskKey}},watch:{},created(){},mounted(){this.initForm()},methods:{copyCallBack(e){this.formData={...e},["hiddenDangerContent","beforeFileToken","latitude","longitude"].forEach((e=>{this.formData[e]=""}))},async initForm(){if("add"===this.type){if(this.formData.portalId=this.portalId,1!=this.portal.type){const e=this.subProjectList.find((e=>e.portalId==this.portal.id));this.formData.subProjectId=null==e?void 0:e.id,this.formData.subProjectName=null==e?void 0:e.nodeName}const{userName:e="",userFullname:a="",orgList:t=[]}=this.user||{},r=t.find((e=>{var a;return e.portalId==(null===(a=this.portal)||void 0===a?void 0:a.id)}))||t[0],i=(null==r?void 0:r.name)||"",n=(null==r?void 0:r.orgNo)||"";this.formData.hiddenDangerReportor=e,this.formData.hiddenDangerReportorFullname=a,this.formData.hiddenDangerReportorDeptName=i,this.formData.hiddenDangerReportorDeptCode=n,this.formData.hiddenDangerRectifyApprover=e,this.formData.hiddenDangerRectifyApproverFullname=a,this.formData.hiddenDangerRectifyApproverDeptName=i,this.formData.hiddenDangerRectifyApproverDeptCode=n}else this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:a=[],entityObject:t}=e;this.detailParamList=a,this.formData={...this.formData,...t}}))}))},async onDraft(){try{const e={...this.formData,rectifyState:"暂存"};this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{await this.$refs.form.validate();let e="待整改";"UserTask_0"===this.taskKey?e="待整改":"UserTask_2"===this.taskKey?e="待审核":"UserTask_3"===this.taskKey&&(e="整改完成",this.formData.finishDate=this.$dayjs().format("YYYY-MM-DD HH:mm:ss"));const a={...this.formData,rectifyState:e};this.$refs.FlowForm.onSubmit(this.service.submit,a)}catch(e){console.log(e)}},async updateFiles(){return new Promise((async(e,a)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),e()}catch(t){a()}}))},afterSubmit(e,a){this.updateFiles(),"submit"===e&&("UserTask_0"==this.taskKey&&function(e){t({url:"/cybereng-safety/safety/hiddenDanger/delayTask",method:"get",params:e})}({id:this.formData.id}),"UserTask_1"!=this.taskKey&&"UserTask_2"!=this.taskKey||function(e){t({url:"/cybereng-safety/safety/hiddenDanger/sendMessage",method:"get",params:e})}({id:this.formData.id}),this.formData.id&&function(e){t({url:"/cybereng-safety/yxst/safety/hiddenDanger",method:"put",params:e})}({id:this.formData.id}))},handleSubProjectChange(e={}){this.formData.unitEngineeringId="",this.formData.unitEngineeringName="",this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""},handleUnitEngineeringChange(e={}){this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""}}},[["render",function(e,a,t,r,i,n){const o=u("van-field"),d=u("FormItemPicker"),l=u("FormItemCoord"),s=u("FormItemCalendar"),m=u("van-cell-group"),y=u("UploadFiles"),h=u("FormItemPerson"),v=u("van-form"),b=u("FlowForm");return f(),p(b,{ref:"FlowForm","model-key":i.modelKey,"form-key":i.formKey,"entity-name":i.entityName,"detail-param-list":i.detailParamList,"detail-entity-name-list":i.detailEntityNameList,onDraftClick:n.onDraft,onSubmitClick:n.onSubmit,onAfterSubmit:n.afterSubmit,onCopyCallBack:n.copyCallBack},{default:D((()=>[g(v,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:D((()=>[g(m,{border:!1},{default:D((()=>[i.formData.hiddenDangerCode?(f(),p(o,{key:0,modelValue:i.formData.hiddenDangerCode,"onUpdate:modelValue":a[0]||(a[0]=e=>i.formData.hiddenDangerCode=e),label:"隐患整改单号",placeholder:"审批完成后自动生成",readonly:""},null,8,["modelValue"])):c("",!0),g(d,{label:"子工程",value:i.formData.subProjectId,"onUpdate:value":a[1]||(a[1]=e=>i.formData.subProjectId=e),text:i.formData.subProjectName,"onUpdate:text":a[2]||(a[2]=e=>i.formData.subProjectName=e),columns:[...n.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:1!=n.portal.type||"view"===i.type||!n.canEdit0,onChange:n.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),g(d,{label:"单位工程",value:i.formData.unitEngineeringId,"onUpdate:value":a[3]||(a[3]=e=>i.formData.unitEngineeringId=e),text:i.formData.unitEngineeringName,"onUpdate:text":a[4]||(a[4]=e=>i.formData.unitEngineeringName=e),columns:[...n.unitEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择单位工程",required:"",rules:[{required:!0,message:"请选择单位工程"}],readonly:"view"===i.type||!n.canEdit0,onChange:n.handleUnitEngineeringChange},null,8,["value","text","columns","readonly","onChange"]),g(d,{label:"分部工程",value:i.formData.divisionEngineeringId,"onUpdate:value":a[5]||(a[5]=e=>i.formData.divisionEngineeringId=e),text:i.formData.divisionEngineeringName,"onUpdate:text":a[6]||(a[6]=e=>i.formData.divisionEngineeringName=e),columns:[...n.divisionEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择分部工程",required:"",rules:[{required:!0,message:"请选择分部工程"}],readonly:"view"===i.type||!n.canEdit0},null,8,["value","text","columns","readonly"]),g(l,{label:"定位",longitude:i.formData.longitude,"onUpdate:longitude":a[7]||(a[7]=e=>i.formData.longitude=e),latitude:i.formData.latitude,"onUpdate:latitude":a[8]||(a[8]=e=>i.formData.latitude=e),title:"选择定位",readonly:"view"===i.type||!n.canEdit0},null,8,["longitude","latitude","readonly"]),g(d,{label:"隐患级别",value:i.formData.hiddenDangerLevel,"onUpdate:value":a[9]||(a[9]=e=>i.formData.hiddenDangerLevel=e),"dict-name":"hidden_danger_level",title:"选择隐患级别",required:"",rules:[{required:!0,message:"请选择隐患级别"}],readonly:"view"===i.type||!n.canEdit0},null,8,["value","readonly"]),g(d,{label:"隐患分类",value:i.formData.hiddenDangerCategory,"onUpdate:value":a[10]||(a[10]=e=>i.formData.hiddenDangerCategory=e),"dict-name":"hidden_danger_category",title:"选择隐患分类",required:"",rules:[{required:!0,message:"请选择隐患分类"}],readonly:"view"===i.type||!n.canEdit0},null,8,["value","readonly"]),g(s,{label:"整改期限",value:i.formData.rectifyDate,"onUpdate:value":a[11]||(a[11]=e=>i.formData.rectifyDate=e),title:"选择整改期限",required:"",rules:[{required:!0,message:"请选择整改期限"}],readonly:"view"===i.type||!n.canEdit0},null,8,["value","readonly"]),"add"!==i.type?(f(),p(o,{key:1,modelValue:i.formData.rectifyState,"onUpdate:modelValue":a[12]||(a[12]=e=>i.formData.rectifyState=e),label:"整改状态",placeholder:"",readonly:""},null,8,["modelValue"])):c("",!0),"add"!==i.type?(f(),p(o,{key:2,modelValue:i.formData.overdueState,"onUpdate:modelValue":a[13]||(a[13]=e=>i.formData.overdueState=e),label:"逾期情况",placeholder:"",readonly:""},null,8,["modelValue"])):c("",!0)])),_:1}),g(m,{border:!1},{default:D((()=>[g(o,{label:"隐患内容",modelValue:i.formData.hiddenDangerContent,"onUpdate:modelValue":a[14]||(a[14]=e=>i.formData.hiddenDangerContent=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入隐患内容",required:"",rules:[{required:!0,message:"请输入隐患内容"}],"input-align":"left",readonly:"view"===i.type||!n.canEdit0},null,8,["modelValue","readonly"]),g(o,{label:"附件照片","label-align":"top","input-align":"left"},{input:D((()=>[g(y,{ref:"beforeFiles",g9s:i.formData.beforeFileToken,"onUpdate:g9s":a[15]||(a[15]=e=>i.formData.beforeFileToken=e),accept:"image/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===i.type||!n.canEdit0},null,8,["g9s","readonly"])])),_:1})])),_:1}),g(m,{border:!1},{default:D((()=>[g(h,{label:"隐患上报人",userName:i.formData.hiddenDangerReportor,"onUpdate:userName":a[16]||(a[16]=e=>i.formData.hiddenDangerReportor=e),userFullname:i.formData.hiddenDangerReportorFullname,"onUpdate:userFullname":a[17]||(a[17]=e=>i.formData.hiddenDangerReportorFullname=e),deptName:i.formData.hiddenDangerReportorDeptName,"onUpdate:deptName":a[18]||(a[18]=e=>i.formData.hiddenDangerReportorDeptName=e),deptCode:i.formData.hiddenDangerReportorDeptCode,"onUpdate:deptCode":a[19]||(a[19]=e=>i.formData.hiddenDangerReportorDeptCode=e),title:"选择隐患上报人",required:"",rules:[{required:!0,message:"请选择隐患上报人"}],readonly:!0},null,8,["userName","userFullname","deptName","deptCode"]),g(o,{modelValue:i.formData.hiddenDangerReportorDeptName,"onUpdate:modelValue":a[20]||(a[20]=e=>i.formData.hiddenDangerReportorDeptName=e),label:"上报人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),g(h,{label:"隐患整改人",userName:i.formData.hiddenDangerRectifier,"onUpdate:userName":a[21]||(a[21]=e=>i.formData.hiddenDangerRectifier=e),userFullname:i.formData.hiddenDangerRectifierFullname,"onUpdate:userFullname":a[22]||(a[22]=e=>i.formData.hiddenDangerRectifierFullname=e),deptName:i.formData.hiddenDangerRectifierDeptName,"onUpdate:deptName":a[23]||(a[23]=e=>i.formData.hiddenDangerRectifierDeptName=e),deptCode:i.formData.hiddenDangerRectifierDeptCode,"onUpdate:deptCode":a[24]||(a[24]=e=>i.formData.hiddenDangerRectifierDeptCode=e),title:"选择隐患整改人",required:"",rules:[{required:!0,message:"请选择隐患整改人"}],readonly:"view"===i.type||!n.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),g(o,{modelValue:i.formData.hiddenDangerRectifierDeptName,"onUpdate:modelValue":a[25]||(a[25]=e=>i.formData.hiddenDangerRectifierDeptName=e),label:"整改人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),g(h,{label:"隐患审核人",userName:i.formData.hiddenDangerRectifyApprover,"onUpdate:userName":a[26]||(a[26]=e=>i.formData.hiddenDangerRectifyApprover=e),userFullname:i.formData.hiddenDangerRectifyApproverFullname,"onUpdate:userFullname":a[27]||(a[27]=e=>i.formData.hiddenDangerRectifyApproverFullname=e),deptName:i.formData.hiddenDangerRectifyApproverDeptName,"onUpdate:deptName":a[28]||(a[28]=e=>i.formData.hiddenDangerRectifyApproverDeptName=e),deptCode:i.formData.hiddenDangerRectifyApproverDeptCode,"onUpdate:deptCode":a[29]||(a[29]=e=>i.formData.hiddenDangerRectifyApproverDeptCode=e),title:"选择隐患审核人",required:"",rules:[{required:!0,message:"请选择隐患审核人"}],readonly:"view"===i.type||!n.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),g(o,{modelValue:i.formData.hiddenDangerRectifyApproverDeptName,"onUpdate:modelValue":a[30]||(a[30]=e=>i.formData.hiddenDangerRectifyApproverDeptName=e),label:"审核人所在部门",placeholder:"",readonly:""},null,8,["modelValue"])])),_:1}),"view"===i.type||i.taskKey&&"UserTask_0"!==i.taskKey?(f(),p(m,{key:0,border:!1},{default:D((()=>[g(o,{label:"整改措施",modelValue:i.formData.rectifyMeasures,"onUpdate:modelValue":a[31]||(a[31]=e=>i.formData.rectifyMeasures=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改措施",required:"",rules:[{required:!0,message:"请填写整改措施"}],"input-align":"left",readonly:"view"===i.type||!n.canEdit2},null,8,["modelValue","readonly"]),g(o,{label:"整改情况",modelValue:i.formData.rectifySituation,"onUpdate:modelValue":a[32]||(a[32]=e=>i.formData.rectifySituation=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改情况",required:"",rules:[{required:!0,message:"请填写整改情况"}],"input-align":"left",readonly:"view"===i.type||!n.canEdit2},null,8,["modelValue","readonly"]),g(o,{label:"附件照片","label-align":"top","input-align":"left"},{input:D((()=>[g(y,{ref:"afterFiles",g9s:i.formData.afterFileToken,"onUpdate:g9s":a[33]||(a[33]=e=>i.formData.afterFileToken=e),accept:"image/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===i.type||!n.canEdit2},null,8,["g9s","readonly"])])),_:1})])),_:1})):c("",!0)])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit","onCopyCallBack"])}]]))}}}));
