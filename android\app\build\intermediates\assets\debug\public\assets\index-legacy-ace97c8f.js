System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var l,n,t,s,o,c,m,d,u,p;return{setters:[e=>{l=e.F,n=e.D},e=>{t=e._},e=>{s=e.Q,o=e.R,c=e.X,m=e.V,d=e.k,u=e.U,p=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"one-line"},i={class:"form-info"},r={class:"form-info"},f={class:"form-info"},h={class:"check-wp"},g={class:"check-wp"},k={class:"form-info"},b={class:"form-info"},y={class:"form-info"},V={class:"attachment-desc"},D={class:"comment-wp"},C={class:"one-line"},v={class:"check-wp"},j={class:"check-wp"},w={class:"footer-input"},U={class:"form-info"},P={class:"form-info"},T={class:"form-info"},x={class:"form-info"};e("default",t({name:"JL03",components:{FormTemplate:l,DocumentPart:n},emits:[],props:{},setup(e,{attrs:a,slots:l,emit:n}){},data:()=>({detailTable:[],attachmentDesc:"批复意见"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit({formData:e,detailParamList:a,taskStart:l},n){return new Promise(((a,t)=>{try{if("submit"===n&&l&&!e.check1&&!e.check2)return this.$showNotify({type:"danger",message:"请选择开工类型!",duration:3e3}),t(!1),!1;a()}catch(s){t(s)}}))},changeCheck1(e){e&&(this.$refs.FormTemplate.formData.check2=!1)},changeCheck2(e){e&&(this.$refs.FormTemplate.formData.check1=!1)}}},[["render",function(e,l,n,t,F,N){const L=s("van-checkbox"),O=s("van-field"),S=s("DocumentPart"),q=s("FormTemplate");return o(),c(q,{ref:"FormTemplate",nature:"分开工","on-after-init":N.onAfterInit,"on-before-submit":N.onBeforeSubmit,"detail-table":F.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:F.attachmentDesc},{default:m((({formData:e,formTable:n,baseObj:t,uploadAccept:s,taskStart:o,taskComment2:c,taskComment3:w,taskComment4:U})=>[d(S,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:t.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!o},{default:m((()=>[u("div",a,[l[0]||(l[0]=u("span",{style:{"padding-left":"2em"}},"你方",-1)),u("span",i,p(e.field1),1),l[1]||(l[1]=u("span",null,"年",-1)),u("span",r,p(e.field2),1),l[2]||(l[2]=u("span",null,"月",-1)),u("span",f,p(e.field3),1),l[3]||(l[3]=u("span",null,"日报送的",-1)),u("div",h,[d(L,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!0,onChange:N.changeCheck1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),l[4]||(l[4]=u("span",null,"分部工程",-1)),l[5]||(l[5]=u("span",null,"/",-1)),u("div",g,[d(L,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!0,onChange:N.changeCheck2},null,8,["modelValue","onUpdate:modelValue","onChange"])]),l[6]||(l[6]=u("span",null,"分部工程部分工作",-1)),l[7]||(l[7]=u("span",null,"开工申请表",-1)),l[8]||(l[8]=u("span",null,"（ ",-1)),u("span",k,p(e.constructionName),1),l[9]||(l[9]=u("span",null," [ ",-1)),u("span",b,p(e.field4),1),l[10]||(l[10]=u("span",null," ] 分开工",-1)),u("span",y,p(e.field5),1),l[11]||(l[11]=u("span",null,"号）",-1)),l[12]||(l[12]=u("span",null,"已经通过审核，",-1)),l[13]||(l[13]=u("span",null,"同意开工。",-1))]),u("div",V,[l[14]||(l[14]=u("div",null,"附件：",-1)),d(O,{modelValue:e.attachmentDesc,"onUpdate:modelValue":a=>e.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),d(S,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:t.epcDeptName,personLabel:"项目负责人：",labelWidth:"10em",disabled:!0},{default:m((()=>[u("div",D,[u("div",C,[l[15]||(l[15]=u("span",null,"今已收到",-1)),u("div",v,[d(L,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!0,onChange:N.changeCheck1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),l[16]||(l[16]=u("span",null,"分部工程",-1)),l[17]||(l[17]=u("span",null,"/",-1)),u("div",j,[d(L,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!0,onChange:N.changeCheck2},null,8,["modelValue","onUpdate:modelValue","onChange"])]),l[18]||(l[18]=u("span",null,"分部工程部分工作",-1)),l[19]||(l[19]=u("span",null,"的开工批复。",-1))])])])),_:2},1032,["deptValue","deptOptions"])])),footer:m((({formData:e,formTable:a,baseObj:n,uploadAccept:t,taskStart:s,taskComment2:o,taskComment3:c,taskComment4:m})=>[u("div",w,[l[20]||(l[20]=u("span",null,"说明：本表一式",-1)),u("span",U,p(e.num1),1),l[21]||(l[21]=u("span",null,"份，由监理机构填写。承包人签收后，承包人",-1)),u("span",P,p(e.num2),1),l[22]||(l[22]=u("span",null,"份，监理机构",-1)),u("span",T,p(e.num3),1),l[23]||(l[23]=u("span",null,"份，发包人",-1)),u("span",x,p(e.num4),1),l[24]||(l[24]=u("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
