System.register(["./verder-legacy-e6127216.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-09188690.js","./index-legacy-645a3645.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js"],(function(e,t){"use strict";var n,i,r,o,a,s,l,p,c,u,m,d,h,f,y,g,v,b,w,x,E,_,S,A,C,R,P,k,M,T,N,D,O,I,L,B,F,j,V;return{setters:[e=>{n=e.a,i=e.a8,r=e.Q,o=e.R,a=e.X,s=e.V,l=e.S,p=e.F,c=e.W,u=e.U,m=e.Y,d=e.Z,h=e.k,f=e.B,y=e.a9,g=e.a1,v=e.y,b=e.a2,w=e.t,x=e.v},e=>{E=e.g,_=e.a,S=e.b,A=e.c,C=e.e,R=e.s,P=e.p,k=e.f,M=e.h,T=e.i,N=e.u,D=e.j},e=>{O=e.u},e=>{I=e._,L=e.m,B=e.x},e=>{F=e.U},e=>{j=e.f},e=>{V=e.F}],execute:function(){var t=document.createElement("style");t.textContent=".flow-step .step[data-v-ed60f061]{font-weight:400;font-size:3.2vw;color:#666}.bmp-container .exmple-wp[data-v-ff27054e]{display:flex;flex-direction:row;align-items:center;padding-bottom:6.4vw}.bmp-container .exmple-wp .exmple-item[data-v-ff27054e]{margin-left:3.73333vw;display:inline-flex;flex-direction:row;align-items:center}.bmp-container .exmple-wp .exmple-item .exmple-icon[data-v-ff27054e]{height:3.73333vw;width:3.73333vw;border-radius:.53333vw;margin-right:1.06667vw;border:.53333vw solid #71c54b}.bmp-container .exmple-wp .exmple-item .exmple-icon.current[data-v-ff27054e]{border-color:#59a0fe}.bmp-container .exmple-wp .exmple-item .exmple-icon.done[data-v-ff27054e]{border-color:#71c54b}.bmp-container .exmple-wp .exmple-item .exmple-icon.process[data-v-ff27054e]{border-color:#535353}.bmp-container .exmple-wp .exmple-item .exmple-text[data-v-ff27054e]{font-size:3.2vw;color:#333}.current-highlight:not(.djs-connection) .djs-visual>:nth-child(1){stroke:#59a0fe!important}.highlight:not(.djs-connection):not(.current-highlight) .djs-visual>:nth-child(1){stroke:#71c54b!important;fill:#f1f9ed!important}.bpm-initiator-infos[data-v-d09c136a]{margin-bottom:2.66667vw}.pop-window[data-v-7e388d2b]{position:relative;box-sizing:border-box}.pop-window-header[data-v-7e388d2b]{position:relative;display:flex;align-items:center;justify-content:space-between;height:var(--van-picker-toolbar-height)}.pop-window-body .list-content[data-v-7e388d2b]{padding:2.66667vw 0;min-height:77.86667vw;max-height:50vh;box-sizing:border-box;overflow-y:auto}.person-cell.select[data-v-7e388d2b]{background-color:var(--primary-color);color:#fff}.person-cell.select[data-v-7e388d2b] .van-cell__value{color:#f7f7f7}.person-cell[data-v-7e388d2b] .van-cell__title{flex:initial}.person-cell[data-v-7e388d2b] .van-cell__value{flex:1;text-align:right}.pop-window-footer[data-v-7e388d2b]{padding-left:var(--van-padding-md);padding-right:var(--van-padding-md);background-color:#fff}.pop-window-footer .confirm-button[data-v-7e388d2b]{margin:2.66667vw 0}.bpm-person-infos[data-v-87d93fe7]{margin-bottom:2.66667vw}.label[data-v-87d93fe7]{font-size:3.73333vw;color:#646566}.bpm-person-view[data-v-d92c9a21]{margin-bottom:2.66667vw}.view-height[data-v-e6f48463]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat));overflow-x:hidden;overflow-y:scroll}.view-height.has-tab[data-v-e6f48463]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height))}.buttons[data-v-e6f48463]{display:flex;padding:4vw 2.66667vw;padding-bottom:calc(4vw + var(--sab));background-color:#fff}.buttons .button[data-v-e6f48463]{flex:1;display:flex;align-items:center;justify-content:center;margin:0 2.66667vw}\n",document.head.appendChild(t);const $=e=>{const t=n({loading:!1});return[(...n)=>new Promise((async(i,r)=>{if(!t.loading&&e)try{t.loading=!0,i(await e(...n))}catch(q){r(q)}finally{setTimeout((()=>{t.loading=!1}),30)}})),i(t,"loading")]},z=e("b",{_Object:{submit:{type:"submit",text:"同意",code:"SUBMIT",customCode:"SUBMIT"},saveDraft:{type:"saveDraft",text:"暂存",code:"SAVEDRAFT",customCode:"SAVEDRAFT"},reject:{type:"reject",text:"退回",code:"REJECT",customCode:"REJECT",targetNode:"fawkes_custom_flow_start"},abandon:{type:"abandon",text:"废弃",code:"ABANDON",customCode:"ABANDON"},addCountersignee:{type:"addCountersignee",text:"加签",code:"ADDCOUNTERSIGNEE",customCode:"ADDCOUNTERSIGNEE"},circulate:{type:"circulate",text:"抄送",code:"CIRCULATE",customCode:"CIRCULATE"}},_Array:[{type:"submit",text:"同意",code:"SUBMIT",customCode:"SUBMIT"},{type:"saveDraft",text:"暂存",code:"SAVEDRAFT",customCode:"SAVEDRAFT"},{type:"reject",text:"退回",code:"REJECT",customCode:"REJECT",targetNode:"fawkes_custom_flow_start"},{type:"abandon",text:"废弃",code:"ABANDON",customCode:"ABANDON"},{type:"addCountersignee",text:"加签",code:"ADDCOUNTERSIGNEE",customCode:"ADDCOUNTERSIGNEE"},{type:"circulate",text:"抄送",code:"CIRCULATE",customCode:"CIRCULATE"}]}),U={name:"FlowTimeline",components:{},props:{bizId:{type:String,default:""},taskId:{type:String,default:""}},emits:[],setup(e,{attrs:t,slots:n,emit:i}){},data:()=>({loading:!1,list:[],signUrl:"/api/sys-user/user/sign_token"}),computed:{},watch:{},created(){},mounted(){this.getProcessHistory()},methods:{async getProcessHistory(){try{this.loading=!0;const e=await E({bizId:this.bizId,taskId:this.taskId});this.list=(e||[]).reverse()}catch(q){console.log(q)}finally{this.loading=!1}}}},W={class:"mt-0 mb-[5px]"},K={key:0},G={key:1},H=I(U,[["render",function(e,t,n,i,h,f){const y=r("van-step"),g=r("van-steps"),v=r("van-empty");return h.list&&h.list.length?(o(),a(g,{key:0,class:"flow-step",direction:"vertical","inactive-icon":"checked","active-icon":"clock",active:0},{default:s((()=>[(o(!0),l(p,null,c([...h.list],((e,t)=>(o(),a(y,{key:t,class:"step"},{default:s((()=>[u("h4",W,m(e.taskName),1),u("div",null,"处理人: "+m(e.assigneeName),1),u("div",null,"状态: "+m(e.approveStateName),1),u("div",null,"发起时间:"+m(e.createDate),1),e.approveDate?(o(),l("div",K,"处理时间:"+m(e.approveDate),1)):d("",!0),e.comment?(o(),l("div",G,"处理意见:"+m(e.comment),1)):d("",!0)])),_:2},1024)))),128))])),_:1})):(o(),a(v,{key:1,description:"暂无流转"}))}],["__scopeId","data-v-ed60f061"]]);function q(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}const Y=Object.prototype.toString,X=Object.prototype.hasOwnProperty;function J(e){return void 0===e}function Q(e){return void 0!==e}function Z(e){return null==e}function ee(e){return"[object Array]"===Y.call(e)}function te(e){return"[object Object]"===Y.call(e)}function ne(e){return"[object Number]"===Y.call(e)}function ie(e){const t=Y.call(e);return"[object Function]"===t||"[object AsyncFunction]"===t||"[object GeneratorFunction]"===t||"[object AsyncGeneratorFunction]"===t||"[object Proxy]"===t}function re(e){return"[object String]"===Y.call(e)}function oe(e,t){return!Z(e)&&X.call(e,t)}function ae(e,t){const n=de(t);let i;return le(e,(function(e,t){if(n(e,t))return i=e,!1})),i}function se(e,t){const n=de(t);let i=[];return le(e,(function(e,t){n(e,t)&&i.push(e)})),i}function le(e,t){let n,i;if(J(e))return;const r=ee(e)?fe:he;for(let o in e)if(oe(e,o)&&(n=e[o],i=t(n,r(o)),!1===i))return n}function pe(e,t,n){return le(e,(function(e,i){n=t(n,e,i)})),n}function ce(e,t){return!!pe(e,(function(e,n,i){return e&&t(n,i)}),!0)}function ue(e,t){return!!ae(e,t)}function me(e){return function(t){return ce(e,(function(e,n){return t[n]===e}))}}function de(e){return ie(e)?e:t=>t===e}function he(e){return e}function fe(e){return Number(e)}function ye(e,t){return e.bind(t)}function ge(e,...t){return Object.assign(e,...t)}function ve(e,t){var n=this;t=t||1e3,e.on(["render.shape","render.connection"],t,(function(e,t){var i=e.type,r=t.element,o=t.gfx,a=t.attrs;if(n.canRender(r))return"render.shape"===i?n.drawShape(o,r,a):n.drawConnection(o,r,a)})),e.on(["render.getShapePath","render.getConnectionPath"],t,(function(e,t){if(n.canRender(t))return"render.getShapePath"===e.type?n.getShapePath(t):n.getConnectionPath(t)}))}function be(e,t){var n=we(e);return n&&"function"==typeof n.$instanceOf&&n.$instanceOf(t)}function we(e){return e&&e.businessObject||e}function xe(e){return e&&e.di}function Ee(e,t){return!be(e,"bpmn:CallActivity")&&(be(e,"bpmn:SubProcess")?!(!(t=t||xe(e))||!be(t,"bpmndi:BPMNPlane"))||t&&!!t.isExpanded:!be(e,"bpmn:Participant")||!!we(e).processRef)}ve.prototype.canRender=function(e){},ve.prototype.drawShape=function(e,t){},ve.prototype.drawConnection=function(e,t){},ve.prototype.getShapePath=function(e){},ve.prototype.getConnectionPath=function(e){};var _e={width:90,height:20};function Se(e){var t=e.length/2-1,n=e[Math.floor(t)],i=e[Math.ceil(t+.01)],r=function(e){var t=e.length/2-1,n=e[Math.floor(t)],i=e[Math.ceil(t+.01)];return{x:n.x+(i.x-n.x)/2,y:n.y+(i.y-n.y)/2}}(e),o=Math.atan((i.y-n.y)/(i.x-n.x)),a=r.x,s=r.y;return Math.abs(o)<Math.PI/2?s-=15:a+=15,{x:a,y:s}}function Ae(e,t){var n,i,r,o=e.label;return o&&o.bounds?(r=o.bounds,i={width:Math.max(_e.width,r.width),height:r.height},n={x:r.x+r.width/2,y:r.y+r.height/2}):(n=function(e){return e.waypoints?Se(e.waypoints):be(e,"bpmn:Group")?{x:e.x+e.width/2,y:e.y+_e.height/2}:{x:e.x+e.width/2,y:e.y+e.height+_e.height/2}}(t),i=_e),ge({x:n.x-i.width/2,y:n.y-i.height/2},i)}function Ce(e){var t=e.businessObject,n=function(e){return be(e,"bpmn:FlowElement")||be(e,"bpmn:Participant")||be(e,"bpmn:Lane")||be(e,"bpmn:SequenceFlow")||be(e,"bpmn:MessageFlow")||be(e,"bpmn:DataInput")||be(e,"bpmn:DataOutput")?"name":be(e,"bpmn:TextAnnotation")?"text":be(e,"bpmn:Group")?"categoryValueRef":void 0}(t);if(n)return"categoryValueRef"===n?function(e){var t=e.categoryValueRef;return t&&t.value||""}(t):t[n]||""}function Re(e,t){return t.appendChild(function(e,t){if(e.ownerDocument!==t.ownerDocument)try{return t.ownerDocument.importNode(e,!0)}catch(q){}return e}(e,t))}function Pe(e,t){return Re(t,e),e}var ke={"alignment-baseline":1,"baseline-shift":1,clip:1,"clip-path":1,"clip-rule":1,color:1,"color-interpolation":1,"color-interpolation-filters":1,"color-profile":1,"color-rendering":1,cursor:1,direction:1,display:1,"dominant-baseline":1,"enable-background":1,fill:1,"fill-opacity":1,"fill-rule":1,filter:1,"flood-color":1,"flood-opacity":1,font:1,"font-family":1,"font-size":2,"font-size-adjust":1,"font-stretch":1,"font-style":1,"font-variant":1,"font-weight":1,"glyph-orientation-horizontal":1,"glyph-orientation-vertical":1,"image-rendering":1,kerning:1,"letter-spacing":1,"lighting-color":1,marker:1,"marker-end":1,"marker-mid":1,"marker-start":1,mask:1,opacity:1,overflow:1,"pointer-events":1,"shape-rendering":1,"stop-color":1,"stop-opacity":1,stroke:1,"stroke-dasharray":1,"stroke-dashoffset":1,"stroke-linecap":1,"stroke-linejoin":1,"stroke-miterlimit":1,"stroke-opacity":1,"stroke-width":2,"text-anchor":1,"text-decoration":1,"text-rendering":1,"unicode-bidi":1,visibility:1,"word-spacing":1,"writing-mode":1};function Me(e,t,n){var i=t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),r=ke[i];r?(2===r&&"number"==typeof n&&(n=String(n)+"px"),e.style[i]=n):e.setAttributeNS(null,t,n)}function Te(e,t,n){if("string"==typeof t){if(void 0===n)return function(e,t){return ke[t]?e.style[t]:e.getAttributeNS(null,t)}(e,t);Me(e,t,n)}else!function(e,t){var n,i,r=Object.keys(t);for(n=0;i=r[n];n++)Me(e,i,t[i])}(e,t);return e}const Ne=Object.prototype.toString;function De(e){return new Oe(e)}function Oe(e){if(!e||!e.nodeType)throw new Error("A DOM element reference is required");this.el=e,this.list=e.classList}function Ie(e){for(var t;t=e.firstChild;)e.removeChild(t);return e}Oe.prototype.add=function(e){return this.list.add(e),this},Oe.prototype.remove=function(e){return"[object RegExp]"==Ne.call(e)?this.removeMatching(e):(this.list.remove(e),this)},Oe.prototype.removeMatching=function(e){const t=this.array();for(let n=0;n<t.length;n++)e.test(t[n])&&this.remove(t[n]);return this},Oe.prototype.toggle=function(e,t){return void 0!==t?t!==this.list.toggle(e,t)&&this.list.toggle(e):this.list.toggle(e),this},Oe.prototype.array=function(){return Array.from(this.list)},Oe.prototype.has=Oe.prototype.contains=function(e){return this.list.contains(e)};var Le="http://www.w3.org/2000/svg",Be='<svg xmlns="'+Le+'"';function Fe(e){var t=!1;"<svg"===e.substring(0,4)?-1===e.indexOf(Le)&&(e=Be+e.substring(4)):(e=Be+">"+e+"</svg>",t=!0);var n=function(e){var t;return(t=new DOMParser).async=!1,t.parseFromString(e,"text/xml")}(e);if(!t)return n;for(var i=document.createDocumentFragment(),r=n.firstChild;r.firstChild;)i.appendChild(r.firstChild);return i}function je(e,t){var n;return"<"===(e=e.trim()).charAt(0)?(n=Fe(e).firstChild,n=document.importNode(n,!0)):n=document.createElementNS(Le,e),t&&Te(n,t),n}var Ve=null;function $e(){return null===Ve&&(Ve=je("svg")),Ve}function ze(e,t){var n,i,r=Object.keys(t);for(n=0;i=r[n];n++)e[i]=t[i];return e}function Ue(e){return e?$e().createSVGTransformFromMatrix(e):$e().createSVGTransform()}var We=/([&<>]{1})/g,Ke=/([&<>\n\r"]{1})/g,Ge={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"'"};function He(e,t){return e.replace(t,(function(e,t){return Ge[t]||t}))}function qe(e,t){var n,i,r,o,a;switch(e.nodeType){case 3:t.push(He(e.textContent,We));break;case 1:if(t.push("<",e.tagName),e.hasAttributes())for(n=0,i=(r=e.attributes).length;n<i;++n)o=r.item(n),t.push(" ",o.name,'="',He(o.value,Ke),'"');if(e.hasChildNodes()){for(t.push(">"),n=0,i=(a=e.childNodes).length;n<i;++n)qe(a.item(n),t);t.push("</",e.tagName,">")}else t.push("/>");break;case 8:t.push("\x3c!--",He(e.nodeValue,We),"--\x3e");break;case 4:t.push("<![CDATA[",e.nodeValue,"]]>");break;default:throw new Error("unable to handle node "+e.nodeType)}return t}function Ye(e,t){var n=Fe(t);if(Ie(e),t){(function(e){return"#document-fragment"===e.nodeName})(n)||(n=n.documentElement);for(var i,r=(i=n.childNodes,Array.prototype.slice.call(i)),o=0;o<r.length;o++)Re(r[o],e)}}function Xe(e,t){if(void 0!==t){try{Ye(e,t)}catch(q){throw new Error("error parsing SVG: "+q.message)}return e}return function(e){for(var t=e.firstChild,n=[];t;)qe(t,n),t=t.nextSibling;return n.join("")}(e)}function Je(e){var t=e.parentNode;return t&&t.removeChild(e),e}function Qe(e,t){return t instanceof SVGMatrix?e.createSVGTransformFromMatrix(t):t}function Ze(e,t){var n=e.transform.baseVal;return t&&(Array.isArray(t)||(t=[t]),function(e,t){var n,i;for(e.clear(),n=0;i=t[n];n++)e.appendItem(Qe(e,i))}(n,t)),n.consolidate()}function et(e){return e.flat().join(",").replace(/,?([A-z]),?/g,"$1")}function tt(e){return["L",e.x,e.y]}function nt(e,t){const n=e.length,i=[(r=e[0],["M",r.x,r.y])];var r,o,a,s;for(let l=1;l<n;l++){const n=e[l-1],r=e[l],p=e[l+1];if(!p||!t){i.push(tt(r));continue}const c=Math.min(t,rt(r.x-n.x,r.y-n.y),rt(p.x-r.x,p.y-r.y));if(!c){i.push(tt(r));continue}const u=it(r,n,c),m=it(r,n,.5*c),d=it(r,p,c),h=it(r,p,.5*c);i.push(tt(u)),i.push((a=h,s=d,["C",(o=m).x,o.y,a.x,a.y,s.x,s.y]))}return i}function it(e,t,n){const i=t.x-e.x,r=t.y-e.y,o=n/rt(i,r);return{x:e.x+i*o,y:e.y+r*o}}function rt(e,t){return Math.sqrt(Math.pow(e,2)+Math.pow(t,2))}function ot(e,t,n){ne(t)&&(n=t,t=null),t||(t={});const i=je("path",t);return ne(n)&&(i.dataset.cornerRadius=String(n)),at(i,e)}function at(e,t){return Te(e,{d:et(nt(t,parseInt(e.dataset.cornerRadius,10)||0))}),e}var st="hsl(225, 10%, 15%)";function lt(e,t){return ue(e.eventDefinitions,(function(e){return e.$type===t}))}function pt(e,t){var n=xe(e);return n.get("color:background-color")||n.get("bioc:fill")||t||"white"}function ct(e,t){var n=xe(e);return n.get("color:border-color")||n.get("bioc:stroke")||t||st}function ut(e,t,n){var i=xe(e).get("label");return i&&i.get("color:color")||t||ct(e,n)}function mt(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(n){if("default"!==n&&!(n in e)){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}}))})),Object.freeze(e)}const dt=Object.prototype.toString,ht=Object.prototype.hasOwnProperty;function ft(e,t){return ht.call(e,t)}function yt(e,t){let n,i;if(void 0===e)return;const r=function(e){return"[object Array]"===dt.call(e)}(e)?vt:gt;for(let o in e)if(ft(e,o)&&(n=e[o],i=t(n,r(o)),!1===i))return n}function gt(e){return e}function vt(e){return Number(e)}function bt(e,...t){const n=e.style;return yt(t,(function(e){e&&yt(e,(function(e,t){n[t]=e}))})),e}const wt=Object.prototype.toString;function xt(e){return new Et(e)}function Et(e){if(!e||!e.nodeType)throw new Error("A DOM element reference is required");this.el=e,this.list=e.classList}function _t(e){for(var t;t=e.firstChild;)e.removeChild(t);return e}Et.prototype.add=function(e){return this.list.add(e),this},Et.prototype.remove=function(e){return"[object RegExp]"==wt.call(e)?this.removeMatching(e):(this.list.remove(e),this)},Et.prototype.removeMatching=function(e){const t=this.array();for(let n=0;n<t.length;n++)e.test(t[n])&&this.remove(t[n]);return this},Et.prototype.toggle=function(e,t){return void 0!==t?t!==this.list.toggle(e,t)&&this.list.toggle(e):this.list.toggle(e),this},Et.prototype.array=function(){return Array.from(this.list)},Et.prototype.has=Et.prototype.contains=function(e){return this.list.contains(e)};var St,At,Ct,Rt={};function Pt(){St=window.addEventListener?"addEventListener":"attachEvent",At=window.removeEventListener?"removeEventListener":"detachEvent",Ct="addEventListener"!==St?"on":""}var kt,Mt=mt({__proto__:null,bind:Rt.bind=function(e,t,n,i){return St||Pt(),e[St](Ct+t,n,i||!1),n},unbind:Rt.unbind=function(e,t,n,i){return At||Pt(),e[At](Ct+t,n,i||!1),n},default:Rt},[Rt]),Tt=["focus","blur"],Nt={bind:function(e,t,n,i,r){return-1!==Tt.indexOf(n)&&(r=!0),Mt.bind(e,n,(function(n){var r=n.target||n.srcElement;n.delegateTarget=function(e,t,n){var i=n?e:e.parentNode;return i&&"function"==typeof i.closest&&i.closest(t)||null}(r,t,!0),n.delegateTarget&&i.call(e,n)}),r)},unbind:function(e,t,n,i){return-1!==Tt.indexOf(t)&&(i=!0),Mt.unbind(e,t,n,i)}},Dt=function(e,t){if("string"!=typeof e)throw new TypeError("String expected");t||(t=document);var n=/<([\w:]+)/.exec(e);if(!n)return t.createTextNode(e);e=e.replace(/^\s+|\s+$/g,"");var i=n[1];if("body"==i)return(r=t.createElement("html")).innerHTML=e,r.removeChild(r.lastChild);var r,o=Object.prototype.hasOwnProperty.call(It,i)?It[i]:It._default,a=o[0],s=o[1],l=o[2];for((r=t.createElement("div")).innerHTML=s+e+l;a--;)r=r.lastChild;if(r.firstChild==r.lastChild)return r.removeChild(r.firstChild);for(var p=t.createDocumentFragment();r.firstChild;)p.appendChild(r.removeChild(r.firstChild));return p},Ot=!1;"undefined"!=typeof document&&((kt=document.createElement("div")).innerHTML='  <link/><table></table><a href="/a">a</a><input type="checkbox"/>',Ot=!kt.getElementsByTagName("link").length,kt=void 0);var It={legend:[1,"<fieldset>","</fieldset>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],_default:Ot?[1,"X<div>","</div>"]:[0,"",""]};It.td=It.th=[3,"<table><tbody><tr>","</tr></tbody></table>"],It.option=It.optgroup=[1,'<select multiple="multiple">',"</select>"],It.thead=It.tbody=It.colgroup=It.caption=It.tfoot=[1,"<table>","</table>"],It.polyline=It.ellipse=It.polygon=It.circle=It.text=It.line=It.path=It.rect=It.g=[1,'<svg xmlns="http://www.w3.org/2000/svg" version="1.1">',"</svg>"];var Lt=Dt;function Bt(e,t){return(t=t||document).querySelector(e)}function Ft(e){e.parentNode&&e.parentNode.removeChild(e)}function jt(e,t,n,i,r){var o=Ue();o.setTranslate(t,n);var a=Ue();a.setRotate(i||0,0,0);var s=Ue();s.setScale(r||1,r||1),Ze(e,[o,a,s])}function Vt(e,t,n){var i=Ue();i.setTranslate(t,n),Ze(e,i)}var $t=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e){var t=e.exports=function(e,n){if(n||(n=16),void 0===e&&(e=128),e<=0)return"0";for(var i=Math.log(Math.pow(2,e))/Math.log(n),r=2;i===1/0;r*=2)i=Math.log(Math.pow(2,e/r))/Math.log(n)*r;var o=i-Math.floor(i),a="";for(r=0;r<Math.floor(i);r++)a=Math.floor(Math.random()*n).toString(n)+a;if(o){var s=Math.pow(n,o);a=Math.floor(Math.random()*s).toString(n)+a}var l=parseInt(a,n);return l!==1/0&&l>=Math.pow(2,e)?t(e,n):a};t.rack=function(e,n,i){var r=function(r){var a=0;do{if(a++>10){if(!i)throw new Error("too many ID collisions, use more bits");e+=i}var s=t(e,n)}while(Object.hasOwnProperty.call(o,s));return o[s]=r,s},o=r.hats={};return r.get=function(e){return r.hats[e]},r.set=function(e,t){return r.hats[e]=t,r},r.bits=e||128,r.base=n||16,r}}));function zt(e){if(!(this instanceof zt))return new zt(e);e=e||[128,36,1],this._seed=e.length?$t.rack(e[0],e[1],e[2]):e}zt.prototype.next=function(e){return this._seed(e||!0)},zt.prototype.nextPrefixed=function(e,t){var n;do{n=e+this.next(!0)}while(this.assigned(n));return this.claim(n,t),n},zt.prototype.claim=function(e,t){this._seed.set(e,t||!0)},zt.prototype.assigned=function(e){return this._seed.get(e)||!1},zt.prototype.unclaim=function(e){delete this._seed.hats[e]},zt.prototype.clear=function(){var e,t=this._seed.hats;for(e in t)this.unclaim(e)};var Ut=new zt,Wt=.95;function Kt(e,t,n,i,r,o,a){ve.call(this,t,a);var s=e&&e.defaultFillColor,l=e&&e.defaultStrokeColor,p=e&&e.defaultLabelColor,c=Ut.next(),u={};function m(e){return n.computeStyle(e,{strokeLinecap:"round",strokeLinejoin:"round",stroke:st,strokeWidth:2,fill:"white"})}function d(e){return n.computeStyle(e,["no-fill"],{strokeLinecap:"round",strokeLinejoin:"round",stroke:st,strokeWidth:2})}function h(e,t){var{ref:n={x:0,y:0},scale:i=1,element:o}=t,a=je("marker",{id:e,viewBox:"0 0 20 20",refX:n.x,refY:n.y,markerWidth:20*i,markerHeight:20*i,orient:"auto"});Pe(a,o);var s=Bt("defs",r._svg);s||(s=je("defs"),Pe(r._svg,s)),Pe(s,a),u[e]=a}function f(e){return e.replace(/[^0-9a-zA-Z]+/g,"_")}function y(e,t,n){var i=e+"-"+f(t)+"-"+f(n)+"-"+c;return u[i]||function(e,t,n,i){"sequenceflow-end"===t&&h(e,{element:je("path",{d:"M 1 5 L 11 10 L 1 15 Z",...m({fill:i,stroke:i,strokeWidth:1})}),ref:{x:11,y:10},scale:.5}),"messageflow-start"===t&&h(e,{element:je("circle",{cx:6,cy:6,r:3.5,...m({fill:n,stroke:i,strokeWidth:1,strokeDasharray:[1e4,1]})}),ref:{x:6,y:6}}),"messageflow-end"===t&&h(e,{element:je("path",{d:"m 1 5 l 0 -3 l 7 3 l -7 3 z",...m({fill:n,stroke:i,strokeWidth:1,strokeDasharray:[1e4,1]})}),ref:{x:8.5,y:5}}),"association-start"===t&&h(e,{element:je("path",{d:"M 11 5 L 1 10 L 11 15",...d({fill:"none",stroke:i,strokeWidth:1.5,strokeDasharray:[1e4,1]})}),ref:{x:1,y:10},scale:.5}),"association-end"===t&&h(e,{element:je("path",{d:"M 1 5 L 11 10 L 1 15",...d({fill:"none",stroke:i,strokeWidth:1.5,strokeDasharray:[1e4,1]})}),ref:{x:11,y:10},scale:.5}),"conditional-flow-marker"===t&&h(e,{element:je("path",{d:"M 0 10 L 8 6 L 16 10 L 8 14 Z",...m({fill:n,stroke:i})}),ref:{x:-1,y:10},scale:.5}),"conditional-default-flow-marker"===t&&h(e,{element:je("path",{d:"M 6 4 L 10 16",...m({stroke:i})}),ref:{x:0,y:10},scale:.5})}(i,e,t,n),"url(#"+i+")"}function g(e,t,n,i,r){te(i)&&(r=i,i=0),i=i||0,"none"===(r=m(r)).fill&&delete r.fillOpacity;var o=je("circle",{cx:t/2,cy:n/2,r:Math.round((t+n)/4-i),...r});return Pe(e,o),o}function v(e,t,n,i,r,o){te(r)&&(o=r,r=0),r=r||0,o=m(o);var a=je("rect",{x:r,y:r,width:t-2*r,height:n-2*r,rx:i,ry:i,...o});return Pe(e,a),a}function b(e,t,n,i){var r=ot(t,n=d(n),i);return Pe(e,r),r}function w(e,t,n){return b(e,t,n,5)}function x(e,t,n){n=d(n);var i=je("path",{...n,d:t});return Pe(e,i),i}function E(e,t,n,i){return x(t,n,ge({"data-marker":e},i))}function _(e){return k[e]}function S(e){return function(t,n,i){return _(e)(t,n,i)}}function A(e,t){var n=we(e),i=function(e){return"bpmn:IntermediateThrowEvent"===e.$type||"bpmn:EndEvent"===e.$type}(n);return n.eventDefinitions&&n.eventDefinitions.length>1?n.parallelMultiple?_("bpmn:ParallelMultipleEventDefinition")(t,e,i):_("bpmn:MultipleEventDefinition")(t,e,i):lt(n,"bpmn:MessageEventDefinition")?_("bpmn:MessageEventDefinition")(t,e,i):lt(n,"bpmn:TimerEventDefinition")?_("bpmn:TimerEventDefinition")(t,e,i):lt(n,"bpmn:ConditionalEventDefinition")?_("bpmn:ConditionalEventDefinition")(t,e):lt(n,"bpmn:SignalEventDefinition")?_("bpmn:SignalEventDefinition")(t,e,i):lt(n,"bpmn:EscalationEventDefinition")?_("bpmn:EscalationEventDefinition")(t,e,i):lt(n,"bpmn:LinkEventDefinition")?_("bpmn:LinkEventDefinition")(t,e,i):lt(n,"bpmn:ErrorEventDefinition")?_("bpmn:ErrorEventDefinition")(t,e,i):lt(n,"bpmn:CancelEventDefinition")?_("bpmn:CancelEventDefinition")(t,e,i):lt(n,"bpmn:CompensateEventDefinition")?_("bpmn:CompensateEventDefinition")(t,e,i):lt(n,"bpmn:TerminateEventDefinition")?_("bpmn:TerminateEventDefinition")(t,e,i):null}function C(e,t,n){n=ge({size:{width:100}},n);var i=o.createText(t||"",n);return De(i).add("djs-label"),Pe(e,i),i}function R(e,t,n){return C(e,we(t).name,{box:t,align:n,padding:7,style:{fill:ut(t,p,l)}})}function P(e,t,n){jt(C(e,t,{box:{height:30,width:n.height},align:"center-middle",style:{fill:ut(n,p,l)}}),0,1*n.height,270)}var k=this.handlers={"bpmn:Event":function(e,t,n){return"fillOpacity"in n||(n.fillOpacity=Wt),g(e,t.width,t.height,n)},"bpmn:StartEvent":function(e,t,n){var i={fill:pt(t,s),stroke:ct(t,l)};we(t).isInterrupting||(i={strokeDasharray:"6",fill:pt(t,s),stroke:ct(t,l)});var r=_("bpmn:Event")(e,t,i);return n&&!1===n.renderIcon||A(t,e),r},"bpmn:MessageEventDefinition":function(e,t,n){return x(e,i.getScaledPath("EVENT_MESSAGE",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:t.width,containerHeight:t.height,position:{mx:.235,my:.315}}),{strokeWidth:1,fill:n?ct(t,l):pt(t,s),stroke:n?pt(t,s):ct(t,l)})},"bpmn:TimerEventDefinition":function(e,t){var n=g(e,t.width,t.height,.2*t.height,{strokeWidth:2,fill:pt(t,s),stroke:ct(t,l)});x(e,i.getScaledPath("EVENT_TIMER_WH",{xScaleFactor:.75,yScaleFactor:.75,containerWidth:t.width,containerHeight:t.height,position:{mx:.5,my:.5}}),{strokeWidth:2,stroke:ct(t,l)});for(var r=0;r<12;r++){var o=i.getScaledPath("EVENT_TIMER_LINE",{xScaleFactor:.75,yScaleFactor:.75,containerWidth:t.width,containerHeight:t.height,position:{mx:.5,my:.5}}),a=t.width/2;x(e,o,{strokeWidth:1,transform:"rotate("+30*r+","+t.height/2+","+a+")",stroke:ct(t,l)})}return n},"bpmn:EscalationEventDefinition":function(e,t,n){return x(e,i.getScaledPath("EVENT_ESCALATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.5,my:.2}}),{strokeWidth:1,fill:n?ct(t,l):"none",stroke:ct(t,l)})},"bpmn:ConditionalEventDefinition":function(e,t){return x(e,i.getScaledPath("EVENT_CONDITIONAL",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.5,my:.222}}),{strokeWidth:1,stroke:ct(t,l)})},"bpmn:LinkEventDefinition":function(e,t,n){return x(e,i.getScaledPath("EVENT_LINK",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.57,my:.263}}),{strokeWidth:1,fill:n?ct(t,l):"none",stroke:ct(t,l)})},"bpmn:ErrorEventDefinition":function(e,t,n){return x(e,i.getScaledPath("EVENT_ERROR",{xScaleFactor:1.1,yScaleFactor:1.1,containerWidth:t.width,containerHeight:t.height,position:{mx:.2,my:.722}}),{strokeWidth:1,fill:n?ct(t,l):"none",stroke:ct(t,l)})},"bpmn:CancelEventDefinition":function(e,t,n){var r=x(e,i.getScaledPath("EVENT_CANCEL_45",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.638,my:-.055}}),{strokeWidth:1,fill:n?ct(t,l):"none",stroke:ct(t,l)});return function(e,t){var n=Ue();n.setRotate(t,0,0),Ze(e,n)}(r,45),r},"bpmn:CompensateEventDefinition":function(e,t,n){return x(e,i.getScaledPath("EVENT_COMPENSATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.22,my:.5}}),{strokeWidth:1,fill:n?ct(t,l):"none",stroke:ct(t,l)})},"bpmn:SignalEventDefinition":function(e,t,n){return x(e,i.getScaledPath("EVENT_SIGNAL",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:t.width,containerHeight:t.height,position:{mx:.5,my:.2}}),{strokeWidth:1,fill:n?ct(t,l):"none",stroke:ct(t,l)})},"bpmn:MultipleEventDefinition":function(e,t,n){return x(e,i.getScaledPath("EVENT_MULTIPLE",{xScaleFactor:1.1,yScaleFactor:1.1,containerWidth:t.width,containerHeight:t.height,position:{mx:.222,my:.36}}),{strokeWidth:1,fill:n?ct(t,l):"none"})},"bpmn:ParallelMultipleEventDefinition":function(e,t){return x(e,i.getScaledPath("EVENT_PARALLEL_MULTIPLE",{xScaleFactor:1.2,yScaleFactor:1.2,containerWidth:t.width,containerHeight:t.height,position:{mx:.458,my:.194}}),{strokeWidth:1,fill:ct(t,l),stroke:ct(t,l)})},"bpmn:EndEvent":function(e,t,n){var i=_("bpmn:Event")(e,t,{strokeWidth:4,fill:pt(t,s),stroke:ct(t,l)});return n&&!1===n.renderIcon||A(t,e),i},"bpmn:TerminateEventDefinition":function(e,t){return g(e,t.width,t.height,8,{strokeWidth:4,fill:ct(t,l),stroke:ct(t,l)})},"bpmn:IntermediateEvent":function(e,t,n){var i=_("bpmn:Event")(e,t,{strokeWidth:1.5,fill:pt(t,s),stroke:ct(t,l)});return g(e,t.width,t.height,3,{strokeWidth:1.5,fill:pt(t,"none"),stroke:ct(t,l)}),n&&!1===n.renderIcon||A(t,e),i},"bpmn:IntermediateCatchEvent":S("bpmn:IntermediateEvent"),"bpmn:IntermediateThrowEvent":S("bpmn:IntermediateEvent"),"bpmn:Activity":function(e,t,n){return"fillOpacity"in(n=n||{})||(n.fillOpacity=Wt),v(e,t.width,t.height,10,n)},"bpmn:Task":function(e,t){var n={fill:pt(t,s),stroke:ct(t,l)},i=_("bpmn:Activity")(e,t,n);return R(e,t,"center-middle"),M(e,t),i},"bpmn:ServiceTask":function(e,t){var n=_("bpmn:Task")(e,t);return x(e,i.getScaledPath("TASK_TYPE_SERVICE",{abspos:{x:12,y:18}}),{strokeWidth:1,fill:pt(t,s),stroke:ct(t,l)}),x(e,i.getScaledPath("TASK_TYPE_SERVICE_FILL",{abspos:{x:17.2,y:18}}),{strokeWidth:0,fill:pt(t,s)}),x(e,i.getScaledPath("TASK_TYPE_SERVICE",{abspos:{x:17,y:22}}),{strokeWidth:1,fill:pt(t,s),stroke:ct(t,l)}),n},"bpmn:UserTask":function(e,t){var n=_("bpmn:Task")(e,t);return x(e,i.getScaledPath("TASK_TYPE_USER_1",{abspos:{x:15,y:12}}),{strokeWidth:.5,fill:pt(t,s),stroke:ct(t,l)}),x(e,i.getScaledPath("TASK_TYPE_USER_2",{abspos:{x:15,y:12}}),{strokeWidth:.5,fill:pt(t,s),stroke:ct(t,l)}),x(e,i.getScaledPath("TASK_TYPE_USER_3",{abspos:{x:15,y:12}}),{strokeWidth:.5,fill:ct(t,l),stroke:ct(t,l)}),n},"bpmn:ManualTask":function(e,t){var n=_("bpmn:Task")(e,t);return x(e,i.getScaledPath("TASK_TYPE_MANUAL",{abspos:{x:17,y:15}}),{strokeWidth:.5,fill:pt(t,s),stroke:ct(t,l)}),n},"bpmn:SendTask":function(e,t){var n=_("bpmn:Task")(e,t);return x(e,i.getScaledPath("TASK_TYPE_SEND",{xScaleFactor:1,yScaleFactor:1,containerWidth:21,containerHeight:14,position:{mx:.285,my:.357}}),{strokeWidth:1,fill:ct(t,l),stroke:pt(t,s)}),n},"bpmn:ReceiveTask":function(e,t){var n,r=we(t),o=_("bpmn:Task")(e,t);return r.instantiate?(g(e,28,28,4.4,{strokeWidth:1}),n=i.getScaledPath("TASK_TYPE_INSTANTIATING_SEND",{abspos:{x:7.77,y:9.52}})):n=i.getScaledPath("TASK_TYPE_SEND",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:21,containerHeight:14,position:{mx:.3,my:.4}}),x(e,n,{strokeWidth:1,fill:pt(t,s),stroke:ct(t,l)}),o},"bpmn:ScriptTask":function(e,t){var n=_("bpmn:Task")(e,t);return x(e,i.getScaledPath("TASK_TYPE_SCRIPT",{abspos:{x:15,y:20}}),{strokeWidth:1,stroke:ct(t,l)}),n},"bpmn:BusinessRuleTask":function(e,t){var n=_("bpmn:Task")(e,t);return Te(x(e,i.getScaledPath("TASK_TYPE_BUSINESS_RULE_HEADER",{abspos:{x:8,y:8}})),{strokeWidth:1,fill:pt(t,"#aaaaaa"),stroke:ct(t,l)}),Te(x(e,i.getScaledPath("TASK_TYPE_BUSINESS_RULE_MAIN",{abspos:{x:8,y:8}})),{strokeWidth:1,stroke:ct(t,l)}),n},"bpmn:SubProcess":function(e,t,n){n={fill:pt(t,s),stroke:ct(t,l),...n};var i=_("bpmn:Activity")(e,t,n),r=Ee(t);return function(e){return e&&!!we(e).triggeredByEvent}(t)&&Te(i,{strokeDasharray:"0, 5.5",strokeWidth:2.5}),R(e,t,r?"center-top":"center-middle"),r?M(e,t):M(e,t,["SubProcessMarker"]),i},"bpmn:AdHocSubProcess":function(e,t){return _("bpmn:SubProcess")(e,t)},"bpmn:Transaction":function(e,t){var i=_("bpmn:SubProcess")(e,t,{strokeWidth:1.5}),r=n.style(["no-fill","no-events"],{stroke:ct(t,l),strokeWidth:1.5});return v(e,t.width,t.height,7,3,r),i},"bpmn:CallActivity":function(e,t){return _("bpmn:SubProcess")(e,t,{strokeWidth:5})},"bpmn:Participant":function(e,t){var n={fillOpacity:Wt,fill:pt(t,s),stroke:ct(t,l),strokeWidth:1.5},i=_("bpmn:Lane")(e,t,n);return Ee(t)?(b(e,[{x:30,y:0},{x:30,y:t.height}],{stroke:ct(t,l),strokeWidth:1.5}),P(e,we(t).name,t)):C(e,we(t).name,{box:t,align:"center-middle",style:{fill:ut(t,p,l)}}),!!we(t).participantMultiplicity&&_("ParticipantMultiplicityMarker")(e,t),i},"bpmn:Lane":function(e,t,n){var i=v(e,t.width,t.height,0,{fill:pt(t,s),fillOpacity:.35,stroke:ct(t,l),strokeWidth:1.5,...n}),r=we(t);return"bpmn:Lane"===r.$type&&P(e,r.name,t),i},"bpmn:InclusiveGateway":function(e,t){var n=_("bpmn:Gateway")(e,t);return g(e,t.width,t.height,.24*t.height,{strokeWidth:2.5,fill:pt(t,s),stroke:ct(t,l)}),n},"bpmn:ExclusiveGateway":function(e,t){var n=_("bpmn:Gateway")(e,t),r=i.getScaledPath("GATEWAY_EXCLUSIVE",{xScaleFactor:.4,yScaleFactor:.4,containerWidth:t.width,containerHeight:t.height,position:{mx:.32,my:.3}});return xe(t).isMarkerVisible&&x(e,r,{strokeWidth:1,fill:ct(t,l),stroke:ct(t,l)}),n},"bpmn:ComplexGateway":function(e,t){var n=_("bpmn:Gateway")(e,t);return x(e,i.getScaledPath("GATEWAY_COMPLEX",{xScaleFactor:.5,yScaleFactor:.5,containerWidth:t.width,containerHeight:t.height,position:{mx:.46,my:.26}}),{strokeWidth:1,fill:ct(t,l),stroke:ct(t,l)}),n},"bpmn:ParallelGateway":function(e,t){var n=_("bpmn:Gateway")(e,t);return x(e,i.getScaledPath("GATEWAY_PARALLEL",{xScaleFactor:.6,yScaleFactor:.6,containerWidth:t.width,containerHeight:t.height,position:{mx:.46,my:.2}}),{strokeWidth:1,fill:ct(t,l),stroke:ct(t,l)}),n},"bpmn:EventBasedGateway":function(e,t){var n=we(t),r=_("bpmn:Gateway")(e,t);g(e,t.width,t.height,.2*t.height,{strokeWidth:1,fill:"none",stroke:ct(t,l)});var o=n.eventGatewayType,a=!!n.instantiate;if("Parallel"===o){var s=i.getScaledPath("GATEWAY_PARALLEL",{xScaleFactor:.4,yScaleFactor:.4,containerWidth:t.width,containerHeight:t.height,position:{mx:.474,my:.296}});x(e,s,{strokeWidth:1,fill:"none"})}else"Exclusive"===o&&(a||g(e,t.width,t.height,.26*t.height,{strokeWidth:1,fill:"none",stroke:ct(t,l)}),function(){var n=i.getScaledPath("GATEWAY_EVENT_BASED",{xScaleFactor:.18,yScaleFactor:.18,containerWidth:t.width,containerHeight:t.height,position:{mx:.36,my:.44}});x(e,n,{strokeWidth:2,fill:pt(t,"none"),stroke:ct(t,l)})}());return r},"bpmn:Gateway":function(e,t){return function(e,t,n,i){var r=t/2,o=n/2,a=[{x:r,y:0},{x:t,y:o},{x:r,y:n},{x:0,y:o}].map((function(e){return e.x+","+e.y})).join(" ");i=m(i);var s=je("polygon",{...i,points:a});return Pe(e,s),s}(e,t.width,t.height,{fill:pt(t,s),fillOpacity:Wt,stroke:ct(t,l)})},"bpmn:SequenceFlow":function(e,t){var n,i=pt(t,s),r=ct(t,l),o=w(e,t.waypoints,{markerEnd:y("sequenceflow-end",i,r),stroke:ct(t,l)}),a=we(t);return t.source&&(n=t.source.businessObject,a.conditionExpression&&n.$instanceOf("bpmn:Activity")&&Te(o,{markerStart:y("conditional-flow-marker",i,r)}),n.default&&(n.$instanceOf("bpmn:Gateway")||n.$instanceOf("bpmn:Activity"))&&n.default===a&&Te(o,{markerStart:y("conditional-default-flow-marker",i,r)})),o},"bpmn:Association":function(e,t,n){var i=we(t),r=pt(t,s),o=ct(t,l);return n={strokeDasharray:"0, 5",stroke:ct(t,l),...n},"One"!==i.associationDirection&&"Both"!==i.associationDirection||(n.markerEnd=y("association-end",r,o)),"Both"===i.associationDirection&&(n.markerStart=y("association-start",r,o)),w(e,t.waypoints,n)},"bpmn:DataInputAssociation":function(e,t){var n=pt(t,s),i=ct(t,l);return _("bpmn:Association")(e,t,{markerEnd:y("association-end",n,i)})},"bpmn:DataOutputAssociation":function(e,t){var n=pt(t,s),i=ct(t,l);return _("bpmn:Association")(e,t,{markerEnd:y("association-end",n,i)})},"bpmn:MessageFlow":function(e,t){var n=we(t),r=xe(t),o=pt(t,s),a=ct(t,l),c=w(e,t.waypoints,{markerEnd:y("messageflow-end",o,a),markerStart:y("messageflow-start",o,a),strokeDasharray:"10, 11",strokeWidth:1.5,stroke:ct(t,l)});if(n.messageRef){var u=c.getPointAtLength(c.getTotalLength()/2),m=i.getScaledPath("MESSAGE_FLOW_MARKER",{abspos:{x:u.x,y:u.y}}),d={strokeWidth:1};"initiating"===r.messageVisibleKind?(d.fill="white",d.stroke=st):(d.fill="#888",d.stroke="white");var h=x(e,m,d),f=C(e,n.messageRef.name,{align:"center-top",fitBox:!0,style:{fill:ct(t,p)}}),g=h.getBBox(),v=f.getBBox();jt(f,u.x-v.width/2,u.y+g.height/2+10,0)}return c},"bpmn:DataObject":function(e,t){var n=x(e,i.getScaledPath("DATA_OBJECT_PATH",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.474,my:.296}}),{fill:pt(t,s),fillOpacity:Wt,stroke:ct(t,l)});return function(e){var t=e.dataObjectRef;return e.isCollection||t&&t.isCollection}(we(t))&&function(e,t){var n=(t.height-18)/t.height,r=i.getScaledPath("DATA_OBJECT_COLLECTION_PATH",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.33,my:n}});x(e,r,{strokeWidth:2})}(e,t),n},"bpmn:DataObjectReference":S("bpmn:DataObject"),"bpmn:DataInput":function(e,t){var n=i.getRawPath("DATA_ARROW"),r=_("bpmn:DataObject")(e,t);return x(e,n,{strokeWidth:1}),r},"bpmn:DataOutput":function(e,t){var n=i.getRawPath("DATA_ARROW"),r=_("bpmn:DataObject")(e,t);return x(e,n,{strokeWidth:1,fill:st}),r},"bpmn:DataStoreReference":function(e,t){return x(e,i.getScaledPath("DATA_STORE",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:0,my:.133}}),{strokeWidth:2,fill:pt(t,s),fillOpacity:Wt,stroke:ct(t,l)})},"bpmn:BoundaryEvent":function(e,t,n){var i=we(t).cancelActivity,r={strokeWidth:1.5,fill:pt(t,s),stroke:ct(t,l)};i||(r.strokeDasharray="6");var o={...r,fillOpacity:1},a={...r,fill:"none"},p=_("bpmn:Event")(e,t,o);return g(e,t.width,t.height,3,a),n&&!1===n.renderIcon||A(t,e),p},"bpmn:Group":function(e,t){return v(e,t.width,t.height,10,{stroke:ct(t,l),strokeWidth:1.5,strokeDasharray:"10,6,0,6",fill:"none",pointerEvents:"none"})},label:function(e,t){return function(e,t){var n={width:90,height:30,x:t.width/2+t.x,y:t.height/2+t.y};return C(e,Ce(t),{box:n,fitBox:!0,style:ge({},o.getExternalStyle(),{fill:ut(t,p,l)})})}(e,t)},"bpmn:TextAnnotation":function(e,t){var n=v(e,t.width,t.height,0,0,{fill:"none",stroke:"none"});return x(e,i.getScaledPath("TEXT_ANNOTATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:0,my:0}}),{stroke:ct(t,l)}),C(e,we(t).text||"",{box:t,align:"left-top",padding:7,style:{fill:ut(t,p,l)}}),n},ParticipantMultiplicityMarker:function(e,t){E("participant-multiplicity",e,i.getScaledPath("MARKER_PARALLEL",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:t.width/2/t.width,my:(t.height-15)/t.height}}),{strokeWidth:2,fill:pt(t,s),stroke:ct(t,l)})},SubProcessMarker:function(e,t){Vt(v(e,14,14,0,{strokeWidth:1,fill:pt(t,s),stroke:ct(t,l)}),t.width/2-7.5,t.height-20),E("sub-process",e,i.getScaledPath("MARKER_SUB_PROCESS",{xScaleFactor:1.5,yScaleFactor:1.5,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2-7.5)/t.width,my:(t.height-20)/t.height}}),{fill:pt(t,s),stroke:ct(t,l)})},ParallelMarker:function(e,t,n){E("parallel",e,i.getScaledPath("MARKER_PARALLEL",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2+n.parallel)/t.width,my:(t.height-20)/t.height}}),{fill:pt(t,s),stroke:ct(t,l)})},SequentialMarker:function(e,t,n){E("sequential",e,i.getScaledPath("MARKER_SEQUENTIAL",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2+n.seq)/t.width,my:(t.height-19)/t.height}}),{fill:pt(t,s),stroke:ct(t,l)})},CompensationMarker:function(e,t,n){E("compensation",e,i.getScaledPath("MARKER_COMPENSATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2+n.compensation)/t.width,my:(t.height-13)/t.height}}),{strokeWidth:1,fill:pt(t,s),stroke:ct(t,l)})},LoopMarker:function(e,t,n){E("loop",e,i.getScaledPath("MARKER_LOOP",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2+n.loop)/t.width,my:(t.height-7)/t.height}}),{strokeWidth:1.5,fill:pt(t,s),stroke:ct(t,l),strokeMiterlimit:.5})},AdhocMarker:function(e,t,n){E("adhoc",e,i.getScaledPath("MARKER_ADHOC",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2+n.adhoc)/t.width,my:(t.height-15)/t.height}}),{strokeWidth:1,fill:ct(t,l),stroke:ct(t,l)})}};function M(e,t,n){var i,r=we(t),o=n&&-1!==n.indexOf("SubProcessMarker");i=o?{seq:-21,parallel:-22,compensation:-42,loop:-18,adhoc:10}:{seq:-3,parallel:-6,compensation:-27,loop:0,adhoc:10},le(n,(function(n){_(n)(e,t,i)})),r.isForCompensation&&_("CompensationMarker")(e,t,i),"bpmn:AdHocSubProcess"===r.$type&&_("AdhocMarker")(e,t,i);var a=r.loopCharacteristics,s=a&&a.isSequential;a&&(void 0===s&&_("LoopMarker")(e,t,i),!1===s&&_("ParallelMarker")(e,t,i),!0===s&&_("SequentialMarker")(e,t,i))}this._drawPath=x,this._renderer=_}q(Kt,ve),Kt.$inject=["config.bpmnRenderer","eventBus","styles","pathMap","canvas","textRenderer"],Kt.prototype.canRender=function(e){return be(e,"bpmn:BaseElement")},Kt.prototype.drawShape=function(e,t){var n=t.type;return this._renderer(n)(e,t)},Kt.prototype.drawConnection=function(e,t){var n=t.type;return this._renderer(n)(e,t)},Kt.prototype.getShapePath=function(e){return be(e,"bpmn:Event")?(n=(t=e).x+t.width/2,i=t.y+t.height/2,r=t.width/2,et([["M",n,i],["m",0,-r],["a",r,r,0,1,1,0,2*r],["a",r,r,0,1,1,0,-2*r],["z"]])):be(e,"bpmn:Activity")?function(e,t){var n=e.x,i=e.y,r=e.width,o=e.height;return et([["M",n+t,i],["l",r-2*t,0],["a",t,t,0,0,1,t,t],["l",0,o-2*t],["a",t,t,0,0,1,-t,t],["l",2*t-r,0],["a",t,t,0,0,1,-t,-t],["l",0,2*t-o],["a",t,t,0,0,1,t,-t],["z"]])}(e,10):be(e,"bpmn:Gateway")?function(e){var t=e.width/2,n=e.height/2;return et([["M",e.x+t,e.y],["l",t,n],["l",-t,n],["l",-t,-n],["z"]])}(e):function(e){var t=e.x,n=e.y,i=e.width;return et([["M",t,n],["l",i,0],["l",0,e.height],["l",-i,0],["z"]])}(e);var t,n,i,r};var Gt={width:150,height:50};function Ht(e,t){var n;t.textContent=e;try{var i,r=""===e;return t.textContent=r?"dummy":e,i={width:(n=t.getBBox()).width+2*n.x,height:n.height},r&&(i.width=0),i}catch(q){return{width:0,height:0}}}function qt(e,t,n){for(var i,r=e.shift(),o=r;;){if((i=Ht(o,n)).width=o?i.width:0," "===o||""===o||i.width<Math.round(t)||o.length<2)return Yt(e,o,r,i);o=Jt(o,i.width,t)}}function Yt(e,t,n,i){if(t.length<n.length){var r=n.slice(t.length).trim();e.unshift(r)}return{width:i.width,height:i.height,text:t}}var Xt="­";function Jt(e,t,n){var i=Math.max(e.length*(n/t),1),r=function(e,t){var n,i=e.split(/(\s|-|\u00AD)/g),r=[],o=0;if(i.length>1)for(;n=i.shift();){if(!(n.length+o<t)){"-"!==n&&n!==Xt||r.pop();break}r.push(n),o+=n.length}var a=r[r.length-1];return a&&a===Xt&&(r[r.length-1]="-"),r.join("")}(e,i);return r||(r=e.slice(0,Math.max(Math.round(i-1),1))),r}function Qt(e){this._config=ge({},{size:Gt,padding:0,style:{},align:"center-top"},e||{})}function Zt(e){var t=ge({fontFamily:"Arial, sans-serif",fontSize:12,fontWeight:"normal",lineHeight:1.2},e&&e.defaultStyle||{}),n=parseInt(t.fontSize,10)-1,i=ge({},t,{fontSize:n},e&&e.externalStyle||{}),r=new Qt({style:t});this.getExternalLabelBounds=function(e,t){var n=r.getDimensions(t,{box:{width:90,height:30},style:i});return{x:Math.round(e.x+e.width/2-n.width/2),y:Math.round(e.y),width:Math.ceil(n.width),height:Math.ceil(n.height)}},this.getTextAnnotationBounds=function(e,n){var i=r.getDimensions(n,{box:e,style:t,align:"left-top",padding:5});return{x:e.x,y:e.y,width:e.width,height:Math.max(30,Math.round(i.height))}},this.createText=function(e,t){return r.createText(e,t||{})},this.getDefaultStyle=function(){return t},this.getExternalStyle=function(){return i}}Qt.prototype.createText=function(e,t){return this.layoutText(e,t).element},Qt.prototype.getDimensions=function(e,t){return this.layoutText(e,t).dimensions},Qt.prototype.layoutText=function(e,t){var n=ge({},this._config.size,t.box),i=ge({},this._config.style,t.style),r=function(e){var t=e.split("-");return{horizontal:t[0]||"center",vertical:t[1]||"top"}}(t.align||this._config.align),o=function(e){return te(e)?ge({top:0,left:0,right:0,bottom:0},e):{top:e,left:e,right:e,bottom:e}}(void 0!==t.padding?t.padding:this._config.padding),a=t.fitBox||!1,s=function(e){if("fontSize"in e&&"lineHeight"in e)return e.lineHeight*parseInt(e.fontSize,10)}(i),l=e.split(/\u00AD?\r?\n/),p=[],c=n.width-o.left-o.right,u=je("text");for(Te(u,{x:0,y:0}),Te(u,i),Pe(function(){var e=document.getElementById("helper-svg");return e||(Te(e=je("svg"),{id:"helper-svg"}),bt(e,{visibility:"hidden",position:"fixed",width:0,height:0}),document.body.appendChild(e)),e}(),u);l.length;)p.push(qt(l,c,u));"middle"===r.vertical&&(o.top=o.bottom=0);var m=pe(p,(function(e,t,n){return e+(s||t.height)}),0)+o.top+o.bottom,d=pe(p,(function(e,t,n){return t.width>e?t.width:e}),0),h=o.top;"middle"===r.vertical&&(h+=(n.height-m)/2),h-=(s||p[0].height)/4;var f=je("text");return Te(f,i),le(p,(function(e){var t;switch(h+=s||e.height,r.horizontal){case"left":t=o.left;break;case"right":t=(a?d:c)-o.right-e.width;break;default:t=Math.max(((a?d:c)-e.width)/2+o.left,0)}var n=je("tspan");Te(n,{x:t,y:h}),n.textContent=e.text,Pe(f,n)})),Je(u),{dimensions:{width:d,height:m},element:f}},Zt.$inject=["config.textRenderer"];var en=/\{([^{}]+)\}/g,tn=/(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g;const nn={__init__:["bpmnRenderer"],bpmnRenderer:["type",Kt],textRenderer:["type",Zt],pathMap:["type",function(){this.pathMap={EVENT_MESSAGE:{d:"m {mx},{my} l 0,{e.y1} l {e.x1},0 l 0,-{e.y1} z l {e.x0},{e.y0} l {e.x0},-{e.y0}",height:36,width:36,heightElements:[6,14],widthElements:[10.5,21]},EVENT_SIGNAL:{d:"M {mx},{my} l {e.x0},{e.y0} l -{e.x1},0 Z",height:36,width:36,heightElements:[18],widthElements:[10,20]},EVENT_ESCALATION:{d:"M {mx},{my} l {e.x0},{e.y0} l -{e.x0},-{e.y1} l -{e.x0},{e.y1} Z",height:36,width:36,heightElements:[20,7],widthElements:[8]},EVENT_CONDITIONAL:{d:"M {e.x0},{e.y0} l {e.x1},0 l 0,{e.y2} l -{e.x1},0 Z M {e.x2},{e.y3} l {e.x0},0 M {e.x2},{e.y4} l {e.x0},0 M {e.x2},{e.y5} l {e.x0},0 M {e.x2},{e.y6} l {e.x0},0 M {e.x2},{e.y7} l {e.x0},0 M {e.x2},{e.y8} l {e.x0},0 ",height:36,width:36,heightElements:[8.5,14.5,18,11.5,14.5,17.5,20.5,23.5,26.5],widthElements:[10.5,14.5,12.5]},EVENT_LINK:{d:"m {mx},{my} 0,{e.y0} -{e.x1},0 0,{e.y1} {e.x1},0 0,{e.y0} {e.x0},-{e.y2} -{e.x0},-{e.y2} z",height:36,width:36,heightElements:[4.4375,6.75,7.8125],widthElements:[9.84375,13.5]},EVENT_ERROR:{d:"m {mx},{my} {e.x0},-{e.y0} {e.x1},-{e.y1} {e.x2},{e.y2} {e.x3},-{e.y3} -{e.x4},{e.y4} -{e.x5},-{e.y5} z",height:36,width:36,heightElements:[.023,8.737,8.151,16.564,10.591,8.714],widthElements:[.085,6.672,6.97,4.273,5.337,6.636]},EVENT_CANCEL_45:{d:"m {mx},{my} -{e.x1},0 0,{e.x0} {e.x1},0 0,{e.y1} {e.x0},0 0,-{e.y1} {e.x1},0 0,-{e.y0} -{e.x1},0 0,-{e.y1} -{e.x0},0 z",height:36,width:36,heightElements:[4.75,8.5],widthElements:[4.75,8.5]},EVENT_COMPENSATION:{d:"m {mx},{my} {e.x0},-{e.y0} 0,{e.y1} z m {e.x1},-{e.y2} {e.x2},-{e.y3} 0,{e.y1} -{e.x2},-{e.y3} z",height:36,width:36,heightElements:[6.5,13,.4,6.1],widthElements:[9,9.3,8.7]},EVENT_TIMER_WH:{d:"M {mx},{my} l {e.x0},-{e.y0} m -{e.x0},{e.y0} l {e.x1},{e.y1} ",height:36,width:36,heightElements:[10,2],widthElements:[3,7]},EVENT_TIMER_LINE:{d:"M {mx},{my} m {e.x0},{e.y0} l -{e.x1},{e.y1} ",height:36,width:36,heightElements:[10,3],widthElements:[0,0]},EVENT_MULTIPLE:{d:"m {mx},{my} {e.x1},-{e.y0} {e.x1},{e.y0} -{e.x0},{e.y1} -{e.x2},0 z",height:36,width:36,heightElements:[6.28099,12.56199],widthElements:[3.1405,9.42149,12.56198]},EVENT_PARALLEL_MULTIPLE:{d:"m {mx},{my} {e.x0},0 0,{e.y1} {e.x1},0 0,{e.y0} -{e.x1},0 0,{e.y1} -{e.x0},0 0,-{e.y1} -{e.x1},0 0,-{e.y0} {e.x1},0 z",height:36,width:36,heightElements:[2.56228,7.68683],widthElements:[2.56228,7.68683]},GATEWAY_EXCLUSIVE:{d:"m {mx},{my} {e.x0},{e.y0} {e.x1},{e.y0} {e.x2},0 {e.x4},{e.y2} {e.x4},{e.y1} {e.x2},0 {e.x1},{e.y3} {e.x0},{e.y3} {e.x3},0 {e.x5},{e.y1} {e.x5},{e.y2} {e.x3},0 z",height:17.5,width:17.5,heightElements:[8.5,6.5312,-6.5312,-8.5],widthElements:[6.5,-6.5,3,-3,5,-5]},GATEWAY_PARALLEL:{d:"m {mx},{my} 0,{e.y1} -{e.x1},0 0,{e.y0} {e.x1},0 0,{e.y1} {e.x0},0 0,-{e.y1} {e.x1},0 0,-{e.y0} -{e.x1},0 0,-{e.y1} -{e.x0},0 z",height:30,width:30,heightElements:[5,12.5],widthElements:[5,12.5]},GATEWAY_EVENT_BASED:{d:"m {mx},{my} {e.x0},{e.y0} {e.x0},{e.y1} {e.x1},{e.y2} {e.x2},0 z",height:11,width:11,heightElements:[-6,6,12,-12],widthElements:[9,-3,-12]},GATEWAY_COMPLEX:{d:"m {mx},{my} 0,{e.y0} -{e.x0},-{e.y1} -{e.x1},{e.y2} {e.x0},{e.y1} -{e.x2},0 0,{e.y3} {e.x2},0  -{e.x0},{e.y1} l {e.x1},{e.y2} {e.x0},-{e.y1} 0,{e.y0} {e.x3},0 0,-{e.y0} {e.x0},{e.y1} {e.x1},-{e.y2} -{e.x0},-{e.y1} {e.x2},0 0,-{e.y3} -{e.x2},0 {e.x0},-{e.y1} -{e.x1},-{e.y2} -{e.x0},{e.y1} 0,-{e.y0} -{e.x3},0 z",height:17.125,width:17.125,heightElements:[4.875,3.4375,2.125,3],widthElements:[3.4375,2.125,4.875,3]},DATA_OBJECT_PATH:{d:"m 0,0 {e.x1},0 {e.x0},{e.y0} 0,{e.y1} -{e.x2},0 0,-{e.y2} {e.x1},0 0,{e.y0} {e.x0},0",height:61,width:51,heightElements:[10,50,60],widthElements:[10,40,50,60]},DATA_OBJECT_COLLECTION_PATH:{d:"m{mx},{my} m 3,2 l 0,10 m 3,-10 l 0,10 m 3,-10 l 0,10",height:10,width:10,heightElements:[],widthElements:[]},DATA_ARROW:{d:"m 5,9 9,0 0,-3 5,5 -5,5 0,-3 -9,0 z",height:61,width:51,heightElements:[],widthElements:[]},DATA_STORE:{d:"m  {mx},{my} l  0,{e.y2} c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0 l  0,-{e.y2} c -{e.x0},-{e.y1} -{e.x1},-{e.y1} -{e.x2},0c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0 m  -{e.x2},{e.y0}c  {e.x0},{e.y1} {e.x1},{e.y1} {e.x2},0m  -{e.x2},{e.y0}c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0",height:61,width:61,heightElements:[7,10,45],widthElements:[2,58,60]},TEXT_ANNOTATION:{d:"m {mx}, {my} m 10,0 l -10,0 l 0,{e.y0} l 10,0",height:30,width:10,heightElements:[30],widthElements:[10]},MARKER_SUB_PROCESS:{d:"m{mx},{my} m 7,2 l 0,10 m -5,-5 l 10,0",height:10,width:10,heightElements:[],widthElements:[]},MARKER_PARALLEL:{d:"m{mx},{my} m 3,2 l 0,10 m 3,-10 l 0,10 m 3,-10 l 0,10",height:10,width:10,heightElements:[],widthElements:[]},MARKER_SEQUENTIAL:{d:"m{mx},{my} m 0,3 l 10,0 m -10,3 l 10,0 m -10,3 l 10,0",height:10,width:10,heightElements:[],widthElements:[]},MARKER_COMPENSATION:{d:"m {mx},{my} 7,-5 0,10 z m 7.1,-0.3 6.9,-4.7 0,10 -6.9,-4.7 z",height:10,width:21,heightElements:[],widthElements:[]},MARKER_LOOP:{d:"m {mx},{my} c 3.526979,0 6.386161,-2.829858 6.386161,-6.320661 0,-3.490806 -2.859182,-6.320661 -6.386161,-6.320661 -3.526978,0 -6.38616,2.829855 -6.38616,6.320661 0,1.745402 0.714797,3.325567 1.870463,4.469381 0.577834,0.571908 1.265885,1.034728 2.029916,1.35457 l -0.718163,-3.909793 m 0.718163,3.909793 -3.885211,0.802902",height:13.9,width:13.7,heightElements:[],widthElements:[]},MARKER_ADHOC:{d:"m {mx},{my} m 0.84461,2.64411 c 1.05533,-1.23780996 2.64337,-2.07882 4.29653,-1.97997996 2.05163,0.0805 3.85579,1.15803 5.76082,1.79107 1.06385,0.34139996 2.24454,0.1438 3.18759,-0.43767 0.61743,-0.33642 1.2775,-0.64078 1.7542,-1.17511 0,0.56023 0,1.12046 0,1.6807 -0.98706,0.96237996 -2.29792,1.62393996 -3.6918,1.66181996 -1.24459,0.0927 -2.46671,-0.2491 -3.59505,-0.74812 -1.35789,-0.55965 -2.75133,-1.33436996 -4.27027,-1.18121996 -1.37741,0.14601 -2.41842,1.13685996 -3.44288,1.96782996 z",height:4,width:15,heightElements:[],widthElements:[]},TASK_TYPE_SEND:{d:"m {mx},{my} l 0,{e.y1} l {e.x1},0 l 0,-{e.y1} z l {e.x0},{e.y0} l {e.x0},-{e.y0}",height:14,width:21,heightElements:[6,14],widthElements:[10.5,21]},TASK_TYPE_SCRIPT:{d:"m {mx},{my} c 9.966553,-6.27276 -8.000926,-7.91932 2.968968,-14.938 l -8.802728,0 c -10.969894,7.01868 6.997585,8.66524 -2.968967,14.938 z m -7,-12 l 5,0 m -4.5,3 l 4.5,0 m -3,3 l 5,0m -4,3 l 5,0",height:15,width:12.6,heightElements:[6,14],widthElements:[10.5,21]},TASK_TYPE_USER_1:{d:"m {mx},{my} c 0.909,-0.845 1.594,-2.049 1.594,-3.385 0,-2.554 -1.805,-4.62199999 -4.357,-4.62199999 -2.55199998,0 -4.28799998,2.06799999 -4.28799998,4.62199999 0,1.348 0.974,2.562 1.89599998,3.405 -0.52899998,0.187 -5.669,2.097 -5.794,4.7560005 v 6.718 h 17 v -6.718 c 0,-2.2980005 -5.5279996,-4.5950005 -6.0509996,-4.7760005 zm -8,6 l 0,5.5 m 11,0 l 0,-5"},TASK_TYPE_USER_2:{d:"m {mx},{my} m 2.162,1.009 c 0,2.4470005 -2.158,4.4310005 -4.821,4.4310005 -2.66499998,0 -4.822,-1.981 -4.822,-4.4310005 "},TASK_TYPE_USER_3:{d:"m {mx},{my} m -6.9,-3.80 c 0,0 2.25099998,-2.358 4.27399998,-1.177 2.024,1.181 4.221,1.537 4.124,0.965 -0.098,-0.57 -0.117,-3.79099999 -4.191,-4.13599999 -3.57499998,0.001 -4.20799998,3.36699999 -4.20699998,4.34799999 z"},TASK_TYPE_MANUAL:{d:"m {mx},{my} c 0.234,-0.01 5.604,0.008 8.029,0.004 0.808,0 1.271,-0.172 1.417,-0.752 0.227,-0.898 -0.334,-1.314 -1.338,-1.316 -2.467,-0.01 -7.886,-0.004 -8.108,-0.004 -0.014,-0.079 0.016,-0.533 0,-0.61 0.195,-0.042 8.507,0.006 9.616,0.002 0.877,-0.007 1.35,-0.438 1.353,-1.208 0.003,-0.768 -0.479,-1.09 -1.35,-1.091 -2.968,-0.002 -9.619,-0.013 -9.619,-0.013 v -0.591 c 0,0 5.052,-0.016 7.225,-0.016 0.888,-0.002 1.354,-0.416 1.351,-1.193 -0.006,-0.761 -0.492,-1.196 -1.361,-1.196 -3.473,-0.005 -10.86,-0.003 -11.0829995,-0.003 -0.022,-0.047 -0.045,-0.094 -0.069,-0.139 0.3939995,-0.319 2.0409995,-1.626 2.4149995,-2.017 0.469,-0.4870005 0.519,-1.1650005 0.162,-1.6040005 -0.414,-0.511 -0.973,-0.5 -1.48,-0.236 -1.4609995,0.764 -6.5999995,3.6430005 -7.7329995,4.2710005 -0.9,0.499 -1.516,1.253 -1.882,2.19 -0.37000002,0.95 -0.17,2.01 -0.166,2.979 0.004,0.718 -0.27300002,1.345 -0.055,2.063 0.629,2.087 2.425,3.312 4.859,3.318 4.6179995,0.014 9.2379995,-0.139 13.8569995,-0.158 0.755,-0.004 1.171,-0.301 1.182,-1.033 0.012,-0.754 -0.423,-0.969 -1.183,-0.973 -1.778,-0.01 -5.824,-0.004 -6.04,-0.004 10e-4,-0.084 0.003,-0.586 10e-4,-0.67 z"},TASK_TYPE_INSTANTIATING_SEND:{d:"m {mx},{my} l 0,8.4 l 12.6,0 l 0,-8.4 z l 6.3,3.6 l 6.3,-3.6"},TASK_TYPE_SERVICE:{d:"m {mx},{my} v -1.71335 c 0.352326,-0.0705 0.703932,-0.17838 1.047628,-0.32133 0.344416,-0.14465 0.665822,-0.32133 0.966377,-0.52145 l 1.19431,1.18005 1.567487,-1.57688 -1.195028,-1.18014 c 0.403376,-0.61394 0.683079,-1.29908 0.825447,-2.01824 l 1.622133,-0.01 v -2.2196 l -1.636514,0.01 c -0.07333,-0.35153 -0.178319,-0.70024 -0.323564,-1.04372 -0.145244,-0.34406 -0.321407,-0.6644 -0.522735,-0.96217 l 1.131035,-1.13631 -1.583305,-1.56293 -1.129598,1.13589 c -0.614052,-0.40108 -1.302883,-0.68093 -2.022633,-0.82247 l 0.0093,-1.61852 h -2.241173 l 0.0042,1.63124 c -0.353763,0.0736 -0.705369,0.17977 -1.049785,0.32371 -0.344415,0.14437 -0.665102,0.32092 -0.9635006,0.52046 l -1.1698628,-1.15823 -1.5667691,1.5792 1.1684265,1.15669 c -0.4026573,0.61283 -0.68308,1.29797 -0.8247287,2.01713 l -1.6588041,0.003 v 2.22174 l 1.6724648,-0.006 c 0.073327,0.35077 0.1797598,0.70243 0.3242851,1.04472 0.1452428,0.34448 0.3214064,0.6644 0.5227339,0.96066 l -1.1993431,1.19723 1.5840256,1.56011 1.1964668,-1.19348 c 0.6140517,0.40346 1.3028827,0.68232 2.0233517,0.82331 l 7.19e-4,1.69892 h 2.226848 z m 0.221462,-3.9957 c -1.788948,0.7502 -3.8576,-0.0928 -4.6097055,-1.87438 -0.7521065,-1.78321 0.090598,-3.84627 1.8802645,-4.59604 1.78823,-0.74936 3.856881,0.0929 4.608987,1.87437 0.752106,1.78165 -0.0906,3.84612 -1.879546,4.59605 z"},TASK_TYPE_SERVICE_FILL:{d:"m {mx},{my} c -1.788948,0.7502 -3.8576,-0.0928 -4.6097055,-1.87438 -0.7521065,-1.78321 0.090598,-3.84627 1.8802645,-4.59604 1.78823,-0.74936 3.856881,0.0929 4.608987,1.87437 0.752106,1.78165 -0.0906,3.84612 -1.879546,4.59605 z"},TASK_TYPE_BUSINESS_RULE_HEADER:{d:"m {mx},{my} 0,4 20,0 0,-4 z"},TASK_TYPE_BUSINESS_RULE_MAIN:{d:"m {mx},{my} 0,12 20,0 0,-12 zm 0,8 l 20,0 m -13,-4 l 0,8"},MESSAGE_FLOW_MARKER:{d:"m {mx},{my} m -10.5 ,-7 l 0,14 l 21,0 l 0,-14 z l 10.5,6 l 10.5,-6"}},this.getRawPath=function(e){return this.pathMap[e].d},this.getScaledPath=function(e,t){var n,i,r=this.pathMap[e];t.abspos?(n=t.abspos.x,i=t.abspos.y):(n=t.containerWidth*t.position.mx,i=t.containerHeight*t.position.my);var o={};if(t.position){for(var a=t.containerHeight/r.height*t.yScaleFactor,s=t.containerWidth/r.width*t.xScaleFactor,l=0;l<r.heightElements.length;l++)o["y"+l]=r.heightElements[l]*a;for(var p=0;p<r.widthElements.length;p++)o["x"+p]=r.widthElements[p]*s}var c,u,m=(c=r.d,u={mx:n,my:i,e:o},String(c).replace(en,(function(e,t){return function(e,t,n){var i=n;return t.replace(tn,(function(e,t,n,r,o){t=t||r,i&&(t in i&&(i=i[t]),"function"==typeof i&&o&&(i=i()))})),i=(null==i||i==n?e:i)+""}(e,t,u)})));return m}}]},rn={translate:["value",function(e,t){return t=t||{},e.replace(/{([^}]+)}/g,(function(e,n){return t[n]||"{"+n+"}"}))}]};function on(e){return{top:e.y,right:e.x+(e.width||0),bottom:e.y+(e.height||0),left:e.x}}function an(e){return t={x:e.x+(e.width||0)/2,y:e.y+(e.height||0)/2},{x:Math.round(t.x),y:Math.round(t.y)};var t}function sn(e){for(var t=e.waypoints,n=t.reduce((function(e,n,i){var r,o,a=t[i-1];if(a){var s=e[e.length-1],l=s&&s.endLength||0,p=(r=a,o=n,Math.sqrt(Math.pow(r.x-o.x,2)+Math.pow(r.y-o.y,2)));e.push({start:a,end:n,startLength:l,endLength:l+p,length:p})}return e}),[]),i=n.reduce((function(e,t){return e+t.length}),0)/2,r=0,o=n[r];o.endLength<i;)o=n[++r];var a=(i-o.startLength)/o.length;return{x:o.start.x+(o.end.x-o.start.x)*a,y:o.start.y+(o.end.y-o.start.y)*a}}function ln(e){return te(t=e)&&oe(t,"waypoints")?sn(e):an(e);var t}function pn(e){return e?"<"+e.$type+(e.id?' id="'+e.id:"")+'" />':"<null>"}function cn(e,t,n){return ge({id:e.id,type:e.$type,businessObject:e,di:t},n)}function un(e,t,n){var i=e.waypoint;return!i||i.length<2?[ln(t),ln(n)]:i.map((function(e){return{x:e.x,y:e.y}}))}function mn(e,t,n,i){return new Error(e("element {element} referenced by {referenced}#{property} not yet drawn",{element:pn(n),referenced:pn(t),property:i}))}function dn(e,t,n,i,r,o){this._eventBus=e,this._canvas=t,this._elementFactory=n,this._elementRegistry=i,this._translate=r,this._textRenderer=o}dn.$inject=["eventBus","canvas","elementFactory","elementRegistry","translate","textRenderer"],dn.prototype.add=function(e,t,n){var i,r,o,a,s,l,p,c=this._translate;if(be(t,"bpmndi:BPMNPlane")){var u=be(e,"bpmn:SubProcess")?{id:e.id+"_plane"}:{};i=this._elementFactory.createRoot(cn(e,t,u)),this._canvas.addRootElement(i)}else if(be(t,"bpmndi:BPMNShape")){var m=!Ee(e,t),d=function(e){return be(e,"bpmn:Group")}(e);r=n&&(n.hidden||n.collapsed);var h=t.bounds;i=this._elementFactory.createShape(cn(e,t,{collapsed:m,hidden:r,x:Math.round(h.x),y:Math.round(h.y),width:Math.round(h.width),height:Math.round(h.height),isFrame:d})),be(e,"bpmn:BoundaryEvent")&&this._attachBoundary(e,i),be(e,"bpmn:Lane")&&(o=0),be(e,"bpmn:DataStoreReference")&&(a=n,s=ln(h),l=s.x,p=s.y,l>=a.x&&l<=a.x+a.width&&p>=a.y&&p<=a.y+a.height||(n=this._canvas.findRoot(n))),this._canvas.addShape(i,n,o)}else{if(!be(t,"bpmndi:BPMNEdge"))throw new Error(c("unknown di {di} for element {semantic}",{di:pn(t),semantic:pn(e)}));var f=this._getSource(e),y=this._getTarget(e);r=n&&(n.hidden||n.collapsed),i=this._elementFactory.createConnection(cn(e,t,{hidden:r,source:f,target:y,waypoints:un(t,f,y)})),be(e,"bpmn:DataAssociation")&&(n=this._canvas.findRoot(n)),this._canvas.addConnection(i,n,o)}return function(e){return be(e,"bpmn:Event")||be(e,"bpmn:Gateway")||be(e,"bpmn:DataStoreReference")||be(e,"bpmn:DataObjectReference")||be(e,"bpmn:DataInput")||be(e,"bpmn:DataOutput")||be(e,"bpmn:SequenceFlow")||be(e,"bpmn:MessageFlow")||be(e,"bpmn:Group")}(e)&&Ce(i)&&this.addLabel(e,t,i),this._eventBus.fire("bpmnElement.added",{element:i}),i},dn.prototype._attachBoundary=function(e,t){var n=this._translate,i=e.attachedToRef;if(!i)throw new Error(n("missing {semantic}#attachedToRef",{semantic:pn(e)}));var r=this._elementRegistry.get(i.id),o=r&&r.attachers;if(!r)throw mn(n,e,i,"attachedToRef");t.host=r,o||(r.attachers=o=[]),-1===o.indexOf(t)&&o.push(t)},dn.prototype.addLabel=function(e,t,n){var i,r,o;return i=Ae(t,n),(r=Ce(n))&&(i=this._textRenderer.getExternalLabelBounds(i,r)),o=this._elementFactory.createLabel(cn(e,t,{id:e.id+"_label",labelTarget:n,type:"label",hidden:n.hidden||!Ce(n),x:Math.round(i.x),y:Math.round(i.y),width:Math.round(i.width),height:Math.round(i.height)})),this._canvas.addShape(o,n.parent)},dn.prototype._getConnectedElement=function(e,t){var n,i,r=e.$type,o=this._translate;if(i=e[t+"Ref"],"source"===t&&"bpmn:DataInputAssociation"===r&&(i=i&&i[0]),("source"===t&&"bpmn:DataOutputAssociation"===r||"target"===t&&"bpmn:DataInputAssociation"===r)&&(i=e.$parent),n=i&&this._getElement(i))return n;throw i?mn(o,e,i,t+"Ref"):new Error(o("{semantic}#{side} Ref not specified",{semantic:pn(e),side:t}))},dn.prototype._getSource=function(e){return this._getConnectedElement(e,"source")},dn.prototype._getTarget=function(e){return this._getConnectedElement(e,"target")},dn.prototype._getElement=function(e){return this._elementRegistry.get(e.id)};const hn={__depends__:[nn,{__depends__:[rn],bpmnImporter:["type",dn]}]};function fn(e){return e.originalEvent||e.srcEvent}function yn(e,t){return(fn(e)||e).button===t}function gn(e){return yn(e,0)}function vn(e){var t=fn(e)||e;return!!gn(e)&&(/mac/i.test(navigator.platform)?t.metaKey:t.ctrlKey)}function bn(e){return!0}function wn(e){return gn(e)||function(e){return yn(e,1)}(e)}function xn(e,t,n){var i=this;function r(n,i,r){var o,a;(function(e,t){var n=l[e]||gn;return!n(t)})(n,i)||(r?a=t.getGraphics(r):(o=i.delegateTarget||i.target)&&(a=o,r=t.get(a)),a&&r&&!1===e.fire(n,{element:r,gfx:a,originalEvent:i})&&(i.stopPropagation(),i.preventDefault()))}var o={};function a(e){return o[e]}var s={click:"element.click",contextmenu:"element.contextmenu",dblclick:"element.dblclick",mousedown:"element.mousedown",mousemove:"element.mousemove",mouseover:"element.hover",mouseout:"element.out",mouseup:"element.mouseup"},l={"element.contextmenu":bn,"element.mousedown":wn,"element.mouseup":wn,"element.click":wn,"element.dblclick":wn};function p(e,t,n,i){var a=o[n]=function(e){r(n,e)};i&&(l[n]=i),a.$delegate=Nt.bind(e,"svg, .djs-element",t,a)}function c(e,t,n){var i=a(n);i&&Nt.unbind(e,t,i.$delegate)}e.on("canvas.destroy",(function(e){var t;t=e.svg,le(s,(function(e,n){c(t,n,e)}))})),e.on("canvas.init",(function(e){var t;t=e.svg,le(s,(function(e,n){p(t,n,e)}))})),e.on(["shape.added","connection.added"],(function(t){var n=t.element,i=t.gfx;e.fire("interactionEvents.createHit",{element:n,gfx:i})})),e.on(["shape.changed","connection.changed"],500,(function(t){var n=t.element,i=t.gfx;e.fire("interactionEvents.updateHit",{element:n,gfx:i})})),e.on("interactionEvents.createHit",500,(function(e){var t=e.element,n=e.gfx;i.createDefaultHit(t,n)})),e.on("interactionEvents.updateHit",(function(e){var t=e.element,n=e.gfx;i.updateDefaultHit(t,n)}));var u=h("djs-hit djs-hit-stroke"),m=h("djs-hit djs-hit-click-stroke"),d={all:h("djs-hit djs-hit-all"),"click-stroke":m,stroke:u,"no-move":h("djs-hit djs-hit-no-move")};function h(e,t){return t=ge({stroke:"white",strokeWidth:15},t||{}),n.cls(e,["no-fill","no-border"],t)}function f(e,t){var n=d[t];if(!n)throw new Error("invalid hit type <"+t+">");return Te(e,n),e}function y(e,t){Pe(e,t)}this.removeHits=function(e){var t;le((t=".djs-hit",(e||document).querySelectorAll(t)),Je)},this.createDefaultHit=function(e,t){var n,i=e.waypoints,r=e.isFrame;return i?this.createWaypointsHit(t,i):(n=r?"stroke":"all",this.createBoxHit(t,n,{width:e.width,height:e.height}))},this.createWaypointsHit=function(e,t){var n=ot(t);return f(n,"stroke"),y(e,n),n},this.createBoxHit=function(e,t,n){n=ge({x:0,y:0},n);var i=je("rect");return f(i,t),Te(i,n),y(e,i),i},this.updateDefaultHit=function(e,t){var n=Bt(".djs-hit",t);if(n)return e.waypoints?at(n,e.waypoints):Te(n,{width:e.width,height:e.height}),n},this.fire=r,this.triggerMouseEvent=function(e,t,n){var i=s[e];if(!i)throw new Error("unmapped DOM event name <"+e+">");return r(i,t,n)},this.mouseHandler=a,this.registerEvent=p,this.unregisterEvent=c}xn.$inject=["eventBus","elementRegistry","styles"];const En={__init__:["interactionEvents"],interactionEvents:["type",xn]};function _n(e,t){var n,i,r,o;return t=!!t,ee(e)||(e=[e]),le(e,(function(e){var a=e;e.waypoints&&!t&&(a=_n(e.waypoints,!0));var s=a.x,l=a.y,p=a.height||0,c=a.width||0;(s<n||void 0===n)&&(n=s),(l<i||void 0===i)&&(i=l),(s+c>r||void 0===r)&&(r=s+c),(l+p>o||void 0===o)&&(o=l+p)})),{x:n,y:i,height:o-i,width:r-n}}function Sn(e){return"waypoints"in e?"connection":"x"in e?"shape":"root"}function An(e){return!(!e||!e.isFrame)}function Cn(e,t){this._eventBus=e,this.offset=5;var n=t.cls("djs-outline",["no-fill"]),i=this;function r(e){var t=je("rect");return Te(t,ge({x:0,y:0,rx:4,width:100,height:100},n)),t}e.on(["shape.added","shape.changed"],500,(function(e){var t=e.element,n=e.gfx,o=Bt(".djs-outline",n);o||Pe(n,o=i.getOutline(t)||r()),i.updateShapeOutline(o,t)})),e.on(["connection.added","connection.changed"],(function(e){var t=e.element,n=e.gfx,o=Bt(".djs-outline",n);o||Pe(n,o=r()),i.updateConnectionOutline(o,t)}))}Cn.prototype.updateShapeOutline=function(e,t){var n=!1,i=this._getProviders();i.length&&le(i,(function(i){n=n||i.updateOutline(t,e)})),n||Te(e,{x:-this.offset,y:-this.offset,width:t.width+2*this.offset,height:t.height+2*this.offset})},Cn.prototype.updateConnectionOutline=function(e,t){var n=_n(t);Te(e,{x:n.x-this.offset,y:n.y-this.offset,width:n.width+2*this.offset,height:n.height+2*this.offset})},Cn.prototype.registerProvider=function(e,t){t||(t=e,e=1e3),this._eventBus.on("outline.getProviders",e,(function(e){e.providers.push(t)}))},Cn.prototype._getProviders=function(){var e=this._eventBus.createEvent({type:"outline.getProviders",providers:[]});return this._eventBus.fire(e),e.providers},Cn.prototype.getOutline=function(e){var t;return le(this._getProviders(),(function(n){ie(n.getOutline)&&(t=t||n.getOutline(e))})),t},Cn.$inject=["eventBus","styles","elementRegistry"];const Rn={__init__:["outline"],outline:["type",Cn]};function Pn(e,t){this._eventBus=e,this._canvas=t,this._selectedElements=[];var n=this;e.on(["shape.remove","connection.remove"],(function(e){var t=e.element;n.deselect(t)})),e.on(["diagram.clear","root.set"],(function(e){n.select(null)}))}Pn.$inject=["eventBus","canvas"],Pn.prototype.deselect=function(e){var t=this._selectedElements,n=t.indexOf(e);if(-1!==n){var i=t.slice();t.splice(n,1),this._eventBus.fire("selection.changed",{oldSelection:i,newSelection:t})}},Pn.prototype.get=function(){return this._selectedElements},Pn.prototype.isSelected=function(e){return-1!==this._selectedElements.indexOf(e)},Pn.prototype.select=function(e,t){var n=this._selectedElements,i=n.slice();ee(e)||(e=e?[e]:[]);var r=this._canvas,o=r.getRootElement();e=e.filter((function(e){var t=r.findRoot(e);return o===t})),t?le(e,(function(e){-1===n.indexOf(e)&&n.push(e)})):this._selectedElements=n=e.slice(),this._eventBus.fire("selection.changed",{oldSelection:i,newSelection:n})};var kn="hover",Mn="selected";function Tn(e,t,n){this._canvas=e;var i=this;function r(t,n){e.addMarker(t,n)}function o(t,n){e.removeMarker(t,n)}this._multiSelectionBox=null,t.on("element.hover",(function(e){r(e.element,kn)})),t.on("element.out",(function(e){o(e.element,kn)})),t.on("selection.changed",(function(e){var t=e.oldSelection,n=e.newSelection;le(t,(function(e){-1===n.indexOf(e)&&o(e,Mn)})),le(n,(function(e){-1===t.indexOf(e)&&r(e,Mn)})),i._updateSelectionOutline(n)})),t.on("element.changed",(function(e){n.isSelected(e.element)&&i._updateSelectionOutline(n.get())}))}function Nn(e,t,n,i){e.on("create.end",500,(function(e){var n=e.context,i=n.canExecute,r=n.elements,o=(n.hints||{}).autoSelect;if(i){if(!1===o)return;ee(o)?t.select(o):t.select(r.filter(Dn))}})),e.on("connect.end",500,(function(e){var n=e.context.connection;n&&t.select(n)})),e.on("shape.move.end",500,(function(e){var n=e.previousSelection||[],r=i.get(e.context.shape.id);ae(n,(function(e){return r.id===e.id}))||t.select(r)})),e.on("element.click",(function(e){if(gn(e)){var i=e.element;i===n.getRootElement()&&(i=null);var r=t.isSelected(i),o=t.get().length>1,a=vn(e)||function(e){var t=fn(e)||e;return gn(e)&&t.shiftKey}(e);if(r&&o)return a?t.deselect(i):t.select(i);r?t.deselect(i):t.select(i,a)}}))}function Dn(e){return!e.hidden}Tn.$inject=["canvas","eventBus","selection"],Tn.prototype._updateSelectionOutline=function(e){var t=this._canvas.getLayer("selectionOutline");Ie(t);var n=e.length>1;if(De(this._canvas.getContainer())[n?"add":"remove"]("djs-multi-select"),n){var i=function(e){return{x:e.x-6,y:e.y-6,width:e.width+12,height:e.height+12}}(_n(e)),r=je("rect");Te(r,ge({rx:3},i)),De(r).add("djs-selection-outline"),Pe(t,r)}},Nn.$inject=["eventBus","selection","canvas","elementRegistry"];const On={__init__:["selectionVisuals","selectionBehavior"],__depends__:[En,Rn],selection:["type",Pn],selectionVisuals:["type",Tn],selectionBehavior:["type",Nn]};function In(e){this._counter=0,this._prefix=(e?e+"-":"")+Math.floor(1e9*Math.random())+"-"}In.prototype.next=function(){return this._prefix+ ++this._counter};var Ln=new In("ov");function Bn(e,t,n,i){var r,o;this._eventBus=t,this._canvas=n,this._elementRegistry=i,this._ids=Ln,this._overlayDefaults=ge({show:null,scale:!0},e&&e.defaults),this._overlays={},this._overlayContainers=[],this._overlayRoot=(r=n.getContainer(),bt(o=Lt('<div class="djs-overlay-container" />'),{position:"absolute",width:0,height:0}),r.insertBefore(o,r.firstChild),o),this._init()}function Fn(e,t,n){bt(e,{left:t+"px",top:n+"px"})}function jn(e,t){e.style.display=!1===t?"none":""}function Vn(e,t){e.style["transform-origin"]="top left",["","-ms-","-webkit-"].forEach((function(n){e.style[n+"transform"]=t}))}Bn.$inject=["config.overlays","eventBus","canvas","elementRegistry"],Bn.prototype.get=function(e){if(re(e)&&(e={id:e}),re(e.element)&&(e.element=this._elementRegistry.get(e.element)),e.element){var t=this._getOverlayContainer(e.element,!0);return t?e.type?se(t.overlays,me({type:e.type})):t.overlays.slice():[]}return e.type?se(this._overlays,me({type:e.type})):e.id?this._overlays[e.id]:null},Bn.prototype.add=function(e,t,n){if(te(t)&&(n=t,t=null),e.id||(e=this._elementRegistry.get(e)),!n.position)throw new Error("must specifiy overlay position");if(!n.html)throw new Error("must specifiy overlay html");if(!e)throw new Error("invalid element specified");var i=this._ids.next();return n=ge({},this._overlayDefaults,n,{id:i,type:t,element:e,html:n.html}),this._addOverlay(n),i},Bn.prototype.remove=function(e){var t=this.get(e)||[];ee(t)||(t=[t]);var n=this;le(t,(function(e){var t=n._getOverlayContainer(e.element,!0);if(e&&(Ft(e.html),Ft(e.htmlContainer),delete e.htmlContainer,delete e.element,delete n._overlays[e.id]),t){var i=t.overlays.indexOf(e);-1!==i&&t.overlays.splice(i,1)}}))},Bn.prototype.isShown=function(){return"none"!==this._overlayRoot.style.display},Bn.prototype.show=function(){jn(this._overlayRoot)},Bn.prototype.hide=function(){jn(this._overlayRoot,!1)},Bn.prototype.clear=function(){this._overlays={},this._overlayContainers=[],_t(this._overlayRoot)},Bn.prototype._updateOverlayContainer=function(e){var t=e.element,n=e.html,i=t.x,r=t.y;if(t.waypoints){var o=_n(t);i=o.x,r=o.y}Fn(n,i,r),function(e,t,n){2==arguments.length?e.getAttribute(t):null===n?e.removeAttribute(t):e.setAttribute(t,n)}(e.html,"data-container-id",t.id)},Bn.prototype._updateOverlay=function(e){var t,n,i=e.position,r=e.htmlContainer,o=e.element,a=i.left,s=i.top;void 0!==i.right&&(t=o.waypoints?_n(o).width:o.width,a=-1*i.right+t),void 0!==i.bottom&&(n=o.waypoints?_n(o).height:o.height,s=-1*i.bottom+n),Fn(r,a||0,s||0),this._updateOverlayVisibilty(e,this._canvas.viewbox())},Bn.prototype._createOverlayContainer=function(e){var t=Lt('<div class="djs-overlays" />');bt(t,{position:"absolute"}),this._overlayRoot.appendChild(t);var n={html:t,element:e,overlays:[]};return this._updateOverlayContainer(n),this._overlayContainers.push(n),n},Bn.prototype._updateRoot=function(e){var t=e.scale||1,n="matrix("+[t,0,0,t,-1*e.x*t,-1*e.y*t].join(",")+")";Vn(this._overlayRoot,n)},Bn.prototype._getOverlayContainer=function(e,t){var n=ae(this._overlayContainers,(function(t){return t.element===e}));return n||t?n:this._createOverlayContainer(e)},Bn.prototype._addOverlay=function(e){var t,n,i=e.id,r=e.element,o=e.html;o.get&&o.constructor.prototype.jquery&&(o=o.get(0)),re(o)&&(o=Lt(o)),n=this._getOverlayContainer(r),bt(t=Lt('<div class="djs-overlay" data-overlay-id="'+i+'">'),{position:"absolute"}),t.appendChild(o),e.type&&xt(t).add("djs-overlay-"+e.type),jn(t,this._canvas.findRoot(r)===this._canvas.getRootElement()),e.htmlContainer=t,n.overlays.push(e),n.html.appendChild(t),this._overlays[i]=e,this._updateOverlay(e),this._updateOverlayVisibilty(e,this._canvas.viewbox())},Bn.prototype._updateOverlayVisibilty=function(e,t){var n=e.show,i=this._canvas.findRoot(e.element),r=n&&n.minZoom,o=n&&n.maxZoom,a=e.htmlContainer,s=!0;(i!==this._canvas.getRootElement()||n&&(Q(r)&&r>t.scale||Q(o)&&o<t.scale))&&(s=!1),jn(a,s),this._updateOverlayScale(e,t)},Bn.prototype._updateOverlayScale=function(e,t){var n,i,r,o=e.scale,a=e.htmlContainer,s="";!0!==o&&(!1===o?(n=1,i=1):(n=o.min,i=o.max),Q(n)&&t.scale<n&&(r=(1/t.scale||1)*n),Q(i)&&t.scale>i&&(r=(1/t.scale||1)*i)),Q(r)&&(s="scale("+r+","+r+")"),Vn(a,s)},Bn.prototype._updateOverlaysVisibilty=function(e){var t=this;le(this._overlays,(function(n){t._updateOverlayVisibilty(n,e)}))},Bn.prototype._init=function(){var e=this._eventBus,t=this;e.on("canvas.viewbox.changing",(function(e){t.hide()})),e.on("canvas.viewbox.changed",(function(e){var n;n=e.viewbox,t._updateRoot(n),t._updateOverlaysVisibilty(n),t.show()})),e.on(["shape.remove","connection.remove"],(function(e){var n=e.element;le(t.get({element:n}),(function(e){t.remove(e.id)}));var i=t._getOverlayContainer(n);if(i){Ft(i.html);var r=t._overlayContainers.indexOf(i);-1!==r&&t._overlayContainers.splice(r,1)}})),e.on("element.changed",500,(function(e){var n=e.element,i=t._getOverlayContainer(n,!0);i&&(le(i.overlays,(function(e){t._updateOverlay(e)})),t._updateOverlayContainer(i))})),e.on("element.marker.update",(function(e){var n=t._getOverlayContainer(e.element,!0);n&&xt(n.html)[e.add?"add":"remove"](e.marker)})),e.on("root.set",(function(){t._updateOverlaysVisibilty(t._canvas.viewbox())})),e.on("diagram.clear",this.clear,this)};const $n={__init__:["overlays"],overlays:["type",Bn]};function zn(e,t,n,i){e.on("element.changed",(function(i){var r=i.element;(r.parent||r===t.getRootElement())&&(i.gfx=n.getGraphics(r)),i.gfx&&e.fire(Sn(r)+".changed",i)})),e.on("elements.changed",(function(t){var n=t.elements;n.forEach((function(t){e.fire("element.changed",{element:t})})),i.updateContainments(n)})),e.on("shape.changed",(function(e){i.update("shape",e.element,e.gfx)})),e.on("connection.changed",(function(e){i.update("connection",e.element,e.gfx)}))}zn.$inject=["eventBus","canvas","elementRegistry","graphicsFactory"];const Un={__init__:["changeSupport"],changeSupport:["type",zn]};function Wn(e){this._eventBus=e}function Kn(e){return function(t,n,i,r,o){(ie(t)||ne(t))&&(o=r,r=i,i=n,n=t,t=null),this.on(t,e,n,i,r,o)}}function Gn(e,t){t.invoke(Wn,this),this.executed((function(t){var n=t.context;n.rootElement?e.setRootElement(n.rootElement):n.rootElement=e.getRootElement()})),this.revert((function(t){var n=t.context;n.rootElement&&e.setRootElement(n.rootElement)}))}Wn.$inject=["eventBus"],Wn.prototype.on=function(e,t,n,i,r,o){if((ie(t)||ne(t))&&(o=r,r=i,i=n,n=t,t=null),ie(n)&&(o=r,r=i,i=n,n=1e3),te(r)&&(o=r,r=!1),!ie(i))throw new Error("handlerFn must be a function");ee(e)||(e=[e]);var a=this._eventBus;le(e,(function(e){var s=["commandStack",e,t].filter((function(e){return e})).join(".");a.on(s,n,r?function(e,t){return function(n){return e.call(t||null,n.context,n.command,n)}}(i,o):i,o)}))},Wn.prototype.canExecute=Kn("canExecute"),Wn.prototype.preExecute=Kn("preExecute"),Wn.prototype.preExecuted=Kn("preExecuted"),Wn.prototype.execute=Kn("execute"),Wn.prototype.executed=Kn("executed"),Wn.prototype.postExecute=Kn("postExecute"),Wn.prototype.postExecuted=Kn("postExecuted"),Wn.prototype.revert=Kn("revert"),Wn.prototype.reverted=Kn("reverted"),q(Gn,Wn),Gn.$inject=["canvas","injector"];const Hn={__init__:["rootElementsBehavior"],rootElementsBehavior:["type",Gn]};var qn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Yn="_plane";function Xn(e){var t=e.id;return be(e,"bpmn:SubProcess")?function(e){return e+Yn}(t):t}function Jn(e,t,n){var i=Lt('<ul class="bjs-breadcrumbs"></ul>'),r=n.getContainer(),o=xt(r);r.appendChild(i);var a=[];function s(e){e&&(a=function(e){for(var t=we(e),n=[],i=t;i;i=i.$parent)(be(i,"bpmn:SubProcess")||be(i,"bpmn:Process"))&&n.push(i);return n.reverse()}(e));var r=a.map((function(e){var i,r=(i=""+(i=e.name||e.id))&&i.replace(/[&<>"']/g,(function(e){return qn[e]})),o=Lt('<li><span class="bjs-crumb"><a title="'+r+'">'+r+"</a></span></li>"),a=n.findRoot(Xn(e))||n.findRoot(e.id);if(!a&&be(e,"bpmn:Process")){var s=t.find((function(t){var n=we(t);return n&&n.get("processRef")&&n.get("processRef")===e}));a=n.findRoot(s.id)}return o.addEventListener("click",(function(){n.setRootElement(a)})),o}));i.innerHTML="";var s=r.length>1;o.toggle("bjs-breadcrumbs-shown",s),r.forEach((function(e){i.appendChild(e)}))}e.on("element.changed",(function(e){var t=we(e.element);ae(a,(function(e){return e===t}))&&s()})),e.on("root.set",(function(e){s(e.element)}))}function Qn(e,t){var n=null,i=new Zn;e.on("root.set",(function(e){var r=e.element,o=t.viewbox(),a=i.get(r);if(i.set(n,{x:o.x,y:o.y,zoom:o.scale}),n=r,!be(r,"bpmn:Collaboration")||a){a=a||{x:0,y:0,zoom:1};var s=(o.x-a.x)*o.scale,l=(o.y-a.y)*o.scale;0===s&&0===l||t.scroll({dx:s,dy:l}),a.zoom!==o.scale&&t.zoom(a.zoom,{x:0,y:0})}})),e.on("diagram.clear",(function(){i.clear(),n=null}))}function Zn(){this._entries=[],this.set=function(e,t){var n=!1;for(var i in this._entries)if(this._entries[i][0]===e){this._entries[i][1]=t,n=!0;break}n||this._entries.push([e,t])},this.get=function(e){for(var t in this._entries)if(this._entries[t][0]===e)return this._entries[t][1];return null},this.clear=function(){this._entries.length=0},this.remove=function(e){var t=-1;for(var n in this._entries)if(this._entries[n][0]===e){t=n;break}-1!==t&&this._entries.splice(t,1)}}Jn.$inject=["eventBus","elementRegistry","canvas"],Qn.$inject=["eventBus","canvas"];var ei=180,ti=160;function ni(e,t){this._eventBus=e,this._moddle=t;var n=this;e.on("import.render.start",1500,(function(e,t){n._handleImport(t.definitions)}))}function ii(e){return be(e,"bpmndi:BPMNDiagram")?e:ii(e.$parent)}ni.prototype._handleImport=function(e){if(e.diagrams){var t=this;this._definitions=e,this._processToDiagramMap={},e.diagrams.forEach((function(e){e.plane&&e.plane.bpmnElement&&(t._processToDiagramMap[e.plane.bpmnElement.id]=e)}));var n=[];e.diagrams.forEach((function(e){var i=t._createNewDiagrams(e.plane);Array.prototype.push.apply(n,i)})),n.forEach((function(e){t._movePlaneElementsToOrigin(e.plane)}))}},ni.prototype._createNewDiagrams=function(e){var t=this,n=[],i=[];e.get("planeElement").forEach((function(t){var r=t.bpmnElement;if(r){var o=r.$parent;be(r,"bpmn:SubProcess")&&!t.isExpanded&&n.push(r),function(e,t){var n=e.$parent;return!(!be(n,"bpmn:SubProcess")||n===t.bpmnElement)&&!function(e,t){return ue(t,(function(t){return be(e,t)}))}(e,["bpmn:DataInputAssociation","bpmn:DataOutputAssociation"])}(r,e)&&i.push({diElement:t,parent:o})}}));var r=[];return n.forEach((function(e){if(!t._processToDiagramMap[e.id]){var n=t._createDiagram(e);t._processToDiagramMap[e.id]=n,r.push(n)}})),i.forEach((function(e){for(var i=e.diElement,r=e.parent;r&&-1===n.indexOf(r);)r=r.$parent;if(r){var o=t._processToDiagramMap[r.id];t._moveToDiPlane(i,o.plane)}})),r},ni.prototype._movePlaneElementsToOrigin=function(e){var t=e.get("planeElement"),n=function(e){var t,n={top:1/0,right:-1/0,bottom:-1/0,left:1/0};return e.planeElement.forEach((function(e){if(e.bounds){var t=on(e.bounds);n.top=Math.min(t.top,n.top),n.left=Math.min(t.left,n.left)}})),{x:(t=n).left,y:t.top,width:t.right-t.left,height:t.bottom-t.top}}(e),i=n.x-ei,r=n.y-ti;t.forEach((function(e){e.waypoint?e.waypoint.forEach((function(e){e.x=e.x-i,e.y=e.y-r})):e.bounds&&(e.bounds.x=e.bounds.x-i,e.bounds.y=e.bounds.y-r)}))},ni.prototype._moveToDiPlane=function(e,t){var n=ii(e).plane.get("planeElement");n.splice(n.indexOf(e),1),t.get("planeElement").push(e)},ni.prototype._createDiagram=function(e){var t=this._moddle.create("bpmndi:BPMNPlane",{bpmnElement:e}),n=this._moddle.create("bpmndi:BPMNDiagram",{plane:t});return t.$parent=n,t.bpmnElement=e,n.$parent=this._definitions,this._definitions.diagrams.push(n),n},ni.$inject=["eventBus","moddle"];var ri=250;function oi(e,t,n,i){Wn.call(this,t),this._canvas=e,this._eventBus=t,this._elementRegistry=n,this._overlays=i;var r=this;this.executed("shape.toggleCollapse",ri,(function(e){var t=e.shape;r._canDrillDown(t)?r._addOverlay(t):r._removeOverlay(t)}),!0),this.reverted("shape.toggleCollapse",ri,(function(e){var t=e.shape;r._canDrillDown(t)?r._addOverlay(t):r._removeOverlay(t)}),!0),this.executed(["shape.create","shape.move","shape.delete"],ri,(function(e){var t=e.oldParent,n=e.newParent||e.parent,i=e.shape;r._canDrillDown(i)&&r._addOverlay(i),r._updateDrilldownOverlay(t),r._updateDrilldownOverlay(n),r._updateDrilldownOverlay(i)}),!0),this.reverted(["shape.create","shape.move","shape.delete"],ri,(function(e){var t=e.oldParent,n=e.newParent||e.parent,i=e.shape;r._canDrillDown(i)&&r._addOverlay(i),r._updateDrilldownOverlay(t),r._updateDrilldownOverlay(n),r._updateDrilldownOverlay(i)}),!0),t.on("import.render.complete",(function(){n.filter((function(e){return r._canDrillDown(e)})).map((function(e){r._addOverlay(e)}))}))}q(oi,Wn),oi.prototype._updateDrilldownOverlay=function(e){var t=this._canvas;if(e){var n=t.findRoot(e);n&&this._updateOverlayVisibility(n)}},oi.prototype._canDrillDown=function(e){var t=this._canvas;return be(e,"bpmn:SubProcess")&&t.findRoot(Xn(e))},oi.prototype._updateOverlayVisibility=function(e){var t=this._overlays,n=we(e),i=t.get({element:n.id,type:"drilldown"})[0];if(i){var r=n&&n.get("flowElements")&&n.get("flowElements").length;xt(i.html).toggle("bjs-drilldown-empty",!r)}},oi.prototype._addOverlay=function(e){var t=this._canvas,n=this._overlays;n.get({element:e,type:"drilldown"}).length&&this._removeOverlay(e);var i=Lt('<button class="bjs-drilldown"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M4.81801948,3.50735931 L10.4996894,9.1896894 L10.5,4 L12,4 L12,12 L4,12 L4,10.5 L9.6896894,10.4996894 L3.75735931,4.56801948 C3.46446609,4.27512627 3.46446609,3.80025253 3.75735931,3.50735931 C4.05025253,3.21446609 4.52512627,3.21446609 4.81801948,3.50735931 Z"/></svg></button>');i.addEventListener("click",(function(){t.setRootElement(t.findRoot(Xn(e)))})),n.add(e,"drilldown",{position:{bottom:-7,right:-8},html:i}),this._updateOverlayVisibility(e)},oi.prototype._removeOverlay=function(e){this._overlays.remove({element:e,type:"drilldown"})},oi.$inject=["canvas","eventBus","elementRegistry","overlays"];const ai={__depends__:[$n,Un,Hn],__init__:["drilldownBreadcrumbs","drilldownOverlayBehavior","drilldownCentering","subprocessCompatibility"],drilldownBreadcrumbs:["type",Jn],drilldownCentering:["type",Qn],drilldownOverlayBehavior:["type",oi],subprocessCompatibility:["type",ni]},si=/^class[ {]/;function li(e){return Array.isArray(e)}function pi(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function ci(...e){1===e.length&&li(e[0])&&(e=e[0]);const t=(e=[...e]).pop();return t.$inject=e,t}const ui=/constructor\s*[^(]*\(\s*([^)]*)\)/m,mi=/^(?:async\s+)?(?:function\s*[^(]*)?(?:\(\s*([^)]*)\)|(\w+))/m,di=/\/\*([^*]*)\*\//m;function hi(e){if("function"!=typeof e)throw new Error(`Cannot annotate "${e}". Expected a function!`);const t=e.toString().match(function(e){return si.test(e.toString())}(e)?ui:mi);if(!t)return[];const n=t[1]||t[2];return n&&n.split(",").map((e=>{const t=e.match(di);return(t&&t[1]||e).trim()}))||[]}function fi(e,t){t=t||{get:function(e,t){if(n.push(e),!1===t)return null;throw a(`No provider for "${e}"!`)}};const n=[],i=this._providers=Object.create(t._providers||null),r=this._instances=Object.create(null),o=r.injector=this,a=function(e){const t=n.join(" -> ");return n.length=0,new Error(t?`${e} (Resolving: ${t})`:e)};function s(e,o){if(!i[e]&&-1!==e.indexOf(".")){const t=e.split(".");let n=s(t.shift());for(;t.length;)n=n[t.shift()];return n}if(pi(r,e))return r[e];if(pi(i,e)){if(-1!==n.indexOf(e))throw n.push(e),a("Cannot resolve circular dependency!");return n.push(e),r[e]=i[e][0](i[e][1]),n.pop(),r[e]}return t.get(e,o)}function l(e,t){if(void 0===t&&(t={}),"function"!=typeof e){if(!li(e))throw a(`Cannot invoke "${e}". Expected a function!`);e=ci(e.slice())}return{fn:e,dependencies:(e.$inject||hi(e)).map((e=>pi(t,e)?t[e]:s(e)))}}function p(e){const{fn:t,dependencies:n}=l(e);return new(Function.prototype.bind.apply(t,[null].concat(n)))}function c(e,t,n){const{fn:i,dependencies:r}=l(e,n);return i.apply(t,r)}function u(e){return ci((t=>e.get(t)))}function m(e,t){if(t&&t.length){const n=Object.create(null),r=Object.create(null),o=[],a=[],s=[];let l,p,c,m;for(let e in i)l=i[e],-1!==t.indexOf(e)&&("private"===l[2]?(p=o.indexOf(l[3]),-1===p?(c=l[3].createChild([],t),m=u(c),o.push(l[3]),a.push(c),s.push(m),n[e]=[m,e,"private",c]):n[e]=[s[p],e,"private",a[p]]):n[e]=[l[2],l[1]],r[e]=!0),"factory"!==l[2]&&"type"!==l[2]||!l[1].$scope||t.forEach((t=>{-1!==l[1].$scope.indexOf(t)&&(n[e]=[l[2],l[1]],r[t]=!0)}));t.forEach((e=>{if(!r[e])throw new Error('No provider for "'+e+'". Cannot use provider from the parent!')})),e.unshift(n)}return new fi(e,o)}const d={factory:c,type:p,value:function(e){return e}};function h(e,t){const n=e.__init__||[];return function(){n.forEach((e=>{"string"==typeof e?t.get(e):t.invoke(e)}))}}function f(e){const t=e.__exports__;if(t){const n=e.__modules__,r=Object.keys(e).reduce(((t,n)=>("__exports__"!==n&&"__modules__"!==n&&"__init__"!==n&&"__depends__"!==n&&(t[n]=e[n]),t)),Object.create(null)),o=m((n||[]).concat(r)),a=ci((function(e){return o.get(e)}));t.forEach((function(e){i[e]=[a,e,"private",o]}));const s=(e.__init__||[]).slice();return s.unshift((function(){o.init()})),h(e=Object.assign({},e,{__init__:s}),o)}return Object.keys(e).forEach((function(t){if("__init__"===t||"__depends__"===t)return;if("private"===e[t][2])return void(i[t]=e[t]);const n=e[t][0],r=e[t][1];i[t]=[d[n],yi(n,r),n]})),h(e,o)}function y(e,t){return-1!==e.indexOf(t)||-1!==(e=(t.__depends__||[]).reduce(y,e)).indexOf(t)?e:e.concat(t)}this.get=s,this.invoke=c,this.instantiate=p,this.createChild=m,this.init=function(e){const t=e.reduce(y,[]).map(f);let n=!1;return function(){n||(n=!0,t.forEach((e=>e())))}}(e)}function yi(e,t){return"value"!==e&&li(t)&&(t=ci(t.slice())),t}function gi(e,t){ve.call(this,e,1),this.CONNECTION_STYLE=t.style(["no-fill"],{strokeWidth:5,stroke:"fuchsia"}),this.SHAPE_STYLE=t.style({fill:"white",stroke:"fuchsia",strokeWidth:2}),this.FRAME_STYLE=t.style(["no-fill"],{stroke:"fuchsia",strokeDasharray:4,strokeWidth:2})}q(gi,ve),gi.prototype.canRender=function(){return!0},gi.prototype.drawShape=function(e,t,n){var i=je("rect");return Te(i,{x:0,y:0,width:t.width||0,height:t.height||0}),An(t)?Te(i,ge({},this.FRAME_STYLE,n||{})):Te(i,ge({},this.SHAPE_STYLE,n||{})),Pe(e,i),i},gi.prototype.drawConnection=function(e,t,n){var i=ot(t.waypoints,ge({},this.CONNECTION_STYLE,n||{}));return Pe(e,i),i},gi.prototype.getShapePath=function(e){var t=e.x,n=e.y,i=e.width;return et([["M",t,n],["l",i,0],["l",0,e.height],["l",-i,0],["z"]])},gi.prototype.getConnectionPath=function(e){var t,n,i=e.waypoints,r=[];for(t=0;n=i[t];t++)n=n.original||n,r.push([0===t?"M":"L",n.x,n.y]);return et(r)},gi.$inject=["eventBus","styles"];const vi={__init__:["defaultRenderer"],defaultRenderer:["type",gi],styles:["type",function(){var e={"no-fill":{fill:"none"},"no-border":{strokeOpacity:0},"no-events":{pointerEvents:"none"}},t=this;this.cls=function(e,t,n){return ge(this.style(t,n),{class:e})},this.style=function(t,n){ee(t)||n||(n=t,t=[]);var i=pe(t,(function(t,n){return ge(t,e[n]||{})}),{});return n?ge(i,n):i},this.computeStyle=function(e,n,i){return ee(n)||(i=n,n=[]),t.style(n||[],ge({},i,e||{}))}}]};function bi(e,t){return Math.round(e*t)/t}function wi(e){return ne(e)?e+"px":e}function xi(e,t,n){const i=je("g");De(i).add(t);const r=void 0!==n?n:e.childNodes.length-1;return e.insertBefore(i,e.childNodes[r]||null),i}const Ei={shape:["x","y","width","height"],connection:["waypoints"]};function _i(e,t,n,i){this._eventBus=t,this._elementRegistry=i,this._graphicsFactory=n,this._rootsIdx=0,this._layers={},this._planes=[],this._rootElement=null,this._init(e||{})}function Si(e,t){const n="matrix("+t.a+","+t.b+","+t.c+","+t.d+","+t.e+","+t.f+")";e.setAttribute("transform",n)}_i.$inject=["config.canvas","eventBus","graphicsFactory","elementRegistry"],_i.prototype._init=function(e){const t=this._eventBus,n=this._container=function(e){const t=(e=ge({},{width:"100%",height:"100%"},e)).container||document.body,n=document.createElement("div");return n.setAttribute("class","djs-container djs-parent"),bt(n,{position:"relative",overflow:"hidden",width:wi(e.width),height:wi(e.height)}),t.appendChild(n),n}(e),i=this._svg=je("svg");Te(i,{width:"100%",height:"100%"}),Pe(n,i);const r=this._viewport=xi(i,"viewport");e.deferUpdate&&(this._viewboxChanged=function(e,t){let n,i,r,o;function a(n){let a=Date.now(),p=n?0:o+t-a;if(p>0)return s(p);e.apply(r,i),l()}function s(e){n=setTimeout(a,e)}function l(){n&&clearTimeout(n),n=o=i=r=void 0}function p(...e){o=Date.now(),i=e,r=this,n||s(t)}return p.flush=function(){n&&a(!0),l()},p.cancel=l,p}(ye(this._viewboxChanged,this),300)),t.on("diagram.init",(()=>{t.fire("canvas.init",{svg:i,viewport:r})})),t.on(["shape.added","connection.added","shape.removed","connection.removed","elements.changed","root.set"],(()=>{delete this._cachedViewbox})),t.on("diagram.destroy",500,this._destroy,this),t.on("diagram.clear",500,this._clear,this)},_i.prototype._destroy=function(){this._eventBus.fire("canvas.destroy",{svg:this._svg,viewport:this._viewport});const e=this._container.parentNode;e&&e.removeChild(this._container),delete this._svg,delete this._container,delete this._layers,delete this._planes,delete this._rootElement,delete this._viewport},_i.prototype._clear=function(){this._elementRegistry.getAll().forEach((e=>{const t=Sn(e);"root"===t?this.removeRootElement(e):this._removeElement(e,t)})),this._planes=[],this._rootElement=null,delete this._cachedViewbox},_i.prototype.getDefaultLayer=function(){return this.getLayer("base",0)},_i.prototype.getLayer=function(e,t){if(!e)throw new Error("must specify a name");let n=this._layers[e];if(n||(n=this._layers[e]=this._createLayer(e,t)),void 0!==t&&n.index!==t)throw new Error("layer <"+e+"> already created at index <"+t+">");return n.group},_i.prototype._getChildIndex=function(e){return pe(this._layers,(function(t,n){return n.visible&&e>=n.index&&t++,t}),0)},_i.prototype._createLayer=function(e,t){void 0===t&&(t=1);const n=this._getChildIndex(t);return{group:xi(this._viewport,"layer-"+e,n),index:t,visible:!0}},_i.prototype.showLayer=function(e){if(!e)throw new Error("must specify a name");const t=this._layers[e];if(!t)throw new Error("layer <"+e+"> does not exist");const n=this._viewport,i=t.group,r=t.index;if(t.visible)return i;const o=this._getChildIndex(r);return n.insertBefore(i,n.childNodes[o]||null),t.visible=!0,i},_i.prototype.hideLayer=function(e){if(!e)throw new Error("must specify a name");const t=this._layers[e];if(!t)throw new Error("layer <"+e+"> does not exist");const n=t.group;return t.visible?(Je(n),t.visible=!1,n):n},_i.prototype._removeLayer=function(e){const t=this._layers[e];t&&(delete this._layers[e],Je(t.group))},_i.prototype.getActiveLayer=function(){const e=this._findPlaneForRoot(this.getRootElement());return e?e.layer:null},_i.prototype.findRoot=function(e){if("string"==typeof e&&(e=this._elementRegistry.get(e)),!e)return;const t=this._findPlaneForRoot(function(e){for(;e.parent;)e=e.parent;return e}(e))||{};return t.rootElement},_i.prototype.getRootElements=function(){return this._planes.map((function(e){return e.rootElement}))},_i.prototype._findPlaneForRoot=function(e){return ae(this._planes,(function(t){return t.rootElement===e}))},_i.prototype.getContainer=function(){return this._container},_i.prototype._updateMarker=function(e,t,n){let i;e.id||(e=this._elementRegistry.get(e)),i=this._elementRegistry._elements[e.id],i&&(le([i.gfx,i.secondaryGfx],(function(e){e&&(n?De(e).add(t):De(e).remove(t))})),this._eventBus.fire("element.marker.update",{element:e,gfx:i.gfx,marker:t,add:!!n}))},_i.prototype.addMarker=function(e,t){this._updateMarker(e,t,!0)},_i.prototype.removeMarker=function(e,t){this._updateMarker(e,t,!1)},_i.prototype.hasMarker=function(e,t){return e.id||(e=this._elementRegistry.get(e)),De(this.getGraphics(e)).has(t)},_i.prototype.toggleMarker=function(e,t){this.hasMarker(e,t)?this.removeMarker(e,t):this.addMarker(e,t)},_i.prototype.getRootElement=function(){const e=this._rootElement;return e||this._planes.length?e:this.setRootElement(this.addRootElement(null))},_i.prototype.addRootElement=function(e){const t=this._rootsIdx++;e||(e={id:"__implicitroot_"+t,children:[],isImplicit:!0});const n=e.layer="root-"+t;this._ensureValid("root",e);const i=this.getLayer(n,0);return this.hideLayer(n),this._addRoot(e,i),this._planes.push({rootElement:e,layer:i}),e},_i.prototype.removeRootElement=function(e){if("string"==typeof e&&(e=this._elementRegistry.get(e)),this._findPlaneForRoot(e))return this._removeRoot(e),this._removeLayer(e.layer),this._planes=this._planes.filter((function(t){return t.rootElement!==e})),this._rootElement===e&&(this._rootElement=null),e},_i.prototype.setRootElement=function(e){if(e===this._rootElement)return;let t;if(!e)throw new Error("rootElement required");return t=this._findPlaneForRoot(e),t||(e=this.addRootElement(e)),this._setRoot(e),e},_i.prototype._removeRoot=function(e){const t=this._elementRegistry,n=this._eventBus;n.fire("root.remove",{element:e}),n.fire("root.removed",{element:e}),t.remove(e)},_i.prototype._addRoot=function(e,t){const n=this._elementRegistry,i=this._eventBus;i.fire("root.add",{element:e}),n.add(e,t),i.fire("root.added",{element:e,gfx:t})},_i.prototype._setRoot=function(e,t){const n=this._rootElement;n&&(this._elementRegistry.updateGraphics(n,null,!0),this.hideLayer(n.layer)),e&&(t||(t=this._findPlaneForRoot(e).layer),this._elementRegistry.updateGraphics(e,this._svg,!0),this.showLayer(e.layer)),this._rootElement=e,this._eventBus.fire("root.set",{element:e})},_i.prototype._ensureValid=function(e,t){if(!t.id)throw new Error("element must have an id");if(this._elementRegistry.get(t.id))throw new Error("element <"+t.id+"> already exists");const n=Ei[e],i=ce(n,(function(e){return void 0!==t[e]}));if(!i)throw new Error("must supply { "+n.join(", ")+" } with "+e)},_i.prototype._setParent=function(e,t,n){!function(e,t,n){if(e&&t){"number"!=typeof n&&(n=-1);var i=e.indexOf(t);if(-1!==i){if(i===n)return;if(-1===n)return;e.splice(i,1)}-1!==n?e.splice(n,0,t):e.push(t)}}(t.children,e,n),e.parent=t},_i.prototype._addElement=function(e,t,n,i){n=n||this.getRootElement();const r=this._eventBus,o=this._graphicsFactory;this._ensureValid(e,t),r.fire(e+".add",{element:t,parent:n}),this._setParent(t,n,i);const a=o.create(e,t,i);return this._elementRegistry.add(t,a),o.update(e,t,a),r.fire(e+".added",{element:t,gfx:a}),t},_i.prototype.addShape=function(e,t,n){return this._addElement("shape",e,t,n)},_i.prototype.addConnection=function(e,t,n){return this._addElement("connection",e,t,n)},_i.prototype._removeElement=function(e,t){const n=this._elementRegistry,i=this._graphicsFactory,r=this._eventBus;if(e=n.get(e.id||e))return r.fire(t+".remove",{element:e}),i.remove(e),function(e,t){if(!e||!t)return-1;var n=e.indexOf(t);-1!==n&&e.splice(n,1)}(e.parent&&e.parent.children,e),e.parent=null,r.fire(t+".removed",{element:e}),n.remove(e),e},_i.prototype.removeShape=function(e){return this._removeElement(e,"shape")},_i.prototype.removeConnection=function(e){return this._removeElement(e,"connection")},_i.prototype.getGraphics=function(e,t){return this._elementRegistry.getGraphics(e,t)},_i.prototype._changeViewbox=function(e){this._eventBus.fire("canvas.viewbox.changing"),e.apply(this),this._cachedViewbox=null,this._viewboxChanged()},_i.prototype._viewboxChanged=function(){this._eventBus.fire("canvas.viewbox.changed",{viewbox:this.viewbox()})},_i.prototype.viewbox=function(e){if(void 0===e&&this._cachedViewbox)return this._cachedViewbox;const t=this._viewport,n=this.getSize();let i,r,o,a,s,l,p;return e?(this._changeViewbox((function(){s=Math.min(n.width/e.width,n.height/e.height);const i=this._svg.createSVGMatrix().scale(s).translate(-e.x,-e.y);Ze(t,i)})),e):(o=this._rootElement?this.getActiveLayer():null,i=o&&o.getBBox()||{},a=Ze(t),r=a?a.matrix:function(e,t,n,i,r,o){var a=$e().createSVGMatrix();switch(arguments.length){case 0:return a;case 1:return ze(a,e);case 6:return ze(a,{a:e,b:t,c:n,d:i,e:r,f:o})}}(),s=bi(r.a,1e3),l=bi(-r.e||0,1e3),p=bi(-r.f||0,1e3),e=this._cachedViewbox={x:l?l/s:0,y:p?p/s:0,width:n.width/s,height:n.height/s,scale:s,inner:{width:i.width||0,height:i.height||0,x:i.x||0,y:i.y||0},outer:n})},_i.prototype.scroll=function(e){const t=this._viewport;let n=t.getCTM();return e&&this._changeViewbox((function(){e=ge({dx:0,dy:0},e||{}),n=this._svg.createSVGMatrix().translate(e.dx,e.dy).multiply(n),Si(t,n)})),{x:n.e,y:n.f}},_i.prototype.scrollToElement=function(e,t){let n=100;"string"==typeof e&&(e=this._elementRegistry.get(e));const i=this.findRoot(e);if(i!==this.getRootElement()&&this.setRootElement(i),i===e)return;t||(t={}),"number"==typeof t&&(n=t),t={top:t.top||n,right:t.right||n,bottom:t.bottom||n,left:t.left||n};const r=_n(e),o=on(r),a=this.viewbox(),s=this.zoom();let l,p;a.y+=t.top/s,a.x+=t.left/s,a.width-=(t.right+t.left)/s,a.height-=(t.bottom+t.top)/s;const c=on(a);if(r.width<a.width&&r.height<a.height){const e=Math.max(0,o.right-c.right),t=Math.min(0,o.left-c.left),n=Math.max(0,o.bottom-c.bottom),i=Math.min(0,o.top-c.top);l=e||t,p=n||i}else l=r.x-a.x,p=r.y-a.y;this.scroll({dx:-l*s,dy:-p*s})},_i.prototype.zoom=function(e,t){if(!e)return this.viewbox(e).scale;if("fit-viewport"===e)return this._fitViewport(t);let n,i;return this._changeViewbox((function(){"object"!=typeof t&&(n=this.viewbox().outer,t={x:n.width/2,y:n.height/2}),i=this._setZoom(e,t)})),bi(i.a,1e3)},_i.prototype._fitViewport=function(e){const t=this.viewbox(),n=t.outer,i=t.inner;let r,o;return i.x>=0&&i.y>=0&&i.x+i.width<=n.width&&i.y+i.height<=n.height&&!e?o={x:0,y:0,width:Math.max(i.width+i.x,n.width),height:Math.max(i.height+i.y,n.height)}:(r=Math.min(1,n.width/i.width,n.height/i.height),o={x:i.x+(e?i.width/2-n.width/r/2:0),y:i.y+(e?i.height/2-n.height/r/2:0),width:n.width/r,height:n.height/r}),this.viewbox(o),this.viewbox(!1).scale},_i.prototype._setZoom=function(e,t){const n=this._svg,i=this._viewport,r=n.createSVGMatrix(),o=n.createSVGPoint();let a,s,l,p,c;l=i.getCTM();const u=l.a;return t?(a=ge(o,t),s=a.matrixTransform(l.inverse()),p=r.translate(s.x,s.y).scale(1/u*e).translate(-s.x,-s.y),c=l.multiply(p)):c=r.scale(e),Si(this._viewport,c),c},_i.prototype.getSize=function(){return{width:this._container.clientWidth,height:this._container.clientHeight}},_i.prototype.getAbsoluteBBox=function(e){const t=this.viewbox();let n;return n=e.waypoints?this.getGraphics(e).getBBox():e,{x:n.x*t.scale-t.x*t.scale,y:n.y*t.scale-t.y*t.scale,width:n.width*t.scale,height:n.height*t.scale}},_i.prototype.resized=function(){delete this._cachedViewbox,this._eventBus.fire("canvas.resized")};var Ai="data-element-id";function Ci(e){this._elements={},this._eventBus=e}Ci.$inject=["eventBus"],Ci.prototype.add=function(e,t,n){var i=e.id;this._validateId(i),Te(t,Ai,i),n&&Te(n,Ai,i),this._elements[i]={element:e,gfx:t,secondaryGfx:n}},Ci.prototype.remove=function(e){var t=this._elements,n=e.id||e,i=n&&t[n];i&&(Te(i.gfx,Ai,""),i.secondaryGfx&&Te(i.secondaryGfx,Ai,""),delete t[n])},Ci.prototype.updateId=function(e,t){this._validateId(t),"string"==typeof e&&(e=this.get(e)),this._eventBus.fire("element.updateId",{element:e,newId:t});var n=this.getGraphics(e),i=this.getGraphics(e,!0);this.remove(e),e.id=t,this.add(e,n,i)},Ci.prototype.updateGraphics=function(e,t,n){var i=e.id||e,r=this._elements[i];return n?r.secondaryGfx=t:r.gfx=t,t&&Te(t,Ai,i),t},Ci.prototype.get=function(e){var t;t="string"==typeof e?e:e&&Te(e,Ai);var n=this._elements[t];return n&&n.element},Ci.prototype.filter=function(e){var t=[];return this.forEach((function(n,i){e(n,i)&&t.push(n)})),t},Ci.prototype.find=function(e){for(var t=this._elements,n=Object.keys(t),i=0;i<n.length;i++){var r=t[n[i]],o=r.element;if(e(o,r.gfx))return o}},Ci.prototype.getAll=function(){return this.filter((function(e){return e}))},Ci.prototype.forEach=function(e){var t=this._elements;Object.keys(t).forEach((function(n){var i=t[n],r=i.element,o=i.gfx;return e(r,o)}))},Ci.prototype.getGraphics=function(e,t){var n=e.id||e,i=this._elements[n];return i&&(t?i.secondaryGfx:i.gfx)},Ci.prototype._validateId=function(e){if(!e)throw new Error("element must have an id");if(this._elements[e])throw new Error("element with id "+e+" already added")};var Ri={exports:{}},Pi={extend:function(e,t,n,i){var r=n.inverse;return Object.defineProperty(e,"remove",{value:function(e){var n=this.indexOf(e);return-1!==n&&(this.splice(n,1),t.unset(e,r,i)),e}}),Object.defineProperty(e,"contains",{value:function(e){return-1!==this.indexOf(e)}}),Object.defineProperty(e,"add",{value:function(e,n){var o=this.indexOf(e);if(void 0===n){if(-1!==o)return;n=this.length}-1!==o&&this.splice(o,1),this.splice(n,0,e),-1===o&&t.set(e,r,i)}}),Object.defineProperty(e,"__refs_collection",{value:!0}),e},isExtended:function(e){return!0===e.__refs_collection}},ki=Pi;function Mi(e,t,n){var i=ki.extend(n[t.name]||[],e,t,n);Object.defineProperty(n,t.name,{enumerable:t.enumerable,value:i}),i.length&&i.forEach((function(i){e.set(i,t.inverse,n)}))}function Ti(e,t){if(!(this instanceof Ti))return new Ti(e,t);e.inverse=t,t.inverse=e,this.props={},this.props[e.name]=e,this.props[t.name]=t}Ti.prototype.bind=function(e,t){if("string"==typeof t){if(!this.props[t])throw new Error("no property <"+t+"> in ref");t=this.props[t]}t.collection?Mi(this,t,e):function(e,t,n){var i=t.inverse,r=n[t.name];Object.defineProperty(n,t.name,{configurable:t.configurable,enumerable:t.enumerable,get:function(){return r},set:function(t){if(t!==r){var o=r;r=null,o&&e.unset(o,i,n),r=t,e.set(r,i,n)}}})}(this,t,e)},Ti.prototype.ensureRefsCollection=function(e,t){var n=e[t.name];return ki.isExtended(n)||Mi(this,t,e),n},Ti.prototype.ensureBound=function(e,t){(function(e,t){return Object.prototype.hasOwnProperty.call(e,t.name||t)})(e,t)||this.bind(e,t)},Ti.prototype.unset=function(e,t,n){e&&(this.ensureBound(e,t),t.collection?this.ensureRefsCollection(e,t).remove(n):e[t.name]=void 0)},Ti.prototype.set=function(e,t,n){e&&(this.ensureBound(e,t),t.collection?this.ensureRefsCollection(e,t).add(n):e[t.name]=n)};var Ni=Ti;Ri.exports=Ni,Ri.exports.Collection=Pi;var Di=Ri.exports;const Oi=L(Di);var Ii=new Oi({name:"children",enumerable:!0,collection:!0},{name:"parent"}),Li=new Oi({name:"labels",enumerable:!0,collection:!0},{name:"labelTarget"}),Bi=new Oi({name:"attachers",collection:!0},{name:"host"}),Fi=new Oi({name:"outgoing",collection:!0},{name:"source"}),ji=new Oi({name:"incoming",collection:!0},{name:"target"});function Vi(){Object.defineProperty(this,"businessObject",{writable:!0}),Object.defineProperty(this,"label",{get:function(){return this.labels[0]},set:function(e){var t=this.label,n=this.labels;!e&&t?n.remove(t):n.add(e,0)}}),Ii.bind(this,"parent"),Li.bind(this,"labels"),Fi.bind(this,"outgoing"),ji.bind(this,"incoming")}function $i(){Vi.call(this),Ii.bind(this,"children"),Bi.bind(this,"host"),Bi.bind(this,"attachers")}function zi(){Vi.call(this),Ii.bind(this,"children")}function Ui(){$i.call(this),Li.bind(this,"labelTarget")}function Wi(){Vi.call(this),Fi.bind(this,"source"),ji.bind(this,"target")}q($i,Vi),q(zi,$i),q(Ui,$i),q(Wi,Vi);var Ki={connection:Wi,shape:$i,label:Ui,root:zi};function Gi(){this._uid=12}Gi.prototype.createRoot=function(e){return this.create("root",e)},Gi.prototype.createLabel=function(e){return this.create("label",e)},Gi.prototype.createShape=function(e){return this.create("shape",e)},Gi.prototype.createConnection=function(e){return this.create("connection",e)},Gi.prototype.create=function(e,t){return(t=ge({},t||{})).id||(t.id=e+"_"+this._uid++),function(e,t){var n=Ki[e];if(!n)throw new Error("unknown type: <"+e+">");return ge(new n,t)}(e,t)};var Hi="__fn",qi=Array.prototype.slice;function Yi(){this._listeners={},this.on("diagram.destroy",1,this._destroy,this)}function Xi(){}function Ji(e,t){this._eventBus=e,this._elementRegistry=t}function Qi(e,t,n){var i=n||t.firstChild;e!==i&&t.insertBefore(e,i)}Yi.prototype.on=function(e,t,n,i){if(e=ee(e)?e:[e],ie(t)&&(i=n,n=t,t=1e3),!ne(t))throw new Error("priority must be a number");var r=n;i&&((r=ye(n,i))[Hi]=n[Hi]||n);var o=this;e.forEach((function(e){o._addListener(e,{priority:t,callback:r,next:null})}))},Yi.prototype.once=function(e,t,n,i){var r=this;if(ie(t)&&(i=n,n=t,t=1e3),!ne(t))throw new Error("priority must be a number");function o(){o.__isTomb=!0;var t=n.apply(i,arguments);return r.off(e,o),t}o[Hi]=n,this.on(e,t,o)},Yi.prototype.off=function(e,t){e=ee(e)?e:[e];var n=this;e.forEach((function(e){n._removeListener(e,t)}))},Yi.prototype.createEvent=function(e){var t=new Xi;return t.init(e),t},Yi.prototype.fire=function(e,t){var n,i,r,o;if(o=qi.call(arguments),"object"==typeof e&&(e=(t=e).type),!e)throw new Error("no event type specified");if(i=this._listeners[e]){n=t instanceof Xi?t:this.createEvent(t),o[0]=n;var a=n.type;e!==a&&(n.type=e);try{r=this._invokeListeners(n,o,i)}finally{e!==a&&(n.type=a)}return void 0===r&&n.defaultPrevented&&(r=!1),r}},Yi.prototype.handleError=function(e){return!1===this.fire("error",{error:e})},Yi.prototype._destroy=function(){this._listeners={}},Yi.prototype._invokeListeners=function(e,t,n){for(var i;n&&!e.cancelBubble;)i=this._invokeListener(e,t,n),n=n.next;return i},Yi.prototype._invokeListener=function(e,t,n){var i;if(n.callback.__isTomb)return i;try{i=function(e,t){return e.apply(null,t)}(n.callback,t),void 0!==i&&(e.returnValue=i,e.stopPropagation()),!1===i&&e.preventDefault()}catch(jr){if(!this.handleError(jr))throw console.error("unhandled error in event listener",jr),jr}return i},Yi.prototype._addListener=function(e,t){var n,i=this._getListeners(e);if(i){for(;i;){if(i.priority<t.priority)return t.next=i,void(n?n.next=t:this._setListeners(e,t));n=i,i=i.next}n.next=t}else this._setListeners(e,t)},Yi.prototype._getListeners=function(e){return this._listeners[e]},Yi.prototype._setListeners=function(e,t){this._listeners[e]=t},Yi.prototype._removeListener=function(e,t){var n,i,r,o=this._getListeners(e);if(t)for(;o;)n=o.next,(r=o.callback)!==t&&r[Hi]!==t||(i?i.next=n:this._setListeners(e,n)),i=o,o=n;else this._setListeners(e,null)},Xi.prototype.stopPropagation=function(){this.cancelBubble=!0},Xi.prototype.preventDefault=function(){this.defaultPrevented=!0},Xi.prototype.init=function(e){ge(this,e||{})},Ji.$inject=["eventBus","elementRegistry"],Ji.prototype._getChildrenContainer=function(e){var t,n=this._elementRegistry.getGraphics(e);return e.parent?(t=function(e){return e.parentNode.childNodes[1]}(n),t||(De(t=je("g")).add("djs-children"),Pe(n.parentNode,t))):t=n,t},Ji.prototype._clear=function(e){var t=function(e){return e.childNodes[0]}(e);return _t(t),t},Ji.prototype._createContainer=function(e,t,n,i){var r=je("g");De(r).add("djs-group"),void 0!==n?Qi(r,t,t.childNodes[n]):Pe(t,r);var o=je("g");De(o).add("djs-element"),De(o).add("djs-"+e),i&&De(o).add("djs-frame"),Pe(r,o);var a=je("g");return De(a).add("djs-visual"),Pe(o,a),o},Ji.prototype.create=function(e,t,n){var i=this._getChildrenContainer(t.parent);return this._createContainer(e,i,n,An(t))},Ji.prototype.updateContainments=function(e){var t,n=this,i=this._elementRegistry;t=pe(e,(function(e,t){return t.parent&&(e[t.parent.id]=t.parent),e}),{}),le(t,(function(e){var t=e.children;if(t){var r=n._getChildrenContainer(e);le(t.slice().reverse(),(function(e){Qi(i.getGraphics(e).parentNode,r)}))}}))},Ji.prototype.drawShape=function(e,t,n={}){return this._eventBus.fire("render.shape",{gfx:e,element:t,attrs:n})},Ji.prototype.getShapePath=function(e){return this._eventBus.fire("render.getShapePath",e)},Ji.prototype.drawConnection=function(e,t,n={}){return this._eventBus.fire("render.connection",{gfx:e,element:t,attrs:n})},Ji.prototype.getConnectionPath=function(e){return this._eventBus.fire("render.getConnectionPath",e)},Ji.prototype.update=function(e,t,n){if(t.parent){var i=this._clear(n);if("shape"===e)this.drawShape(i,t),Vt(n,t.x,t.y);else{if("connection"!==e)throw new Error("unknown type: "+e);this.drawConnection(i,t)}t.hidden?Te(n,"display","none"):Te(n,"display","block")}},Ji.prototype.remove=function(e){Je(this._elementRegistry.getGraphics(e).parentNode)};const Zi={__depends__:[vi],__init__:["canvas"],canvas:["type",_i],elementRegistry:["type",Ci],elementFactory:["type",Gi],eventBus:["type",Yi],graphicsFactory:["type",Ji]};function er(e){return function(e){var t=new fi(e);return t.init(),t}([{config:["value",e=e||{}]},Zi].concat(e.modules||[]))}function tr(e,t){this._injector=t=t||er(e),this.get=t.get,this.invoke=t.invoke,this.get("eventBus").fire("diagram.init")}function nr(){}function ir(e,t){this.model=e,this.properties=t}tr.prototype.destroy=function(){this.get("eventBus").fire("diagram.destroy")},tr.prototype.clear=function(){this.get("eventBus").fire("diagram.clear")},nr.prototype.get=function(e){return this.$model.properties.get(this,e)},nr.prototype.set=function(e,t){this.$model.properties.set(this,e,t)},ir.prototype.createType=function(e){var t=this.model,n=this.properties,i=Object.create(nr.prototype);le(e.properties,(function(e){e.isMany||void 0===e.default||(i[e.name]=e.default)})),n.defineModel(i,t),n.defineDescriptor(i,e);var r=e.ns.name;function o(e){n.define(this,"$type",{value:r,enumerable:!0}),n.define(this,"$attrs",{value:{}}),n.define(this,"$parent",{writable:!0}),le(e,ye((function(e,t){this.set(t,e)}),this))}return o.prototype=i,o.hasType=i.$instanceOf=this.model.hasType,n.defineModel(o,t),n.defineDescriptor(o,e),o};var rr={String:!0,Boolean:!0,Integer:!0,Real:!0,Element:!0},or={String:function(e){return e},Boolean:function(e){return"true"===e},Integer:function(e){return parseInt(e,10)},Real:function(e){return parseFloat(e)}};function ar(e,t){var n=or[e];return n?n(t):t}function sr(e){return!!rr[e]}function lr(e){return!!or[e]}function pr(e,t){var n,i,r=e.split(/:/);if(1===r.length)n=e,i=t;else{if(2!==r.length)throw new Error("expected <prefix:localName> or <localName>, got "+e);n=r[1],i=r[0]}return{name:e=(i?i+":":"")+n,prefix:i,localName:n}}function cr(e){this.ns=e,this.name=e.name,this.allTypes=[],this.allTypesByName={},this.properties=[],this.propertiesByName={}}function ur(e,t){this.packageMap={},this.typeMap={},this.packages=[],this.properties=t,le(e,ye(this.registerPackage,this))}function mr(e,t,n){var i=t[n];if(i in e)throw new Error("package with "+n+" <"+i+"> already defined")}function dr(e){this.model=e}function hr(e,t,n){Object.defineProperty(e,t.name,{enumerable:!t.isReference,writable:!0,value:n,configurable:!0})}function fr(e){return e.replace(/^:/,"")}function yr(e,t={}){this.properties=new dr(this),this.factory=new ir(this,this.properties),this.registry=new ur(e,this.properties),this.typeCache={},this.config=t}cr.prototype.build=function(){return function(e,t){let n={},i=Object(e);return le(t,(function(t){t in i&&(n[t]=e[t])})),n}(this,["ns","name","allTypes","allTypesByName","properties","propertiesByName","bodyProperty","idProperty"])},cr.prototype.addProperty=function(e,t,n){"boolean"==typeof t&&(n=t,t=void 0),this.addNamedProperty(e,!1!==n);var i=this.properties;void 0!==t?i.splice(t,0,e):i.push(e)},cr.prototype.replaceProperty=function(e,t,n){var i=e.ns,r=this.properties,o=this.propertiesByName,a=e.name!==t.name;if(e.isId){if(!t.isId)throw new Error("property <"+t.ns.name+"> must be id property to refine <"+e.ns.name+">");this.setIdProperty(t,!1)}if(e.isBody){if(!t.isBody)throw new Error("property <"+t.ns.name+"> must be body property to refine <"+e.ns.name+">");this.setBodyProperty(t,!1)}var s=r.indexOf(e);if(-1===s)throw new Error("property <"+i.name+"> not found in property list");r.splice(s,1),this.addProperty(t,n?void 0:s,a),o[i.name]=o[i.localName]=t},cr.prototype.redefineProperty=function(e,t,n){var i=e.ns.prefix,r=t.split("#"),o=pr(r[0],i),a=pr(r[1],o.prefix).name,s=this.propertiesByName[a];if(!s)throw new Error("refined property <"+a+"> not found");this.replaceProperty(s,e,n),delete e.redefines},cr.prototype.addNamedProperty=function(e,t){var n=e.ns,i=this.propertiesByName;t&&(this.assertNotDefined(e,n.name),this.assertNotDefined(e,n.localName)),i[n.name]=i[n.localName]=e},cr.prototype.removeNamedProperty=function(e){var t=e.ns,n=this.propertiesByName;delete n[t.name],delete n[t.localName]},cr.prototype.setBodyProperty=function(e,t){if(t&&this.bodyProperty)throw new Error("body property defined multiple times (<"+this.bodyProperty.ns.name+">, <"+e.ns.name+">)");this.bodyProperty=e},cr.prototype.setIdProperty=function(e,t){if(t&&this.idProperty)throw new Error("id property defined multiple times (<"+this.idProperty.ns.name+">, <"+e.ns.name+">)");this.idProperty=e},cr.prototype.assertNotTrait=function(e){if((e.extends||[]).length)throw new Error(`cannot create <${e.name}> extending <${e.extends}>`)},cr.prototype.assertNotDefined=function(e,t){var n=e.name,i=this.propertiesByName[n];if(i)throw new Error("property <"+n+"> already defined; override of <"+i.definedBy.ns.name+"#"+i.ns.name+"> by <"+e.definedBy.ns.name+"#"+e.ns.name+"> not allowed without redefines")},cr.prototype.hasProperty=function(e){return this.propertiesByName[e]},cr.prototype.addTrait=function(e,t){t&&this.assertNotTrait(e);var n=this.allTypesByName,i=this.allTypes,r=e.name;r in n||(le(e.properties,ye((function(n){n=ge({},n,{name:n.ns.localName,inherited:t}),Object.defineProperty(n,"definedBy",{value:e});var i=n.replaces,r=n.redefines;i||r?this.redefineProperty(n,i||r,i):(n.isBody&&this.setBodyProperty(n),n.isId&&this.setIdProperty(n),this.addProperty(n))}),this)),i.push(e),n[r]=e)},ur.prototype.getPackage=function(e){return this.packageMap[e]},ur.prototype.getPackages=function(){return this.packages},ur.prototype.registerPackage=function(e){e=ge({},e);var t=this.packageMap;mr(t,e,"prefix"),mr(t,e,"uri"),le(e.types,ye((function(t){this.registerType(t,e)}),this)),t[e.uri]=t[e.prefix]=e,this.packages.push(e)},ur.prototype.registerType=function(e,t){var n=pr((e=ge({},e,{superClass:(e.superClass||[]).slice(),extends:(e.extends||[]).slice(),properties:(e.properties||[]).slice(),meta:ge(e.meta||{})})).name,t.prefix),i=n.name,r={};le(e.properties,ye((function(e){var t=pr(e.name,n.prefix),i=t.name;sr(e.type)||(e.type=pr(e.type,t.prefix).name),ge(e,{ns:t,name:i}),r[i]=e}),this)),ge(e,{ns:n,name:i,propertiesByName:r}),le(e.extends,ye((function(e){var t=pr(e,n.prefix),r=this.typeMap[t.name];r.traits=r.traits||[],r.traits.push(i)}),this)),this.definePackage(e,t),this.typeMap[i]=e},ur.prototype.mapTypes=function(e,t,n){var i=sr(e.name)?{name:e.name}:this.typeMap[e.name],r=this;function o(n,i){var o=pr(n,sr(n)?"":e.prefix);r.mapTypes(o,t,i)}function a(e){return o(e,!0)}if(!i)throw new Error("unknown type <"+e.name+">");le(i.superClass,n?a:function(e){return o(e,!1)}),t(i,!n),le(i.traits,a)},ur.prototype.getEffectiveDescriptor=function(e){var t=pr(e),n=new cr(t);this.mapTypes(t,(function(e,t){n.addTrait(e,t)}));var i=n.build();return this.definePackage(i,i.allTypes[i.allTypes.length-1].$pkg),i},ur.prototype.definePackage=function(e,t){this.properties.define(e,"$pkg",{value:t})},dr.prototype.set=function(e,t,n){if(!re(t)||!t.length)throw new TypeError("property name must be a non-empty string");var i=this.getProperty(e,t),r=i&&i.name;void 0===n?i?delete e[r]:delete e.$attrs[fr(t)]:i?r in e?e[r]=n:hr(e,i,n):e.$attrs[fr(t)]=n},dr.prototype.get=function(e,t){var n=this.getProperty(e,t);if(!n)return e.$attrs[fr(t)];var i=n.name;return!e[i]&&n.isMany&&hr(e,n,[]),e[i]},dr.prototype.define=function(e,t,n){if(!n.writable){var i=n.value;delete(n=ge({},n,{get:function(){return i}})).value}Object.defineProperty(e,t,n)},dr.prototype.defineDescriptor=function(e,t){this.define(e,"$descriptor",{value:t})},dr.prototype.defineModel=function(e,t){this.define(e,"$model",{value:t})},dr.prototype.getProperty=function(e,t){var n=this.model,i=n.getPropertyDescriptor(e,t);if(i)return i;if(t.includes(":"))return null;const r=n.config.strict;if(void 0!==r){const n=new TypeError(`unknown property <${t}> on <${e.$type}>`);if(r)throw n;"undefined"!=typeof console&&console.warn(n)}return null},yr.prototype.create=function(e,t){var n=this.getType(e);if(!n)throw new Error("unknown type <"+e+">");return new n(t)},yr.prototype.getType=function(e){var t=this.typeCache,n=re(e)?e:e.ns.name,i=t[n];return i||(e=this.registry.getEffectiveDescriptor(n),i=t[n]=this.factory.createType(e)),i},yr.prototype.createAny=function(e,t,n){var i=pr(e),r={$type:e,$instanceOf:function(e){return e===this.$type},get:function(e){return this[e]},set:function(e,t){!function(e,t,n){let i=e;le(t,(function(e,r){if("number"!=typeof e&&"string"!=typeof e)throw new Error("illegal key type: "+typeof e+". Key should be of type number or string.");if("constructor"===e)throw new Error("illegal key: constructor");if("__proto__"===e)throw new Error("illegal key: __proto__");let o=t[r+1],a=i[e];Q(o)&&Z(a)&&(a=i[e]=isNaN(+o)?{}:[]),J(o)?J(n)?delete i[e]:i[e]=n:i=a}))}(this,[e],t)}},o={name:e,isGeneric:!0,ns:{prefix:i.prefix,localName:i.localName,uri:t}};return this.properties.defineDescriptor(r,o),this.properties.defineModel(r,this),this.properties.define(r,"get",{enumerable:!1,writable:!0}),this.properties.define(r,"set",{enumerable:!1,writable:!0}),this.properties.define(r,"$parent",{enumerable:!1,writable:!0}),this.properties.define(r,"$instanceOf",{enumerable:!1,writable:!0}),le(n,(function(e,t){te(e)&&void 0!==e.value?r[e.name]=e.value:r[t]=e})),r},yr.prototype.getPackage=function(e){return this.registry.getPackage(e)},yr.prototype.getPackages=function(){return this.registry.getPackages()},yr.prototype.getElementDescriptor=function(e){return e.$descriptor},yr.prototype.hasType=function(e,t){return void 0===t&&(t=e,e=this),t in e.$model.getElementDescriptor(e).allTypesByName},yr.prototype.getPropertyDescriptor=function(e,t){return this.getElementDescriptor(e).propertiesByName[t]},yr.prototype.getTypeDescriptor=function(e){return this.registry.typeMap[e]};var gr=String.fromCharCode,vr=Object.prototype.hasOwnProperty,br=/&#(\d+);|&#x([0-9a-f]+);|&(\w+);/gi,wr={amp:"&",apos:"'",gt:">",lt:"<",quot:'"'};function xr(e,t,n,i){return i?vr.call(wr,i)?wr[i]:"&"+i+";":gr(t||parseInt(n,16))}function Er(e){return e.length>3&&-1!==e.indexOf("&")?e.replace(br,xr):e}Object.keys(wr).forEach((function(e){wr[e.toUpperCase()]=wr[e]}));var _r="xsi:type",Sr="non-whitespace outside of root node";function Ar(e){return new Error(e)}function Cr(e){return"missing namespace for prefix <"+e+">"}function Rr(e){return{get:e,enumerable:!0}}function Pr(e){var t,n={};for(t in e)n[t]=e[t];return n}function kr(e){return e+"$uri"}function Mr(){return{line:0,column:0}}function Tr(e){throw e}function Nr(e){if(!this)return new Nr(e);var t,n,i,r,o,a,s,l,p,c=e&&e.proxy,u=Tr,m=Mr,d=!1,h=!1,f=null,y=!1;function g(e){e instanceof Error||(e=Ar(e)),f=e,u(e,m)}function v(e){o&&(e instanceof Error||(e=Ar(e)),o(e,m))}this.on=function(e,p){if("function"!=typeof p)throw Ar("required args <name, cb>");switch(e){case"openTag":n=p;break;case"text":t=p;break;case"closeTag":i=p;break;case"error":u=p;break;case"warn":o=p;break;case"cdata":r=p;break;case"attention":l=p;break;case"question":s=p;break;case"comment":a=p;break;default:throw Ar("unsupported event: "+e)}return this},this.ns=function(e){if(void 0===e&&(e={}),"object"!=typeof e)throw Ar("required args <nsMap={}>");var t,n={};for(t in e)n[t]=e[t];return n["http://www.w3.org/2001/XMLSchema-instance"]="xsi",h=!0,p=n,this},this.parse=function(e){if("string"!=typeof e)throw Ar("required args <xml=string>");return f=null,function(e){var o,u,f,b,w,x,E,_,S,A,C,R=h?[]:null,P=h?function(e){var t,n,i={};for(t in e)i[n=e[t]]=n,i[kr(n)]=t;return i}(p):null,k=[],M=0,T=!1,N=!1,D=0,O=0,I="",L=0;function B(){if(null!==C)return C;var e,t,n,i,r,o,a,s,l,c,u,m=h&&P.xmlns,f=h&&d?[]:null,y=L,g=I,b=g.length,w={},x={};e:for(;y<b;y++)if(l=!1,!(32===(c=g.charCodeAt(y))||c<14&&c>8)){for((c<65||c>122||c>90&&c<97)&&95!==c&&58!==c&&(v("illegal first char attribute name"),l=!0),u=y+1;u<b;u++)if(!((c=g.charCodeAt(u))>96&&c<123||c>64&&c<91||c>47&&c<59||46===c||45===c||95===c)){if(32===c||c<14&&c>8){v("missing attribute value"),y=u;continue e}if(61===c)break;v("illegal attribute name char"),l=!0}if("xmlns:xmlns"===(s=g.substring(y,u))&&(v("illegal declaration of xmlns"),l=!0),34===(c=g.charCodeAt(u+1)))-1===(u=g.indexOf('"',y=u+2))&&-1!==(u=g.indexOf("'",y))&&(v("attribute value quote missmatch"),l=!0);else if(39===c)-1===(u=g.indexOf("'",y=u+2))&&-1!==(u=g.indexOf('"',y))&&(v("attribute value quote missmatch"),l=!0);else for(v("missing attribute value quotes"),l=!0,u+=1;u<b&&!(32===(c=g.charCodeAt(u+1))||c<14&&c>8);u++);for(-1===u&&(v("missing closing quotes"),u=b,l=!0),l||(o=g.substring(y,u)),y=u;u+1<b&&!(32===(c=g.charCodeAt(u+1))||c<14&&c>8);u++)y===u&&(v("illegal character after attribute end"),l=!0);if(y=u+1,!l)if(s in x)v("attribute <"+s+"> already defined");else if(x[s]=!0,h)if(d){if(null!==(r="xmlns"===s?"xmlns":120===s.charCodeAt(0)&&"xmlns:"===s.substr(0,6)?s.substr(6):null)){if(e=Er(o),t=kr(r),!(a=p[e])){if("xmlns"===r||t in P&&P[t]!==e)do{a="ns"+M++}while(void 0!==P[a]);else a=r;p[e]=a}P[r]!==a&&(i||(P=Pr(P),i=!0),P[r]=a,"xmlns"===r&&(P[kr(a)]=e,m=a),P[t]=e),w[s]=o;continue}f.push(s,o)}else-1!==(c=s.indexOf(":"))?(n=P[s.substring(0,c)])?((s=m===n?s.substr(c+1):n+s.substr(c))===_r&&(-1!==(c=o.indexOf(":"))?(n=o.substring(0,c),o=(n=P[n]||n)+o.substring(c)):o=m+":"+o),w[s]=o):v(Cr(s.substring(0,c))):w[s]=o;else w[s]=o}if(d)for(y=0,b=f.length;y<b;y++){if(s=f[y++],o=f[y],-1!==(c=s.indexOf(":"))){if(!(n=P[s.substring(0,c)])){v(Cr(s.substring(0,c)));continue}(s=m===n?s.substr(c+1):n+s.substr(c))===_r&&(-1!==(c=o.indexOf(":"))?(n=o.substring(0,c),o=(n=P[n]||n)+o.substring(c)):o=m+":"+o)}w[s]=o}return C=w}function F(){for(var t,n,i=/(\r\n|\r|\n)/g,r=0,o=0,a=0,s=O;D>=a&&(t=i.exec(e))&&!((s=t[0].length+t.index)>D);)r+=1,a=s;return-1==D?(o=s,n=e.substring(O)):0===O?n=e.substring(O,D):(o=D-a,n=-1==O?e.substring(D):e.substring(D,O+1)),{data:n,line:r,column:o}}for(m=F,c&&(A=Object.create({},{name:Rr((function(){return _})),originalName:Rr((function(){return S})),attrs:Rr(B),ns:Rr((function(){return P}))}));-1!==O;){if(-1===(D=60===e.charCodeAt(O)?O:e.indexOf("<",O)))return k.length?g("unexpected end of file"):0===O?g("missing start tag"):void(O<e.length&&e.substring(O).trim()&&v(Sr));if(O!==D)if(k.length){if(t&&(t(e.substring(O,D),Er,m),y))return}else if(e.substring(O,D).trim()&&(v(Sr),y))return;if(33===(w=e.charCodeAt(D+1))){if(91===(b=e.charCodeAt(D+2))&&"CDATA["===e.substr(D+3,6)){if(-1===(O=e.indexOf("]]>",D)))return g("unclosed cdata");if(r&&(r(e.substring(D+9,O),m),y))return;O+=3;continue}if(45===b&&45===e.charCodeAt(D+3)){if(-1===(O=e.indexOf("--\x3e",D)))return g("unclosed comment");if(a&&(a(e.substring(D+4,O),Er,m),y))return;O+=3;continue}}if(63!==w){for(u=D+1;;u++){if(x=e.charCodeAt(u),isNaN(x))return O=-1,g("unclosed tag");if(34===x)u=-1!==(b=e.indexOf('"',u+1))?b:u;else if(39===x)u=-1!==(b=e.indexOf("'",u+1))?b:u;else if(62===x){O=u;break}}if(33!==w){if(C={},47===w){if(T=!1,N=!0,!k.length)return g("missing open tag");if(u=_=k.pop(),b=D+2+u.length,e.substring(D+2,b)!==u)return g("closing tag mismatch");for(;b<O;b++)if(!(32===(w=e.charCodeAt(b))||w>8&&w<14))return g("close tag")}else{if(47===e.charCodeAt(O-1)?(u=_=e.substring(D+1,O-1),T=!0,N=!0):(u=_=e.substring(D+1,O),T=!0,N=!1),!(w>96&&w<123||w>64&&w<91||95===w||58===w))return g("illegal first char nodeName");for(b=1,f=u.length;b<f;b++)if(!((w=u.charCodeAt(b))>96&&w<123||w>64&&w<91||w>47&&w<59||45===w||95===w||46==w)){if(32===w||w<14&&w>8){_=u.substring(0,b),C=null;break}return g("invalid nodeName")}N||k.push(_)}if(h){if(o=P,T&&(N||R.push(o),null===C&&(d=-1!==u.indexOf("xmlns",b))&&(L=b,I=u,B(),d=!1)),S=_,-1!==(w=_.indexOf(":"))){if(!(E=P[_.substring(0,w)]))return g("missing namespace on <"+S+">");_=_.substr(w+1)}else E=P.xmlns;E&&(_=E+":"+_)}if(T&&(L=b,I=u,n&&(c?n(A,Er,N,m):n(_,B,Er,N,m),y)))return;if(N){if(i&&(i(c?A:_,Er,T,m),y))return;h&&(P=T?o:R.pop())}O+=1}else{if(l&&(l(e.substring(D,O+1),Er,m),y))return;O+=1}}else{if(-1===(O=e.indexOf("?>",D)))return g("unclosed question");if(s&&(s(e.substring(D,O+2),m),y))return;O+=2}}}(e),m=Mr,y=!1,f},this.stop=function(){y=!0}}function Dr(e){return e.xml&&"lowerCase"===e.xml.tagAlias}var Or={xsi:"http://www.w3.org/2001/XMLSchema-instance",xml:"http://www.w3.org/XML/1998/namespace"},Ir="xsi:type";function Lr(e){return e.xml&&e.xml.serialize}function Br(e){return Lr(e)===Ir}function Fr(e,t){return Dr(t)?e.prefix+":"+((n=e.localName).charAt(0).toUpperCase()+n.slice(1)):e.name;var n}function jr(e){return new Error(e)}function Vr(e){return e.$descriptor}function $r(e){ge(this,e),this.elementsById={},this.references=[],this.warnings=[],this.addReference=function(e){this.references.push(e)},this.addElement=function(e){if(!e)throw jr("expected element");var t,n=this.elementsById,i=Vr(e).idProperty;if(i&&(t=e.get(i.name))){if(!/^([a-z][\w-.]*:)?[a-z_][\w-.]*$/i.test(t))throw new Error("illegal ID <"+t+">");if(n[t])throw jr("duplicate ID <"+t+">");n[t]=e}},this.addWarning=function(e){this.warnings.push(e)}}function zr(){}function Ur(){}function Wr(){}function Kr(e,t){this.property=e,this.context=t}function Gr(e,t){this.element=t,this.propertyDesc=e}function Hr(){}function qr(e,t,n){this.model=e,this.type=e.getType(t),this.context=n}function Yr(e,t,n){qr.call(this,e,t,n)}function Xr(e,t,n){this.model=e,this.context=n}function Jr(e){e instanceof yr&&(e={model:e}),ge(this,{lax:!1},e)}zr.prototype.handleEnd=function(){},zr.prototype.handleText=function(){},zr.prototype.handleNode=function(){},Ur.prototype=Object.create(zr.prototype),Ur.prototype.handleNode=function(){return this},Wr.prototype=Object.create(zr.prototype),Wr.prototype.handleText=function(e){this.body=(this.body||"")+e},Kr.prototype=Object.create(Wr.prototype),Kr.prototype.handleNode=function(e){if(this.element)throw jr("expected no sub nodes");return this.element=this.createReference(e),this},Kr.prototype.handleEnd=function(){this.element.id=this.body},Kr.prototype.createReference=function(e){return{property:this.property.ns.name,id:""}},Gr.prototype=Object.create(Wr.prototype),Gr.prototype.handleEnd=function(){var e=this.body||"",t=this.element,n=this.propertyDesc;e=ar(n.type,e),n.isMany?t.get(n.name).push(e):t.set(n.name,e)},Hr.prototype=Object.create(Wr.prototype),Hr.prototype.handleNode=function(e){var t=this,n=this.element;return n?t=this.handleChild(e):(n=this.element=this.createElement(e),this.context.addElement(n)),t},qr.prototype=Object.create(Hr.prototype),qr.prototype.addReference=function(e){this.context.addReference(e)},qr.prototype.handleText=function(e){if(!Vr(this.element).bodyProperty)throw jr("unexpected body text <"+e+">");Wr.prototype.handleText.call(this,e)},qr.prototype.handleEnd=function(){var e=this.body,t=this.element,n=Vr(t).bodyProperty;n&&void 0!==e&&(e=ar(n.type,e),t.set(n.name,e))},qr.prototype.createElement=function(e){var t,n=e.attributes,i=this.type,r=Vr(i),o=this.context,a=new i({}),s=this.model;return le(n,(function(e,n){var i=r.propertiesByName[n];i&&i.isReference?i.isMany?le(e.split(" "),(function(e){o.addReference({element:a,property:i.ns.name,id:e})})):o.addReference({element:a,property:i.ns.name,id:e}):(i?e=ar(i.type,e):"xmlns"!==n&&(t=pr(n,r.ns.prefix),s.getPackage(t.prefix)&&o.addWarning({message:"unknown attribute <"+n+">",element:a,property:n,value:e})),a.set(n,e))})),a},qr.prototype.getPropertyForNode=function(e){var t,n,i=pr(e.name),r=this.type,o=this.model,a=Vr(r),s=i.name,l=a.propertiesByName[s];if(l&&!l.isAttr)return Br(l)&&(t=e.attributes[Ir])?(t=function(e,t){var n=pr(e);return function(e,t){var n=e.name,i=e.localName,r=t.xml&&t.xml.typePrefix;return r&&0===i.indexOf(r)?e.prefix+":"+i.slice(r.length):n}(n,t.getPackage(n.prefix))}(t,o),ge({},l,{effectiveType:Vr(n=o.getType(t)).name})):l;var p=o.getPackage(i.prefix);if(p){if(t=Fr(i,p),n=o.getType(t),l=ae(a.properties,(function(e){return!e.isVirtual&&!e.isReference&&!e.isAttribute&&n.hasType(e.type)})))return ge({},l,{effectiveType:Vr(n).name})}else if(l=ae(a.properties,(function(e){return!e.isReference&&!e.isAttribute&&"Element"===e.type})))return l;throw jr("unrecognized element <"+i.name+">")},qr.prototype.toString=function(){return"ElementDescriptor["+Vr(this.type).name+"]"},qr.prototype.valueHandler=function(e,t){return new Gr(e,t)},qr.prototype.referenceHandler=function(e){return new Kr(e,this.context)},qr.prototype.handler=function(e){return"Element"===e?new Xr(this.model,e,this.context):new qr(this.model,e,this.context)},qr.prototype.handleChild=function(e){var t,n,i,r;if(t=this.getPropertyForNode(e),i=this.element,lr(n=t.effectiveType||t.type))return this.valueHandler(t,i);var o=(r=t.isReference?this.referenceHandler(t).handleNode(e):this.handler(n).handleNode(e)).element;return void 0!==o&&(t.isMany?i.get(t.name).push(o):i.set(t.name,o),t.isReference?(ge(o,{element:i}),this.context.addReference(o)):o.$parent=i),r},Yr.prototype=Object.create(qr.prototype),Yr.prototype.createElement=function(e){var t=e.name,n=pr(t),i=this.model,r=this.type,o=i.getPackage(n.prefix),a=o&&Fr(n,o)||t;if(!r.hasType(a))throw jr("unexpected element <"+e.originalName+">");return qr.prototype.createElement.call(this,e)},Xr.prototype=Object.create(Hr.prototype),Xr.prototype.createElement=function(e){var t=e.name,n=pr(t).prefix,i=e.ns[n+"$uri"],r=e.attributes;return this.model.createAny(t,i,r)},Xr.prototype.handleChild=function(e){var t=new Xr(this.model,"Element",this.context).handleNode(e),n=this.element,i=t.element;return void 0!==i&&((n.$children=n.$children||[]).push(i),i.$parent=n),t},Xr.prototype.handleEnd=function(){this.body&&(this.element.$body=this.body)},Jr.prototype.fromXML=function(e,t,n){var i=t.rootHandler;t instanceof qr?(i=t,t={}):"string"==typeof t?(i=this.handler(t),t={}):"string"==typeof i&&(i=this.handler(i));var r=this.model,o=this.lax,a=new $r(ge({},t,{rootHandler:i})),s=new Nr({proxy:!0}),l=function(){var e=[];return Object.defineProperty(e,"peek",{value:function(){return this[this.length-1]}}),e}();function p(e,t,n){var i=t(),r=i.line,o=i.column,s=i.data;"<"===s.charAt(0)&&-1!==s.indexOf(" ")&&(s=s.slice(0,s.indexOf(" "))+">");var l="unparsable content "+(s?s+" ":"")+"detected\n\tline: "+r+"\n\tcolumn: "+o+"\n\tnested error: "+e.message;if(n)return a.addWarning({message:l,error:e}),!0;throw jr(l)}function c(e,t){return p(e,t,!0)}i.context=a,l.push(i);var u=/^<\?xml /i,m=/ encoding="([^"]+)"/i,d=/^utf-8$/i;function h(e,t){try{l.peek().handleText(e)}catch(n){c(n,t)}}var f=r.getPackages().reduce((function(e,t){return e[t.uri]=t.prefix,e}),{"http://www.w3.org/XML/1998/namespace":"xml"});return s.ns(f).on("openTag",(function(e,t,n,i){var r=e.attrs||{},a=Object.keys(r).reduce((function(e,n){var i=t(r[n]);return e[n]=i,e}),{});!function(e,t){var n=l.peek();try{l.push(n.handleNode(e))}catch(i){p(i,t,o)&&l.push(new Ur)}}({name:e.name,originalName:e.originalName,attributes:a,ns:e.ns},i)})).on("question",(function(e){if(u.test(e)){var t=m.exec(e),n=t&&t[1];n&&!d.test(n)&&a.addWarning({message:"unsupported document encoding <"+n+">, falling back to UTF-8"})}})).on("closeTag",(function(){l.pop().handleEnd()})).on("cdata",h).on("text",(function(e,t,n){!function(e,t){e.trim()&&h(e,t)}(t(e),n)})).on("error",p).on("warn",c),new Promise((function(t,n){var r;try{s.parse(e),function(){var e,t,n=a.elementsById,i=a.references;for(e=0;t=i[e];e++){var r=t.element,o=n[t.id],s=Vr(r).propertiesByName[t.property];if(o||a.addWarning({message:"unresolved reference <"+t.id+">",element:t.element,property:t.property,value:t.id}),s.isMany){var l=r.get(s.name),p=l.indexOf(t);-1===p&&(p=l.length),o?l[p]=o:l.splice(p,1)}else r.set(s.name,o)}}()}catch(q){r=q}var o=i.element;r||o||(r=jr("failed to parse document as <"+i.type.$descriptor.name+">"));var l=a.warnings,p=a.references,c=a.elementsById;return r?(r.warnings=l,n(r)):t({rootElement:o,elementsById:c,references:p,warnings:l})}))},Jr.prototype.handler=function(e){return new Yr(this.model,e)};var Qr=/<|>|'|"|&|\n\r|\n/g,Zr=/<|>|&/g;function eo(e){var t={},n={},i={},r=[],o=[];this.byUri=function(t){return n[t]||e&&e.byUri(t)},this.add=function(e,t){n[e.uri]=e,t?r.push(e):o.push(e),this.mapPrefix(e.prefix,e.uri)},this.uriByPrefix=function(e){return t[e||"xmlns"]},this.mapPrefix=function(e,n){t[e||"xmlns"]=n},this.getNSKey=function(e){return void 0!==e.prefix?e.uri+"|"+e.prefix:e.uri},this.logUsed=function(t){var n=t.uri,r=this.getNSKey(t);i[r]=this.byUri(n),e&&e.logUsed(t)},this.getUsed=function(e){var t=this;return[].concat(r,o).filter((function(e){var n=t.getNSKey(e);return i[n]}))}}function to(e,t){return Dr(t)?(n=e).charAt(0).toLowerCase()+n.slice(1):e;var n}function no(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}function io(e){return re(e)?e:(e.prefix?e.prefix+":":"")+e.localName}var ro={"\n":"#10","\n\r":"#10",'"':"#34","'":"#39","<":"#60",">":"#62","&":"#38"},oo={"<":"lt",">":"gt","&":"amp"};function ao(e,t,n){return(e=re(e)?e:""+e).replace(t,(function(e){return"&"+n[e]+";"}))}function so(e){this.tagName=e}function lo(){}function po(e){this.tagName=e}function co(e,t){this.body=[],this.attrs=[],this.parent=e,this.propertyDescriptor=t}function uo(e,t){co.call(this,e,t)}function mo(){this.value="",this.write=function(e){this.value+=e}}function ho(e,t){var n=[""];this.append=function(t){return e.write(t),this},this.appendNewLine=function(){return t&&e.write("\n"),this},this.appendIndent=function(){return t&&e.write(n.join("  ")),this},this.indent=function(){return n.push(""),this},this.unindent=function(){return n.pop(),this}}function fo(e){return e=ge({format:!1,preamble:!0},e||{}),{toXML:function(t,n){var i=n||new mo,r=new ho(i,e.format);if(e.preamble&&r.append('<?xml version="1.0" encoding="UTF-8"?>\n'),(new co).build(t).serializeTo(r),!n)return i.value}}}function yo(e,t){yr.call(this,e,t)}so.prototype.build=function(e){return this.element=e,this},so.prototype.serializeTo=function(e){e.appendIndent().append("<"+this.tagName+">"+this.element.id+"</"+this.tagName+">").appendNewLine()},lo.prototype.serializeValue=lo.prototype.serializeTo=function(e){e.append(this.escape?ao(this.value,Zr,oo):this.value)},lo.prototype.build=function(e,t){return this.value=t,"String"===e.type&&-1!==t.search(Zr)&&(this.escape=!0),this},no(po,lo),po.prototype.serializeTo=function(e){e.appendIndent().append("<"+this.tagName+">"),this.serializeValue(e),e.append("</"+this.tagName+">").appendNewLine()},co.prototype.build=function(e){this.element=e;var t,n,i=e.$descriptor,r=this.propertyDescriptor,o=i.isGeneric;return t=o?this.parseGeneric(e):this.parseNsAttributes(e),this.ns=r?this.nsPropertyTagName(r):this.nsTagName(i),this.tagName=this.addTagName(this.ns),o||(n=function(e){return se(e.$descriptor.properties,(function(t){var n=t.name;if(t.isVirtual)return!1;if(!oe(e,n))return!1;var i=e[n];return i!==t.default&&null!==i&&(!t.isMany||i.length)}))}(e),this.parseAttributes(se(n,(function(e){return e.isAttr}))),this.parseContainments(function(e){return se(e,(function(e){return!e.isAttr}))}(n))),this.parseGenericAttributes(e,t),this},co.prototype.nsTagName=function(e){return function(e,t){return t.isGeneric?ge({localName:t.ns.localName},e):ge({localName:to(t.ns.localName,t.$pkg)},e)}(this.logNamespaceUsed(e.ns),e)},co.prototype.nsPropertyTagName=function(e){return function(e,t){return ge({localName:t.ns.localName},e)}(this.logNamespaceUsed(e.ns),e)},co.prototype.isLocalNs=function(e){return e.uri===this.ns.uri},co.prototype.nsAttributeName=function(e){var t;if(t=re(e)?pr(e):e.ns,e.inherited)return{localName:t.localName};var n=this.logNamespaceUsed(t);return this.getNamespaces().logUsed(n),this.isLocalNs(n)?{localName:t.localName}:ge({localName:t.localName},n)},co.prototype.parseGeneric=function(e){var t=this,n=this.body,i=[];return le(e,(function(r,o){"$body"===o?n.push((new lo).build({type:"String"},r)):"$children"===o?le(r,(function(e){n.push(new co(t).build(e))})):0!==o.indexOf("$")&&t.parseNsAttribute(e,o,r)&&i.push({name:o,value:r})})),i},co.prototype.parseNsAttribute=function(e,t,n){var i,r=e.$model,o=pr(t);if("xmlns"===o.prefix&&(i={prefix:o.localName,uri:n}),o.prefix||"xmlns"!==o.localName||(i={uri:n}),!i)return{name:t,value:n};if(r&&r.getPackage(n))this.logNamespace(i,!0,!0);else{var a=this.logNamespaceUsed(i,!0);this.getNamespaces().logUsed(a)}},co.prototype.parseNsAttributes=function(e,t){var n=this,i=e.$attrs,r=[];return le(i,(function(t,i){var o=n.parseNsAttribute(e,i,t);o&&r.push(o)})),r},co.prototype.parseGenericAttributes=function(e,t){var n=this;le(t,(function(t){if(t.name!==Ir)try{n.addAttribute(n.nsAttributeName(t.name),t.value)}catch(q){console.warn("missing namespace information for ",t.name,"=",t.value,"on",e,q)}}))},co.prototype.parseContainments=function(e){var t=this,n=this.body,i=this.element;le(e,(function(e){var r=i.get(e.name),o=e.isReference;if(e.isMany||(r=[r]),e.isBody)n.push((new lo).build(e,r[0]));else if(lr(e.type))le(r,(function(i){n.push(new po(t.addTagName(t.nsPropertyTagName(e))).build(e,i))}));else if(o)le(r,(function(i){n.push(new so(t.addTagName(t.nsPropertyTagName(e))).build(i))}));else{var a=Br(e),s=function(e){return"property"===Lr(e)}(e);le(r,(function(i){var r;r=a?new uo(t,e):s?new co(t,e):new co(t),n.push(r.build(i))}))}}))},co.prototype.getNamespaces=function(e){var t,n=this.namespaces,i=this.parent;return n||(t=i&&i.getNamespaces(),e||!t?this.namespaces=n=new eo(t):n=t),n},co.prototype.logNamespace=function(e,t,n){var i=this.getNamespaces(n),r=e.uri,o=e.prefix;return i.byUri(r)&&!n||i.add(e,t),i.mapPrefix(o,r),e},co.prototype.logNamespaceUsed=function(e,t){var n,i,r,o=this.element.$model,a=this.getNamespaces(t),s=e.prefix,l=e.uri;if(!s&&!l)return{localName:e.localName};if(r=Or[s]||o&&(o.getPackage(s)||{}).uri,!(l=l||r||a.uriByPrefix(s)))throw new Error("no namespace uri given for prefix <"+s+">");if(!(e=a.byUri(l))){for(n=s,i=1;a.uriByPrefix(n);)n=s+"_"+i++;e=this.logNamespace({prefix:n,uri:l},r===l)}return s&&a.mapPrefix(s,l),e},co.prototype.parseAttributes=function(e){var t=this,n=this.element;le(e,(function(e){var i=n.get(e.name);if(e.isReference)if(e.isMany){var r=[];le(i,(function(e){r.push(e.id)})),i=r.join(" ")}else i=i.id;t.addAttribute(t.nsAttributeName(e),i)}))},co.prototype.addTagName=function(e){var t=this.logNamespaceUsed(e);return this.getNamespaces().logUsed(t),io(e)},co.prototype.addAttribute=function(e,t){var n=this.attrs;re(t)&&(t=ao(t,Qr,ro));var i=function(e,t){const n=de(t);let i=ee(e)?-1:void 0;return le(e,(function(e,t){if(n(e,t))return i=t,!1})),i}(n,(function(t){return t.name.localName===e.localName&&t.name.uri===e.uri&&t.name.prefix===e.prefix})),r={name:e,value:t};-1!==i?n.splice(i,1,r):n.push(r)},co.prototype.serializeAttributes=function(e){var t=this.attrs,n=this.namespaces;n&&(t=function(e){return e.getUsed().filter((function(e){return"xml"!==e.prefix})).map((function(e){return{name:"xmlns"+(e.prefix?":"+e.prefix:""),value:e.uri}}))}(n).concat(t)),le(t,(function(t){e.append(" ").append(io(t.name)).append('="').append(t.value).append('"')}))},co.prototype.serializeTo=function(e){var t=this.body[0],n=t&&t.constructor!==lo;e.appendIndent().append("<"+this.tagName),this.serializeAttributes(e),e.append(t?">":" />"),t&&(n&&e.appendNewLine().indent(),le(this.body,(function(t){t.serializeTo(e)})),n&&e.unindent().appendIndent(),e.append("</"+this.tagName+">")),e.appendNewLine()},no(uo,co),uo.prototype.parseNsAttributes=function(e){var t=co.prototype.parseNsAttributes.call(this,e),n=e.$descriptor;if(n.name===this.propertyDescriptor.type)return t;var i=this.typeNs=this.nsTagName(n);this.getNamespaces().logUsed(this.typeNs);var r=e.$model.getPackage(i.uri),o=r.xml&&r.xml.typePrefix||"";return this.addAttribute(this.nsAttributeName(Ir),(i.prefix?i.prefix+":":"")+o+n.ns.localName),t},uo.prototype.isLocalNs=function(e){return e.uri===(this.typeNs||this.ns).uri},yo.prototype=Object.create(yr.prototype),yo.prototype.fromXML=function(e,t,n){re(t)||(n=t,t="bpmn:Definitions");var i=new Jr(ge({model:this,lax:!0},n)),r=i.handler(t);return i.fromXML(e,r)},yo.prototype.toXML=function(e,t){var n=new fo(t);return new Promise((function(t,i){try{return t({xml:n.toXML(e)})}catch(r){return i(r)}}))};var go={bpmn:{name:"BPMN20",uri:"http://www.omg.org/spec/BPMN/20100524/MODEL",prefix:"bpmn",associations:[],types:[{name:"Interface",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"operations",type:"Operation",isMany:!0},{name:"implementationRef",isAttr:!0,type:"String"}]},{name:"Operation",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"inMessageRef",type:"Message",isReference:!0},{name:"outMessageRef",type:"Message",isReference:!0},{name:"errorRef",type:"Error",isMany:!0,isReference:!0},{name:"implementationRef",isAttr:!0,type:"String"}]},{name:"EndPoint",superClass:["RootElement"]},{name:"Auditing",superClass:["BaseElement"]},{name:"GlobalTask",superClass:["CallableElement"],properties:[{name:"resources",type:"ResourceRole",isMany:!0}]},{name:"Monitoring",superClass:["BaseElement"]},{name:"Performer",superClass:["ResourceRole"]},{name:"Process",superClass:["FlowElementsContainer","CallableElement"],properties:[{name:"processType",type:"ProcessType",isAttr:!0},{name:"isClosed",isAttr:!0,type:"Boolean"},{name:"auditing",type:"Auditing"},{name:"monitoring",type:"Monitoring"},{name:"properties",type:"Property",isMany:!0},{name:"laneSets",isMany:!0,replaces:"FlowElementsContainer#laneSets",type:"LaneSet"},{name:"flowElements",isMany:!0,replaces:"FlowElementsContainer#flowElements",type:"FlowElement"},{name:"artifacts",type:"Artifact",isMany:!0},{name:"resources",type:"ResourceRole",isMany:!0},{name:"correlationSubscriptions",type:"CorrelationSubscription",isMany:!0},{name:"supports",type:"Process",isMany:!0,isReference:!0},{name:"definitionalCollaborationRef",type:"Collaboration",isAttr:!0,isReference:!0},{name:"isExecutable",isAttr:!0,type:"Boolean"}]},{name:"LaneSet",superClass:["BaseElement"],properties:[{name:"lanes",type:"Lane",isMany:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Lane",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"partitionElementRef",type:"BaseElement",isAttr:!0,isReference:!0},{name:"partitionElement",type:"BaseElement"},{name:"flowNodeRef",type:"FlowNode",isMany:!0,isReference:!0},{name:"childLaneSet",type:"LaneSet",xml:{serialize:"xsi:type"}}]},{name:"GlobalManualTask",superClass:["GlobalTask"]},{name:"ManualTask",superClass:["Task"]},{name:"UserTask",superClass:["Task"],properties:[{name:"renderings",type:"Rendering",isMany:!0},{name:"implementation",isAttr:!0,type:"String"}]},{name:"Rendering",superClass:["BaseElement"]},{name:"HumanPerformer",superClass:["Performer"]},{name:"PotentialOwner",superClass:["HumanPerformer"]},{name:"GlobalUserTask",superClass:["GlobalTask"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"renderings",type:"Rendering",isMany:!0}]},{name:"Gateway",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"gatewayDirection",type:"GatewayDirection",default:"Unspecified",isAttr:!0}]},{name:"EventBasedGateway",superClass:["Gateway"],properties:[{name:"instantiate",default:!1,isAttr:!0,type:"Boolean"},{name:"eventGatewayType",type:"EventBasedGatewayType",isAttr:!0,default:"Exclusive"}]},{name:"ComplexGateway",superClass:["Gateway"],properties:[{name:"activationCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"ExclusiveGateway",superClass:["Gateway"],properties:[{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"InclusiveGateway",superClass:["Gateway"],properties:[{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"ParallelGateway",superClass:["Gateway"]},{name:"RootElement",isAbstract:!0,superClass:["BaseElement"]},{name:"Relationship",superClass:["BaseElement"],properties:[{name:"type",isAttr:!0,type:"String"},{name:"direction",type:"RelationshipDirection",isAttr:!0},{name:"source",isMany:!0,isReference:!0,type:"Element"},{name:"target",isMany:!0,isReference:!0,type:"Element"}]},{name:"BaseElement",isAbstract:!0,properties:[{name:"id",isAttr:!0,type:"String",isId:!0},{name:"documentation",type:"Documentation",isMany:!0},{name:"extensionDefinitions",type:"ExtensionDefinition",isMany:!0,isReference:!0},{name:"extensionElements",type:"ExtensionElements"}]},{name:"Extension",properties:[{name:"mustUnderstand",default:!1,isAttr:!0,type:"Boolean"},{name:"definition",type:"ExtensionDefinition",isAttr:!0,isReference:!0}]},{name:"ExtensionDefinition",properties:[{name:"name",isAttr:!0,type:"String"},{name:"extensionAttributeDefinitions",type:"ExtensionAttributeDefinition",isMany:!0}]},{name:"ExtensionAttributeDefinition",properties:[{name:"name",isAttr:!0,type:"String"},{name:"type",isAttr:!0,type:"String"},{name:"isReference",default:!1,isAttr:!0,type:"Boolean"},{name:"extensionDefinition",type:"ExtensionDefinition",isAttr:!0,isReference:!0}]},{name:"ExtensionElements",properties:[{name:"valueRef",isAttr:!0,isReference:!0,type:"Element"},{name:"values",type:"Element",isMany:!0},{name:"extensionAttributeDefinition",type:"ExtensionAttributeDefinition",isAttr:!0,isReference:!0}]},{name:"Documentation",superClass:["BaseElement"],properties:[{name:"text",type:"String",isBody:!0},{name:"textFormat",default:"text/plain",isAttr:!0,type:"String"}]},{name:"Event",isAbstract:!0,superClass:["FlowNode","InteractionNode"],properties:[{name:"properties",type:"Property",isMany:!0}]},{name:"IntermediateCatchEvent",superClass:["CatchEvent"]},{name:"IntermediateThrowEvent",superClass:["ThrowEvent"]},{name:"EndEvent",superClass:["ThrowEvent"]},{name:"StartEvent",superClass:["CatchEvent"],properties:[{name:"isInterrupting",default:!0,isAttr:!0,type:"Boolean"}]},{name:"ThrowEvent",isAbstract:!0,superClass:["Event"],properties:[{name:"dataInputs",type:"DataInput",isMany:!0},{name:"dataInputAssociations",type:"DataInputAssociation",isMany:!0},{name:"inputSet",type:"InputSet"},{name:"eventDefinitions",type:"EventDefinition",isMany:!0},{name:"eventDefinitionRef",type:"EventDefinition",isMany:!0,isReference:!0}]},{name:"CatchEvent",isAbstract:!0,superClass:["Event"],properties:[{name:"parallelMultiple",isAttr:!0,type:"Boolean",default:!1},{name:"dataOutputs",type:"DataOutput",isMany:!0},{name:"dataOutputAssociations",type:"DataOutputAssociation",isMany:!0},{name:"outputSet",type:"OutputSet"},{name:"eventDefinitions",type:"EventDefinition",isMany:!0},{name:"eventDefinitionRef",type:"EventDefinition",isMany:!0,isReference:!0}]},{name:"BoundaryEvent",superClass:["CatchEvent"],properties:[{name:"cancelActivity",default:!0,isAttr:!0,type:"Boolean"},{name:"attachedToRef",type:"Activity",isAttr:!0,isReference:!0}]},{name:"EventDefinition",isAbstract:!0,superClass:["RootElement"]},{name:"CancelEventDefinition",superClass:["EventDefinition"]},{name:"ErrorEventDefinition",superClass:["EventDefinition"],properties:[{name:"errorRef",type:"Error",isAttr:!0,isReference:!0}]},{name:"TerminateEventDefinition",superClass:["EventDefinition"]},{name:"EscalationEventDefinition",superClass:["EventDefinition"],properties:[{name:"escalationRef",type:"Escalation",isAttr:!0,isReference:!0}]},{name:"Escalation",properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"escalationCode",isAttr:!0,type:"String"}],superClass:["RootElement"]},{name:"CompensateEventDefinition",superClass:["EventDefinition"],properties:[{name:"waitForCompletion",isAttr:!0,type:"Boolean",default:!0},{name:"activityRef",type:"Activity",isAttr:!0,isReference:!0}]},{name:"TimerEventDefinition",superClass:["EventDefinition"],properties:[{name:"timeDate",type:"Expression",xml:{serialize:"xsi:type"}},{name:"timeCycle",type:"Expression",xml:{serialize:"xsi:type"}},{name:"timeDuration",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"LinkEventDefinition",superClass:["EventDefinition"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"target",type:"LinkEventDefinition",isReference:!0},{name:"source",type:"LinkEventDefinition",isMany:!0,isReference:!0}]},{name:"MessageEventDefinition",superClass:["EventDefinition"],properties:[{name:"messageRef",type:"Message",isAttr:!0,isReference:!0},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"ConditionalEventDefinition",superClass:["EventDefinition"],properties:[{name:"condition",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"SignalEventDefinition",superClass:["EventDefinition"],properties:[{name:"signalRef",type:"Signal",isAttr:!0,isReference:!0}]},{name:"Signal",superClass:["RootElement"],properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"ImplicitThrowEvent",superClass:["ThrowEvent"]},{name:"DataState",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"}]},{name:"ItemAwareElement",superClass:["BaseElement"],properties:[{name:"itemSubjectRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"dataState",type:"DataState"}]},{name:"DataAssociation",superClass:["BaseElement"],properties:[{name:"sourceRef",type:"ItemAwareElement",isMany:!0,isReference:!0},{name:"targetRef",type:"ItemAwareElement",isReference:!0},{name:"transformation",type:"FormalExpression",xml:{serialize:"property"}},{name:"assignment",type:"Assignment",isMany:!0}]},{name:"DataInput",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isCollection",default:!1,isAttr:!0,type:"Boolean"},{name:"inputSetRef",type:"InputSet",isMany:!0,isVirtual:!0,isReference:!0},{name:"inputSetWithOptional",type:"InputSet",isMany:!0,isVirtual:!0,isReference:!0},{name:"inputSetWithWhileExecuting",type:"InputSet",isMany:!0,isVirtual:!0,isReference:!0}]},{name:"DataOutput",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isCollection",default:!1,isAttr:!0,type:"Boolean"},{name:"outputSetRef",type:"OutputSet",isMany:!0,isVirtual:!0,isReference:!0},{name:"outputSetWithOptional",type:"OutputSet",isMany:!0,isVirtual:!0,isReference:!0},{name:"outputSetWithWhileExecuting",type:"OutputSet",isMany:!0,isVirtual:!0,isReference:!0}]},{name:"InputSet",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"dataInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"optionalInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"whileExecutingInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"outputSetRefs",type:"OutputSet",isMany:!0,isReference:!0}]},{name:"OutputSet",superClass:["BaseElement"],properties:[{name:"dataOutputRefs",type:"DataOutput",isMany:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"inputSetRefs",type:"InputSet",isMany:!0,isReference:!0},{name:"optionalOutputRefs",type:"DataOutput",isMany:!0,isReference:!0},{name:"whileExecutingOutputRefs",type:"DataOutput",isMany:!0,isReference:!0}]},{name:"Property",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"}]},{name:"DataInputAssociation",superClass:["DataAssociation"]},{name:"DataOutputAssociation",superClass:["DataAssociation"]},{name:"InputOutputSpecification",superClass:["BaseElement"],properties:[{name:"dataInputs",type:"DataInput",isMany:!0},{name:"dataOutputs",type:"DataOutput",isMany:!0},{name:"inputSets",type:"InputSet",isMany:!0},{name:"outputSets",type:"OutputSet",isMany:!0}]},{name:"DataObject",superClass:["FlowElement","ItemAwareElement"],properties:[{name:"isCollection",default:!1,isAttr:!0,type:"Boolean"}]},{name:"InputOutputBinding",properties:[{name:"inputDataRef",type:"InputSet",isAttr:!0,isReference:!0},{name:"outputDataRef",type:"OutputSet",isAttr:!0,isReference:!0},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"Assignment",superClass:["BaseElement"],properties:[{name:"from",type:"Expression",xml:{serialize:"xsi:type"}},{name:"to",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"DataStore",superClass:["RootElement","ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"capacity",isAttr:!0,type:"Integer"},{name:"isUnlimited",default:!0,isAttr:!0,type:"Boolean"}]},{name:"DataStoreReference",superClass:["ItemAwareElement","FlowElement"],properties:[{name:"dataStoreRef",type:"DataStore",isAttr:!0,isReference:!0}]},{name:"DataObjectReference",superClass:["ItemAwareElement","FlowElement"],properties:[{name:"dataObjectRef",type:"DataObject",isAttr:!0,isReference:!0}]},{name:"ConversationLink",superClass:["BaseElement"],properties:[{name:"sourceRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"ConversationAssociation",superClass:["BaseElement"],properties:[{name:"innerConversationNodeRef",type:"ConversationNode",isAttr:!0,isReference:!0},{name:"outerConversationNodeRef",type:"ConversationNode",isAttr:!0,isReference:!0}]},{name:"CallConversation",superClass:["ConversationNode"],properties:[{name:"calledCollaborationRef",type:"Collaboration",isAttr:!0,isReference:!0},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0}]},{name:"Conversation",superClass:["ConversationNode"]},{name:"SubConversation",superClass:["ConversationNode"],properties:[{name:"conversationNodes",type:"ConversationNode",isMany:!0}]},{name:"ConversationNode",isAbstract:!0,superClass:["InteractionNode","BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRef",type:"Participant",isMany:!0,isReference:!0},{name:"messageFlowRefs",type:"MessageFlow",isMany:!0,isReference:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0}]},{name:"GlobalConversation",superClass:["Collaboration"]},{name:"PartnerEntity",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRef",type:"Participant",isMany:!0,isReference:!0}]},{name:"PartnerRole",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRef",type:"Participant",isMany:!0,isReference:!0}]},{name:"CorrelationProperty",superClass:["RootElement"],properties:[{name:"correlationPropertyRetrievalExpression",type:"CorrelationPropertyRetrievalExpression",isMany:!0},{name:"name",isAttr:!0,type:"String"},{name:"type",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"Error",superClass:["RootElement"],properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"errorCode",isAttr:!0,type:"String"}]},{name:"CorrelationKey",superClass:["BaseElement"],properties:[{name:"correlationPropertyRef",type:"CorrelationProperty",isMany:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Expression",superClass:["BaseElement"],isAbstract:!1,properties:[{name:"body",isBody:!0,type:"String"}]},{name:"FormalExpression",superClass:["Expression"],properties:[{name:"language",isAttr:!0,type:"String"},{name:"evaluatesToTypeRef",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"Message",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"itemRef",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"ItemDefinition",superClass:["RootElement"],properties:[{name:"itemKind",type:"ItemKind",isAttr:!0},{name:"structureRef",isAttr:!0,type:"String"},{name:"isCollection",default:!1,isAttr:!0,type:"Boolean"},{name:"import",type:"Import",isAttr:!0,isReference:!0}]},{name:"FlowElement",isAbstract:!0,superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"auditing",type:"Auditing"},{name:"monitoring",type:"Monitoring"},{name:"categoryValueRef",type:"CategoryValue",isMany:!0,isReference:!0}]},{name:"SequenceFlow",superClass:["FlowElement"],properties:[{name:"isImmediate",isAttr:!0,type:"Boolean"},{name:"conditionExpression",type:"Expression",xml:{serialize:"xsi:type"}},{name:"sourceRef",type:"FlowNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"FlowNode",isAttr:!0,isReference:!0}]},{name:"FlowElementsContainer",isAbstract:!0,superClass:["BaseElement"],properties:[{name:"laneSets",type:"LaneSet",isMany:!0},{name:"flowElements",type:"FlowElement",isMany:!0}]},{name:"CallableElement",isAbstract:!0,superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"ioSpecification",type:"InputOutputSpecification",xml:{serialize:"property"}},{name:"supportedInterfaceRef",type:"Interface",isMany:!0,isReference:!0},{name:"ioBinding",type:"InputOutputBinding",isMany:!0,xml:{serialize:"property"}}]},{name:"FlowNode",isAbstract:!0,superClass:["FlowElement"],properties:[{name:"incoming",type:"SequenceFlow",isMany:!0,isReference:!0},{name:"outgoing",type:"SequenceFlow",isMany:!0,isReference:!0},{name:"lanes",type:"Lane",isMany:!0,isVirtual:!0,isReference:!0}]},{name:"CorrelationPropertyRetrievalExpression",superClass:["BaseElement"],properties:[{name:"messagePath",type:"FormalExpression"},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"CorrelationPropertyBinding",superClass:["BaseElement"],properties:[{name:"dataPath",type:"FormalExpression"},{name:"correlationPropertyRef",type:"CorrelationProperty",isAttr:!0,isReference:!0}]},{name:"Resource",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"resourceParameters",type:"ResourceParameter",isMany:!0}]},{name:"ResourceParameter",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isRequired",isAttr:!0,type:"Boolean"},{name:"type",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"CorrelationSubscription",superClass:["BaseElement"],properties:[{name:"correlationKeyRef",type:"CorrelationKey",isAttr:!0,isReference:!0},{name:"correlationPropertyBinding",type:"CorrelationPropertyBinding",isMany:!0}]},{name:"MessageFlow",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"sourceRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"MessageFlowAssociation",superClass:["BaseElement"],properties:[{name:"innerMessageFlowRef",type:"MessageFlow",isAttr:!0,isReference:!0},{name:"outerMessageFlowRef",type:"MessageFlow",isAttr:!0,isReference:!0}]},{name:"InteractionNode",isAbstract:!0,properties:[{name:"incomingConversationLinks",type:"ConversationLink",isMany:!0,isVirtual:!0,isReference:!0},{name:"outgoingConversationLinks",type:"ConversationLink",isMany:!0,isVirtual:!0,isReference:!0}]},{name:"Participant",superClass:["InteractionNode","BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"interfaceRef",type:"Interface",isMany:!0,isReference:!0},{name:"participantMultiplicity",type:"ParticipantMultiplicity"},{name:"endPointRefs",type:"EndPoint",isMany:!0,isReference:!0},{name:"processRef",type:"Process",isAttr:!0,isReference:!0}]},{name:"ParticipantAssociation",superClass:["BaseElement"],properties:[{name:"innerParticipantRef",type:"Participant",isAttr:!0,isReference:!0},{name:"outerParticipantRef",type:"Participant",isAttr:!0,isReference:!0}]},{name:"ParticipantMultiplicity",properties:[{name:"minimum",default:0,isAttr:!0,type:"Integer"},{name:"maximum",default:1,isAttr:!0,type:"Integer"}],superClass:["BaseElement"]},{name:"Collaboration",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isClosed",isAttr:!0,type:"Boolean"},{name:"participants",type:"Participant",isMany:!0},{name:"messageFlows",type:"MessageFlow",isMany:!0},{name:"artifacts",type:"Artifact",isMany:!0},{name:"conversations",type:"ConversationNode",isMany:!0},{name:"conversationAssociations",type:"ConversationAssociation"},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0},{name:"messageFlowAssociations",type:"MessageFlowAssociation",isMany:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0},{name:"choreographyRef",type:"Choreography",isMany:!0,isReference:!0},{name:"conversationLinks",type:"ConversationLink",isMany:!0}]},{name:"ChoreographyActivity",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"participantRef",type:"Participant",isMany:!0,isReference:!0},{name:"initiatingParticipantRef",type:"Participant",isAttr:!0,isReference:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0},{name:"loopType",type:"ChoreographyLoopType",default:"None",isAttr:!0}]},{name:"CallChoreography",superClass:["ChoreographyActivity"],properties:[{name:"calledChoreographyRef",type:"Choreography",isAttr:!0,isReference:!0},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0}]},{name:"SubChoreography",superClass:["ChoreographyActivity","FlowElementsContainer"],properties:[{name:"artifacts",type:"Artifact",isMany:!0}]},{name:"ChoreographyTask",superClass:["ChoreographyActivity"],properties:[{name:"messageFlowRef",type:"MessageFlow",isMany:!0,isReference:!0}]},{name:"Choreography",superClass:["Collaboration","FlowElementsContainer"]},{name:"GlobalChoreographyTask",superClass:["Choreography"],properties:[{name:"initiatingParticipantRef",type:"Participant",isAttr:!0,isReference:!0}]},{name:"TextAnnotation",superClass:["Artifact"],properties:[{name:"text",type:"String"},{name:"textFormat",default:"text/plain",isAttr:!0,type:"String"}]},{name:"Group",superClass:["Artifact"],properties:[{name:"categoryValueRef",type:"CategoryValue",isAttr:!0,isReference:!0}]},{name:"Association",superClass:["Artifact"],properties:[{name:"associationDirection",type:"AssociationDirection",isAttr:!0},{name:"sourceRef",type:"BaseElement",isAttr:!0,isReference:!0},{name:"targetRef",type:"BaseElement",isAttr:!0,isReference:!0}]},{name:"Category",superClass:["RootElement"],properties:[{name:"categoryValue",type:"CategoryValue",isMany:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Artifact",isAbstract:!0,superClass:["BaseElement"]},{name:"CategoryValue",superClass:["BaseElement"],properties:[{name:"categorizedFlowElements",type:"FlowElement",isMany:!0,isVirtual:!0,isReference:!0},{name:"value",isAttr:!0,type:"String"}]},{name:"Activity",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"isForCompensation",default:!1,isAttr:!0,type:"Boolean"},{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0},{name:"ioSpecification",type:"InputOutputSpecification",xml:{serialize:"property"}},{name:"boundaryEventRefs",type:"BoundaryEvent",isMany:!0,isReference:!0},{name:"properties",type:"Property",isMany:!0},{name:"dataInputAssociations",type:"DataInputAssociation",isMany:!0},{name:"dataOutputAssociations",type:"DataOutputAssociation",isMany:!0},{name:"startQuantity",default:1,isAttr:!0,type:"Integer"},{name:"resources",type:"ResourceRole",isMany:!0},{name:"completionQuantity",default:1,isAttr:!0,type:"Integer"},{name:"loopCharacteristics",type:"LoopCharacteristics"}]},{name:"ServiceTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"SubProcess",superClass:["Activity","FlowElementsContainer","InteractionNode"],properties:[{name:"triggeredByEvent",default:!1,isAttr:!0,type:"Boolean"},{name:"artifacts",type:"Artifact",isMany:!0}]},{name:"LoopCharacteristics",isAbstract:!0,superClass:["BaseElement"]},{name:"MultiInstanceLoopCharacteristics",superClass:["LoopCharacteristics"],properties:[{name:"isSequential",default:!1,isAttr:!0,type:"Boolean"},{name:"behavior",type:"MultiInstanceBehavior",default:"All",isAttr:!0},{name:"loopCardinality",type:"Expression",xml:{serialize:"xsi:type"}},{name:"loopDataInputRef",type:"ItemAwareElement",isReference:!0},{name:"loopDataOutputRef",type:"ItemAwareElement",isReference:!0},{name:"inputDataItem",type:"DataInput",xml:{serialize:"property"}},{name:"outputDataItem",type:"DataOutput",xml:{serialize:"property"}},{name:"complexBehaviorDefinition",type:"ComplexBehaviorDefinition",isMany:!0},{name:"completionCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"oneBehaviorEventRef",type:"EventDefinition",isAttr:!0,isReference:!0},{name:"noneBehaviorEventRef",type:"EventDefinition",isAttr:!0,isReference:!0}]},{name:"StandardLoopCharacteristics",superClass:["LoopCharacteristics"],properties:[{name:"testBefore",default:!1,isAttr:!0,type:"Boolean"},{name:"loopCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"loopMaximum",type:"Integer",isAttr:!0}]},{name:"CallActivity",superClass:["Activity","InteractionNode"],properties:[{name:"calledElement",type:"String",isAttr:!0}]},{name:"Task",superClass:["Activity","InteractionNode"]},{name:"SendTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"ReceiveTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"instantiate",default:!1,isAttr:!0,type:"Boolean"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"ScriptTask",superClass:["Task"],properties:[{name:"scriptFormat",isAttr:!0,type:"String"},{name:"script",type:"String"}]},{name:"BusinessRuleTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"}]},{name:"AdHocSubProcess",superClass:["SubProcess"],properties:[{name:"completionCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"ordering",type:"AdHocOrdering",isAttr:!0},{name:"cancelRemainingInstances",default:!0,isAttr:!0,type:"Boolean"}]},{name:"Transaction",superClass:["SubProcess"],properties:[{name:"protocol",isAttr:!0,type:"String"},{name:"method",isAttr:!0,type:"String"}]},{name:"GlobalScriptTask",superClass:["GlobalTask"],properties:[{name:"scriptLanguage",isAttr:!0,type:"String"},{name:"script",isAttr:!0,type:"String"}]},{name:"GlobalBusinessRuleTask",superClass:["GlobalTask"],properties:[{name:"implementation",isAttr:!0,type:"String"}]},{name:"ComplexBehaviorDefinition",superClass:["BaseElement"],properties:[{name:"condition",type:"FormalExpression"},{name:"event",type:"ImplicitThrowEvent"}]},{name:"ResourceRole",superClass:["BaseElement"],properties:[{name:"resourceRef",type:"Resource",isReference:!0},{name:"resourceParameterBindings",type:"ResourceParameterBinding",isMany:!0},{name:"resourceAssignmentExpression",type:"ResourceAssignmentExpression"},{name:"name",isAttr:!0,type:"String"}]},{name:"ResourceParameterBinding",properties:[{name:"expression",type:"Expression",xml:{serialize:"xsi:type"}},{name:"parameterRef",type:"ResourceParameter",isAttr:!0,isReference:!0}],superClass:["BaseElement"]},{name:"ResourceAssignmentExpression",properties:[{name:"expression",type:"Expression",xml:{serialize:"xsi:type"}}],superClass:["BaseElement"]},{name:"Import",properties:[{name:"importType",isAttr:!0,type:"String"},{name:"location",isAttr:!0,type:"String"},{name:"namespace",isAttr:!0,type:"String"}]},{name:"Definitions",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"targetNamespace",isAttr:!0,type:"String"},{name:"expressionLanguage",default:"http://www.w3.org/1999/XPath",isAttr:!0,type:"String"},{name:"typeLanguage",default:"http://www.w3.org/2001/XMLSchema",isAttr:!0,type:"String"},{name:"imports",type:"Import",isMany:!0},{name:"extensions",type:"Extension",isMany:!0},{name:"rootElements",type:"RootElement",isMany:!0},{name:"diagrams",isMany:!0,type:"bpmndi:BPMNDiagram"},{name:"exporter",isAttr:!0,type:"String"},{name:"relationships",type:"Relationship",isMany:!0},{name:"exporterVersion",isAttr:!0,type:"String"}]}],enumerations:[{name:"ProcessType",literalValues:[{name:"None"},{name:"Public"},{name:"Private"}]},{name:"GatewayDirection",literalValues:[{name:"Unspecified"},{name:"Converging"},{name:"Diverging"},{name:"Mixed"}]},{name:"EventBasedGatewayType",literalValues:[{name:"Parallel"},{name:"Exclusive"}]},{name:"RelationshipDirection",literalValues:[{name:"None"},{name:"Forward"},{name:"Backward"},{name:"Both"}]},{name:"ItemKind",literalValues:[{name:"Physical"},{name:"Information"}]},{name:"ChoreographyLoopType",literalValues:[{name:"None"},{name:"Standard"},{name:"MultiInstanceSequential"},{name:"MultiInstanceParallel"}]},{name:"AssociationDirection",literalValues:[{name:"None"},{name:"One"},{name:"Both"}]},{name:"MultiInstanceBehavior",literalValues:[{name:"None"},{name:"One"},{name:"All"},{name:"Complex"}]},{name:"AdHocOrdering",literalValues:[{name:"Parallel"},{name:"Sequential"}]}],xml:{tagAlias:"lowerCase",typePrefix:"t"}},bpmndi:{name:"BPMNDI",uri:"http://www.omg.org/spec/BPMN/20100524/DI",prefix:"bpmndi",types:[{name:"BPMNDiagram",properties:[{name:"plane",type:"BPMNPlane",redefines:"di:Diagram#rootElement"},{name:"labelStyle",type:"BPMNLabelStyle",isMany:!0}],superClass:["di:Diagram"]},{name:"BPMNPlane",properties:[{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"}],superClass:["di:Plane"]},{name:"BPMNShape",properties:[{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"},{name:"isHorizontal",isAttr:!0,type:"Boolean"},{name:"isExpanded",isAttr:!0,type:"Boolean"},{name:"isMarkerVisible",isAttr:!0,type:"Boolean"},{name:"label",type:"BPMNLabel"},{name:"isMessageVisible",isAttr:!0,type:"Boolean"},{name:"participantBandKind",type:"ParticipantBandKind",isAttr:!0},{name:"choreographyActivityShape",type:"BPMNShape",isAttr:!0,isReference:!0}],superClass:["di:LabeledShape"]},{name:"BPMNEdge",properties:[{name:"label",type:"BPMNLabel"},{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"},{name:"sourceElement",isAttr:!0,isReference:!0,type:"di:DiagramElement",redefines:"di:Edge#source"},{name:"targetElement",isAttr:!0,isReference:!0,type:"di:DiagramElement",redefines:"di:Edge#target"},{name:"messageVisibleKind",type:"MessageVisibleKind",isAttr:!0,default:"initiating"}],superClass:["di:LabeledEdge"]},{name:"BPMNLabel",properties:[{name:"labelStyle",type:"BPMNLabelStyle",isAttr:!0,isReference:!0,redefines:"di:DiagramElement#style"}],superClass:["di:Label"]},{name:"BPMNLabelStyle",properties:[{name:"font",type:"dc:Font"}],superClass:["di:Style"]}],enumerations:[{name:"ParticipantBandKind",literalValues:[{name:"top_initiating"},{name:"middle_initiating"},{name:"bottom_initiating"},{name:"top_non_initiating"},{name:"middle_non_initiating"},{name:"bottom_non_initiating"}]},{name:"MessageVisibleKind",literalValues:[{name:"initiating"},{name:"non_initiating"}]}],associations:[]},dc:{name:"DC",uri:"http://www.omg.org/spec/DD/20100524/DC",prefix:"dc",types:[{name:"Boolean"},{name:"Integer"},{name:"Real"},{name:"String"},{name:"Font",properties:[{name:"name",type:"String",isAttr:!0},{name:"size",type:"Real",isAttr:!0},{name:"isBold",type:"Boolean",isAttr:!0},{name:"isItalic",type:"Boolean",isAttr:!0},{name:"isUnderline",type:"Boolean",isAttr:!0},{name:"isStrikeThrough",type:"Boolean",isAttr:!0}]},{name:"Point",properties:[{name:"x",type:"Real",default:"0",isAttr:!0},{name:"y",type:"Real",default:"0",isAttr:!0}]},{name:"Bounds",properties:[{name:"x",type:"Real",default:"0",isAttr:!0},{name:"y",type:"Real",default:"0",isAttr:!0},{name:"width",type:"Real",isAttr:!0},{name:"height",type:"Real",isAttr:!0}]}],associations:[]},di:{name:"DI",uri:"http://www.omg.org/spec/DD/20100524/DI",prefix:"di",types:[{name:"DiagramElement",isAbstract:!0,properties:[{name:"id",isAttr:!0,isId:!0,type:"String"},{name:"extension",type:"Extension"},{name:"owningDiagram",type:"Diagram",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"owningElement",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"modelElement",isReadOnly:!0,isVirtual:!0,isReference:!0,type:"Element"},{name:"style",type:"Style",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"ownedElement",type:"DiagramElement",isReadOnly:!0,isMany:!0,isVirtual:!0}]},{name:"Node",isAbstract:!0,superClass:["DiagramElement"]},{name:"Edge",isAbstract:!0,superClass:["DiagramElement"],properties:[{name:"source",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"target",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"waypoint",isUnique:!1,isMany:!0,type:"dc:Point",xml:{serialize:"xsi:type"}}]},{name:"Diagram",isAbstract:!0,properties:[{name:"id",isAttr:!0,isId:!0,type:"String"},{name:"rootElement",type:"DiagramElement",isReadOnly:!0,isVirtual:!0},{name:"name",isAttr:!0,type:"String"},{name:"documentation",isAttr:!0,type:"String"},{name:"resolution",isAttr:!0,type:"Real"},{name:"ownedStyle",type:"Style",isReadOnly:!0,isMany:!0,isVirtual:!0}]},{name:"Shape",isAbstract:!0,superClass:["Node"],properties:[{name:"bounds",type:"dc:Bounds"}]},{name:"Plane",isAbstract:!0,superClass:["Node"],properties:[{name:"planeElement",type:"DiagramElement",subsettedProperty:"DiagramElement-ownedElement",isMany:!0}]},{name:"LabeledEdge",isAbstract:!0,superClass:["Edge"],properties:[{name:"ownedLabel",type:"Label",isReadOnly:!0,subsettedProperty:"DiagramElement-ownedElement",isMany:!0,isVirtual:!0}]},{name:"LabeledShape",isAbstract:!0,superClass:["Shape"],properties:[{name:"ownedLabel",type:"Label",isReadOnly:!0,subsettedProperty:"DiagramElement-ownedElement",isMany:!0,isVirtual:!0}]},{name:"Label",isAbstract:!0,superClass:["Node"],properties:[{name:"bounds",type:"dc:Bounds"}]},{name:"Style",isAbstract:!0,properties:[{name:"id",isAttr:!0,isId:!0,type:"String"}]},{name:"Extension",properties:[{name:"values",isMany:!0,type:"Element"}]}],associations:[],xml:{tagAlias:"lowerCase"}},bioc:{name:"bpmn.io colors for BPMN",uri:"http://bpmn.io/schema/bpmn/biocolor/1.0",prefix:"bioc",types:[{name:"ColoredShape",extends:["bpmndi:BPMNShape"],properties:[{name:"stroke",isAttr:!0,type:"String"},{name:"fill",isAttr:!0,type:"String"}]},{name:"ColoredEdge",extends:["bpmndi:BPMNEdge"],properties:[{name:"stroke",isAttr:!0,type:"String"},{name:"fill",isAttr:!0,type:"String"}]}],enumerations:[],associations:[]},color:{name:"BPMN in Color",uri:"http://www.omg.org/spec/BPMN/non-normative/color/1.0",prefix:"color",types:[{name:"ColoredLabel",extends:["bpmndi:BPMNLabel"],properties:[{name:"color",isAttr:!0,type:"String"}]},{name:"ColoredShape",extends:["bpmndi:BPMNShape"],properties:[{name:"background-color",isAttr:!0,type:"String"},{name:"border-color",isAttr:!0,type:"String"}]},{name:"ColoredEdge",extends:["bpmndi:BPMNEdge"],properties:[{name:"border-color",isAttr:!0,type:"String"}]}],enumerations:[],associations:[]}};function vo(e,t){return new yo(ge({},go,e),t)}function bo(e){return function(){if(!window.Promise)throw new Error("Promises is not supported in this environment. Please polyfill Promise.");var t=arguments.length;if(!(t>=1&&ie(arguments[t-1])))return e.apply(this,arguments);var n=arguments[t-1];console.warn(new Error("Passing callbacks to "+e.name+" is deprecated and will be removed in a future major release. Please switch to promises: https://bpmn.io/l/moving-to-promises.html"));var i=Array.prototype.slice.call(arguments,0,-1);e.apply(this,i).then((function(e){var t=Object.keys(e)[0];return n(null,e[t])}),(function(e){return n(e,e.warnings)}))}}function wo(e,t){return e.$instanceOf(t)}function xo(e,t){var n={},i=[],r={};function o(e,t){return function(n){e(n,t)}}function a(e){n[e.id]=e}function s(n,i){try{var o=r[n.id]&&function(n,i){if(n.gfx)throw new Error(t("already rendered {element}",{element:pn(n)}));return e.element(n,r[n.id],i)}(n,i);return a(n),o}catch(q){l(q.message,{element:n,error:q}),console.error(t("failed to import {element}",{element:pn(n)})),console.error(q)}}function l(t,n){e.error(t,n)}var p=this.registerDi=function(e){var n,i=e.bpmnElement;i?r[i.id]?l(t("multiple DI elements defined for {element}",{element:pn(i)}),{element:i}):(r[i.id]=e,oe(n=i,"di")||Object.defineProperty(n,"di",{enumerable:!1,get:function(){throw new Error("Tried to access di from the businessObject. The di is available through the diagram element only. For more information, see https://github.com/bpmn-io/bpmn-js/issues/1472")}})):l(t("no bpmnElement referenced in {element}",{element:pn(e)}),{element:e})};function c(e){var t;t=e.plane,p(t),le(t.planeElement,u)}function u(e){p(e)}this.handleDefinitions=function(a,s){var u=a.diagrams;if(s&&-1===u.indexOf(s))throw new Error(t("diagram not part of bpmn:Definitions"));if(!s&&u&&u.length&&(s=u[0]),!s)throw new Error(t("no diagram to display"));r={},c(s);var f=s.plane;if(!f)throw new Error(t("no plane for {element}",{element:pn(s)}));var y=f.bpmnElement;if(!y){if(y=function(e){return ae(e.rootElements,(function(e){return wo(e,"bpmn:Process")||wo(e,"bpmn:Collaboration")}))}(a),!y)throw new Error(t("no process or collaboration to display"));l(t("correcting missing bpmnElement on {plane} to {rootElement}",{plane:pn(f),rootElement:pn(y)})),f.bpmnElement=y,p(f)}var g,v,w=function(t,n){return e.root(t,r[t.id],n)}(y,f);if(wo(y,"bpmn:Process")||wo(y,"bpmn:SubProcess"))d(y,w);else{if(!wo(y,"bpmn:Collaboration"))throw new Error(t("unsupported bpmnElement for {plane}: {rootElement}",{plane:pn(f),rootElement:pn(y)}));v=w,le((g=y).participants,o(R,v)),b(g.artifacts,v),i.push((function(){!function(e,t){le(e,o(h,t))}(g.messageFlows,v)})),function(e,t){var i=se(e,(function(e){return!n[e.id]&&wo(e,"bpmn:Process")&&e.laneSets}));i.forEach(o(d,t))}(a.rootElements,w)}m(i)};var m=this.handleDeferred=function(){for(;i.length;)i.shift()()};function d(e,t){C(e,t),w(e.ioSpecification,t),b(e.artifacts,t),a(e)}function h(e,t){s(e,t)}function f(e,t){s(e,t)}function y(e,t){s(e,t)}function g(e,t){s(e,t)}function v(e,t){s(e,t)}function b(e,t){le(e,(function(e){wo(e,"bpmn:Association")?i.push((function(){v(e,t)})):v(e,t)}))}function w(e,t){e&&(le(e.dataInputs,o(y,t)),le(e.dataOutputs,o(g,t)))}var x=this.handleSubProcess=function(e,t){C(e,t),b(e.artifacts,t)};function E(e,t){var n=s(e,t);wo(e,"bpmn:SubProcess")&&x(e,n||t),wo(e,"bpmn:Activity")&&w(e.ioSpecification,t),i.push((function(){le(e.dataInputAssociations,o(f,t)),le(e.dataOutputAssociations,o(f,t))}))}function _(e,t){s(e,t)}function S(e,t){i.push((function(){var n=s(e,t);e.childLaneSet&&A(e.childLaneSet,n||t),function(e){le(e.flowNodeRef,(function(t){var n=t.get("lanes");n&&n.push(e)}))}(e)}))}function A(e,t){le(e.lanes,o(S,t))}function C(e,n){!function(e,n){le(e,(function(e){wo(e,"bpmn:SequenceFlow")?i.push((function(){!function(e,t){s(e,t)}(e,n)})):wo(e,"bpmn:BoundaryEvent")?i.unshift((function(){E(e,n)})):wo(e,"bpmn:FlowNode")?E(e,n):wo(e,"bpmn:DataObject")||(wo(e,"bpmn:DataStoreReference")||wo(e,"bpmn:DataObjectReference")?_(e,n):l(t("unrecognized flowElement {element} in context {context}",{element:pn(e),context:n?pn(n.businessObject):"null"}),{element:e,context:n}))}))}(e.flowElements,n),e.laneSets&&function(e,t){le(e,o(A,t))}(e.laneSets,n)}function R(e,t){var n=s(e,t),i=e.processRef;i&&d(i,n||t)}}function Eo(e,t,n){var i,r,o,a,s=[];function l(e,t){var n=new xo({root:function(e,t){return i.add(e,t)},element:function(e,t,n){return i.add(e,t,n)},error:function(e,t){s.push({message:e,context:t})}},o);t=t||e.diagrams&&e.diagrams[0];var r=function(e,t){if(t){var n,i=t.plane.bpmnElement,r=i;be(i,"bpmn:Process")||be(i,"bpmn:Collaboration")||(r=function(e){for(var t=e;t;){if(be(t,"bpmn:Process"))return t;t=t.$parent}}(i)),n=be(r,"bpmn:Collaboration")?r:ae(e.rootElements,(function(e){if(be(e,"bpmn:Collaboration"))return ae(e.participants,(function(e){return e.processRef===r}))}));var o=[r];n&&(o=function(e,t){let n=[];return le(e,(function(e,i){n.push(t(e,i))})),n}(n.participants,(function(e){return e.processRef})),o.push(n));var a=_o(o),s=[t],l=[i];return le(e.diagrams,(function(e){var t=e.plane.bpmnElement;-1!==a.indexOf(t)&&-1===l.indexOf(t)&&(s.push(e),l.push(t))})),s}}(e,t);if(!r)throw new Error(o("no diagram to display"));le(r,(function(t){n.handleDefinitions(e,t)}));var l=t.plane.bpmnElement.id;a.setRootElement(a.findRoot(l+"_plane")||a.findRoot(l))}return new Promise((function(p,c){try{return i=e.get("bpmnImporter"),r=e.get("eventBus"),o=e.get("translate"),a=e.get("canvas"),r.fire("import.render.start",{definitions:t}),l(t,n),r.fire("import.render.complete",{error:void 0,warnings:s}),p({warnings:s})}catch(q){return q.warnings=s,c(q)}}))}function _o(e){var t=[];return le(e,(function(e){e&&(t.push(e),t=t.concat(_o(e.flowElements)))})),t}var So,Ao='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14.02 5.57" width="53" height="21"><path fill="currentColor" d="M1.88.92v.14c0 .41-.13.68-.*********.46.44.46.86v.33c0 .61-.33.95-.95.95H0V0h.95c.65 0 .93.3.93.92zM.63.57v1.06h.24c.24 0 .38-.1.38-.43V.98c0-.28-.1-.4-.32-.4zm0 1.63v1.22h.36c.2 0 .32-.1.32-.39v-.35c0-.37-.12-.48-.4-.48H.63zM4.18.99v.52c0 .64-.31.98-.94.98h-.3V4h-.62V0h.92c.63 0 .94.35.94.99zM2.94.57v1.35h.3c.2 0 .3-.09.3-.37v-.6c0-.29-.1-.38-.3-.38h-.3zm2.89 2.27L6.25 0h.88v4h-.6V1.12L6.1 3.99h-.6l-.46-2.82v2.82h-.55V0h.87zM8.14 1.1V4h-.56V0h.79L9 2.4V0h.56v4h-.64zm2.49 2.29v.6h-.6v-.6zM12.12 1c0-.63.33-1 .95-1 .61 0 .95.37.95 1v2.04c0 .64-.34 1-.95 1-.62 0-.95-.37-.95-1zm.62 2.08c0 .28.13.39.33.39s.32-.1.32-.4V.98c0-.29-.12-.4-.32-.4s-.33.11-.33.4z"/><path fill="currentColor" d="M0 4.53h14.02v1.04H0zM11.08 0h.63v.62h-.63zm.63 4V1h-.63v2.98z"/></svg>',Co={verticalAlign:"middle"},Ro={color:"#404040"},Po={zIndex:"1001",position:"fixed",top:"0",left:"0",right:"0",bottom:"0"},ko={width:"100%",height:"100%",background:"rgba(40,40,40,0.2)"},Mo={position:"absolute",left:"50%",top:"40%",transform:"translate(-50%)",width:"260px",padding:"10px",background:"white",boxShadow:"0 1px 4px rgba(0,0,0,0.3)",fontFamily:"Helvetica, Arial, sans-serif",fontSize:"14px",display:"flex",lineHeight:"1.3"},To='<div class="bjs-powered-by-lightbox"><div class="backdrop"></div><div class="notice"><a href="https://bpmn.io" target="_blank" rel="noopener" class="link">'+Ao+'</a><span>Web-based tooling for BPMN, DMN and forms powered by <a href="https://bpmn.io" target="_blank" rel="noopener">bpmn.io</a>.</span></div></div>';function No(){So||(bt(So=Lt(To),Po),bt(Bt("svg",So),Co),bt(Bt(".backdrop",So),ko),bt(Bt(".notice",So),Mo),bt(Bt(".link",So),Ro,{margin:"15px 20px 15px 10px",alignSelf:"center"}),Nt.bind(So,".backdrop","click",(function(e){document.body.removeChild(So)}))),document.body.appendChild(So)}function Do(e){e=ge({},Io,e),this._moddle=this._createModdle(e),this._container=this._createContainer(e),function(e){const t=Lt('<a href="http://bpmn.io" target="_blank" class="bjs-powered-by" title="Powered by bpmn.io" >'+Ao+"</a>");bt(Bt("svg",t),Co),bt(t,Ro,{position:"absolute",bottom:"15px",right:"15px",zIndex:"100"}),e.appendChild(t),Mt.bind(t,"click",(function(e){No(),e.preventDefault()}))}(this._container),this._init(this._container,this._moddle,e)}function Oo(e,t){return e.warnings=t,e}q(Do,tr),Do.prototype.importXML=async function(e,t){const n=this;let i=[];try{let r;e=this._emit("import.parse.start",{xml:e})||e;try{r=await this._moddle.fromXML(e,"bpmn:Definitions")}catch(jr){throw this._emit("import.parse.complete",{error:jr}),jr}let o=r.rootElement;const a=r.references,s=r.warnings,l=r.elementsById;i=i.concat(s),o=this._emit("import.parse.complete",function(e){const t=n.get("eventBus").createEvent(e);return Object.defineProperty(t,"context",{enumerable:!0,get:function(){return console.warn(new Error("import.parse.complete <context> is deprecated and will be removed in future library versions")),{warnings:e.warnings,references:e.references,elementsById:e.elementsById}}}),t}({error:null,definitions:o,elementsById:l,references:a,warnings:i}))||o;const p=await this.importDefinitions(o,t);return i=i.concat(p.warnings),this._emit("import.done",{error:null,warnings:i}),{warnings:i}}catch(r){let e=r;throw i=i.concat(e.warnings||[]),Oo(e,i),e=function(e){const t=/unparsable content <([^>]+)> detected([\s\S]*)$/.exec(e.message);return t&&(e.message="unparsable content <"+t[1]+"> detected; this may indicate an invalid BPMN 2.0 diagram file"+t[2]),e}(e),this._emit("import.done",{error:e,warnings:e.warnings}),e}},Do.prototype.importXML=bo(Do.prototype.importXML),Do.prototype.importDefinitions=async function(e,t){return this._setDefinitions(e),{warnings:(await this.open(t)).warnings}},Do.prototype.importDefinitions=bo(Do.prototype.importDefinitions),Do.prototype.open=async function(e){const t=this._definitions;let n=e;if(!t){const e=new Error("no XML imported");throw Oo(e,[]),e}if("string"==typeof e&&(n=function(e,t){return t&&ae(e.diagrams,(function(e){return e.id===t}))||null}(t,e),!n)){const t=new Error("BPMNDiagram <"+e+"> not found");throw Oo(t,[]),t}try{this.clear()}catch(jr){throw Oo(jr,[]),jr}const{warnings:i}=await Eo(this,t,n);return{warnings:i}},Do.prototype.open=bo(Do.prototype.open),Do.prototype.saveXML=async function(e){e=e||{};let t,n,i=this._definitions;try{if(!i)throw new Error("no definitions loaded");i=this._emit("saveXML.start",{definitions:i})||i,n=(await this._moddle.toXML(i,e)).xml,n=this._emit("saveXML.serialized",{xml:n})||n}catch(o){t=o}const r=t?{error:t}:{xml:n};if(this._emit("saveXML.done",r),t)throw t;return r},Do.prototype.saveXML=bo(Do.prototype.saveXML),Do.prototype.saveSVG=async function(){let e,t;this._emit("saveSVG.start");try{const t=this.get("canvas"),n=t.getActiveLayer(),i=Bt("defs",t._svg),r=Xe(n),o=i?"<defs>"+Xe(i)+"</defs>":"",a=n.getBBox();e='<?xml version="1.0" encoding="utf-8"?>\n\x3c!-- created with bpmn-js / http://bpmn.io --\x3e\n<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="'+a.width+'" height="'+a.height+'" viewBox="'+a.x+" "+a.y+" "+a.width+" "+a.height+'" version="1.1">'+o+r+"</svg>"}catch(q){t=q}if(this._emit("saveSVG.done",{error:t,svg:e}),t)throw t;return{svg:e}},Do.prototype.saveSVG=bo(Do.prototype.saveSVG),Do.prototype._setDefinitions=function(e){this._definitions=e},Do.prototype.getModules=function(){return this._modules},Do.prototype.clear=function(){this.getDefinitions()&&tr.prototype.clear.call(this)},Do.prototype.destroy=function(){tr.prototype.destroy.call(this),Ft(this._container)},Do.prototype.on=function(e,t,n,i){return this.get("eventBus").on(e,t,n,i)},Do.prototype.off=function(e,t){this.get("eventBus").off(e,t)},Do.prototype.attachTo=function(e){if(!e)throw new Error("parentNode required");this.detach(),e.get&&e.constructor.prototype.jquery&&(e=e.get(0)),"string"==typeof e&&(e=Bt(e)),e.appendChild(this._container),this._emit("attach",{}),this.get("canvas").resized()},Do.prototype.getDefinitions=function(){return this._definitions},Do.prototype.detach=function(){const e=this._container,t=e.parentNode;t&&(this._emit("detach",{}),t.removeChild(e))},Do.prototype._init=function(e,t,n){const i=n.modules||this.getModules(n),r=n.additionalModules||[],o=[].concat([{bpmnjs:["value",this],moddle:["value",t]}],i,r),a=ge(function(e,t){let n={};return le(Object(e),(function(e,i){-1===t.indexOf(i)&&(n[i]=e)})),n}(n,["additionalModules"]),{canvas:ge({},n.canvas,{container:e}),modules:o});tr.call(this,a),n&&n.container&&this.attachTo(n.container)},Do.prototype._emit=function(e,t){return this.get("eventBus").fire(e,t)},Do.prototype._createContainer=function(e){const t=Lt('<div class="bjs-container"></div>');return bt(t,{width:Lo(e.width),height:Lo(e.height),position:e.position}),t},Do.prototype._createModdle=function(e){return new vo(ge({},this._moddleExtensions,e.moddleExtensions))},Do.prototype._modules=[];const Io={width:"100%",height:"100%",position:"relative"};function Lo(e){return e+(ne(e)?"px":"")}function Bo(e){Do.call(this,e)}q(Bo,Do),Bo.prototype._modules=[hn,rn,On,$n,ai],Bo.prototype._moddleExtensions={};const Fo={name:"FlowBmp",components:{},props:{modelKey:String,bizId:String,processInstanceId:String,show:Boolean},emits:[],setup(e,{attrs:t,slots:n,emit:i}){},data:()=>({loading:!1,bpmnXml:"",flowInstance:{},setup:!1}),computed:{},watch:{modelKey:{immediate:!0,handler(e){e&&this.getModelXML()}},show:{immediate:!0,handler(e){e&&!this.setup&&this.setupBmp()}}},created(){},mounted(){},methods:{async getModelXML(){try{var e;this.loading=!0;const t=await _({modelKey:this.modelKey});if(this.bpmnXml=t.bpmnXml,"-1"==(null===(e=this.$route.query)||void 0===e?void 0:e.processState))return;this.getNodesHistory()}catch(q){console.log(q)}finally{this.loading=!1}},async getNodesHistory(){try{this.loading=!0;const e=await S({bizId:this.bizId});this.flowInstance=e||{}}catch(q){console.log(q)}finally{this.loading=!1}},async setupBmp(){try{const e=this.$refs.canvas;if(!e)return;e.innerHTML="";const t=new Bo({container:e}),{warnings:n}=await t.importXML(this.bpmnXml);this.$nextTick((()=>{const e=t.get("canvas"),{currentNode:n=[],userTask:i=[],exclusiveGateway:r=[]}=this.flowInstance;n.forEach((t=>{e.addMarker(t,"current-highlight")})),i.forEach((t=>{e.addMarker(t,"highlight")})),r.forEach((t=>{e.addMarker(t,"highlight")})),e.zoom("fit-viewport")})),this.setup=!0}catch(q){console.log(q)}}}},jo={class:"bmp-container"},Vo={class:"canvas",ref:"canvas"},$o=I(Fo,[["render",function(e,t,n,i,a,p){const c=r("van-divider");return o(),l("div",jo,[h(c,{class:"divider"},{default:s((()=>t[0]||(t[0]=[f("流程图")]))),_:1,__:[0]}),t[1]||(t[1]=y('<div class="exmple-wp" data-v-ff27054e><div class="exmple-item" data-v-ff27054e><div class="exmple-icon current" data-v-ff27054e></div><span class="exmple-text" data-v-ff27054e>进行中</span></div><div class="exmple-item" data-v-ff27054e><div class="exmple-icon done" data-v-ff27054e></div><span class="exmple-text" data-v-ff27054e>已通过</span></div><div class="exmple-item" data-v-ff27054e><div class="exmple-icon process" data-v-ff27054e></div><span class="exmple-text" data-v-ff27054e>未完成</span></div></div>',1)),u("div",Vo,null,512)])}],["__scopeId","data-v-ff27054e"]]),zo={name:"BpmInitiatorInfos",componentName:"BpmInitiatorInfos",props:{info:{type:Object,default:null},modelValue:{type:Object,default:()=>({})},required:{type:Boolean,default:!0},readonly:{type:Boolean,default:!0},fieldReadonlyConfig:{type:Object,default:()=>({})},type:{type:String,default:""},format:{type:String,default:"YYYY-MM-DD"}},emits:["update:modelValue","setInitiatorInfo"],inject:{registerValidationResult:{default:null}},data(){return{taskKey:this.$route.params.taskKey||this.$route.query.taskKey,formData:{initiator:null,initiatorFullName:"",initiatorPhone:"",initiatedTime:"",initiatorDept:"",label:"",approvalTimeLabel:"",userPhoneLabel:"",userOrgLabel:""}}},computed:{currentUser(){return this.$store.USER_INFO||{}},fieldReadonly(){const e={initiatorFullName:!0,initiatorDept:!0,initiatorPhone:!0,initiatedTime:!0};return this.readonly?e:{...e,...this.fieldReadonlyConfig}}},watch:{info:{handler(e){if(e){const{userName:t,userFullName:n,userPhone:i,approvalTime:r,userOrg:o,label:a,approvalTimeLabel:s,userPhoneLabel:l,userOrgLabel:p}=e;this.formData={initiator:t,initiatorFullName:n,initiatorPhone:i,initiatedTime:r,initiatorDept:o,label:a,approvalTimeLabel:s,userPhoneLabel:l,userOrgLabel:p}}else"UserTask_0"===this.taskKey||"fawkes_custom_flow_start"===this.taskKey?this.setInitiatorInfo():this.resetForm()},immediate:!0,deep:!0},formData:{handler(e){this.$emit("update:modelValue",e)},deep:!0},modelValue:{handler(e){e&&Object.keys(e).forEach((t=>{this.formData.hasOwnProperty(t)&&(this.formData[t]=e[t])}))},immediate:!0,deep:!0}},created(){"add"===this.type&&this.setInitiatorInfo()},methods:{formatDate(e){var t;return e?this.$dayjs(e).format((null===(t=this.info)||void 0===t?void 0:t.format)||this.format):""},setInitiatorInfo(){var e;const t=this.currentUser;if(!t)return;this.formData.initiator=t.userName,this.formData.initiatorFullName=t.userFullname,this.formData.initiatorPhone=t.phone,this.formData.initiatedTime=this.formatDate(new Date),this.formData.initiatorDept=null!==(e=t.orgList)&&void 0!==e&&e.length?t.orgList.map((e=>e.name)).join(";"):"";const{initiator:n,initiatorFullName:i,initiatedTime:r,initiatorPhone:o,initiatorDept:a}=this.formData,s={label:this.formData.label||"发起人",prop:"initiator",userName:n,userFullName:i,approvalTime:r,userPhone:o,userOrg:a,type:0};this.$emit("setInitiatorInfo",s)},getFormData(){return this.formData},resetForm(){const{label:e,approvalTimeLabel:t,userPhoneLabel:n,userOrgLabel:i}=this.formData;this.formData={initiator:null,initiatorFullName:"",initiatorPhone:"",initiatedTime:"",initiatorDept:"",label:e||"",approvalTimeLabel:t||"",userPhoneLabel:n||"",userOrgLabel:i||""}},validate(){return!this.required||!!this.formData.initiatorFullName},validateFormItem(e){const{type:t,fields:n}=e,i="initiator";if(this.required&&(!n||n.includes(i))){const e=this.validate();this.registerValidationResult&&this.registerValidationResult(i,{valid:e,message:e?"":"发起人不能为空"})}},handleValidateForm(e){const{type:t,fields:n}=e,i="initiator";if(this.required&&(!n||n.includes(i))){const e=this.validate();this.$parent&&this.$parent.registerValidationResult&&this.$parent.registerValidationResult(i,{valid:e,message:e?"":"发起人不能为空"})}}}},Uo={class:"bpm-initiator-infos"},Wo=I(zo,[["render",function(e,t,n,i,a,p){const c=r("van-field"),u=r("van-cell-group");return o(),l("div",Uo,[g(e.$slots,"before",{},void 0,!0),h(u,{border:!1},{default:s((()=>[h(c,{modelValue:a.formData.initiatorFullName,"onUpdate:modelValue":t[0]||(t[0]=e=>a.formData.initiatorFullName=e),label:a.formData.label||"发起人",readonly:p.fieldReadonly.initiatorFullName,required:n.required},null,8,["modelValue","label","readonly","required"]),h(c,{modelValue:a.formData.initiatorDept,"onUpdate:modelValue":t[1]||(t[1]=e=>a.formData.initiatorDept=e),label:a.formData.userOrgLabel||"发起人部门",readonly:p.fieldReadonly.initiatorDept},null,8,["modelValue","label","readonly"]),h(c,{modelValue:a.formData.initiatorPhone,"onUpdate:modelValue":t[2]||(t[2]=e=>a.formData.initiatorPhone=e),label:a.formData.userPhoneLabel||"联系方式",readonly:p.fieldReadonly.initiatorPhone},null,8,["modelValue","label","readonly"]),h(c,{modelValue:a.formData.initiatedTime,"onUpdate:modelValue":t[3]||(t[3]=e=>a.formData.initiatedTime=e),formatter:p.formatDate,label:a.formData.approvalTimeLabel||"发起日期",readonly:p.fieldReadonly.initiatedTime},null,8,["modelValue","formatter","label","readonly"]),g(e.$slots,"after",{},void 0,!0)])),_:3})])}],["__scopeId","data-v-d09c136a"]]),Ko={name:"FormItemMultiplePerson",components:{},emits:["update:userName","update:userFullname","change"],props:{userName:String,userFullname:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:Boolean,readonly:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]}},setup(e,{attrs:t,slots:n,emit:i}){},data:()=>({showPicker:!1,loading:!1,finished:!1,list:[],searchParams:{pageNo:1,pageSize:200,isMobile:!0,searchValue:"",accountStatus:"1"},loaded:!1,user:{},selectUserList:[]}),computed:{portal(){return this.$store.PORTAL},selectedUser(){return this.user&&this.user.id?{...this.user}:{...this.list.find((e=>e.id==this.userId||e.userName===this.userName))}}},watch:{"searchParams.searchValue"(e){""===e&&this.onSearch(e)},list:{handler(e){this.loaded||this.setInitPerson()},deep:!0}},created(){},mounted(){},methods:{onSearch(e){this.finished=!1,this.searchParams.pageNo=1,this.$nextTick((()=>{this.onLoadList()}))},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams};1===e.pageNo&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await B(e),n=this.searchParams.pageNo<=1?[]:this.list||[];this.list=[...n,...t.list],this.list.length>=t.total?this.finished=!0:this.searchParams.pageNo++}catch(q){console.log(q),this.finished=!0}finally{setTimeout((()=>{this.loading=!1,this.loaded=!0,this.$closeToast()}),100)}},onShowPicker(){this.readonly||(this.showPicker=!0,this.loaded||this.onSearch())},setInitPerson(){let e=[];if(this.userName){let t=this.userName.split(",");this.list.forEach((n=>{t.includes(n.userName)&&e.push(n)}))}this.selectUserList=e},onClosePicker(){this.showPicker=!1},handleSelect(e){let t=this.selectUserList.findIndex((t=>t.userName==e.userName));t>-1?this.selectUserList.splice(t,1):this.selectUserList.push(e)},getDept(e=[]){return e.find((e=>{var t;return e.portalId==(null===(t=this.portal)||void 0===t?void 0:t.id)}))||e[0]},onSelectConfirm(){const e=this.selectUserList.map((e=>e.userName)).join(","),t=this.selectUserList.map((e=>e.userFullname)).join(",");this.$emit("update:userName",e),this.$emit("update:userFullname",t),this.$emit("change",this.selectUserList),this.onClosePicker()}}},Go={class:"pop-window"},Ho={class:"pop-window-header"},qo={class:"van-picker__title van-ellipsis"},Yo={class:"pop-window-body"},Xo={class:"list-content"},Jo={key:0,class:"p-[10px]"},Qo={class:"pop-window-footer van-safe-area-bottom"},Zo=e("a",I(Ko,[["render",function(e,t,n,i,y,g){const w=r("van-field"),x=r("van-search"),E=r("van-cell"),_=r("van-empty"),S=r("van-list"),A=r("van-button"),C=r("van-popup");return o(),l(p,null,[h(w,{name:n.name,"model-value":n.userFullname,label:n.label,required:n.required,rules:n.rules,"input-align":n.inputAlign,"error-message-align":n.errorMessageAlign,"label-width":n.labelWidth,readonly:"","is-link":!n.readonly,placeholder:"请选择",onClick:t[0]||(t[0]=e=>g.onShowPicker())},null,8,["name","model-value","label","required","rules","input-align","error-message-align","label-width","is-link"]),h(C,{show:y.showPicker,"onUpdate:show":t[4]||(t[4]=e=>y.showPicker=e),position:"bottom",closeable:"",teleport:"#app"},{default:s((()=>[u("div",Go,[u("div",Ho,[u("div",qo,m(n.title),1)]),u("div",Yo,[h(x,{modelValue:y.searchParams.searchValue,"onUpdate:modelValue":t[1]||(t[1]=e=>y.searchParams.searchValue=e),placeholder:"输入账号或姓名搜索","show-action":!1,onSearch:g.onSearch},null,8,["modelValue","onSearch"]),u("div",Xo,[h(S,{loading:y.loading,"onUpdate:loading":t[2]||(t[2]=e=>y.loading=e),finished:y.finished,"finished-text":y.list&&y.list.length?"没有更多了":"",onLoad:g.onLoadList,"immediate-check":!1},{default:s((()=>[y.list&&y.list.length?(o(!0),l(p,{key:0},c(y.list,(e=>{var t;return o(),a(E,{class:v(["person-cell",{select:y.selectUserList.findIndex((t=>t.userName===e.userName))>-1}]),key:e.id,title:e.userFullname,value:null===(t=g.getDept(e.orgList))||void 0===t?void 0:t.name,onClick:t=>g.handleSelect(e)},null,8,["class","title","value","onClick"])})),128)):(o(),l(p,{key:1},[y.loading?d("",!0):(o(),l("div",Jo,[h(_,{description:"暂无更多数据"})]))],64))])),_:1},8,["loading","finished","finished-text","onLoad"])])]),u("div",Qo,[h(A,{class:"confirm-button",round:"",block:"",type:"primary",onClick:t[3]||(t[3]=b((e=>g.onSelectConfirm()),["stop","prevent"]))},{default:s((()=>t[5]||(t[5]=[f(" 确定 ")]))),_:1,__:[5]})])])])),_:1},8,["show"])],64)}],["__scopeId","data-v-7e388d2b"]])),ea={class:"bpm-person-infos"},ta={class:"bpm-person-view"},na={name:"FlowForm",components:{FlowTimeline:H,FlowBmp:$o,UploadFiles:F,BpmInitiatorInfos:Wo,BpmPersonInfos:I({name:"BpmPersonInfos",componentName:"BpmPersonInfos",components:{FormItemPerson:V,FormItemMultiplePerson:Zo},props:{personList:{type:Array,default:()=>[]},title:{type:String,default:""}},emits:["setBpmPerson","validate"],data:()=>({tempPersonList:[],hiddenPersonList:[],formData:{},flowConfig:{},formRules:{},isCcInfo:!1}),watch:{personList:{handler(e){if(e){let t=e.filter((e=>0!==e.isVisible));this.hiddenPersonList=e.filter((e=>0===e.isVisible)),this.flowConfig={},this.formRules={};for(let e=0;e<t.length;e++){let n=t[e];this.flowConfig[n.prop]=0===n.isEditable?"readonly":"false",0!==n.isRequired&&(this.formRules[n.prop]={required:!0}),2===n.type?this.isCcInfo=!0:this.isCcInfo=!1}this.tempPersonList=JSON.parse(JSON.stringify(t||[]))}else this.tempPersonList=[],this.hiddenPersonList=[]},immediate:!0,deep:!0}},inject:{registerValidationResult:{default:null}},methods:{getFieldReadonly(e){return"readonly"===this.flowConfig[e]},handlePersonChange(e,t){this.formData[t.prop]=t.userName,this.formData[t.prop+"FullName"]=t.userFullName,this.isCcInfo?this.$emit("setBpmPerson",{ccParamList:[...this.tempPersonList,...this.hiddenPersonList]}):this.$emit("setBpmPerson",{approverParamList:[...this.tempPersonList,...this.hiddenPersonList]})},isFieldRequired(e){var t;return!0===(null===(t=this.formRules[e])||void 0===t?void 0:t.required)},validateField(e){if(!this.formRules[e])return!0;if(this.formRules[e].required){const t=this.tempPersonList.find((t=>t.prop===e));if(!(t&&t.id&&t.userFullName))return!1}return!0},validate(){let e=!0;for(const t in this.formRules)this.validateField(t)||(e=!1);return this.$emit("validate",e),e},validateFormItem(e){const{type:t,fields:n}=e;console.log("BpmPersonInfos validateFormItem 被调用:",{type:t,fields:n,personList:this.tempPersonList.length,formRules:this.formRules});for(const i of this.tempPersonList){const e=i.prop;if(console.log("处理字段:",{prop:e,hasRule:!!this.formRules[e],isRequired:!(!this.formRules[e]||!this.formRules[e].required),item:i}),this.formRules[e]&&this.formRules[e].required){const t=!n||n.includes(e);if(console.log("字段验证状态:",{prop:e,shouldValidate:t,fields:n}),t){const t=this.validateField(e);console.log("验证结果:",{prop:e,isValid:t,hasRegisterFn:!!this.registerValidationResult}),this.registerValidationResult&&this.registerValidationResult(e,{valid:t,message:t?"":`${i.label||e}不能为空，请选择`})}}}}}},[["render",function(e,t,n,i,m,f){const y=r("van-switch"),v=r("van-cell"),b=r("FormItemMultiplePerson"),w=r("FormItemPerson"),x=r("van-cell-group");return o(),l("div",ea,[g(e.$slots,"before",{},void 0,!0),m.tempPersonList.length>0?(o(),a(x,{key:0,border:!1},{default:s((()=>[(o(!0),l(p,null,c(m.tempPersonList,((e,n)=>(o(),l(p,{key:n},[m.isCcInfo?(o(),a(v,{key:0,center:""},{title:s((()=>t[0]||(t[0]=[u("span",{class:"label"},"是否抄送",-1)]))),"right-icon":s((()=>[h(y,{modelValue:e.isCc,"onUpdate:modelValue":t=>e.isCc=t,"active-value":0,"inactive-value":-1,size:"24"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)):d("",!0),m.isCcInfo&&0!==e.isCc&&"0"!==e.isCc?d("",!0):(o(),l(p,{key:1},[1===e.isMultiple?(o(),a(b,{key:0,readonly:f.getFieldReadonly(e.prop),required:f.isFieldRequired(e.prop),label:e.label,title:e.label,userName:e.userName,"onUpdate:userName":t=>e.userName=t,userFullname:e.userFullName,"onUpdate:userFullname":t=>e.userFullName=t,onChange:t=>f.handlePersonChange(t,e)},null,8,["readonly","required","label","title","userName","onUpdate:userName","userFullname","onUpdate:userFullname","onChange"])):(o(),a(w,{key:1,readonly:f.getFieldReadonly(e.prop),required:f.isFieldRequired(e.prop),label:e.label,title:e.label,userName:e.userName,"onUpdate:userName":t=>e.userName=t,userFullname:e.userFullName,"onUpdate:userFullname":t=>e.userFullName=t,id:e.id,"onUpdate:id":t=>e.id=t,onChange:t=>f.handlePersonChange(t,e)},null,8,["readonly","required","label","title","userName","onUpdate:userName","userFullname","onUpdate:userFullname","id","onUpdate:id","onChange"]))],64))],64)))),128))])),_:1})):d("",!0),g(e.$slots,"after",{},void 0,!0)])}],["__scopeId","data-v-87d93fe7"]]),BpmPersonView:I({name:"BpmPersonView",componentName:"BpmPersonView",props:{personList:{type:Array,default:()=>[]},title:{type:String,default:""}},data:()=>({tempPersonList:[],hiddenPersonList:[],formData:{},flowConfig:{}}),watch:{personList:{handler(e){if(e){let t=e.filter((e=>0!==e.isVisible));this.hiddenPersonList=e.filter((e=>0===e.isVisible)),this.tempPersonList=JSON.parse(JSON.stringify(t||[])),console.log("this.tempPersonList===>",this.tempPersonList)}else this.tempPersonList=[],this.hiddenPersonList=[]},immediate:!0,deep:!0}},methods:{formatDate(e){return e?this.$dayjs(e).format("YYYY-MM-DD HH:mm:ss"):""}}},[["render",function(e,t,n,i,u,m){const f=r("van-field"),y=r("van-cell-group");return o(),l("div",ta,[g(e.$slots,"before",{},void 0,!0),u.tempPersonList.length>0?(o(),a(y,{key:0,border:!1},{default:s((()=>[(o(!0),l(p,null,c(u.tempPersonList,((e,t)=>(o(),l(p,{key:t},[h(f,{label:e.label,modelValue:e.userFullName,"onUpdate:modelValue":t=>e.userFullName=t,readonly:""},null,8,["label","modelValue","onUpdate:modelValue"]),e.approvalTime?(o(),a(f,{key:0,label:e.approvalTimeLabel||"审核日期",value:m.formatDate(e.approvalTime),readonly:""},null,8,["label","value"])):d("",!0)],64)))),128))])),_:1})):d("",!0),g(e.$slots,"after",{},void 0,!0)])}],["__scopeId","data-v-d92c9a21"]])},props:{modelKey:String,formKey:String,entityName:String,detailParamList:{type:Array,default:()=>[]},detailEntityNameList:{type:Array,default:()=>[]},allTaskKeys:{type:Array,default:()=>[]},bpmPersonObj:{type:Object,default:()=>({approverConfigList:[],ccUserConfigList:[],approverParamList:[],ccParamList:[],initiatorInfo:null})},beforeSubmit:{type:Function,default:null},validationConfig:{type:Object,default:()=>({saveDraft:void 0,submit:void 0,reject:["comment"],abandon:["comment"]})}},emits:["afterSubmit","draftClick","submitClick","abandonClick","afterAbandon","rejectClick","afterReject","backSubmitClick","afterBackSubmit","copyCallBack","changeComment","setAllTaskKeys"],inject:["appReload2"],provide(){return{registerValidationResult:this.registerValidationResult,validateFormItem:this.validateFormItem}},setup(e,{attrs:t,slots:n,emit:i}){const[r,o]=$(C),[a,s]=$(R),[l,p]=$(P),[c,u]=$(k),[m,d]=$(M),[h,f]=$(T),[y,g]=$(N),[v,b]=$(D);return{requestFormData:r,loadingFormData:o,requestSubmitForm:a,loadingSubmitForm:s,requestProcessStart:l,loadingProcessStart:p,requestProcessUpdate:c,loadingProcessUpdate:u,requestProcessAbandon:m,loadingProcessAbandon:d,requestProcessReject:h,loadingProcessReject:f,requestUpdateExecutor:y,loadingUpdateExecutor:g,requestFormButtons:v,loadingFormButtons:b}},data:()=>({comment:"通过",fileToken:"",tabs:["详情","流转"],tab:"详情",buttonList:[],redirectPaths:{SafetyHiddenDangerNew:"SafetyHiddenDanger",UseSealApply:"UseSealApply",QualityThreat:"QualityThreat",QualityThreatSupervisor:"QualityThreat",QualityThreatNew:"QualityThreat",MeetFlow:"MeetingList"},validationResults:{}}),computed:{currentUser(){return this.$store.USER_INFO||{}},portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},saveData(){var e;return null===(e=this.$bizStore)||void 0===e||null===(e=e.tempData)||void 0===e?void 0:e[this.formKey]},pageQuery(){return this.$route.query||{}},taskId(){var e;return null===(e=this.pageQuery)||void 0===e?void 0:e.taskId},type(){var e;return null===(e=this.pageQuery)||void 0===e?void 0:e.type},bizId(){var e;return null===(e=this.pageQuery)||void 0===e?void 0:e.bizId},taskKey(){var e;return(null===(e=this.pageQuery)||void 0===e?void 0:e.taskKey)||""},formKey(){var e;return null===(e=this.pageQuery)||void 0===e?void 0:e.formKey},processInstanceId(){var e;return(null===(e=this.pageQuery)||void 0===e?void 0:e.processInstanceId)||""},redirectPathName(){return"add"===this.type?this.redirectPaths[this.formKey]:""},historyPath(){return this.$router.options.history.state.back}},watch:{taskId:{immediate:!1,handler(e,t){e&&e!=t&&this.getFormButtons()}}},created(){},mounted(){this.getFormButtons()},methods:{async copyFilling(){try{await this.$confirm({title:"提示",message:"是否复制上次提交的值?"}),this.portalId==this.saveData.portalId?this.$emit("copyCallBack",this.saveData):this.$showToast("门户不同不能复制")}catch(q){console.log(q)}},async onInit(e,t){try{this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const n={id:this.bizId,entityName:this.entityName,detailEntityNameList:this.detailEntityNameList},i=await this.requestFormData(e,n);t&&"function"==typeof t&&t(i)}catch(q){}finally{this.$closeToast()}},async getFormButtons(){try{var e;if("execute"!==this.type||!this.taskId)return;const t={taskId:this.taskId},n=await this.requestFormButtons(t),i=JSON.parse(n.buttonList);let r=[];r=null!=i&&null!==(e=i.taskButtonList)&&void 0!==e&&e.length?i.taskButtonList.sort(((e,t)=>this.sortButtonType(t.type)-this.sortButtonType(e.type))):null==i?void 0:i.sort(((e,t)=>this.sortButtonType(t.type)-this.sortButtonType(e.type))),r=r.filter((e=>["submit","saveDraft","reject","abandon"].includes(e.type))),this.buttonList=[...r]}catch(q){}},sortButtonType(e){switch(e){case"submit":return 1;case"saveDraft":return 2;case"reject":return 3;case"abandon":return 4;case"addCountersignee":return 5;case"edit":return 6;case"circulate":return 7;default:return 8}},buttonTypeStyle(e){switch(e){case"submit":return"primary";case"abandon":return"danger";default:return"default"}},async handleButtonClick(e){console.log("buttonInfo ==>",e);const{type:t,targetNode:n}=e;if(await this.validateForAction(e))switch(t){case"saveDraft":return void this.$emit("draftClick",{buttonInfo:e});case"submit":return void this.$emit("submitClick",{buttonInfo:e});case"abandon":return void this.onAbandon();case"reject":return void this.onReject(n);default:return void this.$showToast({message:"暂不支持该操作!"})}},async handleFormAction(e){const{url:t,formData:n,buttonInfo:i={type:e.actionType||"submit"},actionType:r="submit",confirmMsg:o="确定提交?",loadingMsg:a="正在提交...",successMsg:s="提交成功!"}=e,l="submit"===r;try{var p,c,u,m,d,h,f,y,g;await this.$confirm({title:"提示",message:o}),this.$showToast({type:"loading",loadingType:"spinner",forbidClick:!0,message:a,duration:0});let e={approverConfigList:null===(p=this.bpmPersonObj)||void 0===p?void 0:p.approverConfigList,ccUserConfigList:null===(c=this.bpmPersonObj)||void 0===c?void 0:c.ccUserConfigList,approverParamList:null===(u=this.bpmPersonObj)||void 0===u?void 0:u.approverParamList,ccParamList:null===(m=this.bpmPersonObj)||void 0===m?void 0:m.ccParamList,initiatorInfo:null===(d=this.bpmPersonObj)||void 0===d?void 0:d.initiatorInfo};console.log("this.beforeSubmit",this.beforeSubmit);let r={...n};if(this.beforeSubmit){const t=await this.beforeSubmit(i,e);if(!1===t)return;t&&(r=t)}r.notifyMethod?Array.isArray(r.notifyMethod)&&(r.notifyMethod=JSON.stringify(r.notifyMethod)):r.notifyMethod=JSON.stringify([0]);let _=JSON.parse(JSON.stringify(this.bpmPersonObj.approverParamList||[]));var v;Array.isArray(r.approverParamList)&&(_=_.map(((e,t)=>{let n=r.approverParamList.find((t=>t.prop===e.prop));return{...e,...n}})),delete r.approverParamList),null!==(h=this.bpmPersonObj)&&void 0!==h&&h.initiatorInfo&&_.push(null===(v=this.bpmPersonObj)||void 0===v?void 0:v.initiatorInfo);let S=JSON.parse(JSON.stringify((null===(f=this.bpmPersonObj)||void 0===f?void 0:f.ccParamList)||[]));Array.isArray(r.ccParamList)&&(S=S.map(((e,t)=>{let n=r.ccParamList.find((t=>t.prop===e.prop));return{...e,...n}})),delete r.ccParamList);const A={formKey:this.formKey,bizId:this.bizId,taskId:this.taskId,taskKey:this.taskKey,entityName:this.entityName,notifyMethod:r.notifyMethod};i.code===z._Object.saveDraft.code&&(r.processState=3);for(let t=0;t<_.length;t++){_[t]={..._[t],...A};let e=_[t];r[e.prop]=e.userName||""}for(let t=0;t<S.length;t++){S[t]={...S[t],...A};for(let n=0;n<(null===(b=this.bpmPersonObj)||void 0===b?void 0:b.ccParamList.length);n++){var b;if(this.bpmPersonObj.ccParamList[n].prop===S[t].prop){var w;null!==(w=this.bpmPersonObj)&&void 0!==w&&w.ccParamList[n].notifyMethod||(this.bpmPersonObj.ccParamList[n].notifyMethod=S[t].notifyMethod);break}}let e=S[t];r[e.prop]=e.userName||""}if(i.code===z._Object.submit.code&&Array.isArray(this.allTaskKeys)&&this.allTaskKeys.length>0)for(let t=0;t<this.allTaskKeys.length;t++){let e=this.allTaskKeys[t];Array.isArray(e)&&e.length>0&&this.taskKey===e[e.length-1]&&(_=[])}const C={entityName:this.entityName,entityObject:r,detailParamList:this.detailParamList,formProcessParam:this.useProcess(i,r),approverParamList:_,ccParamList:S};if(await this.requestSubmitForm(t,C),this.$emit("afterSubmit",i,C),this.$closeToast(),l&&"add"===this.type&&this.$bizStore.saveData({[this.formKey]:{...n}}),l&&null!==(y=this.bpmPersonObj)&&void 0!==y&&y.ccParamList&&(null===(g=this.bpmPersonObj)||void 0===g?void 0:g.ccParamList.length)>0)for(let t=0;t<(null===(x=this.bpmPersonObj)||void 0===x?void 0:x.ccParamList.length);t++){var x,E;const e=null===(E=this.bpmPersonObj)||void 0===E?void 0:E.ccParamList[t];this.ccSend(e.userName,e.userFullname,e.notifyMethod)}this.$nextTick((()=>{this.$showToast({message:s,icon:"success",forbidClick:!0,duration:1500}),setTimeout((()=>{this.routerJump()}),1500)}))}catch(q){console.log(q),this.$closeToast()}},async onSaveDraft(e,t,n={type:"saveDraft"}){await this.validateForAction(n)&&await this.handleFormAction({url:e,formData:t,buttonInfo:n,actionType:"saveDraft",confirmMsg:"确定暂存?",loadingMsg:"正在暂存...",successMsg:"暂存成功!"})},async onSubmit(e,t,n={type:"submit"}){await this.validateForAction(n)&&await this.handleFormAction({url:e,formData:t,buttonInfo:n,actionType:"submit",confirmMsg:"确定提交?",loadingMsg:"正在提交...",successMsg:"提交成功!"})},async onAbandon(){try{await this.$confirm({title:"提示",message:"确定废弃?"}),this.$showToast({type:"loading",loadingType:"spinner",forbidClick:!0,message:"正在废弃...",duration:0});const e={taskId:this.taskId,bizId:this.bizId,formKey:this.formKey,comment:this.comment||"",fileToken:this.fileToken||""};this.$emit("abandonClick",e),await this.requestProcessAbandon(e),this.$emit("afterAbandon"),this.$closeToast(),this.$nextTick((()=>{this.$showToast({message:"废弃成功!",icon:"success",forbidClick:!0,duration:1500}),setTimeout((()=>{this.routerJump()}),1500)}))}catch(q){console.log(q),this.$closeToast()}},async onReject(e){try{await this.$confirm({title:"提示",message:"确定退回?"}),this.$showToast({type:"loading",loadingType:"spinner",forbidClick:!0,message:"正在退回...",duration:0});const t={taskId:this.taskId,targetKey:e[0],comment:this.comment||"",fileToken:this.fileToken||""};this.$emit("rejectClick",t),await this.requestProcessReject(t),this.$emit("afterReject"),this.$closeToast(),this.$nextTick((()=>{this.$showToast({message:"退回成功!",icon:"success",forbidClick:!0,duration:1500}),setTimeout((()=>{this.routerJump()}),1500)}))}catch(q){console.log(q),this.$closeToast()}},async onBackSubmit(e,t){try{const n={entityName:this.entityName,detailParamList:this.detailParamList,entityObject:{...t}};this.$emit("backSubmitClick",n),await this.requestSubmitForm(e,n),this.$emit("afterBackSubmit")}catch(q){console.log(q)}},getComment(){return this.comment},setComment(e){this.comment=e},handleCommentChange(e){this.$emit("changeComment",e)},routerJump(){this.redirectPathName?this.$router.replace({name:this.redirectPathName}):"/Tasks"===this.historyPath?this.handleNextTask({page:1,size:10}):this.$router.back()},async handleNextTask({page:e=1,size:t=10}){try{const{list:n=[],nextPage:i,hasNextPage:r}=await O({page:e,size:t});if(0===n.length)return void this.$router.replace({name:"Tasks"});let o=n.find((e=>j.includes(e.formKey)));if(o){const e=this.$showLoadingToast({duration:2e3,forbidClick:!0,message:"3秒后进入下一条待办"});let t=3;const n=setInterval((()=>{if(t--,t)e.message=`${t}秒后进入下一条待办`;else{clearInterval(n),this.$closeToast();const e={path:`/FormCenter/${o.formKey}`,query:{type:"execute",taskKey:o.taskKey,formKey:o.formKey,bizId:o.formBizId,taskId:o.taskId,processInstanceId:o.processInstanceId,title:o.formName}};this.$bizStore.saveData({redirectParams:e}),this.$router.replace({name:"NextTaskRedirect"})}}),1e3)}else r?this.handleNextTask({page:i,size:t}):this.$router.replace({name:"Tasks"})}catch(q){console.log("catch next",q),this.$router.replace({name:"Tasks"}),this.appReload2()}},useProcess(e={},t={}){return{modelKey:this.modelKey,bizId:this.bizId,processInstanceId:this.processInstanceId,formKey:this.formKey,stageFlag:"saveDraft"===e.type?1:0,variable:t,taskId:this.taskId,approval:"saveDraft"===e.type?"stage":"",comment:this.comment||"",targetKey:e.targetKey||""}},setInitiatorInfo(e){this.bpmPersonObj.initiatorInfo=e},setBpmPerson(e){let{approverConfigList:t,ccUserConfigList:n,approverParamList:i,ccParamList:r,initiatorInfo:o}=e;t&&(this.bpmPersonObj.approverConfigList=t||[]),n&&(this.bpmPersonObj.ccUserConfigList=n||[]),i&&(this.bpmPersonObj.approverParamList=i||[]),r&&(this.bpmPersonObj.ccParamList=r||[]),o&&(this.bpmPersonObj.initiatorInfo=o)},setPropConfig(e=[]){if(Array.isArray(e)){var t,n;let i=e.filter((e=>1===e.type));this.bpmPersonObj.approverConfigList=null===(t=this.bpmPersonObj)||void 0===t?void 0:t.approverConfigList.map(((e,t)=>{let n=i.find((t=>t.prop===e.prop));return{...e,...n}})),this.bpmPersonObj.ccUserConfigList=null===(n=this.bpmPersonObj)||void 0===n?void 0:n.ccUserConfigList.map(((t,n)=>{let i=e.find((e=>e.prop===t.prop));return{...t,...i}}))}},async ccSend(e,t,n){if(e)try{var i,r,o,a;let s=JSON.stringify([0]);Array.isArray(n)&&(s=JSON.stringify(n));const l={formBizId:this.bizId,formKey:this.formKey,processInstanceId:this.processInstanceId,processType:"1",receiverUserName:e,receiverUserFullName:t,notify:s,senderUserName:(null===(i=this.currentUser)||void 0===i?void 0:i.userName)||"",senderUserFullName:(null===(r=this.currentUser)||void 0===r?void 0:r.userFullname)||"",taskId:this.taskId,taskKey:this.taskKey,title:(null===(o=this.pageQuery)||void 0===o?void 0:o.title)||"",sourceId:this.portalId||"",sourceName:(null===(a=this.portal)||void 0===a?void 0:a.name)||""};A(l).then((e=>{console.log(`${this.taskKey} 抄送成功！`,l)}))}catch(q){console.error("抄送失败",q)}},setAllTaskKeys(e){this.allTaskKeys=e},validateForAction(e){console.log("validateForAction 方法被调用:",e);const t=e.type,n=this.validationConfig[t];this.validationResults={};const i=[this.$refs.initiatorInfos,this.$refs.personInfos].filter(Boolean);console.log("验证组件情况:",{buttonInfo:e,fieldsToValidate:n,initiatorInfos:!!this.$refs.initiatorInfos,personInfos:!!this.$refs.personInfos,componentsToValidate:i.length,componentsList:i});for(const r of i)null!=r&&r.validateFormItem&&r.validateFormItem({type:t,fields:n});return new Promise((e=>{setTimeout((()=>{const t=Object.values(this.validationResults);if(t.some((e=>!e.valid))){const n=t.find((e=>!e.valid));n&&n.message?this.$showToast({message:n.message}):this.$showToast({message:"表单验证未通过，请检查必填项"}),e(!1)}else e(!0)}),100)}))},registerValidationResult(e,t){this.validationResults[e]=t}}},ia={class:"form-wp"},ra={class:"buttons"},oa={class:"form-wp"},aa={class:"buttons"},sa={class:"form-wp"};e("F",I(na,[["render",function(e,t,n,i,y,b){const E=r("Navbar"),_=r("van-tab"),S=r("van-tabs"),A=r("BpmInitiatorInfos"),C=r("BpmPersonInfos"),R=r("van-form"),P=r("van-button"),k=r("BpmPersonView"),M=r("van-field"),T=r("van-cell-group"),N=r("FlowTimeline"),D=r("FlowBmp");return o(),l(p,null,[h(E,{back:"",title:b.pageQuery.title},{right:s((()=>[g(e.$slots,"NavbarRight",{},(()=>[b.saveData&&b.portalId==b.saveData.portalId&&"add"===b.type?(o(),l("span",{key:0,class:"color-[#fff]",onClick:t[0]||(t[0]=(...e)=>b.copyFilling&&b.copyFilling(...e))},"复制填写")):d("",!0)]),!0)])),_:3},8,["title"]),"add"!==b.type?(o(),a(S,{key:0,class:"tabs-wp",active:y.tab,"onUpdate:active":t[1]||(t[1]=e=>y.tab=e),"line-width":"4em"},{default:s((()=>[(o(!0),l(p,null,c(y.tabs,(e=>(o(),a(_,{class:"px-[20px]",key:e,name:e,title:e},null,8,["name","title"])))),128))])),_:1},8,["active"])):d("",!0),u("div",{class:v(["view-height",{"has-tab":"add"!==b.type}])},["add"===b.type?(o(),l(p,{key:0},[u("div",ia,[g(e.$slots,"default",{},void 0,!0),h(R,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:s((()=>[g(e.$slots,"initiator-infos",{},(()=>[h(A,{ref:"initiatorInfos",info:n.bpmPersonObj.initiatorInfo,onSetInitiatorInfo:b.setInitiatorInfo},null,8,["info","onSetInitiatorInfo"])]),!0),g(e.$slots,"person-section",{},(()=>[g(e.$slots,"approver-select",{},(()=>[n.bpmPersonObj.approverConfigList.length>0?(o(),a(C,{key:0,ref:"personInfos",type:1,"person-list":n.bpmPersonObj.approverConfigList,onSetBpmPerson:b.setBpmPerson},null,8,["person-list","onSetBpmPerson"])):d("",!0)]),!0),g(e.$slots,"cc-select",{},(()=>[n.bpmPersonObj.ccUserConfigList.length>0?(o(),a(C,{key:0,ref:"personInfosCc",type:2,"person-list":n.bpmPersonObj.ccUserConfigList,onSetBpmPerson:b.setBpmPerson},null,8,["person-list","onSetBpmPerson"])):d("",!0)]),!0)]),!0)])),_:3},512)]),u("div",ra,[h(P,{class:"button",round:"",block:"",plain:"",type:"primary",onClick:t[2]||(t[2]=t=>e.$emit("draftClick"))},{default:s((()=>t[5]||(t[5]=[f(" 暂存 ")]))),_:1,__:[5]}),h(P,{class:"button",round:"",block:"",type:"primary",onClick:t[3]||(t[3]=t=>e.$emit("submitClick"))},{default:s((()=>t[6]||(t[6]=[f(" 提交 ")]))),_:1,__:[6]})])],64)):(o(),l(p,{key:1},[w(u("div",oa,[g(e.$slots,"default",{},void 0,!0),h(R,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:s((()=>[g(e.$slots,"initiator-infos",{},(()=>[h(A,{ref:"initiatorInfos",info:n.bpmPersonObj.initiatorInfo,onSetInitiatorInfo:b.setInitiatorInfo},null,8,["info","onSetInitiatorInfo"])]),!0),g(e.$slots,"person-section",{},(()=>["view"!==b.type?(o(),l(p,{key:0},[g(e.$slots,"approver-select",{},(()=>[n.bpmPersonObj.approverConfigList.length>0?(o(),a(C,{key:0,ref:"personInfos",type:1,"person-list":n.bpmPersonObj.approverConfigList,onSetBpmPerson:b.setBpmPerson},null,8,["person-list","onSetBpmPerson"])):d("",!0)]),!0),g(e.$slots,"cc-select",{},(()=>[n.bpmPersonObj.ccUserConfigList.length>0?(o(),a(C,{key:0,ref:"personInfosCc",type:2,"person-list":n.bpmPersonObj.ccUserConfigList,onSetBpmPerson:b.setBpmPerson},null,8,["person-list","onSetBpmPerson"])):d("",!0)]),!0)],64)):d("",!0),"view"===b.type?(o(),l(p,{key:1},[g(e.$slots,"approver-view",{},(()=>[n.bpmPersonObj.approverParamList.length>0?(o(),a(k,{key:0,ref:"personView",title:"审批人","person-list":n.bpmPersonObj.approverParamList},null,8,["person-list"])):d("",!0)]),!0),g(e.$slots,"cc-view",{},(()=>[n.bpmPersonObj.ccParamList.length>0?(o(),a(k,{key:0,ref:"personViewCc",title:"抄送人","person-list":n.bpmPersonObj.ccParamList},null,8,["person-list"])):d("",!0)]),!0)],64)):d("",!0)]),!0)])),_:3},512),"execute"===b.type?(o(),l(p,{key:0},["UserTask_0"!==b.taskKey?(o(),a(R,{key:0,ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:s((()=>[h(T,{border:!1,class:"mt[10px]"},{default:s((()=>[h(M,{label:"处理意见",modelValue:y.comment,"onUpdate:modelValue":[t[4]||(t[4]=e=>y.comment=e),b.handleCommentChange],rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入处理意见",required:!0,"input-align":"left",readonly:"view"===b.type},null,8,["modelValue","readonly","onUpdate:modelValue"])])),_:1})])),_:1},512)):d("",!0),u("div",aa,[(o(!0),l(p,null,c([...y.buttonList],((e,t)=>(o(),a(P,{key:t,class:"button",round:"",block:"",plain:"submit"!==e.type,type:b.buttonTypeStyle(e.type),onClick:t=>b.handleButtonClick(e)},{default:s((()=>[f(m(e.text),1)])),_:2},1032,["plain","type","onClick"])))),128))])],64)):d("",!0)],512),[[x,"详情"===y.tab]]),w(u("div",sa,[h(N,{"biz-id":b.bizId,"task-id":b.taskId},null,8,["biz-id","task-id"]),h(D,{show:"流转"===y.tab,"model-key":n.modelKey,"biz-id":b.bizId},null,8,["show","model-key","biz-id"])],512),[[x,"流转"===y.tab]])],64))],2)],64)}],["__scopeId","data-v-e6f48463"]]))}}}));
