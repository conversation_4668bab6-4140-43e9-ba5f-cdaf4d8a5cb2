System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,t){"use strict";var a,l,n,s,o,r,i,d,m,c,u,p,f;return{setters:[e=>{a=e.F,l=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,r=e.X,i=e.V,d=e.k,m=e.U,c=e.Y,u=e.S,p=e.W,f=e.F},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const t={class:"one-line"},b={class:"form-info"},y={class:"form-table"},h={class:"center"},g={class:"attachment-desc"},j={class:"footer-input"},D={class:"form-info"};e("default",n({name:"JL23",components:{FormTemplate:a,DocumentPart:l},emits:[],props:{},setup(e,{attrs:t,slots:a,emit:l}){},data:()=>({detailTable:[{},{},{},{},{},{},{},{},{},{},{},{}],attachmentDesc:"施工图纸核查意见（应由核查监理人员签字）。"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:t}){},onBeforeSubmit:({formData:e,detailParamList:t},a)=>new Promise(((e,t)=>{try{e()}catch(a){t(a)}}))}},[["render",function(e,a,l,n,v,w){const k=s("van-field"),x=s("DocumentPart"),P=s("FormTemplate");return o(),r(P,{ref:"FormTemplate",nature:"图核","on-after-init":w.onAfterInit,"on-before-submit":w.onBeforeSubmit,"detail-table":v.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:v.attachmentDesc},{default:i((({formData:e,formTable:l,baseObj:n,uploadAccept:s,taskStart:r,taskComment2:j,taskComment3:D,taskComment4:v})=>[d(x,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!r},{default:i((()=>[m("div",t,[a[0]||(a[0]=m("span",{style:{"padding-left":"2em"}},"经对以下图纸（共",-1)),m("span",b,c(e.field1),1),a[1]||(a[1]=m("span",null,"张）核查意见如下：",-1))]),m("div",y,[m("table",null,[a[2]||(a[2]=m("thead",null,[m("tr",null,[m("th",{colspan:"1",rowspan:"1"},"序号"),m("th",{colspan:"1",rowspan:"1"},"施工设计图纸名称"),m("th",{colspan:"1",rowspan:"1"},"图号"),m("th",{colspan:"1",rowspan:"1"},"核查人员"),m("th",{colspan:"1",rowspan:"1"},"备注")])],-1)),m("tbody",null,[(o(!0),u(f,null,p(l||[],((e,t)=>(o(),u("tr",{key:t},[m("td",h,c(t+1),1),m("td",null,c(e.field1),1),m("td",null,c(e.field2),1),m("td",null,c(e.field3),1),m("td",null,c(e.field4),1)])))),128))])])]),m("div",g,[a[3]||(a[3]=m("div",null,"附件：",-1)),d(k,{modelValue:e.attachmentDesc,"onUpdate:modelValue":t=>e.attachmentDesc=t,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!r},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:i((({formData:e,formTable:t,baseObj:l,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:r,taskComment4:i})=>[m("div",j,[a[4]||(a[4]=m("span",null,"说明：1、本表一式",-1)),m("span",D,c(e.num1),1),a[5]||(a[5]=m("span",null,"份，由监理机构填写并存档。",-1))]),a[6]||(a[6]=m("div",{class:"footer-input"},[m("div",{style:{"text-indent":"3em"}},[m("span",null,"2、各图号可以是单张号、连续号或区间号。")])],-1))])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
