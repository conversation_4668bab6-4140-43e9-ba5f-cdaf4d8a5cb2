System.register(["./index-legacy-09188690.js","./wbsUtil-legacy-542349ac.js","./constants-legacy-a38f15af.js","./form-legacy-20b7cd9d.js","./verder-legacy-e6127216.js","./FormItemPicker-legacy-fd45c24d.js","./sift-legacy-1dc85988.js","./constants-legacy-82bb4fe6.js","./vant-legacy-b51a9379.js","./array-legacy-2920c097.js"],(function(e,t){"use strict";var s,a,i,n,o,r,l,c,d,h,u,p,m,v,f,g,y,b,w,k,L,_,I,C;return{setters:[e=>{s=e.h,a=e._,i=e.A},e=>{n=e.a,o=e.g},e=>{r=e.R},e=>{l=e.d},e=>{c=e.Q,d=e.R,h=e.S,u=e.k,p=e.V,m=e.U,v=e.Y,f=e.B,g=e.a2,y=e.X,b=e.F,w=e.W,k=e.Z,L=e.y},e=>{_=e.F},e=>{I=e.S},e=>{C=e.R},null,null],execute:function(){var t=document.createElement("style");t.textContent=".task-item[data-v-8780bee6]{background-color:#fff;border-radius:1.33333vw;padding:2.66667vw;margin-bottom:2.66667vw}.task-item .body[data-v-8780bee6]{padding:2.66667vw 1.33333vw 1.33333vw;position:relative}.task-item .body .item-info[data-v-8780bee6]{display:flex;flex-direction:row;align-items:center;font-size:3.73333vw;color:#9a9a9a;line-height:1;padding:1.33333vw 0}.task-item .body .item-info>.key[data-v-8780bee6]{min-width:5em}.task-item .body .item-info>.value[data-v-8780bee6]{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#333}.task-item .body .right[data-v-8780bee6]{position:absolute;top:1.33333vw;right:1.06667vw}.task-item .body .right .tag[data-v-8780bee6]{padding:1.6vw 3.2vw;text-align:center}.van-button--disabled[data-v-8780bee6]{background-color:#1d2129;border:none}.page[data-v-0a4e37ca]{height:100%}.view-height[data-v-633316ff]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height) - var(--sab));padding-top:2.66667vw;overflow-x:hidden;overflow-y:scroll}.view-height.no-tabbar[data-v-633316ff]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--sab) - 12vw)}.btn-group[data-v-633316ff]{width:100%;padding:0 4vw;box-sizing:border-box;display:flex;justify-content:space-between;gap:0 4vw;margin:2.66667vw 0}.btn-group>button[data-v-633316ff]{flex:1}\n",document.head.appendChild(t);const j={class:"body"},x={class:"item-info header"},S={class:"value"},T={class:"item-info"},P={class:"value"},E={class:"item-info"},$={class:"value"},M={class:"item-info"},R={class:"value"},N={class:"item-info"},A={class:"value"},D={class:"right"},F={key:0,class:"p-[10px]"},O={class:"btn-group"};e("default",a({name:"QualityInspectionCorrection",components:{FormItemPicker:_,List:a({name:"List",components:{ListItem:a({name:"ListItem",components:{},props:{item:{type:Object,default:()=>({})},wbsList:{type:Object,default:()=>({})}},emits:[],setup(e,{attrs:t,slots:s,emit:a}){},data:()=>({statusMap:r}),watch:{},created(){},computed:{statusTypeMap(){var e;return(null===(e=this.statusMap)||void 0===e?void 0:e.TYPE_MAP)||{}},statusLabelMap(){var e;return(null===(e=this.statusMap)||void 0===e?void 0:e.LABEL_MAP)||{}},user(){return this.$store.USER_INFO}},mounted(){},methods:{getWbsLabel:n,handelDel(){let e={formKey:"QualityInspectionCorrection",entityName:"QualityInspectionCorrection",detailEntityNameList:[]};this.$confirm({title:"提示",message:`确认删除${this.item.correctionNumber}?`}).then((()=>{l(i.VUE_APP_TCS_API_SERVICENAME,this.item.id,e).then((e=>{this.$emit("delSuccess")}))})).catch((()=>{}))},toFormCenter(){const{id:e,formKey:t,taskId:s,taskKey:a,processInstanceId:i,formName:n}=this.item;this.$router.push({path:"/FormCenter/QualityInspectionCorrection",query:{type:this.user.userName===this.item.createBy&&3===this.item.processState?"execute":"view",taskKey:a,formKey:"QualityInspectionCorrection",bizId:e,taskId:s,processInstanceId:i,title:"质量检查问题整改流程"}})}}},[["render",function(e,t,s,a,i,n){const o=c("van-tag"),r=c("van-button"),l=c("van-swipe-cell");return d(),h("div",{class:"task-item",onClick:t[0]||(t[0]=g((e=>n.toFormCenter()),["stop","prevent"]))},[u(l,null,{right:p((()=>[u(r,{square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:n.user.userName!==s.item.createBy||![i.statusMap.PENDING_REVIEW,i.statusMap.STAGE].includes(s.item.rectificationStatus),onClick:n.handelDel},null,8,["disabled","onClick"])])),default:p((()=>[m("div",j,[m("div",x,[t[1]||(t[1]=m("span",{class:"key"},"整改单号",-1)),m("span",S,v(s.item.correctionNumber),1)]),m("div",T,[t[2]||(t[2]=m("span",{class:"key"},"所属标段",-1)),m("span",P,v(e.$formatLabel(s.item.sectionId,e.$DICT_CODE.project_section)),1)]),m("div",E,[t[3]||(t[3]=m("span",{class:"key"},"整改部位",-1)),m("span",$,v(n.getWbsLabel(s.wbsList,s.item.projectPosition)),1)]),m("div",M,[t[4]||(t[4]=m("span",{class:"key"},"整改期限",-1)),m("span",R,v(e.$dayjs(s.item.inspectionDate).format("YYYY-MM-DD")),1)]),m("div",N,[t[5]||(t[5]=m("span",{class:"key"},"整改内容",-1)),m("span",A,v(s.item.description),1)]),m("div",D,[u(o,{class:"tag",color:n.statusTypeMap[s.item.rectificationStatus],plain:"",size:"medium"},{default:p((()=>[f(v(n.statusLabelMap[s.item.rectificationStatus]),1)])),_:1},8,["color"])])])])),_:1})])}],["__scopeId","data-v-8780bee6"]])},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(e,{attrs:t,slots:s,emit:a}){},data:()=>({loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20},wbsList:[]}),computed:{},watch:{},created(){},mounted(){this.onLoadList(),this.getWbsList()},methods:{async getWbsList(){this.wbsList=await o(null,!0,this.$store.PORTAL)},onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams,...this.search};1===e.page&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await function(e){return s({url:"project-yjp-tcs/quality/qualityInspection/correction/page",method:"get",params:e})}(e),a=this.searchParams.page<=1?[]:this.list||[];this.list.length>=t.total&&(this.finished=!0),this.list=[...a,...t.records],this.searchParams.page++}catch(e){this.finished=!0}finally{setTimeout((()=>{this.loading=!1,this.$closeToast()}),100)}}}},[["render",function(e,t,s,a,i,n){const o=c("ListItem"),r=c("van-empty"),l=c("van-list"),m=c("van-pull-refresh");return d(),y(m,{modelValue:i.refreshing,"onUpdate:modelValue":t[1]||(t[1]=e=>i.refreshing=e),onRefresh:n.onRefresh},{default:p((()=>[u(l,{loading:i.loading,"onUpdate:loading":t[0]||(t[0]=e=>i.loading=e),finished:i.finished,"finished-text":i.list&&i.list.length?"没有更多了":"",onLoad:n.onLoadList,"immediate-check":!1},{default:p((()=>[i.list&&i.list.length?(d(!0),h(b,{key:0},w(i.list||[],((e,t)=>(d(),y(o,{key:e.id,item:e,wbsList:i.wbsList,onDelSuccess:n.onRefresh},null,8,["item","wbsList","onDelSuccess"])))),128)):(d(),h(b,{key:1},[i.loading?k("",!0):(d(),h("div",F,[u(r,{description:"暂无数据"})]))],64))])),_:1},8,["loading","finished","finished-text","onLoad"])])),_:1},8,["modelValue","onRefresh"])}],["__scopeId","data-v-0a4e37ca"]])},props:{},emits:[],setup(e,{attrs:t,slots:s,emit:a}){},data:()=>({search:{correctionNumber:"",sectionId:"",rectificationStatus:""},Sift:I,showTop:!1,statusOptions:Object.entries(C.LABEL_MAP).map((([e,t])=>({value:e,label:t})))}),watch:{},mounted(){},methods:{handleAdd(){this.$router.push({path:"/FormCenter/QualityInspectionCorrection",query:{type:"add",title:"新增质量整改",taskKey:"UserTask_0"}})},handleQuery(){this.showTop=!1,this.$nextTick((()=>{this.$refs.List&&this.$refs.List.onRefresh(this.search)}))},handleResetting(){this.search={correctionNumber:"",sectionId:"",rectificationStatus:""},this.handleQuery()}}},[["render",function(e,t,s,a,i,n){const o=c("van-icon"),r=c("Navbar"),l=c("FormItemPicker"),v=c("van-button"),y=c("van-popup"),w=c("List");return d(),h(b,null,[u(r,{back:""},{right:p((()=>[u(o,{name:i.Sift,size:"2em",onClick:t[0]||(t[0]=g((e=>i.showTop=!i.showTop),["stop","prevent"]))},null,8,["name"])])),_:1}),u(y,{show:i.showTop,"onUpdate:show":t[3]||(t[3]=e=>i.showTop=e),position:"top"},{default:p((()=>[u(l,{value:i.search.sectionId,"onUpdate:value":t[1]||(t[1]=e=>i.search.sectionId=e),"dict-name":e.$DICT_CODE.project_section,placeholder:"选择所属标段"},null,8,["value","dict-name"]),u(l,{value:i.search.rectificationStatus,"onUpdate:value":t[2]||(t[2]=e=>i.search.rectificationStatus=e),placeholder:"选择流转状态",columns:[...i.statusOptions],"columns-field-names":{text:"label",value:"value",children:"none"}},null,8,["value","columns"]),m("div",O,[u(v,{round:"",type:"primary",plain:"",onClick:g(n.handleQuery,["stop","prevent"])},{default:p((()=>t[5]||(t[5]=[f("查询")]))),_:1,__:[5]},8,["onClick"]),u(v,{round:"",plain:"",onClick:g(n.handleResetting,["stop","prevent"])},{default:p((()=>t[6]||(t[6]=[f("重置")]))),_:1,__:[6]},8,["onClick"])])])),_:1},8,["show"]),m("div",{class:L(["view-height",{"no-tabbar":e.envFeishu}])},[u(w,{ref:"List",search:i.search},null,8,["search"])],2),u(v,{type:"primary",size:"normal",style:{width:"100%"},onClick:t[4]||(t[4]=e=>n.handleAdd())},{default:p((()=>t[7]||(t[7]=[f("新增整改")]))),_:1,__:[7]})],64)}],["__scopeId","data-v-633316ff"]]))}}}));
