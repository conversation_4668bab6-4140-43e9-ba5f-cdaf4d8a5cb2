System.register(["./index-legacy-f817ae93.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,t){"use strict";var l,a,n,s,o,d,c,m,i,u,p,r,f;return{setters:[e=>{l=e.F,a=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,d=e.X,c=e.V,m=e.k,i=e.U,u=e.Y,p=e.S,r=e.W,f=e.F},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const t={class:"one-line"},b={class:"form-info"},y={class:"cb-table"},v={colspan:"2"},D={class:"cell"},h={class:"cell",style:{"text-align":"center"}},g={class:"cell"},j={class:"cell"},V={class:"attachment-desc"},k={class:"comment-wp"},w={class:"textarea-wp"},C={class:"footer-input"},P={class:"form-info"},x={class:"form-info"},L={class:"form-info"},N={class:"form-info"};e("default",n({name:"CB15",components:{FormTemplate:l,DocumentPart:a},emits:[],props:{},setup(e,{attrs:t,slots:l,emit:a}){},data:()=>({detailTable:[],attachmentDesc:"",tableList:[{content:"施工技术交底和安全交底情况"},{content:"主要施工设备到位情况"},{content:"施工安全、质量保证措施落实情况"},{content:"工程设备检查和验收情况"},{content:"原材料,中间产品质量及准备情况"},{content:"现场施工人员安排情况"},{content:"风、水、电等必须的辅助生产设施准备情况"},{content:"场地平整、交通、临时设施准备情况"},{content:"测量放样情况"},{content:"工艺试验情况"}]}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:t}){},onBeforeSubmit:({formData:e,detailParamList:t,taskComment3:l},a)=>new Promise(((e,t)=>{try{e()}catch(l){t(l)}}))}},[["render",function(e,l,a,n,O,F){const T=s("van-field"),_=s("DocumentPart"),S=s("FormTemplate");return o(),d(S,{ref:"FormTemplate",nature:"分开工","on-after-init":F.onAfterInit,"on-before-submit":F.onBeforeSubmit,"detail-table":O.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:O.attachmentDesc},{default:c((({formData:e,formTable:a,baseObj:n,uploadAccept:s,taskStart:d,taskComment2:C,taskComment3:P,taskComment4:x,taskComment5:L})=>[m(_,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:n.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!d},{default:c((()=>[i("div",t,[i("span",b,u(e.field1),1),l[0]||(l[0]=i("span",null,"已具备开工条件，",-1)),l[1]||(l[1]=i("span",null,"施工准备已就绪，",-1)),l[2]||(l[2]=i("span",null,"请贵方审批。",-1))]),i("table",y,[i("tbody",null,[i("tr",null,[l[3]||(l[3]=i("th",{colspan:"2",style:{width:"25%"}},[i("div",{class:"cell"},"申请开工分部工程名称、编码")],-1)),i("td",v,[i("div",D,u(e.field2),1)])]),l[4]||(l[4]=i("tr",null,[i("th",{rowspan:"11"},[i("div",{class:"cell"},"承包人施工准备工作自检记录")]),i("td",null,[i("div",{class:"cell"},"序号")]),i("td",null,[i("div",{class:"cell"},"检查内容")]),i("td",null,[i("div",{class:"cell"},"检查结果")])],-1)),(o(!0),p(f,null,r(O.tableList||[],((t,l)=>(o(),p("tr",{key:l},[i("td",null,[i("div",h,u(l+1),1)]),i("td",null,[i("div",g,u(t.content),1)]),i("td",null,[i("div",j,u(e[`field${l+3}`]),1)])])))),128))])]),i("div",V,[l[5]||(l[5]=i("div",null,"附件：",-1)),m(T,{modelValue:e.attachmentDesc,"onUpdate:modelValue":t=>e.attachmentDesc=t,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),m(_,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!d},{default:c((()=>[i("div",k,[l[6]||(l[6]=i("div",null,"EPC总承包项目部意见：",-1)),i("div",w,[m(T,{modelValue:e.comment2,"onUpdate:modelValue":t=>e.comment2=t,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),m(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!d},{default:c((()=>l[7]||(l[7]=[i("div",{class:"comment-wp"},[i("div",null,"审核后另行批复。")],-1)]))),_:2,__:[7]},1032,["deptValue","deptOptions","disabled"])])),footer:c((({formData:e,formTable:t,baseObj:a,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:d,taskComment4:c,taskComment5:m})=>[i("div",C,[l[8]||(l[8]=i("span",null,"说明：本表一式",-1)),i("span",P,u(e.num1),1),l[9]||(l[9]=i("span",null,"份，由承包人填写，监理机构签收后，发包人",-1)),i("span",x,u(e.num2),1),l[10]||(l[10]=i("span",null,"份，监理机构",-1)),i("span",L,u(e.num3),1),l[11]||(l[11]=i("span",null,"份，承包人",-1)),i("span",N,u(e.num4),1),l[12]||(l[12]=i("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
