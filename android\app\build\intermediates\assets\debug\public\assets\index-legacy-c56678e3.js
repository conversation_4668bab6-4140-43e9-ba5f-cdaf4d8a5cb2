System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(l,t){"use strict";var e,a,n,s,o,r,c,d,i,u,m,f;return{setters:[l=>{e=l.F,a=l.D},l=>{n=l._},l=>{s=l.Q,o=l.R,r=l.X,c=l.V,d=l.U,i=l.Y,u=l.S,m=l.W,f=l.F},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const t={class:"jl-table"},p={class:"cell"},h={class:"cell"},b={class:"form-table"},y={class:"center"};l("default",n({name:"JL28",components:{FormTemplate:e,DocumentPart:a},emits:[],props:{},setup(l,{attrs:t,slots:e,emit:a}){},data:()=>({detailTable:[{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:l,detailParamList:t}){},onBeforeSubmit:({formData:l,detailParamList:t},e)=>new Promise(((l,t)=>{try{l()}catch(e){t(e)}}))}},[["render",function(l,e,a,n,g,j){const w=s("FormTemplate");return o(),r(w,{ref:"FormTemplate",nature:"平行","on-after-init":j.onAfterInit,"on-before-submit":j.onBeforeSubmit,"detail-table":g.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:g.attachmentDesc},{default:c((({formData:l,formTable:a,baseObj:n,uploadAccept:s,taskStart:r,taskComment2:c,taskComment3:g,taskComment4:j})=>[d("table",t,[d("tbody",null,[d("tr",null,[e[0]||(e[0]=d("th",null,[d("div",{class:"cell"},"单位工程名称及编号")],-1)),d("td",null,[d("div",p,i(l.projectName),1)])]),d("tr",null,[e[1]||(e[1]=d("th",null,[d("div",{class:"cell"},"承包人")],-1)),d("td",null,[d("div",h,i(l.field1),1)])])])]),d("div",b,[d("table",null,[e[2]||(e[2]=d("thead",null,[d("tr",null,[d("th",{colspan:"1",rowspan:"1"},"序号"),d("th",{colspan:"1",rowspan:"1"},"检测项目"),d("th",{colspan:"1",rowspan:"1"},"对应单元工程编号"),d("th",{colspan:"1",rowspan:"1"},"桩号"),d("th",{colspan:"1",rowspan:"1"},"高程"),d("th",{colspan:"1",rowspan:"1"},"代表数量"),d("th",{colspan:"1",rowspan:"1"},"组数"),d("th",{colspan:"1",rowspan:"1"},"取样人"),d("th",{colspan:"1",rowspan:"1"},"送样人"),d("th",{colspan:"1",rowspan:"1"},"送样时间"),d("th",{colspan:"1",rowspan:"1"},"检测机构"),d("th",{colspan:"1",rowspan:"1"},"检测结果"),d("th",{colspan:"1",rowspan:"1"},"检测报告编号")])],-1)),d("tbody",null,[(o(!0),u(f,null,m(a||[],((l,t)=>(o(),u("tr",{key:t},[d("td",y,i(t+1),1),d("td",null,i(l.field1),1),d("td",null,i(l.field2),1),d("td",null,i(l.field3),1),d("td",null,i(l.field4),1),d("td",null,i(l.field5),1),d("td",null,i(l.field6),1),d("td",null,i(l.field7),1),d("td",null,i(l.field8),1),d("td",null,i(l.field9),1),d("td",null,i(l.field10),1),d("td",null,i(l.field11),1),d("td",null,i(l.field12),1)])))),128))])])])])),footer:c((({formData:l,formTable:t,baseObj:a,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:r,taskComment4:c})=>e[3]||(e[3]=[d("div",{class:"footer-input"},[d("span",null," 备注：委托单、平行检测送样台帐、平行检测报告台帐要相互对应。 ")],-1)]))),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
