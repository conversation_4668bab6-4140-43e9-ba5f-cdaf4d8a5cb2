System.register(["./index-legacy-f817ae93.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,l){"use strict";var a,n,t,o,d,s,m,p,u,r,i,c,f;return{setters:[e=>{a=e.F,n=e.D},e=>{t=e._},e=>{o=e.Q,d=e.R,s=e.X,m=e.V,p=e.k,u=e.U,r=e.Y,i=e.S,c=e.W,f=e.F},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const l={class:"one-line"},b={class:"form-info"},y={class:"form-info"},V={class:"form-info"},h={class:"form-info"},D={class:"form-info"},g={class:"attachment-desc"},w={class:"comment-wp"},U={class:"textarea-wp"},v={class:"form-table"},j={class:"center"},k={class:"comment-wp"},x={class:"textarea-wp"},C={class:"footer-input"},P={class:"form-info"},N={class:"form-info"},L={class:"form-info"},O={class:"form-info"};e("default",t({name:"CB32",components:{FormTemplate:a,DocumentPart:n},emits:[],props:{},setup(e,{attrs:l,slots:a,emit:n}){},data:()=>({detailTable:[{},{},{},{},{}],attachmentDesc:"人员工作明细\n材料使用明细\n施工设备使用明细"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:l}){},onBeforeSubmit:({formData:e,detailParamList:l,taskComment3:a},n)=>new Promise(((e,l)=>{try{e()}catch(a){l(a)}}))}},[["render",function(e,a,n,t,F,T){const S=o("van-field"),_=o("DocumentPart"),z=o("FormTemplate");return d(),s(z,{ref:"FormTemplate",nature:"计签","on-after-init":T.onAfterInit,"on-before-submit":T.onBeforeSubmit,"detail-table":F.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:F.attachmentDesc},{default:m((({formData:e,formTable:n,baseObj:t,uploadAccept:o,taskStart:s,taskComment2:C,taskComment3:P,taskComment4:N,taskComment5:L})=>[p(_,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:t.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!s},{default:m((()=>[u("div",l,[a[0]||(a[0]=u("span",{style:{"padding-left":"2em"}},"我方按计日工工作通知",-1)),a[1]||(a[1]=u("span",null,"（监理 [ ",-1)),u("span",b,r(e.field1),1),a[2]||(a[2]=u("span",null,"] 计通",-1)),u("span",y,r(e.field2),1),a[3]||(a[3]=u("span",null,"号）",-1)),a[4]||(a[4]=u("span",null,"实施了下列所列项目，",-1)),a[5]||(a[5]=u("span",null,"现按施工合同约定申报 ",-1)),u("span",V,r(e.field3),1),a[6]||(a[6]=u("span",null,"年",-1)),u("span",h,r(e.field4),1),a[7]||(a[7]=u("span",null,"月",-1)),u("span",D,r(e.field5),1),a[8]||(a[8]=u("span",null,"日的计日工程量，",-1)),a[9]||(a[9]=u("span",null,"请贵方审核。",-1))]),u("div",g,[a[10]||(a[10]=u("div",null,"附件：",-1)),p(S,{modelValue:e.attachmentDesc,"onUpdate:modelValue":l=>e.attachmentDesc=l,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),p(_,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:t.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!s},{default:m((()=>[u("div",w,[a[11]||(a[11]=u("div",null,"EPC总承包项目部意见：",-1)),u("div",U,[p(S,{modelValue:e.comment2,"onUpdate:modelValue":l=>e.comment2=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),u("div",v,[u("table",null,[a[12]||(a[12]=u("thead",null,[u("tr",null,[u("th",{colspan:"1",rowspan:"1"},"序号"),u("th",{colspan:"1",rowspan:"1"},"工程项目名称"),u("th",{colspan:"1",rowspan:"1"},"计日工内容"),u("th",{colspan:"1",rowspan:"1"},"单位"),u("th",{colspan:"1",rowspan:"1"},"申报工程量"),u("th",{colspan:"1",rowspan:"1"},"核准工程量"),u("th",{colspan:"1",rowspan:"1"},"说明")])],-1)),u("tbody",null,[(d(!0),i(f,null,c(n||[],((e,l)=>(d(),i("tr",{key:l},[u("td",j,r(l+1),1),u("td",null,[p(S,{modelValue:e.field1,"onUpdate:modelValue":l=>e.field1=l,label:"",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])]),u("td",null,[p(S,{modelValue:e.field2,"onUpdate:modelValue":l=>e.field2=l,label:"",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])]),u("td",null,[p(S,{modelValue:e.field3,"onUpdate:modelValue":l=>e.field3=l,label:"",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])]),u("td",null,[p(S,{modelValue:e.field4,"onUpdate:modelValue":l=>e.field4=l,label:"",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])]),u("td",null,[p(S,{modelValue:e.field5,"onUpdate:modelValue":l=>e.field5=l,label:"",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])]),u("td",null,[p(S,{modelValue:e.field6,"onUpdate:modelValue":l=>e.field6=l,label:"",placeholder:"",readonly:!C},null,8,["modelValue","onUpdate:modelValue","readonly"])])])))),128))])])]),p(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:t.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!s},{default:m((()=>[u("div",k,[a[13]||(a[13]=u("div",null,"审核意见：",-1)),u("div",x,[p(S,{modelValue:e.comment3,"onUpdate:modelValue":l=>e.comment3=l,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!P},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:m((({formData:e,formTable:l,baseObj:n,uploadAccept:t,taskStart:o,taskComment2:d,taskComment3:s,taskComment4:m,taskComment5:p})=>[u("div",C,[a[14]||(a[14]=u("span",null,"说明：本表一式",-1)),u("span",P,r(e.num1),1),a[15]||(a[15]=u("span",null,"份，由承包人每个工作日完成后填写，经监理机构审核后，",-1)),a[16]||(a[16]=u("span",null,"发包人",-1)),u("span",N,r(e.num2),1),a[17]||(a[17]=u("span",null,"份，监理机构",-1)),u("span",L,r(e.num3),1),a[18]||(a[18]=u("span",null,"份，退返承包人",-1)),u("span",O,r(e.num4),1),a[19]||(a[19]=u("span",null,"份。",-1)),a[20]||(a[20]=u("span",null,"作结算时使用。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
