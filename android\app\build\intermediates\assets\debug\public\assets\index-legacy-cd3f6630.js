System.register(["./index-legacy-f817ae93.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var t,l,n,o,s,d,m,p,i,r;return{setters:[e=>{t=e.F,l=e.D},e=>{n=e._},e=>{o=e.Q,s=e.R,d=e.X,m=e.V,p=e.k,i=e.U,r=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"one-line"},u={class:"form-info"},c={class:"form-info"},f={class:"form-info"},b={class:"comment-wp"},y={class:"textarea-wp"},V={class:"attachment-desc"},D={class:"comment-wp"},h={class:"textarea-wp"},v={class:"comment-wp"},g={class:"textarea-wp"},j={class:"footer-input"},w={class:"form-info"},x={class:"form-info"},k={class:"form-info"};e("default",n({name:"CB38",components:{FormTemplate:t,DocumentPart:l},emits:[],props:{},setup(e,{attrs:a,slots:t,emit:l}){},data:()=>({detailTable:[],attachmentDesc:"1、\n2、"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit:({formData:e,detailParamList:a,taskComment3:t},l)=>new Promise(((e,a)=>{try{e()}catch(t){a(t)}}))}},[["render",function(e,t,l,n,C,P){const N=o("van-field"),U=o("DocumentPart"),L=o("FormTemplate");return s(),d(L,{ref:"FormTemplate",nature:"确认","on-after-init":P.onAfterInit,"on-before-submit":P.onBeforeSubmit,"detail-table":C.detailTable,"is-show-confirm1":!1,attachmentDesc:C.attachmentDesc},{default:m((({formData:e,formTable:l,baseObj:n,uploadAccept:o,taskStart:s,taskComment2:d,taskComment3:j,taskComment4:w,taskComment5:x})=>[p(U,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:n.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",a,[t[0]||(t[0]=i("span",{style:{"padding-left":"2em"}},"按照贵方审核通过的关于",-1)),i("span",u,r(e.field1),1),t[1]||(t[1]=i("span",null,"的工作计划",-1)),t[2]||(t[2]=i("span",null,"（回复单编号：承包 [",-1)),i("span",c,r(e.field2),1),t[3]||(t[3]=i("span",null,"] 回复",-1)),i("span",f,r(e.field3),1),t[4]||(t[4]=i("span",null,"号，",-1)),t[5]||(t[5]=i("span",null,"或监理文件编号），",-1)),t[6]||(t[6]=i("span",null,"我方已完成相关工作，执行情况如下，",-1)),t[7]||(t[7]=i("span",null,"请贵方确认。",-1))]),i("div",b,[i("div",y,[p(N,{modelValue:e.field4,"onUpdate:modelValue":a=>e.field4=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"（完成情况说明）",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),i("div",V,[t[8]||(t[8]=i("div",null,"附件：",-1)),p(N,{modelValue:e.attachmentDesc,"onUpdate:modelValue":a=>e.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),p(U,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",D,[t[9]||(t[9]=i("div",null,"EPC总承包项目部意见：",-1)),i("div",h,[p(N,{modelValue:e.comment2,"onUpdate:modelValue":a=>e.comment2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),p(U,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!s},{default:m((()=>[i("div",v,[t[10]||(t[10]=i("div",null,"审核意见：",-1)),i("div",g,[p(N,{modelValue:e.comment3,"onUpdate:modelValue":a=>e.comment3=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!j},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:m((({formData:e,formTable:a,baseObj:l,uploadAccept:n,taskStart:o,taskComment2:s,taskComment3:d,taskComment4:m,taskComment5:p})=>[i("div",j,[t[11]||(t[11]=i("span",null,"注：1、本表一式",-1)),i("span",w,r(e.num1),1),t[12]||(t[12]=i("span",null,"份，由承包人填写，监理机构确认后，承包人",-1)),i("span",x,r(e.num2),1),t[13]||(t[13]=i("span",null,"份，监理机构",-1)),i("span",k,r(e.num3),1),t[14]||(t[14]=i("span",null,"份。",-1))]),t[15]||(t[15]=i("div",{class:"footer-input",style:{"text-indent":"2em"}}," 2、本表主要用于承包人对监理机构发出的监理通知、指示的执行情况确认。 ",-1))])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
