System.register(["./index-legacy-09188690.js","./index-legacy-b580af71.js","./FormItemPicker-legacy-fd45c24d.js","./index-legacy-645a3645.js","./FormItemPerson-legacy-e6e57748.js","./verder-legacy-e6127216.js"],(function(e,t){"use strict";var a,o,r,i,s,n,m,l,d,p,c,f,h,u,v,b,y,g,k,D,w;return{setters:[e=>{a=e.h,o=e._},e=>{r=e.F},e=>{i=e.F},e=>{s=e.U},e=>{n=e.F},e=>{m=e.Q,l=e.R,d=e.X,p=e.V,c=e.k,f=e.U,h=e.Y,u=e.a1,v=e.S,b=e.F,y=e.B,g=e.y,k=e.Z,D=e.W,w=e.A}],execute:function(){var t=document.createElement("style");t.textContent='.form-papper[data-v-8fb09741]{font-size:3.73333vw;padding:1em}.form-papper .form-title[data-v-8fb09741]{position:relative;overflow:hidden;text-align:center;margin-bottom:1em}.form-papper .form-title .code[data-v-8fb09741]{position:absolute;left:0;top:0}.form-papper .form-title .name[data-v-8fb09741]{font-size:4.26667vw;font-weight:600;padding-bottom:1.33333vw}.form-papper .form-title .archive[data-v-8fb09741]{display:flex;align-items:center;justify-content:center}.form-papper .form-title .archive>span[data-v-8fb09741]{padding:0 1.33333vw}.form-papper .form-body[data-v-8fb09741]{margin:2.66667vw 0;border:1px solid #111}.form-papper .form-body.hide-border[data-v-8fb09741]{border:none}.form-papper .form-target[data-v-8fb09741]{padding:2.66667vw;padding-bottom:0}.form-papper .form-footer[data-v-8fb09741]{margin-bottom:6.4vw}.form-papper .attach-file[data-v-8fb09741]{display:flex;margin:5.33333vw 0 2.66667vw 6.4vw}.form-papper[data-v-8fb09741] .form-info{font-weight:700;text-decoration:underline;padding:0 1.33333vw;word-break:break-all}.form-papper[data-v-8fb09741] .textarea-wp{border:1px solid var(--van-border-color);font-size:3.73333vw;margin:1.33333vw 0}.form-papper[data-v-8fb09741] .textarea-wp .van-cell{padding:.53333vw 1.33333vw;font-size:3.73333vw;line-height:1.2}.form-papper[data-v-8fb09741] .one-line,.form-papper .form-footer[data-v-8fb09741]{margin-bottom:2.66667vw}.form-papper .form-footer[data-v-8fb09741] .footer-intro{display:flex;flex-direction:row}.form-papper .form-footer[data-v-8fb09741] .footer-intro>span{word-break:break-all}.form-papper .form-footer[data-v-8fb09741] .footer-intro>span:first-child{width:3em;flex-shrink:0}.form-papper .form-footer[data-v-8fb09741] .footer-input .form-info{font-weight:400}.form-papper .form-footer[data-v-8fb09741] .footer-input .form-info:before,.form-papper .form-footer[data-v-8fb09741] .footer-input .form-info:after{content:" "}.form-papper .footer-form[data-v-8fb09741]{margin:2.66667vw -3.73333vw}.form-papper[data-v-8fb09741] .comment-wp{margin-bottom:2.66667vw}.form-papper[data-v-8fb09741] .form-table{margin:2.66667vw 0}.form-papper[data-v-8fb09741] .form-table table{table-layout:fixed;width:100%;border-collapse:collapse;border-spacing:0}.form-papper[data-v-8fb09741] .form-table table th,.form-papper[data-v-8fb09741] .form-table table td{color:var(--van-text-color);font-weight:400;border:1px solid var(--van-cell-border-color);padding:1.06667vw;line-height:1.2;min-height:1.2em;height:1.2em;word-break:break-word}.form-papper[data-v-8fb09741] .form-table table th.center,.form-papper[data-v-8fb09741] .form-table table td.center{text-align:center}.form-papper[data-v-8fb09741] .jl-table{table-layout:fixed;width:100%;border-collapse:collapse;border-spacing:0}.form-papper[data-v-8fb09741] .jl-table th,.form-papper[data-v-8fb09741] .jl-table td{color:var(--van-text-color);border:1px solid #111;text-align:center;font-weight:400}.form-papper[data-v-8fb09741] .jl-table th .cell,.form-papper[data-v-8fb09741] .jl-table td .cell{padding:1.06667vw;line-height:1.2;min-height:1em;word-break:break-word}.form-papper[data-v-8fb09741] .jl-table td .cell{text-align:left}.form-papper[data-v-8fb09741] .jl-table .table-sign .sign-item{display:flex;flex-direction:row;align-items:center}.form-papper[data-v-8fb09741] .jl-table .table-sign .sign-item .label{text-align:justify;text-align-last:justify;position:relative;padding-right:1em}.form-papper[data-v-8fb09741] .jl-table .table-sign .sign-item .label:after{position:absolute;right:0;content:":";text-align:left;text-align-last:left}.form-papper[data-v-8fb09741] .check-input{--van-checkbox-size: 3.73333vw;--van-checkbox-disabled-label-color: #333;--van-checkbox-disabled-icon-color: #333;--van-checkbox-disabled-background: #fff}.form-papper[data-v-8fb09741] .check-wp{--van-checkbox-label-color: #333;--van-checkbox-size: 3.73333vw;--van-checkbox-disabled-label-color: #333;--van-checkbox-disabled-icon-color: #333;--van-checkbox-disabled-background: #fff;display:inline-flex;flex-direction:row;align-items:center;position:relative;padding-right:1.06667vw}.form-papper[data-v-8fb09741] .check-wp .van-checkbox{margin-left:2.66667vw}.form-papper[data-v-8fb09741] .attachment-desc .van-cell.van-field{padding-left:0;padding-right:0}.document-part[data-v-35a918cc]{padding:2.66667vw}.document-part+.document-part[data-v-35a918cc]{border-top:1px solid #111}.document-part .part-sign[data-v-35a918cc]{padding:2.66667vw 0 2.66667vw 2.66667vw}.document-part .part-sign .ps-item[data-v-35a918cc]{display:flex;flex-direction:row;width:100%}.document-part .part-sign .ps-item .label[data-v-35a918cc]{width:11em;text-align:right;flex-shrink:0}.document-part .part-sign .ps-item .value[data-v-35a918cc]{flex:1}\n',document.head.appendChild(t);const C={name:"JLTemplate",components:{FlowForm:r,FormItemPicker:i,UploadFiles:s,FormItemPerson:n},emits:[],props:{nature:String,isShowConfirm1:{type:Boolean,default:!1},isShowConfirm2:{type:Boolean,default:!1},isShowConfirm3:{type:Boolean,default:!1},nums:{type:Array,default:()=>[]},detailTable:{type:Array,default:()=>[]},showTarget:{type:Boolean,default:!0},customerHeader:Boolean,titleFill:Boolean,employerTarget:{type:Boolean,default:!1},noApprover:Boolean,hideBorder:Boolean,attachmentDesc:String,onAfterInit:{type:Function,default:()=>()=>{}},onBeforeSubmit:{type:Function,default:()=>Promise.reject()},onAfterSubmit:{type:Function,default:()=>Promise.resolve()}},setup(e,{attrs:t,slots:a,emit:o}){},data(){return{service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/form/commit"},detailEntityNameList:["TechnologyFilesExtend1","TechnologyFilesExtend2"],detailParamList:[],uploadAccept:".jpg,.jpeg,.png,.JPG,.JPEG,.dwg,.DWG,.rar,.RAR,.zip,.ZIP,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx",formData:{menuCode:"jl",id:void 0,portalId:void 0,fileType:this.$route.query.formKey||this.$route.name,sendingType:this.$route.query.sendingType,sendingTypeCode:this.$route.query.sendingTypeCode,sendingName:"",sendingCode:"",modelType:"监理文件",prjDepCode:"",prjDepName:"",subProjectId:"",subProjectCode:"",subProjectName:"",createBy:"",userFullname:"",userTelephoneNum:"",annualDate:this.$dayjs().format("YYYY"),titleCode:"",projectName:"",projectNameCode:"",contractCode:"",constructionName:"",constructionDeptName:"",supervisionName:"",supervisionDeptName:"",supervisionSimpleName:"",informationSimpleName:"",informationDeptName:"",epcName:"",epcDeptName:"",epcDeptNameLine:"",employerName:"",comment1:"",comment2:"",comment3:"",comment4:"",comment5:"",comment6:"",comment7:"",comment8:"",comment9:"",comment10:"",approverUsername1:"",approverFullname1:"",approverUnit1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",approverUsername3:"",approverFullname3:"",approverUnit3:"",approverUsername4:"",approverFullname4:"",approverUnit4:"",approverUsername5:"",approverFullname5:"",approverUnit5:"",approverUsername6:"",approverFullname6:"",approverUnit6:"",approverUsername7:"",approverFullname7:"",approverUnit7:"",approverUsername8:"",approverFullname8:"",approverUnit8:"",approverUsername9:"",approverFullname9:"",approverUnit9:"",approverUsername10:"",approverFullname10:"",approverUnit10:"",duplicateUsername1:"",duplicateUsername2:"",duplicateUsername3:"",duplicateUsername4:"",updateDate:this.$dayjs().format("YYYY-MM-DD HH:mm:ss"),filingPath:"",notifyMethod:"[]",processState:"0",routerPath:"",num1:this.nums[0]||"",num2:this.nums[1]||"",num3:this.nums[2]||"",num4:this.nums[3]||"",num5:this.nums[4]||"",num6:this.nums[5]||"",isconfirm1:this.isShowConfirm1,isconfirm2:this.isShowConfirm2,isconfirm3:this.isShowConfirm3,contentAttachment:"",otherAttachment:"",attachmentDesc:this.attachmentDesc||"",time1:"",time2:"",time3:"",time4:"",time5:"",time6:"",time7:"",time8:"",time9:"",time10:"",field1:void 0,field2:void 0,field3:void 0,field4:void 0,field5:void 0,field6:void 0,field7:void 0,field8:void 0,field9:void 0,field10:void 0,field11:void 0,field12:void 0,field13:void 0,field14:void 0,field15:void 0,field16:void 0,field17:void 0,field18:void 0,field19:void 0,field20:void 0,check1:void 0,check2:void 0,check3:void 0,check4:void 0,check5:void 0,check6:void 0,check7:void 0,check8:void 0,check9:void 0,check10:void 0,check11:void 0,check12:void 0,check13:void 0,check14:void 0,check15:void 0,check16:void 0,check17:void 0,check18:void 0,check19:void 0,check20:void 0},formTable:[...this.detailTable],baseData:{},approverLoading:!1,approverList:[]}},computed:{formKey(){return this.$route.name||""},modelKey(){var e,t;return null!==(e=this.formKey)&&void 0!==e&&e.includes("Branch")?null===(t=this.formKey.replace("JL","technology_JL"))||void 0===t?void 0:t.replace("Branch","_branch_process"):`technology_${this.formKey}`},entityName(){var e;return null!==(e=this.formKey)&&void 0!==e&&e.includes("Branch")?"TechnologyFilesBranch":"TechnologyFiles"},type(){var e;return(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"view"},taskKey(){var e;return(null===(e=this.$route.query)||void 0===e?void 0:e.taskKey)||""},isBranch(){return this.formKey.includes("Branch")},taskStart(){return"view"!=this.type&&!this.isBranch&&("add"==this.type||"execute"==this.type&&"UserTask_0"==this.taskKey)},taskComment2(){return"execute"==this.type&&("UserTask_1"===this.taskKey||"UserTask_4"===this.taskKey)},taskComment3(){return"execute"==this.type&&"UserTask_2"===this.taskKey},taskComment4(){return"execute"==this.type&&"UserTask_3"===this.taskKey},portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},baseObj(){var e;return this.formData.subProjectId&&(null===(e=this.baseData)||void 0===e?void 0:e[this.formData.subProjectId])||{}},canApprover(){const{id:e,subProjectId:t}=this.formData||{};return!!e||!(!this.modelKey||!t)}},watch:{"formData.comment2"(e){this.taskComment2&&this.setComment(e)},"formData.comment3"(e){this.taskComment3&&this.setComment(e)},"formData.comment4"(e){this.taskComment4&&this.setComment(e)},"formData.annualDate":{immediate:!0,handler(e){let t=e||this.$dayjs().format("YYYY");this.formData.filingPath=`\\${this.formData.modelType||"监理文件"}\\文件汇总\\${t}年度\\${this.formData.sendingType||"监理文件"}`}},"formData.subProjectId":{immediate:!0,handler(e){this.canApprover?this.getProcessApprover():this.approverList=[]}}},created(){},mounted(){this.initForm()},methods:{async getBaseData(){try{const t={subProjectId:"",sendingType:this.formData.sendingType},o=await(e=t,a({url:"/cybereng-technology/common/getProcessBaseData",method:"get",params:e}));this.baseData=o||{}}catch(t){console.log(t)}var e},async initForm(){if(await this.getBaseData(),"add"===this.type){if(this.formData.portalId=this.portalId,1!=this.portal.type){const e=this.subProjectList.find((e=>e.portalId==this.portal.id));this.subProjectIdChange(e)}const{userName:e="",userFullname:t="",phone:a,orgList:o=[]}=this.user||{},r=o.find((e=>{var t;return e.portalId==(null===(t=this.portal)||void 0===t?void 0:t.id)}))||o[0],i=(null==r?void 0:r.name)||"",s=(null==r?void 0:r.orgNo)||"";this.formData.createBy=e,this.formData.userFullname=t,this.formData.userTelephoneNum=a,this.formData.prjDepName=i,this.formData.prjDepCode=s}else this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{var t,a;const{entityObject:o,detailParamList:r=[]}=e;this.detailParamList=r;const i=r.find((e=>"TechnologyFilesExtend1"===e.detailEntityName))||{},s=null==i||null===(t=i.detailEntityArray)||void 0===t?void 0:t[0],n={};for(let l=1;l<=20;l++)n[`check${l}`]=null==s?void 0:s[`check${l}`],n[`field${l}`]=null==s?void 0:s[`field${l}`];this.formData={...this.formData,...n,...o};const m=null===(a=r.find((e=>"TechnologyFilesExtend2"===e.detailEntityName)))||void 0===a?void 0:a.detailEntityArray;this.formTable=m||[],this.taskComment2&&o.comment2&&this.setComment(o.comment2),this.taskComment3&&o.comment3&&this.setComment(o.comment3),this.taskComment4&&o.comment4&&this.setComment(o.comment4),this.onAfterInit&&this.onAfterInit({formData:this.formData,detailParamList:r})}))}))},chengeSubProject(e){var t,a,o,r,i,s,n,m,l,d,p,c,f;this.formData.subProjectId=e.id,this.formData.subProjectName=e.nodeName,this.formData.subProjectCode=e.nodeCode,this.formData.contractCode=null===(t=this.baseObj.contractCode)||void 0===t?void 0:t[0],this.formData.projectName=null===(a=this.baseObj.projectName)||void 0===a?void 0:a[0],this.formData.constructionName=null===(o=this.baseObj.constructionName)||void 0===o?void 0:o[0],this.formData.constructionDeptName=null===(r=this.baseObj.constructionDeptName)||void 0===r?void 0:r[0],this.formData.epcName=null===(i=this.baseObj.epcName)||void 0===i?void 0:i[0],this.formData.epcDeptName=null===(s=this.baseObj.epcDeptName)||void 0===s?void 0:s[0],this.formData.epcDeptNameLine=null===(n=this.baseObj.epcDeptNameLine)||void 0===n?void 0:n[0],this.formData.supervisionName=null===(m=this.baseObj.supervisionName)||void 0===m?void 0:m[0],this.formData.supervisionDeptName=null===(l=this.baseObj.supervisionDeptName)||void 0===l?void 0:l[0],this.formData.supervisionSimpleName=null===(d=this.baseObj.supervisionSimpleName)||void 0===d?void 0:d[0],this.formData.employerName=null===(p=this.baseObj.employerName)||void 0===p?void 0:p[0],this.formData.informationSimpleName=null===(c=this.baseObj.informationSimpleName)||void 0===c?void 0:c[0],this.formData.informationDeptName=null===(f=this.baseObj.informationDeptName)||void 0===f?void 0:f[0]},handleSupervisionSimpleNameChange(e){this.formData.supervisionSimpleName=e;const t=(this.baseObj.supervisionSimpleName||[]).findIndex((t=>t===e))||0,a=this.baseObj.supervisionName||[],o=this.baseObj.supervisionDeptName||[];this.formData.supervisionName=a[t],this.formData.supervisionDeptName=o[t]},handleInformationSimpleNameChange(e){this.formData.informationSimpleName=e;const t=(this.baseObj.informationSimpleName||[]).findIndex((t=>t===e))||0,a=this.baseObj.informationDeptName||[];this.formData.informationDeptName=a[t]},async getProcessApprover(){if(!this.isBranch&&!this.noApprover)try{this.approverLoading=!0;const e={...this.formData,formKey:this.formKey,processKey:this.modelKey},t=await function(e){return a({url:`/fawkes-ext/bpmConfig/process/getApprover/${e.processKey}`,method:"post",data:e})}(e);this.approverList=t||[],this.taskStart&&t.forEach((e=>{this.formData[e.fieldName]=e.fieldValue,this.formData[e.fullnameFieldName]=e.fullNameFieldValue}))}catch(e){console.log(e)}finally{this.approverLoading=!1}},updateDetailParamList(){var e;const t=[],a=(null===(e=this.detailParamList.find((e=>"TechnologyFilesExtend1"===e.detailEntityName)))||void 0===e||null===(e=e.detailEntityArray)||void 0===e?void 0:e[0])||{};for(let o=1;o<=20;o++)a[`check${o}`]=this.formData[`check${o}`],a[`field${o}`]=this.formData[`field${o}`];t.push({detailEntityName:"TechnologyFilesExtend1",detailEntityArray:[{...a}]}),t.push({detailEntityName:"TechnologyFilesExtend2",detailEntityArray:[...this.formTable]}),this.detailParamList=t},emitBeforeSubmit(e,t){return new Promise((async(a,o)=>{try{this.updateDetailParamList(),await this.onBeforeSubmit({formData:e,detailParamList:this.detailParamList,taskStart:this.taskStart,taskComment:this.taskComment2,taskComment3:this.taskComment3,taskComment4:this.taskComment4},t),a()}catch(r){o(r)}}))},async onDraft(){try{const e={...this.formData,rectifyState:"暂存"};await this.emitBeforeSubmit(e,"saveDraft"),this.$nextTick((()=>{this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}))}catch(e){console.log(e)}},async onSubmit(){try{try{await this.$refs.form.validate()}catch(e){return void this.$showNotify({type:"primary",message:"请完善表单内容!"})}switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_2":this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_3":this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_4":this.formData.time4=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_5":this.formData.time5=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_6":this.formData.time6=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_7":this.formData.time7=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_8":this.formData.time8=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_9":this.formData.time9=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_10":this.formData.time10=this.$dayjs().format("YYYY-MM-DD HH:mm:ss")}if(!this.isBranch&&!this.noApprover){if(!this.canApprover)return this.$showToast({message:"该业务流程未选择审批人!"}),reject(!1),!1;if(!this.approverList||!this.approverList.length)return this.$showToast({message:"该业务流程未配置审批人!"}),reject(!1),!1}const t={...this.formData};await this.emitBeforeSubmit(t,"submit"),this.$nextTick((()=>{this.$refs.FlowForm.onSubmit(this.service.submit,t)}))}catch(t){console.log(t)}},async onReject(){try{const e={...this.formData};await this.emitBeforeSubmit(e,"reject"),this.$nextTick((()=>{this.$refs.FlowForm.onBackSubmit(this.service.submit,e)}))}catch(e){console.log(e)}},afterSubmit(e,t){this.updateFiles(),this.onAfterSubmit(e,t)},async updateFiles(){return new Promise((async(e,t)=>{try{this.$refs.otherAttachment&&await this.$refs.otherAttachment.update(),this.$refs.contentAttachment&&await this.$refs.contentAttachment.update(),e()}catch(a){t()}}))},getComment(){var e;return null===(e=this.$refs)||void 0===e||null===(e=e.FlowForm)||void 0===e?void 0:e.getComment()},setComment(e){var t;null===(t=this.$refs)||void 0===t||null===(t=t.FlowForm)||void 0===t||t.setComment(e)},onCommentChange(e){this.taskComment2&&(this.formData.comment2=e),this.taskComment3&&(this.formData.comment3=e),this.taskComment4&&(this.formData.comment4=e)}}},j={class:"form-papper"},N={class:"form-title"},x={class:"code"},S={class:"name"},F={class:"archive"},T={key:0},$={key:1,class:"form-header"},_={style:{"margin-top":"5px"}},P={key:0,class:"form-target"},U={class:"one-line"},Y={key:0,class:"form-info"},A={key:1,class:"form-info"},L={class:"form-content"},B={class:"form-footer"},I={class:"footer-form"},O={class:"person-picker"};e("F",o(C,[["render",function(e,t,a,o,r,i){const s=m("FormItemPicker"),n=m("van-cell-group"),w=m("van-radio"),C=m("van-radio-group"),E=m("van-field"),H=m("UploadFiles"),M=m("FormItemPerson"),K=m("van-cell"),V=m("van-form"),q=m("FlowForm");return l(),d(q,{ref:"FlowForm","model-key":i.modelKey,"form-key":i.formKey,"entity-name":i.entityName,"detail-param-list":r.detailParamList,"detail-entity-name-list":r.detailEntityNameList,onChangeComment:i.onCommentChange,onDraftClick:i.onDraft,onSubmitClick:i.onSubmit,onAfterSubmit:i.afterSubmit,onRejectClick:i.onReject},{default:p((()=>[c(V,{ref:"form","label-width":"7em","input-align":"left","error-message-align":"right"},{default:p((()=>[c(n,{border:!1},{default:p((()=>[c(s,{label:"子工程","input-align":"right",value:r.formData.subProjectId,"onUpdate:value":t[0]||(t[0]=e=>r.formData.subProjectId=e),text:r.formData.subProjectName,"onUpdate:text":t[1]||(t[1]=e=>r.formData.subProjectName=e),columns:[...i.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:1!=i.portal.type||!i.taskStart,onChange:i.chengeSubProject},null,8,["value","text","columns","readonly","onChange"])])),_:1}),c(n,{border:!1},{default:p((()=>[f("div",j,[f("div",N,[f("div",x,h(r.formData.fileType),1),f("div",S,[a.titleFill?u(e.$slots,"title",{key:0,formData:r.formData,formTable:r.formTable,baseObj:i.baseObj,uploadAccept:r.uploadAccept,taskStart:i.taskStart,taskComment2:i.taskComment2,taskComment3:i.taskComment3,taskComment4:i.taskComment4},void 0,!0):(l(),v(b,{key:1},[y(h(r.formData.sendingType),1)],64))]),f("div",F,[t[6]||(t[6]=f("span",null,"（ ",-1)),f("div",null,h(r.formData.supervisionSimpleName),1),t[7]||(t[7]=f("span",null,"[ ",-1)),f("div",null,h(r.formData.annualDate),1),t[8]||(t[8]=f("span",null,"]",-1)),f("span",null,h(a.nature),1),f("div",null,h(r.formData.titleCode),1),t[9]||(t[9]=f("span",null,"号 ）",-1))])]),a.customerHeader?(l(),v("div",T,[u(e.$slots,"header",{formData:r.formData,formTable:r.formTable,baseObj:i.baseObj,uploadAccept:r.uploadAccept,taskStart:i.taskStart,taskComment2:i.taskComment2,taskComment3:i.taskComment3,taskComment4:i.taskComment4},void 0,!0)])):(l(),v("div",$,[f("div",null,[t[10]||(t[10]=f("span",null,"合同名称：",-1)),f("span",null,h(r.formData.epcName),1)]),f("div",_,[t[11]||(t[11]=f("span",null,"合同编号：",-1)),f("span",null,h(r.formData.contractCode),1)])])),f("div",{class:g(["form-body",{"hide-border":a.hideBorder}])},[a.showTarget?(l(),v("div",P,[f("div",U,[t[12]||(t[12]=f("span",null,"致：",-1)),a.employerTarget?k("",!0):(l(),v("span",Y,h(r.formData.epcDeptNameLine),1)),a.employerTarget?(l(),v("span",A,h(r.formData.employerName),1)):k("",!0)])])):k("",!0),f("div",L,[u(e.$slots,"default",{formData:r.formData,formTable:r.formTable,baseObj:i.baseObj,uploadAccept:r.uploadAccept,taskStart:i.taskStart,taskComment2:i.taskComment2,taskComment3:i.taskComment3,taskComment4:i.taskComment4},void 0,!0)])],2),f("div",B,[u(e.$slots,"footer",{formData:r.formData,formTable:r.formTable,baseObj:i.baseObj,uploadAccept:r.uploadAccept,taskStart:i.taskStart,taskComment2:i.taskComment2,taskComment3:i.taskComment3,taskComment4:i.taskComment4},void 0,!0)])])])),_:3}),c(n,{border:!1},{default:p((()=>[f("div",I,[a.isShowConfirm1?(l(),d(E,{key:0,name:"radio",label:"是否监理总监审批：","label-width":"14em","input-align":"right"},{input:p((()=>[c(C,{modelValue:r.formData.isconfirm1,"onUpdate:modelValue":t[2]||(t[2]=e=>r.formData.isconfirm1=e),direction:"horizontal",disabled:!i.taskStart},{default:p((()=>[c(w,{name:!0},{default:p((()=>t[13]||(t[13]=[y("是")]))),_:1,__:[13]}),c(w,{name:!1},{default:p((()=>t[14]||(t[14]=[y("否")]))),_:1,__:[14]})])),_:1},8,["modelValue","disabled"])])),_:1})):k("",!0),a.isShowConfirm2?(l(),d(E,{key:1,name:"radio",label:"是否总包项目经理审批：","label-width":"14em","input-align":"right"},{input:p((()=>[c(C,{modelValue:r.formData.isconfirm2,"onUpdate:modelValue":t[3]||(t[3]=e=>r.formData.isconfirm2=e),direction:"horizontal",disabled:!i.taskStart},{default:p((()=>[c(w,{name:!0},{default:p((()=>t[15]||(t[15]=[y("是")]))),_:1,__:[15]}),c(w,{name:!1},{default:p((()=>t[16]||(t[16]=[y("否")]))),_:1,__:[16]})])),_:1},8,["modelValue","disabled"])])),_:1})):k("",!0),a.isShowConfirm3?(l(),d(E,{key:2,name:"radio",label:"是否标段项目经理审批：","label-width":"14em","input-align":"right"},{input:p((()=>[c(C,{modelValue:r.formData.isconfirm3,"onUpdate:modelValue":t[4]||(t[4]=e=>r.formData.isconfirm3=e),direction:"horizontal",disabled:!i.taskStart},{default:p((()=>[c(w,{name:!0},{default:p((()=>t[17]||(t[17]=[y("是")]))),_:1,__:[17]}),c(w,{name:!1},{default:p((()=>t[18]||(t[18]=[y("否")]))),_:1,__:[18]})])),_:1},8,["modelValue","disabled"])])),_:1})):k("",!0),c(E,{label:"附件上传：","label-align":"top","input-align":"left"},{input:p((()=>[c(H,{ref:"otherAttachment",g9s:r.formData.otherAttachment,"onUpdate:g9s":t[5]||(t[5]=e=>r.formData.otherAttachment=e),accept:r.uploadAccept,multiple:!0,"max-count":9,"max-size":52428800,readonly:!i.taskStart},null,8,["g9s","accept","readonly"])])),_:1})])])),_:1}),i.isBranch||a.noApprover?k("",!0):(l(),v(b,{key:0},[i.canApprover?(l(),d(n,{key:0,border:!1},{default:p((()=>[f("div",O,[r.approverList&&r.approverList.length?(l(!0),v(b,{key:0},D(r.approverList||[],((e,t)=>(l(),d(M,{label:`${e.label}`,userName:r.formData[e.fieldName],"onUpdate:userName":t=>r.formData[e.fieldName]=t,userFullname:r.formData[e.fullnameFieldName],"onUpdate:userFullname":t=>r.formData[e.fullnameFieldName]=t,title:"选择执行人",labelWidth:"15em","input-align":"right",required:!!e.required,rules:[{required:!!e.required,message:"请选择执行人"}],multiple:!!e.multipleChoice,readonly:!i.taskStart},null,8,["label","userName","onUpdate:userName","userFullname","onUpdate:userFullname","required","rules","multiple","readonly"])))),256)):(l(),d(K,{key:1,title:"审批人",value:"该业务流程未配置审批人"}))])])),_:1})):k("",!0)],64))])),_:3},512)])),_:3},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onChangeComment","onDraftClick","onSubmitClick","onAfterSubmit","onRejectClick"])}],["__scopeId","data-v-8fb09741"]]));const E={name:"DocumentPart",components:{},emits:["update:deptValue","update:personValue","update:dateValue"],props:{deptLabel:{type:String,default:"部门机构："},deptValue:{type:String,default:""},deptOptions:{type:Array,default:()=>[]},deptProp:{type:String,default:"deptProp"},personLabel:{type:String,default:"负责人："},personValue:{type:String,default:""},dateLabel:{type:String,default:"日期："},dateValue:{type:String,default:""},labelWidth:{type:String,default:"11em"},disabled:Boolean},setup(e,{attrs:t,slots:a,emit:o}){},data:()=>({}),computed:{computedDeptValue:{get(){return this.deptValue||""},set(e){this.$emit("update:deptValue",e)}}},watch:{},created(){},mounted(){},methods:{}},H={class:"document-part"},M={class:"part-slot"},K={class:"part-sign"},V={class:"ps-item"},q=["innerHTML"],z={class:"ps-item"},R={class:"ps-item"};e("D",o(E,[["render",function(e,t,a,o,r,i){return l(),v("div",H,[f("div",M,[u(e.$slots,"default",{},void 0,!0)]),f("div",K,[f("div",V,[f("span",{class:"label",style:w({width:a.labelWidth})},h(a.deptLabel),5),f("span",{class:"value",innerHTML:i.computedDeptValue.replaceAll("\n","<br/>")},null,8,q)]),f("div",z,[f("span",{class:"label",style:w({width:a.labelWidth})},h(a.personLabel),5),t[0]||(t[0]=f("span",{class:"value"},h(""),-1))]),f("div",R,[f("span",{class:"label",style:w({width:a.labelWidth})},h(a.dateLabel),5),t[1]||(t[1]=f("span",{class:"value"},h(""),-1))])])])}],["__scopeId","data-v-35a918cc"]]))}}}));
