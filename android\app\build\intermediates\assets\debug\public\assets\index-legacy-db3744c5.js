System.register(["./index-legacy-09188690.js","./constants-legacy-82bb4fe6.js","./form-legacy-20b7cd9d.js","./verder-legacy-e6127216.js","./FormItemPicker-legacy-fd45c24d.js","./sift-legacy-1dc85988.js","./vant-legacy-b51a9379.js"],(function(e,t){"use strict";var a,s,i,n,o,r,d,l,c,h,p,u,m,v,f,g,y,b,w,k,_;return{setters:[e=>{a=e.h,s=e._,i=e.A},e=>{n=e.R},e=>{o=e.d},e=>{r=e.Q,d=e.R,l=e.S,c=e.k,h=e.V,p=e.U,u=e.Y,m=e.B,v=e.a2,f=e.X,g=e.F,y=e.W,b=e.Z,w=e.y},e=>{k=e.F},e=>{_=e.S},null],execute:function(){var t=document.createElement("style");t.textContent=".task-item[data-v-ce6aa895]{background-color:#fff;border-radius:1.33333vw;padding:2.66667vw;margin-bottom:2.66667vw}.task-item .body[data-v-ce6aa895]{padding:2.66667vw 1.33333vw 1.33333vw;position:relative}.task-item .body .item-info[data-v-ce6aa895]{display:flex;flex-direction:row;align-items:center;font-size:3.73333vw;color:#9a9a9a;line-height:1;padding:1.33333vw 0}.task-item .body .item-info>.key[data-v-ce6aa895]{min-width:5em}.task-item .body .item-info>.value[data-v-ce6aa895]{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#333}.task-item .body .right[data-v-ce6aa895]{position:absolute;top:1.33333vw;right:1.06667vw}.task-item .body .right .tag[data-v-ce6aa895]{padding:1.6vw 3.2vw;text-align:center}.van-button--disabled[data-v-ce6aa895]{background-color:#1d2129;border:none}.page[data-v-e324c0cf]{height:100%}.view-height[data-v-65022d2f]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height) - var(--sab));padding-top:2.66667vw;overflow-x:hidden;overflow-y:scroll}.view-height.no-tabbar[data-v-65022d2f]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--sab) - 12vw)}.btn-group[data-v-65022d2f]{width:100%;padding:0 4vw;box-sizing:border-box;display:flex;justify-content:space-between;gap:0 4vw;margin:2.66667vw 0}.btn-group>button[data-v-65022d2f]{flex:1}\n",document.head.appendChild(t);const I={class:"body"},L={class:"item-info header"},S={class:"value"},C={class:"item-info"},x={class:"value"},T={class:"item-info"},z={class:"value"},E={class:"item-info"},$={class:"value"},j={class:"item-info"},P={class:"value"},M={class:"right"},N={key:0,class:"p-[10px]"},D={class:"btn-group"};e("default",s({name:"SafeInspectionHazard",components:{FormItemPicker:k,List:s({name:"List",components:{ListItem:s({name:"ListItem",components:{},props:{item:{type:Object,default:()=>({})}},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({statusMap:n}),watch:{},created(){},computed:{statusTypeMap(){var e;return(null===(e=this.statusMap)||void 0===e?void 0:e.TYPE_MAP)||{}},statusLabelMap(){var e;return(null===(e=this.statusMap)||void 0===e?void 0:e.LABEL_MAP)||{}},user(){return this.$store.USER_INFO}},methods:{handelDel(){let e={formKey:"SafeInspectionHazard",entityName:"SafeInspectionHazard",detailEntityNameList:[]};this.$confirm({title:"提示",message:`确认删除${this.item.hazardNumber}?`}).then((()=>{o(i.VUE_APP_TCS_API_SERVICENAME,this.item.id,e).then((e=>{this.$emit("delSuccess")}))})).catch((()=>{}))},toFormCenter(){const{id:e,formKey:t,taskId:a,taskKey:s,processInstanceId:i,formName:n}=this.item;this.$router.push({path:"/FormCenter/SafeInspectionHazard",query:{type:this.user.userName===this.item.createBy&&3===this.item.processState?"execute":"view",taskKey:s,formKey:"SafeInspectionHazard",bizId:e,taskId:a,processInstanceId:i,title:"质量检查问题整改流程"}})}}},[["render",function(e,t,a,s,i,n){const o=r("van-tag"),f=r("van-button"),g=r("van-swipe-cell");return d(),l("div",{class:"task-item",onClick:t[0]||(t[0]=v((e=>n.toFormCenter()),["stop","prevent"]))},[c(g,null,{right:h((()=>[c(f,{square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:n.user.userName!==a.item.createBy||![i.statusMap.PENDING_REVIEW,i.statusMap.STAGE].includes(a.item.rectificationStatus),onClick:n.handelDel},null,8,["disabled","onClick"])])),default:h((()=>[p("div",I,[p("div",L,[t[1]||(t[1]=p("span",{class:"key"},"隐患编号",-1)),p("span",S,u(a.item.hazardNumber),1)]),p("div",C,[t[2]||(t[2]=p("span",{class:"key"},"所属标段",-1)),p("span",x,u(e.$formatLabel(a.item.sectionId,e.$DICT_CODE.project_section)),1)]),p("div",T,[t[3]||(t[3]=p("span",{class:"key"},"隐患级别",-1)),p("span",z,u(e.$formatLabel(a.item.level,e.$DICT_CODE.safe_hazard_level)),1)]),p("div",E,[t[4]||(t[4]=p("span",{class:"key"},"整改期限",-1)),p("span",$,u(e.$dayjs(a.item.deadline).format("YYYY-MM-DD")),1)]),p("div",j,[t[5]||(t[5]=p("span",{class:"key"},"整改内容",-1)),p("span",P,u(a.item.description),1)]),p("div",M,[c(o,{class:"tag",color:n.statusTypeMap[a.item.rectificationStatus],plain:"",size:"medium"},{default:h((()=>[m(u(n.statusLabelMap[a.item.rectificationStatus]),1)])),_:1},8,["color"])])])])),_:1})])}],["__scopeId","data-v-ce6aa895"]])},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}),computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams,...this.search};1===e.page&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await function(e){return a({url:"project-yjp-tcs/safe/inspection/hazard/page",method:"get",params:e})}(e),s=this.searchParams.page<=1?[]:this.list||[];this.list.length>=t.total&&(this.finished=!0),this.list=[...s,...t.records],this.searchParams.page++}catch(e){this.finished=!0}finally{setTimeout((()=>{this.loading=!1,this.$closeToast()}),100)}}}},[["render",function(e,t,a,s,i,n){const o=r("ListItem"),p=r("van-empty"),u=r("van-list"),m=r("van-pull-refresh");return d(),f(m,{modelValue:i.refreshing,"onUpdate:modelValue":t[1]||(t[1]=e=>i.refreshing=e),onRefresh:n.onRefresh},{default:h((()=>[c(u,{loading:i.loading,"onUpdate:loading":t[0]||(t[0]=e=>i.loading=e),finished:i.finished,"finished-text":i.list&&i.list.length?"没有更多了":"",onLoad:n.onLoadList,"immediate-check":!1},{default:h((()=>[i.list&&i.list.length?(d(!0),l(g,{key:0},y(i.list||[],((e,t)=>(d(),f(o,{key:e.id,item:e,onDelSuccess:n.onRefresh},null,8,["item","onDelSuccess"])))),128)):(d(),l(g,{key:1},[i.loading?b("",!0):(d(),l("div",N,[c(p,{description:"暂无数据"})]))],64))])),_:1},8,["loading","finished","finished-text","onLoad"])])),_:1},8,["modelValue","onRefresh"])}],["__scopeId","data-v-e324c0cf"]])},props:{},emits:[],setup(e,{attrs:t,slots:a,emit:s}){},data:()=>({search:{hazardNumber:"",sectionId:"",rectificationStatus:""},Sift:_,showTop:!1,statusOptions:Object.entries(n.LABEL_MAP).map((([e,t])=>({value:e,label:t})))}),watch:{},mounted(){},methods:{handleAdd(){this.$router.push({path:"/FormCenter/SafeInspectionHazard",query:{type:"add",title:"新增隐患",taskKey:"UserTask_0"}})},handleQuery(){this.showTop=!1,this.$nextTick((()=>{this.$refs.List&&this.$refs.List.onRefresh(this.search)}))},handleResetting(){this.search={correctionNumber:"",sectionId:"",rectificationStatus:""},this.handleQuery()}}},[["render",function(e,t,a,s,i,n){const o=r("van-icon"),u=r("Navbar"),f=r("FormItemPicker"),y=r("van-button"),b=r("van-popup"),k=r("List");return d(),l(g,null,[c(u,{back:""},{right:h((()=>[c(o,{name:i.Sift,size:"2em",onClick:t[0]||(t[0]=v((e=>i.showTop=!i.showTop),["stop","prevent"]))},null,8,["name"])])),_:1}),c(b,{show:i.showTop,"onUpdate:show":t[3]||(t[3]=e=>i.showTop=e),position:"top"},{default:h((()=>[c(f,{value:i.search.sectionId,"onUpdate:value":t[1]||(t[1]=e=>i.search.sectionId=e),"dict-name":e.$DICT_CODE.project_section,placeholder:"选择所属标段"},null,8,["value","dict-name"]),c(f,{value:i.search.rectificationStatus,"onUpdate:value":t[2]||(t[2]=e=>i.search.rectificationStatus=e),placeholder:"选择流转状态",columns:[...i.statusOptions],"columns-field-names":{text:"label",value:"value",children:"none"}},null,8,["value","columns"]),p("div",D,[c(y,{round:"",type:"primary",plain:"",onClick:v(n.handleQuery,["stop","prevent"])},{default:h((()=>t[5]||(t[5]=[m("查询")]))),_:1,__:[5]},8,["onClick"]),c(y,{round:"",plain:"",onClick:v(n.handleResetting,["stop","prevent"])},{default:h((()=>t[6]||(t[6]=[m("重置")]))),_:1,__:[6]},8,["onClick"])])])),_:1},8,["show"]),p("div",{class:w(["view-height",{"no-tabbar":e.envFeishu}])},[c(k,{ref:"List",search:i.search},null,8,["search"])],2),c(y,{type:"primary",size:"normal",style:{width:"100%"},onClick:t[4]||(t[4]=e=>n.handleAdd())},{default:h((()=>t[7]||(t[7]=[m("新增整改")]))),_:1,__:[7]})],64)}],["__scopeId","data-v-65022d2f"]]))}}}));
