System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,t){"use strict";var a,l,n,s,o,d,i,r,m;return{setters:[e=>{a=e.F,l=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,d=e.X,i=e.V,r=e.k,m=e.U},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){var t=document.createElement("style");t.textContent=".part-div[data-v-9aebf8ae]{border-top:1px solid #333;padding:2.66667vw}.part-div .part-sign[data-v-9aebf8ae]{padding-left:6em}\n",document.head.appendChild(t);const c={class:"comment-wp"},u={class:"textarea-wp"},p={class:"attachment-desc"};e("default",n({name:"JL39",components:{FormTemplate:a,DocumentPart:l},emits:[],props:{},setup(e,{attrs:t,slots:a,emit:l}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:t}){},onBeforeSubmit:({formData:e,detailParamList:t,taskStart:a},l)=>new Promise(((e,t)=>{try{e()}catch(a){t(a)}}))}},[["render",function(e,t,a,l,n,f){const b=s("van-field"),v=s("DocumentPart"),y=s("FormTemplate");return o(),d(y,{ref:"FormTemplate",nature:"联系","on-after-init":f.onAfterInit,"on-before-submit":f.onBeforeSubmit,"detail-table":n.detailTable,"is-show-confirm1":!1,"is-show-confirm2":!0,"is-show-confirm3":!0,"show-target":!0,attachmentDesc:n.attachmentDesc},{default:i((({formData:e,formTable:a,baseObj:l,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:d,taskComment4:f})=>[r(v,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:l.supervisionDeptName,personLabel:"总监理工程师/监理工程师：",labelWidth:"10em",disabled:!s},{default:i((()=>[m("div",c,[t[0]||(t[0]=m("div",null,"事由：",-1)),m("div",u,[r(b,{modelValue:e.field1,"onUpdate:modelValue":t=>e.field1=t,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),m("div",p,[t[1]||(t[1]=m("div",null,"附件：",-1)),r(b,{modelValue:e.attachmentDesc,"onUpdate:modelValue":t=>e.attachmentDesc=t,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!s},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),t[2]||(t[2]=m("div",{class:"part-div"},[m("div",{style:{height:"30px"}}),m("div",{class:"part-sign"},[m("div",null,[m("span",null,"被联系单位签收人：")]),m("div",null,[m("span",null,"日期：")])])],-1))])),footer:i((({formData:e,formTable:a,baseObj:l,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:d,taskComment4:i})=>t[3]||(t[3]=[m("div",{class:"footer-input"},[m("div",null,"说明：本表用于监理机构与监理工作有关单位的联系，监理单位、被联系单位各1份。")],-1)]))),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}],["__scopeId","data-v-9aebf8ae"]]))}}}));
