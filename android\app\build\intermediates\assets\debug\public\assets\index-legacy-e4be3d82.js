System.register(["./index-legacy-f817ae93.js","./index-legacy-645a3645.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js"],(function(e,a){"use strict";var t,l,n,s,o,c,m,d,r,i,p,u;return{setters:[e=>{t=e.F,l=e.D},e=>{n=e.U},e=>{s=e._},e=>{o=e.Q,c=e.R,m=e.X,d=e.V,r=e.U,i=e.Y,p=e.k,u=e.B},null,null,null,null,null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent="[data-v-e7748b53] .one-line{display:flex;flex-direction:row;flex-wrap:wrap}\n",document.head.appendChild(a);const h={class:"one-line",style:{"justify-content":"center",padding:"0 30px"}},f={class:"one-line"},b={class:"form-info"},k={class:"attachment-desc"},g={class:"comment-wp"},y={class:"textarea-wp"},D={class:"comment-wp"},C={class:"check-wp"},V={class:"check-wp"},v={class:"check-wp"},_={class:"check-wp"},w={class:"footer-input"},F={class:"form-info"},T={class:"form-info"},j={class:"form-info"},x={class:"form-info"};e("default",s({name:"CB18",components:{FormTemplate:t,DocumentPart:l,UploadFiles:n},emits:[],props:{},setup(e,{attrs:a,slots:t,emit:l}){},data:()=>({detailTable:[],attachmentDesc:"施工质量评定表\n施工质量检查、检测记录"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit({formData:e,detailParamList:a,taskComment3:t},l){return new Promise(((a,n)=>{try{if("submit"==l&&t&&!(e.check1||e.check2||e.check3||e.check4))return this.$showNotify({type:"danger",message:"请选复核结果!",duration:3e3}),n(!1),!1;a()}catch(s){n(s)}}))},onAfterSubmit(e,a){return new Promise((async(e,a)=>{try{this.$refs.contentAttachment&&await this.$refs.contentAttachment.update(),e()}catch(t){a()}}))},changeCheck1(e){e&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.check3=!1,this.$refs.FormTemplate.formData.check4=!1)},changeCheck2(e){e&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check3=!1,this.$refs.FormTemplate.formData.check4=!1)},changeCheck3(e){e&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.check4=!1)},changeCheck4(e){e&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.check3=!1)}}},[["render",function(e,a,t,l,n,s){const U=o("van-field"),$=o("DocumentPart"),A=o("van-checkbox"),P=o("van-col"),N=o("van-row"),O=o("UploadFiles"),L=o("FormTemplate");return c(),m(L,{ref:"FormTemplate",nature:"质报","on-after-init":s.onAfterInit,"on-before-submit":s.onBeforeSubmit,"on-after-submit":s.onAfterSubmit,"detail-table":n.detailTable,"is-show-confirm1":!1,"title-fill":!0,nums:[6,1,1,4],attachmentDesc:n.attachmentDesc},{title:d((({formData:e,formTable:t,baseObj:l,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:c,taskComment4:m})=>[r("div",h,[r("span",null,i(e.field1),1),a[0]||(a[0]=r("span",null,"工程施工质量报验单",-1))])])),default:d((({formData:e,formTable:t,baseObj:l,uploadAccept:n,taskStart:o,taskComment2:c,taskComment3:m,taskComment4:h,taskComment5:w})=>[p($,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:l.constructionDeptName,personLabel:"质检负责人：",labelWidth:"10em",disabled:!o},{default:d((()=>[r("div",f,[r("span",b,i(e.field1),1),a[1]||(a[1]=r("span",null,"工程已按合同要求完成施工，",-1)),a[2]||(a[2]=r("span",null,"经自检合格，",-1)),a[3]||(a[3]=r("span",null,"报请贵方核验。",-1))]),r("div",k,[a[4]||(a[4]=r("div",null,"附件：",-1)),p(U,{modelValue:e.attachmentDesc,"onUpdate:modelValue":a=>e.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),p($,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:l.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!o},{default:d((()=>[r("div",g,[a[5]||(a[5]=r("div",null,"EPC总承包项目部意见：",-1)),r("div",y,[p(U,{modelValue:e.comment2,"onUpdate:modelValue":a=>e.comment2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!c},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),p($,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:l.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!o},{default:d((()=>[r("div",D,[a[12]||(a[12]=r("div",null,"监理机构意见：",-1)),r("div",null,[a[10]||(a[10]=r("div",{style:{margin:"10px 0"}},"复核结果：",-1)),p(N,{gutter:0},{default:d((()=>[p(P,{span:24},{default:d((()=>[r("div",C,[p(A,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!m,onChange:s.changeCheck1},{default:d((()=>a[6]||(a[6]=[u(" 同意进入下一工序 ")]))),_:2,__:[6]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])])),_:2},1024),p(P,{span:24},{default:d((()=>[r("div",V,[p(A,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!m,onChange:s.changeCheck2},{default:d((()=>a[7]||(a[7]=[u(" 不同意进入下一工序 ")]))),_:2,__:[7]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])])),_:2},1024),p(P,{span:24},{default:d((()=>[r("div",v,[p(A,{modelValue:e.check3,"onUpdate:modelValue":a=>e.check3=a,shape:"square",disabled:!m,onChange:s.changeCheck3},{default:d((()=>a[8]||(a[8]=[u(" 同意进入下一单元工程 ")]))),_:2,__:[8]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])])),_:2},1024),p(P,{span:24},{default:d((()=>[r("div",_,[p(A,{modelValue:e.check4,"onUpdate:modelValue":a=>e.check4=a,shape:"square",disabled:!m,onChange:s.changeCheck4},{default:d((()=>a[9]||(a[9]=[u(" 不同意进入下一单元工程 ")]))),_:2,__:[9]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])])),_:2},1024)])),_:2},1024),a[11]||(a[11]=r("div",{style:{margin:"10px 0"}},"附件：监理复核支持材料。",-1)),p(U,{label:"","label-align":"top","input-align":"left"},{input:d((()=>[p(O,{ref:"contentAttachment",g9s:e.contentAttachment,"onUpdate:g9s":a=>e.contentAttachment=a,accept:n,multiple:!0,"max-count":9,"max-size":52428800,readonly:!m},null,8,["g9s","onUpdate:g9s","accept","readonly"])])),_:2},1024)])])])),_:2},1032,["deptValue","deptOptions","disabled"])])),footer:d((({formData:e,formTable:t,baseObj:l,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:c,taskComment4:m,taskComment5:d})=>[r("div",w,[a[13]||(a[13]=r("span",null,"说明：本表一式",-1)),r("span",F,i(e.num1),1),a[14]||(a[14]=r("span",null,"份，由承包人填写，监理机构审签后，发包人",-1)),r("span",T,i(e.num2),1),a[15]||(a[15]=r("span",null,"份，监理机构",-1)),r("span",j,i(e.num3),1),a[16]||(a[16]=r("span",null,"份，承包人",-1)),r("span",x,i(e.num4),1),a[17]||(a[17]=r("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","on-after-submit","detail-table","attachmentDesc"])}],["__scopeId","data-v-e7748b53"]]))}}}));
