System.register(["./index-legacy-f817ae93.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,t){"use strict";var a,l,n,s,o,m,d,p,i,r;return{setters:[e=>{a=e.F,l=e.D},e=>{n=e._},e=>{s=e.Q,o=e.R,m=e.X,d=e.V,p=e.k,i=e.U,r=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const t={class:"one-line"},c={class:"form-info"},u={class:"form-info"},f={class:"attachment-desc"},b={class:"comment-wp"},y={class:"textarea-wp"},D={class:"footer-input"},g={class:"form-info"},h={class:"form-info"},j={class:"form-info"},v={class:"form-info"};e("default",n({name:"CB03",components:{FormTemplate:a,DocumentPart:l},emits:[],props:{},setup(e,{attrs:t,slots:a,emit:l}){},data:()=>({detailTable:[],attachmentDesc:"1、施工进度计划。"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:t}){},onBeforeSubmit:({formData:e,detailParamList:t,taskComment3:a},l)=>new Promise(((e,t)=>{try{e()}catch(a){t(a)}}))}},[["render",function(e,a,l,n,V,k){const C=s("van-field"),P=s("DocumentPart"),w=s("FormTemplate");return o(),m(w,{ref:"FormTemplate",nature:"图计","on-after-init":k.onAfterInit,"on-before-submit":k.onBeforeSubmit,"detail-table":V.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:V.attachmentDesc},{default:d((({formData:e,formTable:l,baseObj:n,uploadAccept:s,taskStart:o,taskComment2:m,taskComment3:D,taskComment4:g,taskComment5:h})=>[p(P,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:e.constructionDeptName,deptOptions:n.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!o},{default:d((()=>[i("div",t,[a[0]||(a[0]=i("span",{style:{"padding-left":"2em"}},"我方今提交",-1)),i("span",c,r(e.projectName),1),a[1]||(a[1]=i("span",null,"工程的",-1)),i("span",u,r(e.field1),1),a[2]||(a[2]=i("span",null,"请贵方审批。",-1))]),i("div",f,[a[3]||(a[3]=i("div",null,"附件：",-1)),p(C,{modelValue:e.attachmentDesc,"onUpdate:modelValue":t=>e.attachmentDesc=t,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),p(P,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:e.epcDeptName,deptOptions:n.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!o},{default:d((()=>[i("div",b,[a[4]||(a[4]=i("div",null,"EPC总承包项目部意见：",-1)),i("div",y,[p(C,{modelValue:e.comment2,"onUpdate:modelValue":t=>e.comment2=t,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!m},null,8,["modelValue","onUpdate:modelValue","readonly"])])])])),_:2},1032,["deptValue","deptOptions","disabled"]),p(P,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:n.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!o},{default:d((()=>a[5]||(a[5]=[i("div",{class:"comment-wp"},[i("div",null,"监理机构将另行签发审批意见。")],-1)]))),_:2,__:[5]},1032,["deptValue","deptOptions","disabled"])])),footer:d((({formData:e,formTable:t,baseObj:l,uploadAccept:n,taskStart:s,taskComment2:o,taskComment3:m,taskComment4:d,taskComment5:p})=>[i("div",D,[a[6]||(a[6]=i("span",null,"说明：本表一式",-1)),i("span",g,r(e.num1),1),a[7]||(a[7]=i("span",null,"份，由承包人填写，监理机构签收后，发包人",-1)),i("span",h,r(e.num2),1),a[8]||(a[8]=i("span",null,"份，监理机构",-1)),i("span",j,r(e.num3),1),a[9]||(a[9]=i("span",null,"份，承包人",-1)),i("span",v,r(e.num4),1),a[10]||(a[10]=i("span",null,"份。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
