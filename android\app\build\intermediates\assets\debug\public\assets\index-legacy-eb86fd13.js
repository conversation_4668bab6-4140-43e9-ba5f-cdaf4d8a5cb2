System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,a){"use strict";var l,n,t,s,o,m,d,c,i,r,p;return{setters:[e=>{l=e.F,n=e.D},e=>{t=e._},e=>{s=e.Q,o=e.R,m=e.X,d=e.V,c=e.k,i=e.U,r=e.Y,p=e.B},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const a={class:"one-line"},u={class:"form-info"},f={class:"form-info"},h={class:"form-info"},b={class:"form-info"},g={class:"check-wp"},y={class:"check-wp"},k={class:"form-info"},D={class:"form-info"},j={class:"attachment-desc"},V={class:"footer-input"},v={class:"form-info"},C={class:"form-info"},_={class:"form-info"},w={class:"form-info"};e("default",t({name:"JL20",components:{FormTemplate:l,DocumentPart:n},emits:[],props:{},setup(e,{attrs:a,slots:l,emit:n}){},data:()=>({detailTable:[],attachmentDesc:"1、合同解除相关文件。\n2、计算资料。\n3、证明文件（包含承包人已得到各项付款的证明文件）。"}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:a}){},onBeforeSubmit({formData:e,detailParamList:a,taskStart:l},n){return new Promise(((a,t)=>{try{if("submit"===n&&l&&!e.check1&&!e.check2)return this.$showNotify({type:"danger",message:"请选择类型：支付/退还",duration:3e3}),t(!1),!1;a()}catch(s){t(s)}}))},changeCheck1(e){e&&(this.$refs.FormTemplate.formData.check2=!1)},changeCheck2(e){e&&(this.$refs.FormTemplate.formData.check1=!1)}}},[["render",function(e,l,n,t,x,P){const T=s("van-checkbox"),F=s("van-field"),L=s("DocumentPart"),N=s("FormTemplate");return o(),m(N,{ref:"FormTemplate",nature:"解付","employer-target":!0,"on-after-init":P.onAfterInit,"on-before-submit":P.onBeforeSubmit,"detail-table":x.detailTable,"is-show-confirm1":!1,attachmentDesc:x.attachmentDesc},{default:d((({formData:e,formTable:n,baseObj:t,uploadAccept:s,taskStart:o,taskComment2:m,taskComment3:V,taskComment4:v})=>[c(L,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:e.supervisionDeptName,deptOptions:t.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!o},{default:d((()=>[i("div",a,[l[2]||(l[2]=i("span",{style:{"padding-left":"2em"}},"根据施工合同约定，经核查，合同解除后承包人应获得工程价款总金额为",-1)),l[3]||(l[3]=i("span",null,"（大写）",-1)),i("span",u,r(e.field1),1),l[4]||(l[4]=i("span",null,"（小写",-1)),i("span",f,r(e.field2),1),l[5]||(l[5]=i("span",null,"），",-1)),l[6]||(l[6]=i("span",null,"已得到各顶付款总金额为",-1)),l[7]||(l[7]=i("span",null,"（大写）",-1)),i("span",h,r(e.field3),1),l[8]||(l[8]=i("span",null,"（小写",-1)),i("span",b,r(e.field4),1),l[9]||(l[9]=i("span",null,"），",-1)),l[10]||(l[10]=i("span",null,"现应",-1)),i("div",g,[c(T,{modelValue:e.check1,"onUpdate:modelValue":a=>e.check1=a,shape:"square",disabled:!o,onChange:P.changeCheck1},{default:d((()=>l[0]||(l[0]=[p("支付")]))),_:2,__:[0]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[11]||(l[11]=i("span",null,"/",-1)),i("div",y,[c(T,{modelValue:e.check2,"onUpdate:modelValue":a=>e.check2=a,shape:"square",disabled:!o,onChange:P.changeCheck2},{default:d((()=>l[1]||(l[1]=[p("退还")]))),_:2,__:[1]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),l[12]||(l[12]=i("span",null,"的工程款金额为",-1)),l[13]||(l[13]=i("span",null,"（大写）",-1)),i("span",k,r(e.field5),1),l[14]||(l[14]=i("span",null,"（小写",-1)),i("span",D,r(e.field6),1),l[15]||(l[15]=i("span",null,"）。",-1))]),i("div",j,[l[16]||(l[16]=i("div",null,"附件：",-1)),c(F,{modelValue:e.attachmentDesc,"onUpdate:modelValue":a=>e.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!o},null,8,["modelValue","onUpdate:modelValue","readonly"])])])),_:2},1032,["deptValue","deptOptions","disabled"]),c(L,{deptLabel:"发包人：",deptProp:"employerName",deptValue:e.employerName,deptOptions:t.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!o},{default:d((()=>l[17]||(l[17]=[i("div",{class:"comment-wp"},[i("div",{style:{height:"30px"}})],-1)]))),_:2,__:[17]},1032,["deptValue","deptOptions","disabled"])])),footer:d((({formData:e,formTable:a,baseObj:n,uploadAccept:t,taskStart:s,taskComment2:o,taskComment3:m,taskComment4:d})=>[i("div",V,[l[18]||(l[18]=i("span",null,"说明：本证书一式",-1)),i("span",v,r(e.num1),1),l[19]||(l[19]=i("span",null,"份，由监理机构填写，发包人",-1)),i("span",C,r(e.num2),1),l[20]||(l[20]=i("span",null,"份，监理机构",-1)),i("span",_,r(e.num3),1),l[21]||(l[21]=i("span",null,"份，承包人",-1)),i("span",w,r(e.num4),1),l[22]||(l[22]=i("span",null,"份，",-1)),l[23]||(l[23]=i("span",null,"办理结算的附件。",-1))])])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
