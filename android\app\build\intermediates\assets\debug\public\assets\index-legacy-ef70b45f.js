System.register(["./index-legacy-b580af71.js","./DesignDispatchDrawingTable-legacy-b7054d89.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js"],(function(e,a){"use strict";var t,l,r,o,i,m,n,s,d;return{setters:[e=>{t=e.F},e=>{l=e.D,r=e.a},e=>{o=e._},e=>{i=e.Q,m=e.R,n=e.X,s=e.V,d=e.k},null,null,null,null,null,null,null,null,null,null],execute:function(){e("default",o({name:"DesignEPCAuditNew",components:{FlowForm:t,DesignDispatchCommonTopNew:l,DesignDispatchDrawingTable:r},data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",modelKey:"technology_design_EPC_audit_2",entityName:"TechnologyCommonFile",detailEntityName:"TechnologyDesignEPCAudit",detailParamList:[],detailEntityNameList:["TechnologyDesignEPCAudit","TechnologyDesignEPCAuditDrawing"],service:{query:"/cybereng-technology/design/EPCAudit/query",submit:"cybereng-technology/design/form/commit"},formKey:"DesignEPCAuditNew",formData:{formKey:"DesignEPCAuditNew",portalId:"",subProjectName:"",subProjectId:null,modelType:"",sendingName:"",sendingCode:"",sendingType:"",sendingTypeCode:"",contentDescription:"",profession:"",relavancePartName:"",relavancePart:"",leadDesigner:"",leadDesignerFullname:"",userTelephoneNum:"",fileNum:"",fileName:"",notifyMethods:[],isconfirm1:!0,approverUsername1:"",approverFullname1:"",approverUnit1:"",time1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",time2:"",expireTime2:"",isconfirm2:!0,approverUsername3:"",approverFullname3:"",approverUnit3:"",time3:"",expireTime1:"",duplicateUsername1:"",duplicateFullname1:"",duplicateUsername2:"",duplicateFullname2:""},formDetailTable:[],detailId:""}},computed:{},mounted(){this.initForm()},methods:{async initForm(){this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:a=[],entityObject:t}=e;this.detailParamList=a,this.detailId=a[0].detailEntityArray[0].id,this.formData={...this.formData,...a[0].detailEntityArray[0],...t,notifyMethods:t.notifyMethod?t.notifyMethod.split(","):[]},this.formDetailTable=a[1].detailEntityArray}))}))},async onDraft(){try{const e={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{if(["fileName","fileNum","relavancePartName","profession","leadDesignerFullname","subProjectName","approverFullname1","approverFullname2"].some((e=>!this.formData[e])))return this.$showToast({message:"请登录数智建管系统完善表单"});if(!this.formDetailTable.length)return this.$showToast({message:"请登录数智建管系统完善表单"});this.setTime();const e={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},setTime(){switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_2":this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_3":this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss")}},async afterSubmit(){}}},[["render",function(e,a,t,l,r,o){const u=i("design-dispatch-common-top-new"),f=i("design-dispatch-drawing-table"),y=i("van-field"),p=i("van-cell-group"),c=i("van-form"),D=i("FlowForm");return m(),n(D,{ref:"FlowForm","model-key":r.modelKey,"form-key":r.formKey,"entity-name":r.entityName,"detail-param-list":r.detailParamList,"detail-entity-name-list":r.detailEntityNameList,onDraftClick:o.onDraft,onSubmitClick:o.onSubmit,onAfterSubmit:o.afterSubmit},{default:s((()=>[d(c,{ref:"form","label-width":"8em","input-align":"right","error-message-align":"right"},{default:s((()=>[d(u,{"form-data":r.formData,type:r.type,readonly:""},null,8,["form-data","type"]),d(f,{"form-detail-table":r.formDetailTable,ref:"detailForm","form-data":r.formData,type:r.type},null,8,["form-detail-table","form-data","type"]),d(p,{border:!1},{default:s((()=>[d(y,{modelValue:r.formData.userFullname,"onUpdate:modelValue":a[0]||(a[0]=e=>r.formData.userFullname=e),label:"发起人",readonly:"",required:""},null,8,["modelValue"]),d(y,{modelValue:r.formData.prjDepName,"onUpdate:modelValue":a[1]||(a[1]=e=>r.formData.prjDepName=e),label:"发起人部门",readonly:""},null,8,["modelValue"]),d(y,{modelValue:r.formData.approverFullname1,"onUpdate:modelValue":a[2]||(a[2]=e=>r.formData.approverFullname1=e),label:"合同部审查人",readonly:"",required:""},null,8,["modelValue"]),d(y,{modelValue:r.formData.approverUnit1,"onUpdate:modelValue":a[3]||(a[3]=e=>r.formData.approverUnit1=e),label:"合同部审查人部门",readonly:""},null,8,["modelValue"]),d(y,{modelValue:r.formData.expireTime1,"onUpdate:modelValue":a[4]||(a[4]=e=>r.formData.expireTime1=e),label:"设置审批截止时间",formatter:a=>`${a?e.$dayjs(a).format("YYYY-MM-DD"):""}`,readonly:""},null,8,["modelValue","formatter"]),d(y,{modelValue:r.formData.approverFullname2,"onUpdate:modelValue":a[5]||(a[5]=e=>r.formData.approverFullname2=e),label:"施工项目部审查人","label-width":"9em",readonly:"",required:""},null,8,["modelValue"]),d(y,{"label-width":"10em",modelValue:r.formData.approverUnit2,"onUpdate:modelValue":a[6]||(a[6]=e=>r.formData.approverUnit2=e),label:"施工项目部审查人部门",readonly:""},null,8,["modelValue"]),d(y,{modelValue:r.formData.expireTime2,"onUpdate:modelValue":a[7]||(a[7]=e=>r.formData.expireTime2=e),formatter:a=>`${a?e.$dayjs(a).format("YYYY-MM-DD"):""}`,label:"设置审批截止时间",readonly:""},null,8,["modelValue","formatter"]),d(y,{modelValue:r.formData.duplicateFullname1,"onUpdate:modelValue":a[8]||(a[8]=e=>r.formData.duplicateFullname1=e),label:"抄送至",readonly:""},null,8,["modelValue"])])),_:1})])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}]]))}}}));
