System.register(["./index-legacy-b580af71.js","./index-legacy-645a3645.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js"],(function(e,a){"use strict";var l,t,o,r,n,i,d,m,s,u,y;return{setters:[e=>{l=e.F},e=>{t=e.U},e=>{o=e._},e=>{r=e.Q,n=e.R,i=e.X,d=e.V,m=e.k,s=e.S,u=e.F,y=e.W},null,null,null,null,null,null,null,null,null],execute:function(){const a={name:"NotifyDetail",components:{UploadFiles:t},props:{formData:{type:Object,default:()=>({})},type:{},showContentDescription:{type:Boolean,default:!0},readonly:{type:Boolean,default:!0}}};e("default",o({name:"TechnologyDesignDrawingModifyNotify",components:{FlowForm:l,NotifyDetail:o(a,[["render",function(e,a,l,t,o,s){const u=r("van-field"),y=r("UploadFiles"),f=r("van-cell-group");return n(),i(f,{border:!1},{default:d((()=>[m(u,{modelValue:l.formData.relavancePartName,"onUpdate:modelValue":a[0]||(a[0]=e=>l.formData.relavancePartName=e),label:"工程部位",readonly:l.readonly,required:""},null,8,["modelValue","readonly"]),m(u,{modelValue:l.formData.profession,"onUpdate:modelValue":a[1]||(a[1]=e=>l.formData.profession=e),label:"专业",readonly:l.readonly,required:""},null,8,["modelValue","readonly"]),m(u,{modelValue:l.formData.notifyOrderCode,"onUpdate:modelValue":a[2]||(a[2]=e=>l.formData.notifyOrderCode=e),label:"通知单编号",readonly:l.readonly,required:""},null,8,["modelValue","readonly"]),m(u,{modelValue:l.formData.notifyOrderName,"onUpdate:modelValue":a[3]||(a[3]=e=>l.formData.notifyOrderName=e),label:"通知单名称",readonly:l.readonly,required:""},null,8,["modelValue","readonly"]),m(u,{modelValue:l.formData.subProjectName,"onUpdate:modelValue":a[4]||(a[4]=e=>l.formData.subProjectName=e),label:"子工程",readonly:l.readonly,required:""},null,8,["modelValue","readonly"]),m(u,{label:"附件","label-align":"top","input-align":"left"},{input:d((()=>[m(y,{ref:"afterFiles",g9s:l.formData.attachment,"onUpdate:g9s":a[5]||(a[5]=e=>l.formData.attachment=e),readonly:!0},null,8,["g9s"])])),_:1})])),_:1})}]]),DesignDispatchDrawingModifyTable:o({name:"DesignDispatchDrawingModifyTable",components:{},props:{formDetailTable:{type:Array,default:()=>[]},formData:{type:Object,default:()=>({})},type:{}}},[["render",function(e,a,l,t,o,f){const p=r("van-field"),c=r("van-cell-group");return n(),i(c,{border:!1},{default:d((()=>[(n(!0),s(u,null,y(l.formDetailTable,(e=>(n(),s(u,null,[m(p,{modelValue:e.originalFileName,"onUpdate:modelValue":a=>e.originalFileName=a,label:"原图名",readonly:""},null,8,["modelValue","onUpdate:modelValue"]),m(p,{modelValue:e.originalFileCode,"onUpdate:modelValue":a=>e.originalFileCode=a,label:"原图号",readonly:""},null,8,["modelValue","onUpdate:modelValue"]),m(p,{modelValue:e.newFileCode,"onUpdate:modelValue":a=>e.newFileCode=a,label:"新图号",readonly:""},null,8,["modelValue","onUpdate:modelValue"]),m(p,{label:"修改内容",modelValue:e.modifyContent,"onUpdate:modelValue":a=>e.modifyContent=a,rows:"4",autosize:"",type:"textarea","label-align":"top",maxlength:"500","input-align":"left",readonly:""},null,8,["modelValue","onUpdate:modelValue"])],64)))),256))])),_:1})}]])},data(){var e,a;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(a=this.$route.query)||void 0===a?void 0:a.taskKey)||"",modelKey:"technology_design_drawing_modify_notify",entityName:"TechnologyCommonFile",detailEntityName:"TechnologyDesignEPCAudit",detailParamList:[],detailEntityNameList:["TechnologyDesignEPCAudit","TechnologyDesignDrawingModifyContent"],service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/design/form/commit"},formKey:"TechnologyDesignDrawingModifyNotify",formData:{formKey:"TechnologyDesignDrawingModifyNotify",portalId:"",subProjectName:"",subProjectId:null,modelType:"",sendingName:"",sendingCode:"",sendingType:"",sendingTypeCode:"",contentDescription:"",profession:"",relavancePartName:"",relavancePart:"",leadDesigner:"",leadDesignerFullname:"",userTelephoneNum:"",notifyOrderCode:"",notifyOrderName:"",isRelate:!0,modifyOrderCode:"",attachment:"",isconfirm1:!0,approverUsername1:"",approverFullname1:"",approverUnit1:"",time1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",time2:"",duplicateUsername1:"",duplicateFullname1:"",duplicateUsername2:"",duplicateFullname2:"",relatedBpmId:""},formDetailTable:[],detailId:""}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},canNotEdit:()=>!0},mounted(){this.initForm()},methods:{async initForm(){this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:a=[],entityObject:l}=e;this.detailParamList=a,this.detailId=a[0].detailEntityArray[0].id,this.formData={...this.formData,...a[0].detailEntityArray[0],...l},this.formDetailTable=a[1].detailEntityArray}))}))},async onDraft(){try{const e={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{if(["subProjectName","relavancePartName","profession","leadDesigner","notifyOrderCode","notifyOrderName","approverFullname1","approverFullname2"].some((e=>!this.formData[e])))return this.$showToast({message:"请登录数智建管系统完善表单"});if(!this.formDetailTable.length)return this.$showToast({message:"请登录数智建管系统完善表单"});await this.$refs.form.validate(),this.setTime();const e={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,e)}catch(e){console.log(e)}},setTime(){switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break;case"UserTask_2":this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss")}},async afterSubmit(){}}},[["render",function(e,a,l,t,o,s){const u=r("notify-detail"),y=r("design-dispatch-drawing-modify-table"),f=r("van-field"),p=r("van-cell-group"),c=r("van-form"),D=r("FlowForm");return n(),i(D,{ref:"FlowForm","model-key":o.modelKey,"form-key":o.formKey,"entity-name":o.entityName,"detail-param-list":o.detailParamList,"detail-entity-name-list":o.detailEntityNameList,onDraftClick:s.onDraft,onSubmitClick:s.onSubmit,onAfterSubmit:s.afterSubmit},{default:d((()=>[m(c,{ref:"form","label-width":"8em","input-align":"right","error-message-align":"right"},{default:d((()=>[m(u,{"form-data":o.formData,type:o.type,readonly:""},null,8,["form-data","type"]),m(y,{"form-detail-table":o.formDetailTable,ref:"detailForm","form-data":o.formData,type:o.type},null,8,["form-detail-table","form-data","type"]),m(p,{border:!1},{default:d((()=>[m(f,{modelValue:o.formData.userFullname,"onUpdate:modelValue":a[0]||(a[0]=e=>o.formData.userFullname=e),label:"发起人",readonly:""},null,8,["modelValue"]),m(f,{modelValue:o.formData.prjDepName,"onUpdate:modelValue":a[1]||(a[1]=e=>o.formData.prjDepName=e),label:"发起人部门",readonly:""},null,8,["modelValue"]),m(f,{modelValue:o.formData.approverFullname1,"onUpdate:modelValue":a[2]||(a[2]=e=>o.formData.approverFullname1=e),label:"业主审核人",readonly:"",required:""},null,8,["modelValue"]),m(f,{modelValue:o.formData.approverUnit1,"onUpdate:modelValue":a[3]||(a[3]=e=>o.formData.approverUnit1=e),label:"审核部门",readonly:""},null,8,["modelValue"]),m(f,{modelValue:o.formData.approverFullname2,"onUpdate:modelValue":a[4]||(a[4]=e=>o.formData.approverFullname2=e),label:"签收人",readonly:"",required:""},null,8,["modelValue"]),m(f,{modelValue:o.formData.approverUnit2,"onUpdate:modelValue":a[5]||(a[5]=e=>o.formData.approverUnit2=e),label:"签收部门",readonly:""},null,8,["modelValue"]),m(f,{modelValue:o.formData.duplicateFullname1,"onUpdate:modelValue":a[6]||(a[6]=e=>o.formData.duplicateFullname1=e),label:"抄送至",readonly:""},null,8,["modelValue"])])),_:1})])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}]]))}}}));
