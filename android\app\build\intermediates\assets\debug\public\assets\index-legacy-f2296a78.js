System.register(["./index-legacy-d2bd9315.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./index-legacy-b580af71.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./index-legacy-645a3645.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./formKeys-legacy-257dbd3e.js","./FormItemPerson-legacy-e6e57748.js","./FormItemPicker-legacy-fd45c24d.js"],(function(e,t){"use strict";var l,a,s,n,i,c,d,m,o;return{setters:[e=>{l=e.F,a=e.D},e=>{s=e._},e=>{n=e.Q,i=e.R,c=e.X,d=e.V,m=e.U,o=e.Y},null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){const t={class:"jl-table"},r={class:"cell"},u={class:"cell"},f={class:"cell"};e("default",s({name:"JL27",components:{FormTemplate:l,DocumentPart:a},emits:[],props:{},setup(e,{attrs:t,slots:l,emit:a}){},data:()=>({detailTable:[],attachmentDesc:""}),computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:e,detailParamList:t}){},onBeforeSubmit:({formData:e,detailParamList:t},l)=>new Promise(((e,t)=>{try{e()}catch(l){t(l)}}))}},[["render",function(e,l,a,s,p,y){const g=n("FormTemplate");return i(),c(g,{ref:"FormTemplate",nature:"巡视","on-after-init":y.onAfterInit,"on-before-submit":y.onBeforeSubmit,"detail-table":p.detailTable,"is-show-confirm1":!1,"show-target":!1,"hide-border":"",attachmentDesc:p.attachmentDesc},{default:d((({formData:e,formTable:a,baseObj:s,uploadAccept:n,taskStart:i,taskComment2:c,taskComment3:d,taskComment4:p})=>[m("table",t,[m("tr",null,[l[0]||(l[0]=m("th",{style:{width:"90px"}},[m("div",{class:"cell"},"巡视范围")],-1)),m("td",null,[m("div",r,o(e.field1),1)])]),m("tr",null,[l[1]||(l[1]=m("th",{style:{width:"90px"}},[m("div",{class:"cell"},"巡视情况")],-1)),m("td",null,[m("div",u,o(e.field2),1)])]),m("tr",null,[l[2]||(l[2]=m("th",{style:{width:"90px"}},[m("div",{class:"cell"},"发现问题及处理意见")],-1)),m("td",null,[m("div",f,o(e.field3),1)])]),l[3]||(l[3]=m("tr",null,[m("td",{colspan:"2"},[m("div",{class:"cell"},[m("div",{class:"table-sign",style:{"padding-left":"30%"}},[m("div",{class:"sign-item"},[m("span",{class:"label",style:{width:"5em"}},"巡视人"),m("span",{class:"value"},"（签名）")]),m("div",{class:"sign-item"},[m("span",{class:"label",style:{width:"5em"}},"日期"),m("span",{class:"value"},[m("span",{style:{"padding-left":"4em"}},"年"),m("span",{style:{"padding-left":"2em"}},"月"),m("span",{style:{"padding-left":"2em"}},"日")])])])])])],-1))])])),footer:d((({formData:e,formTable:t,baseObj:a,uploadAccept:s,taskStart:n,taskComment2:i,taskComment3:c,taskComment4:d})=>l[4]||(l[4]=[m("div",{class:"footer-input"},[m("span",null,"说明：1、本表可用于监理人员质量、安全、进度等的巡视记录。")],-1),m("div",{class:"footer-input"},[m("span",{style:{"text-indent":"3em"}},"2、本表按月装订成册。")],-1)]))),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}]]))}}}));
