System.register(["./index-legacy-09188690.js","./index-legacy-b580af71.js","./FormItemPicker-legacy-fd45c24d.js","./FormItemDate-legacy-c4422d42.js","./FormItemCalendar-legacy-ea787ea1.js","./FormItemPerson-legacy-e6e57748.js","./FormItemCoord-legacy-e6ddc9b7.js","./index-legacy-645a3645.js","./verder-legacy-e6127216.js","./vant-legacy-b51a9379.js","./form-legacy-20b7cd9d.js","./api-legacy-347ab5d7.js","./formKeys-legacy-257dbd3e.js","./validate-legacy-4e8f0db9.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js"],(function(e,t){"use strict";var r,a,o,l,i,m,n,p,s,d,u,f,c,D,b;return{setters:[e=>{r=e.h,a=e._},e=>{o=e.F},e=>{l=e.F},e=>{i=e.F},e=>{m=e.F},e=>{n=e.F},e=>{p=e.F},e=>{s=e.U},e=>{d=e.Q,u=e.R,f=e.X,c=e.V,D=e.k,b=e.Z},null,null,null,null,null,null,null,null,null],execute:function(){e("default",a({name:"QualityThreatSupervisor",components:{FlowForm:o,FormItemPicker:l,FormItemDate:i,FormItemCalendar:m,FormItemPerson:n,FormItemCoord:p,UploadFiles:s},props:{},emits:[],setup(e,{attrs:t,slots:r,emit:a}){},data(){var e,t;return{type:(null===(e=this.$route.query)||void 0===e?void 0:e.type)||"",taskKey:(null===(t=this.$route.query)||void 0===t?void 0:t.taskKey)||"",entityName:"QualityProblem",formKey:"QualityThreatSupervisor",modelKey:"quality_hidden_danger_flow_jl",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-quality/quality/form/query",submit:"/cybereng-quality/form/commit"},formData:{type:"2",id:void 0,portalId:void 0,subProjectId:"",subProjectName:"",unitEngineeringId:"",unitEngineeringName:"",divisionEngineeringId:"",divisionEngineeringName:"",longitude:"",latitude:"",problemCode:void 0,rectifyDate:"",overdueState:"",problemContent:"",beforeFileToken:"",problemReportor:"",problemReportorFullname:"",problemReportorDeptName:"",problemReportorDeptCode:"",problemChecker:"",problemCheckerFullname:"",problemCheckerDeptName:"",problemCheckerDeptCode:"",problemRectifier:"",problemRectifierFullname:"",problemRectifierDeptName:"",problemRectifierDeptCode:"",problemSupervisor:"",problemSupervisorFullname:"",problemSupervisorDeptName:"",problemSupervisorDeptCode:"",problemRectifyApprover:"",problemRectifyApproverFullname:"",problemRectifyApproverDeptName:"",problemRectifyApproverDeptCode:"",rectifyState:"待整改",rectifyMeasures:"",rectifySituation:"",afterFileToken:"",finishDate:""}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},unitEngineeringList(){if(1==this.portal.type){if(!this.formData.subProjectId)return[];const e=this.subProjectList.find((e=>e.id==this.formData.subProjectId));return e&&e.children||[]}return this.subProjectList[0]&&this.subProjectList[0].children||[]},divisionEngineeringList(){if(!this.formData.unitEngineeringId)return[];const e=this.unitEngineeringList.find((e=>e.id==this.formData.unitEngineeringId));return e&&e.children||[]},canEdit0(){return"add"===this.type||"UserTask_0"===this.taskKey},canEdit1(){return"execute"===this.type&&"UserTask_1"===this.taskKey},canEdit2(){return"execute"===this.type&&"UserTask_2"===this.taskKey}},watch:{},created(){},mounted(){this.initForm()},methods:{copyCallBack(e){this.formData.divisionEngineeringId=e.divisionEngineeringId,this.formData.divisionEngineeringName=e.divisionEngineeringName,this.formData.portalId=e.portalId,this.formData.problemChecker=e.problemChecker,this.formData.problemCheckerDeptCode=e.problemCheckerDeptCode,this.formData.problemCheckerDeptName=e.problemCheckerDeptName,this.formData.problemCheckerFullname=e.problemCheckerFullname,this.formData.problemRectifier=e.problemRectifier,this.formData.problemRectifierDeptCode=e.problemRectifierDeptCode,this.formData.problemRectifierDeptName=e.problemRectifierDeptName,this.formData.problemRectifierFullname=e.problemRectifierFullname,this.formData.problemRectifyApprover=e.problemRectifyApprover,this.formData.problemRectifyApproverDeptCode=e.problemRectifyApproverDeptCode,this.formData.problemRectifyApproverDeptName=e.problemRectifyApproverDeptName,this.formData.problemRectifyApproverFullname=e.problemRectifyApproverFullname,this.formData.problemReportor=e.problemReportor,this.formData.problemReportorDeptCode=e.problemReportorDeptCode,this.formData.problemReportorDeptName=e.problemReportorDeptName,this.formData.problemReportorFullname=e.problemReportorFullname,this.formData.problemSupervisor=e.problemSupervisor,this.formData.problemSupervisorDeptCode=e.problemSupervisorDeptCode,this.formData.problemSupervisorDeptName=e.problemSupervisorDeptName,this.formData.problemSupervisorFullname=e.problemSupervisorFullname,this.formData.rectifyDate=e.rectifyDate,this.formData.rectifyState=e.rectifyState,this.formData.subProjectId=e.subProjectId,this.formData.subProjectName=e.subProjectName,this.formData.type=e.type,this.formData.unitEngineeringId=e.unitEngineeringId,this.formData.unitEngineeringName=e.unitEngineeringName},async initForm(){if("add"===this.type){if(this.formData.portalId=this.portalId,1!=this.portal.type){const e=this.subProjectList.find((e=>e.portalId==this.portal.id));this.formData.subProjectId=null==e?void 0:e.id,this.formData.subProjectName=null==e?void 0:e.nodeName}const{userName:e="",userFullname:t="",orgList:r=[]}=this.user||{},a=r.find((e=>{var t;return e.portalId==(null===(t=this.portal)||void 0===t?void 0:t.id)}))||r[0],o=(null==a?void 0:a.name)||"",l=(null==a?void 0:a.orgNo)||"";this.formData.problemReportor=e,this.formData.problemReportorFullname=t,this.formData.problemReportorDeptName=o,this.formData.problemReportorDeptCode=l,this.formData.problemRectifyApprover=e,this.formData.problemRectifyApproverFullname=t,this.formData.problemRectifyApproverDeptName=o,this.formData.problemRectifyApproverDeptCode=l}else this.$nextTick((()=>{this.$refs.FlowForm.onInit(this.service.query,(e=>{const{detailParamList:t=[],entityObject:r}=e;this.detailParamList=t,this.formData={...this.formData,...r}}))}))},async onDraft(){try{const e={...this.formData,rectifyState:"暂存"};this.$refs.FlowForm.onSaveDraft(this.service.submit,e)}catch(e){console.log(e)}},async onSubmit(){try{await this.$refs.form.validate();let e="待确认";"UserTask_0"===this.taskKey?e="待确认":"UserTask_1"===this.taskKey?e="待整改":"UserTask_2"===this.taskKey||"UserTask_3"===this.taskKey?e="待审批":"UserTask_4"===this.taskKey&&(e="整改完成",this.formData.finishDate=this.$dayjs().format("YYYY-MM-DD HH:mm:ss"));const t={...this.formData,rectifyState:e};this.$refs.FlowForm.onSubmit(this.service.submit,t)}catch(e){console.log(e)}},async updateFiles(){return new Promise((async(e,t)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),e()}catch(r){this.$showToast({message:"文件更新失败!"}),t()}}))},afterSubmit(e,t){this.updateFiles(),"submit"===e&&"UserTask_1"==this.taskKey&&function(e){r({url:"/cybereng-quality/quality/problem/delayTask",method:"get",params:e})}({id:this.formData.id})},handleSubProjectChange(e={}){this.formData.unitEngineeringId="",this.formData.unitEngineeringName="",this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""},handleUnitEngineeringChange(e={}){this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""}}},[["render",function(e,t,r,a,o,l){const i=d("van-field"),m=d("FormItemPicker"),n=d("FormItemCoord"),p=d("FormItemCalendar"),s=d("van-cell-group"),y=d("UploadFiles"),h=d("FormItemPerson"),g=d("van-form"),v=d("FlowForm");return u(),f(v,{ref:"FlowForm","model-key":o.modelKey,"form-key":o.formKey,"entity-name":o.entityName,"detail-param-list":o.detailParamList,"detail-entity-name-list":o.detailEntityNameList,onDraftClick:l.onDraft,onSubmitClick:l.onSubmit,onAfterSubmit:l.afterSubmit,onCopyCallBack:l.copyCallBack},{default:c((()=>[D(g,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:c((()=>[D(s,{border:!1},{default:c((()=>[o.formData.problemCode?(u(),f(i,{key:0,modelValue:o.formData.problemCode,"onUpdate:modelValue":t[0]||(t[0]=e=>o.formData.problemCode=e),label:"质量问题单号",placeholder:"审批完成后自动生成",readonly:""},null,8,["modelValue"])):b("",!0),D(m,{label:"子工程",value:o.formData.subProjectId,"onUpdate:value":t[1]||(t[1]=e=>o.formData.subProjectId=e),text:o.formData.subProjectName,"onUpdate:text":t[2]||(t[2]=e=>o.formData.subProjectName=e),columns:[...l.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:1!=l.portal.type||"view"===o.type||!l.canEdit0,onChange:l.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),D(m,{label:"单位工程",value:o.formData.unitEngineeringId,"onUpdate:value":t[3]||(t[3]=e=>o.formData.unitEngineeringId=e),text:o.formData.unitEngineeringName,"onUpdate:text":t[4]||(t[4]=e=>o.formData.unitEngineeringName=e),columns:[...l.unitEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择单位工程",required:"",rules:[{required:!0,message:"请选择单位工程"}],readonly:"view"===o.type||!l.canEdit0,onChange:l.handleUnitEngineeringChange},null,8,["value","text","columns","readonly","onChange"]),D(m,{label:"分部工程",value:o.formData.divisionEngineeringId,"onUpdate:value":t[5]||(t[5]=e=>o.formData.divisionEngineeringId=e),text:o.formData.divisionEngineeringName,"onUpdate:text":t[6]||(t[6]=e=>o.formData.divisionEngineeringName=e),columns:[...l.divisionEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择分部工程",required:"",rules:[{required:!0,message:"请选择分部工程"}],readonly:"view"===o.type||!l.canEdit0},null,8,["value","text","columns","readonly"]),D(n,{label:"定位",longitude:o.formData.longitude,"onUpdate:longitude":t[7]||(t[7]=e=>o.formData.longitude=e),latitude:o.formData.latitude,"onUpdate:latitude":t[8]||(t[8]=e=>o.formData.latitude=e),title:"选择定位",readonly:"view"===o.type||!l.canEdit0},null,8,["longitude","latitude","readonly"]),D(p,{label:"整改期限",value:o.formData.rectifyDate,"onUpdate:value":t[9]||(t[9]=e=>o.formData.rectifyDate=e),title:"选择整改期限",required:"",rules:[{required:!0,message:"请选择整改期限"}],readonly:"view"===o.type||!l.canEdit0},null,8,["value","readonly"]),"add"!==o.type?(u(),f(i,{key:1,modelValue:o.formData.rectifyState,"onUpdate:modelValue":t[10]||(t[10]=e=>o.formData.rectifyState=e),label:"整改状态",placeholder:"",readonly:""},null,8,["modelValue"])):b("",!0),"add"!==o.type?(u(),f(i,{key:2,modelValue:o.formData.overdueState,"onUpdate:modelValue":t[11]||(t[11]=e=>o.formData.overdueState=e),label:"逾期情况",placeholder:"",readonly:""},null,8,["modelValue"])):b("",!0)])),_:1}),D(s,{border:!1},{default:c((()=>[D(i,{label:"问题内容",modelValue:o.formData.problemContent,"onUpdate:modelValue":t[12]||(t[12]=e=>o.formData.problemContent=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入问题内容",required:"",rules:[{required:!0,message:"请输入问题内容"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit0},null,8,["modelValue","readonly"]),D(i,{label:"附件照片","label-align":"top","input-align":"left"},{input:c((()=>[D(y,{ref:"beforeFiles",g9s:o.formData.beforeFileToken,"onUpdate:g9s":t[13]||(t[13]=e=>o.formData.beforeFileToken=e),accept:"image/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===o.type||!l.canEdit0},null,8,["g9s","readonly"])])),_:1})])),_:1}),D(s,{border:!1},{default:c((()=>[D(h,{label:"监理发起人",userName:o.formData.problemReportor,"onUpdate:userName":t[14]||(t[14]=e=>o.formData.problemReportor=e),userFullname:o.formData.problemReportorFullname,"onUpdate:userFullname":t[15]||(t[15]=e=>o.formData.problemReportorFullname=e),deptName:o.formData.problemReportorDeptName,"onUpdate:deptName":t[16]||(t[16]=e=>o.formData.problemReportorDeptName=e),deptCode:o.formData.problemReportorDeptCode,"onUpdate:deptCode":t[17]||(t[17]=e=>o.formData.problemReportorDeptCode=e),title:"选择监理发起人",required:"",rules:[{required:!0,message:"请选择监理发起人"}],readonly:!0},null,8,["userName","userFullname","deptName","deptCode"]),D(i,{modelValue:o.formData.problemReportorDeptName,"onUpdate:modelValue":t[18]||(t[18]=e=>o.formData.problemReportorDeptName=e),label:"发起人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),D(h,{label:"总包确认人",userName:o.formData.problemChecker,"onUpdate:userName":t[19]||(t[19]=e=>o.formData.problemChecker=e),userFullname:o.formData.problemCheckerFullname,"onUpdate:userFullname":t[20]||(t[20]=e=>o.formData.problemCheckerFullname=e),deptName:o.formData.problemCheckerDeptName,"onUpdate:deptName":t[21]||(t[21]=e=>o.formData.problemCheckerDeptName=e),deptCode:o.formData.problemCheckerDeptCode,"onUpdate:deptCode":t[22]||(t[22]=e=>o.formData.problemCheckerDeptCode=e),title:"选择总包确认人",required:"",rules:[{required:!0,message:"请选择总包确认人"}],readonly:"view"===o.type||!l.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),D(i,{modelValue:o.formData.problemCheckerDeptName,"onUpdate:modelValue":t[23]||(t[23]=e=>o.formData.problemCheckerDeptName=e),label:"确认人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),D(h,{"label-width":"8em",label:"施工单位整改人",userName:o.formData.problemRectifier,"onUpdate:userName":t[24]||(t[24]=e=>o.formData.problemRectifier=e),userFullname:o.formData.problemRectifierFullname,"onUpdate:userFullname":t[25]||(t[25]=e=>o.formData.problemRectifierFullname=e),deptName:o.formData.problemRectifierDeptName,"onUpdate:deptName":t[26]||(t[26]=e=>o.formData.problemRectifierDeptName=e),deptCode:o.formData.problemRectifierDeptCode,"onUpdate:deptCode":t[27]||(t[27]=e=>o.formData.problemRectifierDeptCode=e),title:"选择施工单位整改人",required:!l.canEdit0,rules:[{required:!l.canEdit0,message:"请选择施工单位整改人"}],readonly:"view"===o.type||!l.canEdit0&&!l.canEdit1},null,8,["userName","userFullname","deptName","deptCode","required","rules","readonly"]),D(h,{label:"总包审核人",userName:o.formData.problemSupervisor,"onUpdate:userName":t[28]||(t[28]=e=>o.formData.problemSupervisor=e),userFullname:o.formData.problemSupervisorFullname,"onUpdate:userFullname":t[29]||(t[29]=e=>o.formData.problemSupervisorFullname=e),deptName:o.formData.problemSupervisorDeptName,"onUpdate:deptName":t[30]||(t[30]=e=>o.formData.problemSupervisorDeptName=e),deptCode:o.formData.problemSupervisorDeptCode,"onUpdate:deptCode":t[31]||(t[31]=e=>o.formData.problemSupervisorDeptCode=e),title:"选择总包审核人",required:"",rules:[{required:!0,message:"请选择总包审核人"}],readonly:"view"===o.type||!l.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),D(h,{label:"监理审核人",userName:o.formData.problemRectifyApprover,"onUpdate:userName":t[32]||(t[32]=e=>o.formData.problemRectifyApprover=e),userFullname:o.formData.problemRectifyApproverFullname,"onUpdate:userFullname":t[33]||(t[33]=e=>o.formData.problemRectifyApproverFullname=e),deptName:o.formData.problemRectifyApproverDeptName,"onUpdate:deptName":t[34]||(t[34]=e=>o.formData.problemRectifyApproverDeptName=e),deptCode:o.formData.problemRectifyApproverDeptCode,"onUpdate:deptCode":t[35]||(t[35]=e=>o.formData.problemRectifyApproverDeptCode=e),title:"选择监理审核人",required:"",rules:[{required:!0,message:"请选择监理审核人"}],readonly:"view"===o.type||!l.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"])])),_:1}),"view"===o.type||o.taskKey&&"UserTask_0"!==o.taskKey&&"UserTask_1"!==o.taskKey?(u(),f(s,{key:0,border:!1},{default:c((()=>[D(i,{label:"整改措施",modelValue:o.formData.rectifyMeasures,"onUpdate:modelValue":t[36]||(t[36]=e=>o.formData.rectifyMeasures=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改措施",required:"",rules:[{required:!0,message:"请填写整改措施"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit2},null,8,["modelValue","readonly"]),D(i,{label:"整改情况",modelValue:o.formData.rectifySituation,"onUpdate:modelValue":t[37]||(t[37]=e=>o.formData.rectifySituation=e),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改情况",required:"",rules:[{required:!0,message:"请填写整改情况"}],"input-align":"left",readonly:"view"===o.type||!l.canEdit2},null,8,["modelValue","readonly"]),D(i,{label:"附件照片","label-align":"top","input-align":"left"},{input:c((()=>[D(y,{ref:"afterFiles",g9s:o.formData.afterFileToken,"onUpdate:g9s":t[38]||(t[38]=e=>o.formData.afterFileToken=e),accept:"image/*",multiple:!0,"max-count":9,"max-size":52428800,readonly:"view"===o.type||!l.canEdit2},null,8,["g9s","readonly"])])),_:1})])),_:1})):b("",!0)])),_:1},512)])),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit","onCopyCallBack"])}]]))}}}));
