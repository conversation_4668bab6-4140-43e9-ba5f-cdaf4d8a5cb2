import{H as V,p as I,c as H,g as w}from"./codeValueHotTable-b9cf8ce9.js";import{d as p}from"./dateFormat-1fb6392c.js";import{s as L,c as E,a as j,b as A}from"./api-e44c60fe.js";import{Q as h,R as N,S as P,k as i,V as n,U as f,a2 as g,B as m,Y as M,t as Y,v as B,T as O}from"./verder-361ae6c7.js";import{_ as U}from"./index-4829f8e2.js";import{e as _}from"./vant-91101745.js";const v=24*60*60*1e3,C="1900/01/01",T="2050/10/01",F={components:{HotTable:V,phoneOrientButton:I,codeValueHotTable:H},data(){const e=new Date;return{currentDate:e,formattedCurrentDate:p(e),datePickerVisible:!1,date:new Date,minDate:new Date(C),maxDate:new Date(T),project:{},data:[],info:[],dataSetting:{data:[]},dataHasChangedList:[],lastDataParams:{},lastDataVisible:!1,calculating:!1,toolbarVisible:!0}},computed:{isFirstDay(){return this.formattedCurrentDate<=C},isLastDay(){return this.formattedCurrentDate>=T}},methods:{goBack(){this.dataHasChangedList.some(e=>e)?_({title:"当前已有录入，是否继续返回？",confirmButtonColor:"#1989fa"}).then(e=>{this.$router.push("/SafetyMonitoring")}):this.$router.push("/SafetyMonitoring")},async currentDateChange(){this.formattedCurrentDate=p(this.currentDate),await this.initData()},showDatePicker(){this.date=this.formattedCurrentDate.split("/"),this.datePickerVisible=!0},onDateConfirm(){this.formattedCurrentDate===this.date.join("/")?this.datePickerVisible=!1:this.switchDate().then(()=>{this.currentDate=new Date(this.date),this.currentDateChange(),this.datePickerVisible=!1}).catch(()=>{})},onSubmit(){if(!this.allowNext())return!1;_({title:"您确认提交吗？",confirmButtonColor:"#1989fa"}).then(()=>{const e=this.$refs.inputTable.hotInstance.getSourceData().filter((s,a)=>this.dataHasChangedList[a]),{id:t,projectDamId:o}=this.project,c=localStorage.getItem("app");try{const{id:s}=JSON.parse(c).USER_INFO;this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),L(s,{valueIsAuto:0,valueType:0,valueList:e,codeId:null,groupId:null,projectId:t,damId:o,isCover:1}).then(()=>{this.$closeToast(),this.$showToast("保存成功"),this.getProject()}).catch(()=>{this.$closeToast(),this.$showToast("保存失败")})}catch(s){}}).catch(()=>{})},allowNext(){return this.calculating?(this.$showToast("正在计算中，请稍候"),!1):!0},switchDate(){return this.allowNext()?this.dataHasChangedList.some(e=>e)?_({title:"当前已有录入，是否继续切换？",confirmButtonColor:"#1989fa"}):new Promise(e=>e()):new Promise((e,t)=>t())},previousDate(){this.switchDate().then(()=>{this.currentDate=new Date(this.currentDate.getTime()-v),this.currentDateChange()}).catch(()=>{})},nextDate(){this.switchDate().then(()=>{this.currentDate=new Date(this.currentDate.getTime()+v),this.currentDateChange()}).catch(()=>{})},async setLastDataParams(){const{id:e,projectInstrId:t,projectDamId:o,projectSort:c,lastRecTime:s}=this.project;if(s){const a=p(s,"YYYY-MM-DD [00:00:00]"),r=p(s,"YYYY-MM-DD [23:59:59]");this.lastDataParams={startTime:a,endTime:r,dayStart:a,dayEnd:r,projectId:e,instrId:t,damId:o,sort:c,codeAutoList:[0,1],valueTypeList:[0,1,2],valueStatusList:[0,1,2],valueCheckList:[0,1]}}else this.lastDataParams={}},openLastData(){this.lastDataVisible=!0,this.$nextTick(e=>{this.setLastDataParams()})},calculate(){this.calculating=!0,this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),E(this.data.map(e=>({id:e.ID,codeId:e.CODE_ID,watchTime:e.WATCH_TIME,valueIsAuto:e.VALUE_IS_AUTO,valueList:this.info.map(t=>({status:e.VALUE_STATUS,value:e[t.valueVectorFieldName],vectorId:t.valueVectorId}))}))).then(e=>{const{valueRecordList:t}=e;Array.isArray(t)&&(this.data.forEach((o,c)=>{const s=t.find(a=>a.codeId===o.CODE_ID);s&&Array.isArray(s.valueList)&&s.valueList.forEach(({vectorId:a,value:r})=>{const{valueVectorFieldName:d}=this.info.find(l=>l.valueVectorId===a);o[d]!==r&&(o[d]=r,this.dataHasChangedList[c]=!0)})}),this.dataSetting.data=this.data,this.$refs.inputTable.hotInstance.updateSettings(this.dataSetting))}).finally(e=>{this.calculating=!1,this.$closeToast()})},initData(){j(this.project,p(this.currentDate,"YYYY-MM-DD [00:00:00]")).then(e=>{const{data:t,info:o}=e;if(Array.isArray(t)&&Array.isArray(o)){this.dataHasChangedList=t.map(a=>!1);const c=t.map(a=>a.CODE_NAME),s=Math.max(...c.map(a=>w(a,14)+5*2));this.data=t,this.info=o,this.dataSetting={data:t,columns:o.map(a=>({data:a.valueVectorFieldName,width:w(a.valueVectorName)+20})),colHeaders:o.map(a=>a.valueVectorName),rowHeaders:c,rowHeaderWidth:Math.max(s,50),beforeChange:a=>{for(const r of a){const d=r[3];if(d&&!/^\d+(\.\d+)?$/.test(d))return this.$showToast("请输入数字类型"),!1}},afterChange:a=>{Array.isArray(a)&&a.forEach(([r,d,l,D])=>{l!==D&&(this.dataHasChangedList[r]=!0)})}}}else this.data=[],this.info=[],this.dataHasChangedList=[],this.dataSetting={data:[]};this.$refs.inputTable.hotInstance.updateSettings(this.dataSetting)})},async getProject(){const{id:e}=this.$route.params,t=await A(e);this.project=t,await this.currentDateChange()},checkToolbarVisible(){this.toolbarVisible=this.hasScrolledToTop()},getScrollTop(){let e=0,t=0,o=0;return document.body&&(t=document.body.scrollTop),document.documentElement&&(o=document.documentElement.scrollTop),e=t-o>0?t:o,e},getScrollHeight(){let e=0,t=0,o=0;return document.body&&(t=document.body.scrollHeight),document.documentElement&&(o=document.documentElement.scrollHeight),e=t-o>0?t:o,e},getWindowHeight(){let e=0;return document.compatMode==="CSS1Compat"?e=document.documentElement.clientHeight:e=document.body.clientHeight,e},hasScrolledToTop(){return this.getScrollTop()===0},hasScrolledToBottom(){return this.getScrollTop()+this.getWindowHeight()===this.getScrollHeight()}},beforeDestroy(){window.removeEventListener("scroll",this.checkToolbarVisible)},mounted(){this.getProject(),window.addEventListener("scroll",this.checkToolbarVisible)}},W={class:"monitor-container"},R={class:"monitor-table-style no-selection-handle"},z={class:"px-9 toolbar"};function J(e,t,o,c,s,a){const r=h("Navbar"),d=h("van-button"),l=h("van-col"),D=h("van-date-picker"),y=h("van-popup"),b=h("van-row"),x=h("hot-table"),S=h("phone-orient-button"),k=h("code-value-hot-table");return N(),P("div",W,[i(r,{back:!e.envFeishu,backEvent:a.goBack,title:s.project.projectName},{right:n(()=>[f("span",{class:"cursor-pointer",style:{height:"42px","line-height":"42px",color:"white","font-size":"17px"},onClick:t[0]||(t[0]=g((...u)=>a.onSubmit&&a.onSubmit(...u),["stop"]))},"提交")]),_:1},8,["back","backEvent","title"]),i(b,{type:"flex",justify:"space-between",align:"center",class:"flex-shrink-0",style:{height:"48px",padding:"0 4%"}},{default:n(()=>[i(l,{span:7,style:{display:"flex","align-items":"center","justify-content":"flex-start"}},{default:n(()=>[i(d,{icon:"arrow-left",plain:"",color:"#9b9b9b",size:"mini",style:{"margin-right":"6px"},disabled:a.isFirstDay,onClick:g(a.previousDate,["stop"])},null,8,["disabled","onClick"]),t[6]||(t[6]=m(" 前一天 "))]),_:1,__:[6]}),i(l,{span:10,style:{display:"flex","align-items":"center","justify-content":"center"},onClick:a.showDatePicker},{default:n(()=>[t[7]||(t[7]=f("i",{class:"iconfont icon-rili text-blue mr-3"},null,-1)),m(" "+M(s.formattedCurrentDate),1)]),_:1,__:[7]},8,["onClick"]),i(y,{show:s.datePickerVisible,"onUpdate:show":t[3]||(t[3]=u=>s.datePickerVisible=u),round:"",position:"bottom"},{default:n(()=>[i(D,{modelValue:s.date,"onUpdate:modelValue":t[1]||(t[1]=u=>s.date=u),title:"请选择","min-date":s.minDate,"max-date":s.maxDate,onCancel:t[2]||(t[2]=u=>s.datePickerVisible=!1),onConfirm:a.onDateConfirm},null,8,["modelValue","min-date","max-date","onConfirm"])]),_:1},8,["show"]),i(l,{span:7,style:{display:"flex","align-items":"center","justify-content":"flex-end"}},{default:n(()=>[t[8]||(t[8]=m(" 后一天 ")),i(d,{icon:"arrow",plain:"",color:"#9b9b9b",size:"mini",style:{"margin-left":"6px"},disabled:a.isLastDay,onClick:g(a.nextDate,["stop"])},null,8,["disabled","onClick"])]),_:1,__:[8]})]),_:1}),f("div",R,[i(x,{ref:"inputTable",stretchH:"all",class:"inputTable","license-key":"non-commercial-and-evaluation",width:"100%",height:"800",settings:s.dataSetting},null,8,["settings"])]),i(S),i(O,{name:"van-fade"},{default:n(()=>[Y(f("div",z,[i(b,{type:"flex",align:"center"},{default:n(()=>[i(l,{span:12,class:"text-center cursor-pointer",onClick:g(a.openLastData,["stop"])},{default:n(()=>t[9]||(t[9]=[m(" 上期数据 ")])),_:1,__:[9]},8,["onClick"]),i(l,{span:12,class:"text-center cursor-pointer",onClick:g(a.calculate,["stop"])},{default:n(()=>t[10]||(t[10]=[m(" 计算 ")])),_:1,__:[10]},8,["onClick"])]),_:1})],512),[[B,s.toolbarVisible]])]),_:1}),i(y,{show:s.lastDataVisible,"onUpdate:show":t[5]||(t[5]=u=>s.lastDataVisible=u),position:"bottom",class:"d-flex flex-column",style:{height:"70%"}},{default:n(()=>[i(b,{type:"flex",justify:"space-between",align:"center",class:"px-8 flex-shrink-0",style:{height:"42px"}},{default:n(()=>[i(l,null,{default:n(()=>t[11]||(t[11]=[m("上期数据")])),_:1,__:[11]}),i(l,null,{default:n(()=>[f("a",{class:"text-blue",onClick:t[4]||(t[4]=u=>s.lastDataVisible=!1)},"关闭")]),_:1})]),_:1}),i(k,{class:"flex-grow-1",params:s.lastDataParams},null,8,["params"])]),_:1},8,["show"])])}const $=U(F,[["render",J],["__scopeId","data-v-f97ede06"]]);export{$ as default};
