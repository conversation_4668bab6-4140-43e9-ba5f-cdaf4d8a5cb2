System.register(["./codeValueHotTable-legacy-ce2ab6ec.js","./dateFormat-legacy-dd142601.js","./api-legacy-38baf147.js","./verder-legacy-e6127216.js","./index-legacy-09188690.js","./vant-legacy-b51a9379.js"],(function(t,e){"use strict";var a,i,s,o,n,l,r,c,d,h,u,m,p,f,g,b,D,y,v,w,C,T,x;return{setters:[t=>{a=t.H,i=t.p,s=t.c,o=t.g},t=>{n=t.d},t=>{l=t.s,r=t.c,c=t.a,d=t.b},t=>{h=t.Q,u=t.R,m=t.S,p=t.k,f=t.V,g=t.U,b=t.a2,D=t.B,y=t.Y,v=t.t,w=t.v,C=t.T},t=>{T=t._},t=>{x=t.e}],execute:function(){var e=document.createElement("style");e.textContent='@charset "UTF-8";.monitor-container[data-v-f97ede06]{display:flex;flex-direction:column;height:100%}[data-v-f97ede06] .handsontable.ht_clone_top_left_corner .colHeader.cornerHeader:before{content:"测点"}.toolbar[data-v-f97ede06]{position:fixed;z-index:121;bottom:2.93333vw;width:100%;height:10.66667vw;line-height:10.66667vw}.toolbar>div[data-v-f97ede06]{width:100%;height:100%;color:#fff;background-color:#000;opacity:.7;border-radius:5.33333vw}.toolbar>div[data-v-f97ede06] :before{position:absolute;left:50%;content:"|"}.inputTable[data-v-f97ede06]{height:26.66667vw}\n',document.head.appendChild(e);const _=864e5,k="1900/01/01",S="2050/10/01",V={class:"monitor-container"},I={class:"monitor-table-style no-selection-handle"},j={class:"px-9 toolbar"};t("default",T({components:{HotTable:a,phoneOrientButton:i,codeValueHotTable:s},data(){const t=new Date;return{currentDate:t,formattedCurrentDate:n(t),datePickerVisible:!1,date:new Date,minDate:new Date(k),maxDate:new Date(S),project:{},data:[],info:[],dataSetting:{data:[]},dataHasChangedList:[],lastDataParams:{},lastDataVisible:!1,calculating:!1,toolbarVisible:!0}},computed:{isFirstDay(){return this.formattedCurrentDate<=k},isLastDay(){return this.formattedCurrentDate>=S}},methods:{goBack(){this.dataHasChangedList.some((t=>t))?x({title:"当前已有录入，是否继续返回？",confirmButtonColor:"#1989fa"}).then((t=>{this.$router.push("/SafetyMonitoring")})):this.$router.push("/SafetyMonitoring")},async currentDateChange(){this.formattedCurrentDate=n(this.currentDate),await this.initData()},showDatePicker(){this.date=this.formattedCurrentDate.split("/"),this.datePickerVisible=!0},onDateConfirm(){this.formattedCurrentDate===this.date.join("/")?this.datePickerVisible=!1:this.switchDate().then((()=>{this.currentDate=new Date(this.date),this.currentDateChange(),this.datePickerVisible=!1})).catch((()=>{}))},onSubmit(){if(!this.allowNext())return!1;x({title:"您确认提交吗？",confirmButtonColor:"#1989fa"}).then((()=>{const t=this.$refs.inputTable.hotInstance.getSourceData().filter(((t,e)=>this.dataHasChangedList[e])),{id:e,projectDamId:a}=this.project,i=localStorage.getItem("app");try{const{id:s}=JSON.parse(i).USER_INFO;this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),l(s,{valueIsAuto:0,valueType:0,valueList:t,codeId:null,groupId:null,projectId:e,damId:a,isCover:1}).then((()=>{this.$closeToast(),this.$showToast("保存成功"),this.getProject()})).catch((()=>{this.$closeToast(),this.$showToast("保存失败")}))}catch(s){}})).catch((()=>{}))},allowNext(){return!this.calculating||(this.$showToast("正在计算中，请稍候"),!1)},switchDate(){return this.allowNext()?this.dataHasChangedList.some((t=>t))?x({title:"当前已有录入，是否继续切换？",confirmButtonColor:"#1989fa"}):new Promise((t=>t())):new Promise(((t,e)=>e()))},previousDate(){this.switchDate().then((()=>{this.currentDate=new Date(this.currentDate.getTime()-_),this.currentDateChange()})).catch((()=>{}))},nextDate(){this.switchDate().then((()=>{this.currentDate=new Date(this.currentDate.getTime()+_),this.currentDateChange()})).catch((()=>{}))},async setLastDataParams(){const{id:t,projectInstrId:e,projectDamId:a,projectSort:i,lastRecTime:s}=this.project;if(s){const o=n(s,"YYYY-MM-DD [00:00:00]"),l=n(s,"YYYY-MM-DD [23:59:59]");this.lastDataParams={startTime:o,endTime:l,dayStart:o,dayEnd:l,projectId:t,instrId:e,damId:a,sort:i,codeAutoList:[0,1],valueTypeList:[0,1,2],valueStatusList:[0,1,2],valueCheckList:[0,1]}}else this.lastDataParams={}},openLastData(){this.lastDataVisible=!0,this.$nextTick((t=>{this.setLastDataParams()}))},calculate(){this.calculating=!0,this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),r(this.data.map((t=>({id:t.ID,codeId:t.CODE_ID,watchTime:t.WATCH_TIME,valueIsAuto:t.VALUE_IS_AUTO,valueList:this.info.map((e=>({status:t.VALUE_STATUS,value:t[e.valueVectorFieldName],vectorId:e.valueVectorId})))})))).then((t=>{const{valueRecordList:e}=t;Array.isArray(e)&&(this.data.forEach(((t,a)=>{const i=e.find((e=>e.codeId===t.CODE_ID));i&&Array.isArray(i.valueList)&&i.valueList.forEach((({vectorId:e,value:i})=>{const{valueVectorFieldName:s}=this.info.find((t=>t.valueVectorId===e));t[s]!==i&&(t[s]=i,this.dataHasChangedList[a]=!0)}))})),this.dataSetting.data=this.data,this.$refs.inputTable.hotInstance.updateSettings(this.dataSetting))})).finally((t=>{this.calculating=!1,this.$closeToast()}))},initData(){c(this.project,n(this.currentDate,"YYYY-MM-DD [00:00:00]")).then((t=>{const{data:e,info:a}=t;if(Array.isArray(e)&&Array.isArray(a)){this.dataHasChangedList=e.map((t=>!1));const t=e.map((t=>t.CODE_NAME)),i=Math.max(...t.map((t=>o(t,14)+10)));this.data=e,this.info=a,this.dataSetting={data:e,columns:a.map((t=>({data:t.valueVectorFieldName,width:o(t.valueVectorName)+20}))),colHeaders:a.map((t=>t.valueVectorName)),rowHeaders:t,rowHeaderWidth:Math.max(i,50),beforeChange:t=>{for(const e of t){const t=e[3];if(t&&!/^\d+(\.\d+)?$/.test(t))return this.$showToast("请输入数字类型"),!1}},afterChange:t=>{Array.isArray(t)&&t.forEach((([t,e,a,i])=>{a!==i&&(this.dataHasChangedList[t]=!0)}))}}}else this.data=[],this.info=[],this.dataHasChangedList=[],this.dataSetting={data:[]};this.$refs.inputTable.hotInstance.updateSettings(this.dataSetting)}))},async getProject(){const{id:t}=this.$route.params,e=await d(t);this.project=e,await this.currentDateChange()},checkToolbarVisible(){this.toolbarVisible=this.hasScrolledToTop()},getScrollTop(){let t=0,e=0,a=0;return document.body&&(e=document.body.scrollTop),document.documentElement&&(a=document.documentElement.scrollTop),t=e-a>0?e:a,t},getScrollHeight(){let t=0,e=0,a=0;return document.body&&(e=document.body.scrollHeight),document.documentElement&&(a=document.documentElement.scrollHeight),t=e-a>0?e:a,t},getWindowHeight(){let t=0;return t="CSS1Compat"===document.compatMode?document.documentElement.clientHeight:document.body.clientHeight,t},hasScrolledToTop(){return 0===this.getScrollTop()},hasScrolledToBottom(){return this.getScrollTop()+this.getWindowHeight()===this.getScrollHeight()}},beforeDestroy(){window.removeEventListener("scroll",this.checkToolbarVisible)},mounted(){this.getProject(),window.addEventListener("scroll",this.checkToolbarVisible)}},[["render",function(t,e,a,i,s,o){const n=h("Navbar"),l=h("van-button"),r=h("van-col"),c=h("van-date-picker"),d=h("van-popup"),T=h("van-row"),x=h("hot-table"),_=h("phone-orient-button"),k=h("code-value-hot-table");return u(),m("div",V,[p(n,{back:!t.envFeishu,backEvent:o.goBack,title:s.project.projectName},{right:f((()=>[g("span",{class:"cursor-pointer",style:{height:"42px","line-height":"42px",color:"white","font-size":"17px"},onClick:e[0]||(e[0]=b(((...t)=>o.onSubmit&&o.onSubmit(...t)),["stop"]))},"提交")])),_:1},8,["back","backEvent","title"]),p(T,{type:"flex",justify:"space-between",align:"center",class:"flex-shrink-0",style:{height:"48px",padding:"0 4%"}},{default:f((()=>[p(r,{span:7,style:{display:"flex","align-items":"center","justify-content":"flex-start"}},{default:f((()=>[p(l,{icon:"arrow-left",plain:"",color:"#9b9b9b",size:"mini",style:{"margin-right":"6px"},disabled:o.isFirstDay,onClick:b(o.previousDate,["stop"])},null,8,["disabled","onClick"]),e[6]||(e[6]=D(" 前一天 "))])),_:1,__:[6]}),p(r,{span:10,style:{display:"flex","align-items":"center","justify-content":"center"},onClick:o.showDatePicker},{default:f((()=>[e[7]||(e[7]=g("i",{class:"iconfont icon-rili text-blue mr-3"},null,-1)),D(" "+y(s.formattedCurrentDate),1)])),_:1,__:[7]},8,["onClick"]),p(d,{show:s.datePickerVisible,"onUpdate:show":e[3]||(e[3]=t=>s.datePickerVisible=t),round:"",position:"bottom"},{default:f((()=>[p(c,{modelValue:s.date,"onUpdate:modelValue":e[1]||(e[1]=t=>s.date=t),title:"请选择","min-date":s.minDate,"max-date":s.maxDate,onCancel:e[2]||(e[2]=t=>s.datePickerVisible=!1),onConfirm:o.onDateConfirm},null,8,["modelValue","min-date","max-date","onConfirm"])])),_:1},8,["show"]),p(r,{span:7,style:{display:"flex","align-items":"center","justify-content":"flex-end"}},{default:f((()=>[e[8]||(e[8]=D(" 后一天 ")),p(l,{icon:"arrow",plain:"",color:"#9b9b9b",size:"mini",style:{"margin-left":"6px"},disabled:o.isLastDay,onClick:b(o.nextDate,["stop"])},null,8,["disabled","onClick"])])),_:1,__:[8]})])),_:1}),g("div",I,[p(x,{ref:"inputTable",stretchH:"all",class:"inputTable","license-key":"non-commercial-and-evaluation",width:"100%",height:"800",settings:s.dataSetting},null,8,["settings"])]),p(_),p(C,{name:"van-fade"},{default:f((()=>[v(g("div",j,[p(T,{type:"flex",align:"center"},{default:f((()=>[p(r,{span:12,class:"text-center cursor-pointer",onClick:b(o.openLastData,["stop"])},{default:f((()=>e[9]||(e[9]=[D(" 上期数据 ")]))),_:1,__:[9]},8,["onClick"]),p(r,{span:12,class:"text-center cursor-pointer",onClick:b(o.calculate,["stop"])},{default:f((()=>e[10]||(e[10]=[D(" 计算 ")]))),_:1,__:[10]},8,["onClick"])])),_:1})],512),[[w,s.toolbarVisible]])])),_:1}),p(d,{show:s.lastDataVisible,"onUpdate:show":e[5]||(e[5]=t=>s.lastDataVisible=t),position:"bottom",class:"d-flex flex-column",style:{height:"70%"}},{default:f((()=>[p(T,{type:"flex",justify:"space-between",align:"center",class:"px-8 flex-shrink-0",style:{height:"42px"}},{default:f((()=>[p(r,null,{default:f((()=>e[11]||(e[11]=[D("上期数据")]))),_:1,__:[11]}),p(r,null,{default:f((()=>[g("a",{class:"text-blue",onClick:e[4]||(e[4]=t=>s.lastDataVisible=!1)},"关闭")])),_:1})])),_:1}),p(k,{class:"flex-grow-1",params:s.lastDataParams},null,8,["params"])])),_:1},8,["show"])])}],["__scopeId","data-v-f97ede06"]]))}}}));
