import{h as l,d as h}from"./dateFormat-1fb6392c.js";import{d as T,f as w}from"./filterDropdownItem-8764eaeb.js";import{p as C,c as D}from"./codeValueHotTable-b9cf8ce9.js";import{_,q as L}from"./index-4829f8e2.js";import{R as f,S as g,A as Y,Q as r,k as i,V as k,U as c,Y as u}from"./verder-361ae6c7.js";import{d as I,b as O}from"./api-e44c60fe.js";import"./vant-91101745.js";const S={props:{width:{type:String,default:"100%"},height:{type:String,default:"200px"},data:{type:Object,default:()=>({data:[]})}},data(){return{echarts:null}},watch:{data(){this.echarts&&this.echarts.setOption(this.getOption(),{notMerge:!0})}},methods:{getXAxisData(){const{data:t,startTime:e,endTime:s}=this.data,a=l(e).to(l(s),!0).split(" ")[1];let o;switch(a){case"years":{o="YYYY";break}case"year":case"months":{o="YYYY-MM";break}case"month":case"days":{o="MM-DD";break}case"day":{o="HH:mm";break}}return t.map(d=>({watchTime:d.watchTime,value:h(d.watchTime,o)}))},getOption(){const{codeName:t,vectorName:e,data:s}=this.data;return{color:["#5B8FF9","#5AD8A6","#5D7092","#F6BD16","#E8684A","#6DC8EC"],tooltip:{trigger:"axis",confine:!0,backgroundColor:"#fff",textStyle:{color:"#434343"},axisPointer:{lineStyle:{color:"#82ceff"}},formatter:n=>{const{data:a,seriesName:o}=n[0],{watchTime:d,value:p}=a;return'\n              <div class="process-line-tooltip pt-6 px-5 pb-5">\n                <p class="text-16 bold mb-3">'.concat(o,'</p>\n                <p class="mb-3">测值: <span class="text-blue">').concat(p,'</span></p>\n                <p class="d-flex align-items-center mb-0">\n                  <i class="van-icon van-icon-clock-o text-gray-7-1 text-16 mr-3"></i>\n                  <span class="text-gray-5-9 text-13">\n                    ').concat(h(d,"YYYY-MM-DD HH:mm"),"\n                  </span>\n                </p>\n              </div>\n            ")}},grid:{top:"3%",left:16,right:20,bottom:14,containLabel:!0},xAxis:{axisTick:{lineStyle:{color:"#e7e7e7"}},axisLine:{lineStyle:{color:"#868686"}},axisLabel:{textStyle:{color:"#8c8c8c"}},data:this.getXAxisData()},yAxis:{name:e,splitLine:{lineStyle:{color:"#e7e7e7"}},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{textStyle:{color:"#8c8c8c"}}},series:[{name:t,type:"line",data:s.map(n=>({watchTime:n.watchTime,value:n.value}))}]}},resize(t){this.echarts&&this.echarts.resize(t)}},beforeDestroy(){this.echarts&&this.echarts.dispose()},mounted(){this.echarts=L(this.$el)}};function A(t,e,s,n,a,o){return f(),g("div",{style:Y({width:s.width,height:s.height})},null,4)}const H=_(S,[["render",A]]);const P={components:{dateRangeDropdownItem:T,filterDropdownItem:w,phoneOrientButton:C,processLineChart:H,codeValueHotTable:D},data(){return{project:{},codeOptions:[],codeId:"",vectorOptions:[],vectorId:"",chartHeight:"186px",echartsData:{data:[]},hotTableParams:{},params:{startTime:"",endTime:""}}},computed:{dateRangeTitle(){const{startTime:t,endTime:e}=this.params;return!t||!e?"":"".concat(h(t)," ~ ").concat(h(e))}},methods:{goBack(){const{id:t}=this.$route.params,e=this.$refs.orientButton.getOrientation();this.$router.push("/SafetyMonitoring/".concat(t,"?orientationType=").concat(e||""))},dateRangeLoad({startDate:t,endDate:e}){this.params.startTime=l(t.join("/")).format("YYYY-MM-DD [00:00:00]"),this.params.endTime=l(e.join("/")).format("YYYY-MM-DD [23:59:59]")},dateRangeConfirm(t){this.dateRangeLoad(t),this.setHotTableParams()},codeConfirm(t){this.codeId=t,this.setHotTableParams()},vectorConfirm(t){this.vectorId=t,this.setHotTableParams()},async getCodes(){const t=await I(this.project);let e=[],s=[];if(Array.isArray(t)&&t.length>0&&(e=t.map(n=>({name:n.codeName,value:n.codeId})),this.codeId=e[0].value,Array.isArray(t[0].instrVectorDTOS)&&t[0].instrVectorDTOS.length>0)){s=t[0].instrVectorDTOS.map(a=>({name:a.valueVectorName,value:a.valueVectorId,type:a.valueVectorType}));const n=s.find(a=>a.type===2)||s[0];this.vectorId=n.value}this.codeOptions=e,this.vectorOptions=s},setEchartsData(t){const{startTime:e,endTime:s}=this.params;this.echartsData={startTime:e,endTime:s,data:t}},setHotTableParams(){this.hotTableParams={codeId:this.codeId,vectorId:this.vectorId,codeAutoList:[0,1],valueStatusList:[1,2,3],valueCheckList:[0,1,2],valueTypeList:[0,1,2],...this.params}},async getProject(){const{id:t}=this.$route.params,e=await O(t);this.project=e,await this.getCodes(),this.setHotTableParams()},orientChange(t){let e=parseInt(window.getComputedStyle(this.$refs.processLineChart.$el).width);const s=(navigator.userAgent||{}).toLowerCase();/android/.test(s)&&(e=screen.width),this.chartHeight=t.indexOf("landscape")===0?"250px":"186px",this.$refs.processLineChart.resize({width:e,height:this.chartHeight})}},mounted(){this.getProject()}},B={class:"monitor-container"},M={class:"mt-2 flex-grow-1 ofy-auto no-scrollbar"},R={class:"bg-white"},j={class:"py-4"},V={class:"headline line-height ml-2 mb-0"},N={class:"bg-white mt-2"},E={class:"py-4"},$={class:"headline line-height ml-2 mb-0"};function z(t,e,s,n,a,o){const d=r("Navbar"),p=r("date-range-dropdown-item"),m=r("filter-dropdown-item"),v=r("van-dropdown-menu"),y=r("process-line-chart"),b=r("code-value-hot-table"),x=r("phone-orient-button");return f(),g("div",B,[i(d,{back:!t.envFeishu,backEvent:o.goBack},null,8,["back","backEvent"]),i(v,{"z-index":"181","active-color":"#1890ff"},{default:k(()=>[i(p,{onLoad:o.dateRangeLoad,onConfirm:o.dateRangeConfirm},null,8,["onLoad","onConfirm"]),i(m,{options:a.codeOptions,required:!0,"default-title":"测点","default-value":a.codeId,onConfirm:o.codeConfirm},null,8,["options","default-value","onConfirm"]),i(m,{options:a.vectorOptions,required:!0,"default-title":"分量","default-value":a.vectorId,onConfirm:o.vectorConfirm},null,8,["options","default-value","onConfirm"])]),_:1}),c("div",M,[c("div",R,[c("div",j,[c("div",V,u(o.dateRangeTitle)+"过程线 ",1)]),i(y,{ref:"processLineChart",height:a.chartHeight,data:a.echartsData},null,8,["height","data"])]),c("div",N,[c("div",E,[c("p",$,u(o.dateRangeTitle)+"列表 ",1)]),i(b,{class:"px-2 pb-5","is-line":!0,height:"auto",params:a.hotTableParams,onResponse:o.setEchartsData},null,8,["params","onResponse"])])]),i(x,{ref:"orientButton",onChange:o.orientChange},null,8,["onChange"])])}const K=_(P,[["render",z],["__scopeId","data-v-c08cad7d"]]);export{K as default};
