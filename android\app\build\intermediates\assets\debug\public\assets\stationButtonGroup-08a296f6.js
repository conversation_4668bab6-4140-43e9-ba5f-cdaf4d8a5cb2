import{l as m}from"./lodash-d061b41e.js";import{_ as u}from"./index-4829f8e2.js";import{Q as l,R as r,S as c,F as p,W as f,X as h,V as g,B as _,Y as I,y as v,t as y,v as S}from"./verder-361ae6c7.js";import{c as b,d as w}from"./api-d1b3ba40.js";const E="/app/assets/no-data-39ad4f3e.jpg";const x={props:{options:{type:Array},defaultProps:{type:Object,default:()=>({name:"name",value:"value"})},defaultValue:{type:[Boolean,Number,String],default:""}},data(){return{data:this.options,value:this.defaultValue}},watch:{defaultValue(t){this.value=t}},computed:{nameField(){return this.defaultProps?this.defaultProps.name:"name"},valueField(){return this.defaultProps?this.defaultProps.value:"value"}},methods:{setOptions(t){this.data=t},setOffset(){const t=this.data.findIndex(e=>e[this.valueField]===this.value);if(t>-1){const e=document.getElementsByClassName("button-group-item")[t],a=Math.min(parseInt(window.getComputedStyle(e).width),screen.width);e.parentNode.scrollLeft=e.offsetLeft-(screen.width-a)/2}},isSelected(t){return this.value===t},click(t){this.value=t,this.$emit("change",t)}}},C={class:"button-group no-scrollbar"};function N(t,e,a,n,s,o){const d=l("van-button");return r(),c("div",C,[(r(!0),c(p,null,f(s.data,i=>(r(),h(d,{key:i[o.valueField],size:"small",round:"",type:"info",onClick:B=>o.click(i[o.valueField]),class:v(["button-group-item",{"is-checked":o.isSelected(i[o.valueField])}])},{default:g(()=>[_(I(i[o.nameField]),1)]),_:2},1032,["onClick","class"]))),128))])}const O=u(x,[["render",N],["__scopeId","data-v-816537c3"]]),k={components:{buttonGroup:O},data(){return{stationId:4e3,categoryId:10004,damId:"",stations:[]}},methods:{switchStation:m.debounce(function(t){const e=this.stations.find(s=>s.damId===t),{damId:a,name:n}=e;localStorage.setItem("monitor.station",JSON.stringify({damId:a,name:n})),this.damId=a,this.$emit("change",a)},500),async getMonitorTree(){const t=await b({stationId:this.stationId,categoryId:this.categoryId,haveCode:0,params:{codeStatus:null,codeIsImport:"",codeIsAuto:""}});this.stations=t&&Array.isArray(t.fixNavigationDTOs)?t.fixNavigationDTOs:[],this.$refs.buttonGroup.setOptions(this.stations)},initStation(){const t=localStorage.getItem("monitor.station");try{const e=JSON.parse(t),{damId:a,name:n}=e;if(a&&this.stations.find(s=>s.name===n))this.damId=a,this.$nextTick(s=>{this.$refs.buttonGroup.setOffset()}),this.$emit("change",a);else throw new Error("error station information")}catch(e){this.damId="",this.stations.length>0&&this.switchStation(this.stations[0].damId)}},async getDefaultCategoryId(){const t=JSON.parse(localStorage.app).USER_INFO.id,{data:e}=await w(this.stationId,t);!Number.isNaN(e)&&typeof e=="number"&&(this.categoryId=e)}},async mounted(){await this.getDefaultCategoryId(),await this.getMonitorTree(),this.initStation()}};function F(t,e,a,n,s,o){const d=l("button-group");return y((r(),h(d,{ref:"buttonGroup",options:s.stations,"default-props":{name:"name",value:"damId"},"default-value":s.damId,onChange:o.switchStation,class:"bg-white"},null,8,["options","default-value","onChange"])),[[S,s.stations.length>0]])}const P=u(k,[["render",F]]);export{E as _,P as s};
