System.register(["./lodash-legacy-aabdf374.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./api-legacy-08eea5a7.js"],(function(t,e){"use strict";var a,o,n,s,i,d,r,u,c,l,h,m,p,f,g,v;return{setters:[t=>{a=t.l},t=>{o=t._},t=>{n=t.Q,s=t.R,i=t.S,d=t.F,r=t.W,u=t.X,c=t.V,l=t.B,h=t.Y,m=t.y,p=t.t,f=t.v},t=>{g=t.c,v=t.d}],execute:function(){var e=document.createElement("style");e.textContent=".button-group[data-v-816537c3]{height:12.8vw;overflow-x:auto;display:flex;align-items:center;flex-shrink:0;padding:0 3.2vw}.button-group-item.van-button[data-v-816537c3]{height:7.46667vw;flex-shrink:0;padding:0 3.73333vw;font-size:3.73333vw}.button-group-item.van-button[data-v-816537c3] :before{background-color:transparent;border-color:transparent}.button-group-item.van-button[data-v-816537c3],.button-group-item.van-button[data-v-816537c3]:focus,.button-group-item.van-button[data-v-816537c3]:active{border:1px solid #eee;background-color:#eee;color:#272727}.button-group-item.van-button.is-checked[data-v-816537c3],.button-group-item.van-button.is-checked[data-v-816537c3]:focus,.button-group-item.van-button.is-checked[data-v-816537c3]:active{border:1px solid #2f80ff;background-color:rgba(47,128,255,.1);color:#2f80ff}.van-button+.van-button[data-v-816537c3]{margin-left:2.66667vw}\n",document.head.appendChild(e),t("_","/app/assets/no-data-39ad4f3e.jpg");const b={props:{options:{type:Array},defaultProps:{type:Object,default:()=>({name:"name",value:"value"})},defaultValue:{type:[Boolean,Number,String],default:""}},data(){return{data:this.options,value:this.defaultValue}},watch:{defaultValue(t){this.value=t}},computed:{nameField(){return this.defaultProps?this.defaultProps.name:"name"},valueField(){return this.defaultProps?this.defaultProps.value:"value"}},methods:{setOptions(t){this.data=t},setOffset(){const t=this.data.findIndex((t=>t[this.valueField]===this.value));if(t>-1){const e=document.getElementsByClassName("button-group-item")[t],a=Math.min(parseInt(window.getComputedStyle(e).width),screen.width);e.parentNode.scrollLeft=e.offsetLeft-(screen.width-a)/2}},isSelected(t){return this.value===t},click(t){this.value=t,this.$emit("change",t)}}},y={class:"button-group no-scrollbar"},I={components:{buttonGroup:o(b,[["render",function(t,e,a,o,p,f){const g=n("van-button");return s(),i("div",y,[(s(!0),i(d,null,r(p.data,(t=>(s(),u(g,{key:t[f.valueField],size:"small",round:"",type:"info",onClick:e=>f.click(t[f.valueField]),class:m(["button-group-item",{"is-checked":f.isSelected(t[f.valueField])}])},{default:c((()=>[l(h(t[f.nameField]),1)])),_:2},1032,["onClick","class"])))),128))])}],["__scopeId","data-v-816537c3"]])},data:()=>({stationId:4e3,categoryId:10004,damId:"",stations:[]}),methods:{switchStation:a.debounce((function(t){const e=this.stations.find((e=>e.damId===t)),{damId:a,name:o}=e;localStorage.setItem("monitor.station",JSON.stringify({damId:a,name:o})),this.damId=a,this.$emit("change",a)}),500),async getMonitorTree(){const t=await g({stationId:this.stationId,categoryId:this.categoryId,haveCode:0,params:{codeStatus:null,codeIsImport:"",codeIsAuto:""}});this.stations=t&&Array.isArray(t.fixNavigationDTOs)?t.fixNavigationDTOs:[],this.$refs.buttonGroup.setOptions(this.stations)},initStation(){const t=localStorage.getItem("monitor.station");try{const e=JSON.parse(t),{damId:a,name:o}=e;if(!a||!this.stations.find((t=>t.name===o)))throw new Error("error station information");this.damId=a,this.$nextTick((t=>{this.$refs.buttonGroup.setOffset()})),this.$emit("change",a)}catch(e){this.damId="",this.stations.length>0&&this.switchStation(this.stations[0].damId)}},async getDefaultCategoryId(){const t=JSON.parse(localStorage.app).USER_INFO.id,{data:e}=await v(this.stationId,t);Number.isNaN(e)||"number"!=typeof e||(this.categoryId=e)}},async mounted(){await this.getDefaultCategoryId(),await this.getMonitorTree(),this.initStation()}};t("s",o(I,[["render",function(t,e,a,o,i,d){const r=n("button-group");return p((s(),u(r,{ref:"buttonGroup",options:i.stations,"default-props":{name:"name",value:"damId"},"default-value":i.damId,onChange:d.switchStation,class:"bg-white"},null,8,["options","default-value","onChange"])),[[f,i.stations.length>0]])}]]))}}}));
