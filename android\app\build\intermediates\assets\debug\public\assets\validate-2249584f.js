function n(t){if(!t||!Array.isArray(t)||t.length!==2)return!1;const[r,e]=t;return/^-?((1[0-7][0-9])|([1-9]?[0-9]))\.\d{1,20}$/.test(r)&&/-?([1-8]?[1-9]|[1-9]0)\.\d{1,20}$/.test(e)}function s(t){if(typeof t=="string")try{let r=JSON.parse(t);return!!(typeof r=="object"&&r)}catch(r){return!1}return!1}function i(t){return t==null?!0:Array.isArray(t)?t.length===0:typeof t=="string"?t.trim()==="":typeof t=="object"&&t!==null?Object.keys(t).length===0:!t}function f(t){try{return new URL(t).protocol==="https:"}catch(r){return console.error("URL格式无效:",r),!1}}export{n as a,s as b,i as c,f as i};
