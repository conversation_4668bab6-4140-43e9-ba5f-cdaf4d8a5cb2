System.register([],(function(t,r){"use strict";return{execute:function(){t({a:function(t){if(!t||!Array.isArray(t)||2!==t.length)return!1;const[r,e]=t;return/^-?((1[0-7][0-9])|([1-9]?[0-9]))\.\d{1,20}$/.test(r)&&/-?([1-8]?[1-9]|[1-9]0)\.\d{1,20}$/.test(e)},b:function(t){if("string"==typeof t)try{let r=JSON.parse(t);return!("object"!=typeof r||!r)}catch(r){return!1}return!1},c:function(t){return null==t||(Array.isArray(t)?0===t.length:"string"==typeof t?""===t.trim():"object"==typeof t&&null!==t?0===Object.keys(t).length:!t)},i:function(t){try{return"https:"===new URL(t).protocol}catch(r){return console.error("URL格式无效:",r),!1}}})}}}));
