import{u as $n,i as On,g as ot,o as da,c as A,r as O,a as xe,b as Dt,d as gt,e as bt,f as we,n as le,h as Rr,w as j,p as An,j as _r,k as i,l as _,m as cn,q as ie,T as fa,s as Ft,t as $e,v as Be,F as ft,x as Vr,y as Lr,z as Mr,A as zr,C as ql,B as Gl,D as Fr,E as Nr,G as Hr,H as Wr,I as jr,J as Kl}from"./verder-361ae6c7.js";function Bn(){}const J=Object.assign,_e=typeof window<"u",Le=e=>e!==null&&typeof e=="object",se=e=>e!=null,ln=e=>typeof e=="function",vo=e=>Le(e)&&ln(e.then)&&ln(e.catch),In=e=>Object.prototype.toString.call(e)==="[object Date]"&&!Number.isNaN(e.getTime());function Zl(e){return e=e.replace(/[^-|\d]/g,""),/^((\+86)|(86))?(1)\d{10}$/.test(e)||/^0[0-9-]{10,13}$/.test(e)}const Jl=e=>typeof e=="number"||/^\d+(\.\d+)?$/.test(e),Yr=()=>_e?/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()):!1;function Lo(e,t){const n=t.split(".");let a=e;return n.forEach(o=>{var l;a=Le(a)&&(l=a[o])!=null?l:""}),a}function ue(e,t,n){return t.reduce((a,o)=>((!n||e[o]!==void 0)&&(a[o]=e[o]),a),{})}const at=(e,t)=>JSON.stringify(e)===JSON.stringify(t),aa=e=>Array.isArray(e)?e:[e],Ur=e=>e.reduce((t,n)=>t.concat(n),[]),ge=null,M=[Number,String],R={type:Boolean,default:!0},Te=e=>({type:e,required:!0}),me=()=>({type:Array,default:()=>[]}),Ce=e=>({type:Number,default:e}),U=e=>({type:M,default:e}),H=e=>({type:String,default:e});var Ve=typeof window<"u";function De(e){return Ve?requestAnimationFrame(e):-1}function va(e){Ve&&cancelAnimationFrame(e)}function Bt(e){De(()=>De(e))}var Xr=e=>e===window,Mo=(e,t)=>({top:0,left:0,right:e,bottom:t,width:e,height:t}),ce=e=>{const t=$n(e);if(Xr(t)){const n=t.innerWidth,a=t.innerHeight;return Mo(n,a)}return t!=null&&t.getBoundingClientRect?t.getBoundingClientRect():Mo(0,0)};function qr(e=!1){const t=O(e);return[t,(a=!t.value)=>{t.value=a}]}function Ie(e){const t=On(e,null);if(t){const n=ot(),{link:a,unlink:o,internalChildren:l}=t;a(n),da(()=>o(n));const c=A(()=>l.indexOf(n));return{parent:t,index:c}}return{parent:null,index:O(-1)}}function Gr(e){const t=[],n=a=>{Array.isArray(a)&&a.forEach(o=>{var l;_r(o)&&(t.push(o),(l=o.component)!=null&&l.subTree&&(t.push(o.component.subTree),n(o.component.subTree.children)),o.children&&n(o.children))})};return n(e),t}var zo=(e,t)=>{const n=e.indexOf(t);return n===-1?e.findIndex(a=>t.key!==void 0&&t.key!==null&&a.type===t.type&&a.key===t.key):n};function Kr(e,t,n){const a=Gr(e.subTree.children);n.sort((l,c)=>zo(a,l.vnode)-zo(a,c.vnode));const o=n.map(l=>l.proxy);t.sort((l,c)=>{const r=o.indexOf(l),u=o.indexOf(c);return r-u})}function Oe(e){const t=xe([]),n=xe([]),a=ot();return{children:t,linkChildren:l=>{An(e,Object.assign({link:u=>{u.proxy&&(n.push(u),t.push(u.proxy),Kr(a,t,n))},unlink:u=>{const s=n.indexOf(u);t.splice(s,1),n.splice(s,1)},children:t,internalChildren:n},l))}}}var Ka=1e3,Za=60*Ka,Ja=60*Za,Fo=24*Ja;function Zr(e){const t=Math.floor(e/Fo),n=Math.floor(e%Fo/Ja),a=Math.floor(e%Ja/Za),o=Math.floor(e%Za/Ka),l=Math.floor(e%Ka);return{total:e,days:t,hours:n,minutes:a,seconds:o,milliseconds:l}}function Jr(e,t){return Math.floor(e/1e3)===Math.floor(t/1e3)}function Qr(e){let t,n,a,o;const l=O(e.time),c=A(()=>Zr(l.value)),r=()=>{a=!1,va(t)},u=()=>Math.max(n-Date.now(),0),s=S=>{var g,w;l.value=S,(g=e.onChange)==null||g.call(e,c.value),S===0&&(r(),(w=e.onFinish)==null||w.call(e))},d=()=>{t=De(()=>{a&&(s(u()),l.value>0&&d())})},f=()=>{t=De(()=>{if(a){const S=u();(!Jr(S,l.value)||S===0)&&s(S),l.value>0&&f()}})},v=()=>{Ve&&(e.millisecond?d():f())},h=()=>{a||(n=Date.now()+l.value,a=!0,v())},y=(S=e.time)=>{r(),l.value=S};return Dt(r),gt(()=>{o&&(a=!0,o=!1,v())}),bt(()=>{a&&(r(),o=!0)}),{start:h,pause:r,reset:y,current:c}}function un(e){let t;we(()=>{e(),le(()=>{t=!0})}),gt(()=>{t&&e()})}function ye(e,t,n={}){if(!Ve)return;const{target:a=window,passive:o=!1,capture:l=!1}=n;let c=!1,r;const u=f=>{if(c)return;const v=$n(f);v&&!r&&(v.addEventListener(e,t,{capture:l,passive:o}),r=!0)},s=f=>{if(c)return;const v=$n(f);v&&r&&(v.removeEventListener(e,t,l),r=!1)};da(()=>s(a)),bt(()=>s(a)),un(()=>u(a));let d;return Rr(a)&&(d=j(a,(f,v)=>{s(v),u(f)})),()=>{d==null||d(),s(a),c=!0}}function ha(e,t,n={}){if(!Ve)return;const{eventName:a="click"}=n;ye(a,l=>{(Array.isArray(e)?e:[e]).every(u=>{const s=$n(u);return s&&!s.contains(l.target)})&&t(l)},{target:document})}var _n,Pa;function es(){if(!_n&&(_n=O(0),Pa=O(0),Ve)){const e=()=>{_n.value=window.innerWidth,Pa.value=window.innerHeight};e(),window.addEventListener("resize",e,{passive:!0}),window.addEventListener("orientationchange",e,{passive:!0})}return{width:_n,height:Pa}}var ts=/scroll|auto|overlay/i,Ql=Ve?window:void 0;function ns(e){return e.tagName!=="HTML"&&e.tagName!=="BODY"&&e.nodeType===1}function ma(e,t=Ql){let n=e;for(;n&&n!==t&&ns(n);){const{overflowY:a}=window.getComputedStyle(n);if(ts.test(a))return n;n=n.parentNode}return t}function dn(e,t=Ql){const n=O();return we(()=>{e.value&&(n.value=ma(e.value,t))}),n}var Vn;function as(){if(!Vn&&(Vn=O("visible"),Ve)){const e=()=>{Vn.value=document.hidden?"hidden":"visible"};e(),window.addEventListener("visibilitychange",e)}return Vn}var ei=Symbol("van-field");function Et(e){const t=On(ei,null);t&&!t.customValue.value&&(t.customValue.value=e,j(e,()=>{t.resetValidation(),t.validateWithTrigger("onChange")}))}function vt(e){const t="scrollTop"in e?e.scrollTop:e.pageYOffset;return Math.max(t,0)}function oa(e,t){"scrollTop"in e?e.scrollTop=t:e.scrollTo(e.scrollX,t)}function Lt(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0}function Dn(e){oa(window,e),oa(document.body,e)}function No(e,t){if(e===window)return 0;const n=t?vt(t):Lt();return ce(e).top+n}const os=Yr();function ti(){os&&Dn(Lt())}const ho=e=>e.stopPropagation();function ve(e,t){(typeof e.cancelable!="boolean"||e.cancelable)&&e.preventDefault(),t&&ho(e)}function zt(e){const t=$n(e);if(!t)return!1;const n=window.getComputedStyle(t),a=n.display==="none",o=t.offsetParent===null&&n.position!=="fixed";return a||o}const{width:qe,height:Ne}=es();function ls(e){const t=window.getComputedStyle(e);return t.transform!=="none"||t.perspective!=="none"||["transform","perspective","filter"].some(n=>(t.willChange||"").includes(n))}function is(e){let t=e.parentElement;for(;t;){if(t&&t.tagName!=="HTML"&&t.tagName!=="BODY"&&ls(t))return t;t=t.parentElement}return null}function ne(e){if(se(e))return Jl(e)?"".concat(e,"px"):String(e)}function yt(e){if(se(e)){if(Array.isArray(e))return{width:ne(e[0]),height:ne(e[1])};const t=ne(e);return{width:t,height:t}}}function wt(e){const t={};return e!==void 0&&(t.zIndex=+e),t}let Oa;function rs(){if(!Oa){const e=document.documentElement,t=e.style.fontSize||window.getComputedStyle(e).fontSize;Oa=parseFloat(t)}return Oa}function ss(e){return e=e.replace(/rem/g,""),+e*rs()}function cs(e){return e=e.replace(/vw/g,""),+e*qe.value/100}function us(e){return e=e.replace(/vh/g,""),+e*Ne.value/100}function mo(e){if(typeof e=="number")return e;if(_e){if(e.includes("rem"))return ss(e);if(e.includes("vw"))return cs(e);if(e.includes("vh"))return us(e)}return parseFloat(e)}const ds=/-(\w)/g,ni=e=>e.replace(ds,(t,n)=>n.toUpperCase()),fs=e=>e.replace(/([A-Z])/g,"-$1").toLowerCase().replace(/^-/,"");function Xe(e,t=2){let n=e+"";for(;n.length<t;)n="0"+n;return n}const ke=(e,t,n)=>Math.min(Math.max(e,t),n);function Ho(e,t,n){const a=e.indexOf(t);return a===-1?e:t==="-"&&a!==0?e.slice(0,a):e.slice(0,a+1)+e.slice(a).replace(n,"")}function Qa(e,t=!0,n=!0){t?e=Ho(e,".",/\./g):e=e.split(".")[0],n?e=Ho(e,"-",/-/g):e=e.replace(/-/,"");const a=t?/[^-0-9.]/g:/[^-0-9]/g;return e.replace(a,"")}function ai(e,t){return Math.round((e+t)*1e10)/1e10}const{hasOwnProperty:vs}=Object.prototype;function hs(e,t,n){const a=t[n];se(a)&&(!vs.call(e,n)||!Le(a)?e[n]=a:e[n]=oi(Object(e[n]),a))}function oi(e,t){return Object.keys(t).forEach(n=>{hs(e,t,n)}),e}var ms={name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(e,t)=>"".concat(e,"年").concat(t,"月"),rangePrompt:e=>"最多选择 ".concat(e," 天")},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:e=>"".concat(e,"折"),condition:e=>"满".concat(e,"元可用")},vanCouponCell:{title:"优惠券",count:e=>"".concat(e,"张可用")},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}};const Wo=O("zh-CN"),jo=xe({"zh-CN":ms}),li={messages(){return jo[Wo.value]},use(e,t){Wo.value=e,this.add({[e]:t})},add(e={}){oi(jo,e)}};var gs=li;function bs(e){const t=ni(e)+".";return(n,...a)=>{const o=gs.messages(),l=Lo(o,t+n)||Lo(o,n);return ln(l)?l(...a):l}}function eo(e,t){return t?typeof t=="string"?" ".concat(e,"--").concat(t):Array.isArray(t)?t.reduce((n,a)=>n+eo(e,a),""):Object.keys(t).reduce((n,a)=>n+(t[a]?eo(e,a):""),""):""}function ys(e){return(t,n)=>(t&&typeof t!="string"&&(n=t,t=""),t=t?"".concat(e,"__").concat(t):e,"".concat(t).concat(eo(t,n)))}function V(e){const t="van-".concat(e);return[t,ys(t),bs(t)]}const xt="van-hairline",ii="".concat(xt,"--top"),ri="".concat(xt,"--left"),ws="".concat(xt,"--right"),go="".concat(xt,"--bottom"),Cn="".concat(xt,"--surround"),ga="".concat(xt,"--top-bottom"),xs="".concat(xt,"-unset--top-bottom"),Ee="van-haptics-feedback",si=Symbol("van-form"),ci=500,Yo=5;function Pt(e,{args:t=[],done:n,canceled:a,error:o}){if(e){const l=e.apply(null,t);vo(l)?l.then(c=>{c?n():a&&a()}).catch(o||Bn):l?n():a&&a()}else n()}function N(e){return e.install=t=>{const{name:n}=e;n&&(t.component(n,e),t.component(ni("-".concat(n)),e))},e}function la(e,t){return e.reduce((n,a)=>Math.abs(n-t)<Math.abs(a-t)?n:a)}const ui=Symbol();function ba(e){const t=On(ui,null);t&&j(t,n=>{n&&e()})}const di=(e,t)=>{const n=O(),a=()=>{n.value=ce(e).height};return we(()=>{if(le(a),t)for(let o=1;o<=3;o++)setTimeout(a,100*o)}),ba(()=>le(a)),j([qe,Ne],a),n};function ya(e,t){const n=di(e,!0);return a=>i("div",{class:t("placeholder"),style:{height:n.value?"".concat(n.value,"px"):void 0}},[a()])}const[fi,Uo]=V("action-bar"),bo=Symbol(fi),Ss={placeholder:Boolean,safeAreaInsetBottom:R};var Cs=_({name:fi,props:Ss,setup(e,{slots:t}){const n=O(),a=ya(n,Uo),{linkChildren:o}=Oe(bo);o();const l=()=>{var c;return i("div",{ref:n,class:[Uo(),{"van-safe-area-bottom":e.safeAreaInsetBottom}]},[(c=t.default)==null?void 0:c.call(t)])};return()=>e.placeholder?a(l):l()}});const vi=N(Cs);function re(e){const t=ot();t&&J(t.proxy,e)}const Ot={to:[String,Object],url:String,replace:Boolean};function hi({to:e,url:t,replace:n,$router:a}){e&&a?a[n?"replace":"push"](e):t&&(n?location.replace(t):location.href=t)}function Nt(){const e=ot().proxy;return()=>hi(e)}const[ks,Xo]=V("badge"),Ts={dot:Boolean,max:M,tag:H("div"),color:String,offset:Array,content:M,showZero:R,position:H("top-right")};var $s=_({name:ks,props:Ts,setup(e,{slots:t}){const n=()=>{if(t.content)return!0;const{content:r,showZero:u}=e;return se(r)&&r!==""&&(u||r!==0&&r!=="0")},a=()=>{const{dot:r,max:u,content:s}=e;if(!r&&n())return t.content?t.content():se(u)&&Jl(s)&&+s>+u?"".concat(u,"+"):s},o=r=>r.startsWith("-")?r.replace("-",""):"-".concat(r),l=A(()=>{const r={background:e.color};if(e.offset){const[u,s]=e.offset,{position:d}=e,[f,v]=d.split("-");t.default?(typeof s=="number"?r[f]=ne(f==="top"?s:-s):r[f]=f==="top"?ne(s):o(s),typeof u=="number"?r[v]=ne(v==="left"?u:-u):r[v]=v==="left"?ne(u):o(u)):(r.marginTop=ne(s),r.marginLeft=ne(u))}return r}),c=()=>{if(n()||e.dot)return i("div",{class:Xo([e.position,{dot:e.dot,fixed:!!t.default}]),style:l.value},[a()])};return()=>{if(t.default){const{tag:r}=e;return i(r,{class:Xo("wrapper")},{default:()=>[t.default(),c()]})}return c()}}});const Ht=N($s);let mi=2e3;const Bs=()=>++mi,Is=e=>{mi=e},[gi,Ds]=V("config-provider"),bi=Symbol(gi),Es={tag:H("div"),theme:H("light"),zIndex:Number,themeVars:Object,themeVarsDark:Object,themeVarsLight:Object,themeVarsScope:H("local"),iconPrefix:String};function Ps(e){return e.replace(/([a-zA-Z])(\d)/g,"$1-$2")}function Os(e){const t={};return Object.keys(e).forEach(n=>{const a=Ps(fs(n));t["--van-".concat(a)]=e[n]}),t}function Ln(e={},t={}){Object.keys(e).forEach(n=>{e[n]!==t[n]&&document.documentElement.style.setProperty(n,e[n])}),Object.keys(t).forEach(n=>{e[n]||document.documentElement.style.removeProperty(n)})}var As=_({name:gi,props:Es,setup(e,{slots:t}){const n=A(()=>Os(J({},e.themeVars,e.theme==="dark"?e.themeVarsDark:e.themeVarsLight)));if(_e){const a=()=>{document.documentElement.classList.add("van-theme-".concat(e.theme))},o=(l=e.theme)=>{document.documentElement.classList.remove("van-theme-".concat(l))};j(()=>e.theme,(l,c)=>{c&&o(c),a()},{immediate:!0}),gt(a),bt(o),Dt(o),j(n,(l,c)=>{e.themeVarsScope==="global"&&Ln(l,c)}),j(()=>e.themeVarsScope,(l,c)=>{c==="global"&&Ln({},n.value),l==="global"&&Ln(n.value,{})}),e.themeVarsScope==="global"&&Ln(n.value,{})}return An(bi,e),cn(()=>{e.zIndex!==void 0&&Is(e.zIndex)}),()=>i(e.tag,{class:Ds(),style:e.themeVarsScope==="local"?n.value:void 0},{default:()=>{var a;return[(a=t.default)==null?void 0:a.call(t)]}})}});const[ps,qo]=V("icon"),Rs=e=>e==null?void 0:e.includes("/"),_s={dot:Boolean,tag:H("i"),name:String,size:M,badge:M,color:String,badgeProps:Object,classPrefix:String};var Vs=_({name:ps,props:_s,setup(e,{slots:t}){const n=On(bi,null),a=A(()=>e.classPrefix||(n==null?void 0:n.iconPrefix)||qo());return()=>{const{tag:o,dot:l,name:c,size:r,badge:u,color:s}=e,d=Rs(c);return i(Ht,ie({dot:l,tag:o,class:[a.value,d?"":"".concat(a.value,"-").concat(c)],style:{color:s,fontSize:ne(r)},content:u},e.badgeProps),{default:()=>{var f;return[(f=t.default)==null?void 0:f.call(t),d&&i("img",{class:qo("image"),src:c},null)]}})}}});const ae=N(Vs);var Ls=ae;const[Ms,kn]=V("loading"),zs=Array(12).fill(null).map((e,t)=>i("i",{class:kn("line",String(t+1))},null)),Fs=i("svg",{class:kn("circular"),viewBox:"25 25 50 50"},[i("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]),Ns={size:M,type:H("circular"),color:String,vertical:Boolean,textSize:M,textColor:String};var Hs=_({name:Ms,props:Ns,setup(e,{slots:t}){const n=A(()=>J({color:e.color},yt(e.size))),a=()=>{const l=e.type==="spinner"?zs:Fs;return i("span",{class:kn("spinner",e.type),style:n.value},[t.icon?t.icon():l])},o=()=>{var l;if(t.default)return i("span",{class:kn("text"),style:{fontSize:ne(e.textSize),color:(l=e.textColor)!=null?l:e.color}},[t.default()])};return()=>{const{type:l,vertical:c}=e;return i("div",{class:kn([l,{vertical:c}]),"aria-live":"polite","aria-busy":!0},[a(),o()])}}});const Ke=N(Hs),[Ws,Wt]=V("button"),js=J({},Ot,{tag:H("button"),text:String,icon:String,type:H("default"),size:H("normal"),color:String,block:Boolean,plain:Boolean,round:Boolean,square:Boolean,loading:Boolean,hairline:Boolean,disabled:Boolean,iconPrefix:String,nativeType:H("button"),loadingSize:M,loadingText:String,loadingType:String,iconPosition:H("left")});var Ys=_({name:Ws,props:js,emits:["click"],setup(e,{emit:t,slots:n}){const a=Nt(),o=()=>n.loading?n.loading():i(Ke,{size:e.loadingSize,type:e.loadingType,class:Wt("loading")},null),l=()=>{if(e.loading)return o();if(n.icon)return i("div",{class:Wt("icon")},[n.icon()]);if(e.icon)return i(ae,{name:e.icon,class:Wt("icon"),classPrefix:e.iconPrefix},null)},c=()=>{let s;if(e.loading?s=e.loadingText:s=n.default?n.default():e.text,s)return i("span",{class:Wt("text")},[s])},r=()=>{const{color:s,plain:d}=e;if(s){const f={color:d?s:"white"};return d||(f.background=s),s.includes("gradient")?f.border=0:f.borderColor=s,f}},u=s=>{e.loading?ve(s):e.disabled||(t("click",s),a())};return()=>{const{tag:s,type:d,size:f,block:v,round:h,plain:y,square:S,loading:g,disabled:w,hairline:b,nativeType:C,iconPosition:T}=e,m=[Wt([d,f,{plain:y,block:v,round:h,square:S,loading:g,disabled:w,hairline:b}]),{[Cn]:b}];return i(s,{type:C,class:m,style:r(),disabled:w,onClick:u},{default:()=>[i("div",{class:Wt("content")},[T==="left"&&l(),c(),T==="right"&&l()])]})}}});const Pe=N(Ys),[Us,Xs]=V("action-bar-button"),qs=J({},Ot,{type:String,text:String,icon:String,color:String,loading:Boolean,disabled:Boolean});var Gs=_({name:Us,props:qs,setup(e,{slots:t}){const n=Nt(),{parent:a,index:o}=Ie(bo),l=A(()=>{if(a){const r=a.children[o.value-1];return!(r&&"isButton"in r)}}),c=A(()=>{if(a){const r=a.children[o.value+1];return!(r&&"isButton"in r)}});return re({isButton:!0}),()=>{const{type:r,icon:u,text:s,color:d,loading:f,disabled:v}=e;return i(Pe,{class:Xs([r,{last:c.value,first:l.value}]),size:"large",type:r,icon:u,color:d,loading:f,disabled:v,onClick:n},{default:()=>[t.default?t.default():s]})}}});const to=N(Gs),[Ks,Aa]=V("action-bar-icon"),Zs=J({},Ot,{dot:Boolean,text:String,icon:String,color:String,badge:M,iconClass:ge,badgeProps:Object,iconPrefix:String});var Js=_({name:Ks,props:Zs,setup(e,{slots:t}){const n=Nt();Ie(bo);const a=()=>{const{dot:o,badge:l,icon:c,color:r,iconClass:u,badgeProps:s,iconPrefix:d}=e;return t.icon?i(Ht,ie({dot:o,class:Aa("icon"),content:l},s),{default:t.icon}):i(ae,{tag:"div",dot:o,name:c,badge:l,color:r,class:[Aa("icon"),u],badgeProps:s,classPrefix:d},null)};return()=>i("div",{role:"button",class:Aa(),tabindex:0,onClick:n},[a(),t.default?t.default():e.text])}});const Qs=N(Js),fn={show:Boolean,zIndex:M,overlay:R,duration:M,teleport:[String,Object],lockScroll:R,lazyRender:R,beforeClose:Function,overlayStyle:Object,overlayClass:ge,transitionAppear:Boolean,closeOnClickOverlay:R},yo=Object.keys(fn);function ec(e,t){return e>t?"horizontal":t>e?"vertical":""}function He(){const e=O(0),t=O(0),n=O(0),a=O(0),o=O(0),l=O(0),c=O(""),r=O(!0),u=()=>c.value==="vertical",s=()=>c.value==="horizontal",d=()=>{n.value=0,a.value=0,o.value=0,l.value=0,c.value="",r.value=!0};return{move:h=>{const y=h.touches[0];n.value=(y.clientX<0?0:y.clientX)-e.value,a.value=y.clientY-t.value,o.value=Math.abs(n.value),l.value=Math.abs(a.value);const S=10;(!c.value||o.value<S&&l.value<S)&&(c.value=ec(o.value,l.value)),r.value&&(o.value>Yo||l.value>Yo)&&(r.value=!1)},start:h=>{d(),e.value=h.touches[0].clientX,t.value=h.touches[0].clientY},reset:d,startX:e,startY:t,deltaX:n,deltaY:a,offsetX:o,offsetY:l,direction:c,isVertical:u,isHorizontal:s,isTap:r}}let hn=0;const Go="van-overflow-hidden";function yi(e,t){const n=He(),a="01",o="10",l=d=>{n.move(d);const f=n.deltaY.value>0?o:a,v=ma(d.target,e.value),{scrollHeight:h,offsetHeight:y,scrollTop:S}=v;let g="11";S===0?g=y>=h?"00":"01":S+y>=h&&(g="10"),g!=="11"&&n.isVertical()&&!(parseInt(g,2)&parseInt(f,2))&&ve(d,!0)},c=()=>{document.addEventListener("touchstart",n.start),document.addEventListener("touchmove",l,{passive:!1}),hn||document.body.classList.add(Go),hn++},r=()=>{hn&&(document.removeEventListener("touchstart",n.start),document.removeEventListener("touchmove",l),hn--,hn||document.body.classList.remove(Go))},u=()=>t()&&c(),s=()=>t()&&r();un(u),bt(s),Dt(s),j(t,d=>{d?c():r()})}function wo(e){const t=O(!1);return j(e,n=>{n&&(t.value=n)},{immediate:!0}),n=>()=>t.value?n():null}const ia=()=>{var e;const{scopeId:t}=((e=ot())==null?void 0:e.vnode)||{};return t?{[t]:""}:null},[tc,nc]=V("overlay"),ac={show:Boolean,zIndex:M,duration:M,className:ge,lockScroll:R,lazyRender:R,customStyle:Object,teleport:[String,Object]};var oc=_({name:tc,inheritAttrs:!1,props:ac,setup(e,{attrs:t,slots:n}){const a=O(),o=wo(()=>e.show||!e.lazyRender),l=r=>{e.lockScroll&&ve(r,!0)},c=o(()=>{var r;const u=J(wt(e.zIndex),e.customStyle);return se(e.duration)&&(u.animationDuration="".concat(e.duration,"s")),$e(i("div",ie({ref:a,style:u,class:[nc(),e.className]},t),[(r=n.default)==null?void 0:r.call(n)]),[[Be,e.show]])});return ye("touchmove",l,{target:a}),()=>{const r=i(fa,{name:"van-fade",appear:!0},{default:c});return e.teleport?i(Ft,{to:e.teleport},{default:()=>[r]}):r}}});const wi=N(oc),lc=J({},fn,{round:Boolean,position:H("center"),closeIcon:H("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:H("top-right"),destroyOnClose:Boolean,safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[ic,Ko]=V("popup");var rc=_({name:ic,inheritAttrs:!1,props:lc,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(e,{emit:t,attrs:n,slots:a}){let o,l;const c=O(),r=O(),u=wo(()=>e.show||!e.lazyRender),s=A(()=>{const $={zIndex:c.value};if(se(e.duration)){const I=e.position==="center"?"animationDuration":"transitionDuration";$[I]="".concat(e.duration,"s")}return $}),d=()=>{o||(o=!0,c.value=e.zIndex!==void 0?+e.zIndex:Bs(),t("open"))},f=()=>{o&&Pt(e.beforeClose,{done(){o=!1,t("close"),t("update:show",!1)}})},v=$=>{t("clickOverlay",$),e.closeOnClickOverlay&&f()},h=()=>{if(e.overlay)return i(wi,ie({show:e.show,class:e.overlayClass,zIndex:c.value,duration:e.duration,customStyle:e.overlayStyle,role:e.closeOnClickOverlay?"button":void 0,tabindex:e.closeOnClickOverlay?0:void 0},ia(),{onClick:v}),{default:a["overlay-content"]})},y=$=>{t("clickCloseIcon",$),f()},S=()=>{if(e.closeable)return i(ae,{role:"button",tabindex:0,name:e.closeIcon,class:[Ko("close-icon",e.closeIconPosition),Ee],classPrefix:e.iconPrefix,onClick:y},null)};let g;const w=()=>{g&&clearTimeout(g),g=setTimeout(()=>{t("opened")})},b=()=>t("closed"),C=$=>t("keydown",$),T=u(()=>{var $;const{destroyOnClose:I,round:x,position:E,safeAreaInsetTop:D,safeAreaInsetBottom:k,show:B}=e;if(!(!B&&I))return $e(i("div",ie({ref:r,style:s.value,role:"dialog",tabindex:0,class:[Ko({round:x,[E]:E}),{"van-safe-area-top":D,"van-safe-area-bottom":k}],onKeydown:C},n,ia()),[($=a.default)==null?void 0:$.call(a),S()]),[[Be,B]])}),m=()=>{const{position:$,transition:I,transitionAppear:x}=e,E=$==="center"?"van-fade":"van-popup-slide-".concat($);return i(fa,{name:I||E,appear:x,onAfterEnter:w,onAfterLeave:b},{default:T})};return j(()=>e.show,$=>{$&&!o&&(d(),n.tabindex===0&&le(()=>{var I;(I=r.value)==null||I.focus()})),!$&&o&&(o=!1,t("close"))}),re({popupRef:r}),yi(r,()=>e.show&&e.lockScroll),ye("popstate",()=>{e.closeOnPopstate&&(f(),l=!1)}),we(()=>{e.show&&d()}),gt(()=>{l&&(t("update:show",!0),l=!1)}),bt(()=>{e.show&&e.teleport&&(f(),l=!0)}),An(ui,()=>e.show),()=>e.teleport?i(Ft,{to:e.teleport},{default:()=>[h(),m()]}):i(ft,null,[h(),m()])}});const Ze=N(rc),[sc,ze]=V("action-sheet"),cc=J({},fn,{title:String,round:R,actions:me(),closeIcon:H("cross"),closeable:R,cancelText:String,description:String,closeOnPopstate:R,closeOnClickAction:Boolean,safeAreaInsetBottom:R}),uc=[...yo,"round","closeOnPopstate","safeAreaInsetBottom"];var dc=_({name:sc,props:cc,emits:["select","cancel","update:show"],setup(e,{slots:t,emit:n}){const a=f=>n("update:show",f),o=()=>{a(!1),n("cancel")},l=()=>{if(e.title)return i("div",{class:ze("header")},[e.title,e.closeable&&i(ae,{name:e.closeIcon,class:[ze("close"),Ee],onClick:o},null)])},c=()=>{if(t.cancel||e.cancelText)return[i("div",{class:ze("gap")},null),i("button",{type:"button",class:ze("cancel"),onClick:o},[t.cancel?t.cancel():e.cancelText])]},r=f=>{if(f.icon)return i(ae,{class:ze("item-icon"),name:f.icon},null)},u=(f,v)=>f.loading?i(Ke,{class:ze("loading-icon")},null):t.action?t.action({action:f,index:v}):[i("span",{class:ze("name")},[f.name]),f.subname&&i("div",{class:ze("subname")},[f.subname])],s=(f,v)=>{const{color:h,loading:y,callback:S,disabled:g,className:w}=f,b=()=>{g||y||(S&&S(f),e.closeOnClickAction&&a(!1),le(()=>n("select",f,v)))};return i("button",{type:"button",style:{color:h},class:[ze("item",{loading:y,disabled:g}),w],onClick:b},[r(f),u(f,v)])},d=()=>{if(e.description||t.description){const f=t.description?t.description():e.description;return i("div",{class:ze("description")},[f])}};return()=>i(Ze,ie({class:ze(),position:"bottom","onUpdate:show":a},ue(e,uc)),{default:()=>{var f;return[l(),d(),i("div",{class:ze("content")},[e.actions.map(s),(f=t.default)==null?void 0:f.call(t)]),c()]}})}});const fc=N(dc),[vc,ct,Zo]=V("picker"),xi=e=>e.find(t=>!t.disabled)||e[0];function hc(e,t){const n=e[0];if(n){if(Array.isArray(n))return"multiple";if(t.children in n)return"cascade"}return"default"}function ea(e,t){t=ke(t,0,e.length);for(let n=t;n<e.length;n++)if(!e[n].disabled)return n;for(let n=t-1;n>=0;n--)if(!e[n].disabled)return n;return 0}const Jo=(e,t,n)=>t!==void 0&&e.some(a=>a[n.value]===t);function no(e,t,n){const a=e.findIndex(l=>l[n.value]===t),o=ea(e,a);return e[o]}function mc(e,t,n){const a=[];let o={[t.children]:e},l=0;for(;o&&o[t.children];){const c=o[t.children],r=n.value[l];if(o=se(r)?no(c,r,t):void 0,!o&&c.length){const u=xi(c)[t.value];o=no(c,u,t)}l++,a.push(c)}return a}function gc(e){const{transform:t}=window.getComputedStyle(e),n=t.slice(7,t.length-1).split(", ")[5];return Number(n)}function bc(e){return J({text:"text",value:"value",children:"children"},e)}const Qo=200,el=300,yc=15,[Si,pa]=V("picker-column"),Ci=Symbol(Si);var wc=_({name:Si,props:{value:M,fields:Te(Object),options:me(),readonly:Boolean,allowHtml:Boolean,optionHeight:Te(Number),swipeDuration:Te(M),visibleOptionNum:Te(M)},emits:["change","clickOption","scrollInto"],setup(e,{emit:t,slots:n}){let a,o,l,c,r;const u=O(),s=O(),d=O(0),f=O(0),v=He(),h=()=>e.options.length,y=()=>e.optionHeight*(+e.visibleOptionNum-1)/2,S=D=>{let k=ea(e.options,D);const B=-k*e.optionHeight,p=()=>{k>h()-1&&(k=ea(e.options,D));const Y=e.options[k][e.fields.value];Y!==e.value&&t("change",Y)};a&&B!==d.value?r=p:p(),d.value=B},g=()=>e.readonly||!e.options.length,w=D=>{a||g()||(r=null,f.value=Qo,S(D),t("clickOption",e.options[D]))},b=D=>ke(Math.round(-D/e.optionHeight),0,h()-1),C=A(()=>b(d.value)),T=(D,k)=>{const B=Math.abs(D/k);D=d.value+B/.003*(D<0?-1:1);const p=b(D);f.value=+e.swipeDuration,S(p)},m=()=>{a=!1,f.value=0,r&&(r(),r=null)},$=D=>{if(!g()){if(v.start(D),a){const k=gc(s.value);d.value=Math.min(0,k-y())}f.value=0,o=d.value,l=Date.now(),c=o,r=null}},I=D=>{if(g())return;v.move(D),v.isVertical()&&(a=!0,ve(D,!0));const k=ke(o+v.deltaY.value,-(h()*e.optionHeight),e.optionHeight),B=b(k);B!==C.value&&t("scrollInto",e.options[B]),d.value=k;const p=Date.now();p-l>el&&(l=p,c=k)},x=()=>{if(g())return;const D=d.value-c,k=Date.now()-l;if(k<el&&Math.abs(D)>yc){T(D,k);return}const p=b(d.value);f.value=Qo,S(p),setTimeout(()=>{a=!1},0)},E=()=>{const D={height:"".concat(e.optionHeight,"px")};return e.options.map((k,B)=>{const p=k[e.fields.text],{disabled:Y}=k,q=k[e.fields.value],F={role:"button",style:D,tabindex:Y?-1:0,class:[pa("item",{disabled:Y,selected:q===e.value}),k.className],onClick:()=>w(B)},Q={class:"van-ellipsis",[e.allowHtml?"innerHTML":"textContent"]:p};return i("li",F,[n.option?n.option(k,B):i("div",Q,null)])})};return Ie(Ci),re({stopMomentum:m}),cn(()=>{const D=a?Math.floor(-d.value/e.optionHeight):e.options.findIndex(p=>p[e.fields.value]===e.value),k=ea(e.options,D),B=-k*e.optionHeight;a&&k<D&&m(),d.value=B}),ye("touchmove",I,{target:u}),()=>i("div",{ref:u,class:pa(),onTouchstartPassive:$,onTouchend:x,onTouchcancel:x},[i("ul",{ref:s,style:{transform:"translate3d(0, ".concat(d.value+y(),"px, 0)"),transitionDuration:"".concat(f.value,"ms"),transitionProperty:f.value?"all":"none"},class:pa("wrapper"),onTransitionend:m},[E()])])}});const[xc]=V("picker-toolbar"),wa={title:String,cancelButtonText:String,confirmButtonText:String},ki=["cancel","confirm","title","toolbar"],Sc=Object.keys(wa);var Ti=_({name:xc,props:wa,emits:["confirm","cancel"],setup(e,{emit:t,slots:n}){const a=()=>{if(n.title)return n.title();if(e.title)return i("div",{class:[ct("title"),"van-ellipsis"]},[e.title])},o=()=>t("cancel"),l=()=>t("confirm"),c=()=>{var u;const s=(u=e.cancelButtonText)!=null?u:Zo("cancel");if(!(!n.cancel&&!s))return i("button",{type:"button",class:[ct("cancel"),Ee],onClick:o},[n.cancel?n.cancel():s])},r=()=>{var u;const s=(u=e.confirmButtonText)!=null?u:Zo("confirm");if(!(!n.confirm&&!s))return i("button",{type:"button",class:[ct("confirm"),Ee],onClick:l},[n.confirm?n.confirm():s])};return()=>i("div",{class:ct("toolbar")},[n.toolbar?n.toolbar():[c(),a(),r()]])}});const xo=(e,t)=>{const n=O(e());return j(e,a=>{a!==n.value&&(n.value=a)}),j(n,a=>{a!==e()&&t(a)}),n};function Cc(e,t,n){let a,o=0;const l=e.scrollLeft,c=n===0?1:Math.round(n*1e3/16);let r=l;function u(){va(a)}function s(){r+=(t-l)/c,e.scrollLeft=r,++o<c&&(a=De(s))}return s(),u}function kc(e,t,n,a){let o,l=vt(e);const c=l<t,r=n===0?1:Math.round(n*1e3/16),u=(t-l)/r;function s(){va(o)}function d(){l+=u,(c&&l>t||!c&&l<t)&&(l=t),oa(e,l),c&&l<t||!c&&l>t?o=De(d):a&&(o=De(a))}return d(),s}let Tc=0;function vn(){const e=ot(),{name:t="unknown"}=(e==null?void 0:e.type)||{};return"".concat(t,"-").concat(++Tc)}function pn(){const e=O([]),t=[];return Vr(()=>{e.value=[]}),[e,a=>(t[a]||(t[a]=o=>{e.value[a]=o}),t[a])]}function $i(e,t){if(!_e||!window.IntersectionObserver)return;const n=new IntersectionObserver(l=>{t(l[0].intersectionRatio>0)},{root:document.body}),a=()=>{e.value&&n.observe(e.value)},o=()=>{e.value&&n.unobserve(e.value)};bt(o),Dt(o),un(a)}const[$c,Bc]=V("sticky"),Ic={zIndex:M,position:H("top"),container:Object,offsetTop:U(0),offsetBottom:U(0)};var Dc=_({name:$c,props:Ic,emits:["scroll","change"],setup(e,{emit:t,slots:n}){const a=O(),o=dn(a),l=xe({fixed:!1,width:0,height:0,transform:0}),c=O(!1),r=A(()=>mo(e.position==="top"?e.offsetTop:e.offsetBottom)),u=A(()=>{if(c.value)return;const{fixed:v,height:h,width:y}=l;if(v)return{width:"".concat(y,"px"),height:"".concat(h,"px")}}),s=A(()=>{if(!l.fixed||c.value)return;const v=J(wt(e.zIndex),{width:"".concat(l.width,"px"),height:"".concat(l.height,"px"),[e.position]:"".concat(r.value,"px")});return l.transform&&(v.transform="translate3d(0, ".concat(l.transform,"px, 0)")),v}),d=v=>t("scroll",{scrollTop:v,isFixed:l.fixed}),f=()=>{if(!a.value||zt(a))return;const{container:v,position:h}=e,y=ce(a),S=vt(window);if(l.width=y.width,l.height=y.height,h==="top")if(v){const g=ce(v),w=g.bottom-r.value-l.height;l.fixed=r.value>y.top&&g.bottom>0,l.transform=w<0?w:0}else l.fixed=r.value>y.top;else{const{clientHeight:g}=document.documentElement;if(v){const w=ce(v),b=g-w.top-r.value-l.height;l.fixed=g-r.value<y.bottom&&g>w.top,l.transform=b<0?-b:0}else l.fixed=g-r.value<y.bottom}d(S)};return j(()=>l.fixed,v=>t("change",v)),ye("scroll",f,{target:o,passive:!0}),$i(a,f),j([qe,Ne],()=>{!a.value||zt(a)||!l.fixed||(c.value=!0,le(()=>{const v=ce(a);l.width=v.width,l.height=v.height,c.value=!1}))}),()=>{var v;return i("div",{ref:a,style:u.value},[i("div",{class:Bc({fixed:l.fixed&&!c.value}),style:s.value},[(v=n.default)==null?void 0:v.call(n)])])}}});const Bi=N(Dc),[Ii,Mn]=V("swipe"),Ec={loop:R,width:M,height:M,vertical:Boolean,autoplay:U(0),duration:U(500),touchable:R,lazyRender:Boolean,initialSwipe:U(0),indicatorColor:String,showIndicators:R,stopPropagation:R},Di=Symbol(Ii);var Pc=_({name:Ii,props:Ec,emits:["change","dragStart","dragEnd"],setup(e,{emit:t,slots:n}){const a=O(),o=O(),l=xe({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let c=!1;const r=He(),{children:u,linkChildren:s}=Oe(Di),d=A(()=>u.length),f=A(()=>l[e.vertical?"height":"width"]),v=A(()=>e.vertical?r.deltaY.value:r.deltaX.value),h=A(()=>l.rect?(e.vertical?l.rect.height:l.rect.width)-f.value*d.value:0),y=A(()=>f.value?Math.ceil(Math.abs(h.value)/f.value):d.value),S=A(()=>d.value*f.value),g=A(()=>(l.active+d.value)%d.value),w=A(()=>{const G=e.vertical?"vertical":"horizontal";return r.direction.value===G}),b=A(()=>{const G={transitionDuration:"".concat(l.swiping?0:e.duration,"ms"),transform:"translate".concat(e.vertical?"Y":"X","(").concat(+l.offset.toFixed(2),"px)")};if(f.value){const L=e.vertical?"height":"width",W=e.vertical?"width":"height";G[L]="".concat(S.value,"px"),G[W]=e[W]?"".concat(e[W],"px"):""}return G}),C=G=>{const{active:L}=l;return G?e.loop?ke(L+G,-1,d.value):ke(L+G,0,y.value):L},T=(G,L=0)=>{let W=G*f.value;e.loop||(W=Math.min(W,-h.value));let te=L-W;return e.loop||(te=ke(te,h.value,0)),te},m=({pace:G=0,offset:L=0,emitChange:W})=>{if(d.value<=1)return;const{active:te}=l,z=C(G),K=T(z,L);if(e.loop){if(u[0]&&K!==h.value){const P=K<h.value;u[0].setOffset(P?S.value:0)}if(u[d.value-1]&&K!==0){const P=K>0;u[d.value-1].setOffset(P?-S.value:0)}}l.active=z,l.offset=K,W&&z!==te&&t("change",g.value)},$=()=>{l.swiping=!0,l.active<=-1?m({pace:d.value}):l.active>=d.value&&m({pace:-d.value})},I=()=>{$(),r.reset(),Bt(()=>{l.swiping=!1,m({pace:-1,emitChange:!0})})},x=()=>{$(),r.reset(),Bt(()=>{l.swiping=!1,m({pace:1,emitChange:!0})})};let E;const D=()=>clearTimeout(E),k=()=>{D(),+e.autoplay>0&&d.value>1&&(E=setTimeout(()=>{x(),k()},+e.autoplay))},B=(G=+e.initialSwipe)=>{if(!a.value)return;const L=()=>{var W,te;if(!zt(a)){const z={width:a.value.offsetWidth,height:a.value.offsetHeight};l.rect=z,l.width=+((W=e.width)!=null?W:z.width),l.height=+((te=e.height)!=null?te:z.height)}d.value&&(G=Math.min(d.value-1,G),G===-1&&(G=d.value-1)),l.active=G,l.swiping=!0,l.offset=T(G),u.forEach(z=>{z.setOffset(0)}),k()};zt(a)?le().then(L):L()},p=()=>B(l.active);let Y;const q=G=>{!e.touchable||G.touches.length>1||(r.start(G),c=!1,Y=Date.now(),D(),$())},F=G=>{e.touchable&&l.swiping&&(r.move(G),w.value&&(!e.loop&&(l.active===0&&v.value>0||l.active===d.value-1&&v.value<0)||(ve(G,e.stopPropagation),m({offset:v.value}),c||(t("dragStart",{index:g.value}),c=!0))))},Q=()=>{if(!e.touchable||!l.swiping)return;const G=Date.now()-Y,L=v.value/G;if((Math.abs(L)>.25||Math.abs(v.value)>f.value/2)&&w.value){const te=e.vertical?r.offsetY.value:r.offsetX.value;let z=0;e.loop?z=te>0?v.value>0?-1:1:0:z=-Math[v.value>0?"ceil":"floor"](v.value/f.value),m({pace:z,emitChange:!0})}else v.value&&m({pace:0});c=!1,l.swiping=!1,t("dragEnd",{index:g.value}),k()},ee=(G,L={})=>{$(),r.reset(),Bt(()=>{let W;e.loop&&G===d.value?W=l.active===0?0:G:W=G%d.value,L.immediate?Bt(()=>{l.swiping=!1}):l.swiping=!1,m({pace:W-l.active,emitChange:!0})})},de=(G,L)=>{const W=L===g.value,te=W?{backgroundColor:e.indicatorColor}:void 0;return i("i",{style:te,class:Mn("indicator",{active:W})},null)},he=()=>{if(n.indicator)return n.indicator({active:g.value,total:d.value});if(e.showIndicators&&d.value>1)return i("div",{class:Mn("indicators",{vertical:e.vertical})},[Array(d.value).fill("").map(de)])};return re({prev:I,next:x,state:l,resize:p,swipeTo:ee}),s({size:f,props:e,count:d,activeIndicator:g}),j(()=>e.initialSwipe,G=>B(+G)),j(d,()=>B(l.active)),j(()=>e.autoplay,k),j([qe,Ne,()=>e.width,()=>e.height],p),j(as(),G=>{G==="visible"?k():D()}),we(B),gt(()=>B(l.active)),ba(()=>B(l.active)),bt(D),Dt(D),ye("touchmove",F,{target:o}),()=>{var G;return i("div",{ref:a,class:Mn()},[i("div",{ref:o,style:b.value,class:Mn("track",{vertical:e.vertical}),onTouchstartPassive:q,onTouchend:Q,onTouchcancel:Q},[(G=n.default)==null?void 0:G.call(n)]),he()])}}});const So=N(Pc),[Oc,tl]=V("tabs");var Ac=_({name:Oc,props:{count:Te(Number),inited:Boolean,animated:Boolean,duration:Te(M),swipeable:Boolean,lazyRender:Boolean,currentIndex:Te(Number)},emits:["change"],setup(e,{emit:t,slots:n}){const a=O(),o=r=>t("change",r),l=()=>{var r;const u=(r=n.default)==null?void 0:r.call(n);return e.animated||e.swipeable?i(So,{ref:a,loop:!1,class:tl("track"),duration:+e.duration*1e3,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:o},{default:()=>[u]}):u},c=r=>{const u=a.value;u&&u.state.active!==r&&u.swipeTo(r,{immediate:!e.inited})};return j(()=>e.currentIndex,c),we(()=>{c(e.currentIndex)}),re({swipeRef:a}),()=>i("div",{class:tl("content",{animated:e.animated||e.swipeable})},[l()])}});const[Ei,zn]=V("tabs"),pc={type:H("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:U(0),duration:U(.3),animated:Boolean,ellipsis:R,swipeable:Boolean,scrollspy:Boolean,offsetTop:U(0),background:String,lazyRender:R,showHeader:R,lineWidth:M,lineHeight:M,beforeChange:Function,swipeThreshold:U(5),titleActiveColor:String,titleInactiveColor:String},Pi=Symbol(Ei);var Rc=_({name:Ei,props:pc,emits:["change","scroll","rendered","clickTab","update:active"],setup(e,{emit:t,slots:n}){let a,o,l,c,r;const u=O(),s=O(),d=O(),f=O(),v=vn(),h=dn(u),[y,S]=pn(),{children:g,linkChildren:w}=Oe(Pi),b=xe({inited:!1,position:"",lineStyle:{},currentIndex:-1}),C=A(()=>g.length>+e.swipeThreshold||!e.ellipsis||e.shrink),T=A(()=>({borderColor:e.color,background:e.background})),m=(z,K)=>{var P;return(P=z.name)!=null?P:K},$=A(()=>{const z=g[b.currentIndex];if(z)return m(z,b.currentIndex)}),I=A(()=>mo(e.offsetTop)),x=A(()=>e.sticky?I.value+a:0),E=z=>{const K=s.value,P=y.value;if(!C.value||!K||!P||!P[b.currentIndex])return;const Z=P[b.currentIndex].$el,X=Z.offsetLeft-(K.offsetWidth-Z.offsetWidth)/2;c&&c(),c=Cc(K,X,z?0:+e.duration)},D=()=>{const z=b.inited;le(()=>{const K=y.value;if(!K||!K[b.currentIndex]||e.type!=="line"||zt(u.value))return;const P=K[b.currentIndex].$el,{lineWidth:Z,lineHeight:X}=e,oe=P.offsetLeft+P.offsetWidth/2,fe={width:ne(Z),backgroundColor:e.color,transform:"translateX(".concat(oe,"px) translateX(-50%)")};if(z&&(fe.transitionDuration="".concat(e.duration,"s")),se(X)){const Me=ne(X);fe.height=Me,fe.borderRadius=Me}b.lineStyle=fe})},k=z=>{const K=z<b.currentIndex?-1:1;for(;z>=0&&z<g.length;){if(!g[z].disabled)return z;z+=K}},B=(z,K)=>{const P=k(z);if(!se(P))return;const Z=g[P],X=m(Z,P),oe=b.currentIndex!==null;b.currentIndex!==P&&(b.currentIndex=P,K||E(),D()),X!==e.active&&(t("update:active",X),oe&&t("change",X,Z.title)),l&&!e.scrollspy&&Dn(Math.ceil(No(u.value)-I.value))},p=(z,K)=>{const P=g.findIndex((Z,X)=>m(Z,X)===z);B(P===-1?0:P,K)},Y=(z=!1)=>{if(e.scrollspy){const K=g[b.currentIndex].$el;if(K&&h.value){const P=No(K,h.value)-x.value;o=!0,r&&r(),r=kc(h.value,P,z?0:+e.duration,()=>{o=!1})}}},q=(z,K,P)=>{const{title:Z,disabled:X}=g[K],oe=m(g[K],K);X||(Pt(e.beforeChange,{args:[oe],done:()=>{B(K),Y()}}),hi(z)),t("clickTab",{name:oe,title:Z,event:P,disabled:X})},F=z=>{l=z.isFixed,t("scroll",z)},Q=z=>{le(()=>{p(z),Y(!0)})},ee=()=>{for(let z=0;z<g.length;z++){const{top:K}=ce(g[z].$el);if(K>x.value)return z===0?0:z-1}return g.length-1},de=()=>{if(e.scrollspy&&!o){const z=ee();B(z)}},he=()=>{if(e.type==="line"&&g.length)return i("div",{class:zn("line"),style:b.lineStyle},null)},G=()=>{var z,K,P;const{type:Z,border:X,sticky:oe}=e,fe=[i("div",{ref:oe?void 0:d,class:[zn("wrap"),{[ga]:Z==="line"&&X}]},[i("div",{ref:s,role:"tablist",class:zn("nav",[Z,{shrink:e.shrink,complete:C.value}]),style:T.value,"aria-orientation":"horizontal"},[(z=n["nav-left"])==null?void 0:z.call(n),g.map(Me=>Me.renderTitle(q)),he(),(K=n["nav-right"])==null?void 0:K.call(n)])]),(P=n["nav-bottom"])==null?void 0:P.call(n)];return oe?i("div",{ref:d},[fe]):fe},L=()=>{D(),le(()=>{var z,K;E(!0),(K=(z=f.value)==null?void 0:z.swipeRef.value)==null||K.resize()})};j(()=>[e.color,e.duration,e.lineWidth,e.lineHeight],D),j(qe,L),j(()=>e.active,z=>{z!==$.value&&p(z)}),j(()=>g.length,()=>{b.inited&&(p(e.active),D(),le(()=>{E(!0)}))});const W=()=>{p(e.active,!0),le(()=>{b.inited=!0,d.value&&(a=ce(d.value).height),E(!0)})},te=(z,K)=>t("rendered",z,K);return re({resize:L,scrollTo:Q}),gt(D),ba(D),un(W),$i(u,D),ye("scroll",de,{target:h,passive:!0}),w({id:v,props:e,setLine:D,scrollable:C,onRendered:te,currentName:$,setTitleRefs:S,scrollIntoView:E}),()=>i("div",{ref:u,class:zn([e.type])},[e.showHeader?e.sticky?i(Bi,{container:u.value,offsetTop:I.value,onScroll:F},{default:()=>[G()]}):G():null,i(Ac,{ref:f,count:g.length,inited:b.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:b.currentIndex,onChange:B},{default:()=>{var z;return[(z=n.default)==null?void 0:z.call(n)]}})])}});const Oi=Symbol(),_c=()=>On(Oi,null),[Vc,nl]=V("tab"),Lc=_({name:Vc,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:M,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:R},setup(e,{slots:t}){const n=A(()=>{const o={},{type:l,color:c,disabled:r,isActive:u,activeColor:s,inactiveColor:d}=e;c&&l==="card"&&(o.borderColor=c,r||(u?o.backgroundColor=c:o.color=c));const v=u?s:d;return v&&(o.color=v),o}),a=()=>{const o=i("span",{class:nl("text",{ellipsis:!e.scrollable})},[t.title?t.title():e.title]);return e.dot||se(e.badge)&&e.badge!==""?i(Ht,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[o]}):o};return()=>i("div",{id:e.id,role:"tab",class:[nl([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:n.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[a()])}}),[Mc,zc]=V("swipe-item");var Fc=_({name:Mc,setup(e,{slots:t}){let n;const a=xe({offset:0,inited:!1,mounted:!1}),{parent:o,index:l}=Ie(Di);if(!o)return;const c=A(()=>{const s={},{vertical:d}=o.props;return o.size.value&&(s[d?"height":"width"]="".concat(o.size.value,"px")),a.offset&&(s.transform="translate".concat(d?"Y":"X","(").concat(a.offset,"px)")),s}),r=A(()=>{const{loop:s,lazyRender:d}=o.props;if(!d||n)return!0;if(!a.mounted)return!1;const f=o.activeIndicator.value,v=o.count.value-1,h=f===0&&s?v:f-1,y=f===v&&s?0:f+1;return n=l.value===f||l.value===h||l.value===y,n}),u=s=>{a.offset=s};return we(()=>{le(()=>{a.mounted=!0})}),re({setOffset:u}),()=>{var s;return i("div",{class:zc(),style:c.value},[r.value?(s=t.default)==null?void 0:s.call(t):null])}}});const Co=N(Fc),[Nc,Ra]=V("tab"),Hc=J({},Ot,{dot:Boolean,name:M,badge:M,title:String,disabled:Boolean,titleClass:ge,titleStyle:[String,Object],showZeroBadge:R});var Wc=_({name:Nc,props:Hc,setup(e,{slots:t}){const n=vn(),a=O(!1),o=ot(),{parent:l,index:c}=Ie(Pi);if(!l)return;const r=()=>{var y;return(y=e.name)!=null?y:c.value},u=()=>{a.value=!0,l.props.lazyRender&&le(()=>{l.onRendered(r(),e.title)})},s=A(()=>{const y=r()===l.currentName.value;return y&&!a.value&&u(),y}),d=O(""),f=O("");cn(()=>{const{titleClass:y,titleStyle:S}=e;d.value=y?Lr(y):"",f.value=S&&typeof S!="string"?Mr(zr(S)):S});const v=y=>i(Lc,ie({key:n,id:"".concat(l.id,"-").concat(c.value),ref:l.setTitleRefs(c.value),style:f.value,class:d.value,isActive:s.value,controls:n,scrollable:l.scrollable.value,activeColor:l.props.titleActiveColor,inactiveColor:l.props.titleInactiveColor,onClick:S=>y(o.proxy,c.value,S)},ue(l.props,["type","color","shrink"]),ue(e,["dot","badge","title","disabled","showZeroBadge"])),{title:t.title}),h=O(!s.value);return j(s,y=>{y?h.value=!1:Bt(()=>{h.value=!0})}),j(()=>e.title,()=>{l.setLine(),l.scrollIntoView()}),An(Oi,s),re({id:n,renderTitle:v}),()=>{var y;const S="".concat(l.id,"-").concat(c.value),{animated:g,swipeable:w,scrollspy:b,lazyRender:C}=l.props;if(!t.default&&!g)return;const T=b||s.value;if(g||w)return i(Co,{id:n,role:"tabpanel",class:Ra("panel-wrapper",{inactive:h.value}),tabindex:s.value?0:-1,"aria-hidden":!s.value,"aria-labelledby":S,"data-allow-mismatch":"attribute"},{default:()=>{var I;return[i("div",{class:Ra("panel")},[(I=t.default)==null?void 0:I.call(t)])]}});const $=a.value||b||!C?(y=t.default)==null?void 0:y.call(t):null;return $e(i("div",{id:n,role:"tabpanel",class:Ra("panel"),tabindex:T?0:-1,"aria-labelledby":S,"data-allow-mismatch":"attribute"},[$]),[[Be,T]])}}});const En=N(Wc),xa=N(Rc),[Ai,_a]=V("picker-group"),pi=Symbol(Ai),jc=J({tabs:me(),activeTab:U(0),nextStepText:String,showToolbar:R},wa);var Yc=_({name:Ai,props:jc,emits:["confirm","cancel","update:activeTab"],setup(e,{emit:t,slots:n}){const a=xo(()=>e.activeTab,s=>t("update:activeTab",s)),{children:o,linkChildren:l}=Oe(pi);l();const c=()=>+a.value<e.tabs.length-1&&e.nextStepText,r=()=>{c()?a.value=+a.value+1:t("confirm",o.map(s=>s.confirm()))},u=()=>t("cancel");return()=>{var s,d;let f=(d=(s=n.default)==null?void 0:s.call(n))==null?void 0:d.filter(h=>h.type!==ql).map(h=>h.type===ft?h.children:h);f&&(f=Ur(f));const v=c()?e.nextStepText:e.confirmButtonText;return i("div",{class:_a()},[e.showToolbar?i(Ti,{title:e.title,cancelButtonText:e.cancelButtonText,confirmButtonText:v,onConfirm:r,onCancel:u},ue(n,ki)):null,i(xa,{active:a.value,"onUpdate:active":h=>a.value=h,class:_a("tabs"),shrink:!0,animated:!0,lazyRender:!1},{default:()=>[e.tabs.map((h,y)=>i(En,{title:h,titleClass:_a("tab-title")},{default:()=>[f==null?void 0:f[y]]}))]})])}}});const Sa=J({loading:Boolean,readonly:Boolean,allowHtml:Boolean,optionHeight:U(44),showToolbar:R,swipeDuration:U(1e3),visibleOptionNum:U(6)},wa),Uc=J({},Sa,{columns:me(),modelValue:me(),toolbarPosition:H("top"),columnsFieldNames:Object});var Xc=_({name:vc,props:Uc,emits:["confirm","cancel","change","scrollInto","clickOption","update:modelValue"],setup(e,{emit:t,slots:n}){const a=O(),o=O(e.modelValue.slice(0)),{parent:l}=Ie(pi),{children:c,linkChildren:r}=Oe(Ci);r();const u=A(()=>bc(e.columnsFieldNames)),s=A(()=>mo(e.optionHeight)),d=A(()=>hc(e.columns,u.value)),f=A(()=>{const{columns:k}=e;switch(d.value){case"multiple":return k;case"cascade":return mc(k,u.value,o);default:return[k]}}),v=A(()=>f.value.some(k=>k.length)),h=A(()=>f.value.map((k,B)=>no(k,o.value[B],u.value))),y=A(()=>f.value.map((k,B)=>k.findIndex(p=>p[u.value.value]===o.value[B]))),S=(k,B)=>{if(o.value[k]!==B){const p=o.value.slice(0);p[k]=B,o.value=p}},g=()=>({selectedValues:o.value.slice(0),selectedOptions:h.value,selectedIndexes:y.value}),w=(k,B)=>{S(B,k),d.value==="cascade"&&o.value.forEach((p,Y)=>{const q=f.value[Y];Jo(q,p,u.value)||S(Y,q.length?q[0][u.value.value]:void 0)}),le(()=>{t("change",J({columnIndex:B},g()))})},b=(k,B)=>{const p={columnIndex:B,currentOption:k};t("clickOption",J(g(),p)),t("scrollInto",p)},C=()=>{c.forEach(B=>B.stopMomentum());const k=g();return le(()=>{const B=g();t("confirm",B)}),k},T=()=>t("cancel",g()),m=()=>f.value.map((k,B)=>i(wc,{value:o.value[B],fields:u.value,options:k,readonly:e.readonly,allowHtml:e.allowHtml,optionHeight:s.value,swipeDuration:e.swipeDuration,visibleOptionNum:e.visibleOptionNum,onChange:p=>w(p,B),onClickOption:p=>b(p,B),onScrollInto:p=>{t("scrollInto",{currentOption:p,columnIndex:B})}},{option:n.option})),$=k=>{if(v.value){const B={height:"".concat(s.value,"px")},p={backgroundSize:"100% ".concat((k-s.value)/2,"px")};return[i("div",{class:ct("mask"),style:p},null),i("div",{class:[xs,ct("frame")],style:B},null)]}},I=()=>{const k=s.value*+e.visibleOptionNum,B={height:"".concat(k,"px")};return!e.loading&&!v.value&&n.empty?n.empty():i("div",{ref:a,class:ct("columns"),style:B},[m(),$(k)])},x=()=>{if(e.showToolbar&&!l)return i(Ti,ie(ue(e,Sc),{onConfirm:C,onCancel:T}),ue(n,ki))};j(f,k=>{k.forEach((B,p)=>{B.length&&!Jo(B,o.value[p],u.value)&&S(p,xi(B)[u.value.value])})},{immediate:!0});let E;return j(()=>e.modelValue,k=>{!at(k,o.value)&&!at(k,E)&&(o.value=k.slice(0),E=k.slice(0))},{deep:!0}),j(o,k=>{at(k,e.modelValue)||(E=k.slice(0),t("update:modelValue",E))},{immediate:!0}),ye("touchmove",ve,{target:a}),re({confirm:C,getSelectedOptions:()=>h.value}),()=>{var k,B;return i("div",{class:ct()},[e.toolbarPosition==="top"?x():null,e.loading?i(Ke,{class:ct("loading")},null):null,(k=n["columns-top"])==null?void 0:k.call(n),I(),(B=n["columns-bottom"])==null?void 0:B.call(n),e.toolbarPosition==="bottom"?x():null])}}});const tn="000000",qc=["title","cancel","confirm","toolbar","columns-top","columns-bottom"],Ri=["title","loading","readonly","optionHeight","swipeDuration","visibleOptionNum","cancelButtonText","confirmButtonText"],St=(e="",t=tn,n=void 0)=>({text:e,value:t,children:n});function Gc({areaList:e,columnsNum:t,columnsPlaceholder:n}){const{city_list:a={},county_list:o={},province_list:l={}}=e,c=+t>1,r=+t>2,u=()=>{if(c)return n.length>1?[St(n[1],tn,r?[]:void 0)]:[]},s=new Map;Object.keys(l).forEach(v=>{s.set(v.slice(0,2),St(l[v],v,u()))});const d=new Map;if(c){const v=()=>{if(r)return n.length>2?[St(n[2])]:[]};Object.keys(a).forEach(h=>{const y=St(a[h],h,v());d.set(h.slice(0,4),y);const S=s.get(h.slice(0,2));S&&S.children.push(y)})}r&&Object.keys(o).forEach(v=>{const h=d.get(v.slice(0,4));h&&h.children.push(St(o[v],v))});const f=Array.from(s.values());if(n.length){const v=r?[St(n[2])]:void 0,h=c?[St(n[1],tn,v)]:void 0;f.unshift(St(n[0],tn,h))}return f}const Ca=N(Xc),[Kc,Zc]=V("area"),Jc=J({},ue(Sa,Ri),{modelValue:String,columnsNum:U(3),columnsPlaceholder:me(),areaList:{type:Object,default:()=>({})}});var Qc=_({name:Kc,props:Jc,emits:["change","confirm","cancel","update:modelValue"],setup(e,{emit:t,slots:n}){const a=O([]),o=O(),l=A(()=>Gc(e)),c=(...s)=>t("change",...s),r=(...s)=>t("cancel",...s),u=(...s)=>t("confirm",...s);return j(a,s=>{const d=s.length?s[s.length-1]:"";d&&d!==e.modelValue&&t("update:modelValue",d)},{deep:!0}),j(()=>e.modelValue,s=>{if(s){const d=a.value.length?a.value[a.value.length-1]:"";s!==d&&(a.value=["".concat(s.slice(0,2),"0000"),"".concat(s.slice(0,4),"00"),s].slice(0,+e.columnsNum))}else a.value=[]},{immediate:!0}),re({confirm:()=>{var s;return(s=o.value)==null?void 0:s.confirm()},getSelectedOptions:()=>{var s;return((s=o.value)==null?void 0:s.getSelectedOptions())||[]}}),()=>i(Ca,ie({ref:o,modelValue:a.value,"onUpdate:modelValue":s=>a.value=s,class:Zc(),columns:l.value,onChange:c,onCancel:r,onConfirm:u},ue(e,Ri)),ue(n,qc))}});const _i=N(Qc),[eu,jt]=V("cell"),ka={tag:H("div"),icon:String,size:String,title:M,value:M,label:M,center:Boolean,isLink:Boolean,border:R,iconPrefix:String,valueClass:ge,labelClass:ge,titleClass:ge,titleStyle:null,arrowDirection:String,required:{type:[Boolean,String],default:null},clickable:{type:Boolean,default:null}},tu=J({},ka,Ot);var nu=_({name:eu,props:tu,setup(e,{slots:t}){const n=Nt(),a=()=>{if(t.label||se(e.label))return i("div",{class:[jt("label"),e.labelClass]},[t.label?t.label():e.label])},o=()=>{var u;if(t.title||se(e.title)){const s=(u=t.title)==null?void 0:u.call(t);return Array.isArray(s)&&s.length===0?void 0:i("div",{class:[jt("title"),e.titleClass],style:e.titleStyle},[s||i("span",null,[e.title]),a()])}},l=()=>{const u=t.value||t.default;if(u||se(e.value))return i("div",{class:[jt("value"),e.valueClass]},[u?u():i("span",null,[e.value])])},c=()=>{if(t.icon)return t.icon();if(e.icon)return i(ae,{name:e.icon,class:jt("left-icon"),classPrefix:e.iconPrefix},null)},r=()=>{if(t["right-icon"])return t["right-icon"]();if(e.isLink){const u=e.arrowDirection&&e.arrowDirection!=="right"?"arrow-".concat(e.arrowDirection):"arrow";return i(ae,{name:u,class:jt("right-icon")},null)}};return()=>{var u;const{tag:s,size:d,center:f,border:v,isLink:h,required:y}=e,S=(u=e.clickable)!=null?u:h,g={center:f,required:!!y,clickable:S,borderless:!v};return d&&(g[d]=!!d),i(s,{class:jt(g),role:S?"button":void 0,tabindex:S?0:void 0,onClick:n},{default:()=>{var w;return[c(),o(),l(),r(),(w=t.extra)==null?void 0:w.call(t)]}})}}});const Je=N(nu),[au,ou]=V("form"),lu={colon:Boolean,disabled:Boolean,readonly:Boolean,required:[Boolean,String],showError:Boolean,labelWidth:M,labelAlign:String,inputAlign:String,scrollToError:Boolean,scrollToErrorPosition:String,validateFirst:Boolean,submitOnEnter:R,showErrorMessage:R,errorMessageAlign:String,validateTrigger:{type:[String,Array],default:"onBlur"}};var iu=_({name:au,props:lu,emits:["submit","failed"],setup(e,{emit:t,slots:n}){const{children:a,linkChildren:o}=Oe(si),l=g=>g?a.filter(w=>g.includes(w.name)):a,c=g=>new Promise((w,b)=>{const C=[];l(g).reduce((m,$)=>m.then(()=>{if(!C.length)return $.validate().then(I=>{I&&C.push(I)})}),Promise.resolve()).then(()=>{C.length?b(C):w()})}),r=g=>new Promise((w,b)=>{const C=l(g);Promise.all(C.map(T=>T.validate())).then(T=>{T=T.filter(Boolean),T.length?b(T):w()})}),u=g=>{const w=a.find(b=>b.name===g);return w?new Promise((b,C)=>{w.validate().then(T=>{T?C(T):b()})}):Promise.reject()},s=g=>typeof g=="string"?u(g):e.validateFirst?c(g):r(g),d=g=>{typeof g=="string"&&(g=[g]),l(g).forEach(b=>{b.resetValidation()})},f=()=>a.reduce((g,w)=>(g[w.name]=w.getValidationStatus(),g),{}),v=(g,w)=>{a.some(b=>b.name===g?(b.$el.scrollIntoView(w),!0):!1)},h=()=>a.reduce((g,w)=>(w.name!==void 0&&(g[w.name]=w.formValue.value),g),{}),y=()=>{const g=h();s().then(()=>t("submit",g)).catch(w=>{t("failed",{values:g,errors:w});const{scrollToError:b,scrollToErrorPosition:C}=e;b&&w[0].name&&v(w[0].name,C?{block:C}:void 0)})},S=g=>{ve(g),y()};return o({props:e}),re({submit:y,validate:s,getValues:h,scrollToField:v,resetValidation:d,getValidationStatus:f}),()=>{var g;return i("form",{class:ou(),onSubmit:S},[(g=n.default)==null?void 0:g.call(n)])}}});const ko=N(iu);function Vi(e){return Array.isArray(e)?!e.length:e===0?!1:!e}function ru(e,t){if(Vi(e)){if(t.required)return!1;if(t.validateEmpty===!1)return!0}return!(t.pattern&&!t.pattern.test(String(e)))}function su(e,t){return new Promise(n=>{const a=t.validator(e,t);if(vo(a)){a.then(n);return}n(a)})}function al(e,t){const{message:n}=t;return ln(n)?n(e,t):n||""}function cu({target:e}){e.composing=!0}function ol({target:e}){e.composing&&(e.composing=!1,e.dispatchEvent(new Event("input")))}function uu(e,t){const n=Lt();e.style.height="auto";let a=e.scrollHeight;if(Le(t)){const{maxHeight:o,minHeight:l}=t;o!==void 0&&(a=Math.min(a,o)),l!==void 0&&(a=Math.max(a,l))}a&&(e.style.height="".concat(a,"px"),Dn(n))}function du(e,t){return e==="number"&&(e="text",t!=null||(t="decimal")),e==="digit"&&(e="tel",t!=null||(t="numeric")),{type:e,inputmode:t}}function lt(e){return[...e].length}function Va(e,t){return[...e].slice(0,t).join("")}const[fu,Fe]=V("field"),To={id:String,name:String,leftIcon:String,rightIcon:String,autofocus:Boolean,clearable:Boolean,maxlength:M,max:Number,min:Number,formatter:Function,clearIcon:H("clear"),modelValue:U(""),inputAlign:String,placeholder:String,autocomplete:String,autocapitalize:String,autocorrect:String,errorMessage:String,enterkeyhint:String,clearTrigger:H("focus"),formatTrigger:H("onChange"),spellcheck:{type:Boolean,default:null},error:{type:Boolean,default:null},disabled:{type:Boolean,default:null},readonly:{type:Boolean,default:null},inputmode:String},vu=J({},ka,To,{rows:M,type:H("text"),rules:Array,autosize:[Boolean,Object],labelWidth:M,labelClass:ge,labelAlign:String,showWordLimit:Boolean,errorMessageAlign:String,colon:{type:Boolean,default:null}});var hu=_({name:fu,props:vu,emits:["blur","focus","clear","keypress","clickInput","endValidate","startValidate","clickLeftIcon","clickRightIcon","update:modelValue"],setup(e,{emit:t,slots:n}){const a=vn(),o=xe({status:"unvalidated",focused:!1,validateMessage:""}),l=O(),c=O(),r=O(),{parent:u}=Ie(si),s=()=>{var P;return String((P=e.modelValue)!=null?P:"")},d=P=>{if(se(e[P]))return e[P];if(u&&se(u.props[P]))return u.props[P]},f=A(()=>{const P=d("readonly");if(e.clearable&&!P){const Z=s()!=="",X=e.clearTrigger==="always"||e.clearTrigger==="focus"&&o.focused;return Z&&X}return!1}),v=A(()=>r.value&&n.input?r.value():e.modelValue),h=A(()=>{var P;const Z=d("required");return Z==="auto"?(P=e.rules)==null?void 0:P.some(X=>X.required):Z}),y=P=>P.reduce((Z,X)=>Z.then(()=>{if(o.status==="failed")return;let{value:oe}=v;if(X.formatter&&(oe=X.formatter(oe,X)),!ru(oe,X)){o.status="failed",o.validateMessage=al(oe,X);return}if(X.validator)return Vi(oe)&&X.validateEmpty===!1?void 0:su(oe,X).then(fe=>{fe&&typeof fe=="string"?(o.status="failed",o.validateMessage=fe):fe===!1&&(o.status="failed",o.validateMessage=al(oe,X))})}),Promise.resolve()),S=()=>{o.status="unvalidated",o.validateMessage=""},g=()=>t("endValidate",{status:o.status,message:o.validateMessage}),w=(P=e.rules)=>new Promise(Z=>{S(),P?(t("startValidate"),y(P).then(()=>{o.status==="failed"?(Z({name:e.name,message:o.validateMessage}),g()):(o.status="passed",Z(),g())})):Z()}),b=P=>{if(u&&e.rules){const{validateTrigger:Z}=u.props,X=aa(Z).includes(P),oe=e.rules.filter(fe=>fe.trigger?aa(fe.trigger).includes(P):X);oe.length&&w(oe)}},C=P=>{var Z;const{maxlength:X}=e;if(se(X)&&lt(P)>+X){const oe=s();if(oe&&lt(oe)===+X)return oe;const fe=(Z=l.value)==null?void 0:Z.selectionEnd;if(o.focused&&fe){const Me=[...P],At=Me.length-+X;return Me.splice(fe-At,At),Me.join("")}return Va(P,+X)}return P},T=(P,Z="onChange")=>{var X,oe;const fe=P;P=C(P);const Me=lt(fe)-lt(P);if(e.type==="number"||e.type==="digit"){const et=e.type==="number";if(P=Qa(P,et,et),Z==="onBlur"&&P!==""&&(e.min!==void 0||e.max!==void 0)){const We=ke(+P,(X=e.min)!=null?X:-1/0,(oe=e.max)!=null?oe:1/0);+P!==We&&(P=We.toString())}}let At=0;if(e.formatter&&Z===e.formatTrigger){const{formatter:et,maxlength:We}=e;if(P=et(P),se(We)&&lt(P)>+We&&(P=Va(P,+We)),l.value&&o.focused){const{selectionEnd:Rn}=l.value,Vo=Va(fe,Rn);At=lt(et(Vo))-lt(Vo)}}if(l.value&&l.value.value!==P)if(o.focused){let{selectionStart:et,selectionEnd:We}=l.value;if(l.value.value=P,se(et)&&se(We)){const Rn=lt(P);Me?(et-=Me,We-=Me):At&&(et+=At,We+=At),l.value.setSelectionRange(Math.min(et,Rn),Math.min(We,Rn))}}else l.value.value=P;P!==e.modelValue&&t("update:modelValue",P)},m=P=>{P.target.composing||T(P.target.value)},$=()=>{var P;return(P=l.value)==null?void 0:P.blur()},I=()=>{var P;return(P=l.value)==null?void 0:P.focus()},x=()=>{const P=l.value;e.type==="textarea"&&e.autosize&&P&&uu(P,e.autosize)},E=P=>{o.focused=!0,t("focus",P),le(x),d("readonly")&&$()},D=P=>{o.focused=!1,T(s(),"onBlur"),t("blur",P),!d("readonly")&&(b("onBlur"),le(x),ti())},k=P=>t("clickInput",P),B=P=>t("clickLeftIcon",P),p=P=>t("clickRightIcon",P),Y=P=>{ve(P),t("update:modelValue",""),t("clear",P)},q=A(()=>{if(typeof e.error=="boolean")return e.error;if(u&&u.props.showError&&o.status==="failed")return!0}),F=A(()=>{const P=d("labelWidth"),Z=d("labelAlign");if(P&&Z!=="top")return{width:ne(P)}}),Q=P=>{P.keyCode===13&&(!(u&&u.props.submitOnEnter)&&e.type!=="textarea"&&ve(P),e.type==="search"&&$()),t("keypress",P)},ee=()=>e.id||"".concat(a,"-input"),de=()=>o.status,he=()=>{const P=Fe("control",[d("inputAlign"),{error:q.value,custom:!!n.input,"min-height":e.type==="textarea"&&!e.autosize}]);if(n.input)return i("div",{class:P,onClick:k},[n.input()]);const Z={id:ee(),ref:l,name:e.name,rows:e.rows!==void 0?+e.rows:void 0,class:P,disabled:d("disabled"),readonly:d("readonly"),autofocus:e.autofocus,placeholder:e.placeholder,autocomplete:e.autocomplete,autocapitalize:e.autocapitalize,autocorrect:e.autocorrect,enterkeyhint:e.enterkeyhint,spellcheck:e.spellcheck,"aria-labelledby":e.label?"".concat(a,"-label"):void 0,"data-allow-mismatch":"attribute",onBlur:D,onFocus:E,onInput:m,onClick:k,onChange:ol,onKeypress:Q,onCompositionend:ol,onCompositionstart:cu};return e.type==="textarea"?i("textarea",ie(Z,{inputmode:e.inputmode}),null):i("input",ie(du(e.type,e.inputmode),Z),null)},G=()=>{const P=n["left-icon"];if(e.leftIcon||P)return i("div",{class:Fe("left-icon"),onClick:B},[P?P():i(ae,{name:e.leftIcon,classPrefix:e.iconPrefix},null)])},L=()=>{const P=n["right-icon"];if(e.rightIcon||P)return i("div",{class:Fe("right-icon"),onClick:p},[P?P():i(ae,{name:e.rightIcon,classPrefix:e.iconPrefix},null)])},W=()=>{if(e.showWordLimit&&e.maxlength){const P=lt(s());return i("div",{class:Fe("word-limit")},[i("span",{class:Fe("word-num")},[P]),Gl("/"),e.maxlength])}},te=()=>{if(u&&u.props.showErrorMessage===!1)return;const P=e.errorMessage||o.validateMessage;if(P){const Z=n["error-message"],X=d("errorMessageAlign");return i("div",{class:Fe("error-message",X)},[Z?Z({message:P}):P])}},z=()=>{const P=d("labelWidth"),Z=d("labelAlign"),X=d("colon")?":":"";if(n.label)return[n.label(),X];if(e.label)return i("label",{id:"".concat(a,"-label"),for:n.input?void 0:ee(),"data-allow-mismatch":"attribute",onClick:oe=>{ve(oe),I()},style:Z==="top"&&P?{width:ne(P)}:void 0},[e.label+X])},K=()=>[i("div",{class:Fe("body")},[he(),f.value&&i(ae,{ref:c,name:e.clearIcon,class:Fe("clear")},null),L(),n.button&&i("div",{class:Fe("button")},[n.button()])]),W(),te()];return re({blur:$,focus:I,validate:w,formValue:v,resetValidation:S,getValidationStatus:de}),An(ei,{customValue:r,resetValidation:S,validateWithTrigger:b}),j(()=>e.modelValue,()=>{T(s()),S(),b("onChange"),le(x)}),we(()=>{T(s(),e.formatTrigger),le(x)}),ye("touchstart",Y,{target:A(()=>{var P;return(P=c.value)==null?void 0:P.$el})}),()=>{const P=d("disabled"),Z=d("labelAlign"),X=G(),oe=()=>{const fe=z();return Z==="top"?[X,fe].filter(Boolean):fe||[]};return i(Je,{size:e.size,class:Fe({error:q.value,disabled:P,["label-".concat(Z)]:Z}),center:e.center,border:e.border,isLink:e.isLink,clickable:e.clickable,titleStyle:F.value,valueClass:Fe("value"),titleClass:[Fe("label",[Z,{required:h.value}]),e.labelClass],arrowDirection:e.arrowDirection},{icon:X&&Z!=="top"?()=>X:null,title:oe,value:K,extra:n.extra})}}});const ut=N(hu);let mn=0;function mu(e){e?(mn||document.body.classList.add("van-toast--unclickable"),mn++):mn&&(mn--,mn||document.body.classList.remove("van-toast--unclickable"))}const[gu,Yt]=V("toast"),bu=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"],yu={icon:String,show:Boolean,type:H("text"),overlay:Boolean,message:M,iconSize:M,duration:Ce(2e3),position:H("middle"),teleport:[String,Object],wordBreak:String,className:ge,iconPrefix:String,transition:H("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:ge,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:M};var Li=_({name:gu,props:yu,emits:["update:show"],setup(e,{emit:t,slots:n}){let a,o=!1;const l=()=>{const f=e.show&&e.forbidClick;o!==f&&(o=f,mu(o))},c=f=>t("update:show",f),r=()=>{e.closeOnClick&&c(!1)},u=()=>clearTimeout(a),s=()=>{const{icon:f,type:v,iconSize:h,iconPrefix:y,loadingType:S}=e;if(f||v==="success"||v==="fail")return i(ae,{name:f||v,size:h,class:Yt("icon"),classPrefix:y},null);if(v==="loading")return i(Ke,{class:Yt("loading"),size:h,type:S},null)},d=()=>{const{type:f,message:v}=e;if(n.message)return i("div",{class:Yt("text")},[n.message()]);if(se(v)&&v!=="")return f==="html"?i("div",{key:0,class:Yt("text"),innerHTML:String(v)},null):i("div",{class:Yt("text")},[v])};return j(()=>[e.show,e.forbidClick],l),j(()=>[e.show,e.type,e.message,e.duration],()=>{u(),e.show&&e.duration>0&&(a=setTimeout(()=>{c(!1)},e.duration))}),we(l),da(l),()=>i(Ze,ie({class:[Yt([e.position,e.wordBreak==="normal"?"break-normal":e.wordBreak,{[e.type]:!e.icon}]),e.className],lockScroll:!1,onClick:r,onClosed:u,"onUpdate:show":c},ue(e,bu)),{default:()=>[s(),d()]})}});function Ta(){const e=xe({show:!1}),t=o=>{e.show=o},n=o=>{J(e,o,{transitionAppear:!0}),t(!0)},a=()=>t(!1);return re({open:n,close:a,toggle:t}),{open:n,close:a,state:e,toggle:t}}function $a(e){const t=Fr(e),n=document.createElement("div");return document.body.appendChild(n),{instance:t.mount(n),unmount(){t.unmount(),document.body.removeChild(n)}}}const wu={icon:"",type:"text",message:"",className:"",overlay:!1,onClose:void 0,onOpened:void 0,duration:2e3,teleport:"body",iconSize:void 0,iconPrefix:void 0,position:"middle",transition:"van-fade",forbidClick:!1,loadingType:void 0,overlayClass:"",overlayStyle:void 0,closeOnClick:!1,closeOnClickOverlay:!1};let It=[],xu=!1,ll=J({},wu);const Su=new Map;function Mi(e){return Le(e)?e:{message:e}}function Cu(){const{instance:e,unmount:t}=$a({setup(){const n=O(""),{open:a,state:o,close:l,toggle:c}=Ta(),r=()=>{},u=()=>i(Li,ie(o,{onClosed:r,"onUpdate:show":c}),null);return j(n,s=>{o.message=s}),ot().render=u,{open:a,close:l,message:n}}});return e}function ku(){if(!It.length||xu){const e=Cu();It.push(e)}return It[It.length-1]}function ra(e={}){if(!_e)return{};const t=ku(),n=Mi(e);return t.open(J({},ll,Su.get(n.type||ll.type),n)),t}const Tu=e=>t=>ra(J({type:e},Mi(t))),py=Tu("loading"),Ry=e=>{It.length&&(e?(It.forEach(t=>{t.close()}),It=[]):It[0].close())},$u=N(Li),[Bu,La]=V("switch"),Iu={size:M,loading:Boolean,disabled:Boolean,modelValue:ge,activeColor:String,inactiveColor:String,activeValue:{type:ge,default:!0},inactiveValue:{type:ge,default:!1}};var Du=_({name:Bu,props:Iu,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const a=()=>e.modelValue===e.activeValue,o=()=>{if(!e.disabled&&!e.loading){const c=a()?e.inactiveValue:e.activeValue;t("update:modelValue",c),t("change",c)}},l=()=>{if(e.loading){const c=a()?e.activeColor:e.inactiveColor;return i(Ke,{class:La("loading"),color:c},null)}if(n.node)return n.node()};return Et(()=>e.modelValue),()=>{var c;const{size:r,loading:u,disabled:s,activeColor:d,inactiveColor:f}=e,v=a(),h={fontSize:ne(r),backgroundColor:v?d:f};return i("div",{role:"switch",class:La({on:v,loading:u,disabled:s}),style:h,tabindex:s?void 0:0,"aria-checked":v,onClick:o},[i("div",{class:La("node")},[l()]),(c=n.background)==null?void 0:c.call(n)])}}});const $o=N(Du),[Eu,il]=V("address-edit-detail"),rl=V("address-edit")[2];var Pu=_({name:Eu,props:{show:Boolean,rows:M,value:String,rules:Array,focused:Boolean,maxlength:M,searchResult:Array,showSearchResult:Boolean},emits:["blur","focus","input","selectSearch"],setup(e,{emit:t}){const n=O(),a=()=>e.focused&&e.searchResult&&e.showSearchResult,o=s=>{t("selectSearch",s),t("input","".concat(s.address||""," ").concat(s.name||"").trim())},l=()=>{if(!a())return;const{searchResult:s}=e;return s.map(d=>i(Je,{clickable:!0,key:(d.name||"")+(d.address||""),icon:"location-o",title:d.name,label:d.address,class:il("search-item"),border:!1,onClick:()=>o(d)},null))},c=s=>t("blur",s),r=s=>t("focus",s),u=s=>t("input",s);return()=>{if(e.show)return i(ft,null,[i(ut,{autosize:!0,clearable:!0,ref:n,class:il(),rows:e.rows,type:"textarea",rules:e.rules,label:rl("addressDetail"),border:!a(),maxlength:e.maxlength,modelValue:e.value,placeholder:rl("addressDetail"),onBlur:c,onFocus:r,"onUpdate:modelValue":u},null),l()])}}});const[Ou,Ut,Ae]=V("address-edit"),zi={name:"",tel:"",city:"",county:"",province:"",areaCode:"",isDefault:!1,addressDetail:""},Au={areaList:Object,isSaving:Boolean,isDeleting:Boolean,validator:Function,showArea:R,showDetail:R,showDelete:Boolean,disableArea:Boolean,searchResult:Array,telMaxlength:M,showSetDefault:Boolean,saveButtonText:String,areaPlaceholder:String,deleteButtonText:String,showSearchResult:Boolean,detailRows:U(1),detailMaxlength:U(200),areaColumnsPlaceholder:me(),addressInfo:{type:Object,default:()=>J({},zi)},telValidator:{type:Function,default:Zl}};var pu=_({name:Ou,props:Au,emits:["save","focus","change","delete","clickArea","changeArea","changeDetail","selectSearch","changeDefault"],setup(e,{emit:t,slots:n}){const a=O(),o=xe({}),l=O(!1),c=O(!1),r=A(()=>Le(e.areaList)&&Object.keys(e.areaList).length),u=A(()=>{const{province:$,city:I,county:x,areaCode:E}=o;if(E){const D=[$,I,x];return $&&$===I&&D.splice(1,1),D.filter(Boolean).join("/")}return""}),s=A(()=>{var $;return(($=e.searchResult)==null?void 0:$.length)&&c.value}),d=$=>{c.value=$==="addressDetail",t("focus",$)},f=($,I)=>{t("change",{key:$,value:I})},v=A(()=>{const{validator:$,telValidator:I}=e,x=(E,D)=>({validator:k=>{if($){const B=$(E,k);if(B)return B}return k?!0:D}});return{name:[x("name",Ae("nameEmpty"))],tel:[x("tel",Ae("telInvalid")),{validator:I,message:Ae("telInvalid")}],areaCode:[x("areaCode",Ae("areaEmpty"))],addressDetail:[x("addressDetail",Ae("addressEmpty"))]}}),h=()=>t("save",o),y=$=>{o.addressDetail=$,t("changeDetail",$)},S=$=>{o.province=$[0].text,o.city=$[1].text,o.county=$[2].text},g=({selectedValues:$,selectedOptions:I})=>{$.some(x=>x===tn)?ra(Ae("areaEmpty")):(l.value=!1,S(I),t("changeArea",I))},w=()=>t("delete",o),b=$=>{o.areaCode=$||""},C=()=>{setTimeout(()=>{c.value=!1})},T=$=>{o.addressDetail=$},m=()=>{if(e.showSetDefault){const $={"right-icon":()=>i($o,{modelValue:o.isDefault,"onUpdate:modelValue":I=>o.isDefault=I,onChange:I=>t("changeDefault",I)},null)};return $e(i(Je,{center:!0,border:!1,title:Ae("defaultAddress"),class:Ut("default")},$),[[Be,!s.value]])}};return re({setAreaCode:b,setAddressDetail:T}),j(()=>e.addressInfo,$=>{J(o,zi,$),le(()=>{var I;const x=(I=a.value)==null?void 0:I.getSelectedOptions();x&&x.every(E=>E&&E.value!==tn)&&S(x)})},{deep:!0,immediate:!0}),()=>{const{disableArea:$}=e;return i(ko,{class:Ut(),onSubmit:h},{default:()=>{var I;return[i("div",{class:Ut("fields")},[i(ut,{modelValue:o.name,"onUpdate:modelValue":[x=>o.name=x,x=>f("name",x)],clearable:!0,label:Ae("name"),rules:v.value.name,placeholder:Ae("name"),onFocus:()=>d("name")},null),i(ut,{modelValue:o.tel,"onUpdate:modelValue":[x=>o.tel=x,x=>f("tel",x)],clearable:!0,type:"tel",label:Ae("tel"),rules:v.value.tel,maxlength:e.telMaxlength,placeholder:Ae("tel"),onFocus:()=>d("tel")},null),$e(i(ut,{readonly:!0,label:Ae("area"),"is-link":!$,modelValue:u.value,rules:e.showArea?v.value.areaCode:void 0,placeholder:e.areaPlaceholder||Ae("area"),onFocus:()=>d("areaCode"),onClick:()=>{t("clickArea"),l.value=!$}},null),[[Be,e.showArea]]),i(Pu,{show:e.showDetail,rows:e.detailRows,rules:v.value.addressDetail,value:o.addressDetail,focused:c.value,maxlength:e.detailMaxlength,searchResult:e.searchResult,showSearchResult:e.showSearchResult,onBlur:C,onFocus:()=>d("addressDetail"),onInput:y,onSelectSearch:x=>t("selectSearch",x)},null),(I=n.default)==null?void 0:I.call(n)]),m(),$e(i("div",{class:Ut("buttons")},[i(Pe,{block:!0,round:!0,type:"primary",text:e.saveButtonText||Ae("save"),class:Ut("button"),loading:e.isSaving,nativeType:"submit"},null),e.showDelete&&i(Pe,{block:!0,round:!0,class:Ut("button"),loading:e.isDeleting,text:e.deleteButtonText||Ae("delete"),onClick:w},null)]),[[Be,!s.value]]),i(Ze,{show:l.value,"onUpdate:show":x=>l.value=x,round:!0,teleport:"body",position:"bottom",lazyRender:!1},{default:()=>[i(_i,{modelValue:o.areaCode,"onUpdate:modelValue":x=>o.areaCode=x,ref:a,loading:!r.value,areaList:e.areaList,columnsPlaceholder:e.areaColumnsPlaceholder,onConfirm:g,onCancel:()=>{l.value=!1}},null)]})]}})}}});const Ru=N(pu),[Fi,_u]=V("radio-group"),Vu={shape:String,disabled:Boolean,iconSize:M,direction:String,modelValue:ge,checkedColor:String},Ni=Symbol(Fi);var Lu=_({name:Fi,props:Vu,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{linkChildren:a}=Oe(Ni),o=l=>t("update:modelValue",l);return j(()=>e.modelValue,l=>t("change",l)),a({props:e,updateValue:o}),Et(()=>e.modelValue),()=>{var l;return i("div",{class:_u([e.direction]),role:"radiogroup"},[(l=n.default)==null?void 0:l.call(n)])}}});const Bo=N(Lu),[Hi,Mu]=V("checkbox-group"),zu={max:M,shape:H("round"),disabled:Boolean,iconSize:M,direction:String,modelValue:me(),checkedColor:String},Wi=Symbol(Hi);var Fu=_({name:Hi,props:zu,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{children:a,linkChildren:o}=Oe(Wi),l=r=>t("update:modelValue",r),c=(r={})=>{typeof r=="boolean"&&(r={checked:r});const{checked:u,skipDisabled:s}=r,f=a.filter(v=>v.props.bindGroup?v.props.disabled&&s?v.checked.value:u!=null?u:!v.checked.value:!1).map(v=>v.name);l(f)};return j(()=>e.modelValue,r=>t("change",r)),re({toggleAll:c}),Et(()=>e.modelValue),o({props:e,updateValue:l}),()=>{var r;return i("div",{class:Mu([e.direction])},[(r=n.default)==null?void 0:r.call(n)])}}});const ji=N(Fu),[Nu,sl]=V("tag"),Hu={size:String,mark:Boolean,show:R,type:H("default"),color:String,plain:Boolean,round:Boolean,textColor:String,closeable:Boolean};var Wu=_({name:Nu,props:Hu,emits:["close"],setup(e,{slots:t,emit:n}){const a=c=>{c.stopPropagation(),n("close",c)},o=()=>e.plain?{color:e.textColor||e.color,borderColor:e.color}:{color:e.textColor,background:e.color},l=()=>{var c;const{type:r,mark:u,plain:s,round:d,size:f,closeable:v}=e,h={mark:u,plain:s,round:d};f&&(h[f]=f);const y=v&&i(ae,{name:"cross",class:[sl("close"),Ee],onClick:a},null);return i("span",{style:o(),class:sl([h,r])},[(c=t.default)==null?void 0:c.call(t),y])};return()=>i(fa,{name:e.closeable?"van-fade":void 0},{default:()=>[e.show?l():null]})}});const Ba=N(Wu),Io={name:ge,disabled:Boolean,iconSize:M,modelValue:ge,checkedColor:String,labelPosition:String,labelDisabled:Boolean};var Yi=_({props:J({},Io,{bem:Te(Function),role:String,shape:String,parent:Object,checked:Boolean,bindGroup:R,indeterminate:{type:Boolean,default:null}}),emits:["click","toggle"],setup(e,{emit:t,slots:n}){const a=O(),o=v=>{if(e.parent&&e.bindGroup)return e.parent.props[v]},l=A(()=>{if(e.parent&&e.bindGroup){const v=o("disabled")||e.disabled;if(e.role==="checkbox"){const h=o("modelValue").length,y=o("max"),S=y&&h>=+y;return v||S&&!e.checked}return v}return e.disabled}),c=A(()=>o("direction")),r=A(()=>{const v=e.checkedColor||o("checkedColor");if(v&&e.checked&&!l.value)return{borderColor:v,backgroundColor:v}}),u=A(()=>e.shape||o("shape")||"round"),s=v=>{const{target:h}=v,y=a.value,S=y===h||(y==null?void 0:y.contains(h));!l.value&&(S||!e.labelDisabled)&&t("toggle"),t("click",v)},d=()=>{var v,h;const{bem:y,checked:S,indeterminate:g}=e,w=e.iconSize||o("iconSize");return i("div",{ref:a,class:y("icon",[u.value,{disabled:l.value,checked:S,indeterminate:g}]),style:u.value!=="dot"?{fontSize:ne(w)}:{width:ne(w),height:ne(w),borderColor:(v=r.value)==null?void 0:v.borderColor}},[n.icon?n.icon({checked:S,disabled:l.value}):u.value!=="dot"?i(ae,{name:g?"minus":"success",style:r.value},null):i("div",{class:y("icon--dot__icon"),style:{backgroundColor:(h=r.value)==null?void 0:h.backgroundColor}},null)])},f=()=>{const{checked:v}=e;if(n.default)return i("span",{class:e.bem("label",[e.labelPosition,{disabled:l.value}])},[n.default({checked:v,disabled:l.value})])};return()=>{const v=e.labelPosition==="left"?[f(),d()]:[d(),f()];return i("div",{role:e.role,class:e.bem([{disabled:l.value,"label-disabled":e.labelDisabled},c.value]),tabindex:l.value?void 0:0,"aria-checked":e.checked,onClick:s},[v])}}});const ju=J({},Io,{shape:String}),[Yu,Uu]=V("radio");var Xu=_({name:Yu,props:ju,emits:["update:modelValue"],setup(e,{emit:t,slots:n}){const{parent:a}=Ie(Ni),o=()=>(a?a.props.modelValue:e.modelValue)===e.name,l=()=>{a?a.updateValue(e.name):t("update:modelValue",e.name)};return()=>i(Yi,ie({bem:Uu,role:"radio",parent:a,checked:o(),onToggle:l},e),ue(n,["default","icon"]))}});const Do=N(Xu),[qu,Gu]=V("checkbox"),Ku=J({},Io,{shape:String,bindGroup:R,indeterminate:{type:Boolean,default:null}});var Zu=_({name:qu,props:Ku,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{parent:a}=Ie(Wi),o=r=>{const{name:u}=e,{max:s,modelValue:d}=a.props,f=d.slice();if(r)!(s&&f.length>=+s)&&!f.includes(u)&&(f.push(u),e.bindGroup&&a.updateValue(f));else{const v=f.indexOf(u);v!==-1&&(f.splice(v,1),e.bindGroup&&a.updateValue(f))}},l=A(()=>a&&e.bindGroup?a.props.modelValue.indexOf(e.name)!==-1:!!e.modelValue),c=(r=!l.value)=>{a&&e.bindGroup?o(r):t("update:modelValue",r),e.indeterminate!==null&&t("change",r)};return j(()=>e.modelValue,r=>{e.indeterminate===null&&t("change",r)}),re({toggle:c,props:e,checked:l}),Et(()=>e.modelValue),()=>i(Yi,ie({bem:Gu,role:"checkbox",parent:a,checked:l.value,onToggle:c},e),ue(n,["default","icon"]))}});const Eo=N(Zu),[Ju,Xt]=V("address-item");var Qu=_({name:Ju,props:{address:Te(Object),disabled:Boolean,switchable:Boolean,singleChoice:Boolean,defaultTagText:String,rightIcon:H("edit")},emits:["edit","click","select"],setup(e,{slots:t,emit:n}){const a=r=>{e.switchable&&n("select"),n("click",r)},o=()=>i(ae,{name:e.rightIcon,class:Xt("edit"),onClick:r=>{r.stopPropagation(),n("edit"),n("click",r)}},null),l=()=>{if(t.tag)return t.tag(e.address);if(e.address.isDefault&&e.defaultTagText)return i(Ba,{type:"primary",round:!0,class:Xt("tag")},{default:()=>[e.defaultTagText]})},c=()=>{const{address:r,disabled:u,switchable:s,singleChoice:d}=e,f=[i("div",{class:Xt("name")},["".concat(r.name," ").concat(r.tel),l()]),i("div",{class:Xt("address")},[r.address])];return s&&!u?d?i(Do,{name:r.id,iconSize:18},{default:()=>[f]}):i(Eo,{name:r.id,iconSize:18},{default:()=>[f]}):f};return()=>{var r;const{disabled:u}=e;return i("div",{class:Xt({disabled:u}),onClick:a},[i(Je,{border:!1,titleClass:Xt("title")},{title:c,"right-icon":o}),(r=t.bottom)==null?void 0:r.call(t,J({},e.address,{disabled:u}))])}}});const[ed,Fn,td]=V("address-list"),nd={list:me(),modelValue:[...M,Array],switchable:R,disabledText:String,disabledList:me(),showAddButton:R,addButtonText:String,defaultTagText:String,rightIcon:H("edit")};var ad=_({name:ed,props:nd,emits:["add","edit","select","clickItem","editDisabled","selectDisabled","update:modelValue"],setup(e,{slots:t,emit:n}){const a=A(()=>!Array.isArray(e.modelValue)),o=(r,u,s)=>{const d=()=>n(s?"editDisabled":"edit",r,u),f=h=>n("clickItem",r,u,{event:h}),v=()=>{if(n(s?"selectDisabled":"select",r,u),!s)if(a.value)n("update:modelValue",r.id);else{const h=e.modelValue;h.includes(r.id)?n("update:modelValue",h.filter(y=>y!==r.id)):n("update:modelValue",[...h,r.id])}};return i(Qu,{key:r.id,address:r,disabled:s,switchable:e.switchable,singleChoice:a.value,defaultTagText:e.defaultTagText,rightIcon:e.rightIcon,onEdit:d,onClick:f,onSelect:v},{bottom:t["item-bottom"],tag:t.tag})},l=(r,u)=>{if(r)return r.map((s,d)=>o(s,d,u))},c=()=>e.showAddButton?i("div",{class:[Fn("bottom"),"van-safe-area-bottom"]},[i(Pe,{round:!0,block:!0,type:"primary",text:e.addButtonText||td("add"),class:Fn("add"),onClick:()=>n("add")},null)]):void 0;return()=>{var r,u;const s=l(e.list),d=l(e.disabledList,!0),f=e.disabledText&&i("div",{class:Fn("disabled-text")},[e.disabledText]);return i("div",{class:Fn()},[(r=t.top)==null?void 0:r.call(t),!a.value&&Array.isArray(e.modelValue)?i(ji,{modelValue:e.modelValue},{default:()=>[s]}):i(Bo,{modelValue:e.modelValue},{default:()=>[s]}),f,d,(u=t.default)==null?void 0:u.call(t),c()])}}});const od=N(ad),cl=Ve&&"IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype,pt={event:"event",observer:"observer"};function Sn(e,t){if(!e.length)return;const n=e.indexOf(t);if(n>-1)return e.splice(n,1)}function ul(e,t){if(e.tagName!=="IMG"||!e.getAttribute("data-srcset"))return;let n=e.getAttribute("data-srcset");const o=e.parentNode.offsetWidth*t;let l,c,r;n=n.trim().split(",");const u=n.map(f=>(f=f.trim(),l=f.lastIndexOf(" "),l===-1?(c=f,r=999998):(c=f.substr(0,l),r=parseInt(f.substr(l+1,f.length-l-2),10)),[r,c]));u.sort((f,v)=>{if(f[0]<v[0])return 1;if(f[0]>v[0])return-1;if(f[0]===v[0]){if(v[1].indexOf(".webp",v[1].length-5)!==-1)return 1;if(f[1].indexOf(".webp",f[1].length-5)!==-1)return-1}return 0});let s="",d;for(let f=0;f<u.length;f++){d=u[f],s=d[1];const v=u[f+1];if(v&&v[0]<o){s=d[1];break}else if(!v){s=d[1];break}}return s}const ld=(e=1)=>Ve&&window.devicePixelRatio||e;function id(){if(!Ve)return!1;let e=!0;try{const t=document.createElement("canvas");t.getContext&&t.getContext("2d")&&(e=t.toDataURL("image/webp").indexOf("data:image/webp")===0)}catch(t){e=!1}return e}function Ui(e,t){let n=null,a=0;return function(...o){if(n)return;const l=Date.now()-a,c=()=>{a=Date.now(),n=!1,e.apply(this,o)};l>=t?c():n=setTimeout(c,t)}}function rd(e,t,n){e.addEventListener(t,n,{capture:!1,passive:!0})}function sd(e,t,n){e.removeEventListener(t,n,!1)}const ao=(e,t,n)=>{const a=new Image;if(!e||!e.src)return n(new Error("image src is required"));a.src=e.src,e.cors&&(a.crossOrigin=e.cors),a.onload=()=>t({naturalHeight:a.naturalHeight,naturalWidth:a.naturalWidth,src:a.src}),a.onerror=o=>n(o)};class cd{constructor({max:t}){this.options={max:t||100},this.caches=[]}has(t){return this.caches.indexOf(t)>-1}add(t){this.has(t)||(this.caches.push(t),this.caches.length>this.options.max&&this.free())}free(){this.caches.shift()}}const[ud,Ma]=V("back-top"),dd={right:M,bottom:M,zIndex:M,target:[String,Object],offset:U(200),immediate:Boolean,teleport:{type:[String,Object],default:"body"}};var fd=_({name:ud,inheritAttrs:!1,props:dd,emits:["click"],setup(e,{emit:t,slots:n,attrs:a}){let o=!1;const l=O(!1),c=O(),r=O(),u=A(()=>J(wt(e.zIndex),{right:ne(e.right),bottom:ne(e.bottom)})),s=h=>{var y;t("click",h),(y=r.value)==null||y.scrollTo({top:0,behavior:e.immediate?"auto":"smooth"})},d=()=>{l.value=r.value?vt(r.value)>=+e.offset:!1},f=()=>{const{target:h}=e;if(typeof h=="string"){const y=document.querySelector(h);if(y)return y}else return h},v=()=>{_e&&le(()=>{r.value=e.target?f():ma(c.value),d()})};return ye("scroll",Ui(d,100),{target:r}),we(v),gt(()=>{o&&(l.value=!0,o=!1)}),bt(()=>{l.value&&e.teleport&&(l.value=!1,o=!0)}),j(()=>e.target,v),()=>{const h=i("div",ie({ref:e.teleport?void 0:c,class:Ma({active:l.value}),style:u.value,onClick:s},a),[n.default?n.default():i(ae,{name:"back-top",class:Ma("icon")},null)]);return e.teleport?[i("div",{ref:c,class:Ma("placeholder")},null),i(Ft,{to:e.teleport},{default:()=>[h]})]:h}}});const vd=N(fd);var hd=(e,t,n)=>new Promise((a,o)=>{var l=u=>{try{r(n.next(u))}catch(s){o(s)}},c=u=>{try{r(n.throw(u))}catch(s){o(s)}},r=u=>u.done?a(u.value):Promise.resolve(u.value).then(l,c);r((n=n.apply(e,t)).next())});const md={top:U(10),rows:U(4),duration:U(4e3),autoPlay:R,delay:Ce(300),modelValue:me()},[gd,dl]=V("barrage");var bd=_({name:gd,props:md,emits:["update:modelValue"],setup(e,{emit:t,slots:n}){const a=O(),o=dl("item"),l=O(0),c=[],r=(S,g=e.delay)=>{const w=document.createElement("span");return w.className=o,w.innerText=String(S),w.style.animationDuration="".concat(e.duration,"ms"),w.style.animationDelay="".concat(g,"ms"),w.style.animationName="van-barrage",w.style.animationTimingFunction="linear",w},u=O(!0),s=O(e.autoPlay),d=({id:S,text:g},w)=>{var b;const C=r(g,u.value?w*e.delay:void 0);!e.autoPlay&&s.value===!1&&(C.style.animationPlayState="paused"),(b=a.value)==null||b.append(C),l.value++;const T=(l.value-1)%+e.rows*C.offsetHeight+ +e.top;C.style.top="".concat(T,"px"),C.dataset.id=String(S),c.push(C),C.addEventListener("animationend",()=>{t("update:modelValue",[...e.modelValue].filter(m=>String(m.id)!==C.dataset.id))})},f=(S,g)=>{const w=new Map(g.map(b=>[b.id,b]));S.forEach((b,C)=>{w.has(b.id)?w.delete(b.id):d(b,C)}),w.forEach(b=>{const C=c.findIndex(T=>T.dataset.id===String(b.id));C>-1&&(c[C].remove(),c.splice(C,1))}),u.value=!1};j(()=>e.modelValue.slice(),(S,g)=>f(S!=null?S:[],g!=null?g:[]),{deep:!0});const v=O({});return we(()=>hd(this,null,function*(){var S;v.value["--move-distance"]="-".concat((S=a.value)==null?void 0:S.offsetWidth,"px"),yield le(),f(e.modelValue,[])})),re({play:()=>{s.value=!0,c.forEach(S=>{S.style.animationPlayState="running"})},pause:()=>{s.value=!1,c.forEach(S=>{S.style.animationPlayState="paused"})}}),()=>{var S;return i("div",{class:dl(),ref:a,style:v.value},[(S=n.default)==null?void 0:S.call(n)])}}});const yd=N(bd),[wd,be,dt]=V("calendar"),xd=e=>dt("monthTitle",e.getFullYear(),e.getMonth()+1);function Vt(e,t){const n=e.getFullYear(),a=t.getFullYear();if(n===a){const o=e.getMonth(),l=t.getMonth();return o===l?0:o>l?1:-1}return n>a?1:-1}function Re(e,t){const n=Vt(e,t);if(n===0){const a=e.getDate(),o=t.getDate();return a===o?0:a>o?1:-1}return n}const rn=e=>new Date(e),fl=e=>Array.isArray(e)?e.map(rn):rn(e);function Po(e,t){const n=rn(e);return n.setDate(n.getDate()+t),n}function Oo(e,t){const n=rn(e);return n.setMonth(n.getMonth()+t),n.getDate()!==e.getDate()&&n.setDate(0),n}function Xi(e,t){const n=rn(e);return n.setFullYear(n.getFullYear()+t),n.getDate()!==e.getDate()&&n.setDate(0),n}const oo=e=>Po(e,-1),lo=e=>Po(e,1),vl=e=>Oo(e,-1),hl=e=>Oo(e,1),ml=e=>Xi(e,-1),gl=e=>Xi(e,1),Nn=()=>{const e=new Date;return e.setHours(0,0,0,0),e};function Sd(e){const t=e[0].getTime();return(e[1].getTime()-t)/(1e3*60*60*24)+1}function Cd(e,t=0){const n=new Date(e.getFullYear(),e.getMonth()+1,0),a=t+e.getDate()-1,o=t+n.getDate()-1;return Math.floor(a/7)===Math.floor(o/7)}const qi=J({},Sa,{modelValue:me(),filter:Function,formatter:{type:Function,default:(e,t)=>t}}),Gi=Object.keys(Sa);function kd(e,t){if(e<0)return[];const n=Array(e);let a=-1;for(;++a<e;)n[a]=t(a);return n}const Ki=(e,t)=>32-new Date(e,t-1,32).getDate(),nn=(e,t,n,a,o,l)=>{const c=kd(t-e+1,r=>{const u=Xe(e+r);return a(n,{text:u,value:u})});return o?o(n,c,l):c},Zi=(e,t)=>e.map((n,a)=>{const o=t[a];if(o.length){const l=+o[0].value,c=+o[o.length-1].value;return Xe(ke(+n,l,c))}return n}),[Td]=V("calendar-day");var $d=_({name:Td,props:{item:Te(Object),color:String,index:Number,offset:Ce(0),rowHeight:String},emits:["click","clickDisabledDate"],setup(e,{emit:t,slots:n}){const a=A(()=>{const{item:s,index:d,color:f,offset:v,rowHeight:h}=e,y={height:h};if(s.type==="placeholder")return y.width="100%",y;if(d===0&&(y.marginLeft="".concat(100*v/7,"%")),f)switch(s.type){case"end":case"start":case"start-end":case"multiple-middle":case"multiple-selected":y.background=f;break;case"middle":y.color=f;break}return s.date&&Cd(s.date,v)&&(y.marginBottom=0),y}),o=()=>{e.item.type!=="disabled"?t("click",e.item):t("clickDisabledDate",e.item)},l=()=>{const{topInfo:s}=e.item;if(s||n["top-info"])return i("div",{class:be("top-info")},[n["top-info"]?n["top-info"](e.item):s])},c=()=>{const{bottomInfo:s}=e.item;if(s||n["bottom-info"])return i("div",{class:be("bottom-info")},[n["bottom-info"]?n["bottom-info"](e.item):s])},r=()=>n.text?n.text(e.item):e.item.text,u=()=>{const{item:s,color:d,rowHeight:f}=e,{type:v}=s,h=[l(),r(),c()];return v==="selected"?i("div",{class:be("selected-day"),style:{width:f,height:f,background:d}},[h]):h};return()=>{const{type:s,className:d}=e.item;return s==="placeholder"?i("div",{class:be("day"),style:a.value},null):i("div",{role:"gridcell",style:a.value,class:[be("day",s),d],tabindex:s==="disabled"?void 0:-1,onClick:o},[u()])}}});const[Bd]=V("calendar-month"),Id={date:Te(Date),type:String,color:String,minDate:Date,maxDate:Date,showMark:Boolean,rowHeight:M,formatter:Function,lazyRender:Boolean,currentDate:[Date,Array],allowSameDay:Boolean,showSubtitle:Boolean,showMonthTitle:Boolean,firstDayOfWeek:Number};var Dd=_({name:Bd,props:Id,emits:["click","clickDisabledDate"],setup(e,{emit:t,slots:n}){const[a,o]=qr(),l=O(),c=O(),r=di(c),u=A(()=>xd(e.date)),s=A(()=>ne(e.rowHeight)),d=A(()=>{const D=e.date.getDate(),B=(e.date.getDay()-D%7+8)%7;return e.firstDayOfWeek?(B+7-e.firstDayOfWeek)%7:B}),f=A(()=>Ki(e.date.getFullYear(),e.date.getMonth()+1)),v=A(()=>a.value||!e.lazyRender),h=()=>u.value,y=D=>{const k=B=>e.currentDate.some(p=>Re(p,B)===0);if(k(D)){const B=oo(D),p=lo(D),Y=k(B),q=k(p);return Y&&q?"multiple-middle":Y?"end":q?"start":"multiple-selected"}return""},S=D=>{const[k,B]=e.currentDate;if(!k)return"";const p=Re(D,k);if(!B)return p===0?"start":"";const Y=Re(D,B);return e.allowSameDay&&p===0&&Y===0?"start-end":p===0?"start":Y===0?"end":p>0&&Y<0?"middle":""},g=D=>{const{type:k,minDate:B,maxDate:p,currentDate:Y}=e;if(B&&Re(D,B)<0||p&&Re(D,p)>0)return"disabled";if(Y===null)return"";if(Array.isArray(Y)){if(k==="multiple")return y(D);if(k==="range")return S(D)}else if(k==="single")return Re(D,Y)===0?"selected":"";return""},w=D=>{if(e.type==="range"){if(D==="start"||D==="end")return dt(D);if(D==="start-end")return"".concat(dt("start"),"/").concat(dt("end"))}},b=()=>{if(e.showMonthTitle)return i("div",{class:be("month-title")},[n["month-title"]?n["month-title"]({date:e.date,text:u.value}):u.value])},C=()=>{if(e.showMark&&v.value)return i("div",{class:be("month-mark")},[e.date.getMonth()+1])},T=A(()=>{const D=Math.ceil((f.value+d.value)/7);return Array(D).fill({type:"placeholder"})}),m=A(()=>{const D=[],k=e.date.getFullYear(),B=e.date.getMonth();for(let p=1;p<=f.value;p++){const Y=new Date(k,B,p),q=g(Y);let F={date:Y,type:q,text:p,bottomInfo:w(q)};e.formatter&&(F=e.formatter(F)),D.push(F)}return D}),$=A(()=>m.value.filter(D=>D.type==="disabled")),I=(D,k)=>{if(l.value){const B=ce(l.value),p=T.value.length,q=(Math.ceil((k.getDate()+d.value)/7)-1)*B.height/p;oa(D,B.top+q+D.scrollTop-ce(D).top)}},x=(D,k)=>i($d,{item:D,index:k,color:e.color,offset:d.value,rowHeight:s.value,onClick:B=>t("click",B),onClickDisabledDate:B=>t("clickDisabledDate",B)},ue(n,["top-info","bottom-info","text"])),E=()=>i("div",{ref:l,role:"grid",class:be("days")},[C(),(v.value?m:T).value.map(x)]);return re({getTitle:h,getHeight:()=>r.value,setVisible:o,scrollToDate:I,disabledDays:$}),()=>i("div",{class:be("month"),ref:c},[b(),E()])}});const[Ed]=V("calendar-header");var Pd=_({name:Ed,props:{date:Date,minDate:Date,maxDate:Date,title:String,subtitle:String,showTitle:Boolean,showSubtitle:Boolean,firstDayOfWeek:Number,switchMode:H("none")},emits:["clickSubtitle","panelChange"],setup(e,{slots:t,emit:n}){const a=A(()=>e.date&&e.minDate&&Vt(vl(e.date),e.minDate)<0),o=A(()=>e.date&&e.minDate&&Vt(ml(e.date),e.minDate)<0),l=A(()=>e.date&&e.maxDate&&Vt(hl(e.date),e.maxDate)>0),c=A(()=>e.date&&e.maxDate&&Vt(gl(e.date),e.maxDate)>0),r=()=>{if(e.showTitle){const h=e.title||dt("title"),y=t.title?t.title():h;return i("div",{class:be("header-title")},[y])}},u=h=>n("clickSubtitle",h),s=h=>n("panelChange",h),d=h=>{const y=e.switchMode==="year-month",S=t[h?"next-month":"prev-month"],g=t[h?"next-year":"prev-year"],w=h?l.value:a.value,b=h?c.value:o.value,C=h?"arrow":"arrow-left",T=h?"arrow-double-right":"arrow-double-left",m=()=>s((h?hl:vl)(e.date)),$=()=>s((h?gl:ml)(e.date)),I=i("view",{class:be("header-action",{disabled:w}),onClick:w?void 0:m},[S?S({disabled:w}):i(ae,{class:{[Ee]:!w},name:C},null)]),x=y&&i("view",{class:be("header-action",{disabled:b}),onClick:b?void 0:$},[g?g({disabled:b}):i(ae,{class:{[Ee]:!b},name:T},null)]);return h?[I,x]:[x,I]},f=()=>{if(e.showSubtitle){const h=t.subtitle?t.subtitle({date:e.date,text:e.subtitle}):e.subtitle,y=e.switchMode!=="none";return i("div",{class:be("header-subtitle",{"with-switch":y}),onClick:u},[y?[d(),i("div",{class:be("header-subtitle-text")},[h]),d(!0)]:h])}},v=()=>{const{firstDayOfWeek:h}=e,y=dt("weekdays"),S=[...y.slice(h,7),...y.slice(0,h)];return i("div",{class:be("weekdays")},[S.map(g=>i("span",{class:be("weekday")},[g]))])};return()=>i("div",{class:be("header")},[r(),f(),v()])}});const Od={show:Boolean,type:H("single"),switchMode:H("none"),title:String,color:String,round:R,readonly:Boolean,poppable:R,maxRange:U(null),position:H("bottom"),teleport:[String,Object],showMark:R,showTitle:R,formatter:Function,rowHeight:M,confirmText:String,rangePrompt:String,lazyRender:R,showConfirm:R,defaultDate:[Date,Array],allowSameDay:Boolean,showSubtitle:R,closeOnPopstate:R,showRangePrompt:R,confirmDisabledText:String,closeOnClickOverlay:R,safeAreaInsetTop:Boolean,safeAreaInsetBottom:R,minDate:{type:Date,validator:In},maxDate:{type:Date,validator:In},firstDayOfWeek:{type:M,default:0,validator:e=>e>=0&&e<=6}};var Ad=_({name:wd,props:Od,emits:["select","confirm","unselect","monthShow","overRange","update:show","clickSubtitle","clickDisabledDate","clickOverlay","panelChange"],setup(e,{emit:t,slots:n}){const a=A(()=>e.switchMode!=="none"),o=A(()=>!e.minDate&&!a.value?Nn():e.minDate),l=A(()=>!e.maxDate&&!a.value?Oo(Nn(),6):e.maxDate),c=(L,W=o.value,te=l.value)=>W&&Re(L,W)===-1?W:te&&Re(L,te)===1?te:L,r=(L=e.defaultDate)=>{const{type:W,allowSameDay:te}=e;if(L===null)return L;const z=Nn();if(W==="range"){Array.isArray(L)||(L=[]),L.length===1&&Re(L[0],z)===1&&(L=[]);const K=o.value,P=l.value,Z=c(L[0]||z,K,P?te?P:oo(P):void 0),X=c(L[1]||(te?z:lo(z)),K?te?K:lo(K):void 0);return[Z,X]}return W==="multiple"?Array.isArray(L)?L.map(K=>c(K)):[c(z)]:((!L||Array.isArray(L))&&(L=z),c(L))},u=()=>{const L=Array.isArray(f.value)?f.value[0]:f.value;return L||c(Nn())};let s;const d=O(),f=O(r()),v=O(u()),h=O(),[y,S]=pn(),g=A(()=>e.firstDayOfWeek?+e.firstDayOfWeek%7:0),w=A(()=>{const L=[];if(!o.value||!l.value)return L;const W=new Date(o.value);W.setDate(1);do L.push(new Date(W)),W.setMonth(W.getMonth()+1);while(Vt(W,l.value)!==1);return L}),b=A(()=>{if(f.value){if(e.type==="range")return!f.value[0]||!f.value[1];if(e.type==="multiple")return!f.value.length}return!f.value}),C=()=>f.value,T=()=>{const L=vt(d.value),W=L+s,te=w.value.map((X,oe)=>y.value[oe].getHeight()),z=te.reduce((X,oe)=>X+oe,0);if(W>z&&L>0)return;let K=0,P;const Z=[-1,-1];for(let X=0;X<w.value.length;X++){const oe=y.value[X];K<=W&&K+te[X]>=L&&(Z[1]=X,P||(P=oe,Z[0]=X),y.value[X].showed||(y.value[X].showed=!0,t("monthShow",{date:oe.date,title:oe.getTitle()}))),K+=te[X]}w.value.forEach((X,oe)=>{const fe=oe>=Z[0]-1&&oe<=Z[1]+1;y.value[oe].setVisible(fe)}),P&&(h.value=P)},m=L=>{a.value?v.value=L:De(()=>{w.value.some((W,te)=>Vt(W,L)===0?(d.value&&y.value[te].scrollToDate(d.value,L),!0):!1),T()})},$=()=>{if(!(e.poppable&&!e.show))if(f.value){const L=e.type==="single"?f.value:f.value[0];In(L)&&m(L)}else a.value||De(T)},I=()=>{e.poppable&&!e.show||(a.value||De(()=>{s=Math.floor(ce(d).height)}),$())},x=(L=r())=>{f.value=L,$()},E=L=>{const{maxRange:W,rangePrompt:te,showRangePrompt:z}=e;return W&&Sd(L)>+W?(z&&ra(te||dt("rangePrompt",W)),t("overRange"),!1):!0},D=L=>{v.value=L,t("panelChange",{date:L})},k=()=>{var L;return t("confirm",(L=f.value)!=null?L:fl(f.value))},B=(L,W)=>{const te=z=>{f.value=z,t("select",fl(z))};if(W&&e.type==="range"&&!E(L)){te([L[0],Po(L[0],+e.maxRange-1)]);return}te(L),W&&!e.showConfirm&&k()},p=(L,W,te)=>{var z;return(z=L.find(K=>Re(W,K.date)===-1&&Re(K.date,te)===-1))==null?void 0:z.date},Y=A(()=>y.value.reduce((L,W)=>{var te,z;return L.push(...(z=(te=W.disabledDays)==null?void 0:te.value)!=null?z:[]),L},[])),q=L=>{if(e.readonly||!L.date)return;const{date:W}=L,{type:te}=e;if(te==="range"){if(!f.value){B([W]);return}const[z,K]=f.value;if(z&&!K){const P=Re(W,z);if(P===1){const Z=p(Y.value,z,W);if(Z){const X=oo(Z);Re(z,X)===-1?B([z,X]):B([W])}else B([z,W],!0)}else P===-1?B([W]):e.allowSameDay&&B([W,W],!0)}else B([W])}else if(te==="multiple"){if(!f.value){B([W]);return}const z=f.value,K=z.findIndex(P=>Re(P,W)===0);if(K!==-1){const[P]=z.splice(K,1);t("unselect",rn(P))}else e.maxRange&&z.length>=+e.maxRange?ra(e.rangePrompt||dt("rangePrompt",e.maxRange)):B([...z,W])}else B(W,!0)},F=L=>t("clickOverlay",L),Q=L=>t("update:show",L),ee=(L,W)=>{const te=W!==0||!e.showSubtitle;return i(Dd,ie({ref:a.value?h:S(W),date:L,currentDate:f.value,showMonthTitle:te,firstDayOfWeek:g.value,lazyRender:a.value?!1:e.lazyRender,maxDate:l.value,minDate:o.value},ue(e,["type","color","showMark","formatter","rowHeight","showSubtitle","allowSameDay"]),{onClick:q,onClickDisabledDate:z=>t("clickDisabledDate",z)}),ue(n,["top-info","bottom-info","month-title","text"]))},de=()=>{if(n.footer)return n.footer();if(e.showConfirm){const L=n["confirm-text"],W=b.value,te=W?e.confirmDisabledText:e.confirmText;return i(Pe,{round:!0,block:!0,type:"primary",color:e.color,class:be("confirm"),disabled:W,nativeType:"button",onClick:k},{default:()=>[L?L({disabled:W}):te||dt("confirm")]})}},he=()=>i("div",{class:[be("footer"),{"van-safe-area-bottom":e.safeAreaInsetBottom}]},[de()]),G=()=>{var L,W;return i("div",{class:be()},[i(Pd,{date:(L=h.value)==null?void 0:L.date,maxDate:l.value,minDate:o.value,title:e.title,subtitle:(W=h.value)==null?void 0:W.getTitle(),showTitle:e.showTitle,showSubtitle:e.showSubtitle,switchMode:e.switchMode,firstDayOfWeek:g.value,onClickSubtitle:te=>t("clickSubtitle",te),onPanelChange:D},ue(n,["title","subtitle","prev-month","prev-year","next-month","next-year"])),i("div",{ref:d,class:be("body"),onScroll:a.value?void 0:T},[a.value?ee(v.value,0):w.value.map(ee)]),he()])};return j(()=>e.show,I),j(()=>[e.type,e.minDate,e.maxDate,e.switchMode],()=>x(r(f.value))),j(()=>e.defaultDate,L=>{x(L)}),re({reset:x,scrollToDate:m,getSelectedDate:C}),un(I),()=>e.poppable?i(Ze,{show:e.show,class:be("popup"),round:e.round,position:e.position,closeable:e.showTitle||e.showSubtitle,teleport:e.teleport,closeOnPopstate:e.closeOnPopstate,safeAreaInsetTop:e.safeAreaInsetTop,closeOnClickOverlay:e.closeOnClickOverlay,onClickOverlay:F,"onUpdate:show":Q},{default:G}):G()}});const pd=N(Ad),[Rd,qt]=V("image"),_d={src:String,alt:String,fit:String,position:String,round:Boolean,block:Boolean,width:M,height:M,radius:M,lazyLoad:Boolean,iconSize:M,showError:R,errorIcon:H("photo-fail"),iconPrefix:String,showLoading:R,loadingIcon:H("photo"),crossorigin:String,referrerpolicy:String};var Vd=_({name:Rd,props:_d,emits:["load","error"],setup(e,{emit:t,slots:n}){const a=O(!1),o=O(!0),l=O(),{$Lazyload:c}=ot().proxy,r=A(()=>{const g={width:ne(e.width),height:ne(e.height)};return se(e.radius)&&(g.overflow="hidden",g.borderRadius=ne(e.radius)),g});j(()=>e.src,()=>{a.value=!1,o.value=!0});const u=g=>{o.value&&(o.value=!1,t("load",g))},s=()=>{const g=new Event("load");Object.defineProperty(g,"target",{value:l.value,enumerable:!0}),u(g)},d=g=>{a.value=!0,o.value=!1,t("error",g)},f=(g,w,b)=>b?b():i(ae,{name:g,size:e.iconSize,class:w,classPrefix:e.iconPrefix},null),v=()=>{if(o.value&&e.showLoading)return i("div",{class:qt("loading")},[f(e.loadingIcon,qt("loading-icon"),n.loading)]);if(a.value&&e.showError)return i("div",{class:qt("error")},[f(e.errorIcon,qt("error-icon"),n.error)])},h=()=>{if(a.value||!e.src)return;const g={alt:e.alt,class:qt("img"),style:{objectFit:e.fit,objectPosition:e.position},crossorigin:e.crossorigin,referrerpolicy:e.referrerpolicy};return e.lazyLoad?$e(i("img",ie({ref:l},g),null),[[Nr("lazy"),e.src]]):i("img",ie({ref:l,src:e.src,onLoad:u,onError:d},g),null)},y=({el:g})=>{const w=()=>{g===l.value&&o.value&&s()};l.value?w():le(w)},S=({el:g})=>{g===l.value&&!a.value&&d()};return c&&_e&&(c.$on("loaded",y),c.$on("error",S),Dt(()=>{c.$off("loaded",y),c.$off("error",S)})),we(()=>{le(()=>{var g;(g=l.value)!=null&&g.complete&&!e.lazyLoad&&s()})}),()=>{var g;return i("div",{class:qt({round:e.round,block:e.block}),style:r.value},[h(),v(),(g=n.default)==null?void 0:g.call(n)])}}});const Ia=N(Vd),[Ld,pe]=V("card"),Md={tag:String,num:M,desc:String,thumb:String,title:String,price:M,centered:Boolean,lazyLoad:Boolean,currency:H("¥"),thumbLink:String,originPrice:M};var zd=_({name:Ld,props:Md,emits:["clickThumb"],setup(e,{slots:t,emit:n}){const a=()=>{if(t.title)return t.title();if(e.title)return i("div",{class:[pe("title"),"van-multi-ellipsis--l2"]},[e.title])},o=()=>{if(t.tag||e.tag)return i("div",{class:pe("tag")},[t.tag?t.tag():i(Ba,{mark:!0,type:"primary"},{default:()=>[e.tag]})])},l=()=>t.thumb?t.thumb():i(Ia,{src:e.thumb,fit:"cover",width:"100%",height:"100%",lazyLoad:e.lazyLoad},null),c=()=>{if(t.thumb||e.thumb)return i("a",{href:e.thumbLink,class:pe("thumb"),onClick:s=>n("clickThumb",s)},[l(),o()])},r=()=>{if(t.desc)return t.desc();if(e.desc)return i("div",{class:[pe("desc"),"van-ellipsis"]},[e.desc])},u=()=>{const s=e.price.toString().split(".");return i("div",null,[i("span",{class:pe("price-currency")},[e.currency]),i("span",{class:pe("price-integer")},[s[0]]),s.length>1&&i(ft,null,[Gl("."),i("span",{class:pe("price-decimal")},[s[1]])])])};return()=>{var s,d,f;const v=t.num||se(e.num),h=t.price||se(e.price),y=t["origin-price"]||se(e.originPrice),S=v||h||y||t.bottom,g=h&&i("div",{class:pe("price")},[t.price?t.price():u()]),w=y&&i("div",{class:pe("origin-price")},[t["origin-price"]?t["origin-price"]():"".concat(e.currency," ").concat(e.originPrice)]),b=v&&i("div",{class:pe("num")},[t.num?t.num():"x".concat(e.num)]),C=t.footer&&i("div",{class:pe("footer")},[t.footer()]),T=S&&i("div",{class:pe("bottom")},[(s=t["price-top"])==null?void 0:s.call(t),g,w,b,(d=t.bottom)==null?void 0:d.call(t)]);return i("div",{class:pe()},[i("div",{class:pe("header")},[c(),i("div",{class:pe("content",{centered:e.centered})},[i("div",null,[a(),r(),(f=t.tags)==null?void 0:f.call(t)]),T])]),C])}}});const Fd=N(zd),[Nd,it,Hd]=V("cascader"),Wd={title:String,options:me(),closeable:R,swipeable:R,closeIcon:H("cross"),showHeader:R,modelValue:M,fieldNames:Object,placeholder:String,activeColor:String};var jd=_({name:Nd,props:Wd,emits:["close","change","finish","clickTab","update:modelValue"],setup(e,{slots:t,emit:n}){const a=O([]),o=O(0),[l,c]=pn(),{text:r,value:u,children:s}=J({text:"text",value:"value",children:"children"},e.fieldNames),d=(m,$)=>{for(const I of m){if(I[u]===$)return[I];if(I[s]){const x=d(I[s],$);if(x)return[I,...x]}}},f=()=>{const{options:m,modelValue:$}=e;if($!==void 0){const I=d(m,$);if(I){let x=m;a.value=I.map(E=>{const D={options:x,selected:E},k=x.find(B=>B[u]===E[u]);return k&&(x=k[s]),D}),x&&a.value.push({options:x,selected:null}),le(()=>{o.value=a.value.length-1});return}}a.value=[{options:m,selected:null}]},v=(m,$)=>{if(m.disabled)return;if(a.value[$].selected=m,a.value.length>$+1&&(a.value=a.value.slice(0,$+1)),m[s]){const E={options:m[s],selected:null};a.value[$+1]?a.value[$+1]=E:a.value.push(E),le(()=>{o.value++})}const I=a.value.map(E=>E.selected).filter(Boolean);n("update:modelValue",m[u]);const x={value:m[u],tabIndex:$,selectedOptions:I};n("change",x),m[s]||n("finish",x)},h=()=>n("close"),y=({name:m,title:$})=>n("clickTab",m,$),S=()=>e.showHeader?i("div",{class:it("header")},[i("h2",{class:it("title")},[t.title?t.title():e.title]),e.closeable?i(ae,{name:e.closeIcon,class:[it("close-icon"),Ee],onClick:h},null):null]):null,g=(m,$,I)=>{const{disabled:x}=m,E=!!($&&m[u]===$[u]),D=m.color||(E?e.activeColor:void 0),k=t.option?t.option({option:m,selected:E}):i("span",null,[m[r]]);return i("li",{ref:E?c(I):void 0,role:"menuitemradio",class:[it("option",{selected:E,disabled:x}),m.className],style:{color:D},tabindex:x?void 0:E?0:-1,"aria-checked":E,"aria-disabled":x||void 0,onClick:()=>v(m,I)},[k,E?i(ae,{name:"success",class:it("selected-icon")},null):null])},w=(m,$,I)=>i("ul",{role:"menu",class:it("options")},[m.map(x=>g(x,$,I))]),b=(m,$)=>{const{options:I,selected:x}=m,E=e.placeholder||Hd("select"),D=x?x[r]:E;return i(En,{title:D,titleClass:it("tab",{unselected:!x})},{default:()=>{var k,B;return[(k=t["options-top"])==null?void 0:k.call(t,{tabIndex:$}),w(I,x,$),(B=t["options-bottom"])==null?void 0:B.call(t,{tabIndex:$})]}})},C=()=>i(xa,{active:o.value,"onUpdate:active":m=>o.value=m,shrink:!0,animated:!0,class:it("tabs"),color:e.activeColor,swipeable:e.swipeable,onClickTab:y},{default:()=>[a.value.map(b)]}),T=m=>{const $=m.parentElement;$&&($.scrollTop=m.offsetTop-($.offsetHeight-m.offsetHeight)/2)};return f(),j(o,m=>{const $=l.value[m];$&&T($)}),j(()=>e.options,f,{deep:!0}),j(()=>e.modelValue,m=>{m!==void 0&&a.value.map(I=>{var x;return(x=I.selected)==null?void 0:x[u]}).includes(m)||f()}),()=>i("div",{class:it()},[S(),C()])}});const Yd=N(jd),[Ud,bl]=V("cell-group"),Xd={title:String,inset:Boolean,border:R};var qd=_({name:Ud,inheritAttrs:!1,props:Xd,setup(e,{slots:t,attrs:n}){const a=()=>{var l;return i("div",ie({class:[bl({inset:e.inset}),{[ga]:e.border&&!e.inset}]},n,ia()),[(l=t.default)==null?void 0:l.call(t)])},o=()=>i("div",{class:bl("title",{inset:e.inset})},[t.title?t.title():e.title]);return()=>e.title||t.title?i(ft,null,[o(),a()]):a()}});const Gd=N(qd),[Kd,Hn]=V("circle");let Zd=0;const yl=e=>Math.min(Math.max(+e,0),100);function Jd(e,t){const n=e?1:0;return"M ".concat(t/2," ").concat(t/2," m 0, -500 a 500, 500 0 1, ").concat(n," 0, 1000 a 500, 500 0 1, ").concat(n," 0, -1000")}const Qd={text:String,size:M,fill:H("none"),rate:U(100),speed:U(0),color:[String,Object],clockwise:R,layerColor:String,currentRate:Ce(0),strokeWidth:U(40),strokeLinecap:String,startPosition:H("top")};var ef=_({name:Kd,props:Qd,emits:["update:currentRate"],setup(e,{emit:t,slots:n}){const a="van-circle-".concat(Zd++),o=A(()=>+e.strokeWidth+1e3),l=A(()=>Jd(e.clockwise,o.value)),c=A(()=>{const v={top:0,right:90,bottom:180,left:270}[e.startPosition];if(v)return{transform:"rotate(".concat(v,"deg)")}});j(()=>e.rate,f=>{let v;const h=Date.now(),y=e.currentRate,S=yl(f),g=Math.abs((y-S)*1e3/+e.speed),w=()=>{const b=Date.now(),T=Math.min((b-h)/g,1)*(S-y)+y;t("update:currentRate",yl(parseFloat(T.toFixed(1)))),(S>y?T<S:T>S)&&(v=De(w))};e.speed?(v&&va(v),v=De(w)):t("update:currentRate",S)},{immediate:!0});const r=()=>{const{strokeWidth:v,currentRate:h,strokeLinecap:y}=e,S=3140*h/100,g=Le(e.color)?"url(#".concat(a,")"):e.color,w={stroke:g,strokeWidth:"".concat(+v+1,"px"),strokeLinecap:y,strokeDasharray:"".concat(S,"px 3140px")};return i("path",{d:l.value,style:w,class:Hn("hover"),stroke:g},null)},u=()=>{const f={fill:e.fill,stroke:e.layerColor,strokeWidth:"".concat(e.strokeWidth,"px")};return i("path",{class:Hn("layer"),style:f,d:l.value},null)},s=()=>{const{color:f}=e;if(!Le(f))return;const v=Object.keys(f).sort((h,y)=>parseFloat(h)-parseFloat(y)).map((h,y)=>i("stop",{key:y,offset:h,"stop-color":f[h]},null));return i("defs",null,[i("linearGradient",{id:a,x1:"100%",y1:"0%",x2:"0%",y2:"0%"},[v])])},d=()=>{if(n.default)return n.default();if(e.text)return i("div",{class:Hn("text")},[e.text])};return()=>i("div",{class:Hn(),style:yt(e.size)},[i("svg",{viewBox:"0 0 ".concat(o.value," ").concat(o.value),style:c.value},[s(),u(),r()]),d()])}});const tf=N(ef),[Ji,nf]=V("row"),Qi=Symbol(Ji),af={tag:H("div"),wrap:R,align:String,gutter:{type:[String,Number,Array],default:0},justify:String};var of=_({name:Ji,props:af,setup(e,{slots:t}){const{children:n,linkChildren:a}=Oe(Qi),o=A(()=>{const r=[[]];let u=0;return n.forEach((s,d)=>{u+=Number(s.span),u>24?(r.push([d]),u-=24):r[r.length-1].push(d)}),r}),l=A(()=>{let r=0;Array.isArray(e.gutter)?r=Number(e.gutter[0])||0:r=Number(e.gutter);const u=[];return r&&o.value.forEach(s=>{const d=r*(s.length-1)/s.length;s.forEach((f,v)=>{if(v===0)u.push({right:d});else{const h=r-u[f-1].right,y=d-h;u.push({left:h,right:y})}})}),u}),c=A(()=>{const{gutter:r}=e,u=[];if(Array.isArray(r)&&r.length>1){const s=Number(r[1])||0;if(s<=0)return u;o.value.forEach((d,f)=>{f!==o.value.length-1&&d.forEach(()=>{u.push({bottom:s})})})}return u});return a({spaces:l,verticalSpaces:c}),()=>{const{tag:r,wrap:u,align:s,justify:d}=e;return i(r,{class:nf({["align-".concat(s)]:s,["justify-".concat(d)]:d,nowrap:!u})},{default:()=>{var f;return[(f=t.default)==null?void 0:f.call(t)]}})}}});const[lf,rf]=V("col"),sf={tag:H("div"),span:U(0),offset:M};var cf=_({name:lf,props:sf,setup(e,{slots:t}){const{parent:n,index:a}=Ie(Qi),o=A(()=>{if(!n)return;const{spaces:l,verticalSpaces:c}=n;let r={};if(l&&l.value&&l.value[a.value]){const{left:s,right:d}=l.value[a.value];r={paddingLeft:s?"".concat(s,"px"):null,paddingRight:d?"".concat(d,"px"):null}}const{bottom:u}=c.value[a.value]||{};return J(r,{marginBottom:u?"".concat(u,"px"):null})});return()=>{const{tag:l,span:c,offset:r}=e;return i(l,{style:o.value,class:rf({[c]:c,["offset-".concat(r)]:r})},{default:()=>{var u;return[(u=t.default)==null?void 0:u.call(t)]}})}}});const uf=N(cf),[er,df]=V("collapse"),tr=Symbol(er),ff={border:R,accordion:Boolean,modelValue:{type:[String,Number,Array],default:""}};var vf=_({name:er,props:ff,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{linkChildren:a,children:o}=Oe(tr),l=s=>{t("change",s),t("update:modelValue",s)},c=(s,d)=>{const{accordion:f,modelValue:v}=e;l(f?s===v?"":s:d?v.concat(s):v.filter(h=>h!==s))},r=(s={})=>{if(e.accordion)return;typeof s=="boolean"&&(s={expanded:s});const{expanded:d,skipDisabled:f}=s,h=o.filter(y=>y.disabled&&f?y.expanded.value:d!=null?d:!y.expanded.value).map(y=>y.itemName.value);l(h)},u=s=>{const{accordion:d,modelValue:f}=e;return d?f===s:f.includes(s)};return re({toggleAll:r}),a({toggle:c,isExpanded:u}),()=>{var s;return i("div",{class:[df(),{[ga]:e.border}]},[(s=n.default)==null?void 0:s.call(n)])}}});const hf=N(vf),[mf,Wn]=V("collapse-item"),gf=["icon","title","value","label","right-icon"],bf=J({},ka,{name:M,isLink:R,disabled:Boolean,readonly:Boolean,lazyRender:R});var yf=_({name:mf,props:bf,setup(e,{slots:t}){const n=O(),a=O(),{parent:o,index:l}=Ie(tr);if(!o)return;const c=A(()=>{var S;return(S=e.name)!=null?S:l.value}),r=A(()=>o.isExpanded(c.value)),u=O(r.value),s=wo(()=>u.value||!e.lazyRender),d=()=>{r.value?n.value&&(n.value.style.height=""):u.value=!1};j(r,(S,g)=>{if(g===null)return;S&&(u.value=!0),(S?le:De)(()=>{if(!a.value||!n.value)return;const{offsetHeight:b}=a.value;if(b){const C="".concat(b,"px");n.value.style.height=S?"0":C,Bt(()=>{n.value&&(n.value.style.height=S?C:"0")})}else d()})});const f=(S=!r.value)=>{o.toggle(c.value,S)},v=()=>{!e.disabled&&!e.readonly&&f()},h=()=>{const{border:S,disabled:g,readonly:w}=e,b=ue(e,Object.keys(ka));return w&&(b.isLink=!1),(g||w)&&(b.clickable=!1),i(Je,ie({role:"button",class:Wn("title",{disabled:g,expanded:r.value,borderless:!S}),"aria-expanded":String(r.value),onClick:v},b),ue(t,gf))},y=s(()=>{var S;return $e(i("div",{ref:n,class:Wn("wrapper"),onTransitionend:d},[i("div",{ref:a,class:Wn("content")},[(S=t.default)==null?void 0:S.call(t)])]),[[Be,u.value]])});return re({toggle:f,expanded:r,itemName:c}),()=>i("div",{class:[Wn({border:l.value&&e.border})]},[h(),y()])}});const wf=N(yf),xf=N(As),[Sf,wl,za]=V("contact-card"),Cf={tel:String,name:String,type:H("add"),addText:String,editable:R};var kf=_({name:Sf,props:Cf,emits:["click"],setup(e,{emit:t}){const n=o=>{e.editable&&t("click",o)},a=()=>e.type==="add"?e.addText||za("addContact"):[i("div",null,["".concat(za("name"),"：").concat(e.name)]),i("div",null,["".concat(za("tel"),"：").concat(e.tel)])];return()=>i(Je,{center:!0,icon:e.type==="edit"?"contact":"add-square",class:wl([e.type]),border:!1,isLink:e.editable,titleClass:wl("title"),onClick:n},{title:a})}});const Tf=N(kf),[$f,Gt,Ct]=V("contact-edit"),io={tel:"",name:""},Bf={isEdit:Boolean,isSaving:Boolean,isDeleting:Boolean,showSetDefault:Boolean,setDefaultLabel:String,contactInfo:{type:Object,default:()=>J({},io)},telValidator:{type:Function,default:Zl}};var If=_({name:$f,props:Bf,emits:["save","delete","changeDefault"],setup(e,{emit:t}){const n=xe(J({},io,e.contactInfo)),a=()=>{e.isSaving||t("save",n)},o=()=>t("delete",n),l=()=>i("div",{class:Gt("buttons")},[i(Pe,{block:!0,round:!0,type:"primary",text:Ct("save"),class:Gt("button"),loading:e.isSaving,nativeType:"submit"},null),e.isEdit&&i(Pe,{block:!0,round:!0,text:Ct("delete"),class:Gt("button"),loading:e.isDeleting,onClick:o},null)]),c=()=>i($o,{modelValue:n.isDefault,"onUpdate:modelValue":u=>n.isDefault=u,onChange:u=>t("changeDefault",u)},null),r=()=>{if(e.showSetDefault)return i(Je,{title:e.setDefaultLabel,class:Gt("switch-cell"),border:!1},{"right-icon":c})};return j(()=>e.contactInfo,u=>J(n,io,u)),()=>i(ko,{class:Gt(),onSubmit:a},{default:()=>[i("div",{class:Gt("fields")},[i(ut,{modelValue:n.name,"onUpdate:modelValue":u=>n.name=u,clearable:!0,label:Ct("name"),rules:[{required:!0,message:Ct("nameEmpty")}],maxlength:"30",placeholder:Ct("name")},null),i(ut,{modelValue:n.tel,"onUpdate:modelValue":u=>n.tel=u,clearable:!0,type:"tel",label:Ct("tel"),rules:[{validator:e.telValidator,message:Ct("telInvalid")}],placeholder:Ct("tel")},null)]),r(),l()]})}});const Df=N(If),[Ef,rt,Pf]=V("contact-list"),Of={list:Array,addText:String,modelValue:ge,defaultTagText:String};var Af=_({name:Ef,props:Of,emits:["add","edit","select","update:modelValue"],setup(e,{emit:t}){const n=(a,o)=>{const l=()=>{t("update:modelValue",a.id),t("select",a,o)},c=()=>i(Do,{class:rt("radio"),name:a.id,iconSize:18},null),r=()=>i(ae,{name:"edit",class:rt("edit"),onClick:s=>{s.stopPropagation(),t("edit",a,o)}},null),u=()=>{const s=["".concat(a.name,"，").concat(a.tel)];return a.isDefault&&e.defaultTagText&&s.push(i(Ba,{type:"primary",round:!0,class:rt("item-tag")},{default:()=>[e.defaultTagText]})),s};return i(Je,{key:a.id,isLink:!0,center:!0,class:rt("item"),titleClass:rt("item-title"),onClick:l},{icon:r,title:u,"right-icon":c})};return()=>i("div",{class:rt()},[i(Bo,{modelValue:e.modelValue,class:rt("group")},{default:()=>[e.list&&e.list.map(n)]}),i("div",{class:[rt("bottom"),"van-safe-area-bottom"]},[i(Pe,{round:!0,block:!0,type:"primary",class:rt("add"),text:e.addText||Pf("addContact"),onClick:()=>t("add")},null)])])}});const pf=N(Af);function Rf(e,t){const{days:n}=t;let{hours:a,minutes:o,seconds:l,milliseconds:c}=t;if(e.includes("DD")?e=e.replace("DD",Xe(n)):a+=n*24,e.includes("HH")?e=e.replace("HH",Xe(a)):o+=a*60,e.includes("mm")?e=e.replace("mm",Xe(o)):l+=o*60,e.includes("ss")?e=e.replace("ss",Xe(l)):c+=l*1e3,e.includes("S")){const r=Xe(c,3);e.includes("SSS")?e=e.replace("SSS",r):e.includes("SS")?e=e.replace("SS",r.slice(0,2)):e=e.replace("S",r.charAt(0))}return e}const[_f,Vf]=V("count-down"),Lf={time:U(0),format:H("HH:mm:ss"),autoStart:R,millisecond:Boolean};var Mf=_({name:_f,props:Lf,emits:["change","finish"],setup(e,{emit:t,slots:n}){const{start:a,pause:o,reset:l,current:c}=Qr({time:+e.time,millisecond:e.millisecond,onChange:s=>t("change",s),onFinish:()=>t("finish")}),r=A(()=>Rf(e.format,c.value)),u=()=>{l(+e.time),e.autoStart&&a()};return j(()=>e.time,u,{immediate:!0}),re({start:a,pause:o,reset:u}),()=>i("div",{role:"timer",class:Vf()},[n.default?n.default(c.value):r.value])}});const zf=N(Mf);function xl(e){const t=new Date(e*1e3);return"".concat(t.getFullYear(),".").concat(Xe(t.getMonth()+1),".").concat(Xe(t.getDate()))}const Ff=e=>(e/10).toFixed(e%10===0?0:1),Sl=e=>(e/100).toFixed(e%100===0?0:e%10===0?1:2),[Nf,tt,Fa]=V("coupon");var Hf=_({name:Nf,props:{chosen:Boolean,coupon:Te(Object),disabled:Boolean,currency:H("¥")},setup(e){const t=A(()=>{const{startAt:o,endAt:l}=e.coupon;return"".concat(xl(o)," - ").concat(xl(l))}),n=A(()=>{const{coupon:o,currency:l}=e;if(o.valueDesc)return[o.valueDesc,i("span",null,[o.unitDesc||""])];if(o.denominations){const c=Sl(o.denominations);return[i("span",null,[l])," ".concat(c)]}return o.discount?Fa("discount",Ff(o.discount)):""}),a=A(()=>{const o=Sl(e.coupon.originCondition||0);return o==="0"?Fa("unlimited"):Fa("condition",o)});return()=>{const{chosen:o,coupon:l,disabled:c}=e,r=c&&l.reason||l.description;return i("div",{class:tt({disabled:c})},[i("div",{class:tt("content")},[i("div",{class:tt("head")},[i("h2",{class:tt("amount")},[n.value]),i("p",{class:tt("condition")},[l.condition||a.value])]),i("div",{class:tt("body")},[i("p",{class:tt("name")},[l.name]),i("p",{class:tt("valid")},[t.value]),!c&&i(Eo,{class:tt("corner"),modelValue:o},null)])]),r&&i("p",{class:tt("description")},[r])])}}});const ro=N(Hf),[Wf,Cl,so]=V("coupon-cell"),jf={title:String,border:R,editable:R,coupons:me(),currency:H("¥"),chosenCoupon:{type:[Number,Array],default:-1}},Yf=e=>{const{value:t,denominations:n}=e;return se(t)?t:se(n)?n:0};function Uf({coupons:e,chosenCoupon:t,currency:n}){let a=0,o=!1;return(Array.isArray(t)?t:[t]).forEach(l=>{const c=e[+l];c&&(o=!0,a+=Yf(c))}),o?"-".concat(n," ").concat((a/100).toFixed(2)):e.length===0?so("noCoupon"):so("count",e.length)}var Xf=_({name:Wf,props:jf,setup(e){return()=>{const t=Array.isArray(e.chosenCoupon)?e.chosenCoupon.length:e.coupons[+e.chosenCoupon];return i(Je,{class:Cl(),value:Uf(e),title:e.title||so("title"),border:e.border,isLink:e.editable,valueClass:Cl("value",{selected:t})},null)}}});const qf=N(Xf),[Gf,jn]=V("empty"),Kf={image:H("default"),imageSize:[Number,String,Array],description:String};var Zf=_({name:Gf,props:Kf,setup(e,{slots:t}){const n=()=>{const w=t.description?t.description():e.description;if(w)return i("p",{class:jn("description")},[w])},a=()=>{if(t.default)return i("div",{class:jn("bottom")},[t.default()])},o=vn(),l=w=>"".concat(o,"-").concat(w),c=w=>"url(#".concat(l(w),")"),r=(w,b,C)=>i("stop",{"stop-color":w,offset:"".concat(b,"%"),"stop-opacity":C},null),u=(w,b)=>[r(w,0),r(b,100)],s=w=>[i("defs",null,[i("radialGradient",{id:l(w),cx:"50%",cy:"54%",fx:"50%",fy:"54%",r:"297%",gradientTransform:"matrix(-.16 0 0 -.33 .58 .72)","data-allow-mismatch":"attribute"},[r("#EBEDF0",0),r("#F2F3F5",100,.3)])]),i("ellipse",{fill:c(w),opacity:".8",cx:"80",cy:"140",rx:"46",ry:"8","data-allow-mismatch":"attribute"},null)],d=()=>[i("defs",null,[i("linearGradient",{id:l("a"),x1:"64%",y1:"100%",x2:"64%","data-allow-mismatch":"attribute"},[r("#FFF",0,.5),r("#F2F3F5",100)])]),i("g",{opacity:".8","data-allow-mismatch":"children"},[i("path",{d:"M36 131V53H16v20H2v58h34z",fill:c("a")},null),i("path",{d:"M123 15h22v14h9v77h-31V15z",fill:c("a")},null)])],f=()=>[i("defs",null,[i("linearGradient",{id:l("b"),x1:"64%",y1:"97%",x2:"64%",y2:"0%","data-allow-mismatch":"attribute"},[r("#F2F3F5",0,.3),r("#F2F3F5",100)])]),i("g",{opacity:".8","data-allow-mismatch":"children"},[i("path",{d:"M87 6c3 0 7 3 8 6a8 8 0 1 1-1 16H80a7 7 0 0 1-8-6c0-4 3-7 6-7 0-5 4-9 9-9Z",fill:c("b")},null),i("path",{d:"M19 23c2 0 3 1 4 3 2 0 4 2 4 4a4 4 0 0 1-4 3v1h-7v-1l-1 1c-2 0-3-2-3-4 0-1 1-3 3-3 0-2 2-4 4-4Z",fill:c("b")},null)])],v=()=>i("svg",{viewBox:"0 0 160 160"},[i("defs",{"data-allow-mismatch":"children"},[i("linearGradient",{id:l(1),x1:"64%",y1:"100%",x2:"64%"},[r("#FFF",0,.5),r("#F2F3F5",100)]),i("linearGradient",{id:l(2),x1:"50%",x2:"50%",y2:"84%"},[r("#EBEDF0",0),r("#DCDEE0",100,0)]),i("linearGradient",{id:l(3),x1:"100%",x2:"100%",y2:"100%"},[u("#EAEDF0","#DCDEE0")]),i("radialGradient",{id:l(4),cx:"50%",cy:"0%",fx:"50%",fy:"0%",r:"100%",gradientTransform:"matrix(0 1 -.54 0 .5 -.5)"},[r("#EBEDF0",0),r("#FFF",100,0)])]),i("g",{fill:"none"},[d(),i("path",{fill:c(4),d:"M0 139h160v21H0z","data-allow-mismatch":"attribute"},null),i("path",{d:"M80 54a7 7 0 0 1 3 13v27l-2 2h-2a2 2 0 0 1-2-2V67a7 7 0 0 1 3-13z",fill:c(2),"data-allow-mismatch":"attribute"},null),i("g",{opacity:".6","stroke-linecap":"round","stroke-width":"7","data-allow-mismatch":"children"},[i("path",{d:"M64 47a19 19 0 0 0-5 13c0 5 2 10 5 13",stroke:c(3)},null),i("path",{d:"M53 36a34 34 0 0 0 0 48",stroke:c(3)},null),i("path",{d:"M95 73a19 19 0 0 0 6-13c0-5-2-9-6-13",stroke:c(3)},null),i("path",{d:"M106 84a34 34 0 0 0 0-48",stroke:c(3)},null)]),i("g",{transform:"translate(31 105)"},[i("rect",{fill:"#EBEDF0",width:"98",height:"34",rx:"2"},null),i("rect",{fill:"#FFF",x:"9",y:"8",width:"80",height:"18",rx:"1.1"},null),i("rect",{fill:"#EBEDF0",x:"15",y:"12",width:"18",height:"6",rx:"1.1"},null)])])]),h=()=>i("svg",{viewBox:"0 0 160 160"},[i("defs",{"data-allow-mismatch":"children"},[i("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:l(5)},[u("#F2F3F5","#DCDEE0")]),i("linearGradient",{x1:"95%",y1:"48%",x2:"5.5%",y2:"51%",id:l(6)},[u("#EAEDF1","#DCDEE0")]),i("linearGradient",{y1:"45%",x2:"100%",y2:"54%",id:l(7)},[u("#EAEDF1","#DCDEE0")])]),d(),f(),i("g",{transform:"translate(36 50)",fill:"none"},[i("g",{transform:"translate(8)"},[i("rect",{fill:"#EBEDF0",opacity:".6",x:"38",y:"13",width:"36",height:"53",rx:"2"},null),i("rect",{fill:c(5),width:"64",height:"66",rx:"2","data-allow-mismatch":"attribute"},null),i("rect",{fill:"#FFF",x:"6",y:"6",width:"52",height:"55",rx:"1"},null),i("g",{transform:"translate(15 17)",fill:c(6),"data-allow-mismatch":"attribute"},[i("rect",{width:"34",height:"6",rx:"1"},null),i("path",{d:"M0 14h34v6H0z"},null),i("rect",{y:"28",width:"34",height:"6",rx:"1"},null)])]),i("rect",{fill:c(7),y:"61",width:"88",height:"28",rx:"1","data-allow-mismatch":"attribute"},null),i("rect",{fill:"#F7F8FA",x:"29",y:"72",width:"30",height:"6",rx:"1"},null)])]),y=()=>i("svg",{viewBox:"0 0 160 160"},[i("defs",null,[i("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:l(8),"data-allow-mismatch":"attribute"},[u("#EAEDF1","#DCDEE0")])]),d(),f(),s("c"),i("path",{d:"m59 60 21 21 21-21h3l9 9v3L92 93l21 21v3l-9 9h-3l-21-21-21 21h-3l-9-9v-3l21-21-21-21v-3l9-9h3Z",fill:c(8),"data-allow-mismatch":"attribute"},null)]),S=()=>i("svg",{viewBox:"0 0 160 160"},[i("defs",{"data-allow-mismatch":"children"},[i("linearGradient",{x1:"50%",y1:"100%",x2:"50%",id:l(9)},[u("#EEE","#D8D8D8")]),i("linearGradient",{x1:"100%",y1:"50%",y2:"50%",id:l(10)},[u("#F2F3F5","#DCDEE0")]),i("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:l(11)},[u("#F2F3F5","#DCDEE0")]),i("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:l(12)},[u("#FFF","#F7F8FA")])]),d(),f(),s("d"),i("g",{transform:"rotate(-45 113 -4)",fill:"none","data-allow-mismatch":"children"},[i("rect",{fill:c(9),x:"24",y:"52.8",width:"5.8",height:"19",rx:"1"},null),i("rect",{fill:c(10),x:"22.1",y:"67.3",width:"9.9",height:"28",rx:"1"},null),i("circle",{stroke:c(11),"stroke-width":"8",cx:"27",cy:"27",r:"27"},null),i("circle",{fill:c(12),cx:"27",cy:"27",r:"16"},null),i("path",{d:"M37 7c-8 0-15 5-16 12",stroke:c(11),"stroke-width":"3",opacity:".5","stroke-linecap":"round",transform:"rotate(45 29 13)"},null)])]),g=()=>{var w;if(t.image)return t.image();const b={error:y,search:S,network:v,default:h};return((w=b[e.image])==null?void 0:w.call(b))||i("img",{src:e.image},null)};return()=>i("div",{class:jn()},[i("div",{class:jn("image"),style:yt(e.imageSize)},[g()]),n(),a()])}});const nr=N(Zf),[Jf,nt,Kt]=V("coupon-list"),Qf={code:H(""),coupons:me(),currency:H("¥"),showCount:R,emptyImage:String,enabledTitle:String,disabledTitle:String,disabledCoupons:me(),showExchangeBar:R,showCloseButton:R,closeButtonText:String,inputPlaceholder:String,exchangeMinLength:Ce(1),exchangeButtonText:String,displayedCouponIndex:Ce(-1),exchangeButtonLoading:Boolean,exchangeButtonDisabled:Boolean,chosenCoupon:{type:[Number,Array],default:-1}};var ev=_({name:Jf,props:Qf,emits:["change","exchange","update:code"],setup(e,{emit:t,slots:n}){const[a,o]=pn(),l=O(),c=O(),r=O(0),u=O(0),s=O(e.code),d=A(()=>!e.exchangeButtonLoading&&(e.exchangeButtonDisabled||!s.value||s.value.length<e.exchangeMinLength)),f=()=>{const C=ce(l).height,T=ce(c).height+44;u.value=(C>T?C:Ne.value)-T},v=()=>{t("exchange",s.value),e.code||(s.value="")},h=b=>{le(()=>{var C;return(C=a.value[b])==null?void 0:C.scrollIntoView()})},y=()=>i(nr,{image:e.emptyImage},{default:()=>[i("p",{class:nt("empty-tip")},[Kt("noCoupon")])]}),S=()=>{if(e.showExchangeBar)return i("div",{ref:c,class:nt("exchange-bar")},[i(ut,{modelValue:s.value,"onUpdate:modelValue":b=>s.value=b,clearable:!0,border:!1,class:nt("field"),placeholder:e.inputPlaceholder||Kt("placeholder"),maxlength:"20"},null),i(Pe,{plain:!0,type:"primary",class:nt("exchange"),text:e.exchangeButtonText||Kt("exchange"),loading:e.exchangeButtonLoading,disabled:d.value,onClick:v},null)])},g=()=>{const{coupons:b,chosenCoupon:C}=e,T=e.showCount?" (".concat(b.length,")"):"",m=(e.enabledTitle||Kt("enable"))+T,$=(I=[],x=0)=>I.includes(x)?I.filter(E=>E!==x):[...I,x];return i(En,{title:m},{default:()=>{var I;return[i("div",{class:nt("list",{"with-bottom":e.showCloseButton}),style:{height:"".concat(u.value,"px")}},[b.map((x,E)=>i(ro,{key:x.id,ref:o(E),coupon:x,chosen:Array.isArray(C)?C.includes(E):E===C,currency:e.currency,onClick:()=>t("change",Array.isArray(C)?$(C,E):E)},null)),!b.length&&y(),(I=n["list-footer"])==null?void 0:I.call(n)])]}})},w=()=>{const{disabledCoupons:b}=e,C=e.showCount?" (".concat(b.length,")"):"",T=(e.disabledTitle||Kt("disabled"))+C;return i(En,{title:T},{default:()=>{var m;return[i("div",{class:nt("list",{"with-bottom":e.showCloseButton}),style:{height:"".concat(u.value,"px")}},[b.map($=>i(ro,{disabled:!0,key:$.id,coupon:$,currency:e.currency},null)),!b.length&&y(),(m=n["disabled-list-footer"])==null?void 0:m.call(n)])]}})};return j(()=>e.code,b=>{s.value=b}),j(Ne,f),j(s,b=>t("update:code",b)),j(()=>e.displayedCouponIndex,h),we(()=>{f(),h(e.displayedCouponIndex)}),()=>i("div",{ref:l,class:nt()},[S(),i(xa,{active:r.value,"onUpdate:active":b=>r.value=b,class:nt("tab")},{default:()=>[g(),w()]}),i("div",{class:nt("bottom")},[n["list-button"]?n["list-button"]():$e(i(Pe,{round:!0,block:!0,type:"primary",class:nt("close"),text:e.closeButtonText||Kt("close"),onClick:()=>t("change",Array.isArray(e.chosenCoupon)?[]:-1)},null),[[Be,e.showCloseButton]])])])}});const tv=N(ev),kl=new Date().getFullYear(),[nv]=V("date-picker"),av=J({},qi,{columnsType:{type:Array,default:()=>["year","month","day"]},minDate:{type:Date,default:()=>new Date(kl-10,0,1),validator:In},maxDate:{type:Date,default:()=>new Date(kl+10,11,31),validator:In}});var ov=_({name:nv,props:av,emits:["confirm","cancel","change","update:modelValue"],setup(e,{emit:t,slots:n}){const a=O(e.modelValue),o=O(!1),l=O(),c=A(()=>o.value?e.modelValue:a.value),r=m=>m===e.minDate.getFullYear(),u=m=>m===e.maxDate.getFullYear(),s=m=>m===e.minDate.getMonth()+1,d=m=>m===e.maxDate.getMonth()+1,f=m=>{const{minDate:$,columnsType:I}=e,x=I.indexOf(m),E=c.value[x];if(E)return+E;switch(m){case"year":return $.getFullYear();case"month":return $.getMonth()+1;case"day":return $.getDate()}},v=()=>{const m=e.minDate.getFullYear(),$=e.maxDate.getFullYear();return nn(m,$,"year",e.formatter,e.filter,c.value)},h=()=>{const m=f("year"),$=r(m)?e.minDate.getMonth()+1:1,I=u(m)?e.maxDate.getMonth()+1:12;return nn($,I,"month",e.formatter,e.filter,c.value)},y=()=>{const m=f("year"),$=f("month"),I=r(m)&&s($)?e.minDate.getDate():1,x=u(m)&&d($)?e.maxDate.getDate():Ki(m,$);return nn(I,x,"day",e.formatter,e.filter,c.value)},S=()=>{var m;return(m=l.value)==null?void 0:m.confirm()},g=()=>a.value,w=A(()=>e.columnsType.map(m=>{switch(m){case"year":return v();case"month":return h();case"day":return y();default:return[]}}));j(a,m=>{at(m,e.modelValue)||t("update:modelValue",m)}),j(()=>e.modelValue,(m,$)=>{o.value=at($,a.value),m=Zi(m,w.value),at(m,a.value)||(a.value=m),o.value=!1},{immediate:!0});const b=(...m)=>t("change",...m),C=(...m)=>t("cancel",...m),T=(...m)=>t("confirm",...m);return re({confirm:S,getSelectedDate:g}),()=>i(Ca,ie({ref:l,modelValue:a.value,"onUpdate:modelValue":m=>a.value=m,columns:w.value,onChange:b,onCancel:C,onConfirm:T},ue(e,Gi)),n)}});const lv=N(ov),[iv,je,Yn]=V("dialog"),rv=J({},fn,{title:String,theme:String,width:M,message:[String,Function],callback:Function,allowHtml:Boolean,className:ge,transition:H("van-dialog-bounce"),messageAlign:String,closeOnPopstate:R,showCancelButton:Boolean,cancelButtonText:String,cancelButtonColor:String,cancelButtonDisabled:Boolean,confirmButtonText:String,confirmButtonColor:String,confirmButtonDisabled:Boolean,showConfirmButton:R,closeOnClickOverlay:Boolean,keyboardEnabled:R,destroyOnClose:Boolean}),sv=[...yo,"transition","closeOnPopstate","destroyOnClose"];var ar=_({name:iv,props:rv,emits:["confirm","cancel","keydown","update:show"],setup(e,{emit:t,slots:n}){const a=O(),o=xe({confirm:!1,cancel:!1}),l=w=>t("update:show",w),c=w=>{var b;l(!1),(b=e.callback)==null||b.call(e,w)},r=w=>()=>{e.show&&(t(w),e.beforeClose?(o[w]=!0,Pt(e.beforeClose,{args:[w],done(){c(w),o[w]=!1},canceled(){o[w]=!1}})):c(w))},u=r("cancel"),s=r("confirm"),d=Hr(w=>{var b,C;if(!e.keyboardEnabled||w.target!==((C=(b=a.value)==null?void 0:b.popupRef)==null?void 0:C.value))return;({Enter:e.showConfirmButton?s:Bn,Escape:e.showCancelButton?u:Bn})[w.key](),t("keydown",w)},["enter","esc"]),f=()=>{const w=n.title?n.title():e.title;if(w)return i("div",{class:je("header",{isolated:!e.message&&!n.default})},[w])},v=w=>{const{message:b,allowHtml:C,messageAlign:T}=e,m=je("message",{"has-title":w,[T]:T}),$=ln(b)?b():b;return C&&typeof $=="string"?i("div",{class:m,innerHTML:$},null):i("div",{class:m},[$])},h=()=>{if(n.default)return i("div",{class:je("content")},[n.default()]);const{title:w,message:b,allowHtml:C}=e;if(b){const T=!!(w||n.title);return i("div",{key:C?1:0,class:je("content",{isolated:!T})},[v(T)])}},y=()=>i("div",{class:[ii,je("footer")]},[e.showCancelButton&&i(Pe,{size:"large",text:e.cancelButtonText||Yn("cancel"),class:je("cancel"),style:{color:e.cancelButtonColor},loading:o.cancel,disabled:e.cancelButtonDisabled,onClick:u},null),e.showConfirmButton&&i(Pe,{size:"large",text:e.confirmButtonText||Yn("confirm"),class:[je("confirm"),{[ri]:e.showCancelButton}],style:{color:e.confirmButtonColor},loading:o.confirm,disabled:e.confirmButtonDisabled,onClick:s},null)]),S=()=>i(vi,{class:je("footer")},{default:()=>[e.showCancelButton&&i(to,{type:"warning",text:e.cancelButtonText||Yn("cancel"),class:je("cancel"),color:e.cancelButtonColor,loading:o.cancel,disabled:e.cancelButtonDisabled,onClick:u},null),e.showConfirmButton&&i(to,{type:"danger",text:e.confirmButtonText||Yn("confirm"),class:je("confirm"),color:e.confirmButtonColor,loading:o.confirm,disabled:e.confirmButtonDisabled,onClick:s},null)]}),g=()=>n.footer?n.footer():e.theme==="round-button"?S():y();return()=>{const{width:w,title:b,theme:C,message:T,className:m}=e;return i(Ze,ie({ref:a,role:"dialog",class:[je([C]),m],style:{width:ne(w)},tabindex:0,"aria-labelledby":b||T,onKeydown:d,"onUpdate:show":l},ue(e,sv)),{default:()=>[f(),h(),g()]})}}});let co;const cv={title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1,destroyOnClose:!1};let uv=J({},cv);function dv(){({instance:co}=$a({setup(){const{state:t,toggle:n}=Ta();return()=>i(ar,ie(t,{"onUpdate:show":n}),null)}}))}function fv(e){return _e?new Promise((t,n)=>{co||dv(),co.open(J({},uv,e,{callback:a=>{(a==="confirm"?t:n)(a)}}))}):Promise.resolve(void 0)}const _y=e=>fv(J({showCancelButton:!0},e)),vv=N(ar),[hv,mv]=V("divider"),gv={dashed:Boolean,hairline:R,vertical:Boolean,contentPosition:H("center")};var bv=_({name:hv,props:gv,setup(e,{slots:t}){return()=>{var n;return i("div",{role:"separator",class:mv({dashed:e.dashed,hairline:e.hairline,vertical:e.vertical,["content-".concat(e.contentPosition)]:!!t.default&&!e.vertical})},[!e.vertical&&((n=t.default)==null?void 0:n.call(t))])}}});const yv=N(bv),[or,Un]=V("dropdown-menu"),wv={overlay:R,zIndex:M,duration:U(.2),direction:H("down"),activeColor:String,autoLocate:Boolean,closeOnClickOutside:R,closeOnClickOverlay:R,swipeThreshold:M},lr=Symbol(or);var xv=_({name:or,props:wv,setup(e,{slots:t}){const n=vn(),a=O(),o=O(),l=O(0),{children:c,linkChildren:r}=Oe(lr),u=dn(a),s=A(()=>c.some(b=>b.state.showWrapper)),d=A(()=>e.swipeThreshold&&c.length>+e.swipeThreshold),f=A(()=>{if(s.value&&se(e.zIndex))return{zIndex:+e.zIndex+1}}),v=()=>{c.forEach(b=>{b.toggle(!1)})},h=()=>{e.closeOnClickOutside&&v()},y=()=>{if(o.value){const b=ce(o);e.direction==="down"?l.value=b.bottom:l.value=Ne.value-b.top}},S=()=>{s.value&&y()},g=b=>{c.forEach((C,T)=>{T===b?C.toggle():C.state.showPopup&&C.toggle(!1,{immediate:!0})})},w=(b,C)=>{const{showPopup:T}=b.state,{disabled:m,titleClass:$}=b;return i("div",{id:"".concat(n,"-").concat(C),role:"button",tabindex:m?void 0:0,"data-allow-mismatch":"attribute",class:[Un("item",{disabled:m,grow:d.value}),{[Ee]:!m}],onClick:()=>{m||g(C)}},[i("span",{class:[Un("title",{down:T===(e.direction==="down"),active:T}),$],style:{color:T?e.activeColor:""}},[i("div",{class:"van-ellipsis"},[b.renderTitle()])])])};return re({close:v}),r({id:n,props:e,offset:l,updateOffset:y}),ha(a,h),ye("scroll",S,{target:u,passive:!0}),()=>{var b;return i("div",{ref:a,class:Un()},[i("div",{ref:o,style:f.value,class:Un("bar",{opened:s.value,scrollable:d.value})},[c.map(w)]),(b=t.default)==null?void 0:b.call(t)])}}});const[Sv,Xn]=V("dropdown-item"),Cv={title:String,options:me(),disabled:Boolean,teleport:[String,Object],lazyRender:R,modelValue:ge,titleClass:ge};var kv=_({name:Sv,inheritAttrs:!1,props:Cv,emits:["open","opened","close","closed","change","update:modelValue"],setup(e,{emit:t,slots:n,attrs:a}){const o=xe({showPopup:!1,transition:!0,showWrapper:!1}),l=O(),{parent:c,index:r}=Ie(lr);if(!c)return;const u=b=>()=>t(b),s=u("open"),d=u("close"),f=u("opened"),v=()=>{o.showWrapper=!1,t("closed")},h=b=>{e.teleport&&b.stopPropagation()},y=(b=!o.showPopup,C={})=>{b!==o.showPopup&&(o.showPopup=b,o.transition=!C.immediate,b&&(c.updateOffset(),o.showWrapper=!0))},S=()=>{if(n.title)return n.title();if(e.title)return e.title;const b=e.options.find(C=>C.value===e.modelValue);return b?b.text:""},g=b=>{const{activeColor:C}=c.props,{disabled:T}=b,m=b.value===e.modelValue,$=()=>{T||(o.showPopup=!1,b.value!==e.modelValue&&(t("update:modelValue",b.value),t("change",b.value)))},I=()=>{if(m)return i(ae,{class:Xn("icon"),color:T?void 0:C,name:"success"},null)};return i(Je,{role:"menuitem",key:String(b.value),icon:b.icon,title:b.text,class:Xn("option",{active:m,disabled:T}),style:{color:m?C:""},tabindex:m?0:-1,clickable:!T,onClick:$},{value:I})},w=()=>{const{offset:b}=c,{autoLocate:C,zIndex:T,overlay:m,duration:$,direction:I,closeOnClickOverlay:x}=c.props,E=wt(T);let D=b.value;if(C&&l.value){const k=is(l.value);k&&(D-=ce(k).top)}return I==="down"?E.top="".concat(D,"px"):E.bottom="".concat(D,"px"),$e(i("div",ie({ref:l,style:E,class:Xn([I]),onClick:h},a),[i(Ze,{show:o.showPopup,"onUpdate:show":k=>o.showPopup=k,role:"menu",class:Xn("content"),overlay:m,position:I==="down"?"top":"bottom",duration:o.transition?$:0,lazyRender:e.lazyRender,overlayStyle:{position:"absolute"},"aria-labelledby":"".concat(c.id,"-").concat(r.value),"data-allow-mismatch":"attribute",closeOnClickOverlay:x,onOpen:s,onClose:d,onOpened:f,onClosed:v},{default:()=>{var k;return[e.options.map(g),(k=n.default)==null?void 0:k.call(n)]}})]),[[Be,o.showWrapper]])};return re({state:o,toggle:y,renderTitle:S}),()=>e.teleport?i(Ft,{to:e.teleport},{default:()=>[w()]}):w()}});const Tv=N(kv),$v=N(xv),Bv={gap:{type:[Number,Object],default:24},icon:String,axis:H("y"),magnetic:String,offset:{type:Object,default:()=>({x:-1,y:-1})},teleport:{type:[String,Object],default:"body"}},[Iv,Tl]=V("floating-bubble");var Dv=_({name:Iv,inheritAttrs:!1,props:Bv,emits:["click","update:offset","offsetChange"],setup(e,{slots:t,emit:n,attrs:a}){const o=O(),l=O({x:0,y:0,width:0,height:0}),c=A(()=>Le(e.gap)?e.gap.x:e.gap),r=A(()=>Le(e.gap)?e.gap.y:e.gap),u=A(()=>({top:r.value,right:qe.value-l.value.width-c.value,bottom:Ne.value-l.value.height-r.value,left:c.value})),s=O(!1);let d=!1;const f=A(()=>{const m={},$=ne(l.value.x),I=ne(l.value.y);return m.transform="translate3d(".concat($,", ").concat(I,", 0)"),(s.value||!d)&&(m.transition="none"),m}),v=()=>{if(!T.value)return;const{width:m,height:$}=ce(o.value),{offset:I}=e;l.value={x:I.x>-1?I.x:qe.value-m-c.value,y:I.y>-1?I.y:Ne.value-$-r.value,width:m,height:$}},h=He();let y=0,S=0;const g=m=>{h.start(m),s.value=!0,y=l.value.x,S=l.value.y};ye("touchmove",m=>{if(m.preventDefault(),h.move(m),e.axis!=="lock"&&!h.isTap.value){if(e.axis==="x"||e.axis==="xy"){let I=y+h.deltaX.value;I<u.value.left&&(I=u.value.left),I>u.value.right&&(I=u.value.right),l.value.x=I}if(e.axis==="y"||e.axis==="xy"){let I=S+h.deltaY.value;I<u.value.top&&(I=u.value.top),I>u.value.bottom&&(I=u.value.bottom),l.value.y=I}const $=ue(l.value,["x","y"]);n("update:offset",$)}},{target:o});const b=()=>{s.value=!1,le(()=>{if(e.magnetic==="x"){const m=la([u.value.left,u.value.right],l.value.x);l.value.x=m}if(e.magnetic==="y"){const m=la([u.value.top,u.value.bottom],l.value.y);l.value.y=m}if(!h.isTap.value){const m=ue(l.value,["x","y"]);n("update:offset",m),(y!==m.x||S!==m.y)&&n("offsetChange",m)}})},C=m=>{h.isTap.value?n("click",m):m.stopPropagation()};we(()=>{v(),le(()=>{d=!0})}),j([qe,Ne,c,r,()=>e.offset],v,{deep:!0});const T=O(!0);return gt(()=>{T.value=!0}),bt(()=>{e.teleport&&(T.value=!1)}),()=>{const m=$e(i("div",ie({class:Tl(),ref:o,onTouchstartPassive:g,onTouchend:b,onTouchcancel:b,onClickCapture:C,style:f.value},a),[t.default?t.default():i(Ls,{name:e.icon,class:Tl("icon")},null)]),[[Be,T.value]]);return e.teleport?i(Ft,{to:e.teleport},{default:()=>[m]}):m}}});const Ev=N(Dv),Pv={height:U(0),anchors:me(),duration:U(.3),contentDraggable:R,lockScroll:Boolean,safeAreaInsetBottom:R},[Ov,qn]=V("floating-panel");var Av=_({name:Ov,props:Pv,emits:["heightChange","update:height"],setup(e,{emit:t,slots:n}){const o=O(),l=O(),c=xo(()=>+e.height,C=>t("update:height",C)),r=A(()=>{var C,T;return{min:(C=e.anchors[0])!=null?C:100,max:(T=e.anchors[e.anchors.length-1])!=null?T:Math.round(Ne.value*.6)}}),u=A(()=>e.anchors.length>=2?e.anchors:[r.value.min,r.value.max]),s=O(!1),d=A(()=>({height:ne(r.value.max),transform:"translateY(calc(100% + ".concat(ne(-c.value),"))"),transition:s.value?"none":"transform ".concat(e.duration,"s cubic-bezier(0.18, 0.89, 0.32, 1.28)")})),f=C=>{const T=Math.abs(C),{min:m,max:$}=r.value;return T>$?-($+(T-$)*.2):T<m?-(m-(m-T)*.2):C};let v,h=-1;const y=He(),S=C=>{y.start(C),s.value=!0,v=-c.value,h=-1},g=C=>{var T;y.move(C);const m=C.target;if(l.value===m||(T=l.value)!=null&&T.contains(m)){const{scrollTop:I}=l.value;if(h=Math.max(h,I),!e.contentDraggable)return;if(-v<r.value.max)ve(C,!0);else if(!(I<=0&&y.deltaY.value>0)||h>0)return}const $=y.deltaY.value+v;c.value=-f($)},w=()=>{h=-1,s.value=!1,c.value=la(u.value,c.value),c.value!==-v&&t("heightChange",{height:c.value})};j(r,()=>{c.value=la(u.value,c.value)},{immediate:!0}),yi(o,()=>e.lockScroll||s.value),ye("touchmove",g,{target:o});const b=()=>n.header?n.header():i("div",{class:qn("header")},[i("div",{class:qn("header-bar")},null)]);return()=>{var C;return i("div",{class:[qn(),{"van-safe-area-bottom":e.safeAreaInsetBottom}],ref:o,style:d.value,onTouchstartPassive:S,onTouchend:w,onTouchcancel:w},[b(),i("div",{class:qn("content"),ref:l},[(C=n.default)==null?void 0:C.call(n)])])}}});const pv=N(Av),[ir,Rv]=V("grid"),_v={square:Boolean,center:R,border:R,gutter:M,reverse:Boolean,iconSize:M,direction:String,clickable:Boolean,columnNum:U(4)},rr=Symbol(ir);var Vv=_({name:ir,props:_v,setup(e,{slots:t}){const{linkChildren:n}=Oe(rr);return n({props:e}),()=>{var a;return i("div",{style:{paddingLeft:ne(e.gutter)},class:[Rv(),{[ii]:e.border&&!e.gutter}]},[(a=t.default)==null?void 0:a.call(t)])}}});const Lv=N(Vv),[Mv,Gn]=V("grid-item"),zv=J({},Ot,{dot:Boolean,text:String,icon:String,badge:M,iconColor:String,iconPrefix:String,badgeProps:Object});var Fv=_({name:Mv,props:zv,setup(e,{slots:t}){const{parent:n,index:a}=Ie(rr),o=Nt();if(!n)return;const l=A(()=>{const{square:d,gutter:f,columnNum:v}=n.props,h="".concat(100/+v,"%"),y={flexBasis:h};if(d)y.paddingTop=h;else if(f){const S=ne(f);y.paddingRight=S,a.value>=+v&&(y.marginTop=S)}return y}),c=A(()=>{const{square:d,gutter:f}=n.props;if(d&&f){const v=ne(f);return{right:v,bottom:v,height:"auto"}}}),r=()=>{if(t.icon)return i(Ht,ie({dot:e.dot,content:e.badge},e.badgeProps),{default:t.icon});if(e.icon)return i(ae,{dot:e.dot,name:e.icon,size:n.props.iconSize,badge:e.badge,class:Gn("icon"),color:e.iconColor,badgeProps:e.badgeProps,classPrefix:e.iconPrefix},null)},u=()=>{if(t.text)return t.text();if(e.text)return i("span",{class:Gn("text")},[e.text])},s=()=>t.default?t.default():[r(),u()];return()=>{const{center:d,border:f,square:v,gutter:h,reverse:y,direction:S,clickable:g}=n.props,w=[Gn("content",[S,{center:d,square:v,reverse:y,clickable:g,surround:f&&h}]),{[xt]:f}];return i("div",{class:[Gn({square:v})],style:l.value},[i("div",{role:g?"button":void 0,class:w,style:c.value,tabindex:g?0:void 0,onClick:o},[s()])])}}});const Nv=N(Fv),[Hv,$l]=V("highlight"),Wv={autoEscape:R,caseSensitive:Boolean,highlightClass:String,highlightTag:H("span"),keywords:Te([String,Array]),sourceString:H(""),tag:H("div"),unhighlightClass:String,unhighlightTag:H("span")};var jv=_({name:Hv,props:Wv,setup(e){const t=A(()=>{const{autoEscape:a,caseSensitive:o,keywords:l,sourceString:c}=e,r=o?"g":"gi";let s=(Array.isArray(l)?l:[l]).filter(f=>f).reduce((f,v)=>{a&&(v=v.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"));const h=new RegExp(v,r);let y;for(;y=h.exec(c);){const S=y.index,g=h.lastIndex;if(S>=g){h.lastIndex++;continue}f.push({start:S,end:g,highlight:!0})}return f},[]);s=s.sort((f,v)=>f.start-v.start).reduce((f,v)=>{const h=f[f.length-1];if(!h||v.start>h.end){const y=h?h.end:0,S=v.start;y!==S&&f.push({start:y,end:S,highlight:!1}),f.push(v)}else h.end=Math.max(h.end,v.end);return f},[]);const d=s[s.length-1];return d||s.push({start:0,end:c.length,highlight:!1}),d&&d.end<c.length&&s.push({start:d.end,end:c.length,highlight:!1}),s}),n=()=>{const{sourceString:a,highlightClass:o,unhighlightClass:l,highlightTag:c,unhighlightTag:r}=e;return t.value.map(u=>{const{start:s,end:d,highlight:f}=u,v=a.slice(s,d);return f?i(c,{class:[$l("tag"),o]},{default:()=>[v]}):i(r,{class:l},{default:()=>[v]})})};return()=>{const{tag:a}=e;return i(a,{class:$l()},{default:()=>[n()]})}}});const Yv=N(jv),Bl=e=>Math.sqrt((e[0].clientX-e[1].clientX)**2+(e[0].clientY-e[1].clientY)**2),Uv=e=>({x:(e[0].clientX+e[1].clientX)/2,y:(e[0].clientY+e[1].clientY)/2}),Na=V("image-preview")[1],Il=2.6,Xv={src:String,show:Boolean,active:Number,minZoom:Te(M),maxZoom:Te(M),rootWidth:Te(Number),rootHeight:Te(Number),disableZoom:Boolean,doubleScale:Boolean,closeOnClickImage:Boolean,closeOnClickOverlay:Boolean,vertical:Boolean};var qv=_({props:Xv,emits:["scale","close","longPress"],setup(e,{emit:t,slots:n}){const a=xe({scale:1,moveX:0,moveY:0,moving:!1,zooming:!1,initializing:!1,imageRatio:0}),o=He(),l=O(),c=O(),r=O(!1),u=O(!1);let s=0;const d=A(()=>{const{scale:F,moveX:Q,moveY:ee,moving:de,zooming:he,initializing:G}=a,L={transitionDuration:he||de||G?"0s":".3s"};return(F!==1||u.value)&&(L.transform="matrix(".concat(F,", 0, 0, ").concat(F,", ").concat(Q,", ").concat(ee,")")),L}),f=A(()=>{if(a.imageRatio){const{rootWidth:F,rootHeight:Q}=e,ee=r.value?Q/a.imageRatio:F;return Math.max(0,(a.scale*ee-F)/2)}return 0}),v=A(()=>{if(a.imageRatio){const{rootWidth:F,rootHeight:Q}=e,ee=r.value?Q:F*a.imageRatio;return Math.max(0,(a.scale*ee-Q)/2)}return 0}),h=(F,Q)=>{var ee;if(F=ke(F,+e.minZoom,+e.maxZoom+1),F!==a.scale){const de=F/a.scale;if(a.scale=F,Q){const he=ce((ee=l.value)==null?void 0:ee.$el),G={x:he.width*.5,y:he.height*.5},L=a.moveX-(Q.x-he.left-G.x)*(de-1),W=a.moveY-(Q.y-he.top-G.y)*(de-1);a.moveX=ke(L,-f.value,f.value),a.moveY=ke(W,-v.value,v.value)}else a.moveX=0,a.moveY=u.value?s:0;t("scale",{scale:F,index:e.active})}},y=()=>{h(1)},S=()=>{const F=a.scale>1?1:2;h(F,F===2||u.value?{x:o.startX.value,y:o.startY.value}:void 0)};let g,w,b,C,T,m,$,I,x=!1;const E=F=>{const{touches:Q}=F;if(g=Q.length,g===2&&e.disableZoom)return;const{offsetX:ee}=o;o.start(F),w=a.moveX,b=a.moveY,I=Date.now(),x=!1,a.moving=g===1&&(a.scale!==1||u.value),a.zooming=g===2&&!ee.value,a.zooming&&(C=a.scale,T=Bl(Q))},D=F=>{const{touches:Q}=F;if(o.move(F),a.moving){const{deltaX:ee,deltaY:de}=o,he=ee.value+w,G=de.value+b;if((e.vertical?o.isVertical()&&Math.abs(G)>v.value:o.isHorizontal()&&Math.abs(he)>f.value)&&!x){a.moving=!1;return}x=!0,ve(F,!0),a.moveX=ke(he,-f.value,f.value),a.moveY=ke(G,-v.value,v.value)}if(a.zooming&&(ve(F,!0),Q.length===2)){const ee=Bl(Q),de=C*ee/T;m=Uv(Q),h(de,m)}},k=F=>{var Q;const ee=(Q=c.value)==null?void 0:Q.$el;if(!ee)return;const de=ee.firstElementChild,he=F.target===ee,G=de==null?void 0:de.contains(F.target);!e.closeOnClickImage&&G||!e.closeOnClickOverlay&&he||t("close")},B=F=>{if(g>1)return;const Q=Date.now()-I,ee=250;o.isTap.value&&(Q<ee?e.doubleScale?$?(clearTimeout($),$=null,S()):$=setTimeout(()=>{k(F),$=null},ee):k(F):Q>ci&&t("longPress"))},p=F=>{let Q=!1;if((a.moving||a.zooming)&&(Q=!0,a.moving&&w===a.moveX&&b===a.moveY&&(Q=!1),!F.touches.length)){a.zooming&&(a.moveX=ke(a.moveX,-f.value,f.value),a.moveY=ke(a.moveY,-v.value,v.value),a.zooming=!1),a.moving=!1,w=0,b=0,C=1,a.scale<1&&y();const ee=+e.maxZoom;a.scale>ee&&h(ee,m)}ve(F,Q),B(F),o.reset()},Y=()=>{const{rootWidth:F,rootHeight:Q}=e,ee=Q/F,{imageRatio:de}=a;r.value=a.imageRatio>ee&&de<Il,u.value=a.imageRatio>ee&&de>=Il,u.value&&(s=(de*F-Q)/2,a.moveY=s,a.initializing=!0,De(()=>{a.initializing=!1})),y()},q=F=>{const{naturalWidth:Q,naturalHeight:ee}=F.target;a.imageRatio=ee/Q,Y()};return j(()=>e.active,y),j(()=>e.show,F=>{F||y()}),j(()=>[e.rootWidth,e.rootHeight],Y),ye("touchmove",D,{target:A(()=>{var F;return(F=c.value)==null?void 0:F.$el})}),re({resetScale:y}),()=>{const F={loading:()=>i(Ke,{type:"spinner"},null)};return i(Co,{ref:c,class:Na("swipe-item"),onTouchstartPassive:E,onTouchend:p,onTouchcancel:p},{default:()=>[n.image?i("div",{class:Na("image-wrap")},[n.image({src:e.src,onLoad:q,style:d.value})]):i(Ia,{ref:l,src:e.src,fit:"contain",class:Na("image",{vertical:r.value}),style:d.value,onLoad:q},F)]})}}});const[Gv,Zt]=V("image-preview"),Kv=["show","teleport","transition","overlayStyle","closeOnPopstate"],Zv={show:Boolean,loop:R,images:me(),minZoom:U(1/3),maxZoom:U(3),overlay:R,vertical:Boolean,closeable:Boolean,showIndex:R,className:ge,closeIcon:H("clear"),transition:String,beforeClose:Function,doubleScale:R,overlayClass:ge,overlayStyle:Object,swipeDuration:U(300),startPosition:U(0),showIndicators:Boolean,closeOnPopstate:R,closeOnClickImage:R,closeOnClickOverlay:R,closeIconPosition:H("top-right"),teleport:[String,Object]};var sr=_({name:Gv,props:Zv,emits:["scale","close","closed","change","longPress","update:show"],setup(e,{emit:t,slots:n}){const a=O(),o=O(),l=xe({active:0,rootWidth:0,rootHeight:0,disableZoom:!1}),c=()=>{if(a.value){const C=ce(a.value.$el);l.rootWidth=C.width,l.rootHeight=C.height,a.value.resize()}},r=C=>t("scale",C),u=C=>t("update:show",C),s=()=>{Pt(e.beforeClose,{args:[l.active],done:()=>u(!1)})},d=C=>{C!==l.active&&(l.active=C,t("change",C))},f=()=>{if(e.showIndex)return i("div",{class:Zt("index")},[n.index?n.index({index:l.active}):"".concat(l.active+1," / ").concat(e.images.length)])},v=()=>{if(n.cover)return i("div",{class:Zt("cover")},[n.cover()])},h=()=>{l.disableZoom=!0},y=()=>{l.disableZoom=!1},S=()=>i(So,{ref:a,lazyRender:!0,loop:e.loop,class:Zt("swipe"),vertical:e.vertical,duration:e.swipeDuration,initialSwipe:e.startPosition,showIndicators:e.showIndicators,indicatorColor:"white",onChange:d,onDragEnd:y,onDragStart:h},{default:()=>[e.images.map((C,T)=>i(qv,{ref:m=>{T===l.active&&(o.value=m)},src:C,show:e.show,active:l.active,maxZoom:e.maxZoom,minZoom:e.minZoom,rootWidth:l.rootWidth,rootHeight:l.rootHeight,disableZoom:l.disableZoom,doubleScale:e.doubleScale,closeOnClickImage:e.closeOnClickImage,closeOnClickOverlay:e.closeOnClickOverlay,vertical:e.vertical,onScale:r,onClose:s,onLongPress:()=>t("longPress",{index:T})},{image:n.image}))]}),g=()=>{if(e.closeable)return i(ae,{role:"button",name:e.closeIcon,class:[Zt("close-icon",e.closeIconPosition),Ee],onClick:s},null)},w=()=>t("closed"),b=(C,T)=>{var m;return(m=a.value)==null?void 0:m.swipeTo(C,T)};return re({resetScale:()=>{var C;(C=o.value)==null||C.resetScale()},swipeTo:b}),we(c),j([qe,Ne],c),j(()=>e.startPosition,C=>d(+C)),j(()=>e.show,C=>{const{images:T,startPosition:m}=e;C?(d(+m),le(()=>{c(),b(+m,{immediate:!0})})):t("close",{index:l.active,url:T[l.active]})}),()=>i(Ze,ie({class:[Zt(),e.className],overlayClass:[Zt("overlay"),e.overlayClass],onClosed:w,"onUpdate:show":u},ue(e,Kv)),{default:()=>[g(),S(),f(),v()]})}});let ta;const Jv={loop:!0,images:[],maxZoom:3,minZoom:1/3,onScale:void 0,onClose:void 0,onChange:void 0,vertical:!1,teleport:"body",className:"",showIndex:!0,closeable:!1,closeIcon:"clear",transition:void 0,beforeClose:void 0,doubleScale:!0,overlayStyle:void 0,overlayClass:void 0,startPosition:0,swipeDuration:300,showIndicators:!1,closeOnPopstate:!0,closeOnClickOverlay:!0,closeIconPosition:"top-right"};function Qv(){({instance:ta}=$a({setup(){const{state:e,toggle:t}=Ta(),n=()=>{e.images=[]};return()=>i(sr,ie(e,{onClosed:n,"onUpdate:show":t}),null)}}))}const eh=(e,t=0)=>{if(_e)return ta||Qv(),e=Array.isArray(e)?{images:e,startPosition:t}:e,ta.open(J({},Jv,e)),ta},th=N(sr);function nh(){const e="A".charCodeAt(0);return Array(26).fill("").map((n,a)=>String.fromCharCode(e+a))}const[cr,Ha]=V("index-bar"),ah={sticky:R,zIndex:M,teleport:[String,Object],highlightColor:String,stickyOffsetTop:Ce(0),indexList:{type:Array,default:nh}},ur=Symbol(cr);var oh=_({name:cr,props:ah,emits:["select","change"],setup(e,{emit:t,slots:n}){const a=O(),o=O(),l=O(""),c=He(),r=dn(a),{children:u,linkChildren:s}=Oe(ur);let d;s({props:e});const f=A(()=>{if(se(e.zIndex))return{zIndex:+e.zIndex+1}}),v=A(()=>{if(e.highlightColor)return{color:e.highlightColor}}),h=(x,E)=>{for(let D=u.length-1;D>=0;D--){const k=D>0?E[D-1].height:0,B=e.sticky?k+e.stickyOffsetTop:0;if(x+B>=E[D].top)return D}return-1},y=x=>u.find(E=>String(E.index)===x),S=()=>{if(zt(a))return;const{sticky:x,indexList:E}=e,D=vt(r.value),k=ce(r),B=u.map(Y=>Y.getRect(r.value,k));let p=-1;if(d){const Y=y(d);if(Y){const q=Y.getRect(r.value,k);e.sticky&&e.stickyOffsetTop?p=h(q.top-e.stickyOffsetTop,B):p=h(q.top,B)}}else p=h(D,B);l.value=E[p],x&&u.forEach((Y,q)=>{const{state:F,$el:Q}=Y;if(q===p||q===p-1){const ee=Q.getBoundingClientRect();F.left=ee.left,F.width=ee.width}else F.left=null,F.width=null;if(q===p)F.active=!0,F.top=Math.max(e.stickyOffsetTop,B[q].top-D)+k.top;else if(q===p-1&&d===""){const ee=B[p].top-D;F.active=ee>0,F.top=ee+k.top-B[q].height}else F.active=!1}),d=""},g=()=>{le(S)};ye("scroll",S,{target:r,passive:!0}),we(g),j(()=>e.indexList,g),j(l,x=>{x&&t("change",x)});const w=()=>e.indexList.map(x=>{const E=x===l.value;return i("span",{class:Ha("index",{active:E}),style:E?v.value:void 0,"data-index":x},[x])}),b=x=>{d=String(x);const E=y(d);if(E){const D=vt(r.value),k=ce(r),{offsetHeight:B}=document.documentElement;if(E.$el.scrollIntoView(),D===B-k.height){S();return}e.sticky&&e.stickyOffsetTop&&(Lt()===B-k.height?Dn(Lt()):Dn(Lt()-e.stickyOffsetTop)),t("select",E.index)}},C=x=>{const{index:E}=x.dataset;E&&b(E)},T=x=>{C(x.target)};let m;const $=x=>{if(c.move(x),c.isVertical()){ve(x);const{clientX:E,clientY:D}=x.touches[0],k=document.elementFromPoint(E,D);if(k){const{index:B}=k.dataset;B&&m!==B&&(m=B,C(k))}}},I=()=>i("div",{ref:o,class:Ha("sidebar"),style:f.value,onClick:T,onTouchstartPassive:c.start},[w()]);return re({scrollTo:b}),ye("touchmove",$,{target:o}),()=>{var x;return i("div",{ref:a,class:Ha()},[e.teleport?i(Ft,{to:e.teleport},{default:()=>[I()]}):I(),(x=n.default)==null?void 0:x.call(n)])}}});const[lh,ih]=V("index-anchor"),rh={index:M};var sh=_({name:lh,props:rh,setup(e,{slots:t}){const n=xe({top:0,left:null,rect:{top:0,height:0},width:null,active:!1}),a=O(),{parent:o}=Ie(ur);if(!o)return;const l=()=>n.active&&o.props.sticky,c=A(()=>{const{zIndex:u,highlightColor:s}=o.props;if(l())return J(wt(u),{left:n.left?"".concat(n.left,"px"):void 0,width:n.width?"".concat(n.width,"px"):void 0,transform:n.top?"translate3d(0, ".concat(n.top,"px, 0)"):void 0,color:s})});return re({state:n,getRect:(u,s)=>{const d=ce(a);return n.rect.height=d.height,u===window||u===document.body?n.rect.top=d.top+Lt():n.rect.top=d.top+vt(u)-s.top,n.rect}}),()=>{const u=l();return i("div",{ref:a,style:{height:u?"".concat(n.rect.height,"px"):void 0}},[i("div",{style:c.value,class:[ih({sticky:u}),{[go]:u}]},[t.default?t.default():e.index])])}}});const ch=N(sh),uh=N(oh),[dh,Jt,fh]=V("list"),vh={error:Boolean,offset:U(300),loading:Boolean,disabled:Boolean,finished:Boolean,scroller:Object,errorText:String,direction:H("down"),loadingText:String,finishedText:String,immediateCheck:R};var hh=_({name:dh,props:vh,emits:["load","update:error","update:loading"],setup(e,{emit:t,slots:n}){const a=O(e.loading),o=O(),l=O(),c=_c(),r=dn(o),u=A(()=>e.scroller||r.value),s=()=>{le(()=>{if(a.value||e.finished||e.disabled||e.error||(c==null?void 0:c.value)===!1)return;const{direction:y}=e,S=+e.offset,g=ce(u);if(!g.height||zt(o))return;let w=!1;const b=ce(l);y==="up"?w=g.top-b.top<=S:w=b.bottom-g.bottom<=S,w&&(a.value=!0,t("update:loading",!0),t("load"))})},d=()=>{if(e.finished){const y=n.finished?n.finished():e.finishedText;if(y)return i("div",{class:Jt("finished-text")},[y])}},f=()=>{t("update:error",!1),s()},v=()=>{if(e.error){const y=n.error?n.error():e.errorText;if(y)return i("div",{role:"button",class:Jt("error-text"),tabindex:0,onClick:f},[y])}},h=()=>{if(a.value&&!e.finished&&!e.disabled)return i("div",{class:Jt("loading")},[n.loading?n.loading():i(Ke,{class:Jt("loading-icon")},{default:()=>[e.loadingText||fh("loading")]})])};return j(()=>[e.loading,e.finished,e.error],s),c&&j(c,y=>{y&&s()}),Wr(()=>{a.value=e.loading}),we(()=>{e.immediateCheck&&s()}),re({check:s}),ye("scroll",s,{target:u,passive:!0}),()=>{var y;const S=(y=n.default)==null?void 0:y.call(n),g=i("div",{ref:l,class:Jt("placeholder")},null);return i("div",{ref:o,role:"feed",class:Jt(),"aria-busy":a.value},[e.direction==="down"?S:g,h(),d(),v(),e.direction==="up"?S:g])}}});const mh=N(hh),[gh,st]=V("nav-bar"),bh={title:String,fixed:Boolean,zIndex:M,border:R,leftText:String,rightText:String,leftDisabled:Boolean,rightDisabled:Boolean,leftArrow:Boolean,placeholder:Boolean,safeAreaInsetTop:Boolean,clickable:R};var yh=_({name:gh,props:bh,emits:["clickLeft","clickRight"],setup(e,{emit:t,slots:n}){const a=O(),o=ya(a,st),l=d=>{e.leftDisabled||t("clickLeft",d)},c=d=>{e.rightDisabled||t("clickRight",d)},r=()=>n.left?n.left():[e.leftArrow&&i(ae,{class:st("arrow"),name:"arrow-left"},null),e.leftText&&i("span",{class:st("text")},[e.leftText])],u=()=>n.right?n.right():i("span",{class:st("text")},[e.rightText]),s=()=>{const{title:d,fixed:f,border:v,zIndex:h}=e,y=wt(h),S=e.leftArrow||e.leftText||n.left,g=e.rightText||n.right;return i("div",{ref:a,style:y,class:[st({fixed:f}),{[go]:v,"van-safe-area-top":e.safeAreaInsetTop}]},[i("div",{class:st("content")},[S&&i("div",{class:[st("left",{disabled:e.leftDisabled}),e.clickable&&!e.leftDisabled?Ee:""],onClick:l},[r()]),i("div",{class:[st("title"),"van-ellipsis"]},[n.title?n.title():d]),g&&i("div",{class:[st("right",{disabled:e.rightDisabled}),e.clickable&&!e.rightDisabled?Ee:""],onClick:c},[u()])])])};return()=>e.fixed&&e.placeholder?o(s):s()}});const wh=N(yh),[xh,gn]=V("notice-bar"),Sh={text:String,mode:String,color:String,delay:U(1),speed:U(60),leftIcon:String,wrapable:Boolean,background:String,scrollable:{type:Boolean,default:null}};var Ch=_({name:xh,props:Sh,emits:["close","replay"],setup(e,{emit:t,slots:n}){let a=0,o=0,l;const c=O(),r=O(),u=xe({show:!0,offset:0,duration:0}),s=()=>{if(n["left-icon"])return n["left-icon"]();if(e.leftIcon)return i(ae,{class:gn("left-icon"),name:e.leftIcon},null)},d=()=>{if(e.mode==="closeable")return"cross";if(e.mode==="link")return"arrow"},f=g=>{e.mode==="closeable"&&(u.show=!1,t("close",g))},v=()=>{if(n["right-icon"])return n["right-icon"]();const g=d();if(g)return i(ae,{name:g,class:gn("right-icon"),onClick:f},null)},h=()=>{u.offset=a,u.duration=0,De(()=>{Bt(()=>{u.offset=-o,u.duration=(o+a)/+e.speed,t("replay")})})},y=()=>{const g=e.scrollable===!1&&!e.wrapable,w={transform:u.offset?"translateX(".concat(u.offset,"px)"):"",transitionDuration:"".concat(u.duration,"s")};return i("div",{ref:c,role:"marquee",class:gn("wrap")},[i("div",{ref:r,style:w,class:[gn("content"),{"van-ellipsis":g}],onTransitionend:h},[n.default?n.default():e.text])])},S=()=>{const{delay:g,speed:w,scrollable:b}=e,C=se(g)?+g*1e3:0;a=0,o=0,u.offset=0,u.duration=0,clearTimeout(l),l=setTimeout(()=>{if(!c.value||!r.value||b===!1)return;const T=ce(c).width,m=ce(r).width;(b||m>T)&&Bt(()=>{a=T,o=m,u.offset=-o,u.duration=o/+w})},C)};return ba(S),un(S),ye("pageshow",S),re({reset:S}),j(()=>[e.text,e.scrollable],S),()=>{const{color:g,wrapable:w,background:b}=e;return $e(i("div",{role:"alert",class:gn({wrapable:w}),style:{color:g,background:b}},[s(),y(),v()]),[[Be,u.show]])}}});const kh=N(Ch),[Th,$h]=V("notify"),Bh=["lockScroll","position","show","teleport","zIndex"],Ih=J({},fn,{type:H("danger"),color:String,message:M,position:H("top"),className:ge,background:String,lockScroll:Boolean});var dr=_({name:Th,props:Ih,emits:["update:show"],setup(e,{emit:t,slots:n}){const a=o=>t("update:show",o);return()=>i(Ze,ie({class:[$h([e.type]),e.className],style:{color:e.color,background:e.background},overlay:!1,duration:.2,"onUpdate:show":a},ue(e,Bh)),{default:()=>[n.default?n.default():e.message]})}});let Dl,an;const Dh=e=>Le(e)?e:{message:e};function Eh(){({instance:an}=$a({setup(){const{state:e,toggle:t}=Ta();return()=>i(dr,ie(e,{"onUpdate:show":t}),null)}}))}const Ph=()=>({type:"danger",color:void 0,message:"",onClose:void 0,onClick:void 0,onOpened:void 0,duration:3e3,position:void 0,className:"",lockScroll:!1,background:void 0});let Oh=Ph();const Ah=()=>{an&&an.toggle(!1)};function Vy(e){if(_e)return an||Eh(),e=J({},Oh,Dh(e)),an.open(e),clearTimeout(Dl),e.duration>0&&(Dl=setTimeout(Ah,e.duration)),an}const ph=N(dr),[Rh,Tn]=V("key"),_h=i("svg",{class:Tn("collapse-icon"),viewBox:"0 0 30 24"},[i("path",{d:"M26 13h-2v2h2v-2zm-8-3h2V8h-2v2zm2-4h2V4h-2v2zm2 4h4V4h-2v4h-2v2zm-7 14 3-3h-6l3 3zM6 13H4v2h2v-2zm16 0H8v2h14v-2zm-12-3h2V8h-2v2zM28 0l1 1 1 1v15l-1 2H1l-1-2V2l1-1 1-1zm0 2H2v15h26V2zM6 4v2H4V4zm10 2h2V4h-2v2zM8 9v1H4V8zm8 0v1h-2V8zm-6-5v2H8V4zm4 0v2h-2V4z",fill:"currentColor"},null)]),Vh=i("svg",{class:Tn("delete-icon"),viewBox:"0 0 32 22"},[i("path",{d:"M28 0a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H10.4a2 2 0 0 1-1.4-.6L1 13.1c-.6-.5-.9-1.3-.9-2 0-1 .3-1.7.9-2.2L9 .6a2 2 0 0 1 1.4-.6zm0 2H10.4l-8.2 8.3a1 1 0 0 0-.3.7c0 .3.1.5.3.7l8.2 8.4H28a2 2 0 0 0 2-2V4c0-1.1-.9-2-2-2zm-5 4a1 1 0 0 1 .7.3 1 1 0 0 1 0 1.4L20.4 11l3.3 3.3c.2.2.3.5.3.7 0 .3-.1.5-.3.7a1 1 0 0 1-.7.3 1 1 0 0 1-.7-.3L19 12.4l-3.4 3.3a1 1 0 0 1-.6.3 1 1 0 0 1-.7-.3 1 1 0 0 1-.3-.7c0-.2.1-.5.3-.7l3.3-3.3-3.3-3.3A1 1 0 0 1 14 7c0-.3.1-.5.3-.7A1 1 0 0 1 15 6a1 1 0 0 1 .6.3L19 9.6l3.3-3.3A1 1 0 0 1 23 6z",fill:"currentColor"},null)]);var Wa=_({name:Rh,props:{type:String,text:M,color:String,wider:Boolean,large:Boolean,loading:Boolean},emits:["press"],setup(e,{emit:t,slots:n}){const a=O(!1),o=He(),l=s=>{o.start(s),a.value=!0},c=s=>{o.move(s),o.direction.value&&(a.value=!1)},r=s=>{a.value&&(n.default||ve(s),a.value=!1,t("press",e.text,e.type))},u=()=>{if(e.loading)return i(Ke,{class:Tn("loading-icon")},null);const s=n.default?n.default():e.text;switch(e.type){case"delete":return s||Vh;case"extra":return s||_h;default:return s}};return()=>i("div",{class:Tn("wrapper",{wider:e.wider}),onTouchstartPassive:l,onTouchmovePassive:c,onTouchend:r,onTouchcancel:r},[i("div",{role:"button",tabindex:0,class:Tn([e.color,{large:e.large,active:a.value,delete:e.type==="delete"}])},[u()])])}});const[Lh,kt]=V("number-keyboard"),Mh={show:Boolean,title:String,theme:H("default"),zIndex:M,teleport:[String,Object],maxlength:U(1/0),modelValue:H(""),transition:R,blurOnClose:R,showDeleteKey:R,randomKeyOrder:Boolean,closeButtonText:String,deleteButtonText:String,closeButtonLoading:Boolean,hideOnClickOutside:R,safeAreaInsetBottom:R,extraKey:{type:[String,Array],default:""}};function zh(e){for(let t=e.length-1;t>0;t--){const n=Math.floor(Math.random()*(t+1)),a=e[t];e[t]=e[n],e[n]=a}return e}var Fh=_({name:Lh,inheritAttrs:!1,props:Mh,emits:["show","hide","blur","input","close","delete","update:modelValue"],setup(e,{emit:t,slots:n,attrs:a}){const o=O(),l=()=>{const g=Array(9).fill("").map((w,b)=>({text:b+1}));return e.randomKeyOrder&&zh(g),g},c=()=>[...l(),{text:e.extraKey,type:"extra"},{text:0},{text:e.showDeleteKey?e.deleteButtonText:"",type:e.showDeleteKey?"delete":""}],r=()=>{const g=l(),{extraKey:w}=e,b=Array.isArray(w)?w:[w];return b.length===0?g.push({text:0,wider:!0}):b.length===1?g.push({text:0,wider:!0},{text:b[0],type:"extra"}):b.length===2&&g.push({text:b[0],type:"extra"},{text:0},{text:b[1],type:"extra"}),g},u=A(()=>e.theme==="custom"?r():c()),s=()=>{e.show&&t("blur")},d=()=>{t("close"),e.blurOnClose&&s()},f=()=>t(e.show?"show":"hide"),v=(g,w)=>{if(g===""){w==="extra"&&s();return}const b=e.modelValue;w==="delete"?(t("delete"),t("update:modelValue",b.slice(0,b.length-1))):w==="close"?d():b.length<+e.maxlength&&(t("input",g),t("update:modelValue",b+g))},h=()=>{const{title:g,theme:w,closeButtonText:b}=e,C=n["title-left"],T=b&&w==="default";if(g||T||C)return i("div",{class:kt("header")},[C&&i("span",{class:kt("title-left")},[C()]),g&&i("h2",{class:kt("title")},[g]),T&&i("button",{type:"button",class:[kt("close"),Ee],onClick:d},[b])])},y=()=>u.value.map(g=>{const w={};return g.type==="delete"&&(w.default=n.delete),g.type==="extra"&&(w.default=n["extra-key"]),i(Wa,{key:g.text,text:g.text,type:g.type,wider:g.wider,color:g.color,onPress:v},w)}),S=()=>{if(e.theme==="custom")return i("div",{class:kt("sidebar")},[e.showDeleteKey&&i(Wa,{large:!0,text:e.deleteButtonText,type:"delete",onPress:v},{default:n.delete}),i(Wa,{large:!0,text:e.closeButtonText,type:"close",color:"blue",loading:e.closeButtonLoading,onPress:v},null)])};return j(()=>e.show,g=>{e.transition||t(g?"show":"hide")}),e.hideOnClickOutside&&ha(o,s,{eventName:"touchstart"}),()=>{const g=h(),w=i(fa,{name:e.transition?"van-slide-up":""},{default:()=>[$e(i("div",ie({ref:o,style:wt(e.zIndex),class:kt({unfit:!e.safeAreaInsetBottom,"with-title":!!g}),onAnimationend:f,onTouchstartPassive:ho},a),[g,i("div",{class:kt("body")},[i("div",{class:kt("keys")},[y()]),S()])]),[[Be,e.show]])]});return e.teleport?i(Ft,{to:e.teleport},{default:()=>[w]}):w}}});const Nh=N(Fh),[Hh,Qt,El]=V("pagination"),ja=(e,t,n)=>({number:e,text:t,active:n}),Wh={mode:H("multi"),prevText:String,nextText:String,pageCount:U(0),modelValue:Ce(0),totalItems:U(0),showPageSize:U(5),itemsPerPage:U(10),forceEllipses:Boolean,showPrevButton:R,showNextButton:R};var jh=_({name:Hh,props:Wh,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const a=A(()=>{const{pageCount:d,totalItems:f,itemsPerPage:v}=e,h=+d||Math.ceil(+f/+v);return Math.max(1,h)}),o=A(()=>{const d=[],f=a.value,v=+e.showPageSize,{modelValue:h,forceEllipses:y}=e;let S=1,g=f;const w=v<f;w&&(S=Math.max(h-Math.floor(v/2),1),g=S+v-1,g>f&&(g=f,S=g-v+1));for(let b=S;b<=g;b++){const C=ja(b,b,b===h);d.push(C)}if(w&&v>0&&y){if(S>1){const b=ja(S-1,"...");d.unshift(b)}if(g<f){const b=ja(g+1,"...");d.push(b)}}return d}),l=(d,f)=>{d=ke(d,1,a.value),e.modelValue!==d&&(t("update:modelValue",d),f&&t("change",d))};cn(()=>l(e.modelValue));const c=()=>i("li",{class:Qt("page-desc")},[n.pageDesc?n.pageDesc():"".concat(e.modelValue,"/").concat(a.value)]),r=()=>{const{mode:d,modelValue:f,showPrevButton:v}=e;if(!v)return;const h=n["prev-text"],y=f===1;return i("li",{class:[Qt("item",{disabled:y,border:d==="simple",prev:!0}),Cn]},[i("button",{type:"button",disabled:y,onClick:()=>l(f-1,!0)},[h?h():e.prevText||El("prev")])])},u=()=>{const{mode:d,modelValue:f,showNextButton:v}=e;if(!v)return;const h=n["next-text"],y=f===a.value;return i("li",{class:[Qt("item",{disabled:y,border:d==="simple",next:!0}),Cn]},[i("button",{type:"button",disabled:y,onClick:()=>l(f+1,!0)},[h?h():e.nextText||El("next")])])},s=()=>o.value.map(d=>i("li",{class:[Qt("item",{active:d.active,page:!0}),Cn]},[i("button",{type:"button","aria-current":d.active||void 0,onClick:()=>l(d.number,!0)},[n.page?n.page(d):d.text])]));return()=>i("nav",{role:"navigation",class:Qt()},[i("ul",{class:Qt("items")},[r(),e.mode==="simple"?c():s(),u()])])}});const Yh=N(jh),[Uh,bn]=V("password-input"),Xh={info:String,mask:R,value:H(""),gutter:M,length:U(6),focused:Boolean,errorInfo:String};var qh=_({name:Uh,props:Xh,emits:["focus"],setup(e,{emit:t}){const n=o=>{o.stopPropagation(),t("focus",o)},a=()=>{const o=[],{mask:l,value:c,gutter:r,focused:u}=e,s=+e.length;for(let d=0;d<s;d++){const f=c[d],v=d!==0&&!r,h=u&&d===c.length;let y;d!==0&&r&&(y={marginLeft:ne(r)}),o.push(i("li",{class:[{[ri]:v},bn("item",{focus:h})],style:y},[l?i("i",{style:{visibility:f?"visible":"hidden"}},null):f,h&&i("div",{class:bn("cursor")},null)]))}return o};return()=>{const o=e.errorInfo||e.info;return i("div",{class:bn()},[i("ul",{class:[bn("security"),{[Cn]:!e.gutter}],onTouchstartPassive:n},[a()]),o&&i("div",{class:bn(e.errorInfo?"error-info":"info")},[o])])}}});const Gh=N(qh),Kh=N(Yc);function Qe(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Ao(e){var t=Qe(e).Element;return e instanceof t||e instanceof Element}function Ge(e){var t=Qe(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function fr(e){if(typeof ShadowRoot>"u")return!1;var t=Qe(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var sn=Math.round;function uo(){var e=navigator.userAgentData;return e!=null&&e.brands?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function Zh(){return!/^((?!chrome|android).)*safari/i.test(uo())}function sa(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var a=e.getBoundingClientRect(),o=1,l=1;t&&Ge(e)&&(o=e.offsetWidth>0&&sn(a.width)/e.offsetWidth||1,l=e.offsetHeight>0&&sn(a.height)/e.offsetHeight||1);var c=Ao(e)?Qe(e):window,r=c.visualViewport,u=!Zh()&&n,s=(a.left+(u&&r?r.offsetLeft:0))/o,d=(a.top+(u&&r?r.offsetTop:0))/l,f=a.width/o,v=a.height/l;return{width:f,height:v,top:d,right:s+f,bottom:d+v,left:s,x:s,y:d}}function vr(e){var t=Qe(e),n=t.pageXOffset,a=t.pageYOffset;return{scrollLeft:n,scrollTop:a}}function Jh(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Qh(e){return e===Qe(e)||!Ge(e)?vr(e):Jh(e)}function ht(e){return e?(e.nodeName||"").toLowerCase():null}function Da(e){return((Ao(e)?e.ownerDocument:e.document)||window.document).documentElement}function em(e){return sa(Da(e)).left+vr(e).scrollLeft}function mt(e){return Qe(e).getComputedStyle(e)}function po(e){var t=mt(e),n=t.overflow,a=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+a)}function tm(e){var t=e.getBoundingClientRect(),n=sn(t.width)/e.offsetWidth||1,a=sn(t.height)/e.offsetHeight||1;return n!==1||a!==1}function nm(e,t,n){n===void 0&&(n=!1);var a=Ge(t),o=Ge(t)&&tm(t),l=Da(t),c=sa(e,o,n),r={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(a||!a&&!n)&&((ht(t)!=="body"||po(l))&&(r=Qh(t)),Ge(t)?(u=sa(t,!0),u.x+=t.clientLeft,u.y+=t.clientTop):l&&(u.x=em(l))),{x:c.left+r.scrollLeft-u.x,y:c.top+r.scrollTop-u.y,width:c.width,height:c.height}}function am(e){var t=sa(e),n=e.offsetWidth,a=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-a)<=1&&(a=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:a}}function Ro(e){return ht(e)==="html"?e:e.assignedSlot||e.parentNode||(fr(e)?e.host:null)||Da(e)}function hr(e){return["html","body","#document"].indexOf(ht(e))>=0?e.ownerDocument.body:Ge(e)&&po(e)?e:hr(Ro(e))}function na(e,t){var n;t===void 0&&(t=[]);var a=hr(e),o=a===((n=e.ownerDocument)==null?void 0:n.body),l=Qe(a),c=o?[l].concat(l.visualViewport||[],po(a)?a:[]):a,r=t.concat(c);return o?r:r.concat(na(Ro(c)))}function om(e){return["table","td","th"].indexOf(ht(e))>=0}function Pl(e){return!Ge(e)||mt(e).position==="fixed"?null:e.offsetParent}function lm(e){var t=/firefox/i.test(uo()),n=/Trident/i.test(uo());if(n&&Ge(e)){var a=mt(e);if(a.position==="fixed")return null}var o=Ro(e);for(fr(o)&&(o=o.host);Ge(o)&&["html","body"].indexOf(ht(o))<0;){var l=mt(o);if(l.transform!=="none"||l.perspective!=="none"||l.contain==="paint"||["transform","perspective"].indexOf(l.willChange)!==-1||t&&l.willChange==="filter"||t&&l.filter&&l.filter!=="none")return o;o=o.parentNode}return null}function mr(e){for(var t=Qe(e),n=Pl(e);n&&om(n)&&mt(n).position==="static";)n=Pl(n);return n&&(ht(n)==="html"||ht(n)==="body"&&mt(n).position==="static")?t:n||lm(e)||t}var on="top",ca="bottom",Pn="right",Mt="left",gr="auto",im=[on,ca,Pn,Mt],br="start",ua="end",rm=[].concat(im,[gr]).reduce(function(e,t){return e.concat([t,t+"-"+br,t+"-"+ua])},[]),sm="beforeRead",cm="read",um="afterRead",dm="beforeMain",fm="main",vm="afterMain",hm="beforeWrite",mm="write",gm="afterWrite",fo=[sm,cm,um,dm,fm,vm,hm,mm,gm];function bm(e){var t=new Map,n=new Set,a=[];e.forEach(function(l){t.set(l.name,l)});function o(l){n.add(l.name);var c=[].concat(l.requires||[],l.requiresIfExists||[]);c.forEach(function(r){if(!n.has(r)){var u=t.get(r);u&&o(u)}}),a.push(l)}return e.forEach(function(l){n.has(l.name)||o(l)}),a}function ym(e){var t=bm(e);return fo.reduce(function(n,a){return n.concat(t.filter(function(o){return o.phase===a}))},[])}function wm(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function Tt(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];return[].concat(n).reduce(function(o,l){return o.replace(/%s/,l)},e)}var Rt='Popper: modifier "%s" provided an invalid %s property, expected %s but got %s',xm='Popper: modifier "%s" requires "%s", but "%s" modifier is not available',Ol=["name","enabled","phase","fn","effect","requires","options"];function Sm(e){e.forEach(function(t){[].concat(Object.keys(t),Ol).filter(function(n,a,o){return o.indexOf(n)===a}).forEach(function(n){switch(n){case"name":typeof t.name!="string"&&console.error(Tt(Rt,String(t.name),'"name"','"string"','"'+String(t.name)+'"'));break;case"enabled":typeof t.enabled!="boolean"&&console.error(Tt(Rt,t.name,'"enabled"','"boolean"','"'+String(t.enabled)+'"'));break;case"phase":fo.indexOf(t.phase)<0&&console.error(Tt(Rt,t.name,'"phase"',"either "+fo.join(", "),'"'+String(t.phase)+'"'));break;case"fn":typeof t.fn!="function"&&console.error(Tt(Rt,t.name,'"fn"','"function"','"'+String(t.fn)+'"'));break;case"effect":t.effect!=null&&typeof t.effect!="function"&&console.error(Tt(Rt,t.name,'"effect"','"function"','"'+String(t.fn)+'"'));break;case"requires":t.requires!=null&&!Array.isArray(t.requires)&&console.error(Tt(Rt,t.name,'"requires"','"array"','"'+String(t.requires)+'"'));break;case"requiresIfExists":Array.isArray(t.requiresIfExists)||console.error(Tt(Rt,t.name,'"requiresIfExists"','"array"','"'+String(t.requiresIfExists)+'"'));break;case"options":case"data":break;default:console.error('PopperJS: an invalid property has been provided to the "'+t.name+'" modifier, valid properties are '+Ol.map(function(a){return'"'+a+'"'}).join(", ")+'; but "'+n+'" was provided.')}t.requires&&t.requires.forEach(function(a){e.find(function(o){return o.name===a})==null&&console.error(Tt(xm,String(t.name),a,a))})})})}function Cm(e,t){var n=new Set;return e.filter(function(a){var o=t(a);if(!n.has(o))return n.add(o),!0})}function Ea(e){return e.split("-")[0]}function km(e){var t=e.reduce(function(n,a){var o=n[a.name];return n[a.name]=o?Object.assign({},o,a,{options:Object.assign({},o.options,a.options),data:Object.assign({},o.data,a.data)}):a,n},{});return Object.keys(t).map(function(n){return t[n]})}function yr(e){return e.split("-")[1]}function Tm(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function $m(e){var t=e.reference,n=e.element,a=e.placement,o=a?Ea(a):null,l=a?yr(a):null,c=t.x+t.width/2-n.width/2,r=t.y+t.height/2-n.height/2,u;switch(o){case on:u={x:c,y:t.y-n.height};break;case ca:u={x:c,y:t.y+t.height};break;case Pn:u={x:t.x+t.width,y:r};break;case Mt:u={x:t.x-n.width,y:r};break;default:u={x:t.x,y:t.y}}var s=o?Tm(o):null;if(s!=null){var d=s==="y"?"height":"width";switch(l){case br:u[s]=u[s]-(t[d]/2-n[d]/2);break;case ua:u[s]=u[s]+(t[d]/2-n[d]/2);break}}return u}var Al="Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.",Bm="Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.",pl={placement:"bottom",modifiers:[],strategy:"absolute"};function Rl(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function Im(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,a=n===void 0?[]:n,o=t.defaultOptions,l=o===void 0?pl:o;return function(r,u,s){s===void 0&&(s=l);var d={placement:"bottom",orderedModifiers:[],options:Object.assign({},pl,l),modifiersData:{},elements:{reference:r,popper:u},attributes:{},styles:{}},f=[],v=!1,h={state:d,setOptions:function(w){var b=typeof w=="function"?w(d.options):w;S(),d.options=Object.assign({},l,d.options,b),d.scrollParents={reference:Ao(r)?na(r):r.contextElement?na(r.contextElement):[],popper:na(u)};var C=ym(km([].concat(a,d.options.modifiers)));d.orderedModifiers=C.filter(function(k){return k.enabled});{var T=Cm([].concat(C,d.options.modifiers),function(k){var B=k.name;return B});if(Sm(T),Ea(d.options.placement)===gr){var m=d.orderedModifiers.find(function(k){var B=k.name;return B==="flip"});m||console.error(['Popper: "auto" placements require the "flip" modifier be',"present and enabled to work."].join(" "))}var $=mt(u),I=$.marginTop,x=$.marginRight,E=$.marginBottom,D=$.marginLeft;[I,x,E,D].some(function(k){return parseFloat(k)})&&console.warn(['Popper: CSS "margin" styles cannot be used to apply padding',"between the popper and its reference element or boundary.","To replicate margin, use the `offset` modifier, as well as","the `padding` option in the `preventOverflow` and `flip`","modifiers."].join(" "))}return y(),h.update()},forceUpdate:function(){if(!v){var w=d.elements,b=w.reference,C=w.popper;if(!Rl(b,C)){console.error(Al);return}d.rects={reference:nm(b,mr(C),d.options.strategy==="fixed"),popper:am(C)},d.reset=!1,d.placement=d.options.placement,d.orderedModifiers.forEach(function(k){return d.modifiersData[k.name]=Object.assign({},k.data)});for(var T=0,m=0;m<d.orderedModifiers.length;m++){if(T+=1,T>100){console.error(Bm);break}if(d.reset===!0){d.reset=!1,m=-1;continue}var $=d.orderedModifiers[m],I=$.fn,x=$.options,E=x===void 0?{}:x,D=$.name;typeof I=="function"&&(d=I({state:d,options:E,name:D,instance:h})||d)}}},update:wm(function(){return new Promise(function(g){h.forceUpdate(),g(d)})}),destroy:function(){S(),v=!0}};if(!Rl(r,u))return console.error(Al),h;h.setOptions(s).then(function(g){!v&&s.onFirstUpdate&&s.onFirstUpdate(g)});function y(){d.orderedModifiers.forEach(function(g){var w=g.name,b=g.options,C=b===void 0?{}:b,T=g.effect;if(typeof T=="function"){var m=T({state:d,name:w,instance:h,options:C}),$=function(){};f.push(m||$)}})}function S(){f.forEach(function(g){return g()}),f=[]}return h}}var Kn={passive:!0};function Dm(e){var t=e.state,n=e.instance,a=e.options,o=a.scroll,l=o===void 0?!0:o,c=a.resize,r=c===void 0?!0:c,u=Qe(t.elements.popper),s=[].concat(t.scrollParents.reference,t.scrollParents.popper);return l&&s.forEach(function(d){d.addEventListener("scroll",n.update,Kn)}),r&&u.addEventListener("resize",n.update,Kn),function(){l&&s.forEach(function(d){d.removeEventListener("scroll",n.update,Kn)}),r&&u.removeEventListener("resize",n.update,Kn)}}var Em={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Dm,data:{}};function Pm(e){var t=e.state,n=e.name;t.modifiersData[n]=$m({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}var Om={name:"popperOffsets",enabled:!0,phase:"read",fn:Pm,data:{}},Am={top:"auto",right:"auto",bottom:"auto",left:"auto"};function pm(e){var t=e.x,n=e.y,a=window,o=a.devicePixelRatio||1;return{x:sn(t*o)/o||0,y:sn(n*o)/o||0}}function _l(e){var t,n=e.popper,a=e.popperRect,o=e.placement,l=e.variation,c=e.offsets,r=e.position,u=e.gpuAcceleration,s=e.adaptive,d=e.roundOffsets,f=e.isFixed,v=c.x,h=v===void 0?0:v,y=c.y,S=y===void 0?0:y,g=typeof d=="function"?d({x:h,y:S}):{x:h,y:S};h=g.x,S=g.y;var w=c.hasOwnProperty("x"),b=c.hasOwnProperty("y"),C=Mt,T=on,m=window;if(s){var $=mr(n),I="clientHeight",x="clientWidth";if($===Qe(n)&&($=Da(n),mt($).position!=="static"&&r==="absolute"&&(I="scrollHeight",x="scrollWidth")),$=$,o===on||(o===Mt||o===Pn)&&l===ua){T=ca;var E=f&&$===m&&m.visualViewport?m.visualViewport.height:$[I];S-=E-a.height,S*=u?1:-1}if(o===Mt||(o===on||o===ca)&&l===ua){C=Pn;var D=f&&$===m&&m.visualViewport?m.visualViewport.width:$[x];h-=D-a.width,h*=u?1:-1}}var k=Object.assign({position:r},s&&Am),B=d===!0?pm({x:h,y:S}):{x:h,y:S};if(h=B.x,S=B.y,u){var p;return Object.assign({},k,(p={},p[T]=b?"0":"",p[C]=w?"0":"",p.transform=(m.devicePixelRatio||1)<=1?"translate("+h+"px, "+S+"px)":"translate3d("+h+"px, "+S+"px, 0)",p))}return Object.assign({},k,(t={},t[T]=b?S+"px":"",t[C]=w?h+"px":"",t.transform="",t))}function Rm(e){var t=e.state,n=e.options,a=n.gpuAcceleration,o=a===void 0?!0:a,l=n.adaptive,c=l===void 0?!0:l,r=n.roundOffsets,u=r===void 0?!0:r;{var s=mt(t.elements.popper).transitionProperty||"";c&&["transform","top","right","bottom","left"].some(function(f){return s.indexOf(f)>=0})&&console.warn(["Popper: Detected CSS transitions on at least one of the following",'CSS properties: "transform", "top", "right", "bottom", "left".',"\n\n",'Disable the "computeStyles" modifier\'s `adaptive` option to allow',"for smooth transitions, or remove these properties from the CSS","transition declaration on the popper element if only transitioning","opacity or background-color for example.","\n\n","We recommend using the popper element as a wrapper around an inner","element that can have any CSS property transitioned for animations."].join(" "))}var d={placement:Ea(t.placement),variation:yr(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,_l(Object.assign({},d,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:c,roundOffsets:u})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,_l(Object.assign({},d,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:u})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var _m={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Rm,data:{}};function Vm(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var a=t.styles[n]||{},o=t.attributes[n]||{},l=t.elements[n];!Ge(l)||!ht(l)||(Object.assign(l.style,a),Object.keys(o).forEach(function(c){var r=o[c];r===!1?l.removeAttribute(c):l.setAttribute(c,r===!0?"":r)}))})}function Lm(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(a){var o=t.elements[a],l=t.attributes[a]||{},c=Object.keys(t.styles.hasOwnProperty(a)?t.styles[a]:n[a]),r=c.reduce(function(u,s){return u[s]="",u},{});!Ge(o)||!ht(o)||(Object.assign(o.style,r),Object.keys(l).forEach(function(u){o.removeAttribute(u)}))})}}var Mm={name:"applyStyles",enabled:!0,phase:"write",fn:Vm,effect:Lm,requires:["computeStyles"]},zm=[Em,Om,_m,Mm],Fm=Im({defaultModifiers:zm});function Nm(e,t,n){var a=Ea(e),o=[Mt,on].indexOf(a)>=0?-1:1,l=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,c=l[0],r=l[1];return c=c||0,r=(r||0)*o,[Mt,Pn].indexOf(a)>=0?{x:r,y:c}:{x:c,y:r}}function Hm(e){var t=e.state,n=e.options,a=e.name,o=n.offset,l=o===void 0?[0,0]:o,c=rm.reduce(function(d,f){return d[f]=Nm(f,t.rects,l),d},{}),r=c[t.placement],u=r.x,s=r.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=u,t.modifiersData.popperOffsets.y+=s),t.modifiersData[a]=c}var Wm={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Hm};const[jm,_t]=V("popover"),Ym=["overlay","duration","teleport","overlayStyle","overlayClass","closeOnClickOverlay"],Um={show:Boolean,theme:H("light"),overlay:Boolean,actions:me(),actionsDirection:H("vertical"),trigger:H("click"),duration:M,showArrow:R,placement:H("bottom"),iconPrefix:String,overlayClass:ge,overlayStyle:Object,closeOnClickAction:R,closeOnClickOverlay:R,closeOnClickOutside:R,offset:{type:Array,default:()=>[0,8]},teleport:{type:[String,Object],default:"body"}};var Xm=_({name:jm,props:Um,emits:["select","touchstart","update:show"],setup(e,{emit:t,slots:n,attrs:a}){let o;const l=O(),c=O(),r=O(),u=xo(()=>e.show,b=>t("update:show",b)),s=()=>({placement:e.placement,modifiers:[{name:"computeStyles",options:{adaptive:!1,gpuAcceleration:!1}},J({},Wm,{options:{offset:e.offset}})]}),d=()=>c.value&&r.value?Fm(c.value,r.value.popupRef.value,s()):null,f=()=>{le(()=>{u.value&&(o?o.setOptions(s()):(o=d(),_e&&(window.addEventListener("animationend",f),window.addEventListener("transitionend",f))))})},v=b=>{u.value=b},h=()=>{e.trigger==="click"&&(u.value=!u.value)},y=(b,C)=>{b.disabled||(t("select",b,C),e.closeOnClickAction&&(u.value=!1))},S=()=>{u.value&&e.closeOnClickOutside&&(!e.overlay||e.closeOnClickOverlay)&&(u.value=!1)},g=(b,C)=>n.action?n.action({action:b,index:C}):[b.icon&&i(ae,{name:b.icon,classPrefix:e.iconPrefix,class:_t("action-icon")},null),i("div",{class:[_t("action-text"),{[go]:e.actionsDirection==="vertical"}]},[b.text])],w=(b,C)=>{const{icon:T,color:m,disabled:$,className:I}=b;return i("div",{role:"menuitem",class:[_t("action",{disabled:$,"with-icon":T}),{[ws]:e.actionsDirection==="horizontal"},I],style:{color:m},tabindex:$?void 0:0,"aria-disabled":$||void 0,onClick:()=>y(b,C)},[g(b,C)])};return we(()=>{f(),cn(()=>{var b;l.value=(b=r.value)==null?void 0:b.popupRef.value})}),Dt(()=>{o&&(_e&&(window.removeEventListener("animationend",f),window.removeEventListener("transitionend",f)),o.destroy(),o=null)}),j(()=>[u.value,e.offset,e.placement],f),ha([c,l],S,{eventName:"touchstart"}),()=>{var b;return i(ft,null,[i("span",{ref:c,class:_t("wrapper"),onClick:h},[(b=n.reference)==null?void 0:b.call(n)]),i(Ze,ie({ref:r,show:u.value,class:_t([e.theme]),position:"",transition:"van-popover-zoom",lockScroll:!1,"onUpdate:show":v},a,ia(),ue(e,Ym)),{default:()=>[e.showArrow&&i("div",{class:_t("arrow")},null),i("div",{role:"menu",class:_t("content",e.actionsDirection)},[n.default?n.default():e.actions.map(w)])]})])}}});const qm=N(Xm),[Gm,Ya]=V("progress"),Km={color:String,inactive:Boolean,pivotText:String,textColor:String,showPivot:R,pivotColor:String,trackColor:String,strokeWidth:M,percentage:{type:M,default:0,validator:e=>+e>=0&&+e<=100}};var Zm=_({name:Gm,props:Km,setup(e){const t=A(()=>e.inactive?void 0:e.color),n=()=>{const{textColor:a,pivotText:o,pivotColor:l,percentage:c}=e,r=o!=null?o:"".concat(c,"%");if(e.showPivot&&r){const u={color:a,left:"".concat(+c,"%"),transform:"translate(-".concat(+c,"%,-50%)"),background:l||t.value};return i("span",{style:u,class:Ya("pivot",{inactive:e.inactive})},[r])}};return()=>{const{trackColor:a,percentage:o,strokeWidth:l}=e,c={background:a,height:ne(l)},r={width:"".concat(o,"%"),background:t.value};return i("div",{class:Ya(),style:c},[i("span",{class:Ya("portion",{inactive:e.inactive}),style:r},null),n()])}}});const Jm=N(Zm),[Qm,yn,eg]=V("pull-refresh"),wr=50,tg=["pulling","loosing","success"],ng={disabled:Boolean,modelValue:Boolean,headHeight:U(wr),successText:String,pullingText:String,loosingText:String,loadingText:String,pullDistance:M,successDuration:U(500),animationDuration:U(300)};var ag=_({name:Qm,props:ng,emits:["change","refresh","update:modelValue"],setup(e,{emit:t,slots:n}){let a;const o=O(),l=O(),c=dn(o),r=xe({status:"normal",distance:0,duration:0}),u=He(),s=()=>{if(e.headHeight!==wr)return{height:"".concat(e.headHeight,"px")}},d=()=>r.status!=="loading"&&r.status!=="success"&&!e.disabled,f=T=>{const m=+(e.pullDistance||e.headHeight);return T>m&&(T<m*2?T=m+(T-m)/2:T=m*1.5+(T-m*2)/4),Math.round(T)},v=(T,m)=>{const $=+(e.pullDistance||e.headHeight);r.distance=T,m?r.status="loading":T===0?r.status="normal":T<$?r.status="pulling":r.status="loosing",t("change",{status:r.status,distance:T})},h=()=>{const{status:T}=r;return T==="normal"?"":e["".concat(T,"Text")]||eg(T)},y=()=>{const{status:T,distance:m}=r;if(n[T])return n[T]({distance:m});const $=[];return tg.includes(T)&&$.push(i("div",{class:yn("text")},[h()])),T==="loading"&&$.push(i(Ke,{class:yn("loading")},{default:h})),$},S=()=>{r.status="success",setTimeout(()=>{v(0)},+e.successDuration)},g=T=>{a=vt(c.value)===0,a&&(r.duration=0,u.start(T))},w=T=>{d()&&g(T)},b=T=>{if(d()){a||g(T);const{deltaY:m}=u;u.move(T),a&&m.value>=0&&u.isVertical()&&(ve(T),v(f(m.value)))}},C=()=>{a&&u.deltaY.value&&d()&&(r.duration=+e.animationDuration,r.status==="loosing"?(v(+e.headHeight,!0),t("update:modelValue",!0),le(()=>t("refresh"))):v(0))};return j(()=>e.modelValue,T=>{r.duration=+e.animationDuration,T?v(+e.headHeight,!0):n.success||e.successText?S():v(0,!1)}),ye("touchmove",b,{target:l}),()=>{var T;const m={transitionDuration:"".concat(r.duration,"ms"),transform:r.distance?"translate3d(0,".concat(r.distance,"px, 0)"):""};return i("div",{ref:o,class:yn()},[i("div",{ref:l,class:yn("track"),style:m,onTouchstartPassive:w,onTouchend:C,onTouchcancel:C},[i("div",{class:yn("head"),style:s()},[y()]),(T=n.default)==null?void 0:T.call(n)])])}}});const og=N(ag),[lg,Zn]=V("rate");function ig(e,t,n,a){return e>=t?{status:"full",value:1}:e+.5>=t&&n&&!a?{status:"half",value:.5}:e+1>=t&&n&&a?{status:"half",value:Math.round((e-t+1)*1e10)/1e10}:{status:"void",value:0}}const rg={size:M,icon:H("star"),color:String,count:U(5),gutter:M,clearable:Boolean,readonly:Boolean,disabled:Boolean,voidIcon:H("star-o"),allowHalf:Boolean,voidColor:String,touchable:R,iconPrefix:String,modelValue:Ce(0),disabledColor:String};var sg=_({name:lg,props:rg,emits:["change","update:modelValue"],setup(e,{emit:t}){const n=He(),[a,o]=pn(),l=O(),c=A(()=>e.readonly||e.disabled),r=A(()=>c.value||!e.touchable),u=A(()=>Array(+e.count).fill("").map((C,T)=>ig(e.modelValue,T+1,e.allowHalf,e.readonly)));let s,d,f=Number.MAX_SAFE_INTEGER,v=Number.MIN_SAFE_INTEGER;const h=()=>{d=ce(l);const C=a.value.map(ce);s=[],C.forEach((T,m)=>{f=Math.min(T.top,f),v=Math.max(T.top,v),e.allowHalf?s.push({score:m+.5,left:T.left,top:T.top,height:T.height},{score:m+1,left:T.left+T.width/2,top:T.top,height:T.height}):s.push({score:m+1,left:T.left,top:T.top,height:T.height})})},y=(C,T)=>{for(let m=s.length-1;m>0;m--)if(T>=d.top&&T<=d.bottom){if(C>s[m].left&&T>=s[m].top&&T<=s[m].top+s[m].height)return s[m].score}else{const $=T<d.top?f:v;if(C>s[m].left&&s[m].top===$)return s[m].score}return e.allowHalf?.5:1},S=C=>{c.value||C===e.modelValue||(t("update:modelValue",C),t("change",C))},g=C=>{r.value||(n.start(C),h())},w=C=>{if(!r.value&&(n.move(C),n.isHorizontal()&&!n.isTap.value)){const{clientX:T,clientY:m}=C.touches[0];ve(C),S(y(T,m))}},b=(C,T)=>{const{icon:m,size:$,color:I,count:x,gutter:E,voidIcon:D,disabled:k,voidColor:B,allowHalf:p,iconPrefix:Y,disabledColor:q}=e,F=T+1,Q=C.status==="full",ee=C.status==="void",de=p&&C.value>0&&C.value<1;let he;E&&F!==+x&&(he={paddingRight:ne(E)});const G=L=>{h();let W=p?y(L.clientX,L.clientY):F;e.clearable&&n.isTap.value&&W===e.modelValue&&(W=0),S(W)};return i("div",{key:T,ref:o(T),role:"radio",style:he,class:Zn("item"),tabindex:k?void 0:0,"aria-setsize":x,"aria-posinset":F,"aria-checked":!ee,onClick:G},[i(ae,{size:$,name:Q?m:D,class:Zn("icon",{disabled:k,full:Q}),color:k?q:Q?I:B,classPrefix:Y},null),de&&i(ae,{size:$,style:{width:C.value+"em"},name:ee?D:m,class:Zn("icon",["half",{disabled:k,full:!ee}]),color:k?q:ee?B:I,classPrefix:Y},null)])};return Et(()=>e.modelValue),ye("touchmove",w,{target:l}),()=>i("div",{ref:l,role:"radiogroup",class:Zn({readonly:e.readonly,disabled:e.disabled}),tabindex:e.disabled?void 0:0,"aria-disabled":e.disabled,"aria-readonly":e.readonly,onTouchstartPassive:g},[u.value.map(b)])}});const cg=N(sg),ug={figureArr:me(),delay:Number,duration:Ce(2),isStart:Boolean,direction:H("down"),height:Ce(40)},[dg,Ua]=V("rolling-text-item");var fg=_({name:dg,props:ug,setup(e){const t=A(()=>e.direction==="down"?e.figureArr.slice().reverse():e.figureArr),n=A(()=>{const l=e.height*(e.figureArr.length-1);return"-".concat(l,"px")}),a=A(()=>({lineHeight:ne(e.height)})),o=A(()=>({height:ne(e.height),"--van-translate":n.value,"--van-duration":e.duration+"s","--van-delay":e.delay+"s"}));return()=>i("div",{class:Ua([e.direction]),style:o.value},[i("div",{class:Ua("box",{animate:e.isStart})},[Array.isArray(t.value)&&t.value.map(l=>i("div",{class:Ua("item"),style:a.value},[l]))])])}});const[vg,hg]=V("rolling-text"),mg={startNum:Ce(0),targetNum:Number,textList:me(),duration:Ce(2),autoStart:R,direction:H("down"),stopOrder:H("ltr"),height:Ce(40)},gg=2;var bg=_({name:vg,props:mg,setup(e){const t=A(()=>Array.isArray(e.textList)&&e.textList.length),n=A(()=>t.value?e.textList[0].length:"".concat(Math.max(e.startNum,e.targetNum)).length),a=f=>{const v=[];for(let h=0;h<e.textList.length;h++)v.push(e.textList[h][f]);return v},o=A(()=>t.value?new Array(n.value).fill(""):Xe(e.targetNum,n.value).split("")),l=A(()=>Xe(e.startNum,n.value).split("")),c=f=>{const v=+l.value[f],h=+o.value[f],y=[];for(let S=v;S<=9;S++)y.push(S);for(let S=0;S<=gg;S++)for(let g=0;g<=9;g++)y.push(g);for(let S=0;S<=h;S++)y.push(S);return y},r=(f,v)=>e.stopOrder==="ltr"?.2*f:.2*(v-1-f),u=O(e.autoStart),s=()=>{u.value=!0},d=()=>{u.value=!1,e.autoStart&&De(()=>s())};return j(()=>e.autoStart,f=>{f&&s()}),re({start:s,reset:d}),()=>i("div",{class:hg()},[o.value.map((f,v)=>i(fg,{figureArr:t.value?a(v):c(v),duration:e.duration,direction:e.direction,isStart:u.value,height:e.height,delay:r(v,n.value)},null))])}});const yg=N(bg),wg=N(of),[xg,wn,Sg]=V("search"),Cg=J({},To,{label:String,shape:H("square"),leftIcon:H("search"),clearable:R,actionText:String,background:String,showAction:Boolean});var kg=_({name:xg,props:Cg,emits:["blur","focus","clear","search","cancel","clickInput","clickLeftIcon","clickRightIcon","update:modelValue"],setup(e,{emit:t,slots:n,attrs:a}){const o=vn(),l=O(),c=()=>{n.action||(t("update:modelValue",""),t("cancel"))},r=m=>{m.keyCode===13&&(ve(m),t("search",e.modelValue))},u=()=>e.id||"".concat(o,"-input"),s=()=>{if(n.label||e.label)return i("label",{class:wn("label"),for:u(),"data-allow-mismatch":"attribute"},[n.label?n.label():e.label])},d=()=>{if(e.showAction){const m=e.actionText||Sg("cancel");return i("div",{class:wn("action"),role:"button",tabindex:0,onClick:c},[n.action?n.action():m])}},f=()=>{var m;return(m=l.value)==null?void 0:m.blur()},v=()=>{var m;return(m=l.value)==null?void 0:m.focus()},h=m=>t("blur",m),y=m=>t("focus",m),S=m=>t("clear",m),g=m=>t("clickInput",m),w=m=>t("clickLeftIcon",m),b=m=>t("clickRightIcon",m),C=Object.keys(To),T=()=>{const m=J({},a,ue(e,C),{id:u()}),$=I=>t("update:modelValue",I);return i(ut,ie({ref:l,type:"search",class:wn("field",{"with-message":m.errorMessage}),border:!1,onBlur:h,onFocus:y,onClear:S,onKeypress:r,onClickInput:g,onClickLeftIcon:w,onClickRightIcon:b,"onUpdate:modelValue":$},m),ue(n,["left-icon","right-icon"]))};return re({focus:v,blur:f}),()=>{var m;return i("div",{class:wn({"show-action":e.showAction}),style:{background:e.background}},[(m=n.left)==null?void 0:m.call(n),i("div",{class:wn("content",e.shape)},[s(),T()]),d()])}}});const Tg=N(kg),$g=e=>e==null?void 0:e.includes("/"),Bg=[...yo,"round","closeOnPopstate","safeAreaInsetBottom"],Ig={qq:"qq",link:"link-o",weibo:"weibo",qrcode:"qr",poster:"photo-o",wechat:"wechat","weapp-qrcode":"miniprogram-o","wechat-moments":"wechat-moments"},[Dg,Ye,Eg]=V("share-sheet"),Pg=J({},fn,{title:String,round:R,options:me(),cancelText:String,description:String,closeOnPopstate:R,safeAreaInsetBottom:R});var Og=_({name:Dg,props:Pg,emits:["cancel","select","update:show"],setup(e,{emit:t,slots:n}){const a=v=>t("update:show",v),o=()=>{a(!1),t("cancel")},l=(v,h)=>t("select",v,h),c=()=>{const v=n.title?n.title():e.title,h=n.description?n.description():e.description;if(v||h)return i("div",{class:Ye("header")},[v&&i("h2",{class:Ye("title")},[v]),h&&i("span",{class:Ye("description")},[h])])},r=v=>$g(v)?i("img",{src:v,class:Ye("image-icon")},null):i("div",{class:Ye("icon",[v])},[i(ae,{name:Ig[v]||v},null)]),u=(v,h)=>{const{name:y,icon:S,className:g,description:w}=v;return i("div",{role:"button",tabindex:0,class:[Ye("option"),g,Ee],onClick:()=>l(v,h)},[r(S),y&&i("span",{class:Ye("name")},[y]),w&&i("span",{class:Ye("option-description")},[w])])},s=(v,h)=>i("div",{class:Ye("options",{border:h})},[v.map(u)]),d=()=>{const{options:v}=e;return Array.isArray(v[0])?v.map((h,y)=>s(h,y!==0)):s(v)},f=()=>{var v;const h=(v=e.cancelText)!=null?v:Eg("cancel");if(n.cancel||h)return i("button",{type:"button",class:Ye("cancel"),onClick:o},[n.cancel?n.cancel():h])};return()=>i(Ze,ie({class:Ye(),position:"bottom","onUpdate:show":a},ue(e,Bg)),{default:()=>[c(),d(),f()]})}});const Ag=N(Og),[xr,pg]=V("sidebar"),Sr=Symbol(xr),Rg={modelValue:U(0)};var _g=_({name:xr,props:Rg,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{linkChildren:a}=Oe(Sr),o=()=>+e.modelValue;return a({getActive:o,setActive:c=>{c!==o()&&(t("update:modelValue",c),t("change",c))}}),()=>{var c;return i("div",{role:"tablist",class:pg()},[(c=n.default)==null?void 0:c.call(n)])}}});const Cr=N(_g),[Vg,Vl]=V("sidebar-item"),Lg=J({},Ot,{dot:Boolean,title:String,badge:M,disabled:Boolean,badgeProps:Object});var Mg=_({name:Vg,props:Lg,emits:["click"],setup(e,{emit:t,slots:n}){const a=Nt(),{parent:o,index:l}=Ie(Sr);if(!o)return;const c=()=>{e.disabled||(t("click",l.value),o.setActive(l.value),a())};return()=>{const{dot:r,badge:u,title:s,disabled:d}=e,f=l.value===o.getActive();return i("div",{role:"tab",class:Vl({select:f,disabled:d}),tabindex:d?void 0:0,"aria-selected":f,onClick:c},[i(Ht,ie({dot:r,class:Vl("text"),content:u},e.badgeProps),{default:()=>[n.title?n.title():s]})])}}});const kr=N(Mg),[zg,Xa,Ll]=V("signature"),Fg={tips:String,type:H("png"),penColor:H("#000"),lineWidth:Ce(3),clearButtonText:String,backgroundColor:H(""),confirmButtonText:String},Ng=()=>{var e;const t=document.createElement("canvas");return!!((e=t.getContext)!=null&&e.call(t,"2d"))};var Hg=_({name:zg,props:Fg,emits:["submit","clear","start","end","signing"],setup(e,{emit:t,slots:n}){const a=O(),o=O(),l=A(()=>a.value?a.value.getContext("2d"):null),c=_e?Ng():!0;let r=0,u=0,s;const d=()=>{if(!l.value)return!1;l.value.beginPath(),l.value.lineWidth=e.lineWidth,l.value.strokeStyle=e.penColor,s=ce(a),t("start")},f=C=>{if(!l.value)return!1;ve(C);const T=C.touches[0],m=T.clientX-((s==null?void 0:s.left)||0),$=T.clientY-((s==null?void 0:s.top)||0);l.value.lineCap="round",l.value.lineJoin="round",l.value.lineTo(m,$),l.value.stroke(),t("signing",C)},v=C=>{ve(C),t("end")},h=C=>{const T=document.createElement("canvas");if(T.width=C.width,T.height=C.height,e.backgroundColor){const m=T.getContext("2d");y(m)}return C.toDataURL()===T.toDataURL()},y=C=>{C&&e.backgroundColor&&(C.fillStyle=e.backgroundColor,C.fillRect(0,0,r,u))},S=()=>{var C,T;const m=a.value;if(!m)return;const I=h(m)?"":((T=(C={jpg:()=>m.toDataURL("image/jpeg",.8),jpeg:()=>m.toDataURL("image/jpeg",.8)})[e.type])==null?void 0:T.call(C))||m.toDataURL("image/".concat(e.type));t("submit",{image:I,canvas:m})},g=()=>{l.value&&(l.value.clearRect(0,0,r,u),l.value.closePath(),y(l.value)),t("clear")},w=()=>{var C,T,m;if(c&&a.value){const $=a.value,I=_e?window.devicePixelRatio:1;r=$.width=(((C=o.value)==null?void 0:C.offsetWidth)||0)*I,u=$.height=(((T=o.value)==null?void 0:T.offsetHeight)||0)*I,(m=l.value)==null||m.scale(I,I),y(l.value)}},b=()=>{if(l.value){const C=l.value.getImageData(0,0,r,u);w(),l.value.putImageData(C,0,0)}};return j(qe,b),we(w),re({resize:b,clear:g,submit:S}),()=>i("div",{class:Xa()},[i("div",{class:Xa("content"),ref:o},[c?i("canvas",{ref:a,onTouchstartPassive:d,onTouchmove:f,onTouchend:v},null):n.tips?n.tips():i("p",null,[e.tips])]),i("div",{class:Xa("footer")},[i(Pe,{size:"small",onClick:g},{default:()=>[e.clearButtonText||Ll("clear")]}),i(Pe,{type:"primary",size:"small",onClick:S},{default:()=>[e.confirmButtonText||Ll("confirm")]})])])}});const Wg=N(Hg),[jg,Yg]=V("skeleton-title"),Ug={round:Boolean,titleWidth:M};var Xg=_({name:jg,props:Ug,setup(e){return()=>i("h3",{class:Yg([{round:e.round}]),style:{width:ne(e.titleWidth)}},null)}});const Tr=N(Xg);var qg=Tr;const[Gg,Kg]=V("skeleton-avatar"),Zg={avatarSize:M,avatarShape:H("round")};var Jg=_({name:Gg,props:Zg,setup(e){return()=>i("div",{class:Kg([e.avatarShape]),style:yt(e.avatarSize)},null)}});const $r=N(Jg);var Qg=$r;const _o="100%",eb={round:Boolean,rowWidth:{type:M,default:_o}},[tb,nb]=V("skeleton-paragraph");var ab=_({name:tb,props:eb,setup(e){return()=>i("div",{class:nb([{round:e.round}]),style:{width:e.rowWidth}},null)}});const Br=N(ab);var ob=Br;const[lb,Ml]=V("skeleton"),ib="60%",rb={row:U(0),round:Boolean,title:Boolean,titleWidth:M,avatar:Boolean,avatarSize:M,avatarShape:H("round"),loading:R,animate:R,rowWidth:{type:[Number,String,Array],default:_o}};var sb=_({name:lb,inheritAttrs:!1,props:rb,setup(e,{slots:t,attrs:n}){const a=()=>{if(e.avatar)return i(Qg,{avatarShape:e.avatarShape,avatarSize:e.avatarSize},null)},o=()=>{if(e.title)return i(qg,{round:e.round,titleWidth:e.titleWidth},null)},l=u=>{const{rowWidth:s}=e;return s===_o&&u===+e.row-1?ib:Array.isArray(s)?s[u]:s},c=()=>Array(+e.row).fill("").map((u,s)=>i(ob,{key:s,round:e.round,rowWidth:ne(l(s))},null)),r=()=>t.template?t.template():i(ft,null,[a(),i("div",{class:Ml("content")},[o(),c()])]);return()=>{var u;return e.loading?i("div",ie({class:Ml({animate:e.animate,round:e.round})},n),[r()]):(u=t.default)==null?void 0:u.call(t)}}});const cb=N(sb),[ub,zl]=V("skeleton-image"),db={imageSize:M,imageShape:H("square")};var fb=_({name:ub,props:db,setup(e){return()=>i("div",{class:zl([e.imageShape]),style:yt(e.imageSize)},[i(ae,{name:"photo",class:zl("icon")},null)])}});const vb=N(fb),[hb,xn]=V("slider"),mb={min:U(0),max:U(100),step:U(1),range:Boolean,reverse:Boolean,disabled:Boolean,readonly:Boolean,vertical:Boolean,barHeight:M,buttonSize:M,activeColor:String,inactiveColor:String,modelValue:{type:[Number,Array],default:0}};var gb=_({name:hb,props:mb,emits:["change","dragEnd","dragStart","update:modelValue"],setup(e,{emit:t,slots:n}){let a,o,l;const c=O(),r=[O(),O()],u=O(),s=He(),d=A(()=>Number(e.max)-Number(e.min)),f=A(()=>{const k=e.vertical?"width":"height";return{background:e.inactiveColor,[k]:ne(e.barHeight)}}),v=k=>e.range&&Array.isArray(k),h=()=>{const{modelValue:k,min:B}=e;return v(k)?"".concat((k[1]-k[0])*100/d.value,"%"):"".concat((k-Number(B))*100/d.value,"%")},y=()=>{const{modelValue:k,min:B}=e;return v(k)?"".concat((k[0]-Number(B))*100/d.value,"%"):"0%"},S=A(()=>{const B={[e.vertical?"height":"width"]:h(),background:e.activeColor};u.value&&(B.transition="none");const p=()=>e.vertical?e.reverse?"bottom":"top":e.reverse?"right":"left";return B[p()]=y(),B}),g=k=>{const B=+e.min,p=+e.max,Y=+e.step;k=ke(k,B,p);const q=Math.round((k-B)/Y)*Y;return ai(B,q)},w=()=>{const k=e.modelValue;v(k)?l=k.map(g):l=g(k)},b=k=>{var B,p;const Y=(B=k[0])!=null?B:Number(e.min),q=(p=k[1])!=null?p:Number(e.max);return Y>q?[q,Y]:[Y,q]},C=(k,B)=>{v(k)?k=b(k).map(g):k=g(k),at(k,e.modelValue)||t("update:modelValue",k),B&&!at(k,l)&&t("change",k)},T=k=>{if(k.stopPropagation(),e.disabled||e.readonly)return;w();const{min:B,reverse:p,vertical:Y,modelValue:q}=e,F=ce(c),Q=()=>Y?p?F.bottom-k.clientY:k.clientY-F.top:p?F.right-k.clientX:k.clientX-F.left,ee=Y?F.height:F.width,de=Number(B)+Q()/ee*d.value;if(v(q)){const[he,G]=q,L=(he+G)/2;de<=L?C([de,G],!0):C([he,de],!0)}else C(de,!0)},m=k=>{e.disabled||e.readonly||(s.start(k),o=e.modelValue,w(),u.value="start")},$=k=>{if(e.disabled||e.readonly)return;u.value==="start"&&t("dragStart",k),ve(k,!0),s.move(k),u.value="dragging";const B=ce(c),p=e.vertical?s.deltaY.value:s.deltaX.value,Y=e.vertical?B.height:B.width;let q=p/Y*d.value;if(e.reverse&&(q=-q),v(l)){const F=e.reverse?1-a:a;o[F]=l[F]+q}else o=l+q;C(o)},I=k=>{e.disabled||e.readonly||(u.value==="dragging"&&(C(o,!0),t("dragEnd",k)),u.value="")},x=k=>typeof k=="number"?xn("button-wrapper",["left","right"][k]):xn("button-wrapper",e.reverse?"left":"right"),E=(k,B)=>{const p=u.value==="dragging";if(typeof B=="number"){const Y=n[B===0?"left-button":"right-button"];let q;if(p&&Array.isArray(o)&&(q=o[0]>o[1]?a^1:a),Y)return Y({value:k,dragging:p,dragIndex:q})}return n.button?n.button({value:k,dragging:p}):i("div",{class:xn("button"),style:yt(e.buttonSize)},null)},D=k=>{const B=typeof k=="number"?e.modelValue[k]:e.modelValue;return i("div",{ref:r[k!=null?k:0],role:"slider",class:x(k),tabindex:e.disabled?void 0:0,"aria-valuemin":e.min,"aria-valuenow":B,"aria-valuemax":e.max,"aria-disabled":e.disabled||void 0,"aria-readonly":e.readonly||void 0,"aria-orientation":e.vertical?"vertical":"horizontal",onTouchstartPassive:p=>{typeof k=="number"&&(a=k),m(p)},onTouchend:I,onTouchcancel:I,onClick:ho},[E(B,k)])};return C(e.modelValue),Et(()=>e.modelValue),r.forEach(k=>{ye("touchmove",$,{target:k})}),()=>i("div",{ref:c,style:f.value,class:xn({vertical:e.vertical,disabled:e.disabled}),onClick:T},[i("div",{class:xn("bar"),style:S.value},[e.range?[D(0),D(1)]:D()])])}});const bb=N(gb),[Fl,yb]=V("space"),wb={align:String,direction:{type:String,default:"horizontal"},size:{type:[Number,String,Array],default:8},wrap:Boolean,fill:Boolean};function Ir(e=[]){const t=[];return e.forEach(n=>{Array.isArray(n)?t.push(...n):n.type===ft?t.push(...Ir(n.children)):t.push(n)}),t.filter(n=>{var a;return!(n&&(n.type===ql||n.type===ft&&((a=n.children)==null?void 0:a.length)===0||n.type===jr&&n.children.trim()===""))})}var xb=_({name:Fl,props:wb,setup(e,{slots:t}){const n=A(()=>{var l;return(l=e.align)!=null?l:e.direction==="horizontal"?"center":""}),a=l=>typeof l=="number"?l+"px":l,o=l=>{const c={},r="".concat(a(Array.isArray(e.size)?e.size[0]:e.size)),u="".concat(a(Array.isArray(e.size)?e.size[1]:e.size));return l?e.wrap?{marginBottom:u}:{}:(e.direction==="horizontal"&&(c.marginRight=r),(e.direction==="vertical"||e.wrap)&&(c.marginBottom=u),c)};return()=>{var l;const c=Ir((l=t.default)==null?void 0:l.call(t));return i("div",{class:[yb({[e.direction]:e.direction,["align-".concat(n.value)]:n.value,wrap:e.wrap,fill:e.fill})]},[c.map((r,u)=>i("div",{key:"item-".concat(u),class:"".concat(Fl,"-item"),style:o(u===c.length-1)},[r]))])}}});const Sb=N(xb),[Dr,Nl]=V("steps"),Cb={active:U(0),direction:H("horizontal"),activeIcon:H("checked"),iconPrefix:String,finishIcon:String,activeColor:String,inactiveIcon:String,inactiveColor:String},Er=Symbol(Dr);var kb=_({name:Dr,props:Cb,emits:["clickStep"],setup(e,{emit:t,slots:n}){const{linkChildren:a}=Oe(Er);return a({props:e,onClickStep:l=>t("clickStep",l)}),()=>{var l;return i("div",{class:Nl([e.direction])},[i("div",{class:Nl("items")},[(l=n.default)==null?void 0:l.call(n)])])}}});const[Tb,$t]=V("step");var $b=_({name:Tb,setup(e,{slots:t}){const{parent:n,index:a}=Ie(Er);if(!n)return;const o=n.props,l=()=>{const f=+o.active;return a.value<f?"finish":a.value===f?"process":"waiting"},c=()=>l()==="process",r=A(()=>({background:l()==="finish"?o.activeColor:o.inactiveColor})),u=A(()=>{if(c())return{color:o.activeColor};if(l()==="waiting")return{color:o.inactiveColor}}),s=()=>n.onClickStep(a.value),d=()=>{const{iconPrefix:f,finishIcon:v,activeIcon:h,activeColor:y,inactiveIcon:S}=o;return c()?t["active-icon"]?t["active-icon"]():i(ae,{class:$t("icon","active"),name:h,color:y,classPrefix:f},null):l()==="finish"&&(v||t["finish-icon"])?t["finish-icon"]?t["finish-icon"]():i(ae,{class:$t("icon","finish"),name:v,color:y,classPrefix:f},null):t["inactive-icon"]?t["inactive-icon"]():S?i(ae,{class:$t("icon"),name:S,classPrefix:f},null):i("i",{class:$t("circle"),style:r.value},null)};return()=>{var f;const v=l();return i("div",{class:[xt,$t([o.direction,{[v]:v}])]},[i("div",{class:$t("title",{active:c()}),style:u.value,onClick:s},[(f=t.default)==null?void 0:f.call(t)]),i("div",{class:$t("circle-container"),onClick:s},[d()]),i("div",{class:$t("line"),style:r.value},null)])}}});const Bb=N($b),[Ib,Jn]=V("stepper"),Db=200,Qn=(e,t)=>String(e)===String(t),Eb={min:U(1),max:U(1/0),name:U(""),step:U(1),theme:String,integer:Boolean,disabled:Boolean,showPlus:R,showMinus:R,showInput:R,longPress:R,autoFixed:R,allowEmpty:Boolean,modelValue:M,inputWidth:M,buttonSize:M,placeholder:String,disablePlus:Boolean,disableMinus:Boolean,disableInput:Boolean,beforeChange:Function,defaultValue:U(1),decimalLength:M};var Pb=_({name:Ib,props:Eb,emits:["plus","blur","minus","focus","change","overlimit","update:modelValue"],setup(e,{emit:t}){const n=(x,E=!0)=>{const{min:D,max:k,allowEmpty:B,decimalLength:p}=e;return B&&x===""||(x=Qa(String(x),!e.integer),x=x===""?0:+x,x=Number.isNaN(x)?+D:x,x=E?Math.max(Math.min(+k,x),+D):x,se(p)&&(x=x.toFixed(+p))),x},a=()=>{var x;const E=(x=e.modelValue)!=null?x:e.defaultValue,D=n(E);return Qn(D,e.modelValue)||t("update:modelValue",D),D};let o;const l=O(),c=O(a()),r=A(()=>e.disabled||e.disableMinus||+c.value<=+e.min),u=A(()=>e.disabled||e.disablePlus||+c.value>=+e.max),s=A(()=>({width:ne(e.inputWidth),height:ne(e.buttonSize)})),d=A(()=>yt(e.buttonSize)),f=()=>{const x=n(c.value);Qn(x,c.value)||(c.value=x)},v=x=>{e.beforeChange?Pt(e.beforeChange,{args:[x],done(){c.value=x}}):c.value=x},h=()=>{if(o==="plus"&&u.value||o==="minus"&&r.value){t("overlimit",o);return}const x=o==="minus"?-e.step:+e.step,E=n(ai(+c.value,x));v(E),t(o)},y=x=>{const E=x.target,{value:D}=E,{decimalLength:k}=e;let B=Qa(String(D),!e.integer);if(se(k)&&B.includes(".")){const Y=B.split(".");B="".concat(Y[0],".").concat(Y[1].slice(0,+k))}e.beforeChange?E.value=String(c.value):Qn(D,B)||(E.value=B);const p=B===String(+B);v(p?+B:B)},S=x=>{var E;e.disableInput?(E=l.value)==null||E.blur():t("focus",x)},g=x=>{const E=x.target,D=n(E.value,e.autoFixed);E.value=String(D),c.value=D,le(()=>{t("blur",x),ti()})};let w,b;const C=()=>{b=setTimeout(()=>{h(),C()},Db)},T=()=>{e.longPress&&(w=!1,clearTimeout(b),b=setTimeout(()=>{w=!0,h(),C()},ci))},m=x=>{e.longPress&&(clearTimeout(b),w&&ve(x))},$=x=>{e.disableInput&&ve(x)},I=x=>({onClick:E=>{ve(E),o=x,h()},onTouchstartPassive:()=>{o=x,T()},onTouchend:m,onTouchcancel:m});return j(()=>[e.max,e.min,e.integer,e.decimalLength],f),j(()=>e.modelValue,x=>{Qn(x,c.value)||(c.value=n(x))}),j(c,x=>{t("update:modelValue",x),t("change",x,{name:e.name})}),Et(()=>e.modelValue),()=>i("div",{role:"group",class:Jn([e.theme])},[$e(i("button",ie({type:"button",style:d.value,class:[Jn("minus",{disabled:r.value}),{[Ee]:!r.value}],"aria-disabled":r.value||void 0},I("minus")),null),[[Be,e.showMinus]]),$e(i("input",{ref:l,type:e.integer?"tel":"text",role:"spinbutton",class:Jn("input"),value:c.value,style:s.value,disabled:e.disabled,readonly:e.disableInput,inputmode:e.integer?"numeric":"decimal",placeholder:e.placeholder,autocomplete:"off","aria-valuemax":e.max,"aria-valuemin":e.min,"aria-valuenow":c.value,onBlur:g,onInput:y,onFocus:S,onMousedown:$},null),[[Be,e.showInput]]),$e(i("button",ie({type:"button",style:d.value,class:[Jn("plus",{disabled:u.value}),{[Ee]:!u.value}],"aria-disabled":u.value||void 0},I("plus")),null),[[Be,e.showPlus]])])}});const Ob=N(Pb),Ab=N(kb),[pb,Ue,Rb]=V("submit-bar"),_b={tip:String,label:String,price:Number,tipIcon:String,loading:Boolean,currency:H("¥"),disabled:Boolean,textAlign:String,buttonText:String,buttonType:H("danger"),buttonColor:String,suffixLabel:String,placeholder:Boolean,decimalLength:U(2),safeAreaInsetBottom:R};var Vb=_({name:pb,props:_b,emits:["submit"],setup(e,{emit:t,slots:n}){const a=O(),o=ya(a,Ue),l=()=>{const{price:d,label:f,currency:v,textAlign:h,suffixLabel:y,decimalLength:S}=e;if(typeof d=="number"){const g=(d/100).toFixed(+S).split("."),w=S?".".concat(g[1]):"";return i("div",{class:Ue("text"),style:{textAlign:h}},[i("span",null,[f||Rb("label")]),i("span",{class:Ue("price")},[v,i("span",{class:Ue("price-integer")},[g[0]]),w]),y&&i("span",{class:Ue("suffix-label")},[y])])}},c=()=>{var d;const{tip:f,tipIcon:v}=e;if(n.tip||f)return i("div",{class:Ue("tip")},[v&&i(ae,{class:Ue("tip-icon"),name:v},null),f&&i("span",{class:Ue("tip-text")},[f]),(d=n.tip)==null?void 0:d.call(n)])},r=()=>t("submit"),u=()=>n.button?n.button():i(Pe,{round:!0,type:e.buttonType,text:e.buttonText,class:Ue("button",e.buttonType),color:e.buttonColor,loading:e.loading,disabled:e.disabled,onClick:r},null),s=()=>{var d,f;return i("div",{ref:a,class:[Ue(),{"van-safe-area-bottom":e.safeAreaInsetBottom}]},[(d=n.top)==null?void 0:d.call(n),c(),i("div",{class:Ue("bar")},[(f=n.default)==null?void 0:f.call(n),l(),u()])])};return()=>e.placeholder?o(s):s()}});const Lb=N(Vb),[Mb,qa]=V("swipe-cell"),zb={name:U(""),disabled:Boolean,leftWidth:M,rightWidth:M,beforeClose:Function,stopPropagation:Boolean};var Fb=_({name:Mb,props:zb,emits:["open","close","click"],setup(e,{emit:t,slots:n}){let a,o,l,c;const r=O(),u=O(),s=O(),d=xe({offset:0,dragging:!1}),f=He(),v=x=>x.value?ce(x).width:0,h=A(()=>se(e.leftWidth)?+e.leftWidth:v(u)),y=A(()=>se(e.rightWidth)?+e.rightWidth:v(s)),S=x=>{d.offset=x==="left"?h.value:-y.value,a||(a=!0,t("open",{name:e.name,position:x}))},g=x=>{d.offset=0,a&&(a=!1,t("close",{name:e.name,position:x}))},w=x=>{const E=Math.abs(d.offset),D=.15,k=a?1-D:D,B=x==="left"?h.value:y.value;B&&E>B*k?S(x):g(x)},b=x=>{e.disabled||(l=d.offset,f.start(x))},C=x=>{if(e.disabled)return;const{deltaX:E}=f;f.move(x),f.isHorizontal()&&(o=!0,d.dragging=!0,(!a||E.value*l<0)&&ve(x,e.stopPropagation),d.offset=ke(E.value+l,-y.value,h.value))},T=()=>{d.dragging&&(d.dragging=!1,w(d.offset>0?"left":"right"),setTimeout(()=>{o=!1},0))},m=(x="outside",E)=>{c||(t("click",x),a&&!o&&(c=!0,Pt(e.beforeClose,{args:[{event:E,name:e.name,position:x}],done:()=>{c=!1,g(x)},canceled:()=>c=!1,error:()=>c=!1})))},$=x=>E=>{(o||a)&&E.stopPropagation(),!o&&m(x,E)},I=(x,E)=>{const D=n[x];if(D)return i("div",{ref:E,class:qa(x),onClick:$(x)},[D()])};return re({open:S,close:g}),ha(r,x=>m("outside",x),{eventName:"touchstart"}),ye("touchmove",C,{target:r}),()=>{var x;const E={transform:"translate3d(".concat(d.offset,"px, 0, 0)"),transitionDuration:d.dragging?"0s":".6s"};return i("div",{ref:r,class:qa(),onClick:$("cell"),onTouchstartPassive:b,onTouchend:T,onTouchcancel:T},[i("div",{class:qa("wrapper"),style:E},[I("left",u),(x=n.default)==null?void 0:x.call(n),I("right",s)])])}}});const Nb=N(Fb),[Pr,Hl]=V("tabbar"),Hb={route:Boolean,fixed:R,border:R,zIndex:M,placeholder:Boolean,activeColor:String,beforeChange:Function,inactiveColor:String,modelValue:U(0),safeAreaInsetBottom:{type:Boolean,default:null}},Or=Symbol(Pr);var Wb=_({name:Pr,props:Hb,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const a=O(),{linkChildren:o}=Oe(Or),l=ya(a,Hl),c=()=>{var s;return(s=e.safeAreaInsetBottom)!=null?s:e.fixed},r=()=>{var s;const{fixed:d,zIndex:f,border:v}=e;return i("div",{ref:a,role:"tablist",style:wt(f),class:[Hl({fixed:d}),{[ga]:v,"van-safe-area-bottom":c()}]},[(s=n.default)==null?void 0:s.call(n)])};return o({props:e,setActive:(s,d)=>{Pt(e.beforeChange,{args:[s],done(){t("update:modelValue",s),t("change",s),d()}})}}),()=>e.fixed&&e.placeholder?l(r):r()}});const jb=N(Wb),[Yb,Ga]=V("tabbar-item"),Ub=J({},Ot,{dot:Boolean,icon:String,name:M,badge:M,badgeProps:Object,iconPrefix:String});var Xb=_({name:Yb,props:Ub,emits:["click"],setup(e,{emit:t,slots:n}){const a=Nt(),o=ot().proxy,{parent:l,index:c}=Ie(Or);if(!l)return;const r=A(()=>{var d;const{route:f,modelValue:v}=l.props;if(f&&"$route"in o){const{$route:h}=o,{to:y}=e,S=Le(y)?y:{path:y};return h.matched.some(g=>{const w="path"in S&&S.path===g.path,b="name"in S&&S.name===g.name;return w||b})}return((d=e.name)!=null?d:c.value)===v}),u=d=>{var f;r.value||l.setActive((f=e.name)!=null?f:c.value,a),t("click",d)},s=()=>{if(n.icon)return n.icon({active:r.value});if(e.icon)return i(ae,{name:e.icon,classPrefix:e.iconPrefix},null)};return()=>{var d;const{dot:f,badge:v}=e,{activeColor:h,inactiveColor:y}=l.props,S=r.value?h:y;return i("div",{role:"tab",class:Ga({active:r.value}),style:{color:S},tabindex:0,"aria-selected":r.value,onClick:u},[i(Ht,ie({dot:f,class:Ga("icon"),content:v},e.badgeProps),{default:s}),i("div",{class:Ga("text")},[(d=n.default)==null?void 0:d.call(n,{active:r.value})])])}}});const qb=N(Xb),[Gb,Wl]=V("text-ellipsis"),Kb={rows:U(1),dots:H("..."),content:H(""),expandText:H(""),collapseText:H(""),position:H("end")};var Zb=_({name:Gb,props:Kb,emits:["clickAction"],setup(e,{emit:t,slots:n}){const a=O(e.content),o=O(!1),l=O(!1),c=O(),r=O();let u=!1;const s=A(()=>o.value?e.collapseText:e.expandText),d=w=>{if(!w)return 0;const b=w.match(/^\d*(\.\d*)?/);return b?Number(b[0]):0},f=()=>{if(!c.value||!c.value.isConnected)return;const w=window.getComputedStyle(c.value),b=document.createElement("div");return Array.prototype.slice.apply(w).forEach(T=>{b.style.setProperty(T,w.getPropertyValue(T))}),b.style.position="fixed",b.style.zIndex="-9999",b.style.top="-9999px",b.style.height="auto",b.style.minHeight="auto",b.style.maxHeight="auto",b.innerText=e.content,document.body.appendChild(b),b},v=(w,b)=>{var C,T;const{content:m,position:$,dots:I}=e,x=m.length,E=0+x>>1,D=n.action?(T=(C=r.value)==null?void 0:C.outerHTML)!=null?T:"":e.expandText,k=()=>{const p=(Y,q)=>{if(q-Y<=1)return $==="end"?m.slice(0,Y)+I:I+m.slice(q,x);const F=Math.round((Y+q)/2);return $==="end"?w.innerText=m.slice(0,F)+I:w.innerText=I+m.slice(F,x),w.innerHTML+=D,w.offsetHeight>b?$==="end"?p(Y,F):p(F,q):$==="end"?p(F,q):p(Y,F)};return p(0,x)},B=(p,Y)=>{if(p[1]-p[0]<=1&&Y[1]-Y[0]<=1)return m.slice(0,p[0])+I+m.slice(Y[1],x);const q=Math.floor((p[0]+p[1])/2),F=Math.ceil((Y[0]+Y[1])/2);return w.innerText=e.content.slice(0,q)+e.dots+e.content.slice(F,x),w.innerHTML+=D,w.offsetHeight>=b?B([p[0],q],[F,Y[1]]):B([q,p[1]],[Y[0],F])};return e.position==="middle"?B([0,E],[E,x]):k()},h=()=>{const w=f();if(!w){u=!0;return}const{paddingBottom:b,paddingTop:C,lineHeight:T}=w.style,m=Math.ceil((Number(e.rows)+.5)*d(T)+d(C)+d(b));m<w.offsetHeight?(l.value=!0,a.value=v(w,m)):(l.value=!1,a.value=e.content),document.body.removeChild(w)},y=(w=!o.value)=>{o.value=w},S=w=>{y(),t("clickAction",w)},g=()=>{const w=n.action?n.action({expanded:o.value}):s.value;return i("span",{ref:r,class:Wl("action"),onClick:S},[w])};return we(()=>{h(),n.action&&le(h)}),gt(()=>{u&&(u=!1,h())}),j([qe,()=>[e.content,e.rows,e.position]],h),re({toggle:y}),()=>i("div",{ref:c,class:Wl()},[o.value?e.content:a.value,l.value?g():null])}});const Jb=N(Zb),[Qb]=V("time-picker"),jl=e=>/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/.test(e),ey=["hour","minute","second"],ty=J({},qi,{minHour:U(0),maxHour:U(23),minMinute:U(0),maxMinute:U(59),minSecond:U(0),maxSecond:U(59),minTime:{type:String,validator:jl},maxTime:{type:String,validator:jl},columnsType:{type:Array,default:()=>["hour","minute"]}});var ny=_({name:Qb,props:ty,emits:["confirm","cancel","change","update:modelValue"],setup(e,{emit:t,slots:n}){const a=O(e.modelValue),o=O(),l=v=>{const h=v.split(":");return ey.map((y,S)=>e.columnsType.includes(y)?h[S]:"00")},c=()=>{var v;return(v=o.value)==null?void 0:v.confirm()},r=()=>a.value,u=A(()=>{let{minHour:v,maxHour:h,minMinute:y,maxMinute:S,minSecond:g,maxSecond:w}=e;if(e.minTime||e.maxTime){const b={hour:0,minute:0,second:0};e.columnsType.forEach((m,$)=>{var I;b[m]=(I=a.value[$])!=null?I:0});const{hour:C,minute:T}=b;if(e.minTime){const[m,$,I]=l(e.minTime);v=m,y=+C<=+v?$:"00",g=+C<=+v&&+T<=+y?I:"00"}if(e.maxTime){const[m,$,I]=l(e.maxTime);h=m,S=+C>=+h?$:"59",w=+C>=+h&&+T>=+S?I:"59"}}return e.columnsType.map(b=>{const{filter:C,formatter:T}=e;switch(b){case"hour":return nn(+v,+h,b,T,C,a.value);case"minute":return nn(+y,+S,b,T,C,a.value);case"second":return nn(+g,+w,b,T,C,a.value);default:return[]}})});j(a,v=>{at(v,e.modelValue)||t("update:modelValue",v)}),j(()=>e.modelValue,v=>{v=Zi(v,u.value),at(v,a.value)||(a.value=v)},{immediate:!0});const s=(...v)=>t("change",...v),d=(...v)=>t("cancel",...v),f=(...v)=>t("confirm",...v);return re({confirm:c,getSelectedTime:r}),()=>i(Ca,ie({ref:o,modelValue:a.value,"onUpdate:modelValue":v=>a.value=v,columns:u.value,onChange:s,onCancel:d,onConfirm:f},ue(e,Gi)),n)}});const ay=N(ny),[oy,en]=V("tree-select"),ly={max:U(1/0),items:me(),height:U(300),selectedIcon:H("success"),mainActiveIndex:U(0),activeId:{type:[Number,String,Array],default:0}};var iy=_({name:oy,props:ly,emits:["clickNav","clickItem","update:activeId","update:mainActiveIndex"],setup(e,{emit:t,slots:n}){const a=s=>Array.isArray(e.activeId)?e.activeId.includes(s):e.activeId===s,o=s=>{const d=()=>{if(s.disabled)return;let f;if(Array.isArray(e.activeId)){f=e.activeId.slice();const v=f.indexOf(s.id);v!==-1?f.splice(v,1):f.length<+e.max&&f.push(s.id)}else f=s.id;t("update:activeId",f),t("clickItem",s)};return i("div",{key:s.id,class:["van-ellipsis",en("item",{active:a(s.id),disabled:s.disabled})],onClick:d},[s.text,a(s.id)&&i(ae,{name:e.selectedIcon,class:en("selected")},null)])},l=s=>{t("update:mainActiveIndex",s)},c=s=>t("clickNav",s),r=()=>{const s=e.items.map(d=>i(kr,{dot:d.dot,badge:d.badge,class:[en("nav-item"),d.className],disabled:d.disabled,onClick:c},{title:()=>n["nav-text"]?n["nav-text"](d):d.text}));return i(Cr,{class:en("nav"),modelValue:e.mainActiveIndex,onChange:l},{default:()=>[s]})},u=()=>{if(n.content)return n.content();const s=e.items[+e.mainActiveIndex]||{};if(s.children)return s.children.map(o)};return()=>i("div",{class:en(),style:{height:ne(e.height)}},[r(),i("div",{class:en("content")},[u()])])}});const ry=N(iy),[sy,Se,cy]=V("uploader");function Yl(e,t){return new Promise(n=>{if(t==="file"){n();return}const a=new FileReader;a.onload=o=>{n(o.target.result)},t==="dataUrl"?a.readAsDataURL(e):t==="text"&&a.readAsText(e)})}function Ar(e,t){return aa(e).some(n=>n.file?ln(t)?t(n.file):n.file.size>+t:!1)}function uy(e,t){const n=[],a=[];return e.forEach(o=>{Ar(o,t)?a.push(o):n.push(o)}),{valid:n,invalid:a}}const dy=/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg|avif)/i,fy=e=>dy.test(e);function pr(e){return e.isImage?!0:e.file&&e.file.type?e.file.type.indexOf("image")===0:e.url?fy(e.url):typeof e.content=="string"?e.content.indexOf("data:image")===0:!1}var vy=_({props:{name:M,item:Te(Object),index:Number,imageFit:String,lazyLoad:Boolean,deletable:Boolean,reupload:Boolean,previewSize:[Number,String,Array],beforeDelete:Function},emits:["delete","preview","reupload"],setup(e,{emit:t,slots:n}){const a=()=>{const{status:d,message:f}=e.item;if(d==="uploading"||d==="failed"){const v=d==="failed"?i(ae,{name:"close",class:Se("mask-icon")},null):i(Ke,{class:Se("loading")},null),h=se(f)&&f!=="";return i("div",{class:Se("mask")},[v,h&&i("div",{class:Se("mask-message")},[f])])}},o=d=>{const{name:f,item:v,index:h,beforeDelete:y}=e;d.stopPropagation(),Pt(y,{args:[v,{name:f,index:h}],done:()=>t("delete")})},l=()=>t("preview"),c=()=>t("reupload"),r=()=>{if(e.deletable&&e.item.status!=="uploading"){const d=n["preview-delete"];return i("div",{role:"button",class:Se("preview-delete",{shadow:!d}),tabindex:0,"aria-label":cy("delete"),onClick:o},[d?d():i(ae,{name:"cross",class:Se("preview-delete-icon")},null)])}},u=()=>{if(n["preview-cover"]){const{index:d,item:f}=e;return i("div",{class:Se("preview-cover")},[n["preview-cover"](J({index:d},f))])}},s=()=>{const{item:d,lazyLoad:f,imageFit:v,previewSize:h,reupload:y}=e;return pr(d)?i(Ia,{fit:v,src:d.objectUrl||d.content||d.url,class:Se("preview-image"),width:Array.isArray(h)?h[0]:h,height:Array.isArray(h)?h[1]:h,lazyLoad:f,onClick:y?c:l},{default:u}):i("div",{class:Se("file"),style:yt(e.previewSize)},[i(ae,{class:Se("file-icon"),name:"description"},null),i("div",{class:[Se("file-name"),"van-ellipsis"]},[d.file?d.file.name:d.url]),u()])};return()=>i("div",{class:Se("preview")},[s(),a(),r()])}});const hy={name:U(""),accept:H("image/*"),capture:String,multiple:Boolean,disabled:Boolean,readonly:Boolean,lazyLoad:Boolean,maxCount:U(1/0),imageFit:H("cover"),resultType:H("dataUrl"),uploadIcon:H("photograph"),uploadText:String,deletable:R,reupload:Boolean,afterRead:Function,showUpload:R,modelValue:me(),beforeRead:Function,beforeDelete:Function,previewSize:[Number,String,Array],previewImage:R,previewOptions:Object,previewFullImage:R,maxSize:{type:[Number,String,Function],default:1/0}};var my=_({name:sy,props:hy,emits:["delete","oversize","clickUpload","closePreview","clickPreview","clickReupload","update:modelValue"],setup(e,{emit:t,slots:n}){const a=O(),o=[],l=O(-1),c=O(!1),r=(x=e.modelValue.length)=>({name:e.name,index:x}),u=()=>{a.value&&(a.value.value="")},s=x=>{if(u(),Ar(x,e.maxSize))if(Array.isArray(x)){const E=uy(x,e.maxSize);if(x=E.valid,t("oversize",E.invalid,r()),!x.length)return}else{t("oversize",x,r());return}if(x=xe(x),l.value>-1){const E=[...e.modelValue];E.splice(l.value,1,x),t("update:modelValue",E),l.value=-1}else t("update:modelValue",[...e.modelValue,...aa(x)]);e.afterRead&&e.afterRead(x,r())},d=x=>{const{maxCount:E,modelValue:D,resultType:k}=e;if(Array.isArray(x)){const B=+E-D.length;x.length>B&&(x=x.slice(0,B)),Promise.all(x.map(p=>Yl(p,k))).then(p=>{const Y=x.map((q,F)=>{const Q={file:q,status:"",message:"",objectUrl:URL.createObjectURL(q)};return p[F]&&(Q.content=p[F]),Q});s(Y)})}else Yl(x,k).then(B=>{const p={file:x,status:"",message:"",objectUrl:URL.createObjectURL(x)};B&&(p.content=B),s(p)})},f=x=>{const{files:E}=x.target;if(e.disabled||!E||!E.length)return;const D=E.length===1?E[0]:[].slice.call(E);if(e.beforeRead){const k=e.beforeRead(D,r());if(!k){u();return}if(vo(k)){k.then(B=>{d(B||D)}).catch(u);return}}d(D)};let v;const h=()=>t("closePreview"),y=x=>{if(e.previewFullImage){const E=e.modelValue.filter(pr),D=E.map(k=>(k.objectUrl&&!k.url&&k.status!=="failed"&&(k.url=k.objectUrl,o.push(k.url)),k.url)).filter(Boolean);v=eh(J({images:D,startPosition:E.indexOf(x),onClose:h},e.previewOptions))}},S=()=>{v&&v.close()},g=(x,E)=>{const D=e.modelValue.slice(0);D.splice(E,1),t("update:modelValue",D),t("delete",x,r(E))},w=x=>{c.value=!0,l.value=x,le(()=>I())},b=()=>{c.value||(l.value=-1),c.value=!1},C=(x,E)=>{const D=["imageFit","deletable","reupload","previewSize","beforeDelete"],k=J(ue(e,D),ue(x,D,!0));return i(vy,ie({item:x,index:E,onClick:()=>t(e.reupload?"clickReupload":"clickPreview",x,r(E)),onDelete:()=>g(x,E),onPreview:()=>y(x),onReupload:()=>w(E)},ue(e,["name","lazyLoad"]),k),ue(n,["preview-cover","preview-delete"]))},T=()=>{if(e.previewImage)return e.modelValue.map(C)},m=x=>t("clickUpload",x),$=()=>{const x=e.modelValue.length<+e.maxCount,E=e.readonly?null:i("input",{ref:a,type:"file",class:Se("input"),accept:e.accept,capture:e.capture,multiple:e.multiple&&l.value===-1,disabled:e.disabled,onChange:f,onClick:b},null);return n.default?$e(i("div",{class:Se("input-wrapper"),onClick:m},[n.default(),E]),[[Be,x]]):$e(i("div",{class:Se("upload",{readonly:e.readonly}),style:yt(e.previewSize),onClick:m},[i(ae,{name:e.uploadIcon,class:Se("upload-icon")},null),e.uploadText&&i("span",{class:Se("upload-text")},[e.uploadText]),E]),[[Be,e.showUpload&&x]])},I=()=>{a.value&&!e.disabled&&a.value.click()};return Dt(()=>{o.forEach(x=>URL.revokeObjectURL(x))}),re({chooseFile:I,reuploadFile:w,closeImagePreview:S}),Et(()=>e.modelValue),()=>i("div",{class:Se()},[i("div",{class:Se("wrapper",{disabled:e.disabled})},[T(),$()])])}});const gy=N(my),[by,Ul]=V("watermark"),yy={gapX:Ce(0),gapY:Ce(0),image:String,width:Ce(100),height:Ce(100),rotate:U(-22),zIndex:M,content:String,opacity:M,fullPage:R,textColor:H("#dcdee0")};var wy=_({name:by,props:yy,setup(e,{slots:t}){const n=O(),a=O(""),o=O(""),l=()=>{const d={transformOrigin:"center",transform:"rotate(".concat(e.rotate,"deg)")},f=()=>e.image&&!t.content?i("image",{href:o.value,"xlink:href":o.value,x:"0",y:"0",width:e.width,height:e.height,style:d},null):i("foreignObject",{x:"0",y:"0",width:e.width,height:e.height},[i("div",{xmlns:"http://www.w3.org/1999/xhtml",style:d},[t.content?t.content():i("span",{style:{color:e.textColor}},[e.content])])]),v=e.width+e.gapX,h=e.height+e.gapY;return i("svg",{viewBox:"0 0 ".concat(v," ").concat(h),width:v,height:h,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",style:{padding:"0 ".concat(e.gapX,"px ").concat(e.gapY,"px 0"),opacity:e.opacity}},[f()])},c=d=>{const f=document.createElement("canvas"),v=new Image;v.crossOrigin="anonymous",v.referrerPolicy="no-referrer",v.onload=()=>{f.width=v.naturalWidth,f.height=v.naturalHeight;const h=f.getContext("2d");h==null||h.drawImage(v,0,0),o.value=f.toDataURL()},v.src=d},r=d=>{const f=new Blob([d],{type:"image/svg+xml"});return URL.createObjectURL(f)},u=()=>{a.value&&URL.revokeObjectURL(a.value)},s=()=>{n.value&&(u(),a.value=r(n.value.innerHTML))};return cn(()=>{e.image&&c(e.image)}),j(()=>[o.value,e.content,e.textColor,e.height,e.width,e.rotate,e.gapX,e.gapY],s),we(s),da(u),()=>{const d=J({backgroundImage:"url(".concat(a.value,")")},wt(e.zIndex));return i("div",{class:Ul({full:e.fullPage}),style:d},[i("div",{class:Ul("wrapper"),ref:n},[l()])])}}});const xy=N(wy);class Sy{constructor({el:t,src:n,error:a,loading:o,bindType:l,$parent:c,options:r,cors:u,elRenderer:s,imageCache:d}){this.el=t,this.src=n,this.error=a,this.loading=o,this.bindType=l,this.attempt=0,this.cors=u,this.naturalHeight=0,this.naturalWidth=0,this.options=r,this.$parent=c,this.elRenderer=s,this.imageCache=d,this.performanceData={loadStart:0,loadEnd:0},this.filter(),this.initState(),this.render("loading",!1)}initState(){"dataset"in this.el?this.el.dataset.src=this.src:this.el.setAttribute("data-src",this.src),this.state={loading:!1,error:!1,loaded:!1,rendered:!1}}record(t){this.performanceData[t]=Date.now()}update({src:t,loading:n,error:a}){const o=this.src;this.src=t,this.loading=n,this.error=a,this.filter(),o!==this.src&&(this.attempt=0,this.initState())}checkInView(){const t=ce(this.el);return t.top<window.innerHeight*this.options.preLoad&&t.bottom>this.options.preLoadTop&&t.left<window.innerWidth*this.options.preLoad&&t.right>0}filter(){Object.keys(this.options.filter).forEach(t=>{this.options.filter[t](this,this.options)})}renderLoading(t){this.state.loading=!0,ao({src:this.loading,cors:this.cors},()=>{this.render("loading",!1),this.state.loading=!1,t()},()=>{t(),this.state.loading=!1})}load(t=Bn){if(this.attempt>this.options.attempt-1&&this.state.error){t();return}if(!(this.state.rendered&&this.state.loaded)){if(this.imageCache.has(this.src))return this.state.loaded=!0,this.render("loaded",!0),this.state.rendered=!0,t();this.renderLoading(()=>{var n,a;this.attempt++,(a=(n=this.options.adapter).beforeLoad)==null||a.call(n,this,this.options),this.record("loadStart"),ao({src:this.src,cors:this.cors},o=>{this.naturalHeight=o.naturalHeight,this.naturalWidth=o.naturalWidth,this.state.loaded=!0,this.state.error=!1,this.record("loadEnd"),this.render("loaded",!1),this.state.rendered=!0,this.imageCache.add(this.src),t()},o=>{!this.options.silent&&console.error(o),this.state.error=!0,this.state.loaded=!1,this.render("error",!1)})})}}render(t,n){this.elRenderer(this,t,n)}performance(){let t="loading",n=0;return this.state.loaded&&(t="loaded",n=(this.performanceData.loadEnd-this.performanceData.loadStart)/1e3),this.state.error&&(t="error"),{src:this.src,state:t,time:n}}$destroy(){this.el=null,this.src=null,this.error=null,this.loading=null,this.bindType=null,this.attempt=0}}const Xl="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",Cy=["scroll","wheel","mousewheel","resize","animationend","transitionend","touchmove"],ky={rootMargin:"0px",threshold:0};function Ty(){return class{constructor({preLoad:t,error:n,throttleWait:a,preLoadTop:o,dispatchEvent:l,loading:c,attempt:r,silent:u=!0,scale:s,listenEvents:d,filter:f,adapter:v,observer:h,observerOptions:y}){this.mode=pt.event,this.listeners=[],this.targetIndex=0,this.targets=[],this.options={silent:u,dispatchEvent:!!l,throttleWait:a||200,preLoad:t||1.3,preLoadTop:o||0,error:n||Xl,loading:c||Xl,attempt:r||3,scale:s||ld(s),ListenEvents:d||Cy,supportWebp:id(),filter:f||{},adapter:v||{},observer:!!h,observerOptions:y||ky},this.initEvent(),this.imageCache=new cd({max:200}),this.lazyLoadHandler=Ui(this.lazyLoadHandler.bind(this),this.options.throttleWait),this.setMode(this.options.observer?pt.observer:pt.event)}config(t={}){Object.assign(this.options,t)}performance(){return this.listeners.map(t=>t.performance())}addLazyBox(t){this.listeners.push(t),Ve&&(this.addListenerTarget(window),this.observer&&this.observer.observe(t.el),t.$el&&t.$el.parentNode&&this.addListenerTarget(t.$el.parentNode))}add(t,n,a){if(this.listeners.some(c=>c.el===t))return this.update(t,n),le(this.lazyLoadHandler);const o=this.valueFormatter(n.value);let{src:l}=o;le(()=>{l=ul(t,this.options.scale)||l,this.observer&&this.observer.observe(t);const c=Object.keys(n.modifiers)[0];let r;c&&(r=a.context.$refs[c],r=r?r.$el||r:document.getElementById(c)),r||(r=ma(t));const u=new Sy({bindType:n.arg,$parent:r,el:t,src:l,loading:o.loading,error:o.error,cors:o.cors,elRenderer:this.elRenderer.bind(this),options:this.options,imageCache:this.imageCache});this.listeners.push(u),Ve&&(this.addListenerTarget(window),this.addListenerTarget(r)),this.lazyLoadHandler(),le(()=>this.lazyLoadHandler())})}update(t,n,a){const o=this.valueFormatter(n.value);let{src:l}=o;l=ul(t,this.options.scale)||l;const c=this.listeners.find(r=>r.el===t);c?c.update({src:l,error:o.error,loading:o.loading}):this.add(t,n,a),this.observer&&(this.observer.unobserve(t),this.observer.observe(t)),this.lazyLoadHandler(),le(()=>this.lazyLoadHandler())}remove(t){if(!t)return;this.observer&&this.observer.unobserve(t);const n=this.listeners.find(a=>a.el===t);n&&(this.removeListenerTarget(n.$parent),this.removeListenerTarget(window),Sn(this.listeners,n),n.$destroy())}removeComponent(t){t&&(Sn(this.listeners,t),this.observer&&this.observer.unobserve(t.el),t.$parent&&t.$el.parentNode&&this.removeListenerTarget(t.$el.parentNode),this.removeListenerTarget(window))}setMode(t){!cl&&t===pt.observer&&(t=pt.event),this.mode=t,t===pt.event?(this.observer&&(this.listeners.forEach(n=>{this.observer.unobserve(n.el)}),this.observer=null),this.targets.forEach(n=>{this.initListen(n.el,!0)})):(this.targets.forEach(n=>{this.initListen(n.el,!1)}),this.initIntersectionObserver())}addListenerTarget(t){if(!t)return;let n=this.targets.find(a=>a.el===t);return n?n.childrenCount++:(n={el:t,id:++this.targetIndex,childrenCount:1,listened:!0},this.mode===pt.event&&this.initListen(n.el,!0),this.targets.push(n)),this.targetIndex}removeListenerTarget(t){this.targets.forEach((n,a)=>{n.el===t&&(n.childrenCount--,n.childrenCount||(this.initListen(n.el,!1),this.targets.splice(a,1),n=null))})}initListen(t,n){this.options.ListenEvents.forEach(a=>(n?rd:sd)(t,a,this.lazyLoadHandler))}initEvent(){this.Event={listeners:{loading:[],loaded:[],error:[]}},this.$on=(t,n)=>{this.Event.listeners[t]||(this.Event.listeners[t]=[]),this.Event.listeners[t].push(n)},this.$once=(t,n)=>{const a=(...o)=>{this.$off(t,a),n.apply(this,o)};this.$on(t,a)},this.$off=(t,n)=>{if(!n){if(!this.Event.listeners[t])return;this.Event.listeners[t].length=0;return}Sn(this.Event.listeners[t],n)},this.$emit=(t,n,a)=>{this.Event.listeners[t]&&this.Event.listeners[t].forEach(o=>o(n,a))}}lazyLoadHandler(){const t=[];this.listeners.forEach(n=>{(!n.el||!n.el.parentNode)&&t.push(n),n.checkInView()&&n.load()}),t.forEach(n=>{Sn(this.listeners,n),n.$destroy()})}initIntersectionObserver(){cl&&(this.observer=new IntersectionObserver(this.observerHandler.bind(this),this.options.observerOptions),this.listeners.length&&this.listeners.forEach(t=>{this.observer.observe(t.el)}))}observerHandler(t){t.forEach(n=>{n.isIntersecting&&this.listeners.forEach(a=>{if(a.el===n.target){if(a.state.loaded)return this.observer.unobserve(a.el);a.load()}})})}elRenderer(t,n,a){if(!t.el)return;const{el:o,bindType:l}=t;let c;switch(n){case"loading":c=t.loading;break;case"error":c=t.error;break;default:({src:c}=t);break}if(l?o.style[l]='url("'+c+'")':o.getAttribute("src")!==c&&o.setAttribute("src",c),o.setAttribute("lazy",n),this.$emit(n,t,a),this.options.adapter[n]&&this.options.adapter[n](t,this.options),this.options.dispatchEvent){const r=new CustomEvent(n,{detail:t});o.dispatchEvent(r)}}valueFormatter(t){let n=t,{loading:a,error:o}=this.options;return Le(t)&&({src:n}=t,a=t.loading||this.options.loading,o=t.error||this.options.error),{src:n,loading:a,error:o}}}}var $y=e=>({props:{tag:{type:String,default:"div"}},emits:["show"],render(){return Kl(this.tag,this.show&&this.$slots.default?this.$slots.default():null)},data(){return{el:null,state:{loaded:!1},show:!1}},mounted(){this.el=this.$el,e.addLazyBox(this),e.lazyLoadHandler()},beforeUnmount(){e.removeComponent(this)},methods:{checkInView(){const t=ce(this.$el);return Ve&&t.top<window.innerHeight*e.options.preLoad&&t.bottom>0&&t.left<window.innerWidth*e.options.preLoad&&t.right>0},load(){this.show=!0,this.state.loaded=!0,this.$emit("show",this)},destroy(){return this.$destroy}}});const By={selector:"img"};class Iy{constructor({el:t,binding:n,vnode:a,lazy:o}){this.el=null,this.vnode=a,this.binding=n,this.options={},this.lazy=o,this.queue=[],this.update({el:t,binding:n})}update({el:t,binding:n}){this.el=t,this.options=Object.assign({},By,n.value),this.getImgs().forEach(o=>{this.lazy.add(o,Object.assign({},this.binding,{value:{src:"dataset"in o?o.dataset.src:o.getAttribute("data-src"),error:("dataset"in o?o.dataset.error:o.getAttribute("data-error"))||this.options.error,loading:("dataset"in o?o.dataset.loading:o.getAttribute("data-loading"))||this.options.loading}}),this.vnode)})}getImgs(){return Array.from(this.el.querySelectorAll(this.options.selector))}clear(){this.getImgs().forEach(n=>this.lazy.remove(n)),this.vnode=null,this.binding=null,this.lazy=null}}class Dy{constructor({lazy:t}){this.lazy=t,this.queue=[]}bind(t,n,a){const o=new Iy({el:t,binding:n,vnode:a,lazy:this.lazy});this.queue.push(o)}update(t,n,a){const o=this.queue.find(l=>l.el===t);o&&o.update({el:t,binding:n,vnode:a})}unbind(t){const n=this.queue.find(a=>a.el===t);n&&(n.clear(),Sn(this.queue,n))}}var Ey=e=>({props:{src:[String,Object],tag:{type:String,default:"img"}},render(){var t,n;return Kl(this.tag,{src:this.renderSrc},(n=(t=this.$slots).default)==null?void 0:n.call(t))},data(){return{el:null,options:{src:"",error:"",loading:"",attempt:e.options.attempt},state:{loaded:!1,error:!1,attempt:0},renderSrc:""}},watch:{src(){this.init(),e.addLazyBox(this),e.lazyLoadHandler()}},created(){this.init()},mounted(){this.el=this.$el,e.addLazyBox(this),e.lazyLoadHandler()},beforeUnmount(){e.removeComponent(this)},methods:{init(){const{src:t,loading:n,error:a}=e.valueFormatter(this.src);this.state.loaded=!1,this.options.src=t,this.options.error=a,this.options.loading=n,this.renderSrc=this.options.loading},checkInView(){const t=ce(this.$el);return t.top<window.innerHeight*e.options.preLoad&&t.bottom>0&&t.left<window.innerWidth*e.options.preLoad&&t.right>0},load(t=Bn){if(this.state.attempt>this.options.attempt-1&&this.state.error){t();return}const{src:n}=this.options;ao({src:n},({src:a})=>{this.renderSrc=a,this.state.loaded=!0},()=>{this.state.attempt++,this.renderSrc=this.options.error,this.state.error=!0})}}});const My={install(e,t={}){const n=Ty(),a=new n(t),o=new Dy({lazy:a});e.config.globalProperties.$Lazyload=a,t.lazyComponent&&e.component("LazyComponent",$y(a)),t.lazyImage&&e.component("LazyImage",Ey(a)),e.directive("lazy",{beforeMount:a.add.bind(a),updated:a.update.bind(a),unmounted:a.remove.bind(a)}),e.directive("lazy-container",{beforeMount:o.bind.bind(o),updated:o.update.bind(o),unmounted:o.unbind.bind(o)})}},Py="4.9.19";function Oy(e){[vi,to,Qs,fc,Ru,od,_i,vd,Ht,yd,Pe,pd,Fd,Yd,Je,Gd,Eo,ji,tf,uf,hf,wf,xf,Tf,Df,pf,zf,ro,qf,tv,lv,vv,yv,Tv,$v,nr,ut,Ev,pv,ko,Lv,Nv,Yv,ae,Ia,th,ch,uh,mh,Ke,li,wh,kh,ph,Nh,wi,Yh,Gh,Ca,Kh,qm,Ze,Jm,og,Do,Bo,cg,yg,wg,Tg,Ag,Cr,kr,Wg,cb,$r,vb,Br,Tr,bb,Sb,Bb,Ob,Ab,Bi,Lb,So,Nb,Co,$o,En,jb,qb,xa,Ba,Jb,ay,$u,ry,gy,xy].forEach(n=>{n.install?e.use(n):n.name&&e.component(n.name,n)})}var zy={install:Oy,version:Py};export{My as L,ph as N,ra as a,zy as b,py as c,Ry as d,_y as e,Vy as s};
