import{a as Y,b as g}from"./api-d1b3ba40.js";import{h as d,d as h}from"./dateFormat-1fb6392c.js";import{_ as V}from"./index-4829f8e2.js";import{Q as i,R as m,S as p,k as r,U as s,Y as l,V as c,X as w,F as C,W as I}from"./verder-361ae6c7.js";import"./vant-91101745.js";const M={data(){return{codeId:"",codeInfo:{},startDatePickerVisible:!1,endDatePickerVisible:!1,startDate:d().format("YYYY-MM-DD").split("-"),endDate:d().format("YYYY-MM-DD").split("-"),minDate:new Date(1900,0,1),maxDate:new Date(2050,10,1),params:{startDate:"",endDate:""},data:[],refreshing:!1}},methods:{goBack(){this.$router.push("/CircumstancesDetection")},async getCodeInfo(){const n=await Y(this.codeId);this.codeInfo=n&&n.codeInfo?n.codeInfo:{}},onRefresh(){const{startDate:n,endDate:e}=this.params,D=n?d(n.join("/")).format("YYYY-MM-DD [00:00:00]"):"",f=e?d(e.join("/")).format("YYYY-MM-DD [23:59:59]"):"";g({codeId:this.codeId,startTime:D,endTime:f,page:0,size:999}).then(t=>{this.data=t&&Array.isArray(t.data)?t.data.map(a=>({...a,watchTime:h(a.watchTime,"YYYY-MM-DD HH:mm")})):[]}).finally(t=>{this.refreshing=!1})},onDateReset(n){this[n]=d().format("YYYY-MM-DD").split("-"),this.params[n]="",this["".concat(n,"PickerVisible")]=!1,this.onRefresh()},onDateConfirm(n){this.params[n]=h(this[n],"YYYY-MM-DD"),this["".concat(n,"PickerVisible")]=!1,this.onRefresh()}},mounted(){const{codeId:n}=this.$route.params;this.codeId=n,this.getCodeInfo(),this.onRefresh()}},R={class:"monitor-container"},P={class:"date-picker-search border-bottom flex-shrink-0"},j={class:"border-bottom",style:{display:"flex","justify-content":"space-between","align-items":"center"}},B={class:"bold text-gray-7-1 my-0"},U={class:"text-gray-6-1"},F={class:"text-gray-7-1 bold py-2"},T={class:"text-truncate mt-1 mb-0"},H={class:"text-truncate mt-1 mb-0"},E={key:1,class:"text-gray-6-1 text-center my-5"};function z(n,e,D,f,t,a){const b=i("Navbar"),v=i("van-icon"),u=i("van-date-picker"),_=i("van-popup"),k=i("van-col"),y=i("van-row"),x=i("van-pull-refresh");return m(),p("div",R,[r(b,{back:!n.envFeishu,backEvent:a.goBack,title:t.codeInfo.codeName?"预警历史-".concat(t.codeInfo.codeName):"预警历史"},null,8,["back","backEvent","title"]),s("div",P,[r(v,{name:"search",color:"#347DF6",size:"20px"}),s("div",{class:"date-picker-button text-gray-6-1",onClick:e[0]||(e[0]=o=>t.startDatePickerVisible=!0)},l(t.params.startDate&&t.params.startDate.join("-")||"请选择开始时间"),1),r(_,{show:t.startDatePickerVisible,"onUpdate:show":e[4]||(e[4]=o=>t.startDatePickerVisible=o),round:"",position:"bottom"},{default:c(()=>[r(u,{modelValue:t.startDate,"onUpdate:modelValue":e[1]||(e[1]=o=>t.startDate=o),type:"date",title:"请选择","min-date":t.minDate,"max-date":t.maxDate,"cancel-button-text":"清空",onCancel:e[2]||(e[2]=o=>a.onDateReset("startDate")),onConfirm:e[3]||(e[3]=o=>a.onDateConfirm("startDate"))},null,8,["modelValue","min-date","max-date"])]),_:1},8,["show"]),e[11]||(e[11]=s("span",null,"至",-1)),s("div",{class:"date-picker-button text-gray-6-1",onClick:e[5]||(e[5]=o=>t.endDatePickerVisible=!0)},l(t.params.endDate&&t.params.endDate.join("-")||"请选择结束时间"),1),r(_,{show:t.endDatePickerVisible,"onUpdate:show":e[9]||(e[9]=o=>t.endDatePickerVisible=o),round:"",position:"bottom"},{default:c(()=>[r(u,{modelValue:t.endDate,"onUpdate:modelValue":e[6]||(e[6]=o=>t.endDate=o),type:"date",title:"请选择","min-date":t.minDate,"max-date":t.maxDate,"cancel-button-text":"清空",onCancel:e[7]||(e[7]=o=>a.onDateReset("endDate")),onConfirm:e[8]||(e[8]=o=>a.onDateConfirm("endDate"))},null,8,["modelValue","min-date","max-date"])]),_:1},8,["show"])]),r(x,{modelValue:t.refreshing,"onUpdate:modelValue":e[10]||(e[10]=o=>t.refreshing=o),class:"flex-grow-1 ofy-auto no-scrollbar",onRefresh:a.onRefresh},{default:c(()=>[t.data.length>0?(m(),w(y,{key:0,type:"flex",justify:"center"},{default:c(()=>[r(k,{span:"22"},{default:c(()=>[(m(!0),p(C,null,I(t.data,o=>(m(),p("div",{key:o.tableId,class:"card mt-6"},[s("div",j,[s("h4",B,l(o.title),1),s("span",U,l(o.watchTime),1)]),s("div",F,[s("p",T," 实际值: "+l(o.watchValue)+l(o.unit),1),s("p",H," 阈值: "+l(o.abnormityContent),1)])]))),128)),e[12]||(e[12]=s("p",{class:"text-gray-6-1 text-center py-3 mb-0"},"没有更多了",-1))]),_:1,__:[12]})]),_:1})):(m(),p("p",E,"暂无数据"))]),_:1},8,["modelValue","onRefresh"])])}const Q=V(M,[["render",z],["__scopeId","data-v-4d778149"]]);export{Q as default};
