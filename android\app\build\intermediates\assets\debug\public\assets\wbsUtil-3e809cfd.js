import{h as l,A as f,j as p}from"./index-4829f8e2.js";import{t as m}from"./array-15ef8611.js";const c=f.VUE_APP_BASE_API_SERVICENAME;function A(t){return l({url:"".concat(c,"/wbs/node/tree"),method:"get",params:t})}async function E(t,a,r){let e=await A({portalId:t});r.parentId!="0"&&(e=e.filter(i=>r.id===i.id));let s=await p();return e.forEach(i=>{let o=s.find(d=>d.id===i.id);o&&(i.nodeName=o.ext2)}),a?e:e[0].children}function w(t,a,r){var e;return(e=m(t).find(s=>s[r||"id"]==a))==null?void 0:e.nodeName}export{w as a,E as g};
