System.register(["./index-legacy-09188690.js","./array-legacy-2920c097.js"],(function(e,t){"use strict";var n,r,i,a;return{setters:[e=>{n=e.h,r=e.A,i=e.j},e=>{a=e.t}],execute:function(){e({a:function(e,t,n){var r;let i=a(e);return null===(r=i.find((e=>e[n||"id"]==t)))||void 0===r?void 0:r.nodeName},g:async function(e,r,a){let d=await function(e){return n({url:`${t}/wbs/node/tree`,method:"get",params:e})}({portalId:e});"0"!=a.parentId&&(d=d.filter((e=>a.id===e.id)));let o=await i();return d.forEach((e=>{let t=o.find((t=>t.id===e.id));t&&(e.nodeName=t.ext2)})),r?d:d[0].children}});const t=r.VUE_APP_BASE_API_SERVICENAME}}}));
