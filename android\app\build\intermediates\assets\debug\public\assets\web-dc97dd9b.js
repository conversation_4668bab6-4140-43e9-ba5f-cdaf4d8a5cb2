import{W as n}from"./index-4829f8e2.js";import"./verder-361ae6c7.js";import"./vant-91101745.js";class e extends n{constructor(){super(),this._lastWindow=null}async open(s){this._lastWindow=window.open(s.url,s.windowName||"_blank")}async close(){return new Promise((s,o)=>{this._lastWindow!=null?(this._lastWindow.close(),this._lastWindow=null,s()):o("No active window to close!")})}}const w=new e;export{w as <PERSON><PERSON><PERSON>,e as <PERSON><PERSON><PERSON><PERSON><PERSON>};
