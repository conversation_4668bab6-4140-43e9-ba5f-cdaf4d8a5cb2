<!DOCTYPE html>
<html lang="en">
  <head>
    <script type="module" crossorigin src="/app/assets/polyfills-bc9aff04.js"></script>

    <meta charset="UTF-8" />
    <meta name="format-detection" content="telephone=yes" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,minimum-scale=0.5, maximum-scale=3.0, user-scalable=yes, viewport-fit=cover"
    />
    <meta name="cache-control" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-control" content="no-cache" />
    <meta http-equiv="Cache" content="no-cache" />
    <!-- 屏蔽favicon -->
    <link rel="icon" href="data:image/ico;base64,aWNv" />
    <meta name="format-detection" content="telephone=yes"/>
    <title>【数字孪生姚家平】</title>

    <!-- 飞书配置 -->
    <script type="text/javascript">
      function addMeta(name, content, prop) {
        const meta = document.createElement('meta');
        meta.name = name;
        meta.content = content;
        if (prop) {
          meta.setAttribute(prop, true);
        }
        document.getElementsByTagName('head')[0].appendChild(meta);
      }

      function addScript(type, src, callback) {
        const script = document.createElement('script');
        script.type = type;
        script.src = src;
        document.getElementsByTagName('head')[0].appendChild(script);
        script.onload = function () {
          if (callback && typeof callback === 'function') {
            callback();
          }
        };
      }

      if (window.location.search.includes('env=feishu')) {
        addMeta('orientation', 'portrait', 'lk-config');
        // addMeta('showNavBar', 'false', 'lk-config');
        addScript(
          'text/javascript',
          'https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.23.js'
        );
        window.ENV_FEISHU = true;
      }

      if (window.location.search.includes('debug=true')) {
        addScript(
          'text/javascript',
          'https://unpkg.com/vconsole@latest/dist/vconsole.min.js',
          function () {
            var vConsole = new window.VConsole();
          }
        );
      }
    </script>
    <style>
      .app-loading,
      .app-loading > span {
        position: relative;
        display: block;
        font-size: 0;
        color: #3895ff;
        box-sizing: border-box;
      }

      .app-loading {
        position: fixed;
        left: 0;
        right: 0;
        width: 100%;
        height: 100vh;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
      }

      .app-loading > span {
        display: inline-block;
        float: none;
        background-color: currentColor;
        border: 0 solid currentColor;
        width: 2.66667vw;
        height: 2.66667vw;
        margin: 1.06667vw;
        border-radius: 100%;
        animation: ball-pulse 1s ease infinite;
      }

      .app-loading > span:nth-child(1) {
        animation-delay: 0;
      }

      .app-loading > span:nth-child(2) {
        animation-delay: 0.1s;
      }

      .app-loading > span:nth-child(3) {
        animation-delay: 0.2s;
      }

      @keyframes ball-pulse {
        0%,
        60%,
        100% {
          opacity: 0.8;
          transform: scale(1);
        }

        30% {
          opacity: 0.1;
          transform: scale(0.01);
        }
      }
    </style>
    <script type="module" crossorigin src="/app/assets/index-4829f8e2.js"></script>
    <link rel="modulepreload" crossorigin href="/app/assets/verder-361ae6c7.js">
    <link rel="modulepreload" crossorigin href="/app/assets/vant-91101745.js">
    <link rel="stylesheet" href="/app/assets/index-a9b3f457.css">
    <script type="module">import.meta.url;import("_").catch(()=>1);async function* g(){};if(location.protocol!="file:"){window.__vite_is_modern_browser=true}</script>
    <script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy chunks, syntax error above and the same error below should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>
  </head>
  <body ontouchstart="">
    <div id="app-loading" class="app-loading">
      <span></span>
      <span></span>
      <span></span>
    </div>
    <div id="app"></div>
    
    <script src="/app/hkplayer/h5player.min.js"></script>
    <script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
    <script nomodule crossorigin id="vite-legacy-polyfill" src="/app/assets/polyfills-legacy-40718432.js"></script>
    <script nomodule crossorigin id="vite-legacy-entry" data-src="/app/assets/index-legacy-09188690.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>
  </body>
</html>
