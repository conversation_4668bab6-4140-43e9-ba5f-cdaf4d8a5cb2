1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.hdec.yjp"
4    android:versionCode="1"
5    android:versionName="0.1.0" > <!--  -->
6    <uses-sdk
6-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:60:5-73
7        android:minSdkVersion="22"
7-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:60:5-73
8        android:targetSdkVersion="33" />
8-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:60:5-73
9
10    <!-- Permissions -->
11    <!-- 网络 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:45:5-67
12-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:45:22-64
13    <!-- 相机相册文件 -->
14    <uses-permission android:name="android.permission.CAMERA" />
14-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:47:5-65
14-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:47:22-62
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:48:5-80
15-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:48:22-77
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:49:5-81
16-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:49:22-78
17
18    <!-- 麦克风 -->
19    <uses-permission android:name="android.permission.RECORD_AUDIO" />
19-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:52:5-71
19-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:52:22-68
20    <!-- 定位 -->
21    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
21-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:54:5-81
21-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:54:22-78
22    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
22-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:55:5-79
22-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:55:22-76
23
24    <uses-feature android:name="android.hardware.location.gps" />
24-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:56:5-66
24-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:56:19-63
25    <!-- 本地通知 -->
26    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
26-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:58:5-79
26-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:58:22-76
27
28    <queries>
28-->[:capacitor-camera] E:\project\yjp\mobile-front\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-13:15
29        <intent>
29-->[:capacitor-camera] E:\project\yjp\mobile-front\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-12:18
30            <action android:name="android.media.action.IMAGE_CAPTURE" />
30-->[:capacitor-camera] E:\project\yjp\mobile-front\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-73
30-->[:capacitor-camera] E:\project\yjp\mobile-front\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-70
31        </intent>
32    </queries>
33
34    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
34-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
34-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-78
35    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
35-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-68
35-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-65
36    <uses-feature
36-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
37        android:name="android.hardware.camera"
37-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
38        android:required="false" />
38-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
39    <uses-feature
39-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
40        android:name="android.hardware.camera.front"
40-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
41        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
41-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
42    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
43    <uses-feature
43-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
44        android:name="android.hardware.camera.autofocus"
44-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
45        android:required="false" />
45-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
46    <uses-feature
46-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
47        android:name="android.hardware.camera.flash"
47-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
48        android:required="false" />
48-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
49    <uses-feature
49-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
50        android:name="android.hardware.screen.landscape"
50-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
51        android:required="false" />
51-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
52    <uses-feature
52-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
53        android:name="android.hardware.wifi"
53-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
54        android:required="false" />
54-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
55
56    <application
56-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:6:5-41:19
57        android:allowBackup="true"
57-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:7:5-31
58        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
58-->[androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf27aeb78f2198c76c88b5b28a3978e4\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
59        android:debuggable="true"
60        android:hardwareAccelerated="true"
60-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:13:5-39
61        android:icon="@mipmap/ic_launcher"
61-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:8:5-39
62        android:label="@string/app_name"
62-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:9:5-37
63        android:largeHeap="true"
63-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:14:5-29
64        android:roundIcon="@mipmap/ic_launcher_round"
64-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:10:5-50
65        android:supportsRtl="true"
65-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:11:5-31
66        android:testOnly="true"
67        android:theme="@style/AppTheme"
67-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:12:5-36
68        android:usesCleartextTraffic="true" >
68-->[:capacitor-cordova-android-plugins] E:\project\yjp\mobile-front\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:18-53
69        <activity
69-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:16:9-28:20
70            android:name="com.hdec.yjp.MainActivity"
70-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:18:7-47
71            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode"
71-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:17:7-123
72            android:exported="true"
72-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:22:7-30
73            android:label="@string/title_activity_main"
73-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:19:7-50
74            android:launchMode="singleTask"
74-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:21:7-38
75            android:theme="@style/AppTheme.NoActionBarLaunch" >
75-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:20:7-56
76            <intent-filter>
76-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:24:13-27:29
77                <action android:name="android.intent.action.MAIN" />
77-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:25:17-69
77-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:25:25-66
78
79                <category android:name="android.intent.category.LAUNCHER" />
79-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:26:17-77
79-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:26:27-74
80            </intent-filter>
81        </activity>
82
83        <provider
84            android:name="androidx.core.content.FileProvider"
84-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:31:7-56
85            android:authorities="com.hdec.yjp.fileprovider"
85-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:32:7-58
86            android:exported="false"
86-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:33:7-31
87            android:grantUriPermissions="true" >
87-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:34:7-41
88            <meta-data
88-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:36:13-39:9
89                android:name="android.support.FILE_PROVIDER_PATHS"
89-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:37:9-59
90                android:resource="@xml/file_paths" />
90-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:38:9-43
91        </provider>
92        <provider
92-->[:capacitor-community-file-opener] E:\project\yjp\mobile-front\node_modules\@capacitor-community\file-opener\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-18:20
93            android:name="com.ryltsov.alex.plugins.file.opener.FileOpenerProvider"
93-->[:capacitor-community-file-opener] E:\project\yjp\mobile-front\node_modules\@capacitor-community\file-opener\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-83
94            android:authorities="com.hdec.yjp.file.opener.provider"
94-->[:capacitor-community-file-opener] E:\project\yjp\mobile-front\node_modules\@capacitor-community\file-opener\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-72
95            android:exported="false"
95-->[:capacitor-community-file-opener] E:\project\yjp\mobile-front\node_modules\@capacitor-community\file-opener\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-37
96            android:grantUriPermissions="true" >
96-->[:capacitor-community-file-opener] E:\project\yjp\mobile-front\node_modules\@capacitor-community\file-opener\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-47
97            <meta-data
97-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:36:13-39:9
98                android:name="android.support.FILE_PROVIDER_PATHS"
98-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:37:9-59
99                android:resource="@xml/file_opener_paths" />
99-->E:\project\yjp\mobile-front\android\app\src\main\AndroidManifest.xml:38:9-43
100        </provider>
101
102        <receiver android:name="com.capacitorjs.plugins.localnotifications.TimedNotificationPublisher" />
102-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-106
102-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:19-103
103        <receiver android:name="com.capacitorjs.plugins.localnotifications.NotificationDismissReceiver" />
103-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-107
103-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:19-104
104        <receiver
104-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-24:20
105            android:name="com.capacitorjs.plugins.localnotifications.LocalNotificationRestoreReceiver"
105-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-103
106            android:directBootAware="true"
106-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-43
107            android:exported="false" >
107-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-37
108            <intent-filter>
108-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-23:29
109                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
109-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:17-86
109-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:25-83
110                <action android:name="android.intent.action.BOOT_COMPLETED" />
110-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:17-79
110-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:25-76
111                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
111-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-82
111-->[:capacitor-local-notifications] E:\project\yjp\mobile-front\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-79
112            </intent-filter>
113        </receiver>
114
115        <activity
115-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c486f176340cef68b04fcd9d5614a045\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
116            android:name="com.google.android.gms.common.api.GoogleApiActivity"
116-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c486f176340cef68b04fcd9d5614a045\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
117            android:exported="false"
117-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c486f176340cef68b04fcd9d5614a045\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
118            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
118-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c486f176340cef68b04fcd9d5614a045\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
119
120        <meta-data
120-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f76baadde36fe8e4d93e81cf8fcae26\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:21:9-23:69
121            android:name="com.google.android.gms.version"
121-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f76baadde36fe8e4d93e81cf8fcae26\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:22:13-58
122            android:value="@integer/google_play_services_version" />
122-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f76baadde36fe8e4d93e81cf8fcae26\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:23:13-66
123
124        <provider
124-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b3f02e5594c504e498fdfc5a6967e3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
125            android:name="androidx.startup.InitializationProvider"
125-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b3f02e5594c504e498fdfc5a6967e3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:27:13-67
126            android:authorities="com.hdec.yjp.androidx-startup"
126-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b3f02e5594c504e498fdfc5a6967e3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:28:13-68
127            android:exported="false" >
127-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b3f02e5594c504e498fdfc5a6967e3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:29:13-37
128            <meta-data
128-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b3f02e5594c504e498fdfc5a6967e3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
129                android:name="androidx.emoji2.text.EmojiCompatInitializer"
129-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b3f02e5594c504e498fdfc5a6967e3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:32:17-75
130                android:value="androidx.startup" />
130-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b3f02e5594c504e498fdfc5a6967e3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:33:17-49
131            <meta-data
131-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf1371846bef0d818fd7467b5155d033\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:31:13-33:52
132                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
132-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf1371846bef0d818fd7467b5155d033\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:32:17-78
133                android:value="androidx.startup" />
133-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf1371846bef0d818fd7467b5155d033\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:33:17-49
134        </provider>
135
136        <activity
136-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
137            android:name="com.journeyapps.barcodescanner.CaptureActivity"
137-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
138            android:clearTaskOnLaunch="true"
138-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
139            android:screenOrientation="sensorLandscape"
139-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
140            android:stateNotNeeded="true"
140-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
141            android:theme="@style/zxing_CaptureTheme"
141-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
142            android:windowSoftInputMode="stateAlwaysHidden" />
142-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\591cbdad318bf25b894ba667069fff4e\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
143    </application>
144
145</manifest>
