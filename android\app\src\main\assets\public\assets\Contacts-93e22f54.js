import{_ as L,h as w,A as S}from"./index-4829f8e2.js";import{Q as d,R as g,S as m,X as v,F as b,Y as _,y as x,k as y,A,V as O,U as f,t as C,v as I,W as T,Z as N}from"./verder-361ae6c7.js";import{g as U}from"./file-842bc27d.js";import{g as B}from"./common-3f45b198.js";import"./vant-91101745.js";const P="/app/assets/folder-29fb0457.svg";const E={name:"HeadPortrait",props:{type:{type:Number,default:1},imgUrl:{},text:{}},data(){return{folderImg:P}}},V={key:1};function J(e,s,o,l,r,n){const t=d("van-image");return g(),m("div",{class:x(["head",{"text-head":!o.imgUrl&&o.type===2}])},[o.type===1?(g(),v(t,{key:0,src:r.folderImg,class:"head-img"},null,8,["src"])):(g(),m(b,{key:1},[o.imgUrl?(g(),v(t,{key:0,fit:"cover",src:o.imgUrl,class:"head-img-portrait"},null,8,["src"])):(g(),m("span",V,_(o.text.charAt(0)),1))],64))],2)}const F=L(E,[["render",J],["__scopeId","data-v-b71a23cc"]]);function j(e){return w({url:"".concat(S.VUE_APP_BASE_API_SERVICENAME,"/general-contacts/list"),method:"get",params:e})}const H="/app/assets/call-e73473de.svg";const M={name:"TelPhone",props:{phone:{},callImgStyle:{}},data(){return{showBottom:!1,callImg:H}},methods:{openCall(){this.showBottom=!0},call(){console.log(this.phone),console.log(this.$refs),this.$refs.tels.href="tel://".concat(this.phone),this.$refs.tels.click(),this.onClose()},onClose(){this.showBottom=!1}}},z={class:"phone-img"},D={href:"",ref:"tels"};function R(e,s,o,l,r,n){const t=d("van-image"),a=d("van-popup");return g(),m("div",z,[y(t,{onClick:n.openCall,src:r.callImg,style:A(o.callImgStyle),class:"call-img"},null,8,["onClick","src","style"]),y(a,{show:r.showBottom,"onUpdate:show":s[2]||(s[2]=h=>r.showBottom=h),position:"bottom",style:{height:"300px"},teleport:"#app"},{default:O(()=>[s[3]||(s[3]=f("div",{class:"title"},"是否联系用户",-1)),f("div",{class:"phone-btn",onClick:s[0]||(s[0]=(...h)=>n.call&&n.call(...h))},"确认拨打"),f("div",{class:"phone-btn",onClick:s[1]||(s[1]=(...h)=>n.onClose&&n.onClose(...h))},"取消")]),_:1,__:[3]},8,["show"]),C(f("a",D,null,512),[[I,!1]])])}const Q=L(M,[["render",R],["__scopeId","data-v-4f303594"]]);const W={components:{HeadPortrait:F,TelPhone:Q},data(){return{keyword:"",breadcrumbList:[],searchParams:{searchValue:"",orgNos:[]},list:[],orgList:[],orgAllList:[],userList:[],none:"",isOnSearch:!1,loading:!1}},computed:{currentBreadcrumb(){return this.breadcrumbList[this.breadcrumbList.length-1]}},watch:{keyword(e){e===""&&this.onSearch(e)}},async created(){this.loading=!0,this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),await this.getUserList(),this.$closeToast(),this.loading=!1,setTimeout(()=>{this.none="暂无数据"},2e3)},methods:{async getList(){this.isOnSearch=!1;let e=[];if(this.breadcrumbList.length>0){var s=this.currentBreadcrumb.orgNos;for(let o=0;o<this.orgAllList.length;o++){let l=this.orgAllList[o];s.indexOf(l.content.parentNo)>-1&&e.push(l)}this.searchParams.orgNos=[...s]}else{let o=[];for(let l=0;l<this.orgAllList.length;l++){let r=this.orgAllList[l];r.level===2&&e.push(r),r.level===1&&r.content.orgNos.forEach(n=>{o.push(n)})}this.searchParams.orgNos=[...o]}this.list=[...e,...this.getSearchList()]},getSearchList(){const e=this.searchParams.searchValue,s=this.searchParams.orgNos;if(this.isOnSearch&&s&&s.length>0){let r=this.getSubTree(this.orgAllList,s).map(n=>n.content.orgNos);for(let n=0;n<r.length;n++)for(let t=0;t<r[n].length;t++)s.push(r[n][t])}return this.userList.filter(l=>!(e&&!l.userFullname.includes(e)||s&&s.length>0&&l.orgList.filter(r=>s.indexOf(r.orgNo)>-1).length<1))},onSearch(){this.loading=!0,this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),this.searchParams.searchValue=this.keyword,this.searchParams.searchValue?(this.isOnSearch=!0,this.list=this.getSearchList()):this.getList(),this.$closeToast(),this.loading=!1},onBack(){var e;((e=this.breadcrumbList)==null?void 0:e.length)>0?(this.breadcrumbList.pop(),this.searchParams.searchValue="",this.keyword="",this.getList()):this.$router.replace({name:"Home"})},openNext(e,s){e.type!==2&&(this.breadcrumbList.push({name:e.content.ext1||e.content.name,level:e.level,orgNos:e.content.orgNos}),this.keyword="",this.searchParams.searchValue="",this.getList())},filterImg(e){return U(e.photoToken)},async getAllOrg(){await B().then(e=>{let s=this.mergeNodesByName(e);this.orgList=[...s],this.orgAllList=this.flattenTree(this.orgList),this.initUserOrg(),this.getList(),setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}).catch(()=>{this.loading=!1,this.$closeToast()})},initUserOrg(){let e=new Map(this.orgAllList.map(s=>[s.content.orgNo+"",s]));for(let s=0;s<this.userList.length;s++){this.userList[s].type=2;const o=this.userList[s].orgNos;if(o){let r=Array.from(new Set(o.split(",").map(n=>{var h;let t=e.get(n);if(t)return t.content.ext1?t.content.ext1:t.content.name;for(var a=0;a<this.orgAllList.length;a++){let c=this.orgAllList[a];(h=c==null?void 0:c.content)!=null&&h.orgNos;for(let p=0;p<c.content.orgNos.length;p++)if(c.content.orgNos[p]+""===n)return c.content.ext1?c.content.ext1:c.content.name}}).filter(n=>n!=null&&n!==""))).join(",");this.userList[s].orgName=r}const l=this.userList[s].unitNos;if(l){let r=Array.from(new Set(l.split(",").map(n=>{var h;let t=e.get(n);if(t)return t.content.ext1?t.content.ext1:t.content.name;for(var a=0;a<this.orgAllList.length;a++){let c=this.orgAllList[a];(h=c==null?void 0:c.content)!=null&&h.orgNos;for(let p=0;p<c.content.orgNos.length;p++)if(c.content.orgNos[p]+""===n)return c.content.ext1?c.content.ext1:c.content.name}}).filter(n=>n!=null&&n!==""))).join(",");this.userList[s].unitName=r}}},async getUserList(){this.userList.length<1&&await j().then(e=>{this.userList=e||[],this.getAllOrg()}).catch(()=>{this.$closeToast(),this.loading=!1})},mergeNodesByName(e){var s={};function o(t){let{content:{name:a},content:{ext1:h},content:{pathNo:c}}=t;const p=c.split(".").filter(Boolean).length-1;if(h&&(a=h),!s[a])s[a]=[JSON.parse(JSON.stringify({...t,content:{...t.content,orgNos:[t.content.orgNo],memberNum:parseInt(t.content.memberNum,10)},type:1,level:p}))],s[a][0].children=[];else{let i=s[a].filter(u=>u.content.parentName===t.content.parentName);if(i.length>0){let u=i[0];p>=u.level&&(u.level=p,u.type=1,u.id=t.id,u.parentId=t.parentId,u.content.orgNo=t.content.orgNo,u.content.parentNo=t.content.parentNo,u.content.parentName=t.content.parentName,u.content.pathNo=t.content.pathNo,u.content.pathName=t.content.pathName,u.content.sort=t.content.sort,u.content.ext1=t.content.ext1),u.content.orgNos.push(t.content.orgNo),u.content.memberNum+=parseInt(t.content.memberNum,10)}else s[a].push(JSON.parse(JSON.stringify({...t,content:{...t.content,orgNos:[t.content.orgNo],memberNum:parseInt(t.content.memberNum,10)},type:1,level:p}))),s[a][s[a].length-1].children=[]}t.children&&t.children.length>0&&t.children.forEach(o)}e.forEach(o),this.orgAllList=this.flattenTree(e);let l=new Map(this.orgAllList.map(t=>[t.content.orgNo+"",t])),r=[];for(let[t,a]of Object.entries(s))for(let h=0;h<a.length;h++){let c=a[h],p=Array.from(new Set(this.userList.filter(u=>u.orgList.filter(k=>c.content.orgNos.indexOf(k.orgNo)>-1).length>0).map(u=>u.userName)));c.userNames=p,c.content.memberNum=p.length;let i=l.get(c.content.parentNo+"");i&&(c.content.parentExt1=i.content.ext1),r.push(c)}let n=this.buildTree(r);return n.forEach(t=>{this.calculateAccountCount(t)}),n},calculateAccountCount(e){const s=new Set;return e.userNames&&e.userNames.forEach(o=>s.add(o)),e.children&&e.children.forEach(o=>{this.calculateAccountCount(o).forEach(r=>s.add(r))}),e.content.memberNum=s.size,s},buildTree(e,s=null){if(!e)return[];let o=e.filter(r=>r.level===1),l=o;do{let r=[],n,t;for(let a=0;a<l.length;a++)for(let h=0;h<e.length;h++)n=e[h].content.parentExt1||e[h].content.parentName,t=l[a].content.ext1||l[a].content.name,n===t&&(l[a].children.push(e[h]),r.push(e[h]));l=r}while(l.length>0);return o},flattenTree(e){let s=e,o=[];do{let r=[];for(let n=0;n<s.length;n++){var l=s[n];if(l.children)for(let t=0;t<l.children.length;t++)r.push(l.children[t]);o.push(JSON.parse(JSON.stringify(l))),o[o.length-1].children=[]}s=r}while(s.length>0);return o},getSubTree(e,s=[]){if(!e)return[];let o=e.filter(r=>s.indexOf(r.content.parentNo)>-1),l=o;do{let r=[];for(let n=0;n<l.length;n++)for(let t=0;t<e.length;t++)l[n].content.orgNos.indexOf(e[t].content.parentNo)>-1&&(o.push(e[t]),r.push(e[t]));l=r}while(l.length>0);return o},getUnitOrgName(e){return e.unitName&&e.orgName?e.unitName+"/"+e.orgName:e.unitName?e.unitName:e.orgName?e.orgName:""}}},X={key:0,class:"con"},Y=["onClick"],Z={key:0,class:"folder"},q={class:"folder-name"},G={class:"folder-count"},K={key:1,class:"people"},$={class:"people-left"},ee={key:0,class:"people-job"},te={key:0},se={key:1},ne={key:2},re={class:"people-right"};function oe(e,s,o,l,r,n){const t=d("Navbar"),a=d("van-search"),h=d("head-portrait"),c=d("tel-phone"),p=d("van-empty");return g(),m(b,null,[y(t,{title:r.breadcrumbList.length?n.currentBreadcrumb.name:"通讯录",back:"",backEvent:n.onBack},null,8,["title","backEvent"]),y(a,{modelValue:r.keyword,"onUpdate:modelValue":[s[0]||(s[0]=i=>r.keyword=i),n.onSearch],placeholder:"搜索","show-action":!1,onSearch:n.onSearch},null,8,["modelValue","onSearch","onUpdate:modelValue"]),f("div",{class:x(r.isOnSearch?"view-height onSearch":"view-height")},[r.list&&r.list.length?(g(),m("div",X,[(g(!0),m(b,null,T(r.list,(i,u)=>(g(),m("div",{key:u,class:"list",onClick:k=>n.openNext(i,u)},[y(h,{type:i.type,"img-url":n.filterImg(i),text:i.type===1?i.ext1||i.name:i.userFullname,class:"head-portrait"},null,8,["type","img-url","text"]),i.type===1?(g(),m("div",Z,[f("div",q,_(i.content.ext1||i.content.name),1),f("div",G,"（"+_(i.content.memberNum)+"）",1)])):(g(),m("div",K,[f("div",$,[f("div",null,[f("span",null,_(i.name||i.userFullname),1),i.title?(g(),m("span",ee,_(i.title),1)):N("",!0)]),i.isLocked===-1?(g(),m("div",te," 手机号："+_(i.phone),1)):(g(),m("div",se," 手机号：************** ")),r.isOnSearch&&n.getUnitOrgName(i)?(g(),m("div",ne,_(n.getUnitOrgName(i)),1)):N("",!0)]),f("div",re,[i.isLocked===-1?(g(),v(c,{key:0,phone:i.phone,callImgStyle:r.isOnSearch&&n.getUnitOrgName(i)?{top:"11px"}:{}},null,8,["phone","callImgStyle"])):N("",!0)])]))],8,Y))),128))])):N("",!0),!r.list||r.list.length<1?(g(),v(p,{key:1,description:r.none},null,8,["description"])):N("",!0)],2)],64)}const ue=L(W,[["render",oe],["__scopeId","data-v-4b6eda66"]]);export{ue as default};
