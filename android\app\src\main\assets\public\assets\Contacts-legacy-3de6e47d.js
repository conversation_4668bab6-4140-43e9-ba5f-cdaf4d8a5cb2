System.register(["./index-legacy-09188690.js","./verder-legacy-e6127216.js","./file-legacy-e670f35e.js","./common-legacy-e7aae0fd.js","./vant-legacy-b51a9379.js"],(function(e,t){"use strict";var n,o,l,a,s,i,r,c,h,d,g,m,p,u,f,v,N,w,b,y;return{setters:[e=>{n=e._,o=e.h,l=e.A},e=>{a=e.Q,s=e.R,i=e.S,r=e.X,c=e.F,h=e.Y,d=e.y,g=e.k,m=e.A,p=e.V,u=e.U,f=e.t,v=e.v,N=e.W,w=e.Z},e=>{b=e.g},e=>{y=e.g},null],execute:function(){var t=document.createElement("style");t.textContent=".head[data-v-b71a23cc]{width:10.66667vw;height:10.66667vw;border-radius:1.06667vw;display:flex;justify-content:center;align-items:center;background:rgba(56,149,255,.6);overflow:hidden}.head.text-head[data-v-b71a23cc]{font-size:5.33333vw;color:#fff}.head .head-img[data-v-b71a23cc]{max-width:100%;max-height:100%}.head .head-img-portrait[data-v-b71a23cc]{width:100%;height:100%;background:#3895ff}.phone-img[data-v-4f303594]{width:100%;height:100%}.call-img[data-v-4f303594]{background-color:#74a7f6;padding:2.13333vw;border-radius:1.06667vw}.title[data-v-4f303594],.phone-btn[data-v-4f303594]{text-align:center}.title[data-v-4f303594]{margin:5.33333vw 0}.phone-btn[data-v-4f303594]{font-size:3.73333vw;color:#333;padding:5.33333vw 0}.loading-overlay[data-v-4b6eda66]{display:flex;justify-content:center;align-items:center}.view-height[data-v-4b6eda66]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height) - 17.06667vw);margin-top:2.66667vw;overflow-x:hidden;overflow-y:scroll;background-color:#fff;font-size:3.73333vw}.view-height .title[data-v-4b6eda66]{padding:2.66667vw 3.73333vw 1.6vw}.view-height .con .list[data-v-4b6eda66]{padding:2.66667vw 3.73333vw;border-bottom:1px solid #f6f6f6;display:flex;position:relative;align-items:center}.view-height .con .list .head-portrait[data-v-4b6eda66]{margin-right:2.66667vw}.view-height .con .list .folder[data-v-4b6eda66]{flex:1;display:flex;align-items:center;width:calc(100% - 13.33333vw)}.view-height .con .list .folder .folder-name[data-v-4b6eda66]{max-width:76%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.view-height .con .list .people[data-v-4b6eda66]{flex:1;display:flex;align-items:center;justify-content:space-between}.view-height .con .list .people-left[data-v-4b6eda66]{flex:1;display:flex;flex-direction:column;justify-content:space-between}.view-height .con .list .people-left .people-job[data-v-4b6eda66]{font-size:3.2vw;color:#6d6d6d;margin-left:4.8vw}.view-height .con .list .people-right[data-v-4b6eda66]{width:10.66667vw;height:10.66667vw;flex-shrink:0;margin-left:1.6vw}\n",document.head.appendChild(t);const x={key:1},L={class:"phone-img"},k={href:"",ref:"tels"},S={key:0,class:"con"},A=["onClick"],O={key:0,class:"folder"},T={class:"folder-name"},C={class:"folder-count"},I={key:1,class:"people"},E={class:"people-left"},U={key:0,class:"people-job"},P={key:0},j={key:1},B={key:2},_={class:"people-right"};e("default",n({components:{HeadPortrait:n({name:"HeadPortrait",props:{type:{type:Number,default:1},imgUrl:{},text:{}},data:()=>({folderImg:"/app/assets/folder-29fb0457.svg"})},[["render",function(e,t,n,o,l,g){const m=a("van-image");return s(),i("div",{class:d(["head",{"text-head":!n.imgUrl&&2===n.type}])},[1===n.type?(s(),r(m,{key:0,src:l.folderImg,class:"head-img"},null,8,["src"])):(s(),i(c,{key:1},[n.imgUrl?(s(),r(m,{key:0,fit:"cover",src:n.imgUrl,class:"head-img-portrait"},null,8,["src"])):(s(),i("span",x,h(n.text.charAt(0)),1))],64))],2)}],["__scopeId","data-v-b71a23cc"]]),TelPhone:n({name:"TelPhone",props:{phone:{},callImgStyle:{}},data:()=>({showBottom:!1,callImg:"/app/assets/call-e73473de.svg"}),methods:{openCall(){this.showBottom=!0},call(){console.log(this.phone),console.log(this.$refs),this.$refs.tels.href=`tel://${this.phone}`,this.$refs.tels.click(),this.onClose()},onClose(){this.showBottom=!1}}},[["render",function(e,t,n,o,l,r){const c=a("van-image"),h=a("van-popup");return s(),i("div",L,[g(c,{onClick:r.openCall,src:l.callImg,style:m(n.callImgStyle),class:"call-img"},null,8,["onClick","src","style"]),g(h,{show:l.showBottom,"onUpdate:show":t[2]||(t[2]=e=>l.showBottom=e),position:"bottom",style:{height:"300px"},teleport:"#app"},{default:p((()=>[t[3]||(t[3]=u("div",{class:"title"},"是否联系用户",-1)),u("div",{class:"phone-btn",onClick:t[0]||(t[0]=(...e)=>r.call&&r.call(...e))},"确认拨打"),u("div",{class:"phone-btn",onClick:t[1]||(t[1]=(...e)=>r.onClose&&r.onClose(...e))},"取消")])),_:1,__:[3]},8,["show"]),f(u("a",k,null,512),[[v,!1]])])}],["__scopeId","data-v-4f303594"]])},data:()=>({keyword:"",breadcrumbList:[],searchParams:{searchValue:"",orgNos:[]},list:[],orgList:[],orgAllList:[],userList:[],none:"",isOnSearch:!1,loading:!1}),computed:{currentBreadcrumb(){return this.breadcrumbList[this.breadcrumbList.length-1]}},watch:{keyword(e){""===e&&this.onSearch(e)}},async created(){this.loading=!0,this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),await this.getUserList(),this.$closeToast(),this.loading=!1,setTimeout((()=>{this.none="暂无数据"}),2e3)},methods:{async getList(){this.isOnSearch=!1;let e=[];if(this.breadcrumbList.length>0){var t=this.currentBreadcrumb.orgNos;for(let n=0;n<this.orgAllList.length;n++){let o=this.orgAllList[n];t.indexOf(o.content.parentNo)>-1&&e.push(o)}this.searchParams.orgNos=[...t]}else{let t=[];for(let n=0;n<this.orgAllList.length;n++){let o=this.orgAllList[n];2===o.level&&e.push(o),1===o.level&&o.content.orgNos.forEach((e=>{t.push(e)}))}this.searchParams.orgNos=[...t]}this.list=[...e,...this.getSearchList()]},getSearchList(){const e=this.searchParams.searchValue,t=this.searchParams.orgNos;if(this.isOnSearch&&t&&t.length>0){let e=this.getSubTree(this.orgAllList,t).map((e=>e.content.orgNos));for(let n=0;n<e.length;n++)for(let o=0;o<e[n].length;o++)t.push(e[n][o])}return this.userList.filter((n=>!(e&&!n.userFullname.includes(e)||t&&t.length>0&&n.orgList.filter((e=>t.indexOf(e.orgNo)>-1)).length<1)))},onSearch(){this.loading=!0,this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),this.searchParams.searchValue=this.keyword,this.searchParams.searchValue?(this.isOnSearch=!0,this.list=this.getSearchList()):this.getList(),this.$closeToast(),this.loading=!1},onBack(){var e;(null===(e=this.breadcrumbList)||void 0===e?void 0:e.length)>0?(this.breadcrumbList.pop(),this.searchParams.searchValue="",this.keyword="",this.getList()):this.$router.replace({name:"Home"})},openNext(e,t){2!==e.type&&(this.breadcrumbList.push({name:e.content.ext1||e.content.name,level:e.level,orgNos:e.content.orgNos}),this.keyword="",this.searchParams.searchValue="",this.getList())},filterImg:e=>b(e.photoToken),async getAllOrg(){await y().then((e=>{let t=this.mergeNodesByName(e);this.orgList=[...t],this.orgAllList=this.flattenTree(this.orgList),this.initUserOrg(),this.getList(),setTimeout((()=>{this.loading=!1,this.$closeToast()}),100)})).catch((()=>{this.loading=!1,this.$closeToast()}))},initUserOrg(){let e=new Map(this.orgAllList.map((e=>[e.content.orgNo+"",e])));for(let t=0;t<this.userList.length;t++){this.userList[t].type=2;const n=this.userList[t].orgNos;if(n){let o=Array.from(new Set(n.split(",").map((t=>{let n=e.get(t);if(n)return n.content.ext1?n.content.ext1:n.content.name;for(var o=0;o<this.orgAllList.length;o++){var l;let e=this.orgAllList[o];null==e||null===(l=e.content)||void 0===l||l.orgNos;for(let n=0;n<e.content.orgNos.length;n++)if(e.content.orgNos[n]+""===t)return e.content.ext1?e.content.ext1:e.content.name}})).filter((e=>null!=e&&""!==e)))).join(",");this.userList[t].orgName=o}const o=this.userList[t].unitNos;if(o){let n=Array.from(new Set(o.split(",").map((t=>{let n=e.get(t);if(n)return n.content.ext1?n.content.ext1:n.content.name;for(var o=0;o<this.orgAllList.length;o++){var l;let e=this.orgAllList[o];null==e||null===(l=e.content)||void 0===l||l.orgNos;for(let n=0;n<e.content.orgNos.length;n++)if(e.content.orgNos[n]+""===t)return e.content.ext1?e.content.ext1:e.content.name}})).filter((e=>null!=e&&""!==e)))).join(",");this.userList[t].unitName=n}}},async getUserList(){var e;this.userList.length<1&&await o({url:`${l.VUE_APP_BASE_API_SERVICENAME}/general-contacts/list`,method:"get",params:e}).then((e=>{this.userList=e||[],this.getAllOrg()})).catch((()=>{this.$closeToast(),this.loading=!1}))},mergeNodesByName(e){var t={};e.forEach((function e(n){let{content:{name:o},content:{ext1:l},content:{pathNo:a}}=n;const s=a.split(".").filter(Boolean).length-1;if(l&&(o=l),t[o]){let e=t[o].filter((e=>e.content.parentName===n.content.parentName));if(e.length>0){let t=e[0];s>=t.level&&(t.level=s,t.type=1,t.id=n.id,t.parentId=n.parentId,t.content.orgNo=n.content.orgNo,t.content.parentNo=n.content.parentNo,t.content.parentName=n.content.parentName,t.content.pathNo=n.content.pathNo,t.content.pathName=n.content.pathName,t.content.sort=n.content.sort,t.content.ext1=n.content.ext1),t.content.orgNos.push(n.content.orgNo),t.content.memberNum+=parseInt(n.content.memberNum,10)}else t[o].push(JSON.parse(JSON.stringify({...n,content:{...n.content,orgNos:[n.content.orgNo],memberNum:parseInt(n.content.memberNum,10)},type:1,level:s}))),t[o][t[o].length-1].children=[]}else t[o]=[JSON.parse(JSON.stringify({...n,content:{...n.content,orgNos:[n.content.orgNo],memberNum:parseInt(n.content.memberNum,10)},type:1,level:s}))],t[o][0].children=[];n.children&&n.children.length>0&&n.children.forEach(e)})),this.orgAllList=this.flattenTree(e);let n=new Map(this.orgAllList.map((e=>[e.content.orgNo+"",e]))),o=[];for(let[a,s]of Object.entries(t))for(let e=0;e<s.length;e++){let t=s[e],l=Array.from(new Set(this.userList.filter((e=>e.orgList.filter((e=>t.content.orgNos.indexOf(e.orgNo)>-1)).length>0)).map((e=>e.userName))));t.userNames=l,t.content.memberNum=l.length;let a=n.get(t.content.parentNo+"");a&&(t.content.parentExt1=a.content.ext1),o.push(t)}let l=this.buildTree(o);return l.forEach((e=>{this.calculateAccountCount(e)})),l},calculateAccountCount(e){const t=new Set;return e.userNames&&e.userNames.forEach((e=>t.add(e))),e.children&&e.children.forEach((e=>{this.calculateAccountCount(e).forEach((e=>t.add(e)))})),e.content.memberNum=t.size,t},buildTree(e,t=null){if(!e)return[];let n=e.filter((e=>1===e.level)),o=n;do{let t,n,l=[];for(let a=0;a<o.length;a++)for(let s=0;s<e.length;s++)t=e[s].content.parentExt1||e[s].content.parentName,n=o[a].content.ext1||o[a].content.name,t===n&&(o[a].children.push(e[s]),l.push(e[s]));o=l}while(o.length>0);return n},flattenTree(e){let t=e,n=[];do{let e=[];for(let l=0;l<t.length;l++){var o=t[l];if(o.children)for(let t=0;t<o.children.length;t++)e.push(o.children[t]);n.push(JSON.parse(JSON.stringify(o))),n[n.length-1].children=[]}t=e}while(t.length>0);return n},getSubTree(e,t=[]){if(!e)return[];let n=e.filter((e=>t.indexOf(e.content.parentNo)>-1)),o=n;do{let t=[];for(let l=0;l<o.length;l++)for(let a=0;a<e.length;a++)o[l].content.orgNos.indexOf(e[a].content.parentNo)>-1&&(n.push(e[a]),t.push(e[a]));o=t}while(o.length>0);return n},getUnitOrgName:e=>e.unitName&&e.orgName?e.unitName+"/"+e.orgName:e.unitName?e.unitName:e.orgName?e.orgName:""}},[["render",function(e,t,n,o,l,m){const p=a("Navbar"),f=a("van-search"),v=a("head-portrait"),b=a("tel-phone"),y=a("van-empty");return s(),i(c,null,[g(p,{title:l.breadcrumbList.length?m.currentBreadcrumb.name:"通讯录",back:"",backEvent:m.onBack},null,8,["title","backEvent"]),g(f,{modelValue:l.keyword,"onUpdate:modelValue":[t[0]||(t[0]=e=>l.keyword=e),m.onSearch],placeholder:"搜索","show-action":!1,onSearch:m.onSearch},null,8,["modelValue","onSearch","onUpdate:modelValue"]),u("div",{class:d(l.isOnSearch?"view-height onSearch":"view-height")},[l.list&&l.list.length?(s(),i("div",S,[(s(!0),i(c,null,N(l.list,((e,t)=>(s(),i("div",{key:t,class:"list",onClick:n=>m.openNext(e,t)},[g(v,{type:e.type,"img-url":m.filterImg(e),text:1===e.type?e.ext1||e.name:e.userFullname,class:"head-portrait"},null,8,["type","img-url","text"]),1===e.type?(s(),i("div",O,[u("div",T,h(e.content.ext1||e.content.name),1),u("div",C,"（"+h(e.content.memberNum)+"）",1)])):(s(),i("div",I,[u("div",E,[u("div",null,[u("span",null,h(e.name||e.userFullname),1),e.title?(s(),i("span",U,h(e.title),1)):w("",!0)]),-1===e.isLocked?(s(),i("div",P," 手机号："+h(e.phone),1)):(s(),i("div",j," 手机号：************** ")),l.isOnSearch&&m.getUnitOrgName(e)?(s(),i("div",B,h(m.getUnitOrgName(e)),1)):w("",!0)]),u("div",_,[-1===e.isLocked?(s(),r(b,{key:0,phone:e.phone,callImgStyle:l.isOnSearch&&m.getUnitOrgName(e)?{top:"11px"}:{}},null,8,["phone","callImgStyle"])):w("",!0)])]))],8,A)))),128))])):w("",!0),!l.list||l.list.length<1?(s(),r(y,{key:1,description:l.none},null,8,["description"])):w("",!0)],2)],64)}],["__scopeId","data-v-4b6eda66"]]))}}}));
