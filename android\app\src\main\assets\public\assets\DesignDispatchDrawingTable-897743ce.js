import{_ as c}from"./index-4829f8e2.js";import{Q as d,R as m,X as p,V as f,k as n,S as s,F as i,W as b}from"./verder-361ae6c7.js";import{U as g}from"./index-fc22947f.js";const U={name:"DesignDispatchCommonTopNew",props:{formData:{type:Object,default:()=>({})},type:{},showContentDescription:{type:Boolean,default:!0},readonly:{type:Boolean,default:!0}}};function v(D,a,e,y,V,_){const o=d("van-field"),u=d("van-cell-group");return m(),p(u,{border:!1},{default:f(()=>[n(o,{modelValue:e.formData.fileName,"onUpdate:modelValue":a[0]||(a[0]=l=>e.formData.fileName=l),label:"图册名称",readonly:e.readonly,required:""},null,8,["modelValue","readonly"]),n(o,{modelValue:e.formData.relavancePartName,"onUpdate:modelValue":a[1]||(a[1]=l=>e.formData.relavancePartName=l),label:"工程部位",readonly:e.readonly,required:""},null,8,["modelValue","readonly"]),n(o,{modelValue:e.formData.profession,"onUpdate:modelValue":a[2]||(a[2]=l=>e.formData.profession=l),label:"专业",readonly:e.readonly,required:""},null,8,["modelValue","readonly"]),n(o,{modelValue:e.formData.leadDesignerFullname,"onUpdate:modelValue":a[3]||(a[3]=l=>e.formData.leadDesignerFullname=l),label:"主设人",readonly:e.readonly,required:""},null,8,["modelValue","readonly"]),n(o,{modelValue:e.formData.subProjectName,"onUpdate:modelValue":a[4]||(a[4]=l=>e.formData.subProjectName=l),label:"子工程",readonly:e.readonly,required:""},null,8,["modelValue","readonly"])]),_:1})}const q=c(U,[["render",v]]),N={name:"DesignDispatchDrawingTable",components:{UploadFiles:g},props:{formDetailTable:{type:Array,default:()=>[]},formData:{type:Object,default:()=>({})},type:{}}};function F(D,a,e,y,V,_){const o=d("van-field"),u=d("UploadFiles"),l=d("van-cell-group");return m(),p(l,{border:!1},{default:f(()=>[(m(!0),s(i,null,b(e.formDetailTable,t=>(m(),s(i,null,[n(o,{modelValue:t.fileName,"onUpdate:modelValue":r=>t.fileName=r,label:"图纸名称",readonly:""},null,8,["modelValue","onUpdate:modelValue"]),n(o,{modelValue:t.fileCode,"onUpdate:modelValue":r=>t.fileCode=r,label:"图纸编号",readonly:""},null,8,["modelValue","onUpdate:modelValue"]),n(o,{label:"图纸附件","label-align":"top","input-align":"left"},{input:f(()=>[n(u,{ref_for:!0,ref:"afterFiles",g9s:t.attachment,"onUpdate:g9s":r=>t.attachment=r,readonly:!0},null,8,["g9s","onUpdate:g9s"])]),_:2},1024)],64))),256))]),_:1})}const x=c(N,[["render",F]]);export{q as D,x as a};
