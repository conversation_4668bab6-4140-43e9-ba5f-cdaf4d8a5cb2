import{_ as y,o as k}from"./index-4829f8e2.js";import{V as I,a as w,b as M}from"./index-889f88bd.js";import{b as x,d as D,c as L}from"./file-842bc27d.js";import{Q as n,R as r,S as a,k as P,U as f,X as l,Z as g,V as b,B as F,Y as U,F as V}from"./verder-361ae6c7.js";import"./vant-91101745.js";const E={name:"Detail",components:{VuePdfEmbed:I,VueOfficeDocx:w,VueOfficeExcel:M},props:{},emits:[],setup(e,{attrs:o,slots:m,emit:t}){return{isPdf:x,isXls:D,isDoc:L,isImage:i=>{if(!i)return!1;const d=i.split(".").pop().toLowerCase();return["jpg","jpeg","png","gif","bmp","webp"].includes(d)}}},data(){return{fileInfo:null,refreshing:!1,errorMsg:"",loading:!1,loadingMsg:"加载中...",fileUrl:null}},computed:{},watch:{},created(){var e;try{const o=(e=this.$route.query)==null?void 0:e.fileData;if(o){if(this.fileInfo=JSON.parse(o),!this.fileInfo||!this.fileInfo.fileToken){this.errorMsg="未找到文件信息",this.loading=!1;return}this.initFilePreview()}else this.errorMsg="未找到文件信息"}catch(o){console.error("解析文件信息出错",o),this.errorMsg="文件信息解析失败",this.loading=!1}},mounted(){},methods:{initFilePreview(){this.loading=!0;try{if(!this.fileInfo||!this.fileInfo.fileToken){this.errorMsg="未找到文件信息",this.loading=!1;return}const e=k({f8s:this.fileInfo.fileToken}),o="/api/sys-storage/download";this.fileUrl="".concat(o,"?").concat(e)}catch(e){console.error("文件预览加载失败",e),this.errorMsg="文件加载失败，请稍后重试",this.loading=!1}},pdfLoadingFailed(e){console.error("PDF加载失败",e),this.errorMsg="PDF文件加载失败",this.loading=!1},pdfProgress(e){if(e.loaded&&e.total){const o=Math.round(e.loaded/e.total*100);this.loadingMsg="加载中... ".concat(o,"%"),o===100&&setTimeout(()=>{this.loading=!1},100)}},rendered(){this.loading=!1},imageLoaded(){this.loading=!1},imageLoadError(){this.errorMsg="图片加载失败",this.loading=!1}}},N={class:"view-height"},B={key:1,class:"news-detail"},O={class:"file-preview-wrapper"},R={key:0,class:"file-container"},T={key:0,class:"image-preview"},C=["src"];function S(e,o,m,t,s,i){const d=n("Navbar"),_=n("van-empty"),h=n("vue-pdf-embed"),p=n("vue-office-docx"),u=n("vue-office-excel"),v=n("van-loading");return r(),a(V,null,[P(d,{back:""}),f("div",N,[s.errorMsg?(r(),l(_,{key:0,image:"error",description:s.errorMsg},null,8,["description"])):(r(),a("div",B,[f("div",O,[s.fileUrl?(r(),a("div",R,[t.isImage(s.fileInfo.name)?(r(),a("div",T,[f("img",{src:s.fileUrl,alt:"图片预览",onLoad:o[0]||(o[0]=(...c)=>i.imageLoaded&&i.imageLoaded(...c)),onError:o[1]||(o[1]=(...c)=>i.imageLoadError&&i.imageLoadError(...c))},null,40,C)])):t.isPdf(s.fileInfo.name)?(r(),l(h,{key:1,source:s.fileUrl,onLoadingFailed:i.pdfLoadingFailed,onProgress:i.pdfProgress},null,8,["source","onLoadingFailed","onProgress"])):t.isDoc(s.fileInfo.name)?(r(),l(p,{key:2,src:s.fileUrl,style:{height:"100%",width:"100%"},onRendered:i.rendered},null,8,["src","onRendered"])):t.isXls(s.fileInfo.name)?(r(),l(u,{key:3,src:s.fileUrl,style:{height:"100%",width:"100%"},onRendered:i.rendered},null,8,["src","onRendered"])):g("",!0)])):g("",!0)])])),s.loading?(r(),l(v,{key:2,type:"spinner",vertical:"",class:"loading"},{default:b(()=>[F(U(s.loadingMsg),1)]),_:1})):g("",!0)])],64)}const Q=y(E,[["render",S],["__scopeId","data-v-4ca47c19"]]);export{Q as default};
