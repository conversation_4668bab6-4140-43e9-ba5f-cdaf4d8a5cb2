import{F as V}from"./FormItemPicker-d3f69283.js";import{F as M}from"./FormItemDate-ba00d9d5.js";import{_ as q}from"./FormItemSection-e3118d02.js";import{F as P}from"./FormItemCoord-9e82e1bf.js";import{a as B,b as H,c as j,e as x}from"./api-d09fbc1f.js";import{R as O}from"./constants-94a272fa.js";import{_ as E,D as S}from"./index-4829f8e2.js";import{Q as d,R as o,S as p,k as l,V as m,X as f,a2 as v,Z as b,U as r,Y as u,B as y,F as D,y as Y,W as C}from"./verder-361ae6c7.js";import{F as W}from"./FormItemCalendar-905fde75.js";import"./validate-2249584f.js";import"./vant-91101745.js";function G(e){const t=e,i=[];return e.forEach(_=>{_.children&&_.children.forEach(a=>{i.push({label:a.content?a.content.name:"",value:a.content?a.content.id:""})})}),{departmentList:t,departmentOptions:i}}const J={name:"ListItem",components:{},props:{item:{type:Object,default:()=>({})},navbarType:String},emits:["detailItem"],setup(e,{attrs:t,slots:i,emit:_}){},data(){return{statusMap:O}},watch:{},created(){},computed:{statusTypeMap(){var e;return((e=this.statusMap)==null?void 0:e.TYPE_MAP)||{}},statusLabelMap(){var e;return((e=this.statusMap)==null?void 0:e.LABEL_MAP)||{}},toStartReviewStatus(){var e;return(e=this.statusMap)==null?void 0:e.PENDING_REVIEW}},methods:{hanelDel(){this.$emit("detailItem")}}},K={class:"task-item"},Q={class:"body"},X={class:"item-info header"},Z={class:"value"},$={class:"item-info"},ee={class:"value"},te={class:"item-info"},ae={class:"value"},se={class:"item-info"},ne={class:"value"},ie={class:"item-info"},oe={class:"value"},re={class:"right"};function le(e,t,i,_,a,n){const g=d("van-tag"),I=d("van-button"),c=d("van-swipe-cell");return o(),p("div",K,[l(c,{disabled:!["add","update"].includes(i.navbarType)},{right:m(()=>[["add","update"].includes(i.navbarType)?(o(),f(I,{key:0,square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:i.item.rectificationStatus!==n.toStartReviewStatus,onClick:v(n.hanelDel,["stop","prevent"])},null,8,["disabled","onClick"])):b("",!0)]),default:m(()=>[r("div",Q,[r("div",X,[t[0]||(t[0]=r("span",{class:"key"},"隐患编号",-1)),r("span",Z,u(i.item.hazardNumber),1)]),r("div",$,[t[1]||(t[1]=r("span",{class:"key"},"所属标段",-1)),r("span",ee,u(e.$formatLabel(i.item.sectionId,e.$DICT_CODE.project_section)),1)]),r("div",te,[t[2]||(t[2]=r("span",{class:"key"},"隐患级别",-1)),r("span",ae,u(e.$formatLabel(i.item.level,e.$DICT_CODE.safe_hazard_level)),1)]),r("div",se,[t[3]||(t[3]=r("span",{class:"key"},"整改期限",-1)),r("span",ne,u(e.$dayjs(i.item.deadline).format("YYYY-MM-DD")),1)]),r("div",ie,[t[4]||(t[4]=r("span",{class:"key"},"整改内容",-1)),r("span",oe,u(i.item.description),1)]),r("div",re,[l(g,{class:"tag",color:n.statusTypeMap[i.item.rectificationStatus],plain:"",size:"medium"},{default:m(()=>[y(u(n.statusLabelMap[i.item.rectificationStatus]),1)]),_:1},8,["color"])])])]),_:1},8,["disabled"])])}const de=E(J,[["render",le],["__scopeId","data-v-2c3d01cb"]]);const me={name:"SafetyCheckDetail",components:{FormItemSection:q,FormItemCalendar:W,ListItem:de,FormItemCoord:P,FormItemDate:M,FormItemPicker:V},props:{},emits:[],setup(e,{attrs:t,slots:i,emit:_}){},data(){return{loading:!1,error:"",formData:{content:"",evidence:"",hazards:[],id:null,inspectionArea:[],inspectionDate:"",inspectionNumber:"",inspectionResult:"",inspectionType:"",inspectionUnit:"",inspectionUnitName:"",inspector:"",inspectorFullname:"",reporter:"",reporterName:"",sectionId:""},SafeInspectionHazardList:[],departmentOptions:[],statusMap:O}},mounted(){var e;Object.keys(this.$store.SAFE_INSPECTION_FORM).length?this.formData=this.$store.SAFE_INSPECTION_FORM:this.navbarType==="add"?(this.formData.inspectionUnit=(e=this.portal)==null?void 0:e.id,this.formData.inspector=this.user.userName||"",this.formData.inspectorFullname=this.user.userFullname||"",this.formData.reporter=this.user.userName||"",this.formData.reporterName=this.user.userFullname||"",this.formData.sectionId=this.$getPortalId()):this.getFormData(),this.getData()},computed:{safeInspectionAreaList(){return this.$store.ENUM_DICT[S.safe_inspection_area]},safeInspectionResultList(){return this.$store.ENUM_DICT[S.safe_inspection_result]},safeInspectionTypeList(){return this.$store.ENUM_DICT[S.safe_inspection_type]},portalId(){var e;return((e=this.$store.PORTAL)==null?void 0:e.type)===1?this.$store.PORTAL.id:""},portal(){return this.$store.PORTAL},navbarTitle(){return this.$route.query.title||"安全检查详情"},navbarType(){return this.$route.query.type},user(){return this.$store.USER_INFO},inspectionList(){return this.departmentOptions.length?this.departmentOptions:this.$store.INSPECTION_LIST},inspectionUnitLabel(){const e=this.departmentOptions.find(t=>t.value===this.formData.inspectionUnit);return e?e.label:""},inspectionAreaStr(){return this.formData.inspectionArea&&this.formData.inspectionArea.length?this.formData.inspectionArea.map(e=>this.$formatLabel(e,$DICT_CODE.safe_inspection_area)).join("、"):""},toStartReviewStatus(){var e;return(e=this.statusMap)==null?void 0:e.PENDING_REVIEW}},methods:{chengeInspectionUnitt(e){},chengeSection(e){this.formData.hazards&&this.formData.hazards.length>0&&this.formData.hazards.forEach(t=>{t.sectionId=this.formData.sectionId,t.projectPosition=null})},getFormData(){B({id:this.$route.query.id}).then(e=>{if(console.log("res==============>",e),e){const t=Array.isArray(e.hazards)?e.hazards:[];t.forEach(i=>{i.types=i.types||[],i.type&&i.types.push(i.type),i.typeChild&&i.types.push(i.typeChild)}),this.formData={...e,inspectionArea:e.inspectionArea?e.inspectionArea.split(","):[],hazards:t},console.log("formData======>",this.formData)}})},getData(){H(this.portalId).then(e=>{if(e){const{departmentOptions:t}=G(e);this.departmentOptions=t,this.$store.INSPECTION_LIST=t}})},handleClose(){this.$store.SAFE_INSPECTION_FORM={},this.$router.go(-1)},handleAddHiddenTrouble(){if(!this.formData.sectionId){this.$showToast({position:"top",message:"请先选择所属标段"});return}this.$store.SAFE_INSPECTION_FORM=this.formData,this.$store.HIDDEN_TROUBLE_INFO={},this.$router.push({name:"HiddenTroubleEdit",query:{type:"add",title:"添加隐患",sectionId:this.formData.sectionId}})},hanelItem(e,t){["add","update"].includes(this.navbarType)&&e.rectificationStatus===this.toStartReviewStatus?(this.$store.SAFE_INSPECTION_FORM=this.formData,this.$store.HIDDEN_TROUBLE_INFO=JSON.parse(JSON.stringify({...e,index:t})),this.$router.push({name:"HiddenTroubleEdit",query:{type:"update",title:"编辑隐患"}})):this.$router.push({path:"FormCenter/SafeInspectionHazard",query:{type:"view",taskKey:e.taskKey,bizId:e.id,taskId:e.taskId}})},async handleAddOrCreate(){try{if(await this.$refs.formDateRef.validate(),this.formData.inspectionResult==="1"&&(!this.formData.hazards||this.formData.hazards.length===0)){this.$showToast({position:"top",message:"请至少添加一条隐患记录"}),setTimeout(()=>{this.$closeToast()},100);return}this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e={...this.formData,inspectionArea:this.formData.inspectionArea.join(","),inspectionDate:this.formData.inspectionDate||null,hazards:this.formData.hazards.map(t=>({...t,rectificationStatus:t.rectificationStatus||"1",acceptanceTime:null,acceptanceResult:null,hazards:this.formData.inspectionResult==="1"?t.hazards:[]}))};this.$route.query.type==="add"?await j(e).then(()=>{this.$router.push({path:"/SafetyCheck"})}).finally(()=>{this.$closeToast()}):this.$route.query.type==="update"&&await x(e).then(()=>{this.$router.push({path:"/SafetyCheck"})}).finally(()=>{this.$closeToast()})}catch(e){console.log(e)}},async handleDelete(e){try{await this.$confirm({title:"提示",message:"确认删除".concat(this.formData.hazards[e].hazardNumber,"?")}),this.formData.hazards.splice(e,1)}catch(t){console.log(t)}}}},pe={key:0},ue={key:0},ce={key:0},fe={key:2},he={key:1,class:"btn-group"};function _e(e,t,i,_,a,n){const g=d("Navbar"),I=d("van-empty"),c=d("van-field"),N=d("van-radio"),k=d("van-radio-group"),F=d("FormItemPicker"),R=d("FormItemSection"),A=d("FormItemDate"),z=d("van-cell-group"),w=d("van-form"),T=d("van-button"),U=d("ListItem");return o(),p(D,null,[l(g,{back:"",title:n.navbarTitle},null,8,["title"]),a.loading?(o(),p(D,{key:0},[],64)):a.error?(o(),f(I,{key:1,image:"error",description:a.error},null,8,["description"])):(o(),p("div",{key:2,class:Y(["view-height",{"btn-bom":["add","update"].includes(n.navbarType)}])},[l(w,{ref:"formDateRef",readonly:n.navbarType==="detail","label-width":"7em","input-align":"right","error-message-align":"right",class:"form-bottom"},{default:m(()=>[l(z,{border:!1},{default:m(()=>[l(c,{modelValue:a.formData.inspectionNumber,"onUpdate:modelValue":t[0]||(t[0]=s=>a.formData.inspectionNumber=s),name:"inspectionNumber",label:"检查编号",readonly:"",placeholder:"自动生成"},null,8,["modelValue"]),l(c,{name:"inspectionType",label:"检查类型",placeholder:"检查类型",labelWidth:"5rem",required:"",rules:[{required:!0,message:"请选择检查类型"}]},{input:m(()=>[n.navbarType==="detail"?(o(),p("span",pe,u(e.$formatLabel(a.formData.inspectionType,e.$DICT_CODE.safe_inspection_type)),1)):(o(),f(k,{key:1,modelValue:a.formData.inspectionType,"onUpdate:modelValue":t[1]||(t[1]=s=>a.formData.inspectionType=s),direction:"horizontal"},{default:m(()=>[(o(!0),p(D,null,C(n.safeInspectionTypeList,(s,h)=>(o(),f(N,{key:h,name:s.code},{default:m(()=>[y(u(s["zh-CN"]),1)]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"]))]),_:1}),l(F,{label:"检查单位",readonly:n.navbarType==="detail",name:"inspectionUnit","input-align":"right",value:a.formData.inspectionUnit,"onUpdate:value":t[2]||(t[2]=s=>a.formData.inspectionUnit=s),text:a.formData.inspectionUnitName,"onUpdate:text":t[3]||(t[3]=s=>a.formData.inspectionUnitName=s),columns:[...n.inspectionList],"columns-field-names":{text:"label",value:"value",children:"none"},title:"检查单位",required:"",rules:[{required:!0,message:"请选择检查单位"}],onChange:n.chengeInspectionUnitt},null,8,["readonly","value","text","columns","onChange"]),l(R,{label:"所属标段",placeholder:"请选择",modelValue:a.formData.sectionId,"onUpdate:modelValue":t[4]||(t[4]=s=>a.formData.sectionId=s),readonly:n.navbarType==="detail"||a.formData.hazards.some(s=>s.rectificationStatus!==n.toStartReviewStatus),required:"",rules:[{required:!0,message:"请选择所属标段"}],onSelect:n.chengeSection},null,8,["modelValue","readonly","onSelect"]),l(A,{label:"检查日期",value:a.formData.inspectionDate,"onUpdate:value":t[5]||(t[5]=s=>a.formData.inspectionDate=s),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择检查日期"}],readonly:n.navbarType==="detail"},null,8,["value","readonly"]),l(c,{label:"详细区域",name:"inspectionArea",modelValue:a.formData.inspectionArea,"onUpdate:modelValue":t[6]||(t[6]=s=>a.formData.inspectionArea=s),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"]),l(c,{name:"inspectionResult",label:"检查结果",placeholder:"检查结果",required:"",rules:[{required:!0,message:"请选择检查结果"}]},{input:m(()=>[n.navbarType==="detail"?(o(),p("span",ue,u(e.$formatLabel(a.formData.inspectionResult,e.$DICT_CODE.quality_inspection_result)),1)):(o(),f(k,{key:1,modelValue:a.formData.inspectionResult,"onUpdate:modelValue":t[7]||(t[7]=s=>a.formData.inspectionResult=s),direction:"horizontal",disabled:a.formData.hazards.some(s=>s.rectificationStatus!==n.toStartReviewStatus)},{default:m(()=>[(o(!0),p(D,null,C(n.safeInspectionResultList,(s,h)=>(o(),f(N,{key:h,name:s.code},{default:m(()=>[y(u(s["zh-CN"]),1)]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue","disabled"]))]),_:1}),l(c,{label:"检查内容",name:"content",modelValue:a.formData.content,"onUpdate:modelValue":t[8]||(t[8]=s=>a.formData.content=s),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入检查内容"}],"input-align":"left"},null,8,["modelValue"]),l(c,{label:"检查依据",name:"evidence",modelValue:a.formData.evidence,"onUpdate:modelValue":t[9]||(t[9]=s=>a.formData.evidence=s),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"])]),_:1})]),_:1},8,["readonly"]),a.formData.inspectionResult==="1"?(o(),p("div",ce,[t[12]||(t[12]=r("div",{class:"potential-safety-hazard-title"}," 安全隐患列表 ",-1)),["add","update"].includes(n.navbarType)?(o(),f(T,{key:0,type:"primary",size:"normal",class:"addBtn",onClick:t[10]||(t[10]=v(s=>n.handleAddHiddenTrouble(),["stop","prevent"]))},{default:m(()=>t[11]||(t[11]=[y("新增安全隐患")])),_:1,__:[11]})):b("",!0),a.formData.hazards&&a.formData.hazards.length?(o(!0),p(D,{key:1},C(a.formData.hazards||[],(s,h)=>(o(),f(U,{key:h,item:s,style:{"margin-top":"10px"},onClick:v(L=>n.hanelItem(s,h),["stop","prevent"]),navbarType:n.navbarType,onDetailItem:L=>n.handleDelete(h)},null,8,["item","onClick","navbarType","onDetailItem"]))),128)):(o(),p("div",fe,[l(I,{description:"暂无数据",style:{padding:"0"}})]))])):b("",!0),["add","update"].includes(n.navbarType)?(o(),p("div",he,[l(T,{round:"",type:"danger",plain:"",onClick:v(n.handleClose,["stop","prevent"])},{default:m(()=>t[13]||(t[13]=[y("取消")])),_:1,__:[13]},8,["onClick"]),l(T,{round:"",type:"primary",plain:"",onClick:v(n.handleAddOrCreate,["stop","prevent"])},{default:m(()=>t[14]||(t[14]=[y("保存")])),_:1,__:[14]},8,["onClick"])])):b("",!0)],2))],64)}const Oe=E(me,[["render",_e],["__scopeId","data-v-865c3420"]]);export{Oe as default};
