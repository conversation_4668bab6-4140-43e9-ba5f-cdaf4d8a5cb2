import{a as m,b as u}from"./api-5f0f0fe0.js";import{_ as f}from"./index-4829f8e2.js";import{Q as i,R as r,S as n,k as l,U as o,V as v,X as y,Y as k,F as D}from"./verder-361ae6c7.js";import"./vant-91101745.js";const M={name:"Detail",components:{},props:{},emits:[],setup(e,{attrs:s,slots:a,emit:c}){},data(){var e;return{id:(e=this.$route.query)==null?void 0:e.id,refreshing:!1,loading:!1,errorMsg:"",detail:{}}},computed:{},watch:{},created(){},mounted(){this.onRefresh()},methods:{onRefresh(){this.getDetail()},async getDetail(){if(this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),!this.id){this.errorMsg="未找到相关信息",setTimeout(()=>{this.loading=!1,this.$closeToast()},100),this.refreshing=!1;return}try{this.loading=!0;const e={receiveLogId:this.id},s=await m(e);this.detail=s||{},this.errorMsg="",this.updateStatus()}catch(e){this.errorMsg="出错了, 请稍后重试!",console.log(e)}finally{this.loading=!1,this.refreshing=!1,setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}},async updateStatus(){var e,s;if(!(!((e=this==null?void 0:this.detail)!=null&&e.receiveLogId)||((s=this==null?void 0:this.detail)==null?void 0:s.status)!="-1"))try{await u({receiveLogIdList:this.detail.receiveLogId,status:0})}catch(a){console.log(a)}}}},T={class:"view-height"},b={key:1,class:"message-detail"},L={class:"content"},w=["innerHTML"],R={class:"aside"};function V(e,s,a,c,t,d){const h=i("Navbar"),p=i("van-empty"),_=i("van-pull-refresh");return r(),n(D,null,[l(h,{back:""}),o("div",T,[l(_,{modelValue:t.refreshing,"onUpdate:modelValue":s[0]||(s[0]=g=>t.refreshing=g),onRefresh:d.onRefresh},{default:v(()=>[t.errorMsg?(r(),y(p,{key:0,image:"error",description:t.errorMsg},null,8,["description"])):(r(),n("div",b,[o("div",L,[o("div",{innerHTML:t.detail.msgBody},null,8,w)]),o("div",R,[o("span",null,k(t.detail.createDate),1)])]))]),_:1},8,["modelValue","onRefresh"])])],64)}const N=f(M,[["render",V],["__scopeId","data-v-88923ba6"]]);export{N as default};
