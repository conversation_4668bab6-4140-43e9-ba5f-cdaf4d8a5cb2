System.register(["./FormItemPicker-legacy-fd45c24d.js","./FormItemDate-legacy-c4422d42.js","./FormItemSection-legacy-66423754.js","./FormItemCoord-legacy-e6ddc9b7.js","./api-legacy-53a10b6c.js","./constants-legacy-82bb4fe6.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js","./FormItemCalendar-legacy-ea787ea1.js","./validate-legacy-4e8f0db9.js","./vant-legacy-b51a9379.js"],(function(e,t){"use strict";var a,i,n,o,s,r,l,d,c,p,m,u,h,v,f,y,g,b,D,w,I,_,T,k,S,C;return{setters:[e=>{a=e.F},e=>{i=e.F},e=>{n=e._},e=>{o=e.F},e=>{s=e.a,r=e.b,l=e.c,d=e.e},e=>{c=e.R},e=>{p=e._,m=e.D},e=>{u=e.Q,h=e.R,v=e.S,f=e.k,y=e.V,g=e.X,b=e.a2,D=e.Z,w=e.U,I=e.Y,_=e.B,T=e.F,k=e.y,S=e.W},e=>{C=e.F},null,null],execute:function(){var t=document.createElement("style");t.textContent=".task-item[data-v-2c3d01cb]{background-color:#fff;border-radius:1.33333vw;padding:2.66667vw;margin-bottom:2.66667vw}.task-item .body[data-v-2c3d01cb]{padding:2.66667vw 1.33333vw 1.33333vw;position:relative}.task-item .body .item-info[data-v-2c3d01cb]{display:flex;flex-direction:row;align-items:center;font-size:3.73333vw;color:#9a9a9a;line-height:1;padding:1.33333vw 0}.task-item .body .item-info>.key[data-v-2c3d01cb]{min-width:5em}.task-item .body .item-info>.value[data-v-2c3d01cb]{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#333}.task-item .body .right[data-v-2c3d01cb]{position:absolute;top:1.33333vw;right:1.06667vw}.task-item .body .right .tag[data-v-2c3d01cb]{padding:1.6vw 3.2vw;text-align:center}.task-item .body .detail[data-v-2c3d01cb]{position:absolute;bottom:1.33333vw;right:0}.van-button--disabled[data-v-2c3d01cb]{background-color:#1d2129;border:none}.view-height[data-v-865c3420]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat));padding:0 2.66667vw;overflow-x:hidden;overflow-y:scroll}.view-height.has-tab[data-v-865c3420]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height))}.view-height.btn-bom[data-v-865c3420]{padding:0 2.66667vw 13.33333vw!important}.attend-con[data-v-865c3420]{padding:var(--van-cell-vertical-padding) var(--van-cell-horizontal-padding);color:var(--van-cell-text-color);font-size:var(--van-cell-font-size)}.attend-con .user-list[data-v-865c3420]{margin-top:2.66667vw;display:flex;gap:2.66667vw;flex-wrap:wrap}.attend-con .user-list .user-item[data-v-865c3420]{display:flex;align-items:center}.attend-con .user-list .user-item .icon-box[data-v-865c3420]{width:4.26667vw;height:4.26667vw;border-radius:50%;display:flex;justify-content:center;align-items:center;margin-right:1.06667vw}.attend-con .user-list .user-item .icon-box i[data-v-865c3420]{color:#fff;transform:scale(.7)}.attend[data-v-865c3420]{position:fixed;bottom:0;left:0;right:0;height:10.66667vw;display:flex}.attend .attend-btn[data-v-865c3420]{width:50%;height:100%;display:flex;justify-content:center;align-items:center;font-size:var(--van-cell-font-size);color:#fff;background:#9a9a9a}.attend .attend-btn.attend-btn-sure[data-v-865c3420]{background:#3895ff}.form-bottom[data-v-865c3420]{margin-bottom:2.66667vw}.potential-safety-hazard-title[data-v-865c3420]{height:8vw;line-height:8vw;background-color:#81d3f9;padding:0 5.33333vw;box-sizing:border-box}.addBtn[data-v-865c3420]{margin-top:2.66667vw;width:100%}.btn-group[data-v-865c3420]{width:calc(100% - 5.33333vw);display:flex;justify-content:space-between;gap:0 4vw;position:absolute;bottom:2.66667vw;left:2.66667vw}.btn-group>button[data-v-865c3420]{flex:1}[data-v-865c3420] .van-checkbox-group,[data-v-865c3420] .van-radio-group{gap:2.66667vw 0}.checkbox-group[data-v-865c3420]{display:flex;flex-direction:row;flex-wrap:wrap}.checkbox-group .checkbox[data-v-865c3420]{width:calc(50% - 3.73333vw);margin-right:3.73333vw;margin-bottom:1.33333vw;align-items:start}\n",document.head.appendChild(t);const $={class:"task-item"},z={class:"body"},N={class:"item-info header"},x={class:"value"},E={class:"item-info"},F={class:"value"},O={class:"item-info"},R={class:"value"},A={class:"item-info"},L={class:"value"},U={class:"item-info"},V={class:"value"},q={class:"right"},M={key:0},j={key:0},P={key:0},H={key:2},B={key:1,class:"btn-group"};e("default",p({name:"SafetyCheckDetail",components:{FormItemSection:n,FormItemCalendar:C,ListItem:p({name:"ListItem",components:{},props:{item:{type:Object,default:()=>({})},navbarType:String},emits:["detailItem"],setup(e,{attrs:t,slots:a,emit:i}){},data:()=>({statusMap:c}),watch:{},created(){},computed:{statusTypeMap(){var e;return(null===(e=this.statusMap)||void 0===e?void 0:e.TYPE_MAP)||{}},statusLabelMap(){var e;return(null===(e=this.statusMap)||void 0===e?void 0:e.LABEL_MAP)||{}},toStartReviewStatus(){var e;return null===(e=this.statusMap)||void 0===e?void 0:e.PENDING_REVIEW}},methods:{hanelDel(){this.$emit("detailItem")}}},[["render",function(e,t,a,i,n,o){const s=u("van-tag"),r=u("van-button"),l=u("van-swipe-cell");return h(),v("div",$,[f(l,{disabled:!["add","update"].includes(a.navbarType)},{right:y((()=>[["add","update"].includes(a.navbarType)?(h(),g(r,{key:0,square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:a.item.rectificationStatus!==o.toStartReviewStatus,onClick:b(o.hanelDel,["stop","prevent"])},null,8,["disabled","onClick"])):D("",!0)])),default:y((()=>[w("div",z,[w("div",N,[t[0]||(t[0]=w("span",{class:"key"},"隐患编号",-1)),w("span",x,I(a.item.hazardNumber),1)]),w("div",E,[t[1]||(t[1]=w("span",{class:"key"},"所属标段",-1)),w("span",F,I(e.$formatLabel(a.item.sectionId,e.$DICT_CODE.project_section)),1)]),w("div",O,[t[2]||(t[2]=w("span",{class:"key"},"隐患级别",-1)),w("span",R,I(e.$formatLabel(a.item.level,e.$DICT_CODE.safe_hazard_level)),1)]),w("div",A,[t[3]||(t[3]=w("span",{class:"key"},"整改期限",-1)),w("span",L,I(e.$dayjs(a.item.deadline).format("YYYY-MM-DD")),1)]),w("div",U,[t[4]||(t[4]=w("span",{class:"key"},"整改内容",-1)),w("span",V,I(a.item.description),1)]),w("div",q,[f(s,{class:"tag",color:o.statusTypeMap[a.item.rectificationStatus],plain:"",size:"medium"},{default:y((()=>[_(I(o.statusLabelMap[a.item.rectificationStatus]),1)])),_:1},8,["color"])])])])),_:1},8,["disabled"])])}],["__scopeId","data-v-2c3d01cb"]]),FormItemCoord:o,FormItemDate:i,FormItemPicker:a},props:{},emits:[],setup(e,{attrs:t,slots:a,emit:i}){},data:()=>({loading:!1,error:"",formData:{content:"",evidence:"",hazards:[],id:null,inspectionArea:[],inspectionDate:"",inspectionNumber:"",inspectionResult:"",inspectionType:"",inspectionUnit:"",inspectionUnitName:"",inspector:"",inspectorFullname:"",reporter:"",reporterName:"",sectionId:""},SafeInspectionHazardList:[],departmentOptions:[],statusMap:c}),mounted(){var e;Object.keys(this.$store.SAFE_INSPECTION_FORM).length?this.formData=this.$store.SAFE_INSPECTION_FORM:"add"===this.navbarType?(this.formData.inspectionUnit=null===(e=this.portal)||void 0===e?void 0:e.id,this.formData.inspector=this.user.userName||"",this.formData.inspectorFullname=this.user.userFullname||"",this.formData.reporter=this.user.userName||"",this.formData.reporterName=this.user.userFullname||"",this.formData.sectionId=this.$getPortalId()):this.getFormData(),this.getData()},computed:{safeInspectionAreaList(){return this.$store.ENUM_DICT[m.safe_inspection_area]},safeInspectionResultList(){return this.$store.ENUM_DICT[m.safe_inspection_result]},safeInspectionTypeList(){return this.$store.ENUM_DICT[m.safe_inspection_type]},portalId(){var e;return 1===(null===(e=this.$store.PORTAL)||void 0===e?void 0:e.type)?this.$store.PORTAL.id:""},portal(){return this.$store.PORTAL},navbarTitle(){return this.$route.query.title||"安全检查详情"},navbarType(){return this.$route.query.type},user(){return this.$store.USER_INFO},inspectionList(){return this.departmentOptions.length?this.departmentOptions:this.$store.INSPECTION_LIST},inspectionUnitLabel(){const e=this.departmentOptions.find((e=>e.value===this.formData.inspectionUnit));return e?e.label:""},inspectionAreaStr(){return this.formData.inspectionArea&&this.formData.inspectionArea.length?this.formData.inspectionArea.map((e=>this.$formatLabel(e,$DICT_CODE.safe_inspection_area))).join("、"):""},toStartReviewStatus(){var e;return null===(e=this.statusMap)||void 0===e?void 0:e.PENDING_REVIEW}},methods:{chengeInspectionUnitt(e){},chengeSection(e){this.formData.hazards&&this.formData.hazards.length>0&&this.formData.hazards.forEach((e=>{e.sectionId=this.formData.sectionId,e.projectPosition=null}))},getFormData(){s({id:this.$route.query.id}).then((e=>{if(console.log("res==============>",e),e){const t=Array.isArray(e.hazards)?e.hazards:[];t.forEach((e=>{e.types=e.types||[],e.type&&e.types.push(e.type),e.typeChild&&e.types.push(e.typeChild)})),this.formData={...e,inspectionArea:e.inspectionArea?e.inspectionArea.split(","):[],hazards:t},console.log("formData======>",this.formData)}}))},getData(){r(this.portalId).then((e=>{if(e){const{departmentOptions:t}=function(e){const t=e,a=[];return e.forEach((e=>{e.children&&e.children.forEach((e=>{a.push({label:e.content?e.content.name:"",value:e.content?e.content.id:""})}))})),{departmentList:t,departmentOptions:a}}(e);this.departmentOptions=t,this.$store.INSPECTION_LIST=t}}))},handleClose(){this.$store.SAFE_INSPECTION_FORM={},this.$router.go(-1)},handleAddHiddenTrouble(){this.formData.sectionId?(this.$store.SAFE_INSPECTION_FORM=this.formData,this.$store.HIDDEN_TROUBLE_INFO={},this.$router.push({name:"HiddenTroubleEdit",query:{type:"add",title:"添加隐患",sectionId:this.formData.sectionId}})):this.$showToast({position:"top",message:"请先选择所属标段"})},hanelItem(e,t){["add","update"].includes(this.navbarType)&&e.rectificationStatus===this.toStartReviewStatus?(this.$store.SAFE_INSPECTION_FORM=this.formData,this.$store.HIDDEN_TROUBLE_INFO=JSON.parse(JSON.stringify({...e,index:t})),this.$router.push({name:"HiddenTroubleEdit",query:{type:"update",title:"编辑隐患"}})):this.$router.push({path:"FormCenter/SafeInspectionHazard",query:{type:"view",taskKey:e.taskKey,bizId:e.id,taskId:e.taskId}})},async handleAddOrCreate(){try{if(await this.$refs.formDateRef.validate(),"1"===this.formData.inspectionResult&&(!this.formData.hazards||0===this.formData.hazards.length))return this.$showToast({position:"top",message:"请至少添加一条隐患记录"}),void setTimeout((()=>{this.$closeToast()}),100);this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e={...this.formData,inspectionArea:this.formData.inspectionArea.join(","),inspectionDate:this.formData.inspectionDate||null,hazards:this.formData.hazards.map((e=>({...e,rectificationStatus:e.rectificationStatus||"1",acceptanceTime:null,acceptanceResult:null,hazards:"1"===this.formData.inspectionResult?e.hazards:[]})))};"add"===this.$route.query.type?await l(e).then((()=>{this.$router.push({path:"/SafetyCheck"})})).finally((()=>{this.$closeToast()})):"update"===this.$route.query.type&&await d(e).then((()=>{this.$router.push({path:"/SafetyCheck"})})).finally((()=>{this.$closeToast()}))}catch(e){console.log(e)}},async handleDelete(e){try{await this.$confirm({title:"提示",message:`确认删除${this.formData.hazards[e].hazardNumber}?`}),this.formData.hazards.splice(e,1)}catch(t){console.log(t)}}}},[["render",function(e,t,a,i,n,o){const s=u("Navbar"),r=u("van-empty"),l=u("van-field"),d=u("van-radio"),c=u("van-radio-group"),p=u("FormItemPicker"),m=u("FormItemSection"),C=u("FormItemDate"),$=u("van-cell-group"),z=u("van-form"),N=u("van-button"),x=u("ListItem");return h(),v(T,null,[f(s,{back:"",title:o.navbarTitle},null,8,["title"]),n.loading?(h(),v(T,{key:0},[],64)):n.error?(h(),g(r,{key:1,image:"error",description:n.error},null,8,["description"])):(h(),v("div",{key:2,class:k(["view-height",{"btn-bom":["add","update"].includes(o.navbarType)}])},[f(z,{ref:"formDateRef",readonly:"detail"===o.navbarType,"label-width":"7em","input-align":"right","error-message-align":"right",class:"form-bottom"},{default:y((()=>[f($,{border:!1},{default:y((()=>[f(l,{modelValue:n.formData.inspectionNumber,"onUpdate:modelValue":t[0]||(t[0]=e=>n.formData.inspectionNumber=e),name:"inspectionNumber",label:"检查编号",readonly:"",placeholder:"自动生成"},null,8,["modelValue"]),f(l,{name:"inspectionType",label:"检查类型",placeholder:"检查类型",labelWidth:"5rem",required:"",rules:[{required:!0,message:"请选择检查类型"}]},{input:y((()=>["detail"===o.navbarType?(h(),v("span",M,I(e.$formatLabel(n.formData.inspectionType,e.$DICT_CODE.safe_inspection_type)),1)):(h(),g(c,{key:1,modelValue:n.formData.inspectionType,"onUpdate:modelValue":t[1]||(t[1]=e=>n.formData.inspectionType=e),direction:"horizontal"},{default:y((()=>[(h(!0),v(T,null,S(o.safeInspectionTypeList,((e,t)=>(h(),g(d,{key:t,name:e.code},{default:y((()=>[_(I(e["zh-CN"]),1)])),_:2},1032,["name"])))),128))])),_:1},8,["modelValue"]))])),_:1}),f(p,{label:"检查单位",readonly:"detail"===o.navbarType,name:"inspectionUnit","input-align":"right",value:n.formData.inspectionUnit,"onUpdate:value":t[2]||(t[2]=e=>n.formData.inspectionUnit=e),text:n.formData.inspectionUnitName,"onUpdate:text":t[3]||(t[3]=e=>n.formData.inspectionUnitName=e),columns:[...o.inspectionList],"columns-field-names":{text:"label",value:"value",children:"none"},title:"检查单位",required:"",rules:[{required:!0,message:"请选择检查单位"}],onChange:o.chengeInspectionUnitt},null,8,["readonly","value","text","columns","onChange"]),f(m,{label:"所属标段",placeholder:"请选择",modelValue:n.formData.sectionId,"onUpdate:modelValue":t[4]||(t[4]=e=>n.formData.sectionId=e),readonly:"detail"===o.navbarType||n.formData.hazards.some((e=>e.rectificationStatus!==o.toStartReviewStatus)),required:"",rules:[{required:!0,message:"请选择所属标段"}],onSelect:o.chengeSection},null,8,["modelValue","readonly","onSelect"]),f(C,{label:"检查日期",value:n.formData.inspectionDate,"onUpdate:value":t[5]||(t[5]=e=>n.formData.inspectionDate=e),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择检查日期"}],readonly:"detail"===o.navbarType},null,8,["value","readonly"]),f(l,{label:"详细区域",name:"inspectionArea",modelValue:n.formData.inspectionArea,"onUpdate:modelValue":t[6]||(t[6]=e=>n.formData.inspectionArea=e),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"]),f(l,{name:"inspectionResult",label:"检查结果",placeholder:"检查结果",required:"",rules:[{required:!0,message:"请选择检查结果"}]},{input:y((()=>["detail"===o.navbarType?(h(),v("span",j,I(e.$formatLabel(n.formData.inspectionResult,e.$DICT_CODE.quality_inspection_result)),1)):(h(),g(c,{key:1,modelValue:n.formData.inspectionResult,"onUpdate:modelValue":t[7]||(t[7]=e=>n.formData.inspectionResult=e),direction:"horizontal",disabled:n.formData.hazards.some((e=>e.rectificationStatus!==o.toStartReviewStatus))},{default:y((()=>[(h(!0),v(T,null,S(o.safeInspectionResultList,((e,t)=>(h(),g(d,{key:t,name:e.code},{default:y((()=>[_(I(e["zh-CN"]),1)])),_:2},1032,["name"])))),128))])),_:1},8,["modelValue","disabled"]))])),_:1}),f(l,{label:"检查内容",name:"content",modelValue:n.formData.content,"onUpdate:modelValue":t[8]||(t[8]=e=>n.formData.content=e),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入检查内容"}],"input-align":"left"},null,8,["modelValue"]),f(l,{label:"检查依据",name:"evidence",modelValue:n.formData.evidence,"onUpdate:modelValue":t[9]||(t[9]=e=>n.formData.evidence=e),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"])])),_:1})])),_:1},8,["readonly"]),"1"===n.formData.inspectionResult?(h(),v("div",P,[t[12]||(t[12]=w("div",{class:"potential-safety-hazard-title"}," 安全隐患列表 ",-1)),["add","update"].includes(o.navbarType)?(h(),g(N,{key:0,type:"primary",size:"normal",class:"addBtn",onClick:t[10]||(t[10]=b((e=>o.handleAddHiddenTrouble()),["stop","prevent"]))},{default:y((()=>t[11]||(t[11]=[_("新增安全隐患")]))),_:1,__:[11]})):D("",!0),n.formData.hazards&&n.formData.hazards.length?(h(!0),v(T,{key:1},S(n.formData.hazards||[],((e,t)=>(h(),g(x,{key:t,item:e,style:{"margin-top":"10px"},onClick:b((a=>o.hanelItem(e,t)),["stop","prevent"]),navbarType:o.navbarType,onDetailItem:e=>o.handleDelete(t)},null,8,["item","onClick","navbarType","onDetailItem"])))),128)):(h(),v("div",H,[f(r,{description:"暂无数据",style:{padding:"0"}})]))])):D("",!0),["add","update"].includes(o.navbarType)?(h(),v("div",B,[f(N,{round:"",type:"danger",plain:"",onClick:b(o.handleClose,["stop","prevent"])},{default:y((()=>t[13]||(t[13]=[_("取消")]))),_:1,__:[13]},8,["onClick"]),f(N,{round:"",type:"primary",plain:"",onClick:b(o.handleAddOrCreate,["stop","prevent"])},{default:y((()=>t[14]||(t[14]=[_("保存")]))),_:1,__:[14]},8,["onClick"])])):D("",!0)],2))],64)}],["__scopeId","data-v-865c3420"]]))}}}));
