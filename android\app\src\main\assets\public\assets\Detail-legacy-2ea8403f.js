System.register(["./FormItemPicker-legacy-fd45c24d.js","./FormItemSection-legacy-66423754.js","./FormItemDate-legacy-c4422d42.js","./api-legacy-39a4c48a.js","./index-legacy-09188690.js","./wbsUtil-legacy-542349ac.js","./constants-legacy-a38f15af.js","./verder-legacy-e6127216.js","./FormItemCalendar-legacy-ea787ea1.js","./vant-legacy-b51a9379.js","./array-legacy-2920c097.js"],(function(t,e){"use strict";var a,i,n,o,s,r,l,d,c,p,u,m,f,h,v,y,g,b,D,w,I,T,k,_,$,S,C,L;return{setters:[t=>{a=t.F},t=>{i=t._},t=>{n=t.F},t=>{o=t.a,s=t.b,r=t.c,l=t.e},t=>{d=t._,c=t.D},t=>{p=t.a,u=t.g},t=>{m=t.R},t=>{f=t.Q,h=t.R,v=t.S,y=t.k,g=t.V,b=t.X,D=t.a2,w=t.Z,I=t.U,T=t.Y,k=t.B,_=t.F,$=t.y,S=t.W},t=>{C=t.F},t=>{L=t.a},null],execute:function(){var e=document.createElement("style");e.textContent=".task-item[data-v-d58fca95]{background-color:#fff;border-radius:1.33333vw;padding:2.66667vw;margin-bottom:2.66667vw}.task-item .body[data-v-d58fca95]{padding:2.66667vw 1.33333vw 1.33333vw;position:relative}.task-item .body .item-info[data-v-d58fca95]{display:flex;flex-direction:row;align-items:center;font-size:3.73333vw;color:#9a9a9a;line-height:1;padding:1.33333vw 0}.task-item .body .item-info>.key[data-v-d58fca95]{min-width:5em}.task-item .body .item-info>.value[data-v-d58fca95]{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#333}.task-item .body .right[data-v-d58fca95]{position:absolute;top:1.33333vw;right:1.06667vw}.task-item .body .right .tag[data-v-d58fca95]{padding:1.6vw 3.2vw;text-align:center}.task-item .body .detail[data-v-d58fca95]{position:absolute;bottom:1.33333vw;right:0}.van-button--disabled[data-v-d58fca95]{background-color:#1d2129;border:none}.view-height[data-v-fdc8353f]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat));padding:0 2.66667vw;overflow-x:hidden;overflow-y:scroll}.view-height.has-tab[data-v-fdc8353f]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height))}.view-height.btn-bom[data-v-fdc8353f]{padding:0 2.66667vw 13.33333vw!important}.attend-con[data-v-fdc8353f]{padding:var(--van-cell-vertical-padding) var(--van-cell-horizontal-padding);color:var(--van-cell-text-color);font-size:var(--van-cell-font-size)}.attend-con .user-list[data-v-fdc8353f]{margin-top:2.66667vw;display:flex;gap:2.66667vw;flex-wrap:wrap}.attend-con .user-list .user-item[data-v-fdc8353f]{display:flex;align-items:center}.attend-con .user-list .user-item .icon-box[data-v-fdc8353f]{width:4.26667vw;height:4.26667vw;border-radius:50%;display:flex;justify-content:center;align-items:center;margin-right:1.06667vw}.attend-con .user-list .user-item .icon-box i[data-v-fdc8353f]{color:#fff;transform:scale(.7)}.attend[data-v-fdc8353f]{position:fixed;bottom:0;left:0;right:0;height:10.66667vw;display:flex}.attend .attend-btn[data-v-fdc8353f]{width:50%;height:100%;display:flex;justify-content:center;align-items:center;font-size:var(--van-cell-font-size);color:#fff;background:#9a9a9a}.attend .attend-btn.attend-btn-sure[data-v-fdc8353f]{background:#3895ff}.form-bottom[data-v-fdc8353f]{margin-bottom:2.66667vw}.potential-safety-hazard-title[data-v-fdc8353f]{height:8vw;line-height:8vw;background-color:#81d3f9;padding:0 5.33333vw;box-sizing:border-box}.addBtn[data-v-fdc8353f]{margin-top:2.66667vw;width:100%}.btn-group[data-v-fdc8353f]{width:calc(100% - 5.33333vw);display:flex;justify-content:space-between;gap:0 4vw;position:absolute;bottom:2.66667vw;left:2.66667vw}.btn-group>button[data-v-fdc8353f]{flex:1}[data-v-fdc8353f] .van-checkbox-group,[data-v-fdc8353f] .van-radio-group{gap:2.66667vw 0}.checkbox-group[data-v-fdc8353f]{display:flex;flex-direction:row;flex-wrap:wrap}.checkbox-group .checkbox[data-v-fdc8353f]{width:calc(50% - 3.73333vw);margin-right:3.73333vw;margin-bottom:1.33333vw;align-items:start}\n",document.head.appendChild(e);const N={class:"task-item"},x={class:"body"},A={class:"item-info header"},R={class:"value"},O={class:"item-info"},U={class:"value"},E={class:"item-info"},q={class:"value"},F={class:"item-info"},j={class:"value"},P={class:"item-info"},V={class:"value"},z={class:"right"},M={key:0},Y={key:0},Q={key:0},W={key:2},B={key:1,class:"btn-group"};t("default",d({name:"SafetyCheckDetail",components:{FormItemSection:i,FormItemCalendar:C,ListItem:d({name:"RectifyAndReformListItem",components:{},props:{item:{type:Object,default:()=>({})},wbsList:{type:Object,default:()=>({})},navbarType:String},emits:["detailItem"],setup(t,{attrs:e,slots:a,emit:i}){},data:()=>({statusMap:m}),watch:{},created(){},computed:{statusTypeMap(){var t;return(null===(t=this.statusMap)||void 0===t?void 0:t.TYPE_MAP)||{}},statusLabelMap(){var t;return(null===(t=this.statusMap)||void 0===t?void 0:t.LABEL_MAP)||{}},toStartReviewStatus(){var t;return null===(t=this.statusMap)||void 0===t?void 0:t.PENDING_REVIEW}},mounted(){},methods:{getWbsLabel:p,hanelDel(){this.$emit("detailItem")}}},[["render",function(t,e,a,i,n,o){const s=f("van-tag"),r=f("van-button"),l=f("van-swipe-cell");return h(),v("div",N,[y(l,{disabled:!["add","update"].includes(a.navbarType)},{right:g((()=>[["add","update"].includes(a.navbarType)?(h(),b(r,{key:0,square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:a.item.rectificationStatus!==o.toStartReviewStatus,onClick:D(o.hanelDel,["stop","prevent"])},null,8,["disabled","onClick"])):w("",!0)])),default:g((()=>[I("div",x,[I("div",A,[e[0]||(e[0]=I("span",{class:"key"},"整改单号",-1)),I("span",R,T(a.item.correctionNumber),1)]),I("div",O,[e[1]||(e[1]=I("span",{class:"key"},"所属标段",-1)),I("span",U,T(t.$formatLabel(a.item.sectionId,t.$DICT_CODE.project_section)),1)]),I("div",E,[e[2]||(e[2]=I("span",{class:"key"},"整改部位",-1)),I("span",q,T(o.getWbsLabel(a.wbsList,a.item.projectPosition)),1)]),I("div",F,[e[3]||(e[3]=I("span",{class:"key"},"整改期限",-1)),I("span",j,T(t.$dayjs(a.item.inspectionDate).format("YYYY-MM-DD")),1)]),I("div",P,[e[4]||(e[4]=I("span",{class:"key"},"整改内容",-1)),I("span",V,T(a.item.description),1)]),I("div",z,[y(s,{class:"tag",color:o.statusTypeMap[a.item.rectificationStatus],plain:"",size:"medium"},{default:g((()=>[k(T(o.statusLabelMap[a.item.rectificationStatus]),1)])),_:1},8,["color"])])])])),_:1},8,["disabled"])])}],["__scopeId","data-v-d58fca95"]]),FormItemDate:n,FormItemPicker:a},props:{},emits:[],setup(t,{attrs:e,slots:a,emit:i}){},data:()=>({wbsList:[],loading:!1,error:"",formData:{content:"",evidence:"",corrections:[],id:null,inspectionArea:[],inspectionDate:"",inspectionNumber:"",inspectionResult:"",inspectionType:"",inspectionUnit:"",inspectionUnitName:"",inspector:"",inspectorFullname:"",reporter:"",reporterName:"",sectionId:""},SafeInspectionHazardList:[],departmentOptions:[],statusMap:m}),mounted(){if(this.getWbsList(),Object.keys(this.$store.QUALITY_INSPECTION).length)this.formData=this.$store.QUALITY_INSPECTION;else if("add"!==this.navbarType||Object.keys(this.$store.QUALITY_INSPECTION).length)["update","detail"].includes(this.navbarType)&&this.getFormData();else{var t;this.formData.inspectionUnit=null===(t=this.portal)||void 0===t?void 0:t.id,this.formData.inspector=this.user.userName||"",this.formData.inspectorFullname=this.user.userFullname||"",this.formData.reporter=this.user.userName||"",this.formData.reporterName=this.user.userFullname||"",this.formData.sectionId=this.$getPortalId()}this.getData(),console.log("safeInspectionAreaList",this.safeInspectionAreaList)},computed:{safeInspectionAreaList(){return this.$store.ENUM_DICT[c.safe_inspection_area]},qualityInspectionResult(){return this.$store.ENUM_DICT[c.quality_inspection_result]},safeInspectionTypeList(){return this.$store.ENUM_DICT[c.safe_inspection_type]},portalId(){var t;return 1===(null===(t=this.$store.PORTAL)||void 0===t?void 0:t.type)?this.$store.PORTAL.id:""},portal(){return this.$store.PORTAL},navbarTitle(){return this.$route.query.title||"安全检查详情"},navbarType(){return this.$route.query.type},user(){return this.$store.USER_INFO},inspectionList(){return this.departmentOptions.length?this.departmentOptions:this.$store.INSPECTION_LIST},inspectionUnitLabel(){const t=this.departmentOptions.find((t=>t.value===this.formData.inspectionUnit));return t?t.label:""},inspectionAreaStr(){return this.formData.inspectionArea&&this.formData.inspectionArea.length?this.formData.inspectionArea.map((t=>this.$formatLabel(t,$DICT_CODE.safe_inspection_area))).join("、"):""},toStartReviewStatus(){var t;return null===(t=this.statusMap)||void 0===t?void 0:t.PENDING_REVIEW}},methods:{async getWbsList(){this.wbsList=await u(null,!0,this.$store.PORTAL)},chengeInspectionUnitt(t){console.log("node",t,this.formData.inspectionUnit,this.portalId),this.formData.inspectionUnitName=t.label},chengeSection(t){this.formData.corrections&&this.formData.corrections.length>0&&this.formData.corrections.forEach((t=>{t.sectionId=this.formData.sectionId,t.projectPosition=null}))},getFormData(){o({id:this.$route.query.id}).then((t=>{if(console.log("res==============>",t),t){const e=Array.isArray(t.corrections)?t.corrections:[];this.formData={...t,inspectionArea:t.inspectionArea?t.inspectionArea.split(","):[],corrections:e}}}))},getData(){s(this.portalId).then((t=>{if(t){const{departmentOptions:e}=function(t){const e=t,a=[];return t.forEach((t=>{t.children&&t.children.forEach((t=>{a.push({label:t.content?t.content.name:"",value:t.content?t.content.id:""})}))})),{departmentList:e,departmentOptions:a}}(t);this.departmentOptions=e,this.$store.INSPECTION_LIST=e}}))},handleClose(){this.$store.QUALITY_INSPECTION={},this.$router.go(-1)},handleAddHiddenTrouble(){this.formData.sectionId?(this.$store.QUALITY_INSPECTION=this.formData,this.$store.rectify_AND_REFORM={},this.$router.push({name:"RectifyAndReform",query:{type:"add",title:"添加整改",sectionId:this.formData.sectionId}})):this.$showToast({position:"top",message:"请先选择所属标段"})},hanelItem(t,e){["add","update"].includes(this.navbarType)&&t.rectificationStatus===this.toStartReviewStatus?(this.$store.QUALITY_INSPECTION=this.formData,this.$store.rectify_AND_REFORM=JSON.parse(JSON.stringify({...t,index:e})),this.$router.push({name:"RectifyAndReform",query:{type:"update",title:"编辑整改"}})):this.$router.push({path:"FormCenter/QualityInspectionCorrection",query:{type:"view",taskKey:t.taskKey,bizId:t.id,taskId:t.taskId}})},async handleAddOrCreate(){try{if(await this.$refs.formDateRef.validate(),"1"===this.formData.inspectionResult&&(!this.formData.corrections||0===this.formData.corrections.length))return void L({message:"请至少添加一条整改记录",position:"top"});let t;if("1"===this.formData.inspectionResult&&this.formData.corrections&&this.formData.corrections.length>0)for(let a=0;a<this.formData.corrections.length;a++)if(!this.formData.corrections[a].projectPosition){t="第"+(a+1)+"条质量问题，请选择整改部位";break}if(t)return void L({message:t,position:"top"});this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e={...this.formData,inspectionArea:this.formData.inspectionArea.join(","),inspectionDate:this.formData.inspectionDate||null,corrections:this.formData.corrections.map((t=>({...t,rectificationStatus:t.rectificationStatus||"1",acceptanceTime:null,acceptanceResult:null,corrections:"1"===this.formData.inspectionResult?t.corrections:[]})))};"add"===this.$route.query.type?await r(e).then((()=>{this.$router.push({path:"/QualityInspection"})})).finally((()=>{this.$closeToast()})):"update"===this.$route.query.type&&await l(e).then((()=>{this.$router.push({path:"/QualityInspection"})})).finally((()=>{this.$closeToast()}))}catch(t){console.log(t)}},async handleDelete(t){try{await this.$confirm({title:"提示",message:`确认删除${this.formData.corrections[t].correctionNumber}?`}),this.formData.corrections.splice(t,1)}catch(e){console.log(e)}}}},[["render",function(t,e,a,i,n,o){const s=f("Navbar"),r=f("van-empty"),l=f("van-field"),d=f("van-radio"),c=f("van-radio-group"),p=f("FormItemPicker"),u=f("FormItemSection"),m=f("FormItemDate"),C=f("van-cell-group"),L=f("van-form"),N=f("van-button"),x=f("ListItem");return h(),v(_,null,[y(s,{back:"",title:o.navbarTitle},null,8,["title"]),n.loading?(h(),v(_,{key:0},[],64)):n.error?(h(),b(r,{key:1,image:"error",description:n.error},null,8,["description"])):(h(),v("div",{key:2,class:$(["view-height",{"btn-bom":["add","update"].includes(o.navbarType)}])},[y(L,{ref:"formDateRef",readonly:"detail"===o.navbarType,"label-width":"7em","input-align":"right","error-message-align":"right",class:"form-bottom"},{default:g((()=>[y(C,{border:!1},{default:g((()=>[y(l,{modelValue:n.formData.inspectionNumber,"onUpdate:modelValue":e[0]||(e[0]=t=>n.formData.inspectionNumber=t),name:"inspectionNumber",label:"检查编号",readonly:"",placeholder:"自动生成"},null,8,["modelValue"]),y(l,{name:"inspectionType",label:"检查类型",placeholder:"检查类型",labelWidth:"5rem",required:"",rules:[{required:!0,message:"请选择检查类型"}]},{input:g((()=>["detail"===o.navbarType?(h(),v("span",M,T(t.$formatLabel(n.formData.inspectionType,t.$DICT_CODE.safe_inspection_type)),1)):(h(),b(c,{key:1,modelValue:n.formData.inspectionType,"onUpdate:modelValue":e[1]||(e[1]=t=>n.formData.inspectionType=t),direction:"horizontal"},{default:g((()=>[(h(!0),v(_,null,S(o.safeInspectionTypeList,((t,e)=>(h(),b(d,{key:e,name:t.code},{default:g((()=>[k(T(t["zh-CN"]),1)])),_:2},1032,["name"])))),128))])),_:1},8,["modelValue"]))])),_:1}),y(p,{label:"检查单位",readonly:"detail"===o.navbarType,name:"inspectionUnit","input-align":"right",value:n.formData.inspectionUnit,"onUpdate:value":e[2]||(e[2]=t=>n.formData.inspectionUnit=t),columns:[...o.inspectionList],"columns-field-names":{text:"label",value:"value",children:"none"},title:"检查单位",required:"",rules:[{required:!0,message:"请选择检查单位"}],onChange:o.chengeInspectionUnitt},null,8,["readonly","value","columns","onChange"]),y(u,{label:"所属标段",placeholder:"请选择",modelValue:n.formData.sectionId,"onUpdate:modelValue":e[3]||(e[3]=t=>n.formData.sectionId=t),readonly:"detail"===o.navbarType||n.formData.corrections.some((t=>t.rectificationStatus!==o.toStartReviewStatus)),required:"",rules:[{required:!0,message:"请选择所属标段"}],onSelect:o.chengeSection},null,8,["modelValue","readonly","onSelect"]),y(l,{label:"详细区域",name:"inspectionArea",modelValue:n.formData.inspectionArea,"onUpdate:modelValue":e[4]||(e[4]=t=>n.formData.inspectionArea=t),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"]),y(m,{label:"检查日期",value:n.formData.inspectionDate,"onUpdate:value":e[5]||(e[5]=t=>n.formData.inspectionDate=t),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择检查日期"}],readonly:"detail"===o.navbarType},null,8,["value","readonly"]),y(l,{name:"inspectionResult",label:"检查结果",placeholder:"检查结果",required:"",rules:[{required:!0,message:"请选择检查结果"}]},{input:g((()=>["detail"===o.navbarType?(h(),v("span",Y,T(t.$formatLabel(n.formData.inspectionResult,t.$DICT_CODE.quality_inspection_result)),1)):(h(),b(c,{key:1,modelValue:n.formData.inspectionResult,"onUpdate:modelValue":e[6]||(e[6]=t=>n.formData.inspectionResult=t),direction:"horizontal",disabled:n.formData.corrections.some((t=>t.rectificationStatus!==o.toStartReviewStatus))},{default:g((()=>[(h(!0),v(_,null,S(o.qualityInspectionResult,((t,e)=>(h(),b(d,{key:e,name:t.code},{default:g((()=>[k(T(t["zh-CN"]),1)])),_:2},1032,["name"])))),128))])),_:1},8,["modelValue","disabled"]))])),_:1}),y(l,{label:"检查内容",name:"content",modelValue:n.formData.content,"onUpdate:modelValue":e[7]||(e[7]=t=>n.formData.content=t),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入检查内容"}],"input-align":"left"},null,8,["modelValue"]),y(l,{label:"检查依据",name:"evidence",modelValue:n.formData.evidence,"onUpdate:modelValue":e[8]||(e[8]=t=>n.formData.evidence=t),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"])])),_:1})])),_:1},8,["readonly"]),"1"===n.formData.inspectionResult?(h(),v("div",Q,[e[11]||(e[11]=I("div",{class:"potential-safety-hazard-title"}," 质量问题整改列表 ",-1)),["add","update"].includes(o.navbarType)?(h(),b(N,{key:0,type:"primary",size:"normal",class:"addBtn",onClick:e[9]||(e[9]=D((t=>o.handleAddHiddenTrouble()),["stop","prevent"]))},{default:g((()=>e[10]||(e[10]=[k("新增质量问题")]))),_:1,__:[10]})):w("",!0),n.formData.corrections&&n.formData.corrections.length?(h(!0),v(_,{key:1},S(n.formData.corrections||[],((t,e)=>(h(),b(x,{key:e,item:t,style:{"margin-top":"10px"},onClick:D((a=>o.hanelItem(t,e)),["stop","prevent"]),navbarType:o.navbarType,wbsList:n.wbsList,onDetailItem:t=>o.handleDelete(e)},null,8,["item","onClick","navbarType","wbsList","onDetailItem"])))),128)):(h(),v("div",W,[y(r,{description:"暂无数据",style:{padding:"0"}})]))])):w("",!0),["add","update"].includes(o.navbarType)?(h(),v("div",B,[y(N,{round:"",type:"danger",plain:"",onClick:D(o.handleClose,["stop","prevent"])},{default:g((()=>e[12]||(e[12]=[k("取消")]))),_:1,__:[12]},8,["onClick"]),y(N,{round:"",type:"primary",plain:"",onClick:D(o.handleAddOrCreate,["stop","prevent"])},{default:g((()=>e[13]||(e[13]=[k("保存")]))),_:1,__:[13]},8,["onClick"])])):w("",!0)],2))],64)}],["__scopeId","data-v-fdc8353f"]]))}}}));
