import{s as d}from"./array-15ef8611.js";import{_ as h}from"./index-4829f8e2.js";import{Q as r,R as p,S as f,k as u,V as g,F as C}from"./verder-361ae6c7.js";const x={name:"FormItemCascader",components:{},emits:["update:value","update:text","change"],props:{value:String,text:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:<PERSON><PERSON><PERSON>,readonly:Bo<PERSON>an,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},columns:{type:Array,default:()=>[]},columnsFieldNames:{type:Object,default:()=>({text:"text",value:"value",children:"children"})},dictName:String,triggerChangeMode:{type:<PERSON><PERSON><PERSON>,default:!1}},setup(l,{attrs:t,slots:e,emit:n}){},data(){return{showPicker:!1,lastValue:void 0,selectedText:void 0}},computed:{computedColumns(){return this.dictName?this.$store.ENUM_DICT[this.dictName]:this.columns},computedColumnsFieldNames(){return this.dictName?{text:"zh-CN",value:"code",children:"child"}:this.columnsFieldNames}},watch:{value:{immediate:!0,handler(l){if(l!=null&&l!=""){this.lastValue=l;const e=d(this.computedColumns,l,this.computedColumnsFieldNames.value,this.computedColumnsFieldNames.children).map(n=>n==null?void 0:n[this.computedColumnsFieldNames.text]);this.selectedText=e.join("/")}else this.lastValue="",this.selectedText="";this.$emit("update:text",this.selectedText)}}},created(){},mounted(){},methods:{onShowPicker(){this.readonly||(this.showPicker=!0)},onClosePicker(){this.showPicker=!1},onSelectConfirm({value:l,selectedOptions:t}){this.setSelectValue(l,t),this.onClosePicker()},onSelectChange({value:l,selectedOptions:t}){this.triggerChangeMode&&this.setSelectValue(l,t)},setSelectValue(l,t){const n=t.map(s=>s==null?void 0:s[this.computedColumnsFieldNames.text]).join("/");this.$emit("update:value",l),this.$emit("update:text",n),this.$emit("change",t),this.selectedText=n},chengeLabel(){if(this.value!=null&&this.value!=""){this.lastValue=this.value;const t=d(this.computedColumns,this.value,this.computedColumnsFieldNames.value,this.computedColumnsFieldNames.children).map(e=>e==null?void 0:e[this.computedColumnsFieldNames.text]);this.selectedText=t.join("/")}else this.lastValue="",this.selectedText="";this.$emit("update:text",this.selectedText)}}};function _(l,t,e,n,s,i){const o=r("van-field"),m=r("van-cascader"),c=r("van-popup");return p(),f(C,null,[u(o,{name:e.name,"model-value":s.selectedText,type:e.text,label:e.label,required:e.required,rules:e.rules,"input-align":e.inputAlign,"error-message-align":e.errorMessageAlign,"label-width":e.labelWidth,readonly:"","is-link":!e.readonly,placeholder:"请选择",onClick:t[0]||(t[0]=a=>i.onShowPicker())},null,8,["name","model-value","type","label","required","rules","input-align","error-message-align","label-width","is-link"]),u(c,{show:s.showPicker,"onUpdate:show":t[2]||(t[2]=a=>s.showPicker=a),position:"bottom",teleport:"#app"},{default:g(()=>[u(m,{ref:"picker",modelValue:s.lastValue,"onUpdate:modelValue":t[1]||(t[1]=a=>s.lastValue=a),title:e.title,options:[...i.computedColumns],fieldNames:i.computedColumnsFieldNames,onClose:i.onClosePicker,onFinish:i.onSelectConfirm,onChange:i.onSelectChange},null,8,["modelValue","title","options","fieldNames","onClose","onFinish","onChange"])]),_:1},8,["show"])],64)}const S=h(x,[["render",_],["__scopeId","data-v-ee1910e1"]]);export{S as F};
