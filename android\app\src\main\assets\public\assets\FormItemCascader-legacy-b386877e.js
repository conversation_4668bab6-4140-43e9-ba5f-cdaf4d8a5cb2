System.register(["./array-legacy-2920c097.js","./index-legacy-09188690.js","./verder-legacy-e6127216.js"],(function(e,t){"use strict";var l,i,s,a,n,o,d,u;return{setters:[e=>{l=e.s},e=>{i=e._},e=>{s=e.Q,a=e.R,n=e.S,o=e.k,d=e.V,u=e.F}],execute:function(){var t=document.createElement("style");t.textContent=".picker-content[data-v-ee1910e1]{height:70.4vw}.picker-content .picker-list[data-v-ee1910e1]{overflow-y:auto}.picker-content .picker-list .van-cell.van-cell--clickable[data-v-ee1910e1]:active{background-color:inherit}\n",document.head.appendChild(t);const r={name:"FormItemCascader",components:{},emits:["update:value","update:text","change"],props:{value:String,text:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:Boolean,readonly:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},columns:{type:Array,default:()=>[]},columnsFieldNames:{type:Object,default:()=>({text:"text",value:"value",children:"children"})},dictName:String,triggerChangeMode:{type:Boolean,default:!1}},setup(e,{attrs:t,slots:l,emit:i}){},data:()=>({showPicker:!1,lastValue:void 0,selectedText:void 0}),computed:{computedColumns(){return this.dictName?this.$store.ENUM_DICT[this.dictName]:this.columns},computedColumnsFieldNames(){return this.dictName?{text:"zh-CN",value:"code",children:"child"}:this.columnsFieldNames}},watch:{value:{immediate:!0,handler(e){if(null!=e&&""!=e){this.lastValue=e;const t=l(this.computedColumns,e,this.computedColumnsFieldNames.value,this.computedColumnsFieldNames.children).map((e=>null==e?void 0:e[this.computedColumnsFieldNames.text]));this.selectedText=t.join("/")}else this.lastValue="",this.selectedText="";this.$emit("update:text",this.selectedText)}}},created(){},mounted(){},methods:{onShowPicker(){this.readonly||(this.showPicker=!0)},onClosePicker(){this.showPicker=!1},onSelectConfirm({value:e,selectedOptions:t}){this.setSelectValue(e,t),this.onClosePicker()},onSelectChange({value:e,selectedOptions:t}){this.triggerChangeMode&&this.setSelectValue(e,t)},setSelectValue(e,t){const l=t.map((e=>null==e?void 0:e[this.computedColumnsFieldNames.text])).join("/");this.$emit("update:value",e),this.$emit("update:text",l),this.$emit("change",t),this.selectedText=l},chengeLabel(){if(null!=this.value&&""!=this.value){this.lastValue=this.value;const e=l(this.computedColumns,this.value,this.computedColumnsFieldNames.value,this.computedColumnsFieldNames.children).map((e=>null==e?void 0:e[this.computedColumnsFieldNames.text]));this.selectedText=e.join("/")}else this.lastValue="",this.selectedText="";this.$emit("update:text",this.selectedText)}}};e("F",i(r,[["render",function(e,t,l,i,r,c){const m=s("van-field"),h=s("van-cascader"),p=s("van-popup");return a(),n(u,null,[o(m,{name:l.name,"model-value":r.selectedText,type:l.text,label:l.label,required:l.required,rules:l.rules,"input-align":l.inputAlign,"error-message-align":l.errorMessageAlign,"label-width":l.labelWidth,readonly:"","is-link":!l.readonly,placeholder:"请选择",onClick:t[0]||(t[0]=e=>c.onShowPicker())},null,8,["name","model-value","type","label","required","rules","input-align","error-message-align","label-width","is-link"]),o(p,{show:r.showPicker,"onUpdate:show":t[2]||(t[2]=e=>r.showPicker=e),position:"bottom",teleport:"#app"},{default:d((()=>[o(h,{ref:"picker",modelValue:r.lastValue,"onUpdate:modelValue":t[1]||(t[1]=e=>r.lastValue=e),title:l.title,options:[...c.computedColumns],fieldNames:c.computedColumnsFieldNames,onClose:c.onClosePicker,onFinish:c.onSelectConfirm,onChange:c.onSelectChange},null,8,["modelValue","title","options","fieldNames","onClose","onFinish","onChange"])])),_:1},8,["show"])],64)}],["__scopeId","data-v-ee1910e1"]]))}}}));
