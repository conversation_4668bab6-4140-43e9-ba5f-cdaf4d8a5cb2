import{_ as c}from"./index-4829f8e2.js";import{Q as o,R as f,S as h,k as s,V as p,F as g}from"./verder-361ae6c7.js";const y={name:"FormItemDate",components:{},emits:["update:value","update:text"],props:{value:[String,Number],text:String,name:String,label:String,title:String,showTimeFormat:{type:String,default:""},submitTimeFormat:{type:[String,Object],default:""},inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:Boolean,readonly:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},columnsType:{type:Array,default:()=>["year","month","day"]},minDate:{type:Date,default:()=>{const i=new Date().getFullYear();return new Date(i-10,0,1)}},maxDate:{type:Date,default:()=>{const i=new Date().getFullYear();return new Date(i+10,0,1)}}},setup(i,{attrs:t,slots:e,emit:n}){},data(){return{showPicker:!1,currentDate:void 0}},computed:{displayValue(){if(!this.value)return this.value;if(this.value.includes(" ")){if(this.showTimeFormat){const[i,t]=this.value.split(" "),e=t.split(":");let n="";return this.showTimeFormat==="HH:mm:ss"?n=t:this.showTimeFormat==="HH:mm"?n=e[0]+":"+e[1]:this.showTimeFormat==="HH"&&(n=e[0]),n?i+" "+n:i}return this.value.split(" ")[0]}return this.value}},watch:{value:{immediate:!0,handler(i){if(this.currentDate==null){let t=new Date(i);t instanceof Date&&!isNaN(t.getTime())||(t=new Date);const e=this.columnsType.map(n=>n==="year"?t.getFullYear():n==="month"?t.getMonth()+1:n==="day"?t.getDate():void 0);this.currentDate=e||[]}}}},created(){},mounted(){},methods:{onShowPicker(){this.readonly||(this.showPicker=!0)},onClosePicker(){this.showPicker=!1},onSelectConfirm({selectedValues:i,selectedOptions:t}){let e=i.join("-");if(this.submitTimeFormat){if(typeof this.submitTimeFormat=="string")e=e+" "+this.submitTimeFormat;else if(typeof this.submitTimeFormat=="object"){const{hour:n="00",minute:a="00",second:r="00"}=this.submitTimeFormat;e=e+" "+[n,a,r].join(":")}}this.$emit("update:value",e),this.onClosePicker()}}};function w(i,t,e,n,a,r){const u=o("van-field"),m=o("van-date-picker"),d=o("van-popup");return f(),h(g,null,[s(u,{name:e.name,"model-value":r.displayValue,label:e.label,required:e.required,rules:e.rules,"input-align":e.inputAlign,"error-message-align":e.errorMessageAlign,"label-width":e.labelWidth,readonly:"","is-link":!e.readonly,placeholder:"请选择",onClick:t[0]||(t[0]=l=>r.onShowPicker())},null,8,["name","model-value","label","required","rules","input-align","error-message-align","label-width","is-link"]),s(d,{show:a.showPicker,"onUpdate:show":t[2]||(t[2]=l=>a.showPicker=l),position:"bottom",teleport:"#app"},{default:p(()=>[s(m,{ref:"picker",modelValue:a.currentDate,"onUpdate:modelValue":t[1]||(t[1]=l=>a.currentDate=l),title:e.title,"columns-type":e.columnsType,"min-date":e.minDate,"max-date":e.maxDate,onConfirm:r.onSelectConfirm,onCancel:r.onClosePicker},null,8,["modelValue","title","columns-type","min-date","max-date","onConfirm","onCancel"])]),_:1},8,["show"])],64)}const k=c(y,[["render",w]]);export{k as F};
