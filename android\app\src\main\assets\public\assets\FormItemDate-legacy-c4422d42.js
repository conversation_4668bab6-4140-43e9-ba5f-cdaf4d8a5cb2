System.register(["./index-legacy-09188690.js","./verder-legacy-e6127216.js"],(function(e,t){"use strict";var i,a,n,l,r,o,s;return{setters:[e=>{i=e._},e=>{a=e.Q,n=e.R,l=e.S,r=e.k,o=e.V,s=e.F}],execute:function(){const t={name:"FormItemDate",components:{},emits:["update:value","update:text"],props:{value:[String,Number],text:String,name:String,label:String,title:String,showTimeFormat:{type:String,default:""},submitTimeFormat:{type:[String,Object],default:""},inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:Boolean,readonly:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},columnsType:{type:Array,default:()=>["year","month","day"]},minDate:{type:Date,default:()=>{const e=(new Date).getFullYear();return new Date(e-10,0,1)}},maxDate:{type:Date,default:()=>{const e=(new Date).getFullYear();return new Date(e+10,0,1)}}},setup(e,{attrs:t,slots:i,emit:a}){},data:()=>({showPicker:!1,currentDate:void 0}),computed:{displayValue(){if(!this.value)return this.value;if(this.value.includes(" ")){if(this.showTimeFormat){const[e,t]=this.value.split(" "),i=t.split(":");let a="";return"HH:mm:ss"===this.showTimeFormat?a=t:"HH:mm"===this.showTimeFormat?a=i[0]+":"+i[1]:"HH"===this.showTimeFormat&&(a=i[0]),a?e+" "+a:e}return this.value.split(" ")[0]}return this.value}},watch:{value:{immediate:!0,handler(e){if(null==this.currentDate){let t=new Date(e);t instanceof Date&&!isNaN(t.getTime())||(t=new Date);const i=this.columnsType.map((e=>"year"===e?t.getFullYear():"month"===e?t.getMonth()+1:"day"===e?t.getDate():void 0));this.currentDate=i||[]}}}},created(){},mounted(){},methods:{onShowPicker(){this.readonly||(this.showPicker=!0)},onClosePicker(){this.showPicker=!1},onSelectConfirm({selectedValues:e,selectedOptions:t}){let i=e.join("-");if(this.submitTimeFormat)if("string"==typeof this.submitTimeFormat)i=i+" "+this.submitTimeFormat;else if("object"==typeof this.submitTimeFormat){const{hour:e="00",minute:t="00",second:a="00"}=this.submitTimeFormat;i=i+" "+[e,t,a].join(":")}this.$emit("update:value",i),this.onClosePicker()}}};e("F",i(t,[["render",function(e,t,i,u,m,d){const c=a("van-field"),h=a("van-date-picker"),p=a("van-popup");return n(),l(s,null,[r(c,{name:i.name,"model-value":d.displayValue,label:i.label,required:i.required,rules:i.rules,"input-align":i.inputAlign,"error-message-align":i.errorMessageAlign,"label-width":i.labelWidth,readonly:"","is-link":!i.readonly,placeholder:"请选择",onClick:t[0]||(t[0]=e=>d.onShowPicker())},null,8,["name","model-value","label","required","rules","input-align","error-message-align","label-width","is-link"]),r(p,{show:m.showPicker,"onUpdate:show":t[2]||(t[2]=e=>m.showPicker=e),position:"bottom",teleport:"#app"},{default:o((()=>[r(h,{ref:"picker",modelValue:m.currentDate,"onUpdate:modelValue":t[1]||(t[1]=e=>m.currentDate=e),title:i.title,"columns-type":i.columnsType,"min-date":i.minDate,"max-date":i.maxDate,onConfirm:d.onSelectConfirm,onCancel:d.onClosePicker},null,8,["modelValue","title","columns-type","min-date","max-date","onConfirm","onCancel"])])),_:1},8,["show"])],64)}]]))}}}));
