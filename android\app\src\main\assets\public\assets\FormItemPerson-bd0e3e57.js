import{_ as w,x as N}from"./index-4829f8e2.js";import{Q as l,R as u,S as c,k as r,V as p,U as d,Y as b,F as g,W as P,X as C,y as I,Z as F,a2 as V,B as x}from"./verder-361ae6c7.js";const L={name:"FormItemPerson",components:{},emits:["update:userId","update:userName","update:userFullname","update:deptId","update:deptName","update:deptCode","change"],props:{userId:[String,Number],userName:String,userFullname:String,deptId:[String,Number],deptName:String,deptCode:[String,Number],name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},required:Boolean,readonly:Bo<PERSON>an,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},placeholder:{type:String,default:"请选择"},multiple:Boolean},setup(s,{attrs:t,slots:a,emit:o}){},data(){return{showPicker:!1,loading:!1,finished:!1,list:[],searchParams:{pageNo:1,pageSize:200,isMobile:!0,searchValue:"",accountStatus:"1"},loaded:!1,user:{}}},computed:{portal(){return this.$store.PORTAL},selectedUser(){return this.user&&this.user.id?{...this.user}:{...this.list.find(t=>t.id==this.userId||t.userName===this.userName)}}},watch:{"searchParams.searchValue"(s){s===""&&this.onSearch(s)}},created(){},mounted(){},methods:{onSearch(s){this.finished=!1,this.searchParams.pageNo=1,this.$nextTick(()=>{this.onLoadList()})},async onLoadList(){try{this.loading=!0,this.finished=!1;const s={...this.searchParams};s.pageNo===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const t=await N(s),a=this.searchParams.pageNo<=1?[]:this.list||[];this.list=[...a,...t.list],this.list.length>=t.total?this.finished=!0:this.searchParams.pageNo++}catch(s){console.log(s),this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.loaded=!0,this.$closeToast()},100)}},onShowPicker(){this.readonly||(this.handleSelect({}),this.showPicker=!0,this.loaded||this.onSearch())},onClosePicker(){this.showPicker=!1},handleSelect(s){this.user=s},getDept(s=[]){return s.find(a=>{var o;return a.portalId==((o=this.portal)==null?void 0:o.id)})||s[0]},onSelectConfirm(){const{id:s="",userName:t="",userFullname:a="",orgList:o=[]}=this.selectedUser||{};this.$emit("update:userId",s),this.$emit("update:userName",t),this.$emit("update:userFullname",a);const e=this.getDept(o),n=(e==null?void 0:e.id)||"",h=(e==null?void 0:e.name)||"",m=(e==null?void 0:e.orgNo)||"";this.$emit("update:deptId",n),this.$emit("update:deptName",h),this.$emit("update:deptCode",m),this.$emit("change",this.selectedUser),this.onClosePicker()}}},U={class:"pop-window"},B={class:"pop-window-header"},T={class:"van-picker__title van-ellipsis"},A={class:"pop-window-body"},q={class:"list-content"},D={key:0,class:"p-[10px]"},M={class:"pop-window-footer van-safe-area-bottom"};function W(s,t,a,o,e,n){const h=l("van-field"),m=l("van-search"),_=l("van-cell"),v=l("van-empty"),S=l("van-list"),k=l("van-button"),y=l("van-popup");return u(),c(g,null,[r(h,{name:a.name,"model-value":a.userFullname,label:a.label,required:a.required,rules:a.rules,"input-align":a.inputAlign,"error-message-align":a.errorMessageAlign,"label-width":a.labelWidth,readonly:"","is-link":!a.readonly,placeholder:a.placeholder,onClick:t[0]||(t[0]=i=>n.onShowPicker())},null,8,["name","model-value","label","required","rules","input-align","error-message-align","label-width","is-link","placeholder"]),r(y,{show:e.showPicker,"onUpdate:show":t[4]||(t[4]=i=>e.showPicker=i),position:"bottom",closeable:"",teleport:"#app"},{default:p(()=>[d("div",U,[d("div",B,[d("div",T,b(a.title),1)]),d("div",A,[r(m,{modelValue:e.searchParams.searchValue,"onUpdate:modelValue":t[1]||(t[1]=i=>e.searchParams.searchValue=i),placeholder:"输入账号或姓名搜索","show-action":!1,onSearch:n.onSearch},null,8,["modelValue","onSearch"]),d("div",q,[r(S,{loading:e.loading,"onUpdate:loading":t[2]||(t[2]=i=>e.loading=i),finished:e.finished,"finished-text":e.list&&e.list.length?"没有更多了":"",onLoad:n.onLoadList,"immediate-check":!1},{default:p(()=>[e.list&&e.list.length?(u(!0),c(g,{key:0},P(e.list,i=>{var f;return u(),C(_,{class:I(["person-cell",{select:n.selectedUser.userName===i.userName}]),key:i.id,title:i.userFullname,value:(f=n.getDept(i.orgList))==null?void 0:f.name,onClick:z=>n.handleSelect(i)},null,8,["class","title","value","onClick"])}),128)):(u(),c(g,{key:1},[e.loading?F("",!0):(u(),c("div",D,[r(v,{description:"暂无更多数据"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])])]),d("div",M,[r(k,{class:"confirm-button",round:"",block:"",type:"primary",onClick:t[3]||(t[3]=V(i=>n.onSelectConfirm(),["stop","prevent"]))},{default:p(()=>t[5]||(t[5]=[x(" 确定 ")])),_:1,__:[5]})])])]),_:1},8,["show"])],64)}const O=w(L,[["render",W],["__scopeId","data-v-ef73e4ad"]]);export{O as F};
