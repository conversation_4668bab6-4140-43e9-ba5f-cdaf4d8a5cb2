System.register(["./verder-legacy-e6127216.js","./index-legacy-09188690.js"],(function(e,t){"use strict";var l,i,n,o,s,c,a,d,u,r,m,p;return{setters:[e=>{l=e.Q,i=e.R,n=e.S,o=e.k,s=e.V,c=e.X,a=e.U,d=e.a2,u=e.Y,r=e.F,m=e.W},e=>{p=e._}],execute:function(){var t=document.createElement("style");t.textContent=".picker-content[data-v-c66453ea]{height:70.4vw}.picker-content .picker-list[data-v-c66453ea]{overflow-y:auto}.picker-content .picker-list .van-cell.van-cell--clickable[data-v-c66453ea]:active{background-color:inherit}\n",document.head.appendChild(t);const h={name:"FormItemPicker",components:{},emits:["update:value","update:text","change"],props:{value:[String,Number,Array,<PERSON><PERSON>an],text:String,name:String,label:String,title:String,inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},placeholder:{type:String,default:"请选择"},required:Boolean,readonly:Boolean,multiple:Boolean,labelWidth:{type:String,default:void 0},rules:{type:Array,default:()=>[]},columns:{type:Array,default:()=>[]},columnsFieldNames:{type:Object,default:()=>({text:"text",value:"value",children:"children"})},dictName:String},setup(e,{attrs:t,slots:l,emit:i}){},data:()=>({showPicker:!1,selectedValues:void 0,selectedText:void 0,multipleChecked:[],checked:[],checkboxRefs:[],multipleSelectedText:[]}),computed:{computedColumns(){return this.dictName?this.$store.ENUM_DICT[this.dictName]:this.columns},computedColumnsFieldNames(){return this.dictName?{text:"zh-CN",value:"code",children:"none"}:this.columnsFieldNames}},watch:{value:{immediate:!0,handler(e){if(this.multiple){let t=[];null!=e&&""!=e&&(t=String(e).split(",")||[]),this.multipleChecked=t||[],this.checked=t;const l=[];t.forEach((e=>{const t=this.computedColumns.find((t=>t[this.computedColumnsFieldNames.value]==e));t&&l.push(null==t?void 0:t[this.computedColumnsFieldNames.text])})),this.multipleSelectedText=l,this.$emit("update:text",l.join(","))}else{this.selectedValues=null!=e&&""!==e?[e]:[];let t="";if(this.selectedValues&&this.selectedValues.length){const e=this.computedColumns.find((e=>e[this.computedColumnsFieldNames.value]==this.value));t=(null==e?void 0:e[this.computedColumnsFieldNames.text])||""}this.selectedText=t,this.$emit("update:text",t)}}}},created(){},mounted(){},methods:{onShowPicker(){this.readonly||(this.showPicker=!0)},onClosePicker(){this.showPicker=!1},onSelectConfirm({selectedValues:e,selectedOptions:t}){var l;const i=e[0],n=null===(l=t[0])||void 0===l?void 0:l[this.computedColumnsFieldNames.text];this.$emit("update:value",i),this.$emit("update:text",n),this.$emit("change",t[0]),this.selectedText=n,this.onClosePicker()},handleChecked(e,t){this.checkboxRefs[e].toggle()},onCloseMultiplePicker(){this.onClosePicker(),this.checked=this.multipleChecked||[]},onSelectMultipleConfirm(){const e=this.checked.map((e=>this.computedColumns.find((t=>t[this.computedColumnsFieldNames.value]==e)))).filter((e=>null!=e)),t=e.map((e=>e[this.computedColumnsFieldNames.text])),l=Array.isArray(this.value)?[...this.checked]:this.checked.join(",");this.$emit("update:value",l),this.$emit("update:text",t.join(",")),this.$emit("change",e||[]),this.multipleSelectedText=t,this.onClosePicker()}}},k={key:1,class:"van-picker"},v={class:"van-picker__toolbar"},C={class:"van-picker__title van-ellipsis"},f={class:"van-picker__columns picker-content"},g={class:"van-picker-column picker-list"};e("F",p(h,[["render",function(e,t,p,h,x,y){const b=l("van-field"),S=l("van-picker"),N=l("van-checkbox"),_=l("van-cell"),F=l("van-cell-group"),w=l("van-checkbox-group"),P=l("van-popup");return i(),n(r,null,[o(b,{name:p.name,"model-value":p.multiple?x.multipleSelectedText.join(","):x.selectedText,type:p.multiple?"textarea":"text",rows:p.multiple?1:void 0,autosize:p.multiple,label:p.label,required:p.required,rules:p.rules,"input-align":p.inputAlign,"error-message-align":p.errorMessageAlign,"label-width":p.labelWidth,readonly:"","is-link":!p.readonly,placeholder:p.placeholder,onClick:t[0]||(t[0]=e=>y.onShowPicker())},null,8,["name","model-value","type","rows","autosize","label","required","rules","input-align","error-message-align","label-width","is-link","placeholder"]),o(P,{show:x.showPicker,"onUpdate:show":t[5]||(t[5]=e=>x.showPicker=e),position:"bottom",teleport:"#app"},{default:s((()=>[p.multiple?(i(),n("div",k,[a("div",v,[a("button",{type:"button",class:"van-picker__cancel van-haptics-feedback",onClick:t[2]||(t[2]=d((e=>y.onCloseMultiplePicker()),["stop","prevent"]))}," 取消 "),a("div",C,u(p.title),1),a("button",{type:"button",class:"van-picker__confirm van-haptics-feedback",onClick:t[3]||(t[3]=d((e=>y.onSelectMultipleConfirm()),["stop","prevent"]))}," 确认 ")]),a("div",f,[a("div",g,[o(w,{modelValue:x.checked,"onUpdate:modelValue":t[4]||(t[4]=e=>x.checked=e)},{default:s((()=>[o(F,null,{default:s((()=>[(i(!0),n(r,null,m([...y.computedColumns],((e,t)=>(i(),c(_,{key:t,clickable:"",title:e[y.computedColumnsFieldNames.text],onClick:d((l=>y.handleChecked(t,e)),["stop","prevent"])},{"right-icon":s((()=>[o(N,{onClick:d((l=>y.handleChecked(t,e)),["stop","prevent"]),ref_for:!0,ref:e=>x.checkboxRefs[t]=e,name:e[y.computedColumnsFieldNames.value]},null,8,["onClick","name"])])),_:2},1032,["title","onClick"])))),128))])),_:1})])),_:1},8,["modelValue"])])])])):(i(),c(S,{key:0,ref:"picker",modelValue:x.selectedValues,"onUpdate:modelValue":t[1]||(t[1]=e=>x.selectedValues=e),title:p.title,columns:[...y.computedColumns],columnsFieldNames:y.computedColumnsFieldNames,onConfirm:y.onSelectConfirm,onCancel:y.onClosePicker},null,8,["modelValue","title","columns","columnsFieldNames","onConfirm","onCancel"]))])),_:1},8,["show"])],64)}],["__scopeId","data-v-c66453ea"]]))}}}));
