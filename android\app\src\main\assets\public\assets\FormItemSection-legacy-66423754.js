System.register(["./index-legacy-09188690.js","./verder-legacy-e6127216.js"],(function(e,l){"use strict";var t,a,u,n,o,r,i,d,v,s,m,y;return{setters:[e=>{t=e.u},e=>{a=e.c,u=e.r,n=e.f,o=e.w,r=e.Q,i=e.R,d=e.S,v=e.k,s=e.q,m=e.V,y=e.F}],execute:function(){e("_",{__name:"FormItemSection",props:{modelValue:{type:String,default:""},textKey:{type:String,default:"ext2"},valueKey:{type:String,default:"id"},filterMethod:{type:Function,default:null},readonly:{type:Boolean,default:!1},name:String,label:{type:String,default:"所属标段"},placeholder:{type:String,default:"请选择标段"},required:{type:Boolean,default:!1},rules:{type:Array,default:()=>[]},inputAlign:{type:String,default:void 0},errorMessageAlign:{type:String,default:void 0},labelWidth:{type:String,default:void 0},title:{type:String,default:"请选择标段"}},emits:["select","update:modelValue"],setup(e,{emit:l}){const f=e,p=l,c=a((()=>({name:f.name,label:f.label,placeholder:f.placeholder,required:f.required,rules:f.rules,inputAlign:f.inputAlign,errorMessageAlign:f.errorMessageAlign,labelWidth:f.labelWidth}))),g=t(),V=u(!1),h=u([""]),K=u(null),S=a((()=>{if(f.filterMethod)return f.filterMethod(g.PORTALS);const e=g.PORTAL;return e?"0"===e.parentId?g.PORTALS.filter((e=>"0"!==e.parentId)):g.PORTALS.filter((l=>l.id===e.id)):[]})),x=a((()=>{if(K.value)return K.value[f.textKey];if(f.modelValue){const e=A(f.modelValue);return e?e[f.textKey]:`${f.modelValue}`}return f.placeholder})),A=e=>e?S.value.find((l=>l[f.valueKey]===e||l[f.textKey]===e)):null;a((()=>!f.modelValue||""===f.modelValue.trim())),n((()=>{if(f.modelValue&&S.value.length>0){const e=A(f.modelValue);e&&(K.value=e,h.value=[e[f.valueKey]])}})),o((()=>f.modelValue),(e=>{if(e&&S.value.length>0){const l=A(e);l&&(K.value=l,h.value=[l[f.valueKey]])}}),{immediate:!0}),o(S,(e=>{if(e.length>0){if(K.value){const l=e.find((e=>e[f.valueKey]===K.value[f.valueKey]));if(l)return K.value=l,void(h.value=[l[f.valueKey]])}if(f.modelValue){const l=e.find((e=>e[f.textKey]===f.modelValue||e[f.valueKey]===f.modelValue));l&&(K.value=l,h.value=[l[f.valueKey]])}}else K.value=null,f.modelValue?h.value=[f.modelValue]:h.value=[""]}),{deep:!0});const b=({selectedOptions:e,selectedValues:l})=>{if(!e||0===e.length)return void(V.value=!1);const t=e[0];t?(K.value=t,p("update:modelValue",t[f.valueKey]),p("select",t),V.value=!1):V.value=!1};return(l,t)=>{const a=r("van-field"),u=r("van-picker"),n=r("van-popup");return i(),d(y,null,[v(a,s({"model-value":x.value},{...c.value,...l.$attrs},{type:"text",readonly:"","is-link":!e.readonly,onClick:t[0]||(t[0]=l=>!e.readonly&&(V.value=!0))}),null,16,["model-value","is-link"]),v(n,{show:V.value,"onUpdate:show":t[2]||(t[2]=e=>V.value=e),teleport:"#app","destroy-on-close":"",position:"bottom",round:!0},{default:m((()=>[v(u,{columns:S.value,"model-value":h.value,"columns-field-names":{text:e.textKey,value:e.valueKey},title:e.title,onConfirm:b,onCancel:t[1]||(t[1]=e=>V.value=!1)},null,8,["columns","model-value","columns-field-names","title"])])),_:1},8,["show"])],64)}}})}}}));
