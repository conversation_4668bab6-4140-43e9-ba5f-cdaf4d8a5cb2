System.register(["./FormItemDate-legacy-c4422d42.js","./FormItemSection-legacy-66423754.js","./FormItemCalendar-legacy-ea787ea1.js","./index-legacy-645a3645.js","./FormItemCoord-legacy-e6ddc9b7.js","./FormItemCascader-legacy-b386877e.js","./FormItemPicker-legacy-fd45c24d.js","./wbsUtil-legacy-542349ac.js","./FormItemPerson-legacy-e6e57748.js","./constants-legacy-82bb4fe6.js","./verder-legacy-e6127216.js","./index-legacy-09188690.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./validate-legacy-4e8f0db9.js","./array-legacy-2920c097.js"],(function(e,a){"use strict";var t,l,r,o,n,s,i,d,u,m,c,h,p,f,g,v,D,y,b,I,F;return{setters:[e=>{t=e.F},e=>{l=e._},e=>{r=e.F},e=>{o=e.U},e=>{n=e.F},e=>{s=e.F},e=>{i=e.F},e=>{d=e.g},e=>{u=e.F},e=>{m=e.R},e=>{c=e.Q,h=e.R,p=e.S,f=e.k,g=e.U,v=e.V,D=e.B,y=e.a2,b=e.Z,I=e.F},e=>{F=e._},null,null,null,null,null,null,null],execute:function(){var a=document.createElement("style");a.textContent=".view-height[data-v-a44a7d60]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat));padding:0 2.66667vw;overflow-x:hidden;overflow-y:scroll}.view-height.btn-bom[data-v-a44a7d60]{padding:0 2.66667vw 13.33333vw!important}.btn-group[data-v-a44a7d60]{width:calc(100% - 5.33333vw);display:flex;justify-content:space-between;gap:0 4vw;position:absolute;bottom:2.66667vw;left:2.66667vw}.btn-group>button[data-v-a44a7d60]{flex:1}\n",document.head.appendChild(a);const w={class:"view-height btn-bom"},_={key:0,class:"btn-group"};e("default",F({name:"HiddenTroubleInfo",components:{FormItemSection:l,FormItemDate:t,FormItemPerson:u,FormItemPicker:i,FormItemCascader:s,FormItemCoord:n,UploadFiles:o,FormItemCalendar:r},props:{title:{type:String,default:""}},emits:[],setup(e,{attrs:a,slots:t,emit:l}){},data(){var e;return{taskKey:(null===(e=this.$route.query)||void 0===e?void 0:e.taskKey)||"",formData:{},wbsList:[],statusMap:m}},computed:{navbarTitle(){return this.$route.query.title||"新增隐患"},navbarType(){return this.$route.query.type},portal(){return this.$store.PORTAL},user(){return this.$store.USER_INFO},toStartReviewStatus(){var e;return null===(e=this.statusMap)||void 0===e?void 0:e.PENDING_REVIEW}},mounted(){this.initData()},methods:{handleClose(){this.$store.HIDDEN_TROUBLE_INFO={},this.$router.back()},async handleAddOrCreate(){try{await this.$refs.form.validate(),this.formData.constructionPost=this.formData.lng&&this.formData.lat?[this.formData.lng,this.formData.lat].join(","):"",this.formData.hasOwnProperty("index")&&this.$store.SAFE_INSPECTION_FORM.hasOwnProperty("hazards")?this.$store.SAFE_INSPECTION_FORM.hazards[this.formData.index]=this.formData:!this.formData.hasOwnProperty("index")&&this.$store.SAFE_INSPECTION_FORM.hasOwnProperty("hazards")&&this.$store.SAFE_INSPECTION_FORM.hazards.push(this.formData),this.$store.HIDDEN_TROUBLE_INFO={},this.$router.back()}catch(e){console.log(e)}},async getWbsList(){this.wbsList=await d(this.formData.sectionId,!0,this.portal),this.$nextTick((()=>{this.$refs.formItemCascaderRef.chengeLabel()}))},handlePersonChange(){},async initData(){if(console.log("portal",this.portal),this.$store.HIDDEN_TROUBLE_INFO.hasOwnProperty("name")){if(this.formData.constructionPost){let e=this.formData.constructionPost.split(",");this.formData.lng=e[0],this.formData.lat=e[1]}this.formData=this.$store.HIDDEN_TROUBLE_INFO}else this.formData={sectionId:this.$route.query.sectionId,name:"",hazardNumber:"",projectPosition:"",constructionArea:"",source:"",level:"",deadline:"",dangerSource:"",description:"",requirement:"",fileUpload:"",type:"",typeChild:"",measure:"",measureDate:"",initiator:"",processName:"",processCode:"",measureFileUpload:"",isPass:"",needSupervision:"",positionInfo:null,lng:null,lat:null,height:null,rectificationStatus:this.toStartReviewStatus};this.getWbsList()}}},[["render",function(e,a,t,l,r,o){const n=c("Navbar"),s=c("van-field"),i=c("FormItemSection"),d=c("FormItemCascader"),u=c("FormItemPicker"),m=c("FormItemDate"),F=c("UploadFiles"),C=c("FormItemCoord"),N=c("FormItemPerson"),P=c("van-cell-group"),U=c("van-form"),O=c("van-button");return h(),p(I,null,[f(n,{back:"",title:o.navbarTitle},null,8,["title"]),g("div",w,[f(U,{ref:"form","label-width":"9em","input-align":"right","error-message-align":"right"},{default:v((()=>[f(P,{border:!1},{default:v((()=>[f(s,{modelValue:r.formData.hazardNumber,"onUpdate:modelValue":a[0]||(a[0]=e=>r.formData.hazardNumber=e),label:"隐患编号",placeholder:"自动生成",readonly:""},null,8,["modelValue"]),f(i,{label:"所属标段",placeholder:"请选择",modelValue:r.formData.sectionId,"onUpdate:modelValue":a[1]||(a[1]=e=>r.formData.sectionId=e),readonly:"",required:"",rules:[{required:!0,message:"请选择所属标段"}]},null,8,["modelValue"]),f(d,{ref:"formItemCascaderRef",label:"工程部位",value:r.formData.projectPosition,"onUpdate:value":a[2]||(a[2]=e=>r.formData.projectPosition=e),columns:[...r.wbsList],"columns-field-names":{text:"nodeName",value:"id",children:"children"},"trigger-change-mode":!0,placeholder:"请选择"},null,8,["value","columns"]),f(u,{label:"隐患级别",value:r.formData.level,"onUpdate:value":a[3]||(a[3]=e=>r.formData.level=e),"dict-name":e.$DICT_CODE.safe_hazard_level,placeholder:"请选择",required:"",rules:[{required:!0,message:"请选择隐患级别"}]},null,8,["value","dict-name"]),f(m,{label:"整改期限",value:r.formData.deadline,"onUpdate:value":a[4]||(a[4]=e=>r.formData.deadline=e),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择整改期限"}]},null,8,["value"]),f(s,{label:"详细区域",modelValue:r.formData.constructionArea,"onUpdate:modelValue":a[5]||(a[5]=e=>r.formData.constructionArea=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"]),f(s,{label:"隐患名称",modelValue:r.formData.name,"onUpdate:modelValue":a[6]||(a[6]=e=>r.formData.name=e),rows:"1",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入隐患名称"}],"input-align":"left"},null,8,["modelValue"]),f(s,{label:"整改内容",modelValue:r.formData.description,"onUpdate:modelValue":a[7]||(a[7]=e=>r.formData.description=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改内容"}],"input-align":"left"},null,8,["modelValue"]),f(s,{label:"整改要求及处理意见",modelValue:r.formData.requirement,"onUpdate:modelValue":a[8]||(a[8]=e=>r.formData.requirement=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改要求及处理意见"}],"input-align":"left"},null,8,["modelValue"]),f(s,{label:"资料附件上传","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传资料附件"}]},{input:v((()=>[f(F,{ref:"fileUpload",g9s:r.formData.fileUpload,"onUpdate:g9s":a[9]||(a[9]=e=>r.formData.fileUpload=e),accept:"*",multiple:!0},null,8,["g9s"])])),_:1}),f(C,{label:"经纬度",longitude:r.formData.lng,"onUpdate:longitude":a[10]||(a[10]=e=>r.formData.lng=e),latitude:r.formData.lat,"onUpdate:latitude":a[11]||(a[11]=e=>r.formData.lat=e),title:"选择定位"},null,8,["longitude","latitude"]),f(N,{required:!0,label:"隐患分发人",title:"隐患分发人",userFullname:r.formData.acceptancePersonName,"onUpdate:userFullname":a[12]||(a[12]=e=>r.formData.acceptancePersonName=e),userName:r.formData.acceptancePerson,"onUpdate:userName":a[13]||(a[13]=e=>r.formData.acceptancePerson=e),onChange:o.handlePersonChange,rules:[{required:!0,message:"请选择隐患分发人"}]},null,8,["userFullname","userName","onChange"])])),_:1})])),_:1},512),["add","update"].includes(o.navbarType)?(h(),p("div",_,[f(O,{round:"",type:"danger",plain:"",onClick:y(o.handleClose,["stop","prevent"])},{default:v((()=>a[14]||(a[14]=[D("取消")]))),_:1,__:[14]},8,["onClick"]),f(O,{round:"",type:"primary",plain:"",onClick:y(o.handleAddOrCreate,["stop","prevent"])},{default:v((()=>a[15]||(a[15]=[D("保存")]))),_:1,__:[15]},8,["onClick"])])):b("",!0)])],64)}],["__scopeId","data-v-a44a7d60"]]))}}}));
