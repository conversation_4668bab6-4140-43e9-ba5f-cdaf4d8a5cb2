import{m as V}from"./api-5f0f0fe0.js";import{_ as f,p as k}from"./index-4829f8e2.js";import{R as r,S as c,U as m,Y as p,y as x,a2 as C,Q as o,k as l,V as _,F as d,W as I,Z as S,X as w}from"./verder-361ae6c7.js";import"./vant-91101745.js";const b={name:"ListItem",components:{},props:{item:{type:Object,default:()=>({})}},emits:[],setup(e,{attrs:s,slots:a,emit:h}){return{filterHtmlTagSpecil:k}},data(){return{}},computed:{},watch:{},created(){},mounted(){},methods:{goDetail(){var e;this.$router.push({name:"MessageDetail",query:{id:(e=this.item)==null?void 0:e.receiveLogId}})}}},D={class:"title"},T={class:"aside"};function R(e,s,a,h,t,n){return r(),c("div",{class:x(["list-item",{new:a.item.status=="-1"}]),onClick:s[0]||(s[0]=C(u=>n.goDetail(),["stop","prevent"]))},[m("div",D,p(h.filterHtmlTagSpecil(a.item.msgBody)),1),m("div",T,p(e.$dayjs(a.item.createDate).format("YYYY-MM-DD")),1)],2)}const B=f(b,[["render",R],["__scopeId","data-v-4943696b"]]);const M={name:"MessageList",components:{ListItem:B},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(e,{attrs:s,slots:a,emit:h}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{currentPage:1,pageSize:20,searchValue:"",status:""}}},computed:{portalId(){return this.$store.PORTAL_ID}},watch:{"searchParams.searchValue"(e){e===""&&this.onSearch(e)}},created(){},mounted(){this.onLoadList()},methods:{onSearch(e){this.$nextTick(()=>{this.onRefresh()})},onCancel(){},onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.currentPage=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const e={...this.searchParams,...this.search};e.currentPage===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const s=await V(e),a=this.searchParams.currentPage<=1?[]:this.list||[];this.list=[...a,...s.list],this.searchParams.currentPage++,this.list.length>=s.total&&(this.finished=!0)}catch(e){console.log(e),this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}}}},N={class:"view-height"},Y={key:0,class:"p-[10px]"};function U(e,s,a,h,t,n){const u=o("Navbar"),g=o("van-search"),v=o("ListItem"),y=o("van-empty"),L=o("van-list"),P=o("van-pull-refresh");return r(),c(d,null,[l(u,{back:""}),l(g,{modelValue:t.searchParams.searchValue,"onUpdate:modelValue":s[0]||(s[0]=i=>t.searchParams.searchValue=i),placeholder:"搜索内容","show-action":!1,onSearch:n.onSearch,onCancel:n.onCancel},null,8,["modelValue","onSearch","onCancel"]),m("div",N,[l(P,{modelValue:t.refreshing,"onUpdate:modelValue":s[2]||(s[2]=i=>t.refreshing=i),onRefresh:n.onRefresh},{default:_(()=>[l(L,{loading:t.loading,"onUpdate:loading":s[1]||(s[1]=i=>t.loading=i),finished:t.finished,"finished-text":t.list&&t.list.length?"没有更多了":"",onLoad:n.onLoadList,"immediate-check":!1},{default:_(()=>[t.list&&t.list.length?(r(!0),c(d,{key:0},I([...t.list],(i,j)=>(r(),w(v,{key:i.id,item:i},null,8,["item"]))),128)):(r(),c(d,{key:1},[t.loading?S("",!0):(r(),c("div",Y,[l(y,{description:"暂无更多消息"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])])],64)}const q=f(M,[["render",U],["__scopeId","data-v-e444d598"]]);export{q as default};
