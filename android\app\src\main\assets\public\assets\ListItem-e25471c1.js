import{d as _}from"./api-58f40a5a.js";import{Q as o,R as p,S as f,k as l,V as r,U as t,Y as n,B as h,a2 as v}from"./verder-361ae6c7.js";import{_ as y}from"./index-4829f8e2.js";const k={name:"ListItem",components:{},props:{item:{type:Object,default:()=>({})}},emits:[],setup(i,{attrs:e,slots:s,emit:c}){},data(){return{}},watch:{},created(){},computed:{taskStateText(){switch(String(this.item.inspectionResult)){case"0":return"正常";case"1":return"有问题";default:return"有问题"}},tagColor(){var e;switch(String((e=this.item)==null?void 0:e.inspectionResult)){case"0":return"#07c160";case"1":return"#ff4d4f";default:return"#ff4d4f"}},user(){return this.$store.USER_INFO}},mounted(){},methods:{handelDel(){this.$confirm({title:"提示",message:"确认删除".concat(this.item.inspectionNumber,"?")}).then(()=>{_(this.item).then(()=>{this.$emit("delSuccess")})}).catch(()=>{})},toFormCenter(){this.$store.QUALITY_INSPECTION={},this.$router.push({path:"/QualityInspectionDetail",query:{id:this.item.id,type:this.user.userName===this.item.createBy?"update":"detail",title:this.user.userName===this.item.createBy?"编辑质量检查":"质量检查详情"}})}}},g={class:"body"},C={class:"item-info"},I={class:"value"},D={class:"item-info"},N={class:"value"},b={class:"item-info"},S={class:"value"},w={class:"item-info"},T={class:"value"},B={class:"item-info"},L={class:"value"},Y={class:"item-info"},E={class:"value"},O={class:"right"};function x(i,e,s,c,Q,a){const d=o("van-tag"),m=o("van-button"),u=o("van-swipe-cell");return p(),f("div",{class:"task-item",onClick:e[0]||(e[0]=v(R=>a.toFormCenter(),["stop","prevent"]))},[l(u,null,{right:r(()=>[l(m,{square:"",text:"删除",type:"danger",style:{height:"100%"},disabled:a.user.userName!==s.item.createBy||!s.item.showDel,onClick:a.handelDel},null,8,["disabled","onClick"])]),default:r(()=>[t("div",g,[t("div",C,[e[1]||(e[1]=t("span",{class:"key"},"检查编号",-1)),t("span",I,n(s.item.inspectionNumber),1)]),t("div",D,[e[2]||(e[2]=t("span",{class:"key"},"检查类型",-1)),t("span",N,n(i.$formatLabel(s.item.inspectionType,i.$DICT_CODE.safe_inspection_type)),1)]),t("div",b,[e[3]||(e[3]=t("span",{class:"key"},"检查单位",-1)),t("span",S,n(s.item.inspectionUnitName),1)]),t("div",w,[e[4]||(e[4]=t("span",{class:"key"},"所属标段",-1)),t("span",T,n(i.$formatLabel(s.item.sectionId,i.$DICT_CODE.project_section)),1)]),t("div",B,[e[5]||(e[5]=t("span",{class:"key"},"详细区域",-1)),t("span",L,n(s.item.inspectionArea),1)]),t("div",Y,[e[6]||(e[6]=t("span",{class:"key"},"检查日期",-1)),t("span",E,n(i.$dayjs(s.item.inspectionDate).format("YYYY-MM-DD")),1)]),t("div",O,[l(d,{class:"tag",color:a.tagColor,plain:"",size:"medium"},{default:r(()=>[h(n(a.taskStateText),1)]),_:1},8,["color"])])])]),_:1})])}const F=y(k,[["render",x],["__scopeId","data-v-f11ec8ee"]]);export{F as L};
