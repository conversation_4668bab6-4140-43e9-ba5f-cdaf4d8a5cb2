import{_ as x,a as F,u as E,e as V,l as M,d as U,g as R}from"./index-4829f8e2.js";import{h as j,u as w,r as C,R as c,S as _,U as d,Y as v,Q as y,F as B,W as q,y as O,X as D,V as m,k as r,B as I,Z as N}from"./verder-361ae6c7.js";import{s as T}from"./vant-91101745.js";const W="/app/assets/logo-3b0c2377.svg";function z(t){return typeof t=="function"?t():w(t)}typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const P=()=>{};function G(t,e){function s(...n){return new Promise((o,l)=>{Promise.resolve(t(()=>e.apply(this,n),{fn:e,thisArg:this,args:n})).then(o).catch(l)})}return s}function H(...t){let e=0,s,n=!0,o=P,l,a,u,p,f;!j(t[0])&&typeof t[0]=="object"?{delay:a,trailing:u=!0,leading:p=!0,rejectOnCancel:f=!1}=t[0]:[a,u=!0,p=!0,f=!1]=t;const i=()=>{s&&(clearTimeout(s),s=void 0,o(),o=P)};return S=>{const g=z(a),k=Date.now()-e,b=()=>l=S();return i(),g<=0?(e=Date.now(),b()):(k>g&&(p||!n)?(e=Date.now(),b()):u&&(l=new Promise((L,A)=>{o=f?A:L,s=setTimeout(()=>{e=Date.now(),n=!0,L(b()),i()},Math.max(0,g-k))})),!p&&!s&&(s=setTimeout(()=>n=!0,g)),n=!1,l)}}function J(t,e=200,s=!1,n=!0,o=!1){return G(H(e,s,n,o),t)}const K={class:"flex flex-col items-center justify-center"},Q={class:"font-600 font-[20px] text-[#1D2129]"},X={__name:"LoginLogo",setup(t){const e=F(),s=C(0),n=C(!1),o=J(()=>{s.value++,s.value>=5&&(s.value=0,n.value=!0)},1e3);return(l,a)=>(c(),_("div",K,[d("img",{class:"logo mb-[18px] w-[48px] h-[48px]",onClick:a[0]||(a[0]=(...u)=>w(o)&&w(o)(...u)),src:W,alt:"Logo"}),d("div",Q,"欢迎登录 ~ "+v(w(e).TITLE),1)]))}},Y=x(X,[["__scopeId","data-v-340eaf45"]]);const Z={name:"PassForm",components:{},props:{},emits:[],setup(t,{attrs:e,slots:s,emit:n}){return{store:E()}},data(){return{captcha_key:"",countDown:-2,smsLoading:!1,defaultIndex:0,form:{username:"",password:"",remember:!1,phone:"",captcha_code:""},loading:!1,showPassword:!1,loginList:["帐号登录","短信验证"]}},computed:{},watch:{},created(){},mounted(){this.loadAccount()},methods:{async onSubmit(t){try{this.loading=!0;let e;this.defaultIndex==0?e={scope:"all",grant_type:"password",username:t.username,password:V(t.password)}:e={scope:"all",grant_type:"sms_captcha",username:this.form.phone,captcha_code:this.form.captcha_code,captcha_key:this.captcha_key};const s=await M(e);this.store.$patch({TOKEN:s.access_token,USER_NAME:s.userName}),await this.store.INIT_DATA(),this.saveAccount(),window.ENV_FEISHU?this.$router.replace({name:"Tasks"}):this.$router.replace({name:"Home"})}catch(e){console.log(e)}finally{this.loading=!1}},saveAccount(){this.form.remember?localStorage.setItem("remember_info",JSON.stringify({...this.form,password:V(this.form.password)})):localStorage.removeItem("remember_info")},loadAccount(){try{const t=JSON.parse(localStorage.getItem("remember_info")),{username:e,password:s,remember:n}=t||{};n&&(this.form.remember=!0,this.form.username=e,this.form.password=U(s))}catch(t){console.log(t)}},changeVissible(){this.showPassword=!this.showPassword},cutLogin(t){this.defaultIndex=t},async getSMSCaptcha(){if(this.form.phone===""){T("请输入手机号码!");return}this.smsLoading=!0;try{const t=await R({phone:this.form.phone});t&&(this.captcha_key=t,this.updataCountDown())}catch(t){T("手机号错误或不存在!"),this.smsLoading=!1}},updataCountDown(){this.countDown=60;const t=setInterval(()=>{this.countDown==0?(clearInterval(t),this.smsLoading=!1,this.countDown=-1):this.countDown--},1e3)}}},$={class:"w-[335px] rounded-[5px] px-[10px] py-[20px] pt-[40px] login-form-container"},ee={class:"flex justify-around mt-[10px] h-[25px] text-[12px]"},oe=["loading","onClick"],te={class:"button-container"},se={key:0,class:"word"},ne={key:1,class:"word"},ae={class:"button-container",style:{"margin-top":"20pt"}};function re(t,e,s,n,o,l){const a=y("van-field"),u=y("van-cell-group"),p=y("van-button"),f=y("van-form");return c(),_("div",$,[d("div",ee,[(c(!0),_(B,null,q(o.loginList,(i,h)=>(c(),_("div",{key:h,class:O(o.defaultIndex===h?"Color":"isColor"),loading:o.loading,"native-type":"submit",onClick:S=>l.cutLogin(h)},v(i),11,oe))),128))]),o.defaultIndex===0?(c(),D(f,{key:0,onSubmit:l.onSubmit,"label-align":"top"},{default:m(()=>[r(u,{inset:""},{default:m(()=>[r(a,{modelValue:o.form.username,"onUpdate:modelValue":e[0]||(e[0]=i=>o.form.username=i),name:"username",label:"用户名",placeholder:"请输入您的用户名",rules:[{required:!0,message:"请输入用户名"}]},null,8,["modelValue"]),r(a,{modelValue:o.form.password,"onUpdate:modelValue":e[1]||(e[1]=i=>o.form.password=i),type:o.showPassword?"text":"password",name:"password",label:"密码",placeholder:"请输入您的密码","right-icon":o.showPassword?"eye-o":"closed-eye",onClickRightIcon:l.changeVissible,rules:[{required:!0,message:"请输入密码"}]},null,8,["modelValue","type","right-icon","onClickRightIcon"]),r(a,{style:{height:"0",padding:"0 !important"}})]),_:1}),d("div",te,[r(p,{block:"",type:"primary",loading:o.loading,"native-type":"submit"},{default:m(()=>e[4]||(e[4]=[I(" 登 录 ")])),_:1,__:[4]},8,["loading"])])]),_:1},8,["onSubmit"])):(c(),D(f,{key:1,onSubmit:l.onSubmit,"label-align":"top"},{default:m(()=>[r(u,{inset:""},{default:m(()=>[r(a,{modelValue:o.form.phone,"onUpdate:modelValue":e[2]||(e[2]=i=>o.form.phone=i),label:"手机号",placeholder:"请输入您的手机号",rules:[{required:!0,message:"请输入您的手机号"}]},null,8,["modelValue"]),r(a,{modelValue:o.form.captcha_code,"onUpdate:modelValue":e[3]||(e[3]=i=>o.form.captcha_code=i),label:"验证码",placeholder:"请输入您的验证码",rules:[{required:!0,message:"请输入您的验证码"}]},{button:m(()=>[r(p,{size:"mini",disabled:o.smsLoading,type:"primary",onClick:l.getSMSCaptcha},{default:m(()=>[o.countDown!=-1&&o.countDown!=-2?(c(),_("span",se,v(o.countDown+"s"),1)):N("",!0),o.countDown!=-1||o.countDown!=-2?(c(),_("span",ne,v(o.countDown==-2?"获取验证码":"重新获取"),1)):N("",!0)]),_:1},8,["disabled","onClick"])]),_:1},8,["modelValue"]),r(a,{readonly:""})]),_:1}),d("div",ae,[r(p,{block:"",type:"primary",loading:o.loading,"native-type":"submit"},{default:m(()=>e[5]||(e[5]=[I(" 登 录 ")])),_:1,__:[5]},8,["loading"])])]),_:1},8,["onSubmit"])),e[6]||(e[6]=d("div",{class:"policy"},[d("a",{href:"/app/privacy-policy.html"},"《姚家平隐私协议》")],-1))])}const le=x(Z,[["render",re],["__scopeId","data-v-806cbc49"]]);const ie={class:"page min-h-screen"},ce={class:"login-container"},ue={__name:"Login",setup(t){return(e,s)=>(c(),_("div",ie,[d("div",ce,[r(Y),r(le)])]))}},_e=x(ue,[["__scopeId","data-v-2b2a843d"]]);export{_e as default};
