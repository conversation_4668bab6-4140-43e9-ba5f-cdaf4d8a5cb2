System.register(["./api-legacy-a0a23a0f.js","./file-legacy-a550097a.js","./verder-legacy-e6127216.js","./index-legacy-09188690.js","./vant-legacy-b51a9379.js"],(function(t,e){"use strict";var i,o,a,n,s,l,c,r,d,m,f,h,g;return{setters:[t=>{i=t.d,o=t.e},t=>{a=t.g,n=t.d},t=>{s=t.Q,l=t.R,c=t.S,r=t.U,d=t.a2,m=t.k,f=t.Y},t=>{h=t._},t=>{g=t.a}],execute:function(){var e=document.createElement("style");e.textContent=".monitor-item[data-v-8a10495f]{position:relative;height:24vw}.monitor-item .name-bar[data-v-8a10495f]{position:absolute;bottom:0;left:0;right:0;width:100%;height:6.4vw;line-height:6.4vw;padding:0 2.66667vw;box-sizing:border-box;background:rgba(0,0,0,.6);color:#fff;font-size:3.2vw;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;z-index:9}.monitor-item .fav-tag[data-v-8a10495f]{position:absolute;top:0;right:0;padding:1.33333vw;width:5.86667vw;height:5.86667vw;font-size:4.26667vw;display:flex;flex-direction:row;align-items:center;justify-content:center;z-index:9}.monitor-item .content[data-v-8a10495f]{width:100%;height:100%}.monitor-item .offline[data-v-8a10495f],.monitor-item .online[data-v-8a10495f]{width:100%;height:100%;background-color:#ebf2f6;color:#347caf;font-size:3.73333vw;display:flex;flex-direction:column;align-items:center}.monitor-item .offline[data-v-8a10495f]{padding-top:20%}.monitor-item .online[data-v-8a10495f]{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;width:100%;height:100%;background:rgba(0,0,0,.9)}.monitor-item .online .cover[data-v-8a10495f]{position:absolute;width:100%;height:100%;top:0;left:0;background-size:100%;background-repeat:no-repeat}.monitor-item .online .icon[data-v-8a10495f]{position:absolute;top:50%;left:50%;transform:translate(-50%,-60%);font-size:8vw;color:#fff}\n",document.head.appendChild(e);const v={class:"monitor-item"},u={class:"content"},p={key:0,class:"offline"},w=["src"],b={class:"name-bar"};t("M",h({name:"MonitorItem",components:{},props:{item:{type:Object,default:()=>({})}},emits:["collect"],setup(t,{attrs:e,slots:i,emit:o}){},data:()=>({loading:!1,defaultUrl:"https://statics.ys7.com/device/assets/imgs/public/homeDevice.jpeg",iconUrl:"https://statics.ys7.com/device/assets/imgs/public/homeDevice.jpeg"}),computed:{collectStatus(){return this.item.collectStatus},async cover(){}},watch:{"item.icon":{handler(t){t?(this.iconUrl=this.defaultUrl,a({g9s:[t]}).then((t=>{t.length>0&&n({fileToken:t[0].fileToken,width:127,height:86}).then((t=>{if(200===t.status){const e=URL.createObjectURL(new Blob([t.data]));this.iconUrl=e}}))}))):this.iconUrl=this.defaultUrl},immediate:!0,deep:!0}},created(){},mounted(){},methods:{async toggleFav(){try{if(!0===this.loading)return;this.loading=!0,"0"==this.collectStatus?(await i({deviceId:this.item.id}),this.item.collectStatus="1"):(await o({deviceId:this.item.id}),this.item.collectStatus="0"),g({message:"0"==this.collectStatus?"已收藏":"已取消收藏",forbidClick:!0,onOpened:()=>{},onClose:()=>{this.$emit("collect",this.collectStatus)}})}catch(t){console.log(t)}finally{this.loading=!1}},goDetail(){this.$router.push({name:"MonitorDetail",query:{id:this.item.id}})}}},[["render",function(t,e,i,o,a,n){const h=s("van-icon");return l(),c("div",v,[r("div",u,["0"==i.item.status?(l(),c("div",p,e[2]||(e[2]=[r("div",{class:"text"},"离线",-1)]))):(l(),c("div",{key:1,class:"online",onClick:e[0]||(e[0]=d((t=>n.goDetail()),["stop","prevent"]))},[r("img",{class:"cover",src:a.iconUrl,alt:"图片"},null,8,w),m(h,{class:"icon",name:"play-circle"})]))]),r("div",b,f(i.item.name),1),r("div",{class:"fav-tag",onClick:e[1]||(e[1]=d((t=>n.toggleFav()),["stop","prevent"]))},[m(h,{class:"icon",name:"star",color:"0"==n.collectStatus?"#f5c278":"#efefef"},null,8,["color"])])])}],["__scopeId","data-v-8a10495f"]]))}}}));
