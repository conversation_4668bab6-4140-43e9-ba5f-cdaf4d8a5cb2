import{g as I}from"./api-c1f17e6c.js";import{M}from"./MonitorItem-2ff5bad9.js";import{_ as N,u as b}from"./index-4829f8e2.js";import{Q as n,R as t,S as i,k as a,U as _,V as d,X as v,F as c,W as y,Z as L,Y as B}from"./verder-361ae6c7.js";import"./file-2bef16be.js";import"./vant-91101745.js";const U=b(),A={name:"MonitorSearch",components:{MonitorItem:M},props:{},emits:[],setup(r,{attrs:o,slots:p,emit:f}){},data(){return{search:{name:"",deviceRootType:"001"},loading:!1,finished:!1,list:[],refreshing:!1}},computed:{portalId(){return this.$store.PORTAL_ID}},watch:{},created(){},mounted(){},methods:{onRefresh(){this.refreshing=!1,this.onLoadList()},onSearch(r){this.onLoadList()},onCancel(){console.log("onCancel")},async onLoadList(){try{this.loading=!0,this.finished=!1,this.list=[];const r={...this.search,isFilterHidden:1};let o=U.PORTALS.filter(e=>e.type!=1);console.log("this.portals ",o);const p=await I(r);let f=[];for(let e=0;e<o.length;e++){let s=o[e];s.index=e,s.devices=p.records.filter(h=>h.portalId==s.id)}this.list=[...o],this.finished=!0}catch(r){console.log(r),this.finished=!0,this.list=[]}finally{this.loading=!1}}}},D={class:"pull-wp"},F={class:"group-item"},T={key:0,class:"p-[10px]"};function O(r,o,p,f,e,s){const h=n("Navbar"),k=n("van-search"),x=n("van-icon"),u=n("van-col"),S=n("MonitorItem"),V=n("van-row"),C=n("van-empty"),R=n("van-list"),w=n("van-pull-refresh");return t(),i(c,null,[a(h,{back:""}),a(k,{modelValue:e.search.name,"onUpdate:modelValue":o[0]||(o[0]=l=>e.search.name=l),placeholder:"请输入监控名称搜索","show-action":!1,onSearch:s.onSearch,onCancel:s.onCancel},null,8,["modelValue","onSearch","onCancel"]),_("div",D,[a(w,{modelValue:e.refreshing,"onUpdate:modelValue":o[2]||(o[2]=l=>e.refreshing=l),onRefresh:s.onRefresh},{default:d(()=>[a(R,{loading:e.loading,"onUpdate:loading":o[1]||(o[1]=l=>e.loading=l),finished:e.finished,"finished-text":e.list&&e.list.length?"没有更多了":"",onLoad:s.onLoadList,"immediate-check":!1},{default:d(()=>[e.list&&e.list.length?(t(),v(V,{key:0,class:"p-[10px]"},{default:d(()=>[(t(!0),i(c,null,y(e.list||[],({name:l,devices:m},P)=>(t(),i(c,{key:l},[m&&m.length?(t(),i(c,{key:0},[a(u,{span:"24"},{default:d(()=>[_("div",F,[a(x,{class:"icon",name:"label"}),_("span",null,B(l),1)])]),_:2},1024),(t(!0),i(c,null,y(m||[],g=>(t(),v(u,{class:"col-item",key:g.id,span:"24"},{default:d(()=>[a(S,{item:g,class:"!h-[180px]"},null,8,["item"])]),_:2},1024))),128))],64)):L("",!0)],64))),128))]),_:1})):(t(),i(c,{key:1},[e.loading?L("",!0):(t(),i("div",T,[a(C,{image:"search",description:"暂无搜索结果"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])])],64)}const Z=N(A,[["render",O],["__scopeId","data-v-0330c5e5"]]);export{Z as default};
