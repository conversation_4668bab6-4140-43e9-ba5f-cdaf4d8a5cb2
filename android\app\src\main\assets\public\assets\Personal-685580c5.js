import{u as V,s as O,i as w}from"./index-4829f8e2.js";import{O as E,c as m,Q as s,R as r,S as _,k as e,U as i,F as f,Y as L,V as l,B as x,X as N,Z as F}from"./verder-361ae6c7.js";import{e as I}from"./vant-91101745.js";const S={class:"flex flex-row items-center pt-[10px] px-[20px] pb-[30px] bg-[var(--nav-bar-bg-color)] text-[var(--nav-bar-text-color)]"},P={class:"flex-shrink-0 w-[60px] h-[60px] flex items-center justify-center bg-violet-400 rounded-[50%] mr-[20px]"},z={class:"text-[14px]"},D={class:"mb-[8px]"},T={key:1},H={__name:"Personal",setup(U){const h=E(),c=V(),A=O(),B=m(()=>c.PORTAL),t=m(()=>c.USER_INFO),n=m(()=>{var o,v,p;return((v=(o=t.value)==null?void 0:o.orgList)==null?void 0:v.find(d=>{var a;return d.portalId==((a=B.value)==null?void 0:a.id)}))||((p=t.value)==null?void 0:p.orgList[0])||{}});n!=null&&n.name,n!=null&&n.orgNo;const g=m(()=>c.VERSION_INFO),k=()=>{w().then(()=>{c.CLEAR_DATA(),A.CLEAR_BIZ(),h.replace({name:"Login"})})},R=()=>{I({title:"提示",message:"是否确认退出登录？",beforeClose:async u=>{try{return u==="confirm"&&k(),Promise.resolve(!0)}catch(o){}}})};return(u,o)=>{const v=s("van-nav-bar"),p=s("van-icon"),d=s("van-tag"),a=s("van-cell"),b=s("van-cell-group"),y=s("van-button"),C=s("van-space");return r(),_(f,null,[e(v,{"safe-area-inset-top":"",border:!1,title:"个人信息","left-arrow":"",onClickLeft:o[0]||(o[0]=()=>u.$router.replace({name:"Home"}))}),i("div",S,[i("div",P,[e(p,{name:"contact",size:"2.4em"})]),i("div",z,[t.value&&t.value.id?(r(),_(f,{key:0},[i("div",D,L(t.value.userFullname),1),i("div",null,[e(d,{type:"success",size:"medium"},{default:l(()=>[x(L(n.value.name||"无部门"),1)]),_:1})])],64)):(r(),_("div",T,"未登录"))])]),t.value&&t.value.id?(r(),_(f,{key:0},[e(b,{class:"mt-[10px]"},{default:l(()=>[e(a,{title:"我的消息","is-link":"",to:"/message/list"})]),_:1}),e(b,{class:"mt-[10px]"},{default:l(()=>[e(a,{title:"用户名",value:t.value.userName},null,8,["value"]),e(a,{title:"手机号",value:t.value.phone},null,8,["value"]),e(a,{title:"邮箱",value:t.value.email},null,8,["value"]),g.value.version?(r(),N(a,{key:0,title:"App版本",value:g.value.version},null,8,["value"])):F("",!0)]),_:1}),e(C,{direction:"vertical",fill:"",class:"mt-[40px] px-[12px]"},{default:l(()=>[e(y,{block:"",type:"primary",onClick:R},{default:l(()=>o[1]||(o[1]=[x("退出登录")])),_:1,__:[1]})]),_:1})],64)):(r(),N(C,{key:1,direction:"vertical",fill:"",class:"mt-[40px] px-[12px]"},{default:l(()=>[e(y,{block:"",type:"primary",onClick:k},{default:l(()=>o[2]||(o[2]=[x("登录")])),_:1,__:[2]})]),_:1}))],64)}}};export{H as default};
