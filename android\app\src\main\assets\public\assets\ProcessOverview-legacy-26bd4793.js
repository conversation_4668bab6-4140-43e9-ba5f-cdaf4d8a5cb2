System.register(["./index-legacy-09188690.js","./verder-legacy-e6127216.js","./vant-legacy-b51a9379.js"],(function(t,e){"use strict";var a,o,i,n,s,r,l,d,c,u,m,f,p,h,A;return{setters:[t=>{a=t._,o=t.h,i=t.A,n=t.q},t=>{s=t.R,r=t.S,l=t.U,d=t.Y,c=t.a1,u=t.W,m=t.y,f=t.F,p=t.Q,h=t.k,A=t.V},null],execute:function(){var e=document.createElement("style");e.textContent=".carBox .title[data-v-e2c1dffb]{font-family:MiSans,MiSans;font-weight:600;font-size:3.73333vw;color:#1d2129;line-height:5.33333vw;font-style:normal;text-transform:none;border-bottom:1px solid #e5e6eb;padding-bottom:2.66667vw}.item[data-v-3a17ca2d]{background:#f4f4f4;border-radius:2.13333vw;padding:2.13333vw 3.73333vw;margin:1.06667vw 0}.item .state1[data-v-3a17ca2d]{border-color:#f53f3f;background:#ffebeb;color:#f53f3f}.item .state2[data-v-3a17ca2d]{color:#ff7d00;border-color:#f53f3f}.item .state3[data-v-3a17ca2d]{color:#1d90ef;border-color:#1d90ef}.item>div[data-v-3a17ca2d]{margin-bottom:1.06667vw}.item>div[data-v-3a17ca2d]:first-child{font-family:Alibaba PuHuiTi 2,Alibaba PuHuiTi 20;font-weight:400;font-size:3.73333vw;color:#1d2129;line-height:5.86667vw;text-align:left;font-style:normal;text-transform:none}.item>div[data-v-3a17ca2d]:nth-child(2){width:21.6vw;height:4.8vw;border-radius:2.4vw;font-family:Alibaba PuHuiTi 2,Alibaba PuHuiTi 20;font-weight:400;font-size:3.2vw;line-height:4.8vw;text-align:center;font-style:normal;text-transform:none;border:1px solid;border-radius:9px}.item>div[data-v-3a17ca2d]:last-child{font-family:Alibaba PuHuiTi 2,Alibaba PuHuiTi 20;font-weight:400;font-size:3.2vw;color:#86909c;line-height:5.33333vw;text-align:left;font-style:normal;text-transform:none}#line[data-v-cd13a3f9]{height:53.33333vw}.ratio[data-v-737b3ef7]{margin-top:4.26667vw;font-family:Alibaba PuHuiTi 2,Alibaba PuHuiTi 20;font-weight:400;font-size:5.33333vw;color:#1d2129;line-height:5.86667vw;text-align:left;font-style:normal;text-transform:none;margin-bottom:2.13333vw}.van-progress[data-v-737b3ef7]{margin-bottom:2.66667vw}.OutputValue[data-v-737b3ef7]{border-top:1px solid #f2f3f5;padding-top:2.66667vw}.OutputValue>div[data-v-737b3ef7]{width:50%;display:flex;align-items:center}.OutputValue>div[data-v-737b3ef7]:last-child{border-left:1px solid #e5e6eb;padding-left:4.26667vw}.OutputValue>div img[data-v-737b3ef7]{margin-right:2.66667vw}.OutputValue>div>div[data-v-737b3ef7]{display:flex;flex-direction:column}.OutputValue>div>div span[data-v-737b3ef7]:first-child{font-family:Alibaba PuHuiTi 2,Alibaba PuHuiTi 20;font-weight:400;font-size:3.73333vw;color:#86909c;line-height:5.86667vw;text-align:left;font-style:normal;text-transform:none}.OutputValue>div>div span[data-v-737b3ef7]:last-child{font-family:Alibaba PuHuiTi 2,Alibaba PuHuiTi 20;font-weight:400;font-size:4.26667vw;color:#1d2129;line-height:5.86667vw;text-align:left;font-style:normal;text-transform:none}#pie[data-v-c9324147]{height:37.33333vw}#bar[data-v-a2a07809]{height:74.66667vw}\n",document.head.appendChild(e);const v={class:"carBox p-4 my-4 bg-#fff"},b={class:"title"},g=a({name:"carBox",components:{},props:{},emits:[],setup(t,{attrs:e,slots:a,emit:o}){},props:{title:{type:String,default:""}},data:()=>({dataSource:{}}),computed:{},watch:{},created(){},mounted(){},methods:{}},[["render",function(t,e,a,o,i,n){return s(),r("div",v,[l("div",b,d(a.title),1),c(t.$slots,"default",{},void 0,!0)])}],["__scopeId","data-v-e2c1dffb"]]);function w(t){return o({url:`${i.VUE_APP_BASE_API_SERVICENAME}/progress-boq-approve/statistics`,method:"get",params:t})}const y=a({name:"Tasks",components:{},props:{},emits:[],setup(t,{attrs:e,slots:a,emit:o}){},data:()=>({dataSource:{}}),computed:{state(){return t=>this.$dayjs(t).isBefore(this.$dayjs(new Date),"day")?"1":this.$dayjs(new Date).isSameOrBefore(this.$dayjs(t),"day")&&this.$dayjs(t).diff(this.$dayjs(new Date),"day")<=30?"2":this.$dayjs(new Date).isBefore(this.$dayjs(t),"day")&&this.$dayjs(t).diff(this.$dayjs(new Date),"day")>30?"3":void 0}},watch:{},created(){},mounted(){o({url:`${i.VUE_APP_BASE_API_SERVICENAME}/progress-milepost/statistics`,method:"get"}).then((t=>{this.dataSource=t}))},methods:{}},[["render",function(t,e,a,o,i,n){return s(!0),r(f,null,u(i.dataSource,((e,a)=>(s(),r("div",{key:a,class:"item"},[l("div",null,d(a),1),l("div",{class:m("state"+n.state(e.planDate))},d(t.$dayjs(e.planDate).format("YYYY-MM-DD")),3),l("div",null,d(e.milepostName),1)])))),128)}],["__scopeId","data-v-3a17ca2d"]]),P={id:"line"},E=a({name:"MonthlyPlanCompletion",components:{},props:{},emits:[],setup(t,{attrs:e,slots:a,emit:o}){},data:()=>({}),computed:{},watch:{},created(){},mounted(){o({url:`${i.VUE_APP_BASE_API_SERVICENAME}/progress-real/statisticsForTrend`,method:"get"}).then((t=>{this.init(t)}))},methods:{init(t){var e,a=document.getElementById("line"),o=n(a);(e={xAxis:{type:"category",boundaryGap:!1,data:[this.$dayjs().subtract(5,"month").format("YYYY-MM"),this.$dayjs().subtract(4,"month").format("YYYY-MM"),this.$dayjs().subtract(3,"month").format("YYYY-MM"),this.$dayjs().subtract(2,"month").format("YYYY-MM"),this.$dayjs().subtract(1,"month").format("YYYY-MM"),this.$dayjs().format("YYYY-MM")]},tooltip:{trigger:"axis"},color:["#1D90EF","#50D7A5","#3F6DEB","#F0AD4F"],legend:{data:t.map((t=>Object.keys(t))).flat(),bottom:0},yAxis:{type:"value",axisLabel:{formatter:"{value} %"}},series:t.map((t=>({name:Object.keys(t)[0],type:"line",smooth:!0,showSymbol:!1,data:Object.values(t)[0].map((t=>Object.values(t)[0])).map((t=>100*t))})))})&&o.setOption(e)}}},[["render",function(t,e,a,o,i,n){return s(),r("div",P)}],["__scopeId","data-v-cd13a3f9"]]),B={class:"ratio"},C={class:"flex OutputValue"},x={id:"pie"},I={id:"bar"},S={class:"bg-#F5F5F5"};t("default",a({name:"ProcessOverview",components:{carBox:g,MilestoneMonitor:y,OutputCompletedSituation:a({name:"OutputCompletedSituation",components:{},props:{},emits:[],setup(t,{attrs:e,slots:a,emit:o}){},data:()=>({dataSource:{}}),computed:{},watch:{},created(){},mounted(){w().then((t=>{this.dataSource=t["总合同"]}))},methods:{}},[["render",function(t,e,a,o,i,n){const c=p("van-progress");return s(),r(f,null,[l("div",B,d(i.dataSource.ratio)+"%",1),h(c,{percentage:i.dataSource.ratio,"show-pivot":!1,"stroke-width":"8",color:"#1D90EF","track-color":"#A4D6FF"},null,8,["percentage"]),l("div",C,[l("div",null,[e[1]||(e[1]=l("img",{src:"data:image/png;base64,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",alt:""},null,-1)),l("div",null,[e[0]||(e[0]=l("span",null,"累计产值",-1)),l("span",null,d((i.dataSource.realTotal/1e8).toFixed(2))+"亿",1)])]),l("div",null,[e[3]||(e[3]=l("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABBCAYAAABhNaJ7AAAAAXNSR0IArs4c6QAABYpJREFUeF7tW01sG0UU/sZJExIhN3tAQoptOeLUgkRyoggpTQ7NGQSFa6qCxAnbcMc292bXnDgALUdUED1woRya9ADpKT4APVW1YldCArQmCc2/B94mG63X3t3Z3dm14zBSpFiev+977817b/yG4Yw3FhR/StXnEonEy5y35gBMM2CCAxNB5/MzjgHNFlBjQI0zVNHCSqOgLPuZw+zri4Csqk+0gDwYcnGBFQVlkAEsD3GUawWl5mOcd9dj4CXOkPPu3Rc9bokS4akBGVUniRf7TeJeNJNGgA2V1nPJr9z6uhKQUXXtFEndCadWzysFpy+7EkAqf8jwHQA64E5940B1i2O+WVCadjBdCcho+hoHpk89cgsAOiAbeWXek4ABUXthc2jTgLSqL4Lh5iBJvkPiHIX1gqJ1xAFZVc+2GO5xIDvIBABobnJMmefBiQakNZ0kvzjg4E14J57BIICkf8jw+IyAN2BuciikBQYBZ0z6BgGcodTIKWWDgIymPz4Dtm9X8GY9ryiMsjrGcC9u9b96cQSFV54BB6Cu7uCbh3txbwGcY56lKnqRcZTiWv3V1DBuXBlHKploW7K+0YqdCM6QZ2lNp5D39agJIOAk8UupYdelfv3jEJ/c38bPjYOot0Tz32FRh72iwO1ob/+2B/XBDhobrSiJqBEBehSpLqk4SZxsPUyLmIgmmQCdQ9JacpTh+swork+Pgv6X1aIiQhoBUQG3EkgHJXmLL9Z2sbErR25SCCCJk7rLlLib5sj0GKEIWHjhHEqzYx0uTZbae80jg4jABNxYGMfVC+EOOC+Aot+Ty3zn2y3R7m39AhPwy/vnY1N5EWSZSsdtl8gwBCag1+pvoiMzKN/fxt1H+0KA7Z0CE2BOZMb09tA20G58DCIv8PnaLr6shvMIoQmgPRP4ty6M4N0Zub6/Gx+ygJtzSyGAJqOQ9+PZMbz43JAPOfrvSrnCe9//Iy1EDk0ASX/pynhHkkMblUVGt7lkRYaBCXCK9cklUX6/+uTAMI0w+YB1LqezJiwRgQn46VqyLQByC0ouTQ6jeFncPKzA7UbSjQha+7WbG/7tCQjuBv0QIOoxCMhHd58a2uPU+oYAJ/UWCU/tIETGON0r9MwETAk5EbHaOMCHPz51Pa2JiOQIw+2He47ZnRNwNzPxYwuBzwD7ImTnSwudd31BJeTmXcor265mEjsBIncBokTQXMXZMcebJPNOgDyNjBZaA/zeBZjhq/2uT4REK2CRc0OEoMAEhEmGrFL0C9wOqmfJ0HoufEUcbf78KAudVlN+8NJnf4sIvKNPYA34+s1njfi/H9oPj/aN/CBIC0wALdarVNgEKuMcCEWAaIQXRDJuY8yUWHsQ3hNIIcB6J/D2xZHILkll3wXQvqUR4BUZhtWCpdWd0Lc/3fbw/09jaU2n0pjICqOC3gnIivXdNI8KKGP7edwpV7BvMA7gljXvsExFz3GOk7q5sLbqNd7JdYrcBXjN7fd7qhPqeYkMhcKlle3elMgkMGcWSUVSI+BXIjH3r9XzytQRARW9yGOsE4oZqNNyt+p55ZpZKEnl8XqfbCyWbexzTP1eUOjd0VFLa7r6Xx1tPpbVe7+IIX0jEjT3YrwLYqCCyfB5bu8Buu2gts8xT9JvI4A+TKp/5RMsQZowsK3FsPgkp5y8I+qoYhpwU+h4P9S1jCut6VQ6OxDvhUxVprC3kVdm7Krt+Gjq+PHEQLwbovdCWxxvCD+aGjDP4P/ZnFVNJtU/FxNsqBhlxhjRids8fhNQcZtfqJTzeVXPnmMgEk7Lkxptk6PcTeWFzgAnxixE0AEZ2R1CQI0giWtbLVREgJtrCGlAtw2lPtUvg2OOcUzTaxN2REhcQRTVxNFflTMsg6Ha+EBZCULcv2aFBpoCDWYaAAAAAElFTkSuQmCC",alt:""},null,-1)),l("div",null,[e[2]||(e[2]=l("span",null,"总产值",-1)),l("span",null,d((i.dataSource.planTotal/1e8).toFixed(2))+"亿",1)])])])],64)}],["__scopeId","data-v-737b3ef7"]]),OutputReportProportion:a({name:"OutputReportProportion",components:{},props:{},emits:[],setup(t,{attrs:e,slots:a,emit:o}){},data:()=>({}),computed:{},watch:{},created(){},mounted(){w().then((t=>{const e=[];for(const a in t)"总合同"!=a&&e.push({name:a,value:(t[a].realTotal/1e8).toFixed(2)});this.init(e)}))},methods:{init(t){const e=document.getElementById("pie"),a=n(e),o={tooltip:{trigger:"item"},color:["#1D90EF","#50D7A5","#3F6DEB","#F0AD4F"],legend:{orient:"vertical",right:"10%",icon:"circle",top:"center",textStyle:{rich:{a:{color:"#86909C",fontSize:13},b:{color:"#1D2129",fontSize:14}}},formatter:e=>`{a|${e}}    {b|${t.find((t=>t.name==e)).value}亿}`},series:[{labelLine:{show:!1},label:{show:!1},type:"pie",radius:"70%",center:["30%","50%"],data:t,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};o&&a.setOption(o)}}},[["render",function(t,e,a,o,i,n){return s(),r("div",x)}],["__scopeId","data-v-c9324147"]]),ProgressPlanCompletionStatus:a({name:"Tasks",components:{},props:{},emits:[],setup(t,{attrs:e,slots:a,emit:o}){},data:()=>({}),computed:{},watch:{},created(){},mounted(){o({url:`${i.VUE_APP_BASE_API_SERVICENAME}/progress-real/statisticsForSituation`,method:"get"}).then((t=>{this.init(t)}))},methods:{init(t){const e=document.getElementById("bar"),a=n(e),o={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{itemWidth:14},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value"},yAxis:{type:"category",boundaryGap:!1,data:Object.keys(t),axisLabel:{color:"#86909C"}},color:["#50D7A5","#1D90EF","#F0AD4F","#FC7577"],series:[{name:"按期完成",type:"bar",stack:"total",barWidth:20,label:{show:!1,formatter:t=>t.value||""},emphasis:{focus:"series"},data:Object.keys(t).map((e=>t[e]["按期完成"]))},{name:"正常进行中",type:"bar",barWidth:20,stack:"total",label:{show:!1,formatter:t=>t.value||""},emphasis:{focus:"series"},data:Object.keys(t).map((e=>t[e]["正常进行中"]))},{name:"超期完成",type:"bar",barWidth:20,stack:"total",label:{show:!1,formatter:t=>t.value||""},emphasis:{focus:"series"},data:Object.keys(t).map((e=>t[e]["超期完成"]))},{name:"滞后任务",type:"bar",barWidth:20,stack:"total",label:{show:!1,formatter:t=>t.value||""},emphasis:{focus:"series"},data:Object.keys(t).map((e=>t[e]["滞后任务"]))}]};o&&a.setOption(o)}}},[["render",function(t,e,a,o,i,n){return s(),r("div",I)}],["__scopeId","data-v-a2a07809"]]),MonthlyPlanCompletion:E},props:{},emits:[],setup(t,{attrs:e,slots:a,emit:o}){},data:()=>({dataSource1:{}}),computed:{},watch:{},created(){},mounted(){},methods:{}},[["render",function(t,e,a,o,i,n){const d=p("Navbar"),c=p("OutputCompletedSituation"),u=p("carBox"),m=p("OutputReportProportion"),v=p("MonthlyPlanCompletion"),b=p("ProgressPlanCompletionStatus"),g=p("MilestoneMonitor");return s(),r(f,null,[h(d,{back:!t.envFeishu,backEvent:()=>t.$router.replace({name:"Home"})},null,8,["back","backEvent"]),l("div",S,[h(u,{title:"产值完成情况"},{default:A((()=>[h(c,{dataSource:i.dataSource1},null,8,["dataSource"])])),_:1}),h(u,{title:"产值上报占比"},{default:A((()=>[h(m)])),_:1}),h(u,{title:"月计划完成度趋势"},{default:A((()=>[h(v)])),_:1}),h(u,{title:"进度计划完成情况"},{default:A((()=>[h(b)])),_:1}),h(u,{title:"里程碑监控"},{default:A((()=>[h(g)])),_:1})])],64)}]]))}}}));
