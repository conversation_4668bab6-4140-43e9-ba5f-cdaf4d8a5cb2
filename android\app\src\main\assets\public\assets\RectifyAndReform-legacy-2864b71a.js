System.register(["./FormItemDate-legacy-c4422d42.js","./FormItemSection-legacy-66423754.js","./FormItemCalendar-legacy-ea787ea1.js","./index-legacy-645a3645.js","./FormItemCoord-legacy-e6ddc9b7.js","./FormItemCascader-legacy-b386877e.js","./FormItemPicker-legacy-fd45c24d.js","./wbsUtil-legacy-542349ac.js","./FormItemPerson-legacy-e6e57748.js","./constants-legacy-82bb4fe6.js","./verder-legacy-e6127216.js","./index-legacy-09188690.js","./file-legacy-e670f35e.js","./file-legacy-a550097a.js","./delegate-legacy-70e6b218.js","./index-legacy-ca9792d0.js","./vant-legacy-b51a9379.js","./validate-legacy-4e8f0db9.js","./array-legacy-2920c097.js"],(function(e,t){"use strict";var a,r,l,o,s,n,i,u,d,m,c,p,h,f,g,v,y,D,b,I,F;return{setters:[e=>{a=e.F},e=>{r=e._},e=>{l=e.F},e=>{o=e.U},e=>{s=e.F},e=>{n=e.F},e=>{i=e.F},e=>{u=e.g},e=>{d=e.F},e=>{m=e.R},e=>{c=e.Q,p=e.R,h=e.S,f=e.k,g=e.U,v=e.V,y=e.B,D=e.a2,b=e.Z,I=e.F},e=>{F=e._},null,null,null,null,null,null,null],execute:function(){var t=document.createElement("style");t.textContent=".view-height[data-v-297ec037]{height:calc(100vh - var(--van-nav-bar-height) - var(--sat));padding:0 2.66667vw;overflow-x:hidden;overflow-y:scroll}.view-height.btn-bom[data-v-297ec037]{padding:0 2.66667vw 13.33333vw!important}.btn-group[data-v-297ec037]{width:calc(100% - 5.33333vw);display:flex;justify-content:space-between;gap:0 4vw;position:absolute;bottom:2.66667vw;left:2.66667vw}.btn-group>button[data-v-297ec037]{flex:1}\n",document.head.appendChild(t);const w={class:"view-height btn-bom"},C={key:0,class:"btn-group"};e("default",F({name:"HiddenTroubleInfo",components:{FormItemDate:a,FormItemSection:r,FormItemPerson:d,FormItemPicker:i,FormItemCascader:n,FormItemCoord:s,UploadFiles:o,FormItemCalendar:l},props:{title:{type:String,default:""}},emits:[],setup(e,{attrs:t,slots:a,emit:r}){},data(){var e;return{taskKey:(null===(e=this.$route.query)||void 0===e?void 0:e.taskKey)||"",formData:{},wbsList:[],statusMap:m}},computed:{navbarTitle(){return this.$route.query.title||"新增整改"},navbarType(){return this.$route.query.type},portal(){return this.$store.PORTAL},user(){return this.$store.USER_INFO},toStartReviewStatus(){var e;return null===(e=this.statusMap)||void 0===e?void 0:e.PENDING_REVIEW}},methods:{handleClose(){this.$store.rectify_AND_REFORM={},this.$router.back()},async handleAddOrCreate(){try{await this.$refs.form.validate(),this.formData.constructionPost=this.formData.lng&&this.formData.lat?[this.formData.lng,this.formData.lat].join(","):"",this.formData.hasOwnProperty("index")&&this.$store.QUALITY_INSPECTION.hasOwnProperty("corrections")?this.$store.QUALITY_INSPECTION.corrections[this.formData.index]=this.formData:!this.formData.hasOwnProperty("index")&&this.$store.QUALITY_INSPECTION.hasOwnProperty("corrections")&&this.$store.QUALITY_INSPECTION.corrections.push(this.formData),this.$store.rectify_AND_REFORM={},this.$router.back()}catch(e){}},async getWbsList(){this.wbsList=await u(this.formData.sectionId,!0,this.portal),this.$nextTick((()=>{this.$refs.formItemCascaderRef.chengeLabel()}))},handlePersonChange(e){}},mounted(){if(Object.keys(this.$store.rectify_AND_REFORM).length){if(this.formData.constructionPost){let e=this.formData.constructionPost.split(",");this.formData.lng=e[0],this.formData.lat=e[1]}this.formData=this.$store.rectify_AND_REFORM}else this.formData={sectionId:this.$route.query.sectionId,correctionNumber:"",projectPosition:"",constructionArea:"",deadline:"",description:"",requirement:"",fileUpload:"",measure:"",situation:"",measureDate:"",initiator:"",processName:"",processCode:"",measureFileUpload:"",isPass:"",positionInfo:null,lng:null,lat:null,height:null,rectificationStatus:this.toStartReviewStatus};this.getWbsList()}},[["render",function(e,t,a,r,l,o){const s=c("Navbar"),n=c("van-field"),i=c("FormItemSection"),u=c("FormItemCascader"),d=c("FormItemDate"),m=c("UploadFiles"),F=c("FormItemCoord"),P=c("FormItemPerson"),U=c("van-cell-group"),_=c("van-form"),N=c("van-button");return p(),h(I,null,[f(s,{back:"",title:o.navbarTitle},null,8,["title"]),g("div",w,[f(_,{ref:"form","label-width":"9em","input-align":"right","error-message-align":"right"},{default:v((()=>[f(U,{border:!1},{default:v((()=>[f(n,{modelValue:l.formData.correctionNumber,"onUpdate:modelValue":t[0]||(t[0]=e=>l.formData.correctionNumber=e),label:"整改单号",placeholder:"自动生成",readonly:""},null,8,["modelValue"]),f(i,{label:"所属标段",placeholder:"请选择",modelValue:l.formData.sectionId,"onUpdate:modelValue":t[1]||(t[1]=e=>l.formData.sectionId=e),readonly:"",required:"",rules:[{required:!0,message:"请选择所属标段"}]},null,8,["modelValue"]),f(u,{ref:"formItemCascaderRef",label:"整改部位",value:l.formData.projectPosition,"onUpdate:value":t[2]||(t[2]=e=>l.formData.projectPosition=e),columns:[...l.wbsList],"columns-field-names":{text:"nodeName",value:"id",children:"children"},"trigger-change-mode":!0,placeholder:"请选择",required:"",rules:[{required:!0,message:"请选择整改部位"}]},null,8,["value","columns"]),f(d,{label:"整改期限",value:l.formData.deadline,"onUpdate:value":t[3]||(t[3]=e=>l.formData.deadline=e),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择整改期限"}]},null,8,["value"]),f(n,{label:"详细区域",modelValue:l.formData.constructionArea,"onUpdate:modelValue":t[4]||(t[4]=e=>l.formData.constructionArea=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left"},null,8,["modelValue"]),f(n,{label:"整改内容",modelValue:l.formData.description,"onUpdate:modelValue":t[5]||(t[5]=e=>l.formData.description=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改内容"}],"input-align":"left"},null,8,["modelValue"]),f(n,{label:"整改要求及建议",modelValue:l.formData.requirement,"onUpdate:modelValue":t[6]||(t[6]=e=>l.formData.requirement=e),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改要求及建议"}],"input-align":"left"},null,8,["modelValue"]),f(n,{label:"附件上传","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传资料附件"}]},{input:v((()=>[f(m,{ref:"fileUpload",g9s:l.formData.fileUpload,"onUpdate:g9s":t[7]||(t[7]=e=>l.formData.fileUpload=e),accept:"*",multiple:!0},null,8,["g9s"])])),_:1}),f(F,{label:"经纬度",longitude:l.formData.lng,"onUpdate:longitude":t[8]||(t[8]=e=>l.formData.lng=e),latitude:l.formData.lat,"onUpdate:latitude":t[9]||(t[9]=e=>l.formData.lat=e),title:"选择定位"},null,8,["longitude","latitude"]),f(P,{required:"",label:"问题分发人",title:"问题分发人",userFullname:l.formData.acceptancePersonName,"onUpdate:userFullname":t[10]||(t[10]=e=>l.formData.acceptancePersonName=e),userName:l.formData.acceptancePerson,"onUpdate:userName":t[11]||(t[11]=e=>l.formData.acceptancePerson=e),onChange:o.handlePersonChange,rules:[{required:!0,message:"请选择问题分发人"}]},null,8,["userFullname","userName","onChange"])])),_:1})])),_:1},512),["add","update"].includes(o.navbarType)?(p(),h("div",C,[f(N,{round:"",type:"danger",plain:"",onClick:D(o.handleClose,["stop","prevent"])},{default:v((()=>t[12]||(t[12]=[y("取消")]))),_:1,__:[12]},8,["onClick"]),f(N,{round:"",type:"primary",plain:"",onClick:D(o.handleAddOrCreate,["stop","prevent"])},{default:v((()=>t[13]||(t[13]=[y("保存")]))),_:1,__:[13]},8,["onClick"])])):b("",!0)])],64)}],["__scopeId","data-v-297ec037"]]))}}}));
