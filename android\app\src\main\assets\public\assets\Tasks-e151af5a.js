import{u as w,c as N,r as V,a as I}from"./api-77f046f1.js";import{f as R}from"./formKeys-f34a583f.js";import{Q as l,R as i,S as c,U as r,Y as v,k as m,V as u,B as C,Z as k,X as f,a2 as S,F as _,W as T,_ as M,a3 as P,y as j}from"./verder-361ae6c7.js";import{_ as b}from"./index-4829f8e2.js";import"./vant-91101745.js";const U={name:"TaskItem",components:{},props:{item:{type:Object,default:()=>({})},tabName:String},emits:[],setup(t,{attrs:e,slots:n,emit:d}){},data(){return{}},computed:{taskStateText(){switch(String(this.item.taskState)){case"0":return"流转中";case"1":return"已完成";case"2":return"已废弃";case"3":return"暂存中";default:return"其他"}},tagColor(){var e;switch(String((e=this.item)==null?void 0:e.taskState)){case"0":return"#1989fa";case"1":return"#07c160";case"2":return"#ff4d4f";case"3":return"#faab0c";default:return"#c8c9cc"}},hasView(){var e;const t=(e=this.item)==null?void 0:e.formKey;return R.includes(t)}},watch:{},created(){},mounted(){},methods:{toFormCenter(){if(!this.hasView){this.$showToast({message:"该流程暂不支持移动端查看"});return}const{formBizId:t,formKey:e,taskId:n,taskKey:d,processInstanceId:s,formName:o}=this.item;this.$router.push({path:"/FormCenter/".concat(this.item.formKey),query:{type:this.tabName==="任务待办"?"execute":"view",taskKey:d,formKey:e,bizId:t,taskId:n,processInstanceId:s,title:o}})}}},z={class:"header"},F={class:"left"},D={class:"title"},K={class:"datetime"},B={class:"right"},E={class:"body"},O={class:"item-info"},Y={class:"value"},H={key:0,class:"item-info"},q={class:"value"},A={class:"item-info"},Q={class:"value"},W={key:1,class:"item-info"},X={class:"value"},Z={class:"item-info"},G={class:"value"},J={class:"footer"};function $(t,e,n,d,s,o){const h=l("van-tag");return i(),c("div",{class:"task-item",onClick:e[0]||(e[0]=S(p=>o.toFormCenter(),["stop","prevent"]))},[r("div",z,[r("div",F,[r("div",D,v(n.item.formName||"流程"),1),r("div",K,v(t.$dayjs(n.item.createDate).format("YYYY-MM-DD HH:mm")),1)]),r("div",B,[m(h,{class:"tag",color:o.tagColor,plain:"",size:"medium"},{default:u(()=>[C(v(o.taskStateText),1)]),_:1},8,["color"])])]),r("div",E,[r("div",O,[e[1]||(e[1]=r("span",{class:"key"},"流程名称",-1)),r("span",Y,v(n.item.taskSubject||"-"),1)]),n.tabName==="任务待办"?(i(),c("div",H,[e[2]||(e[2]=r("span",{class:"key"},"任务节点",-1)),r("span",q,v(n.item.taskName||"-"),1)])):k("",!0),r("div",A,[e[3]||(e[3]=r("span",{class:"key"},"开始时间",-1)),r("span",Q,v(n.item.processCreateDate),1)]),n.tabName!=="任务待办"?(i(),c("div",W,[e[4]||(e[4]=r("span",{class:"key"},"完成时间",-1)),r("span",X,v(n.item.finishDate||"暂无"),1)])):k("",!0),r("div",Z,[e[5]||(e[5]=r("span",{class:"key"},"发起人",-1)),r("span",G,v(n.item.taskCreatorName),1)])]),r("div",J,[o.hasView?k("",!0):(i(),f(h,{key:0,class:"no-tag",plain:"",type:"danger"},{default:u(()=>e[6]||(e[6]=[C(v("该流程暂不支持移动端查看"))])),_:1,__:[6]}))])])}const L=b(U,[["render",$],["__scopeId","data-v-d21f52b9"]]);const ee={name:"MyTodo",components:{TaskItem:L},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(t,{attrs:e,slots:n,emit:d}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}},computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const t={...this.searchParams,...this.search};t.page===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e=await w(t),n=this.searchParams.page<=1?[]:this.list||[];this.list=[...n,...e.list],this.searchParams.page++,this.list.length>=e.total&&(this.finished=!0)}catch(t){console.log(t),this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}}}},se={key:0,class:"p-[10px]"};function te(t,e,n,d,s,o){const h=l("TaskItem"),p=l("van-empty"),g=l("van-list"),y=l("van-pull-refresh");return i(),f(y,{modelValue:s.refreshing,"onUpdate:modelValue":e[1]||(e[1]=a=>s.refreshing=a),onRefresh:o.onRefresh},{default:u(()=>[m(g,{loading:s.loading,"onUpdate:loading":e[0]||(e[0]=a=>s.loading=a),finished:s.finished,"finished-text":s.list&&s.list.length?"没有更多了":"",onLoad:o.onLoadList,"immediate-check":!1},{default:u(()=>[s.list&&s.list.length?(i(!0),c(_,{key:0},T(s.list||[],(a,x)=>(i(),f(h,{key:a.id,item:a,tabName:"任务待办"},null,8,["item"]))),128)):(i(),c(_,{key:1},[s.loading?k("",!0):(i(),c("div",se,[m(p,{description:"暂无待办任务"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])}const ae=b(ee,[["render",te],["__scopeId","data-v-6400cf0b"]]);const ne={name:"MyCreate",components:{TaskItem:L},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(t,{attrs:e,slots:n,emit:d}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}},computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const t={...this.searchParams,...this.search};t.page===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e=await N(t),n=this.searchParams.page<=1?[]:this.list||[];this.list=[...n,...e.list],this.searchParams.page++,this.list.length>=e.total&&(this.finished=!0)}catch(t){console.log(t),this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}}}},ie={key:0,class:"p-[10px]"};function oe(t,e,n,d,s,o){const h=l("TaskItem"),p=l("van-empty"),g=l("van-list"),y=l("van-pull-refresh");return i(),f(y,{modelValue:s.refreshing,"onUpdate:modelValue":e[1]||(e[1]=a=>s.refreshing=a),onRefresh:o.onRefresh},{default:u(()=>[m(g,{loading:s.loading,"onUpdate:loading":e[0]||(e[0]=a=>s.loading=a),finished:s.finished,"finished-text":s.list&&s.list.length?"没有更多了":"",onLoad:o.onLoadList,"immediate-check":!1},{default:u(()=>[s.list&&s.list.length?(i(!0),c(_,{key:0},T(s.list||[],(a,x)=>(i(),f(h,{key:a.id,item:a,tabName:"我的发起"},null,8,["item"]))),128)):(i(),c(_,{key:1},[s.loading?k("",!0):(i(),c("div",ie,[m(p,{description:"暂无发起的任务"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])}const re=b(ne,[["render",oe],["__scopeId","data-v-a9a5d373"]]);const le={name:"MyRelation",components:{TaskItem:L},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(t,{attrs:e,slots:n,emit:d}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20}}},computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const t={...this.searchParams,...this.search};t.page===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e=await V(t),n=this.searchParams.page<=1?[]:this.list||[];this.list=[...n,...e.list],this.searchParams.page++,this.list.length>=e.total&&(this.finished=!0)}catch(t){console.log(t),this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}}}},ce={key:0,class:"p-[10px]"};function de(t,e,n,d,s,o){const h=l("TaskItem"),p=l("van-empty"),g=l("van-list"),y=l("van-pull-refresh");return i(),f(y,{modelValue:s.refreshing,"onUpdate:modelValue":e[1]||(e[1]=a=>s.refreshing=a),onRefresh:o.onRefresh},{default:u(()=>[m(g,{loading:s.loading,"onUpdate:loading":e[0]||(e[0]=a=>s.loading=a),finished:s.finished,"finished-text":s.list&&s.list.length?"没有更多了":"",onLoad:o.onLoadList,"immediate-check":!1},{default:u(()=>[s.list&&s.list.length?(i(!0),c(_,{key:0},T(s.list||[],(a,x)=>(i(),f(h,{key:a.id,item:a,tabName:"与我相关"},null,8,["item"]))),128)):(i(),c(_,{key:1},[s.loading?k("",!0):(i(),c("div",ce,[m(p,{description:"暂无相关的任务"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])}const he=b(le,[["render",de],["__scopeId","data-v-116f36ee"]]);const me={name:"MyCirculation",components:{TaskItem:L},props:{search:{type:Object,defalut:()=>({})}},emits:[],setup(t,{attrs:e,slots:n,emit:d}){},data(){return{loading:!1,finished:!1,list:[],refreshing:!1,searchParams:{page:1,size:20,type:"receiver"}}},computed:{},watch:{},created(){},mounted(){this.onLoadList()},methods:{onRefresh(){this.refreshing=!1,this.finished=!1,this.searchParams.page=1,this.onLoadList()},async onLoadList(){try{this.loading=!0,this.finished=!1;const t={...this.searchParams,...this.search};t.page===1&&this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0});const e=await I(t),n=this.searchParams.page<=1?[]:this.list||[];this.list=[...n,...e.list],this.searchParams.page++,this.list.length>=e.total&&(this.finished=!0)}catch(t){console.log(t),this.finished=!0}finally{setTimeout(()=>{this.loading=!1,this.$closeToast()},100)}}}},fe={key:0,class:"p-[10px]"};function ue(t,e,n,d,s,o){const h=l("TaskItem"),p=l("van-empty"),g=l("van-list"),y=l("van-pull-refresh");return i(),f(y,{modelValue:s.refreshing,"onUpdate:modelValue":e[1]||(e[1]=a=>s.refreshing=a),onRefresh:o.onRefresh},{default:u(()=>[m(g,{loading:s.loading,"onUpdate:loading":e[0]||(e[0]=a=>s.loading=a),finished:s.finished,"finished-text":s.list&&s.list.length?"没有更多了":"",onLoad:o.onLoadList,"immediate-check":!1},{default:u(()=>[s.list&&s.list.length?(i(!0),c(_,{key:0},T(s.list||[],(a,x)=>(i(),f(h,{key:a.id,item:a,tabName:"抄送查阅"},null,8,["item"]))),128)):(i(),c(_,{key:1},[s.loading?k("",!0):(i(),c("div",fe,[m(p,{description:"暂无更多数据"})]))],64))]),_:1},8,["loading","finished","finished-text","onLoad"])]),_:1},8,["modelValue","onRefresh"])}const pe=b(me,[["render",ue],["__scopeId","data-v-0839a53d"]]);const _e={name:"Tasks",components:{MyTodo:ae,MyCreate:re,MyRelation:he,MyCirculation:pe},props:{},emits:[],setup(t,{attrs:e,slots:n,emit:d}){},data(){var t,e;return{envFeishu:!!window.ENV_FEISHU,tabName:((e=(t=this.$bizStore)==null?void 0:t.tempData)==null?void 0:e.tasksTabName)||"MyTodo",search:{taskSubject:""}}},computed:{tabs(){return this.envFeishu?[{title:"任务待办",name:"MyTodo"},{title:"与我相关",name:"MyRelation"}]:[{title:"任务待办",name:"MyTodo"},{title:"我的发起",name:"MyCreate"},{title:"与我相关",name:"MyRelation"}]}},watch:{tabName(){this.search.taskSubject=""},"search.taskSubject"(t){t===""&&this.onSearch(t)}},created(){},mounted(){},methods:{onClickTab(t){this.$bizStore.saveData({tasksTabName:t.name})},onSearch(t){this.$nextTick(()=>{this.$refs[this.tabName]&&this.$refs[this.tabName].onRefresh()})},onCancel(){}}};function ge(t,e,n,d,s,o){const h=l("Navbar"),p=l("van-tab"),g=l("van-tabs"),y=l("van-search");return i(),c(_,null,[m(h,{back:!s.envFeishu,backEvent:()=>t.$router.replace({name:"Home"})},null,8,["back","backEvent"]),m(g,{class:"tabs-wp",active:s.tabName,"onUpdate:active":e[0]||(e[0]=a=>s.tabName=a),"line-width":"4em",onClickTab:o.onClickTab},{default:u(()=>[(i(!0),c(_,null,T(o.tabs,a=>(i(),f(p,{class:"px-[20px]",key:a.name,name:a.name,title:a.title},null,8,["name","title"]))),128))]),_:1},8,["active","onClickTab"]),m(y,{modelValue:s.search.taskSubject,"onUpdate:modelValue":e[1]||(e[1]=a=>s.search.taskSubject=a),placeholder:"搜索流程名称","show-action":!1,onSearch:o.onSearch,onCancel:o.onCancel},null,8,["modelValue","onSearch","onCancel"]),r("div",{class:j(["view-height",{"no-tabbar":s.envFeishu}])},[(i(),f(P,{include:o.tabs.map(a=>a.name)},[(i(),f(M(s.tabName),{ref:s.tabName,search:s.search},null,8,["search"]))],1032,["include"]))],2)],64)}const Le=b(_e,[["render",ge],["__scopeId","data-v-9e8d8a56"]]);export{Le as default};
