import{h as e}from"./index-4829f8e2.js";const n="project-yjp-tcs";function u(t){return e({url:"".concat(n,"/quality/qualityInspection/page"),method:"get",params:t})}function a(t){return e({url:"".concat(n,"/quality/qualityInspection/detail/").concat(t.id),method:"get"})}function o(t){return e({url:"".concat(n,"/quality/qualityInspection/").concat(t.id),method:"delete"})}function r(t){return e({url:"".concat(n,"/quality/qualityInspection/add"),method:"post",data:t})}function l(t){return e({url:"".concat(n,"/quality/qualityInspection/edit"),method:"put",data:t})}function s(t){return e({url:"/sys-user/orgs/tree",method:"get",params:{isAll:!1,portalId:t,needMemberNum:!0}})}export{a,s as b,r as c,o as d,l as e,u as g};
