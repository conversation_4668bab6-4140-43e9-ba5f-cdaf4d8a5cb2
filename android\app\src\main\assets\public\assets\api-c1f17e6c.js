import{h as t,A as n}from"./index-4829f8e2.js";let o=n.VUE_APP_SCS_API_SERVICENAME;function c(e){return t({url:o+"/device-info/tree/count3",method:"get",params:e})}function d(e){return t({url:o+"/device-info/tree/count2",method:"get",params:e})}function u(e){return t({url:o+"/device-info/page",method:"get",params:e})}function a(e,r){return t({url:o+"/device-info/video/get/"+(e||0)+"/"+(r||0),method:"get"})}function l(e){return t({url:o+"/device-collect",method:"post",data:e})}function s(e){return t({url:o+"/device-collect",method:"delete",data:e,params:e})}function f(e){return t({url:o+"/device-info/video/get?deviceIndexCode="+e,method:"get"})}export{c as a,a as b,f as c,s as d,l as e,u as g,d as t};
