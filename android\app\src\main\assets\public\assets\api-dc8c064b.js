import{U as m}from"./index-fc22947f.js";import{g as p}from"./file-2bef16be.js";import{i as _,a as f,b as h,c as u,d as g}from"./file-842bc27d.js";import{Q as U,R as v,S as w,U as o,k as F,a2 as $,Y as c}from"./verder-361ae6c7.js";import{_ as y,h as b,A as D}from"./index-4829f8e2.js";const T={name:"ListItem",components:{UploadFiles:m},props:{item:{type:Object,default:()=>({})}},emits:[],setup(s,{attrs:e,slots:t,emit:i}){},data(){return{}},computed:{thumbnailUrl(){var s;if((s=this.item)!=null&&s.thumbnailFileUpload){const e="/api";return"".concat(e,"/sys-storage/download_image?f8s=").concat(this.item.thumbnailFileUpload)}return null}},watch:{},created(){},mounted(){},methods:{async goDetail(){var s;if((s=this.item)!=null&&s.attFileUpload)try{const e=await p({g9s:[this.item.attFileUpload]});if(e&&e.length>0){const t=e[0],i=t.fileName||"",r=_(i),a=f(i),l=h(i),n=u(i),d=g(i);if(r){this.$showToast("暂不支持预览，请上传正确的文件格式");return}if(a){this.$showToast("暂不支持预览，请上传正确的文件格式");return}else this.$router.push({name:"NewsDetail",query:{fileData:JSON.stringify({fileToken:t.fileToken,name:t.fileName,isPdf:l,isDoc:n,isXls:d,isVideo:a})}})}else this.$showToast("文件信息获取失败")}catch(e){console.error("获取文件信息失败",e),this.$showToast("文件预览失败"),setTimeout(()=>{this.$closeToast()},100)}else this.$showToast("暂无详细内容")}}},I={class:"list-item"},N={class:"item-container"},P={class:"container-left"},V={class:"title-wrapper"},k={class:"info-wrapper"},A={class:"aside"},E={class:"value"};function S(s,e,t,i,r,a){const l=U("UploadFiles");return v(),w("div",I,[o("div",N,[o("div",P,[F(l,{ref:"fileUpload",g9s:t.item.thumbnailFileUpload,"onUpdate:g9s":e[0]||(e[0]=n=>t.item.thumbnailFileUpload=n),readonly:""},null,8,["g9s"])]),o("div",{class:"container-right",onClick:e[1]||(e[1]=$(n=>a.goDetail(),["stop","prevent"]))},[o("div",V,c(t.item.name),1),o("div",k,[o("div",A,[o("span",E,c(s.$dayjs(t.item.newsTime).format("YYYY-MM-DD")),1)])])])])])}const X=y(T,[["render",S],["__scopeId","data-v-41e089b5"]]),Y="general-news";function j(s){return b({url:"".concat(D.VUE_APP_BASE_API_SERVICENAME,"/").concat(Y,"/page"),method:"get",params:s})}export{X as L,j as g};
