import{h as e}from"./index-4829f8e2.js";const a="/microdamcodeservice/api";function d(t){return e({url:"".concat(a,"/dams/code/projects"),method:"get",params:t})}function s(t){return e({url:"".concat(a,"/dams/").concat(t,"/code/projectInfo"),method:"get"})}function c({id:t,projectDamId:o,projectInstrId:r}){return e({url:"".concat(a,"/dam/").concat(o,"/findCodeInfoList"),method:"get",params:{params:encodeURI(JSON.stringify({codeStatus:null,isImportant:null,autoType:"",type:1})),projectId:t,instrId:r}})}function i(t){return e({url:"".concat(a,"/monitorData/code/allValue"),method:"get",params:t})}function l(t){return e({url:"".concat(a,"/dam/processLine"),method:"get",params:t})}function m({id:t,projectDamId:o,projectInstrId:r},n){return e({url:"".concat(a,"/monitorData/code/initValueEntry"),method:"get",params:{projectId:t,damId:o,valueIsAuto:0,time:n,instrId:r}})}function p(t){return e({url:"".concat(a,"/monitorData/code/calcValueRecordList"),method:"post",data:{valueRecords:t}})}function f(t,o){return e({url:"".concat(a,"/monitorData/code/entryCodeValues"),method:"post",params:{id:t},data:o})}export{m as a,s as b,p as c,c as d,l as e,i as f,d as g,f as s};
