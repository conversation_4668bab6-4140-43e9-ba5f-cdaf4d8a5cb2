System.register(["./index-legacy-09188690.js"],(function(e,t){"use strict";var n,r;return{setters:[e=>{n=e.h,r=e.A}],execute:function(){e({a:function(e){return n({url:t+"/device-info/tree/count3",method:"get",params:e})},b:function(e,r){return n({url:t+"/device-info/video/get/"+(e||0)+"/"+(r||0),method:"get"})},c:function(e){return n({url:t+"/device-info/video/get?deviceIndexCode="+e,method:"get"})},d:function(e){return n({url:t+"/device-collect",method:"delete",data:e,params:e})},e:function(e){return n({url:t+"/device-collect",method:"post",data:e})},g:function(e){return n({url:t+"/device-info/page",method:"get",params:e})},t:function(e){return n({url:t+"/device-info/tree/count2",method:"get",params:e})}});let t=r.VUE_APP_SCS_API_SERVICENAME}}}));
