import"./index-4829f8e2.js";function i(t){const s=["png","jpg","jpeg","bmp","gif","svg"];if(t){let e=t.substr(t.lastIndexOf(".")+1);return s.indexOf(e.toLowerCase())!==-1}else return!1}function a(t){const s=["mp4","3gp","webm","mov"];if(t){let e=t.substr(t.lastIndexOf(".")+1);return s.indexOf(e.toLowerCase())!==-1}else return!1}function f(t){const s=["pdf"];if(t){let e=t.substr(t.lastIndexOf(".")+1);return s.indexOf(e.toLowerCase())!==-1}else return!1}function l(t){const s=["docx","doc"];if(t){let e=t.substr(t.lastIndexOf(".")+1);return s.indexOf(e.toLowerCase())!==-1}else return!1}function x(t){const s=["xlsx","xls"];if(t){let e=t.substr(t.lastIndexOf(".")+1);return s.indexOf(e.toLowerCase())!==-1}else return!1}function c(t,s=2){if(Number.isNaN(Number(t))||Number(t)<=0)return 0;const e=["B","KB","MB","GB","TB","PB"];let n=0,r=Number(t);for(n;r>1024;n++)r/=1024;return parseFloat(r.toFixed(s))+e[n]}function d(t){if(!t)return"";const s="/api";return"".concat(s,"/sys-storage/download_image?f8s=").concat(t)}function g(t,s){let e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),n=[],r;if(s=s||e.length,t)for(r=0;r<t;r++)n[r]=e[0|Math.random()*s];else{let o;for(n[8]=n[13]=n[18]=n[23]="-",n[14]="4",r=0;r<36;r++)n[r]||(o=0|Math.random()*16,n[r]=e[r==19?o&3|8:o])}return n.join("")}export{a,f as b,l as c,x as d,c as e,d as g,i,g as u};
