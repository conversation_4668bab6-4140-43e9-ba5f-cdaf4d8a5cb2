System.register(["./index-legacy-09188690.js"],(function(t,e){"use strict";var r;return{setters:[t=>{r=t.h}],execute:function(){function e(t,e){return r({url:t,method:"post",data:e})}t({a:function(t){return r({method:"get",url:"/sys-bpm/model",params:t})},b:function(t){return r({method:"get",url:"/sys-bpm/process/history/instance",params:t})},c:function(t){return r({method:"post",url:"/sys-bpm/circulation",data:t})},d:function(t,s,n){return(o=s,r({method:"delete",url:"/sys-bpm/process/delete/bizId",params:{bizId:o}})).then((r=>e(t+"/form/delete",{...n,id:s})));var o},e:function(t,e){return r({url:t,method:"post",data:e})},f:function(t,e){return r({url:"/sys-bpm/process/complete",method:"put",params:t,data:e})},g:function(t){return r({url:"/sys-bpm/process/history",method:"get",params:t})},h:function(t){return r({method:"delete",url:"/sys-bpm/process/",params:t,data:t})},i:function(t){return r({method:"put",url:"/sys-bpm/process/reject",params:t})},j:function(t){return r({url:"/sys-bpm/process/button",method:"get",params:t})},p:function(t,e){return r({url:"/sys-bpm/process/start",method:"post",params:t,data:e})},s:e,u:function(t){return r({url:"/cybereng-technology/common/updateexecutor",method:"put",data:t})}})}}}));
