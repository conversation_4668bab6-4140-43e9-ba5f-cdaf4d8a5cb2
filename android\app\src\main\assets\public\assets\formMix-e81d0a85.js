import{e as f}from"./form-a8596e72.js";import{b as h}from"./validate-2249584f.js";const l={data(){return{bpmPersonObj:{approverConfigList:[],ccUserConfigList:[],approverParamList:[],ccParamList:[],initiatorInfo:null},detailParamList:[]}},props:{isCustomeInit:{type:Boolean,default:!1},excludeProps:{type:Array,default:()=>[]}},computed:{bizId(){var e,t;return((e=this.$route.query)==null?void 0:e.bizId)||((t=this.$route.params)==null?void 0:t.bizId)||""},taskId(){var e,t;return((e=this.$route.query)==null?void 0:e.taskId)||((t=this.$route.params)==null?void 0:t.taskId)||""},processInstanceId(){var e,t;return((e=this.$route.query)==null?void 0:e.processInstanceId)||((t=this.$route.params)==null?void 0:t.processInstanceId)||""},type(){var e,t;return((e=this.$route.query)==null?void 0:e.type)||((t=this.$route.params)==null?void 0:t.type)||"add"},taskKey(){var e,t;return((e=this.$route.query)==null?void 0:e.taskKey)||((t=this.$route.params)==null?void 0:t.taskKey)||""},approverConfigList(){return this.bpmPersonObj.approverConfigList||[]},ccUserConfigList(){return this.bpmPersonObj.ccUserConfigList||[]},approverParamList(){return this.bpmPersonObj.approverParamList||[]},ccParamList(){return this.bpmPersonObj.ccParamList||[]},initiatorInfo(){return this.bpmPersonObj.initiatorInfo},flowConfig(){let e={};(this.type==="view"||this.type==="execute")&&Object.keys(this.formData||{}).forEach(i=>{this.type==="execute"&&i==="notifyMethod"?e[i]="":e[i]="readonly"});const t=this.taskKey||"UserTask_0";return this.type==="execute"&&(t==="UserTask_0"||t==="fawkes_custom_flow_start")&&Object.keys(this.formData||{}).forEach(i=>{e[i]=""}),this.customConfig&&this.customConfig(e),e}},created(){!this.isCustomeInit&&this.getFormDataInit()},methods:{isVisibleTaskKey(e){const i=(this.allTaskKeys||[]).filter(s=>s.includes(e));for(let s=0;s<i.length;s++){const r=i[s],o=r.findIndex(a=>a===e),n=r.findIndex(a=>{var p;return a===(((p=this.formData)==null?void 0:p.taskKey)||this.taskKey)});return this.type==="view"?n>o:n>=o}return!1},isEnableTaskKey(e){return this.isVisibleTaskKey(e)},async getFormDataInit(){var e,t;try{this.$showToast({type:"loading",loadingType:"spinner",message:"加载中...",forbidClick:!0}),this.beforeGetFormData&&await((e=this.beforeGetFormData)==null?void 0:e.call(this)),this.bizId&&this.bizId!=="0"?await this._getFormDataWithBizId():await this._getFormDataForNew(),this.afterGetFormData&&await((t=this.afterGetFormData)==null?void 0:t.call(this))}catch(i){}finally{this.$closeToast()}},async _getFormDataWithBizId(){const e={entityName:this.entityName,taskKey:this.taskKey,id:this.bizId,type:this.type,detailEntityNameList:this.detailEntityNameList||[]},t=await f(this.service.query,e);if(t){let i=(t==null?void 0:t.entityObject)||{};i.notifyMethod?h(i.notifyMethod)&&(i.notifyMethod=JSON.parse(i.notifyMethod)):i.notifyMethod=[0],t.entityObject=i;const s=this.beforeInit?await this.beforeInit(t):null;let r={approverConfigList:(t==null?void 0:t.approverConfigList)||[],ccUserConfigList:(t==null?void 0:t.ccUserConfigList)||[],approverParamList:(t==null?void 0:t.approverParamList)||[],ccParamList:this.type==="view"?(t==null?void 0:t.ccParamList)||[]:[],initiatorInfo:t==null?void 0:t.initiatorInfo};this.excludeProps&&this.excludeProps.length>0&&(r.approverConfigList=r.approverConfigList.filter(o=>this.excludeProps.indexOf(o.prop)<0)),s&&s.processState===3&&(r.approverConfigList=r.approverConfigList.map(o=>{let n=r.approverParamList.find(a=>a.prop===o.prop);return{...o,...n}})),r=this.beforeSetBpmPerson?await this.beforeSetBpmPerson(r,s||i,t):r,this.bpmPersonObj=r,console.log("this.bpmPersonObj===> ",this.bpmPersonObj),s?this.formData=s:(this.formData=i,this.detailParamList=(t==null?void 0:t.detailParamList)||[])}},async _getFormDataForNew(){const e={entityName:this.entityName,detailEntityNameList:this.detailEntityNameList||[],taskKey:this.taskKey,type:this.type},t=await f(this.service.query,e);if(t){let i={approverConfigList:(t==null?void 0:t.approverConfigList)||[],ccUserConfigList:(t==null?void 0:t.ccUserConfigList)||[]};this.excludeProps&&this.excludeProps.length>0&&(i.approverConfigList=i.approverConfigList.filter(s=>this.excludeProps.indexOf(s.prop)<0)),i=this.beforeSetBpmPerson?await this.beforeSetBpmPerson(i,{},t):i,this.bpmPersonObj=i}},setBpmPerson(e){e&&Object.keys(e).forEach(t=>{this.bpmPersonObj[t]=e[t]})}},beforeDestroy(){this.detailParamList=[],this.bpmPersonObj={approverConfigList:[],ccUserConfigList:[],approverParamList:[],ccParamList:[],initiatorInfo:null}}};export{l as f};
