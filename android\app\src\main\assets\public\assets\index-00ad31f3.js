import{h as N,_ as T}from"./index-4829f8e2.js";import{F as P}from"./index-8d635ba7.js";import{F as U}from"./FormItemPicker-d3f69283.js";import{Q as f,R as v,S as _,k as m,V as p,F as g,W as C,X as b,B as y,Y as i,a2 as k,U as t,Z as Y}from"./verder-361ae6c7.js";import{U as I}from"./index-fc22947f.js";import"./vant-91101745.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";function S(s){return N({url:"/cybereng-technology/common/getProcessBaseData",method:"get",params:s})}function O(s){return N({url:"/fawkes-ext/bpmConfig/process/getApprover/".concat(s.processKey),method:"post",data:s})}const V={name:"CheckInput",components:{},emits:["update:value"],model:{prop:"value",event:"update:value"},props:{value:{type:Array,default:()=>[]},options:{type:Array,default:()=>[]},readonly:{type:Boolean,default:!1},clearUnchecked:{type:Boolean,default:!0}},setup(s,{attrs:e,slots:n,emit:d}){},data(){return{showInput:!1,inputValue:"",extOptions:[]}},computed:{checked:{get(){return this.value||[]},set(s){this.$emit("update:value",s)}},checkedOptions(){return[...this.options,...this.extOptions].filter(s=>this.checked.includes(s))}},watch:{},created(){},mounted(){},methods:{openInput(){this.inputValue="",this.showInput=!0},addOption(){if(this.readonly){this.onCancel();return}const s=this.inputValue.trim();if(s!=""){if(this.extOptions.includes(s)||this.options.includes(s)){this.$showToast("已存在该选项");return}this.extOptions.push(s),this.checked.push(s),this.onCancel()}},onCancel(){this.showInput=!1,this.inputValue=""}}},A={class:"check-input"},L={key:0,class:"action-wp"},M={class:"add-input-action"};function B(s,e,n,d,a,o){const h=f("van-checkbox"),r=f("van-checkbox-group"),u=f("van-button"),D=f("van-search");return v(),_("div",A,[m(r,{modelValue:o.checked,"onUpdate:modelValue":e[0]||(e[0]=l=>o.checked=l),class:"checkbox-group",shape:"square",disabled:n.readonly},{default:p(()=>[n.readonly&&n.clearUnchecked?(v(!0),_(g,{key:0},C([...o.checkedOptions],l=>(v(),b(h,{key:l,name:l,value:l,class:"checkbox"},{default:p(()=>[y(i(l),1)]),_:2},1032,["name","value"]))),128)):(v(!0),_(g,{key:1},C([...n.options,...a.extOptions],l=>(v(),b(h,{key:l,name:l,value:l,class:"checkbox"},{default:p(()=>[y(i(l),1)]),_:2},1032,["name","value"]))),128))]),_:1},8,["modelValue","disabled"]),n.readonly?Y("",!0):(v(),_("div",L,[a.showInput?(v(),b(D,{key:1,modelValue:a.inputValue,"onUpdate:modelValue":e[4]||(e[4]=l=>a.inputValue=l),placeholder:"请新增选项","input-align":"left","left-icon":"","show-action":"",onSearch:o.addOption,onCancel:o.onCancel,class:"add-input-wp"},{action:p(()=>[t("div",M,[m(u,{class:"btn",size:"small",onClick:e[2]||(e[2]=k(l=>o.onCancel(),["stop","prevent"]))},{default:p(()=>e[6]||(e[6]=[y("取消")])),_:1,__:[6]}),m(u,{class:"btn",type:"primary",size:"small",onClick:e[3]||(e[3]=k(l=>o.addOption(),["stop","prevent"]))},{default:p(()=>e[7]||(e[7]=[y("新增")])),_:1,__:[7]})])]),_:1},8,["modelValue","onSearch","onCancel"])):(v(),b(u,{key:0,class:"btn",size:"small",onClick:e[1]||(e[1]=k(l=>o.openInput(),["stop","prevent"]))},{default:p(()=>e[5]||(e[5]=[y("新增")])),_:1,__:[5]}))]))])}const H=T(V,[["render",B],["__scopeId","data-v-b303c28c"]]);const E={name:"ConstructionTechnicalScheme",components:{FlowForm:P,FormItemPicker:U,CheckInput:H,UploadFiles:I},props:{},emits:[],setup(s,{attrs:e,slots:n,emit:d}){},data(){var s,e;return{type:((s=this.$route.query)==null?void 0:s.type)||"",taskKey:((e=this.$route.query)==null?void 0:e.taskKey)||"",entityName:this.$route.name==="ConstructionTechnicalSchemeBranch"?"TechnologyFilesBranch":"TechnologyFiles",formKey:this.$route.name==="ConstructionTechnicalSchemeBranch"?"ConstructionTechnicalSchemeBranch":"ConstructionTechnicalScheme",modelKey:this.$route.name==="ConstructionTechnicalSchemeBranch"?"technology_CB01_branch_process":"technology_CB01",service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/form/commit"},detailEntityNameList:["TechnologyConstructionSubmit"],detailParamList:[],formData:{id:void 0,portalId:void 0,sendingName:"",sendingCode:"",sendingType:"施工技术方案申报表",sendingTypeCode:"00_02_01",modelType:"施工文件",prjDepCode:"",prjDepName:"",subProjectId:"",subProjectCode:"",subProjectName:"",createBy:"",userFullname:"",userTelephoneNum:"",annualDate:this.$dayjs().format("YYYY"),epcName:"",titleCode:"",constructionName:"",contractCode:"",supervisionName:"",projectName:"",schemeType:[],constructionDeptName:"",comment2:"",epcDeptName:"",comment3:"",supervisionDeptName:"",approverUsername1:"",approverFullname1:"",approverUnit1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",approverUsername3:"",approverFullname3:"",approverUnit3:"",approverUsername4:"",approverFullname4:"",approverUnit4:"",approverUsername5:"",approverFullname5:"",approverUnit5:"",approverUsername6:"",approverFullname6:"",approverUnit6:"",updateDate:this.$dayjs().format("YYYY-MM-DD HH:mm:ss"),filingPath:"",projectNameCode:"",projectName:"",notifyMethod:"[]",processState:"0",routerPath:"ConstructionTechnicalScheme",num1:4,num2:1,num3:1,num4:1,num5:1,isconfirm1:!0,otherAttachment:"",time1:"",time2:"",time3:"",time4:"",time5:"",time6:""},checkOptions:["施工组织设计","施工措施计划","专项施工方案","度汛方案","灾害应急预案","施工工艺试验方案","专项检测试验方案","工程测量施测计划和方案","制度体系","变更实施方案"],accept:".jpg,.jpeg,.png,.JPG,.JPEG,.dwg,.DWG,.rar,.RAR,.zip,.ZIP,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx",baseData:{}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},baseObj(){var s;return this.formData.subProjectId?((s=this.baseData)==null?void 0:s[this.formData.subProjectId])||{}:{}},noAdd(){return this.type=="view"?!0:this.type=="add"?!1:this.taskKey!="UserTask_0"}},watch:{"formData.annualDate":{immediate:!0,handler(s){let e=s||this.$dayjs().format("YYYY");this.formData.filingPath="\\施工文件\\文件汇总\\".concat(e,"年度\\").concat(this.formData.sendingType||"施工技术方案申报表")}}},created(){},mounted(){this.initForm(),this.getBaseData()},methods:{async getBaseData(){try{const s={subProjectId:"",sendingType:this.formData.sendingType},e=await S(s);this.baseData=e||{}}catch(s){console.log(s)}},async initForm(){if(this.type==="add"){if(this.formData.portalId=this.portalId,this.portal.type!=1){const r=this.subProjectList.find(u=>u.portalId==this.portal.id);this.formData.subProjectId=r==null?void 0:r.id,this.formData.subProjectName=r==null?void 0:r.nodeName,this.formData.subProjectCode=r==null?void 0:r.nodeCode}const{userName:s="",userFullname:e="",phone:n,orgList:d=[]}=this.user||{},a=d.find(r=>{var u;return r.portalId==((u=this.portal)==null?void 0:u.id)})||d[0],o=(a==null?void 0:a.name)||"",h=(a==null?void 0:a.orgNo)||"";this.formData.createBy=s,this.formData.userFullname=e,this.formData.userTelephoneNum=n,this.formData.prjDepName=o,this.formData.prjDepCode=h}else this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,s=>{var a,o,h,r;const{detailParamList:e=[],entityObject:n}=s;this.detailParamList=e;let d=((r=(h=(o=(a=e[0])==null?void 0:a.detailEntityArray)==null?void 0:o[0])==null?void 0:h.schemeType)==null?void 0:r.split(","))||[];d=d.filter(u=>u!==null&&typeof u<"u"&&u!==""),this.formData={...this.formData,...n,schemeType:d},this.checkOptions=this.unique(this.checkOptions.concat(this.formData.schemeType))})})},unique(s){const e=new Map;return s.filter(n=>!e.has(n)&&e.set(n,1))},updateDetailParamList(){var e,n,d;const s=((d=(n=(e=this.detailParamList)==null?void 0:e[0])==null?void 0:n.detailEntityArray)==null?void 0:d[0])||{};s.schemeType=this.formData.schemeType.join(","),this.detailParamList=[{detailEntityName:"TechnologyConstructionSubmit",detailEntityArray:[{...s}]}]},async onDraft(){try{const s={...this.formData,rectifyState:"暂存"};this.updateDetailParamList(),this.$refs.FlowForm.onSaveDraft(this.service.submit,s)}catch(s){console.log(s)}},async onSubmit(){try{switch(await this.$refs.form.validate(),this.taskKey){case"UserTask_0":break;case"UserTask_1":{this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_2":{this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_3":{this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_4":{this.formData.time4=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_5":{this.formData.time5=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_6":{this.formData.time6=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}}if(this.type==="add"&&this.$route.name==="ConstructionTechnicalScheme"){const e={...this.formData,processKey:this.modelKey},n=await O(e);this.formData={...this.formData,...n}}this.updateDetailParamList();const s={...this.formData};this.$refs.FlowForm.onSubmit(this.service.submit,s)}catch(s){console.log(s)}},async updateFiles(){return new Promise(async(s,e)=>{try{this.$refs.otherAttachment&&await this.$refs.otherAttachment.update(),s()}catch(n){e()}})},afterSubmit(s,e){this.updateFiles()},chengeSubProject(s){this.formData.subProjectCode=s.nodeCode,this.formData.constructionName=this.baseObj.constructionName[0],this.formData.epcName=this.baseObj.epcName[0],this.formData.contractCode=this.baseObj.contractCode[0],this.formData.supervisionName=this.baseObj.supervisionName[0],this.formData.projectName=this.baseObj.projectName[0],this.formData.constructionDeptName=this.baseObj.constructionDeptName[0],this.formData.epcDeptName=this.baseObj.epcDeptName[0],this.formData.supervisionDeptName=this.baseObj.supervisionDeptName[0]}}},q={class:"form-papper"},K={class:"form-title"},z={class:"archive"},R={class:"form-header"},G={class:"form-body"},J={class:"form-part"},W={class:"one-line"},Z={class:"form-info"},Q={class:"one-line indent"},X={class:"form-info"},$={class:"luokuan-wp"},ee={class:"lk-item"},te=["innerHTML"],se={class:"form-part"},ae={class:"comment-wp"},oe={class:"textarea-wp"},ne={class:"luokuan-wp"},re={class:"lk-item"},ie=["innerHTML"],le={class:"form-part"},me={class:"comment-wp"},pe={class:"textarea-wp"},ue={class:"luokuan-wp"},de={class:"lk-item"},ce=["innerHTML"],fe={class:"form-footer"},he={class:"footer-input"},ve={class:"footer-form"};function ye(s,e,n,d,a,o){const h=f("FormItemPicker"),r=f("van-cell-group"),u=f("CheckInput"),D=f("van-field"),l=f("van-radio"),j=f("van-radio-group"),w=f("UploadFiles"),x=f("van-form"),F=f("FlowForm");return v(),b(F,{ref:"FlowForm","model-key":a.modelKey,"form-key":a.formKey,"entity-name":a.entityName,"detail-param-list":a.detailParamList,"detail-entity-name-list":a.detailEntityNameList,onDraftClick:o.onDraft,onSubmitClick:o.onSubmit,onAfterSubmit:o.afterSubmit},{default:p(()=>[m(x,{ref:"form","label-width":"7em","input-align":"left","error-message-align":"right"},{default:p(()=>[m(r,{border:!1},{default:p(()=>[m(h,{label:"子工程",value:a.formData.subProjectId,"onUpdate:value":e[0]||(e[0]=c=>a.formData.subProjectId=c),text:a.formData.subProjectName,"onUpdate:text":e[1]||(e[1]=c=>a.formData.subProjectName=c),columns:[...o.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:o.portal.type!=1||o.noAdd,onChange:o.chengeSubProject},null,8,["value","text","columns","readonly","onChange"])]),_:1}),m(r,{border:!1},{default:p(()=>[t("div",q,[t("div",K,[e[12]||(e[12]=t("div",{class:"code"},"CB01",-1)),e[13]||(e[13]=t("div",{class:"name"},"施工技术方案报审表",-1)),t("div",z,[e[7]||(e[7]=t("span",null,"（ ",-1)),t("div",null,i(a.formData.constructionName),1),e[8]||(e[8]=t("span",null,"[ ",-1)),t("div",null,i(a.formData.annualDate),1),e[9]||(e[9]=t("span",null,"]",-1)),e[10]||(e[10]=t("span",null,"技案",-1)),t("div",null,i(a.formData.titleCode),1),e[11]||(e[11]=t("span",null,"号 ）",-1))])]),t("div",R,[t("div",null,[e[14]||(e[14]=t("span",null,"合同名称：",-1)),t("span",null,i(a.formData.epcName),1)]),t("div",null,[e[15]||(e[15]=t("span",null,"合同编号：",-1)),t("span",null,i(a.formData.contractCode),1)])]),t("div",G,[t("div",J,[t("div",W,[e[16]||(e[16]=t("span",null,"致：",-1)),t("span",Z,i(a.formData.supervisionName),1)]),t("div",Q,[e[17]||(e[17]=t("span",null,"我方今提交",-1)),t("span",X,i(a.formData.projectName),1),e[18]||(e[18]=t("span",null,"工程的：",-1))]),t("div",null,[m(u,{value:a.formData.schemeType,"onUpdate:value":e[2]||(e[2]=c=>a.formData.schemeType=c),options:a.checkOptions,readonly:o.noAdd},null,8,["value","options","readonly"])]),e[22]||(e[22]=t("div",{class:"one-line"},"请贵方审批。",-1)),t("div",$,[t("div",ee,[e[19]||(e[19]=t("span",{class:"label"},"施工项目部：",-1)),t("span",{class:"value",innerHTML:a.formData.constructionDeptName.replaceAll("\n","<br/>")},null,8,te)]),e[20]||(e[20]=t("div",{class:"lk-item"},[t("span",{class:"label"}," 施工负责人："),t("span",{class:"value"},i(""))],-1)),e[21]||(e[21]=t("div",{class:"lk-item"},[t("span",{class:"label"}," 日期："),t("span",{class:"value"},i(""))],-1))])]),t("div",se,[t("div",ae,[e[23]||(e[23]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",oe,[m(D,{modelValue:a.formData.comment2,"onUpdate:modelValue":e[3]||(e[3]=c=>a.formData.comment2=c),rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!0},null,8,["modelValue"])])]),t("div",ne,[t("div",re,[e[24]||(e[24]=t("span",{class:"label"},"EPC总承包项目部：",-1)),t("span",{class:"value",innerHTML:a.formData.epcDeptName.replaceAll("\n","<br/>")},null,8,ie)]),e[25]||(e[25]=t("div",{class:"lk-item"},[t("span",{class:"label"}," 总承包项目负责人："),t("span",{class:"value"},i(""))],-1)),e[26]||(e[26]=t("div",{class:"lk-item"},[t("span",{class:"label"}," 日期："),t("span",{class:"value"},i(""))],-1))])]),t("div",le,[t("div",me,[e[27]||(e[27]=t("div",null,"监理机构将另行签发审批意见：",-1)),t("div",pe,[m(D,{modelValue:a.formData.comment3,"onUpdate:modelValue":e[4]||(e[4]=c=>a.formData.comment3=c),rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!0},null,8,["modelValue"])])]),t("div",ue,[t("div",de,[e[28]||(e[28]=t("span",{class:"label"},"监理机构：",-1)),t("span",{class:"value",innerHTML:a.formData.supervisionDeptName.replaceAll("\n","<br/>")},null,8,ce)]),e[29]||(e[29]=t("div",{class:"lk-item"},[t("span",{class:"label"}," 签收人："),t("span",{class:"value"},i(""))],-1)),e[30]||(e[30]=t("div",{class:"lk-item"},[t("span",{class:"label"}," 日期："),t("span",{class:"value"},i(""))],-1))])])]),t("div",fe,[t("div",he,[e[31]||(e[31]=t("span",null,"说明：本表一式",-1)),t("span",null,i(a.formData.num1),1),e[32]||(e[32]=t("span",null,"份，由承包人填写，监理机构签收后，承包人",-1)),t("span",null,i(a.formData.num2),1),e[33]||(e[33]=t("span",null,"份，监理机构",-1)),t("span",null,i(a.formData.num3),1),e[34]||(e[34]=t("span",null,"份，发包人",-1)),t("span",null,i(a.formData.num4),1),e[35]||(e[35]=t("span",null,"份，设代机构",-1)),t("span",null,i(a.formData.num5),1),e[36]||(e[36]=t("span",null,"份",-1))])])])]),_:1}),m(r,{border:!1},{default:p(()=>[t("div",ve,[m(D,{name:"radio",label:"是否施工负责人确认：","label-width":"11em"},{input:p(()=>[m(j,{modelValue:a.formData.isconfirm1,"onUpdate:modelValue":e[5]||(e[5]=c=>a.formData.isconfirm1=c),direction:"horizontal",disabled:o.noAdd},{default:p(()=>[m(l,{name:!0},{default:p(()=>e[37]||(e[37]=[y("是")])),_:1,__:[37]}),m(l,{name:!1},{default:p(()=>e[38]||(e[38]=[y("否")])),_:1,__:[38]})]),_:1},8,["modelValue","disabled"])]),_:1}),m(D,{label:"附件上传：","label-align":"top","input-align":"left"},{input:p(()=>[m(w,{ref:"otherAttachment",g9s:a.formData.otherAttachment,"onUpdate:g9s":e[6]||(e[6]=c=>a.formData.otherAttachment=c),accept:a.accept,multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:o.noAdd},null,8,["g9s","accept","readonly"])]),_:1})])]),_:1})]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}const Ye=T(E,[["render",ye],["__scopeId","data-v-7fc69b09"]]);export{Ye as default};
