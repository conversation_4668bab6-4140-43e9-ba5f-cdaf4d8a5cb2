import{F as C,D as L}from"./index-1be3ad72.js";import{_ as T}from"./index-4829f8e2.js";import{Q as b,R as U,X as k,V as a,k as l,U as t,Y as n}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const B={name:"CB40",components:{FormTemplate:C,DocumentPart:L},emits:[],props:{},setup(f,{attrs:e,slots:c,emit:V}){},data(){return{detailTable:[],attachmentDesc:"1、交接项目清单。\n2、合同工程完工验收前临时交接项目清单（若有）。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:f,detailParamList:e}){},onBeforeSubmit({formData:f,detailParamList:e,taskComment3:c},V){return new Promise((m,r)=>{try{m()}catch(p){r(p)}})}}},O={class:"one-line"},F={class:"form-info"},z={class:"form-info"},A={class:"form-info"},D={class:"form-info"},W={class:"attachment-desc"},g={class:"comment-wp"},I={class:"textarea-wp"},h={class:"comment-wp"},E={class:"textarea-wp"},Q={class:"comment-wp"},R={class:"textarea-wp"},X={class:"footer-input"},Y={class:"form-info"},q={class:"form-info"},G={class:"form-info"},H={class:"form-info"};function J(f,e,c,V,m,r){const p=b("van-field"),u=b("DocumentPart"),w=b("FormTemplate");return U(),k(w,{ref:"FormTemplate",nature:"交接","on-after-init":r.onAfterInit,"on-before-submit":r.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!1,attachmentDesc:m.attachmentDesc},{default:a(({formData:o,formTable:N,baseObj:i,uploadAccept:x,taskStart:d,taskComment2:v,taskComment3:y,taskComment4:P,taskComment5:_})=>[l(u,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:o.constructionDeptName,deptOptions:i.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!d},{default:a(()=>[t("div",O,[e[0]||(e[0]=t("span",{style:{"padding-left":"2em"}},"合同工程已于",-1)),t("span",F,n(o.field1),1),e[1]||(e[1]=t("span",null,"年",-1)),t("span",z,n(o.field2),1),e[2]||(e[2]=t("span",null,"月",-1)),t("span",A,n(o.field3),1),e[3]||(e[3]=t("span",null,"日通过完工验收，",-1)),e[4]||(e[4]=t("span",null,"并形成了",-1)),t("span",D,n(o.field4),1),e[5]||(e[5]=t("span",null,"合同工程完工验收",-1)),e[6]||(e[6]=t("span",null,"鉴定书，",-1)),e[7]||(e[7]=t("span",null,"现提交交接申请，请贵方审批。",-1))]),t("div",W,[e[8]||(e[8]=t("div",null,"附件：",-1)),l(p,{modelValue:o.attachmentDesc,"onUpdate:modelValue":s=>o.attachmentDesc=s,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),l(u,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:i.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!d},{default:a(()=>[t("div",g,[e[9]||(e[9]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",I,[l(p,{modelValue:o.comment2,"onUpdate:modelValue":s=>o.comment2=s,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),l(u,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:i.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!d},{default:a(()=>[t("div",h,[e[10]||(e[10]=t("div",null,"监理机构意见：",-1)),t("div",E,[l(p,{modelValue:o.comment3,"onUpdate:modelValue":s=>o.comment3=s,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!y},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),l(u,{deptLabel:"发包人：",deptProp:"employerName",deptValue:o.employerName,deptOptions:i.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!d},{default:a(()=>[t("div",Q,[e[11]||(e[11]=t("div",null,"发包人意见：",-1)),t("div",R,[l(p,{modelValue:o.comment5,"onUpdate:modelValue":s=>o.comment5=s,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!_},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:a(({formData:o,formTable:N,baseObj:i,uploadAccept:x,taskStart:d,taskComment2:v,taskComment3:y,taskComment4:P,taskComment5:_})=>[t("div",X,[e[12]||(e[12]=t("span",null,"说明：1、本表一式",-1)),t("span",Y,n(o.num1),1),e[13]||(e[13]=t("span",null,"份，由承包人填写，监理机构、发包人审签后，发包人",-1)),t("span",q,n(o.num2),1),e[14]||(e[14]=t("span",null,"份，监理机构",-1)),t("span",G,n(o.num3),1),e[15]||(e[15]=t("span",null,"份，承包人",-1)),t("span",H,n(o.num4),1),e[16]||(e[16]=t("span",null,"份。",-1))]),e[17]||(e[17]=t("div",{class:"footer-input"},"2、发包人同意工程交接后，另行签发工程交接证书。",-1)),e[18]||(e[18]=t("div",{class:"footer-input"},"3、合同工程完工验收前临时交接的项目，可参照本表。",-1))]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ie=T(B,[["render",J]]);export{ie as default};
