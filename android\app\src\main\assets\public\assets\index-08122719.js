import{F}from"./index-8d635ba7.js";import{U as v}from"./index-fc22947f.js";import{_ as c}from"./index-4829f8e2.js";import{Q as d,R as y,X as D,V as s,k as o,S as g,F as b,W as h}from"./verder-361ae6c7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";const U={name:"NotifyDetail",components:{UploadFiles:v},props:{formData:{type:Object,default:()=>({})},type:{},showContentDescription:{type:Boolean,default:!0},readonly:{type:Boolean,default:!0}}};function N(n,e,l,p,a,u){const i=d("van-field"),f=d("UploadFiles"),r=d("van-cell-group");return y(),D(r,{border:!1},{default:s(()=>[o(i,{modelValue:l.formData.relavancePartName,"onUpdate:modelValue":e[0]||(e[0]=t=>l.formData.relavancePartName=t),label:"工程部位",readonly:l.readonly,required:""},null,8,["modelValue","readonly"]),o(i,{modelValue:l.formData.profession,"onUpdate:modelValue":e[1]||(e[1]=t=>l.formData.profession=t),label:"专业",readonly:l.readonly,required:""},null,8,["modelValue","readonly"]),o(i,{modelValue:l.formData.notifyOrderCode,"onUpdate:modelValue":e[2]||(e[2]=t=>l.formData.notifyOrderCode=t),label:"通知单编号",readonly:l.readonly,required:""},null,8,["modelValue","readonly"]),o(i,{modelValue:l.formData.notifyOrderName,"onUpdate:modelValue":e[3]||(e[3]=t=>l.formData.notifyOrderName=t),label:"通知单名称",readonly:l.readonly,required:""},null,8,["modelValue","readonly"]),o(i,{modelValue:l.formData.subProjectName,"onUpdate:modelValue":e[4]||(e[4]=t=>l.formData.subProjectName=t),label:"子工程",readonly:l.readonly,required:""},null,8,["modelValue","readonly"]),o(i,{label:"附件","label-align":"top","input-align":"left"},{input:s(()=>[o(f,{ref:"afterFiles",g9s:l.formData.attachment,"onUpdate:g9s":e[5]||(e[5]=t=>l.formData.attachment=t),readonly:!0},null,8,["g9s"])]),_:1})]),_:1})}const T=c(U,[["render",N]]),w={name:"DesignDispatchDrawingModifyTable",components:{},props:{formDetailTable:{type:Array,default:()=>[]},formData:{type:Object,default:()=>({})},type:{}}};function C(n,e,l,p,a,u){const i=d("van-field"),f=d("van-cell-group");return y(),D(f,{border:!1},{default:s(()=>[(y(!0),g(b,null,h(l.formDetailTable,r=>(y(),g(b,null,[o(i,{modelValue:r.originalFileName,"onUpdate:modelValue":t=>r.originalFileName=t,label:"原图名",readonly:""},null,8,["modelValue","onUpdate:modelValue"]),o(i,{modelValue:r.originalFileCode,"onUpdate:modelValue":t=>r.originalFileCode=t,label:"原图号",readonly:""},null,8,["modelValue","onUpdate:modelValue"]),o(i,{modelValue:r.newFileCode,"onUpdate:modelValue":t=>r.newFileCode=t,label:"新图号",readonly:""},null,8,["modelValue","onUpdate:modelValue"]),o(i,{label:"修改内容",modelValue:r.modifyContent,"onUpdate:modelValue":t=>r.modifyContent=t,rows:"4",autosize:"",type:"textarea","label-align":"top",maxlength:"500","input-align":"left",readonly:""},null,8,["modelValue","onUpdate:modelValue"])],64))),256))]),_:1})}const k=c(w,[["render",C]]),P={name:"TechnologyDesignDrawingModifyNotify",components:{FlowForm:F,NotifyDetail:T,DesignDispatchDrawingModifyTable:k},data(){var n,e;return{type:((n=this.$route.query)==null?void 0:n.type)||"",taskKey:((e=this.$route.query)==null?void 0:e.taskKey)||"",modelKey:"technology_design_drawing_modify_notify",entityName:"TechnologyCommonFile",detailEntityName:"TechnologyDesignEPCAudit",detailParamList:[],detailEntityNameList:["TechnologyDesignEPCAudit","TechnologyDesignDrawingModifyContent"],service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/design/form/commit"},formKey:"TechnologyDesignDrawingModifyNotify",formData:{formKey:"TechnologyDesignDrawingModifyNotify",portalId:"",subProjectName:"",subProjectId:null,modelType:"",sendingName:"",sendingCode:"",sendingType:"",sendingTypeCode:"",contentDescription:"",profession:"",relavancePartName:"",relavancePart:"",leadDesigner:"",leadDesignerFullname:"",userTelephoneNum:"",notifyOrderCode:"",notifyOrderName:"",isRelate:!0,modifyOrderCode:"",attachment:"",isconfirm1:!0,approverUsername1:"",approverFullname1:"",approverUnit1:"",time1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",time2:"",duplicateUsername1:"",duplicateFullname1:"",duplicateUsername2:"",duplicateFullname2:"",relatedBpmId:""},formDetailTable:[],detailId:""}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},canNotEdit(){return!0}},mounted(){this.initForm()},methods:{async initForm(){this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,n=>{const{detailParamList:e=[],entityObject:l}=n;this.detailParamList=e,this.detailId=e[0].detailEntityArray[0].id,this.formData={...this.formData,...e[0].detailEntityArray[0],...l},this.formDetailTable=e[1].detailEntityArray})})},async onDraft(){try{const n={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,n)}catch(n){console.log(n)}},async onSubmit(){try{if(["subProjectName","relavancePartName","profession","leadDesigner","notifyOrderCode","notifyOrderName","approverFullname1","approverFullname2"].some(p=>!this.formData[p]))return this.$showToast({message:"请登录数智建管系统完善表单"});if(!this.formDetailTable.length)return this.$showToast({message:"请登录数智建管系统完善表单"});await this.$refs.form.validate(),this.setTime();const l={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,l)}catch(n){console.log(n)}},setTime(){switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":{this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_2":{this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}}},async afterSubmit(){}}};function O(n,e,l,p,a,u){const i=d("notify-detail"),f=d("design-dispatch-drawing-modify-table"),r=d("van-field"),t=d("van-cell-group"),V=d("van-form"),_=d("FlowForm");return y(),D(_,{ref:"FlowForm","model-key":a.modelKey,"form-key":a.formKey,"entity-name":a.entityName,"detail-param-list":a.detailParamList,"detail-entity-name-list":a.detailEntityNameList,onDraftClick:u.onDraft,onSubmitClick:u.onSubmit,onAfterSubmit:u.afterSubmit},{default:s(()=>[o(V,{ref:"form","label-width":"8em","input-align":"right","error-message-align":"right"},{default:s(()=>[o(i,{"form-data":a.formData,type:a.type,readonly:""},null,8,["form-data","type"]),o(f,{"form-detail-table":a.formDetailTable,ref:"detailForm","form-data":a.formData,type:a.type},null,8,["form-detail-table","form-data","type"]),o(t,{border:!1},{default:s(()=>[o(r,{modelValue:a.formData.userFullname,"onUpdate:modelValue":e[0]||(e[0]=m=>a.formData.userFullname=m),label:"发起人",readonly:""},null,8,["modelValue"]),o(r,{modelValue:a.formData.prjDepName,"onUpdate:modelValue":e[1]||(e[1]=m=>a.formData.prjDepName=m),label:"发起人部门",readonly:""},null,8,["modelValue"]),o(r,{modelValue:a.formData.approverFullname1,"onUpdate:modelValue":e[2]||(e[2]=m=>a.formData.approverFullname1=m),label:"业主审核人",readonly:"",required:""},null,8,["modelValue"]),o(r,{modelValue:a.formData.approverUnit1,"onUpdate:modelValue":e[3]||(e[3]=m=>a.formData.approverUnit1=m),label:"审核部门",readonly:""},null,8,["modelValue"]),o(r,{modelValue:a.formData.approverFullname2,"onUpdate:modelValue":e[4]||(e[4]=m=>a.formData.approverFullname2=m),label:"签收人",readonly:"",required:""},null,8,["modelValue"]),o(r,{modelValue:a.formData.approverUnit2,"onUpdate:modelValue":e[5]||(e[5]=m=>a.formData.approverUnit2=m),label:"签收部门",readonly:""},null,8,["modelValue"]),o(r,{modelValue:a.formData.duplicateFullname1,"onUpdate:modelValue":e[6]||(e[6]=m=>a.formData.duplicateFullname1=m),label:"抄送至",readonly:""},null,8,["modelValue"])]),_:1})]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}const H=c(P,[["render",O]]);export{H as default};
