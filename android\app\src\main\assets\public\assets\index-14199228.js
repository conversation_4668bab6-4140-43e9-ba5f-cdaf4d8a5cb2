import{F as N,D as T}from"./index-a831f9da.js";import{_}from"./index-4829f8e2.js";import{Q as m,R as x,X as y,V as u,k as p,U as n,Y as t}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const P={name:"JL03",components:{FormTemplate:N,DocumentPart:T},emits:[],props:{},setup(l,{attrs:e,slots:r,emit:f}){},data(){return{detailTable:[],attachmentDesc:"批复意见"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:l,detailParamList:e}){},onBeforeSubmit({formData:l,detailParamList:e,taskStart:r},f){return new Promise((i,o)=>{try{if(f==="submit"&&r&&!l.check1&&!l.check2)return this.$showNotify({type:"danger",message:"请选择开工类型!",duration:3*1e3}),o(!1),!1;i()}catch(d){o(d)}})},changeCheck1(l){l&&(this.$refs.FormTemplate.formData.check2=!1)},changeCheck2(l){l&&(this.$refs.FormTemplate.formData.check1=!1)}}},F={class:"one-line"},L={class:"form-info"},B={class:"form-info"},q={class:"form-info"},A={class:"check-wp"},O={class:"check-wp"},S={class:"form-info"},I={class:"form-info"},W={class:"form-info"},z={class:"attachment-desc"},J={class:"comment-wp"},Q={class:"one-line"},R={class:"check-wp"},X={class:"check-wp"},Y={class:"footer-input"},E={class:"form-info"},G={class:"form-info"},H={class:"form-info"},K={class:"form-info"};function M(l,e,r,f,i,o){const d=m("van-checkbox"),b=m("van-field"),h=m("DocumentPart"),V=m("FormTemplate");return x(),y(V,{ref:"FormTemplate",nature:"分开工","on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":i.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:i.attachmentDesc},{default:u(({formData:s,formTable:C,baseObj:c,uploadAccept:g,taskStart:k,taskComment2:v,taskComment3:w,taskComment4:U})=>[p(h,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:c.supervisionDeptName,personLabel:"监理工程师：",labelWidth:"10em",disabled:!k},{default:u(()=>[n("div",F,[e[0]||(e[0]=n("span",{style:{"padding-left":"2em"}},"你方",-1)),n("span",L,t(s.field1),1),e[1]||(e[1]=n("span",null,"年",-1)),n("span",B,t(s.field2),1),e[2]||(e[2]=n("span",null,"月",-1)),n("span",q,t(s.field3),1),e[3]||(e[3]=n("span",null,"日报送的",-1)),n("div",A,[p(d,{modelValue:s.check1,"onUpdate:modelValue":a=>s.check1=a,shape:"square",disabled:!0,onChange:o.changeCheck1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e[4]||(e[4]=n("span",null,"分部工程",-1)),e[5]||(e[5]=n("span",null,"/",-1)),n("div",O,[p(d,{modelValue:s.check2,"onUpdate:modelValue":a=>s.check2=a,shape:"square",disabled:!0,onChange:o.changeCheck2},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e[6]||(e[6]=n("span",null,"分部工程部分工作",-1)),e[7]||(e[7]=n("span",null,"开工申请表",-1)),e[8]||(e[8]=n("span",null,"（ ",-1)),n("span",S,t(s.constructionName),1),e[9]||(e[9]=n("span",null," [ ",-1)),n("span",I,t(s.field4),1),e[10]||(e[10]=n("span",null," ] 分开工",-1)),n("span",W,t(s.field5),1),e[11]||(e[11]=n("span",null,"号）",-1)),e[12]||(e[12]=n("span",null,"已经通过审核，",-1)),e[13]||(e[13]=n("span",null,"同意开工。",-1))]),n("div",z,[e[14]||(e[14]=n("div",null,"附件：",-1)),p(b,{modelValue:s.attachmentDesc,"onUpdate:modelValue":a=>s.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!k},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),p(h,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:s.epcDeptName,deptOptions:c.epcDeptName,personLabel:"项目负责人：",labelWidth:"10em",disabled:!0},{default:u(()=>[n("div",J,[n("div",Q,[e[15]||(e[15]=n("span",null,"今已收到",-1)),n("div",R,[p(d,{modelValue:s.check1,"onUpdate:modelValue":a=>s.check1=a,shape:"square",disabled:!0,onChange:o.changeCheck1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e[16]||(e[16]=n("span",null,"分部工程",-1)),e[17]||(e[17]=n("span",null,"/",-1)),n("div",X,[p(d,{modelValue:s.check2,"onUpdate:modelValue":a=>s.check2=a,shape:"square",disabled:!0,onChange:o.changeCheck2},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e[18]||(e[18]=n("span",null,"分部工程部分工作",-1)),e[19]||(e[19]=n("span",null,"的开工批复。",-1))])])]),_:2},1032,["deptValue","deptOptions"])]),footer:u(({formData:s,formTable:C,baseObj:c,uploadAccept:g,taskStart:k,taskComment2:v,taskComment3:w,taskComment4:U})=>[n("div",Y,[e[20]||(e[20]=n("span",null,"说明：本表一式",-1)),n("span",E,t(s.num1),1),e[21]||(e[21]=n("span",null,"份，由监理机构填写。承包人签收后，承包人",-1)),n("span",G,t(s.num2),1),e[22]||(e[22]=n("span",null,"份，监理机构",-1)),n("span",H,t(s.num3),1),e[23]||(e[23]=n("span",null,"份，发包人",-1)),n("span",K,t(s.num4),1),e[24]||(e[24]=n("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ue=_(P,[["render",M]]);export{ue as default};
