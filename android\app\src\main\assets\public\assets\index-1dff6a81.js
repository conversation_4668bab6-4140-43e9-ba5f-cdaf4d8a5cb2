import{F as N,D as P}from"./index-a831f9da.js";import{_ as T}from"./index-4829f8e2.js";import{Q as u,R as c,X as x,V as m,k as f,U as t,S as D,W as F,F as B,Y as n}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const A={name:"JL08",components:{FormTemplate:N,DocumentPart:P},emits:[],props:{},setup(i,{attrs:e,slots:b,emit:r}){},data(){return{detailTable:[{},{},{},{},{}],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:i,detailParamList:e}){},onBeforeSubmit({formData:i,detailParamList:e},b){return new Promise((r,a)=>{try{r()}catch(l){a(l)}})}}},O={class:"form-table"},S={class:"center"},U={class:"attachment-desc"},W={class:"footer-input"},g={class:"form-info"},I={class:"form-info"},z={class:"form-info"},E={class:"form-info"};function J(i,e,b,r,a,l){const V=u("van-field"),_=u("DocumentPart"),k=u("FormTemplate");return c(),x(k,{ref:"FormTemplate",nature:"计通","on-after-init":l.onAfterInit,"on-before-submit":l.onBeforeSubmit,"detail-table":a.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:a.attachmentDesc},{default:m(({formData:o,formTable:v,baseObj:d,uploadAccept:y,taskStart:p,taskComment2:w,taskComment3:C,taskComment4:L})=>[f(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:d.supervisionDeptName,personLabel:"总监理工程师/监理工程师：",labelWidth:"10em",disabled:!p},{default:m(()=>[e[2]||(e[2]=t("div",{style:{"text-indent":"2em"}}," 依据合同约定，经发包人批准，现决定对下列工作按计日工予以安排，请据以执行。 ",-1)),t("div",O,[t("table",null,[e[0]||(e[0]=t("thead",null,[t("tr",null,[t("th",{colspan:"1",rowspan:"1"},"序号"),t("th",{colspan:"1",rowspan:"1"},"工作项目或内容"),t("th",{colspan:"1",rowspan:"1"},"计划工作时间"),t("th",{colspan:"1",rowspan:"1"},"计价及付款方式"),t("th",{colspan:"1",rowspan:"1"},"备注")])],-1)),t("tbody",null,[(c(!0),D(B,null,F(v||[],(s,h)=>(c(),D("tr",{key:h},[t("td",S,n(h+1),1),t("td",null,n(s.field1),1),t("td",null,n(s.field2),1),t("td",null,n(s.field3),1),t("td",null,n(s.field4),1)]))),128))])])]),t("div",U,[e[1]||(e[1]=t("div",null,"附件：",-1)),f(V,{modelValue:o.attachmentDesc,"onUpdate:modelValue":s=>o.attachmentDesc=s,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2,__:[2]},1032,["deptValue","deptOptions","disabled"]),f(_,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:d.epcDeptName,personLabel:"项目负责人：",labelWidth:"10em",disabled:!p},{default:m(()=>e[3]||(e[3]=[t("div",{class:"comment-wp"},[t("div",null,"我方将按通知执行。")],-1)])),_:2,__:[3]},1032,["deptValue","deptOptions","disabled"])]),footer:m(({formData:o,formTable:v,baseObj:d,uploadAccept:y,taskStart:p,taskComment2:w,taskComment3:C,taskComment4:L})=>[t("div",W,[e[4]||(e[4]=t("span",null,"说明：1、本表一式",-1)),t("span",g,n(o.num1),1),e[5]||(e[5]=t("span",null,"份，由监理机构填写，承包人签署后，承包人",-1)),t("span",I,n(o.num2),1),e[6]||(e[6]=t("span",null,"份，监理机构",-1)),t("span",z,n(o.num3),1),e[7]||(e[7]=t("span",null,"份，发包人",-1)),t("span",E,n(o.num4),1)]),e[8]||(e[8]=t("div",{class:"footer-input"},[t("div",{style:{"text-indent":"3em"}}," 2、本表计价及付款方式填写“按合同计日工单价支付”或“双方协商” ")],-1))]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const nt=T(A,[["render",J]]);export{nt as default};
