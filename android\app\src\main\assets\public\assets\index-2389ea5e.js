import{F as v,b as V}from"./index-8d635ba7.js";import{U as F}from"./index-fc22947f.js";import{_ as w}from"./FormItemSection-e3118d02.js";import{F as E}from"./FormItemDate-ba00d9d5.js";import{f as N}from"./formMix-e81d0a85.js";import{_ as I,A as c}from"./index-4829f8e2.js";import{c as S}from"./validate-2249584f.js";import{Q as l,R as u,X as p,V as m,k as s,B as y,Z as _}from"./verder-361ae6c7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";const C={name:"ProgressMilepost",components:{FlowForm:v,UploadFiles:F,FormItemSection:w,FormItemDate:E},mixins:[N],data(){var t,e;return{allTaskKeys:[["UserTask_0","UserTask_1","UserTask_2","UserTask_3","UserTask_4","UserTask_5","UserTask_6","UserTask_7","UserTask_8","UserTask_9","UserTask_10"],["UserTask_0","UserTask_1","UserTask_2_1","UserTask_3_1"]],type:((t=this.$route.query)==null?void 0:t.type)||"",taskKey:((e=this.$route.query)==null?void 0:e.taskKey)||"",modelKey:"milepost",entityName:"ProgressMilepost",service:{query:c.VUE_APP_BASE_API_SERVICENAME+"/form/query",submit:c.VUE_APP_BASE_API_SERVICENAME+"/form/commit"},formKey:"ProgressMilepost",formData:{formKey:"ProgressMilepost",portalId:"",subProjectName:"",subProjectId:null,modelType:"",sendingName:"",sendingCode:"",sendingType:"",sendingTypeCode:"",processName:"",processCode:"",sectionId:"",milepostName:"",realDate:"",notifyMethod:[0],fileUpload:null,remark:""}}},computed:{canEdit0(){return this.type==="add"||this.taskKey==="UserTask_0"}},methods:{async beforeSubmit(t,e){let n={tableName:"progress_milepost",fieldName:"process_code",fieldVal:this.formData.processCode,dataId:this.formData.id};if(await this.$bizStore.duplicateCheck(n)===0)return this.$showToast({message:"流程编号已存在！"}),!1;let{initiatorInfo:o}=e,a={...this.formData};return(this.type==="add"||t.code===V._Object.saveDraft.code)&&(a.startTime=o.approvalTime,a.submitterName=o.userFullName),a},async onDraft(){try{const t={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,t)}catch(t){console.log(t)}},async onSubmit(){try{if(["sectionId","processName","processCode","milepostName","realDate","notifyMethod","fileUpload"].some(d=>S(this.formData[d])))return this.$showToast({message:"请完善表单数据"});this.setTime();const n={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,n)}catch(t){console.log(t)}},setTime(){switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":{this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_2":{this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_3":{this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}}},async updateFiles(){return new Promise(async(t,e)=>{try{this.$refs.fileUpload&&await this.$refs.fileUpload.update(),t()}catch(n){e()}})},async afterSubmit(t,e){this.updateFiles()}}};function M(t,e,n,d,o,a){const i=l("van-field"),b=l("FormItemSection"),k=l("FormItemDate"),f=l("van-checkbox"),D=l("van-checkbox-group"),U=l("UploadFiles"),h=l("van-cell-group"),T=l("van-form"),g=l("FlowForm");return u(),p(g,{ref:"FlowForm","model-key":o.modelKey,"form-key":o.formKey,"entity-name":o.entityName,"all-task-keys":o.allTaskKeys,"before-submit":a.beforeSubmit,"bpm-person-obj":t.bpmPersonObj,"onUpdate:bpmPersonObj":e[8]||(e[8]=r=>t.bpmPersonObj=r),onDraftClick:a.onDraft,onSubmitClick:a.onSubmit,onAfterSubmit:a.afterSubmit},{default:m(()=>[s(T,{ref:"form","label-width":"8em","input-align":"right","error-message-align":"right"},{default:m(()=>[s(h,{border:!1},{default:m(()=>[s(i,{modelValue:o.formData.processName,"onUpdate:modelValue":e[0]||(e[0]=r=>o.formData.processName=r),label:"流程名称",readonly:o.type==="view"||!a.canEdit0,required:""},null,8,["modelValue","readonly"]),s(i,{modelValue:o.formData.processCode,"onUpdate:modelValue":e[1]||(e[1]=r=>o.formData.processCode=r),label:"流程编号",readonly:o.type==="view"||!a.canEdit0,required:""},null,8,["modelValue","readonly"]),s(b,{label:"所属标段",modelValue:o.formData.sectionId,"onUpdate:modelValue":e[2]||(e[2]=r=>o.formData.sectionId=r),readonly:"",required:""},null,8,["modelValue"]),s(i,{modelValue:o.formData.milepostName,"onUpdate:modelValue":e[3]||(e[3]=r=>o.formData.milepostName=r),label:"里程碑名称",readonly:o.type==="view"||!a.canEdit0,required:""},null,8,["modelValue","readonly"]),s(k,{label:"实际完成时间",value:o.formData.realDate,"onUpdate:value":e[4]||(e[4]=r=>o.formData.realDate=r),submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择"}],readonly:o.type==="view"||!a.canEdit0},null,8,["value","readonly"]),a.canEdit0?(u(),p(i,{key:0,name:"checkboxGroup",label:"通知方式",class:"check-box-color",required:"",rules:[{required:!0,message:"请选择"}]},{input:m(()=>[s(D,{modelValue:o.formData.notifyMethod,"onUpdate:modelValue":e[5]||(e[5]=r=>o.formData.notifyMethod=r),direction:"horizontal",shape:"square",disabled:o.type==="view"||!a.canEdit0},{default:m(()=>[s(f,{name:0},{default:m(()=>e[9]||(e[9]=[y("系统通知")])),_:1,__:[9]}),s(f,{name:1},{default:m(()=>e[10]||(e[10]=[y("短信通知")])),_:1,__:[10]})]),_:1},8,["modelValue","disabled"])]),_:1})):_("",!0),o.formData.fileUpload||a.canEdit0?(u(),p(i,{key:1,label:"上传附件","label-align":"top","input-align":"left",required:""},{input:m(()=>[s(U,{ref:"fileUpload",g9s:o.formData.fileUpload,"onUpdate:g9s":e[6]||(e[6]=r=>o.formData.fileUpload=r),accept:"*",multiple:!0,readonly:o.type==="view"||!a.canEdit0},null,8,["g9s","readonly"])]),_:1})):_("",!0),s(i,{label:"备注",modelValue:o.formData.remark,"onUpdate:modelValue":e[7]||(e[7]=r=>o.formData.remark=r),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入备注",required:!1,"input-align":"left",readonly:o.type==="view"||!a.canEdit0},null,8,["modelValue","readonly"])]),_:1})]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","all-task-keys","before-submit","bpm-person-obj","onDraftClick","onSubmitClick","onAfterSubmit"])}const L=I(C,[["render",M],["__scopeId","data-v-26452589"]]);export{L as default};
