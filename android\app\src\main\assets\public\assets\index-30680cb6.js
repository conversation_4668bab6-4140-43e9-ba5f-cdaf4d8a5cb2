import{F as I}from"./index-8d635ba7.js";import{_ as C}from"./FormItemSection-e3118d02.js";import{F as P}from"./FormItemPicker-d3f69283.js";import{F as T}from"./FormItemDate-ba00d9d5.js";import{F as V}from"./FormItemCalendar-905fde75.js";import{F as q}from"./FormItemCascader-c665b251.js";import{F as S}from"./FormItemPerson-bd0e3e57.js";import{F as E}from"./FormItemCoord-9e82e1bf.js";import{U as N}from"./index-fc22947f.js";import{_ as L,A as h}from"./index-4829f8e2.js";import{g as x}from"./wbsUtil-3e809cfd.js";import{f as A}from"./formMix-e81d0a85.js";import{Q as s,R as d,X as p,V as l,k as a,B as D,Z as b}from"./verder-361ae6c7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./array-15ef8611.js";import"./validate-2249584f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";const j={name:"QualityInspectionCorrection",components:{FlowForm:I,FormItemSection:C,FormItemPicker:P,FormItemDate:T,FormItemCalendar:V,FormItemPerson:S,FormItemCoord:E,UploadFiles:N,FormItemCascader:q},mixins:[A],data(){var o,t;return{allTaskKeys:[["UserTask_0","UserTask_1","UserTask_2","UserTask_3","UserTask_4"],["UserTask_0","UserTask_1"]],type:((o=this.$route.query)==null?void 0:o.type)||"",taskKey:((t=this.$route.query)==null?void 0:t.taskKey)||"",entityName:"QualityInspectionCorrection",formKey:"QualityInspectionCorrection",modelKey:"ZLJC-WTZG002",detailParamList:[],detailEntityNameList:[],service:{submit:h.VUE_APP_TCS_API_SERVICENAME+"/form/commit",query:h.VUE_APP_TCS_API_SERVICENAME+"/form/query"},formData:{sectionId:"",correctionNumber:"",projectPosition:"",constructionArea:"",deadline:"",description:"",requirement:"",fileUpload:"",measure:"",situation:"",measureDate:"",initiator:"",processName:"",processCode:"",measureFileUpload:"",isPass:"",positionInfo:null,lng:null,lat:null,height:null},wbsList:[],bpmPersonInfo:{},approverConfigList:[]}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},canEdit0(){return this.type==="add"||this.taskKey==="UserTask_0"},canEdit1(){return this.type==="execute"&&this.taskKey==="UserTask_1"},canEdit2(){return this.type==="execute"&&this.taskKey==="UserTask_2"},currentUser(){return this.$store.USER_INFO||{}}},watch:{},created(){},mounted(){},methods:{async beforeInit(o){let t=o.entityObject;return this.type==="execute"&&this.taskKey==="UserTask_1"&&(t.isPass=1),t},changeTgHander(){this.formData.isPass===1?this.bpmPersonInfo.approverConfigList=this.approverConfigList:this.bpmPersonInfo.approverConfigList=[],this.setBpmPerson(this.bpmPersonInfo)},async beforeSetBpmPerson(o,t,m){let{approverConfigList:f}=o;return this.type==="add"&&(o.initiatorInfo={type:0,prop:"initiator",taskKey:"UserTask_0",userName:this.currentUser.userName,userFullName:this.currentUser.userFullname,approvalTime:this.$dayjs(new Date).format("YYYY-MM-DD"),userPhone:this.currentUser.phone,userOrg:this.currentUser.orgList&&this.currentUser.orgList.length>0?[...new Set(this.currentUser.orgList.map(e=>e.name))].join(";"):"",label:"上报人",userPhoneLabel:"联系方式",approvalTimeLabel:"上报日期",userOrgLabel:"上报人所在部门"},this.formData.sectionId=this.$getPortalId()),this.getWbsList(),o.initiatorInfo.format="YYYY-MM-DD",this.bpmPersonInfo=JSON.parse(JSON.stringify(o)),this.approverConfigList=JSON.parse(JSON.stringify(f)),o},handleSection(){this.formData.projectPosition=null,this.getWbsList()},async getWbsList(){this.wbsList=await x(this.formData.sectionId,!0,this.portal),this.$nextTick(()=>{this.$refs.formItemCascaderRef.chengeLabel()})},validData(){return["sectionId","projectPosition","constructionArea","deadline","description","requirement","fileUpload"].some(m=>!this.formData[m])},async onDraft(){try{if(this.validData())return this.$showToast({message:"请完善表单数据"});this.doData();const o={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,o)}catch(o){console.log(o)}},async onSubmit(){try{if(this.validData())return this.$showToast({message:"请完善表单数据"});this.doData();const o={...this.formData};await this.$refs.FlowForm.onSubmit(this.service.submit,o)}catch(o){console.log(o)}},doData(){this.formData.reporter||(this.formData.reporter=this.currentUser.userName),this.formData.reporterName||(this.formData.reporterName=this.currentUser.userFullname),this.formData.reportDate||(this.formData.reportDate=this.$dayjs(new Date).format("YYYY-MM-DD HH:mm:ss")),this.formData.phone||(this.formData.phone=this.currentUser.phone)},async afterSubmit(o,t){this.updateFiles()},async updateFiles(){return new Promise(async(o,t)=>{try{this.$refs.fileUpload&&await this.$refs.fileUpload.update(),this.$refs.measureFileUpload&&await this.$refs.measureFileUpload.update(),o()}catch(m){t()}})}}};function K(o,t,m,f,e,i){const n=s("van-field"),U=s("FormItemSection"),F=s("FormItemCascader"),c=s("FormItemDate"),y=s("UploadFiles"),v=s("FormItemCoord"),u=s("van-cell-group"),g=s("van-radio"),w=s("van-radio-group"),k=s("van-form"),_=s("FlowForm");return d(),p(_,{ref:"FlowForm","model-key":e.modelKey,"form-key":e.formKey,"entity-name":e.entityName,"all-task-keys":e.allTaskKeys,"bpm-person-obj":o.bpmPersonObj,"onUpdate:bpmPersonObj":t[15]||(t[15]=r=>o.bpmPersonObj=r),onDraftClick:i.onDraft,onSubmitClick:i.onSubmit,onAfterSubmit:i.afterSubmit},{default:l(()=>[a(k,{ref:"form","label-width":"9em","input-align":"right","error-message-align":"right"},{default:l(()=>[a(u,{border:!1},{default:l(()=>[a(n,{modelValue:e.formData.correctionNumber,"onUpdate:modelValue":t[0]||(t[0]=r=>e.formData.correctionNumber=r),label:"整改单号",placeholder:"自动生成",readonly:""},null,8,["modelValue"]),a(U,{label:"所属标段",placeholder:"请选择",modelValue:e.formData.sectionId,"onUpdate:modelValue":t[1]||(t[1]=r=>e.formData.sectionId=r),readonly:e.type==="view"||!i.canEdit0,required:"",rules:[{required:!0,message:"请选择所属标段"}],onSelect:i.handleSection},null,8,["modelValue","readonly","onSelect"]),a(F,{ref:"formItemCascaderRef",label:"整改部位",value:e.formData.projectPosition,"onUpdate:value":t[2]||(t[2]=r=>e.formData.projectPosition=r),columns:[...e.wbsList],"columns-field-names":{text:"nodeName",value:"id",children:"children"},"trigger-change-mode":!0,placeholder:"请选择",required:"",rules:[{required:!0,message:"请选择整改部位"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","columns","readonly"]),a(c,{label:"整改期限",value:e.formData.deadline,"onUpdate:value":t[3]||(t[3]=r=>e.formData.deadline=r),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择整改期限"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),a(n,{label:"详细区域",modelValue:e.formData.constructionArea,"onUpdate:modelValue":t[4]||(t[4]=r=>e.formData.constructionArea=r),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"200","show-word-limit":"","input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),a(n,{label:"整改内容",modelValue:e.formData.description,"onUpdate:modelValue":t[5]||(t[5]=r=>e.formData.description=r),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改内容"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),a(n,{label:"整改要求及建议",modelValue:e.formData.requirement,"onUpdate:modelValue":t[6]||(t[6]=r=>e.formData.requirement=r),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改要求及建议"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),a(n,{label:"附件上传","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传资料附件"}]},{input:l(()=>[a(y,{ref:"fileUpload",g9s:e.formData.fileUpload,"onUpdate:g9s":t[7]||(t[7]=r=>e.formData.fileUpload=r),accept:"*",multiple:!0,readonly:e.type==="view"||!i.canEdit0},null,8,["g9s","readonly"])]),_:1}),a(v,{label:"经纬度",longitude:e.formData.lng,"onUpdate:longitude":t[8]||(t[8]=r=>e.formData.lng=r),latitude:e.formData.lat,"onUpdate:latitude":t[9]||(t[9]=r=>e.formData.lat=r),title:"选择定位",readonly:e.type==="view"||!i.canEdit0},null,8,["longitude","latitude","readonly"])]),_:1}),i.canEdit1?(d(),p(u,{key:0,border:!1},{default:l(()=>[a(n,{name:"isPass",label:"是否通过",required:"",rules:[{required:!0,message:"请选择是否通过"}]},{input:l(()=>[a(w,{modelValue:e.formData.isPass,"onUpdate:modelValue":t[10]||(t[10]=r=>e.formData.isPass=r),direction:"horizontal",onChange:i.changeTgHander},{default:l(()=>[a(g,{name:1},{default:l(()=>t[16]||(t[16]=[D("通过")])),_:1,__:[16]}),a(g,{name:0},{default:l(()=>t[17]||(t[17]=[D("不通过")])),_:1,__:[17]})]),_:1},8,["modelValue","onChange"])]),_:1})]),_:1})):b("",!0),e.formData.measureDate||e.formData.taskKey==="UserTask_2"?(d(),p(u,{key:1,border:!1},{default:l(()=>[a(n,{label:"整改措施",modelValue:e.formData.measure,"onUpdate:modelValue":t[11]||(t[11]=r=>e.formData.measure=r),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改措施"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),a(n,{label:"整改情况",modelValue:e.formData.situation,"onUpdate:modelValue":t[12]||(t[12]=r=>e.formData.situation=r),rows:"2",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入",maxlength:"500","show-word-limit":"",required:"",rules:[{required:!0,message:"请输入整改情况"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),a(c,{label:"整改完成日期",value:e.formData.measureDate,"onUpdate:value":t[13]||(t[13]=r=>e.formData.measureDate=r),placeholder:"请选择",submitTimeFormat:"00:00:00",required:"",rules:[{required:!0,message:"请选择整改完成日期"}],readonly:e.type==="view"||!i.canEdit2},null,8,["value","readonly"]),a(n,{label:"附件上传","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传附件"}]},{input:l(()=>[a(y,{ref:"measureFileUpload",g9s:e.formData.measureFileUpload,"onUpdate:g9s":t[14]||(t[14]=r=>e.formData.measureFileUpload=r),accept:"*",multiple:!0,readonly:e.type==="view"||!i.canEdit2},null,8,["g9s","readonly"])]),_:1})]),_:1})):b("",!0)]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","all-task-keys","bpm-person-obj","onDraftClick","onSubmitClick","onAfterSubmit"])}const me=L(j,[["render",K]]);export{me as default};
