import{h as P,_ as U}from"./index-4829f8e2.js";import{F as w}from"./index-8d635ba7.js";import{F as x}from"./FormItemPicker-d3f69283.js";import{U as L}from"./index-fc22947f.js";import{F as B}from"./FormItemPerson-bd0e3e57.js";import{Q as h,R as u,X as b,V as f,k as p,U as i,Y as d,a1 as C,S as k,F as S,B as F,y as O,Z as N,W as I,A as T}from"./verder-361ae6c7.js";function H(e){return P({url:"/cybereng-technology/common/getProcessBaseData",method:"get",params:e})}function E(e){return P({url:"/fawkes-ext/bpmConfig/process/getApprover/".concat(e.processKey),method:"post",data:e})}const K={name:"XXHTemplate",components:{FlowForm:w,FormItemPicker:x,UploadFiles:L,FormItemPerson:B},emits:[],props:{nature:String,isShowConfirm1:{type:Boolean,default:!1},isShowConfirm2:{type:Boolean,default:!1},isShowConfirm3:{type:Boolean,default:!1},nums:{type:Array,default:()=>[]},detailTable:{type:Array,default:()=>[]},showTarget:{type:Boolean,default:!0},customerHeader:Boolean,titleFill:Boolean,employerTarget:{type:Boolean,default:!1},noApprover:Boolean,hideBorder:Boolean,attachmentDesc:String,onAfterInit:{type:Function,default:()=>()=>{}},onBeforeSubmit:{type:Function,default:()=>Promise.reject()},onAfterSubmit:{type:Function,default:()=>Promise.resolve()}},setup(e,{attrs:t,slots:r,emit:n}){},data(){return{service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/form/commit"},detailEntityNameList:["TechnologyFilesExtend1","TechnologyFilesExtend2"],detailParamList:[],uploadAccept:".jpg,.jpeg,.png,.JPG,.JPEG,.dwg,.DWG,.rar,.RAR,.zip,.ZIP,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx",formData:{menuCode:"xxh",id:void 0,portalId:void 0,fileType:this.$route.query.formKey||this.$route.name,sendingType:this.$route.query.sendingType,sendingTypeCode:this.$route.query.sendingTypeCode,sendingName:"",sendingCode:"",modelType:"信息化文件",prjDepCode:"",prjDepName:"",subProjectId:"",subProjectCode:"",subProjectName:"",createBy:"",userFullname:"",userTelephoneNum:"",annualDate:this.$dayjs().format("YYYY"),titleCode:"",projectName:"",projectNameCode:"",contractCode:"",constructionName:"",constructionDeptName:"",supervisionName:"",supervisionDeptName:"",supervisionSimpleName:"",informationSimpleName:"",informationDeptName:"",epcName:"",epcDeptName:"",epcDeptNameLine:"",employerName:"",comment1:"",comment2:"",comment3:"",comment4:"",comment5:"",comment6:"",comment7:"",comment8:"",comment9:"",comment10:"",approverUsername1:"",approverFullname1:"",approverUnit1:"",approverUsername2:"",approverFullname2:"",approverUnit2:"",approverUsername3:"",approverFullname3:"",approverUnit3:"",approverUsername4:"",approverFullname4:"",approverUnit4:"",approverUsername5:"",approverFullname5:"",approverUnit5:"",approverUsername6:"",approverFullname6:"",approverUnit6:"",approverUsername7:"",approverFullname7:"",approverUnit7:"",approverUsername8:"",approverFullname8:"",approverUnit8:"",approverUsername9:"",approverFullname9:"",approverUnit9:"",approverUsername10:"",approverFullname10:"",approverUnit10:"",duplicateUsername1:"",duplicateUsername2:"",duplicateUsername3:"",duplicateUsername4:"",updateDate:this.$dayjs().format("YYYY-MM-DD HH:mm:ss"),filingPath:"",notifyMethod:"[]",processState:"0",routerPath:"",num1:this.nums[0]||"",num2:this.nums[1]||"",num3:this.nums[2]||"",num4:this.nums[3]||"",num5:this.nums[4]||"",num6:this.nums[5]||"",isconfirm1:this.isShowConfirm1,isconfirm2:this.isShowConfirm2,isconfirm3:this.isShowConfirm3,contentAttachment:"",otherAttachment:"",attachmentDesc:this.attachmentDesc||"",time1:"",time2:"",time3:"",time4:"",time5:"",time6:"",time7:"",time8:"",time9:"",time10:"",field1:void 0,field2:void 0,field3:void 0,field4:void 0,field5:void 0,field6:void 0,field7:void 0,field8:void 0,field9:void 0,field10:void 0,field11:void 0,field12:void 0,field13:void 0,field14:void 0,field15:void 0,field16:void 0,field17:void 0,field18:void 0,field19:void 0,field20:void 0,check1:void 0,check2:void 0,check3:void 0,check4:void 0,check5:void 0,check6:void 0,check7:void 0,check8:void 0,check9:void 0,check10:void 0,check11:void 0,check12:void 0,check13:void 0,check14:void 0,check15:void 0,check16:void 0,check17:void 0,check18:void 0,check19:void 0,check20:void 0},formTable:[...this.detailTable],baseData:{},approverLoading:!1,approverList:[]}},computed:{formKey(){return this.$route.name||""},modelKey(){const e=this.formKey.replace("Xxh","");return"technology_".concat(e,"_xxh")},entityName(){return"TechnologyFiles"},type(){var e;return((e=this.$route.query)==null?void 0:e.type)||"view"},taskKey(){var e;return((e=this.$route.query)==null?void 0:e.taskKey)||""},isBranch(){return this.formKey.includes("Branch")},taskStart(){return this.type=="view"||this.isBranch?!1:this.type=="add"?!0:this.type=="execute"&&this.taskKey=="UserTask_0"},taskComment1(){return this.type=="execute"&&(this.taskKey==="UserTask_1"||this.taskKey==="UserTask_2")},taskComment2(){return this.type=="execute"&&(this.taskKey==="UserTask_3"||this.taskKey==="UserTask_4")},taskComment3(){return this.type=="execute"&&(this.taskKey==="UserTask_5"||this.taskKey==="UserTask_9")},taskComment4(){return this.type=="execute"&&this.taskKey==="UserTask_6"},taskComment5(){return this.type=="execute"&&this.taskKey==="UserTask_7"},portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},baseObj(){var e;return this.formData.subProjectId?((e=this.baseData)==null?void 0:e[this.formData.subProjectId])||{}:{}},canApprover(){const{id:e,subProjectId:t}=this.formData||{};return e?!0:!!(this.modelKey&&t)}},watch:{"formData.comment1"(e){this.taskComment1&&this.setComment(e)},"formData.comment2"(e){this.taskComment2&&this.setComment(e)},"formData.comment3"(e){this.taskComment3&&this.setComment(e)},"formData.comment4"(e){this.taskComment4&&this.setComment(e)},"formData.comment5"(e){this.taskComment5&&this.setComment(e)},"formData.annualDate":{immediate:!0,handler(e){let t=e||this.$dayjs().format("YYYY");this.formData.filingPath="\\".concat(this.formData.modelType||"信息化文件","\\文件汇总\\").concat(t,"年度\\").concat(this.formData.sendingType||"信息化文件")}},"formData.constructionName":{immediate:!0,handler(e){this.canApprover?this.getProcessApprover():this.approverList=[]}}},created(){},mounted(){this.initForm()},methods:{async getBaseData(){try{const e={subProjectId:"",sendingType:this.formData.sendingType},t=await H(e);this.baseData=t||{}}catch(e){console.log(e)}},async initForm(){if(await this.getBaseData(),this.type==="add"){if(this.formData.portalId=this.portalId,this.portal.type!=1){const l=this.subProjectList.find(c=>c.portalId==this.portal.id);this.subProjectIdChange(l)}const{userName:e="",userFullname:t="",phone:r,orgList:n=[]}=this.user||{},a=n.find(l=>{var c;return l.portalId==((c=this.portal)==null?void 0:c.id)})||n[0],s=(a==null?void 0:a.name)||"",y=(a==null?void 0:a.orgNo)||"";this.formData.createBy=e,this.formData.userFullname=t,this.formData.userTelephoneNum=r,this.formData.prjDepName=s,this.formData.prjDepCode=y}else this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,e=>{var l,c;const{entityObject:t,detailParamList:r=[]}=e;this.detailParamList=r;const n=r.find(m=>m.detailEntityName==="TechnologyFilesExtend1")||{},a=(l=n==null?void 0:n.detailEntityArray)==null?void 0:l[0],s={};for(let m=1;m<=20;m++)s["check".concat(m)]=a==null?void 0:a["check".concat(m)],s["field".concat(m)]=a==null?void 0:a["field".concat(m)];this.formData={...this.formData,...s,...t};const y=(c=r.find(m=>m.detailEntityName==="TechnologyFilesExtend2"))==null?void 0:c.detailEntityArray;this.formTable=y||[],this.taskComment1&&t.comment1&&this.setComment(t.comment1),this.taskComment2&&t.comment2&&this.setComment(t.comment2),this.taskComment3&&t.comment3&&this.setComment(t.comment3),this.taskComment4&&t.comment4&&this.setComment(t.comment4),this.taskComment5&&t.comment5&&this.setComment(t.comment5),this.onAfterInit&&this.onAfterInit({formData:this.formData,detailParamList:r})})})},chengeSubProject(e){var t,r,n,a,s,y,l,c,m,D,v,_,g;this.formData.subProjectId=e.id,this.formData.subProjectName=e.nodeName,this.formData.subProjectCode=e.nodeCode,this.formData.contractCode=(t=this.baseObj.contractCode)==null?void 0:t[0],this.formData.projectName=(r=this.baseObj.projectName)==null?void 0:r[0],this.formData.constructionName=(n=this.baseObj.constructionName)==null?void 0:n[0],this.formData.constructionDeptName=(a=this.baseObj.constructionDeptName)==null?void 0:a[0],this.formData.epcName=(s=this.baseObj.epcName)==null?void 0:s[0],this.formData.epcDeptName=(y=this.baseObj.epcDeptName)==null?void 0:y[0],this.formData.epcDeptNameLine=(l=this.baseObj.epcDeptNameLine)==null?void 0:l[0],this.formData.supervisionName=(c=this.baseObj.supervisionName)==null?void 0:c[0],this.formData.supervisionDeptName=(m=this.baseObj.supervisionDeptName)==null?void 0:m[0],this.formData.supervisionSimpleName=(D=this.baseObj.supervisionSimpleName)==null?void 0:D[0],this.formData.employerName=(v=this.baseObj.employerName)==null?void 0:v[0],this.formData.informationSimpleName=(_=this.baseObj.informationSimpleName)==null?void 0:_[0],this.formData.informationDeptName=(g=this.baseObj.informationDeptName)==null?void 0:g[0]},handleConstructionNameChange(e){const r=(this.baseObj.constructionName||[]).findIndex(a=>a===e)||0,n=this.baseObj.constructionDeptName||[];this.formData.constructionDeptName=n[r]},handleSupervisionNameChange(e){const r=(this.baseObj.supervisionName||[]).findIndex(s=>s===e)||0,n=this.baseObj.supervisionDeptName||[],a=this.baseObj.supervisionSimpleName||[];this.formData.supervisionDeptName=n[r],this.formData.supervisionSimpleName=a[r]},handleInformationSimpleNameChange(e){const r=(this.baseObj.informationSimpleName||[]).findIndex(a=>a===e)||0,n=this.baseObj.informationDeptName||[];this.formData.informationDeptName=n[r]},async getProcessApprover(){if(!(this.isBranch||this.noApprover))try{this.approverLoading=!0;const e={...this.formData,formKey:this.formKey,processKey:this.modelKey},t=await E(e);this.approverList=t||[],this.taskStart&&t.forEach(r=>{this.formData[r.fieldName]=r.fieldValue,this.formData[r.fullnameFieldName]=r.fullNameFieldValue})}catch(e){console.log(e)}finally{this.approverLoading=!1}},updateDetailParamList(){var r,n;const e=[],t=((n=(r=this.detailParamList.find(a=>a.detailEntityName==="TechnologyFilesExtend1"))==null?void 0:r.detailEntityArray)==null?void 0:n[0])||{};for(let a=1;a<=20;a++)t["check".concat(a)]=this.formData["check".concat(a)],t["field".concat(a)]=this.formData["field".concat(a)];e.push({detailEntityName:"TechnologyFilesExtend1",detailEntityArray:[{...t}]}),e.push({detailEntityName:"TechnologyFilesExtend2",detailEntityArray:[...this.formTable]}),this.detailParamList=e},emitBeforeSubmit(e,t){return new Promise(async(r,n)=>{try{this.updateDetailParamList(),await this.onBeforeSubmit({formData:e,detailParamList:this.detailParamList,taskStart:this.taskStart,taskComment1:this.taskComment1,taskComment2:this.taskComment2,taskComment3:this.taskComment3,taskComment4:this.taskComment4,taskComment5:this.taskComment5},t),r()}catch(a){n(a)}})},async onDraft(){try{const e={...this.formData,rectifyState:"暂存"};await this.emitBeforeSubmit(e,"saveDraft"),this.$nextTick(()=>{this.$refs.FlowForm.onSaveDraft(this.service.submit,e)})}catch(e){console.log(e)}},async onSubmit(){try{try{await this.$refs.form.validate()}catch(t){this.$showNotify({type:"primary",message:"请完善表单内容!"});return}switch(this.taskKey){case"UserTask_0":break;case"UserTask_1":{this.formData.time1=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_2":{this.formData.time2=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_3":{this.formData.time3=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_4":{this.formData.time4=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_5":{this.formData.time5=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_6":{this.formData.time6=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_7":{this.formData.time7=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_8":{this.formData.time8=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_9":{this.formData.time9=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}case"UserTask_10":{this.formData.time10=this.$dayjs().format("YYYY-MM-DD HH:mm:ss");break}}if(!(this.isBranch||this.noApprover)){if(!this.canApprover)return this.$showToast({message:"该业务流程未选择审批人!"}),reject(!1),!1;if(!this.approverList||!this.approverList.length)return this.$showToast({message:"该业务流程未配置审批人!"}),reject(!1),!1}const e={...this.formData};await this.emitBeforeSubmit(e,"submit"),this.$nextTick(()=>{this.$refs.FlowForm.onSubmit(this.service.submit,e)})}catch(e){console.log(e)}},async onReject(){try{const e={...this.formData};await this.emitBeforeSubmit(e,"reject"),this.$nextTick(()=>{this.$refs.FlowForm.onBackSubmit(this.service.submit,e)})}catch(e){console.log(e)}},afterSubmit(e,t){this.updateFiles(),this.onAfterSubmit(e,t)},async updateFiles(){return new Promise(async(e,t)=>{try{this.$refs.otherAttachment&&await this.$refs.otherAttachment.update(),this.$refs.contentAttachment&&await this.$refs.contentAttachment.update(),e()}catch(r){t()}})},getComment(){var e,t;return(t=(e=this.$refs)==null?void 0:e.FlowForm)==null?void 0:t.getComment()},setComment(e){var t,r;(r=(t=this.$refs)==null?void 0:t.FlowForm)==null||r.setComment(e)},onCommentChange(e){this.taskComment1&&(this.formData.comment1=e),this.taskComment2&&(this.formData.comment2=e),this.taskComment3&&(this.formData.comment3=e),this.taskComment4&&(this.formData.comment4=e),this.taskComment5&&(this.formData.comment5=e)}}},M={class:"form-papper"},V={class:"form-title"},q={class:"code"},R={class:"name"},W={class:"archive"},z={key:0},X={key:1,class:"form-header"},G={style:{"margin-top":"5px"}},J={key:0,class:"form-target"},Z={class:"one-line"},Q={class:"form-info"},$={class:"form-content"},ee={class:"form-footer"},te={class:"footer-form"},ae={class:"person-picker"};function se(e,t,r,n,a,s){const y=h("FormItemPicker"),l=h("van-cell-group"),c=h("van-radio"),m=h("van-radio-group"),D=h("van-field"),v=h("UploadFiles"),_=h("FormItemPerson"),g=h("van-cell"),Y=h("van-form"),A=h("FlowForm");return u(),b(A,{ref:"FlowForm","model-key":s.modelKey,"form-key":s.formKey,"entity-name":s.entityName,"detail-param-list":a.detailParamList,"detail-entity-name-list":a.detailEntityNameList,onChangeComment:s.onCommentChange,onDraftClick:s.onDraft,onSubmitClick:s.onSubmit,onAfterSubmit:s.afterSubmit,onRejectClick:s.onReject},{default:f(()=>[p(Y,{ref:"form","label-width":"7em","input-align":"left","error-message-align":"right"},{default:f(()=>[p(l,{border:!1},{default:f(()=>[p(y,{label:"子工程","input-align":"right",value:a.formData.subProjectId,"onUpdate:value":t[0]||(t[0]=o=>a.formData.subProjectId=o),text:a.formData.subProjectName,"onUpdate:text":t[1]||(t[1]=o=>a.formData.subProjectName=o),columns:[...s.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:s.portal.type!=1||!s.taskStart,onChange:s.chengeSubProject},null,8,["value","text","columns","readonly","onChange"])]),_:1}),p(l,{border:!1},{default:f(()=>[i("div",M,[i("div",V,[i("div",q,d(a.formData.fileType),1),i("div",R,[r.titleFill?C(e.$slots,"title",{key:0,formData:a.formData,formTable:a.formTable,baseObj:s.baseObj,uploadAccept:a.uploadAccept,taskStart:s.taskStart,taskComment1:s.taskComment1,taskComment2:s.taskComment2,taskComment3:s.taskComment3,taskComment4:s.taskComment4,taskComment5:s.taskComment5},void 0,!0):(u(),k(S,{key:1},[F(d(a.formData.sendingType),1)],64))]),i("div",W,[t[4]||(t[4]=i("span",null,"（ ",-1)),i("div",null,d(a.formData.informationSimpleName),1),t[5]||(t[5]=i("span",null,"[ ",-1)),i("div",null,d(a.formData.annualDate),1),t[6]||(t[6]=i("span",null,"]",-1)),i("span",null,d(r.nature),1),i("div",null,d(a.formData.titleCode),1),t[7]||(t[7]=i("span",null,"号 ）",-1))])]),r.customerHeader?(u(),k("div",z,[C(e.$slots,"header",{formData:a.formData,formTable:a.formTable,baseObj:s.baseObj,uploadAccept:a.uploadAccept,taskStart:s.taskStart,taskComment1:s.taskComment1,taskComment2:s.taskComment2,taskComment3:s.taskComment3,taskComment4:s.taskComment4,taskComment5:s.taskComment5},void 0,!0)])):(u(),k("div",X,[i("div",null,[t[8]||(t[8]=i("span",null,"合同名称：",-1)),i("span",null,d(a.formData.epcName),1)]),i("div",G,[t[9]||(t[9]=i("span",null,"合同编号：",-1)),i("span",null,d(a.formData.contractCode),1)])])),i("div",{class:O(["form-body",{"hide-border":r.hideBorder}])},[r.showTarget?(u(),k("div",J,[i("div",Z,[t[10]||(t[10]=i("span",null,"致：",-1)),i("span",Q,d(a.formData.supervisionName),1)])])):N("",!0),i("div",$,[C(e.$slots,"default",{formData:a.formData,formTable:a.formTable,baseObj:s.baseObj,uploadAccept:a.uploadAccept,taskStart:s.taskStart,taskComment1:s.taskComment1,taskComment2:s.taskComment2,taskComment3:s.taskComment3,taskComment4:s.taskComment4,taskComment5:s.taskComment5},void 0,!0)])],2),i("div",ee,[C(e.$slots,"footer",{formData:a.formData,formTable:a.formTable,baseObj:s.baseObj,uploadAccept:a.uploadAccept,taskStart:s.taskStart,taskComment1:s.taskComment1,taskComment2:s.taskComment2,taskComment3:s.taskComment3,taskComment4:s.taskComment4,taskComment5:s.taskComment5},void 0,!0)])])]),_:3}),p(l,{border:!1},{default:f(()=>[i("div",te,[r.isShowConfirm1?(u(),b(D,{key:0,name:"radio",label:"是否信息化负责人确认：","label-width":"14em","input-align":"right"},{input:f(()=>[p(m,{modelValue:a.formData.isconfirm1,"onUpdate:modelValue":t[2]||(t[2]=o=>a.formData.isconfirm1=o),direction:"horizontal",disabled:!s.taskStart},{default:f(()=>[p(c,{name:!0},{default:f(()=>t[11]||(t[11]=[F("是")])),_:1,__:[11]}),p(c,{name:!1},{default:f(()=>t[12]||(t[12]=[F("否")])),_:1,__:[12]})]),_:1},8,["modelValue","disabled"])]),_:1})):N("",!0),p(D,{label:"附件上传：","label-align":"top","input-align":"left"},{input:f(()=>[p(v,{ref:"otherAttachment",g9s:a.formData.otherAttachment,"onUpdate:g9s":t[3]||(t[3]=o=>a.formData.otherAttachment=o),accept:a.uploadAccept,multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:!s.taskStart},null,8,["g9s","accept","readonly"])]),_:1})])]),_:1}),s.isBranch||r.noApprover?N("",!0):(u(),k(S,{key:0},[s.canApprover?(u(),b(l,{key:0,border:!1},{default:f(()=>[i("div",ae,[a.approverList&&a.approverList.length?(u(!0),k(S,{key:0},I(a.approverList||[],(o,fe)=>(u(),b(_,{label:"".concat(o.label),userName:a.formData[o.fieldName],"onUpdate:userName":j=>a.formData[o.fieldName]=j,userFullname:a.formData[o.fullnameFieldName],"onUpdate:userFullname":j=>a.formData[o.fullnameFieldName]=j,title:"选择执行人",labelWidth:"15em","input-align":"right",required:!!o.required,rules:[{required:!!o.required,message:"请选择执行人"}],multiple:!!o.multipleChoice,readonly:!s.taskStart},null,8,["label","userName","onUpdate:userName","userFullname","onUpdate:userFullname","required","rules","multiple","readonly"]))),256)):(u(),b(g,{key:1,title:"审批人",value:"该业务流程未配置审批人"}))])]),_:1})):N("",!0)],64))]),_:3},512)]),_:3},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onChangeComment","onDraftClick","onSubmitClick","onAfterSubmit","onRejectClick"])}const Ce=U(K,[["render",se],["__scopeId","data-v-7448c7c0"]]);const re={name:"DocumentPart",components:{},emits:["update:deptValue","update:personValue","update:dateValue"],props:{deptLabel:{type:String,default:"部门机构："},deptValue:{type:String,default:""},deptOptions:{type:Array,default:()=>[]},deptProp:{type:String,default:"deptProp"},personLabel:{type:String,default:"负责人："},personValue:{type:String,default:""},dateLabel:{type:String,default:"日期："},dateValue:{type:String,default:""},labelWidth:{type:String,default:"11em"},disabled:Boolean},setup(e,{attrs:t,slots:r,emit:n}){},data(){return{}},computed:{computedDeptValue:{get(){return this.deptValue||""},set(e){this.$emit("update:deptValue",e)}}},watch:{},created(){},mounted(){},methods:{}},ie={class:"document-part"},ne={class:"part-slot"},oe={class:"part-sign"},me={class:"ps-item"},le=["innerHTML"],de={class:"ps-item"},ce={class:"ps-item"};function ue(e,t,r,n,a,s){return u(),k("div",ie,[i("div",ne,[C(e.$slots,"default",{},void 0,!0)]),i("div",oe,[i("div",me,[i("span",{class:"label",style:T({width:r.labelWidth})},d(r.deptLabel),5),i("span",{class:"value",innerHTML:s.computedDeptValue.replaceAll("\n","<br/>")},null,8,le)]),i("div",de,[i("span",{class:"label",style:T({width:r.labelWidth})},d(r.personLabel),5),t[0]||(t[0]=i("span",{class:"value"},d(""),-1))]),i("div",ce,[i("span",{class:"label",style:T({width:r.labelWidth})},d(r.dateLabel),5),t[1]||(t[1]=i("span",{class:"value"},d(""),-1))])])])}const ve=U(re,[["render",ue],["__scopeId","data-v-06eba2c4"]]);export{ve as D,Ce as F};
