import{F as x,D}from"./index-1be3ad72.js";import{_ as P}from"./index-4829f8e2.js";import{Q as b,R as T,X as k,V as p,k as n,U as t,Y as d}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const L={name:"CB06",components:{FormTemplate:x,DocumentPart:D},emits:[],props:{},setup(u,{attrs:e,slots:f,emit:_}){},data(){return{detailTable:[],attachmentDesc:"1、组织机构图。\n2、部门职责及主要人员数量及分工。\n3、人员清单及其资格或岗位证书。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:u,detailParamList:e}){},onBeforeSubmit({formData:u,detailParamList:e,taskComment3:f},_){return new Promise((m,i)=>{try{m()}catch(s){i(s)}})}}},U={class:"one-line"},B={class:"form-info"},F={class:"attachment-desc"},O={class:"comment-wp"},A={class:"textarea-wp"},z={class:"comment-wp"},W={class:"textarea-wp"},g={class:"footer-input"},I={class:"form-info"},E={class:"form-info"},Q={class:"form-info"},R={class:"form-info"};function S(u,e,f,_,m,i){const s=b("van-field"),c=b("DocumentPart"),y=b("FormTemplate");return T(),k(y,{ref:"FormTemplate",nature:"机构","on-after-init":i.onAfterInit,"on-before-submit":i.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:m.attachmentDesc},{default:p(({formData:o,formTable:w,baseObj:r,uploadAccept:C,taskStart:l,taskComment2:V,taskComment3:v,taskComment4:N,taskComment5:h})=>[n(c,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:o.constructionDeptName,deptOptions:r.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!l},{default:p(()=>[t("div",U,[e[0]||(e[0]=t("span",{style:{"padding-left":"2em"}},"现提交第",-1)),t("span",B,d(o.field1),1),e[1]||(e[1]=t("span",null,"次现场机构及主要人员报审表，",-1)),e[2]||(e[2]=t("span",null,"请贵方审查。",-1))]),t("div",F,[e[3]||(e[3]=t("div",null,"附件：",-1)),n(s,{modelValue:o.attachmentDesc,"onUpdate:modelValue":a=>o.attachmentDesc=a,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),n(c,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:r.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!l},{default:p(()=>[t("div",O,[e[4]||(e[4]=t("div",null,"EPC总承包项目部意见：",-1)),t("div",A,[n(s,{modelValue:o.comment2,"onUpdate:modelValue":a=>o.comment2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!V},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),n(c,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!l},{default:p(()=>[t("div",z,[e[5]||(e[5]=t("div",null,"审查意见：",-1)),t("div",W,[n(s,{modelValue:o.comment3,"onUpdate:modelValue":a=>o.comment3=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:o,formTable:w,baseObj:r,uploadAccept:C,taskStart:l,taskComment2:V,taskComment3:v,taskComment4:N,taskComment5:h})=>[t("div",g,[e[6]||(e[6]=t("span",null,"注：本表一式",-1)),t("span",I,d(o.num1),1),e[7]||(e[7]=t("span",null,"份，由承包人填写，监理机构审查后，发包人",-1)),t("span",E,d(o.num2),1),e[8]||(e[8]=t("span",null,"份，监理机构",-1)),t("span",Q,d(o.num3),1),e[9]||(e[9]=t("span",null,"份，承包人",-1)),t("span",R,d(o.num4),1),e[10]||(e[10]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const se=P(L,[["render",S]]);export{se as default};
