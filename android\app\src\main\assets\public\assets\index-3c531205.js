import{F as T,D as C}from"./index-a831f9da.js";import{_ as P}from"./index-4829f8e2.js";import{Q as f,R as k,X as L,V as i,k as a,U as t,Y as s}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const h={name:"JL11",components:{FormTemplate:T,DocumentPart:C},emits:[],props:{},setup(d,{attrs:e,slots:c,emit:m}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:d,detailParamList:e}){},onBeforeSubmit({formData:d,detailParamList:e},c){return new Promise((m,l)=>{try{m()}catch(p){l(p)}})}}},B={class:"comment-wp"},D={style:{"text-indent":"2em"}},F={class:"form-info"},U={class:"form-info"},A={class:"form-info"},O={class:"form-info"},g={class:"comment-wp"},z={class:"textarea-wp"},I={class:"comment-wp"},W={class:"textarea-wp"},j={class:"footer-input"},J={class:"form-info"},Q={class:"form-info"},R={class:"form-info"},S={class:"form-info"};function X(d,e,c,m,l,p){const b=f("van-field"),_=f("DocumentPart"),v=f("FormTemplate");return k(),L(v,{ref:"FormTemplate",nature:"整改","on-after-init":p.onAfterInit,"on-before-submit":p.onBeforeSubmit,"detail-table":l.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:l.attachmentDesc},{default:i(({formData:o,formTable:V,baseObj:r,uploadAccept:w,taskStart:n,taskComment2:x,taskComment3:y,taskComment4:N})=>[a(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!n},{default:i(()=>[t("div",B,[t("div",D,[e[0]||(e[0]=t("span",null,"由于本通知所述原因，通知你方对",-1)),t("span",F,s(o.projectName),1),e[1]||(e[1]=t("span",null," 工程项目应按下述要求进行整改，并于",-1)),t("span",U,s(o.field2),1),e[2]||(e[2]=t("span",null,"年",-1)),t("span",A,s(o.field3),1),e[3]||(e[3]=t("span",null,"月",-1)),t("span",O,s(o.field4),1),e[4]||(e[4]=t("span",null,"日前提交整改措施报告，确保整改的结果达到要求。",-1))])]),t("div",g,[e[5]||(e[5]=t("div",null,"整改原因：",-1)),t("div",z,[a(b,{modelValue:o.field5,"onUpdate:modelValue":u=>o.field5=u,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!n},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),t("div",I,[e[6]||(e[6]=t("div",null,"整改要求：",-1)),t("div",W,[a(b,{modelValue:o.field6,"onUpdate:modelValue":u=>o.field6=u,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!n},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),a(_,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:r.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!n},{default:i(()=>e[7]||(e[7]=[t("div",{class:"comment-wp"},[t("div",{style:{height:"30px"}})],-1)])),_:2,__:[7]},1032,["deptValue","deptOptions","disabled"])]),footer:i(({formData:o,formTable:V,baseObj:r,uploadAccept:w,taskStart:n,taskComment2:x,taskComment3:y,taskComment4:N})=>[t("div",j,[e[8]||(e[8]=t("span",null,"说明：本表一式",-1)),t("span",J,s(o.num1),1),e[9]||(e[9]=t("span",null,"份，由监理机构填写，承包人签收后，承包人",-1)),t("span",Q,s(o.num2),1),e[10]||(e[10]=t("span",null,"份，监理机构",-1)),t("span",R,s(o.num3),1),e[11]||(e[11]=t("span",null,"份，发包人",-1)),t("span",S,s(o.num4),1),e[12]||(e[12]=t("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const pe=P(h,[["render",X]]);export{pe as default};
