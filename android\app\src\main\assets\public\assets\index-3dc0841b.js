import{F as y,D as k}from"./index-a831f9da.js";import{_ as w}from"./index-4829f8e2.js";import{Q as _,R as g,X as x,V as m,U as t,Y as i}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const T={name:"JL27",components:{FormTemplate:y,DocumentPart:k},emits:[],props:{},setup(o,{attrs:e,slots:r,emit:n}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:o,detailParamList:e}){},onBeforeSubmit({formData:o,detailParamList:e},r){return new Promise((n,s)=>{try{n()}catch(a){s(a)}})}}},C={class:"jl-table"},D={class:"cell"},B={class:"cell"},F={class:"cell"};function S(o,e,r,n,s,a){const p=_("FormTemplate");return g(),x(p,{ref:"FormTemplate",nature:"巡视","on-after-init":a.onAfterInit,"on-before-submit":a.onBeforeSubmit,"detail-table":s.detailTable,"is-show-confirm1":!1,"show-target":!1,"hide-border":"",attachmentDesc:s.attachmentDesc},{default:m(({formData:l,formTable:d,baseObj:c,uploadAccept:f,taskStart:u,taskComment2:b,taskComment3:v,taskComment4:h})=>[t("table",C,[t("tr",null,[e[0]||(e[0]=t("th",{style:{width:"90px"}},[t("div",{class:"cell"},"巡视范围")],-1)),t("td",null,[t("div",D,i(l.field1),1)])]),t("tr",null,[e[1]||(e[1]=t("th",{style:{width:"90px"}},[t("div",{class:"cell"},"巡视情况")],-1)),t("td",null,[t("div",B,i(l.field2),1)])]),t("tr",null,[e[2]||(e[2]=t("th",{style:{width:"90px"}},[t("div",{class:"cell"},"发现问题及处理意见")],-1)),t("td",null,[t("div",F,i(l.field3),1)])]),e[3]||(e[3]=t("tr",null,[t("td",{colspan:"2"},[t("div",{class:"cell"},[t("div",{class:"table-sign",style:{"padding-left":"30%"}},[t("div",{class:"sign-item"},[t("span",{class:"label",style:{width:"5em"}},"巡视人"),t("span",{class:"value"},"（签名）")]),t("div",{class:"sign-item"},[t("span",{class:"label",style:{width:"5em"}},"日期"),t("span",{class:"value"},[t("span",{style:{"padding-left":"4em"}},"年"),t("span",{style:{"padding-left":"2em"}},"月"),t("span",{style:{"padding-left":"2em"}},"日")])])])])])],-1))])]),footer:m(({formData:l,formTable:d,baseObj:c,uploadAccept:f,taskStart:u,taskComment2:b,taskComment3:v,taskComment4:h})=>e[4]||(e[4]=[t("div",{class:"footer-input"},[t("span",null,"说明：1、本表可用于监理人员质量、安全、进度等的巡视记录。")],-1),t("div",{class:"footer-input"},[t("span",{style:{"text-indent":"3em"}},"2、本表按月装订成册。")],-1)])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const z=w(T,[["render",S]]);export{z as default};
