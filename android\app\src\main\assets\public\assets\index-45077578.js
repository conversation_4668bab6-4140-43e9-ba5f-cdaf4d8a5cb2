import{F as T,D}from"./index-a831f9da.js";import{_ as N}from"./index-4829f8e2.js";import{Q as c,R as P,X as C,V as d,k as n,U as t,Y as f}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const L={name:"JL09",components:{FormTemplate:T,DocumentPart:D},emits:[],props:{},setup(i,{attrs:e,slots:b,emit:m}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:i,detailParamList:e}){},onBeforeSubmit({formData:i,detailParamList:e},b){return new Promise((m,s)=>{try{m()}catch(p){s(p)}})}}},U={class:"comment-wp"},k={class:"textarea-wp"},B={class:"comment-wp"},F={class:"textarea-wp"},A={class:"comment-wp"},O={class:"textarea-wp"},z={class:"footer-input"},I={class:"form-info"},W={class:"form-info"},g={class:"form-info"};function J(i,e,b,m,s,p){const r=c("van-field"),_=c("DocumentPart"),v=c("FormTemplate");return P(),C(v,{ref:"FormTemplate",nature:"现指","on-after-init":p.onAfterInit,"on-before-submit":p.onBeforeSubmit,"detail-table":s.detailTable,"is-show-confirm1":!1,attachmentDesc:s.attachmentDesc},{default:d(({formData:o,formTable:w,baseObj:u,uploadAccept:y,taskStart:l,taskComment2:x,taskComment3:V,taskComment4:h})=>[n(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:u.supervisionDeptName,personLabel:"监理工程师/监理员：",labelWidth:"10em",disabled:!l},{default:d(()=>[t("div",U,[e[0]||(e[0]=t("div",null,"事由：",-1)),t("div",k,[n(r,{modelValue:o.field1,"onUpdate:modelValue":a=>o.field1=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),t("div",B,[e[1]||(e[1]=t("div",null,"通知内容：",-1)),t("div",F,[n(r,{modelValue:o.field2,"onUpdate:modelValue":a=>o.field2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),n(_,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:o.epcDeptName,deptOptions:u.epcDeptName,personLabel:"现场负责人：",labelWidth:"10em",disabled:!l},{default:d(()=>[t("div",A,[e[2]||(e[2]=t("div",null,"承包人意见：",-1)),t("div",O,[n(r,{modelValue:o.comment3,"onUpdate:modelValue":a=>o.comment3=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!V},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:d(({formData:o,formTable:w,baseObj:u,uploadAccept:y,taskStart:l,taskComment2:x,taskComment3:V,taskComment4:h})=>[t("div",z,[e[3]||(e[3]=t("span",null,"说明：1、本通知一式",-1)),t("span",I,f(o.num1),1),e[4]||(e[4]=t("span",null,"份，由监理机构填写，承包人签署意见后，承包人",-1)),t("span",W,f(o.num2),1),e[5]||(e[5]=t("span",null,"份，监理机构",-1)),t("span",g,f(o.num3),1),e[6]||(e[6]=t("span",null,"份。",-1))]),e[7]||(e[7]=t("div",{class:"footer-input"},[t("span",{style:{"text-indent":"3em"}},"2、本表一般情况下应由监理工程师签发；对现场发现的施工人员违反操作规程的行为，监理员可以签发。")],-1))]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const te=N(L,[["render",J]]);export{te as default};
