import{F as B,D as F}from"./index-1be3ad72.js";import{_ as D}from"./index-4829f8e2.js";import{Q as c,R as y,X as C,V as m,k as n,U as e,Y as s,S as P,W as O,F as k}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const A={name:"CB32",components:{FormTemplate:B,DocumentPart:F},emits:[],props:{},setup(V,{attrs:l,slots:U,emit:v}){},data(){return{detailTable:[{},{},{},{},{}],attachmentDesc:"人员工作明细\n材料使用明细\n施工设备使用明细"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:V,detailParamList:l}){},onBeforeSubmit({formData:V,detailParamList:l,taskComment3:U},v){return new Promise((u,r)=>{try{u()}catch(a){r(a)}})}}},W={class:"one-line"},g={class:"form-info"},z={class:"form-info"},E={class:"form-info"},I={class:"form-info"},S={class:"form-info"},Q={class:"attachment-desc"},R={class:"comment-wp"},X={class:"textarea-wp"},Y={class:"form-table"},q={class:"center"},G={class:"comment-wp"},H={class:"textarea-wp"},J={class:"footer-input"},K={class:"form-info"},M={class:"form-info"},Z={class:"form-info"},$={class:"form-info"};function j(V,l,U,v,u,r){const a=c("van-field"),b=c("DocumentPart"),x=c("FormTemplate");return y(),C(x,{ref:"FormTemplate",nature:"计签","on-after-init":r.onAfterInit,"on-before-submit":r.onBeforeSubmit,"detail-table":u.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:u.attachmentDesc},{default:m(({formData:t,formTable:_,baseObj:f,uploadAccept:L,taskStart:i,taskComment2:p,taskComment3:N,taskComment4:w,taskComment5:T})=>[n(b,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:t.constructionDeptName,deptOptions:f.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!i},{default:m(()=>[e("div",W,[l[0]||(l[0]=e("span",{style:{"padding-left":"2em"}},"我方按计日工工作通知",-1)),l[1]||(l[1]=e("span",null,"（监理 [ ",-1)),e("span",g,s(t.field1),1),l[2]||(l[2]=e("span",null,"] 计通",-1)),e("span",z,s(t.field2),1),l[3]||(l[3]=e("span",null,"号）",-1)),l[4]||(l[4]=e("span",null,"实施了下列所列项目，",-1)),l[5]||(l[5]=e("span",null,"现按施工合同约定申报 ",-1)),e("span",E,s(t.field3),1),l[6]||(l[6]=e("span",null,"年",-1)),e("span",I,s(t.field4),1),l[7]||(l[7]=e("span",null,"月",-1)),e("span",S,s(t.field5),1),l[8]||(l[8]=e("span",null,"日的计日工程量，",-1)),l[9]||(l[9]=e("span",null,"请贵方审核。",-1))]),e("div",Q,[l[10]||(l[10]=e("div",null,"附件：",-1)),n(a,{modelValue:t.attachmentDesc,"onUpdate:modelValue":o=>t.attachmentDesc=o,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),n(b,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:t.epcDeptName,deptOptions:f.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!i},{default:m(()=>[e("div",R,[l[11]||(l[11]=e("div",null,"EPC总承包项目部意见：",-1)),e("div",X,[n(a,{modelValue:t.comment2,"onUpdate:modelValue":o=>t.comment2=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),e("div",Y,[e("table",null,[l[12]||(l[12]=e("thead",null,[e("tr",null,[e("th",{colspan:"1",rowspan:"1"},"序号"),e("th",{colspan:"1",rowspan:"1"},"工程项目名称"),e("th",{colspan:"1",rowspan:"1"},"计日工内容"),e("th",{colspan:"1",rowspan:"1"},"单位"),e("th",{colspan:"1",rowspan:"1"},"申报工程量"),e("th",{colspan:"1",rowspan:"1"},"核准工程量"),e("th",{colspan:"1",rowspan:"1"},"说明")])],-1)),e("tbody",null,[(y(!0),P(k,null,O(_||[],(o,h)=>(y(),P("tr",{key:h},[e("td",q,s(h+1),1),e("td",null,[n(a,{modelValue:o.field1,"onUpdate:modelValue":d=>o.field1=d,label:"",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])]),e("td",null,[n(a,{modelValue:o.field2,"onUpdate:modelValue":d=>o.field2=d,label:"",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])]),e("td",null,[n(a,{modelValue:o.field3,"onUpdate:modelValue":d=>o.field3=d,label:"",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])]),e("td",null,[n(a,{modelValue:o.field4,"onUpdate:modelValue":d=>o.field4=d,label:"",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])]),e("td",null,[n(a,{modelValue:o.field5,"onUpdate:modelValue":d=>o.field5=d,label:"",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])]),e("td",null,[n(a,{modelValue:o.field6,"onUpdate:modelValue":d=>o.field6=d,label:"",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])])]))),128))])])]),n(b,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:t.supervisionDeptName,deptOptions:f.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!i},{default:m(()=>[e("div",G,[l[13]||(l[13]=e("div",null,"审核意见：",-1)),e("div",H,[n(a,{modelValue:t.comment3,"onUpdate:modelValue":o=>t.comment3=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!N},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:m(({formData:t,formTable:_,baseObj:f,uploadAccept:L,taskStart:i,taskComment2:p,taskComment3:N,taskComment4:w,taskComment5:T})=>[e("div",J,[l[14]||(l[14]=e("span",null,"说明：本表一式",-1)),e("span",K,s(t.num1),1),l[15]||(l[15]=e("span",null,"份，由承包人每个工作日完成后填写，经监理机构审核后，",-1)),l[16]||(l[16]=e("span",null,"发包人",-1)),e("span",M,s(t.num2),1),l[17]||(l[17]=e("span",null,"份，监理机构",-1)),e("span",Z,s(t.num3),1),l[18]||(l[18]=e("span",null,"份，退返承包人",-1)),e("span",$,s(t.num4),1),l[19]||(l[19]=e("span",null,"份。",-1)),l[20]||(l[20]=e("span",null,"作结算时使用。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const be=D(A,[["render",j]]);export{be as default};
