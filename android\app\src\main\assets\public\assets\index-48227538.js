import{F as w,D as F}from"./index-a831f9da.js";import{_ as x}from"./index-4829f8e2.js";import{Q as r,R as $,X as N,V as m,k as p,U as s,Y as o,B as b}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const U={name:"JL22",components:{FormTemplate:w,DocumentPart:F},emits:[],props:{},setup(n,{attrs:e,slots:f,emit:h}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:n,detailParamList:e}){},onBeforeSubmit({formData:n,detailParamList:e,taskStart:f},h){return new Promise((u,t)=>{try{if(h==="submit"&&f){if(!n.check1&&!n.check2)return this.$showNotify({type:"danger",message:"请选择退还保证金条件",duration:3*1e3}),t(!1),!1;if(n.check1&&(!n.field5||!n.field6||!n.field7))return this.$showNotify({type:"danger",message:"请填写签发合同工程完工证书日期",duration:3*1e3}),t(!1),!1;if(n.check2&&(!n.field8||!n.field9||!n.field10))return this.$showNotify({type:"danger",message:"请填写签发缺陷责任期终止证书日期",duration:3*1e3}),t(!1),!1;if(!n.check3&&!n.check4)return this.$showNotify({type:"danger",message:"请选择保证金扣留原因",duration:3*1e3}),t(!1),!1}u()}catch(a){t(a)}})},changeCheck1(n){n&&(this.$refs.FormTemplate.formData.check2=!1,this.$refs.FormTemplate.formData.field8="",this.$refs.FormTemplate.formData.field9="",this.$refs.FormTemplate.formData.field10="",this.$nextTick(()=>{var e;(e=this.$refs.FormTemplate.$refs.form)==null||e.resetValidation()}))},changeCheck2(n){n&&(this.$refs.FormTemplate.formData.check1=!1,this.$refs.FormTemplate.formData.field5="",this.$refs.FormTemplate.formData.field6="",this.$refs.FormTemplate.formData.field7="",this.$nextTick(()=>{var e;(e=this.$refs.FormTemplate.$refs.form)==null||e.resetValidation()}))},changeCheck3(n){n&&(this.$refs.FormTemplate.formData.check4=!1)},changeCheck4(n){n&&(this.$refs.FormTemplate.formData.check3=!1)}}},P={class:"one-line"},L={class:"form-info"},B={class:"form-info"},q={class:"form-info"},A={class:"form-info"},O={class:"form-info"},I={class:"table-wp"},W={class:"jl-table"},z={colspan:"2"},J={class:"cell"},Q={class:"one-line"},R={class:"check-wp"},X={class:"form-info"},Y={class:"form-info"},E={class:"form-info"},G={class:"one-line"},H={class:"check-wp"},K={class:"form-info"},M={class:"form-info"},Z={class:"form-info"},S={class:"cell"},j={class:"one-line"},D={class:"form-info"},ss={class:"form-info"},es={class:"cell"},ls={class:"one-line"},ns={class:"form-info"},os={class:"form-info"},ts={class:"cell"},is={class:"one-line"},ds={class:"form-info"},ps={class:"form-info"},as={class:"check-wp"},ms={class:"check-wp"},us={class:"cell"},rs={class:"one-line"},fs={class:"form-info"},hs={class:"form-info"},cs={class:"comment-wp"},vs={class:"textarea-wp"},_s={class:"footer-input"},bs={class:"form-info"},ks={class:"form-info"},gs={class:"form-info"},Vs={class:"form-info"};function ys(n,e,f,h,u,t){const a=r("van-checkbox"),v=r("DocumentPart"),k=r("van-field"),g=r("FormTemplate");return $(),N(g,{ref:"FormTemplate",nature:"保退","employer-target":!0,"on-after-init":t.onAfterInit,"on-before-submit":t.onBeforeSubmit,"detail-table":u.detailTable,"is-show-confirm1":!1,attachmentDesc:u.attachmentDesc},{default:m(({formData:l,formTable:V,baseObj:c,uploadAccept:y,taskStart:d,taskComment2:C,taskComment3:T,taskComment4:_})=>[p(v,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:l.supervisionDeptName,deptOptions:c.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!d},{default:m(()=>[s("div",P,[e[0]||(e[0]=s("span",{style:{"padding-left":"2em"}},"经审核承包人的质量保证金退还申请表",-1)),e[1]||(e[1]=s("span",null,"（ ",-1)),s("span",L,o(l.constructionName),1),e[2]||(e[2]=s("span",null," [ ",-1)),s("span",B,o(l.field1),1),e[3]||(e[3]=s("span",null," ] 退保",-1)),s("span",q,o(l.field2),1),e[4]||(e[4]=s("span",null,"号），",-1)),e[5]||(e[5]=s("span",null,"本次应退还给承包人的质量保证金金额为",-1)),e[6]||(e[6]=s("span",null,"（大写）",-1)),s("span",A,o(l.field3),1),e[7]||(e[7]=s("span",null,"（小写",-1)),s("span",O,o(l.field4),1),e[8]||(e[8]=s("span",null,"）。",-1))]),e[35]||(e[35]=s("div",{class:"one-line"},[s("span",{style:{"padding-left":"2em"}},"请贵方在收到该质量保证金退还证书后按合同约定完成审批，并将上述质量保证金退还给承包人。")],-1)),s("div",I,[s("table",W,[e[34]||(e[34]=s("colgroup",null,[s("col",{width:"50"}),s("col",{width:"50"}),s("col",{width:"auto"})],-1)),s("tbody",null,[s("tr",null,[e[17]||(e[17]=s("th",null,[s("div",{class:"cell"},"退还质量保证金已具备的条件")],-1)),s("td",z,[s("div",J,[s("div",Q,[s("div",R,[p(a,{modelValue:l.check1,"onUpdate:modelValue":i=>l.check1=i,shape:"square",disabled:!d,onChange:t.changeCheck1},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[9]||(e[9]=s("span",null,"于",-1)),s("span",X,o(l.field5),1),e[10]||(e[10]=s("span",null,"年",-1)),s("span",Y,o(l.field6),1),e[11]||(e[11]=s("span",null,"月",-1)),s("span",E,o(l.field7),1),e[12]||(e[12]=s("span",null,"日签发合同工程完工证书",-1))]),s("div",G,[s("div",H,[p(a,{modelValue:l.check2,"onUpdate:modelValue":i=>l.check2=i,shape:"square",disabled:!d,onChange:t.changeCheck2},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[13]||(e[13]=s("span",null,"于",-1)),s("span",K,o(l.field8),1),e[14]||(e[14]=s("span",null,"年",-1)),s("span",M,o(l.field9),1),e[15]||(e[15]=s("span",null,"月",-1)),s("span",Z,o(l.field10),1),e[16]||(e[16]=s("span",null,"日签发缺陷责任期终止证书",-1))])])])]),s("tr",null,[e[20]||(e[20]=s("th",{rowspan:"4"},[s("div",{class:"cell"},"质量保证金退还金额")],-1)),e[21]||(e[21]=s("th",null,[s("div",{class:"cell"},"质量保证金总金额")],-1)),s("td",null,[s("div",S,[s("div",j,[s("span",D,o(l.field11),1),e[18]||(e[18]=s("span",null,"元（小写",-1)),s("span",ss,o(l.field12),1),e[19]||(e[19]=s("span",null,"元）",-1))])])])]),s("tr",null,[e[24]||(e[24]=s("th",null,[s("div",{class:"cell"},"已退还金额")],-1)),s("td",null,[s("div",es,[s("div",ls,[s("span",ns,o(l.field13),1),e[22]||(e[22]=s("span",null,"元（小写",-1)),s("span",os,o(l.field14),1),e[23]||(e[23]=s("span",null,"元）",-1))])])])]),s("tr",null,[e[30]||(e[30]=s("th",null,[s("div",{class:"cell"},"尚应扣留的金额")],-1)),s("td",null,[s("div",ts,[s("div",is,[s("span",ds,o(l.field15),1),e[25]||(e[25]=s("span",null,"元（小写",-1)),s("span",ps,o(l.field16),1),e[26]||(e[26]=s("span",null,"元）",-1))]),e[29]||(e[29]=s("div",{style:{padding:"10px 0"}},"扣留的原因：",-1)),s("div",null,[s("div",as,[p(a,{modelValue:l.check3,"onUpdate:modelValue":i=>l.check3=i,shape:"square",disabled:!d,onChange:t.changeCheck3},{default:m(()=>e[27]||(e[27]=[b("施工合同约定")])),_:2,__:[27]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),s("div",null,[s("div",ms,[p(a,{modelValue:l.check4,"onUpdate:modelValue":i=>l.check4=i,shape:"square",disabled:!d,onChange:t.changeCheck4},{default:m(()=>e[28]||(e[28]=[b("遗留问题")])),_:2,__:[28]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])])])])]),s("tr",null,[e[33]||(e[33]=s("th",null,[s("div",{class:"cell"},"本次应退还金额")],-1)),s("td",null,[s("div",us,[s("div",rs,[s("span",fs,o(l.field17),1),e[31]||(e[31]=s("span",null,"元（小写",-1)),s("span",hs,o(l.field18),1),e[32]||(e[32]=s("span",null,"元）",-1))])])])])])])]),e[36]||(e[36]=s("div",{style:{height:"20px"}},null,-1))]),_:2,__:[35,36]},1032,["deptValue","deptOptions","disabled"]),p(v,{deptLabel:"发包人：",deptProp:"employerName",deptValue:l.employerName,deptOptions:c.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!d},{default:m(()=>[s("div",cs,[e[37]||(e[37]=s("div",null,"发包人审批意见：",-1)),s("div",vs,[p(k,{modelValue:l.comment4,"onUpdate:modelValue":i=>l.comment4=i,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!_},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:m(({formData:l,formTable:V,baseObj:c,uploadAccept:y,taskStart:d,taskComment2:C,taskComment3:T,taskComment4:_})=>[s("div",_s,[e[38]||(e[38]=s("span",null,"说明：本证书一式",-1)),s("span",bs,o(l.num1),1),e[39]||(e[39]=s("span",null,"份，由监理机构填写，",-1)),e[40]||(e[40]=s("span",null,"监理机构、发包人签发后，发包人",-1)),s("span",ks,o(l.num2),1),e[41]||(e[41]=s("span",null,"份，承包人",-1)),s("span",gs,o(l.num3),1),e[42]||(e[42]=s("span",null,"份，监理机构",-1)),s("span",Vs,o(l.num4),1),e[43]||(e[43]=s("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const Ws=x(U,[["render",ys],["__scopeId","data-v-a9662dba"]]);export{Ws as default};
