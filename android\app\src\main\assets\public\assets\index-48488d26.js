import{F as w,D as x}from"./index-a831f9da.js";import{_ as N}from"./index-4829f8e2.js";import{Q as u,R as P,X as F,V as i,k as d,U as t,Y as n,B as c}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const L={name:"JL20",components:{FormTemplate:w,DocumentPart:x},emits:[],props:{},setup(o,{attrs:e,slots:f,emit:b}){},data(){return{detailTable:[],attachmentDesc:"1、合同解除相关文件。\n2、计算资料。\n3、证明文件（包含承包人已得到各项付款的证明文件）。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:o,detailParamList:e}){},onBeforeSubmit({formData:o,detailParamList:e,taskStart:f},b){return new Promise((m,l)=>{try{if(b==="submit"&&f&&!o.check1&&!o.check2)return this.$showNotify({type:"danger",message:"请选择类型：支付/退还",duration:3*1e3}),l(!1),!1;m()}catch(r){l(r)}})},changeCheck1(o){o&&(this.$refs.FormTemplate.formData.check2=!1)},changeCheck2(o){o&&(this.$refs.FormTemplate.formData.check1=!1)}}},U={class:"one-line"},B={class:"form-info"},A={class:"form-info"},O={class:"form-info"},q={class:"form-info"},I={class:"check-wp"},W={class:"check-wp"},z={class:"form-info"},J={class:"form-info"},Q={class:"attachment-desc"},R={class:"footer-input"},X={class:"form-info"},Y={class:"form-info"},$={class:"form-info"},D={class:"form-info"};function E(o,e,f,b,m,l){const r=u("van-checkbox"),h=u("van-field"),V=u("DocumentPart"),v=u("FormTemplate");return P(),F(v,{ref:"FormTemplate",nature:"解付","employer-target":!0,"on-after-init":l.onAfterInit,"on-before-submit":l.onBeforeSubmit,"detail-table":m.detailTable,"is-show-confirm1":!1,attachmentDesc:m.attachmentDesc},{default:i(({formData:s,formTable:C,baseObj:k,uploadAccept:g,taskStart:a,taskComment2:y,taskComment3:_,taskComment4:T})=>[d(V,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:k.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!a},{default:i(()=>[t("div",U,[e[2]||(e[2]=t("span",{style:{"padding-left":"2em"}},"根据施工合同约定，经核查，合同解除后承包人应获得工程价款总金额为",-1)),e[3]||(e[3]=t("span",null,"（大写）",-1)),t("span",B,n(s.field1),1),e[4]||(e[4]=t("span",null,"（小写",-1)),t("span",A,n(s.field2),1),e[5]||(e[5]=t("span",null,"），",-1)),e[6]||(e[6]=t("span",null,"已得到各顶付款总金额为",-1)),e[7]||(e[7]=t("span",null,"（大写）",-1)),t("span",O,n(s.field3),1),e[8]||(e[8]=t("span",null,"（小写",-1)),t("span",q,n(s.field4),1),e[9]||(e[9]=t("span",null,"），",-1)),e[10]||(e[10]=t("span",null,"现应",-1)),t("div",I,[d(r,{modelValue:s.check1,"onUpdate:modelValue":p=>s.check1=p,shape:"square",disabled:!a,onChange:l.changeCheck1},{default:i(()=>e[0]||(e[0]=[c("支付")])),_:2,__:[0]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[11]||(e[11]=t("span",null,"/",-1)),t("div",W,[d(r,{modelValue:s.check2,"onUpdate:modelValue":p=>s.check2=p,shape:"square",disabled:!a,onChange:l.changeCheck2},{default:i(()=>e[1]||(e[1]=[c("退还")])),_:2,__:[1]},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])]),e[12]||(e[12]=t("span",null,"的工程款金额为",-1)),e[13]||(e[13]=t("span",null,"（大写）",-1)),t("span",z,n(s.field5),1),e[14]||(e[14]=t("span",null,"（小写",-1)),t("span",J,n(s.field6),1),e[15]||(e[15]=t("span",null,"）。",-1))]),t("div",Q,[e[16]||(e[16]=t("div",null,"附件：",-1)),d(h,{modelValue:s.attachmentDesc,"onUpdate:modelValue":p=>s.attachmentDesc=p,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!a},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),d(V,{deptLabel:"发包人：",deptProp:"employerName",deptValue:s.employerName,deptOptions:k.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!a},{default:i(()=>e[17]||(e[17]=[t("div",{class:"comment-wp"},[t("div",{style:{height:"30px"}})],-1)])),_:2,__:[17]},1032,["deptValue","deptOptions","disabled"])]),footer:i(({formData:s,formTable:C,baseObj:k,uploadAccept:g,taskStart:a,taskComment2:y,taskComment3:_,taskComment4:T})=>[t("div",R,[e[18]||(e[18]=t("span",null,"说明：本证书一式",-1)),t("span",X,n(s.num1),1),e[19]||(e[19]=t("span",null,"份，由监理机构填写，发包人",-1)),t("span",Y,n(s.num2),1),e[20]||(e[20]=t("span",null,"份，监理机构",-1)),t("span",$,n(s.num3),1),e[21]||(e[21]=t("span",null,"份，承包人",-1)),t("span",D,n(s.num4),1),e[22]||(e[22]=t("span",null,"份，",-1)),e[23]||(e[23]=t("span",null,"办理结算的附件。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const pe=N(L,[["render",E]]);export{pe as default};
