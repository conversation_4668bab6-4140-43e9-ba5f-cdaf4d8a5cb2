import{F as _,D as x}from"./index-a831f9da.js";import{_ as k}from"./index-4829f8e2.js";import{Q as w,R as g,X as y,V as r,U as l,Y as n}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const T={name:"JL33",components:{FormTemplate:_,DocumentPart:x},emits:[],props:{},setup(i,{attrs:t,slots:a,emit:p}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:i,detailParamList:t}){},onBeforeSubmit({formData:i,detailParamList:t,taskStart:a},p){return new Promise((e,o)=>{try{e()}catch(d){o(d)}})}}},C={class:"jl-table"},S={class:"cell"},B={class:"cell"},F={class:"cell"},A={class:"cell"},P={colspan:"7"},j={class:"cell"},L={colspan:"7"},I={class:"cell"},O={colspan:"7"},V={class:"cell"},D={colspan:"7"},J={class:"cell"},N={colspan:"7"},Q={class:"cell"},R={colspan:"7"},U={class:"cell"};function X(i,t,a,p,e,o){const d=w("FormTemplate");return g(),y(d,{ref:"FormTemplate",nature:"日记","on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":e.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:e.attachmentDesc},{default:r(({formData:s,formTable:m,baseObj:c,uploadAccept:u,taskStart:f,taskComment2:v,taskComment3:b,taskComment4:h})=>[l("table",C,[t[11]||(t[11]=l("colgroup",null,[l("col",{width:"55px"}),l("col",{"min-width":"40px"}),l("col",{"min-width":"40px"}),l("col",{"min-width":"40px"}),l("col",{"min-width":"40px"}),l("col",{"min-width":"40px"})],-1)),l("tbody",null,[l("tr",null,[t[0]||(t[0]=l("th",null,[l("div",{class:"cell"},"天气")],-1)),l("td",null,[l("div",S,[l("span",null,n(s.field1),1)])]),t[1]||(t[1]=l("th",null,[l("div",{class:"cell"},"气温")],-1)),l("td",null,[l("div",B,[l("span",null,n(s.field2),1)])]),t[2]||(t[2]=l("th",null,[l("div",{class:"cell"},"风力")],-1)),l("td",null,[l("div",F,[l("span",null,n(s.field3),1)])]),t[3]||(t[3]=l("th",null,[l("div",{class:"cell"},"风向")],-1)),l("td",null,[l("div",A,[l("span",null,n(s.field4),1)])])]),l("tr",null,[t[4]||(t[4]=l("th",null,[l("div",{class:"cell"}," 施工部位、施工内容（包括隐蔽部位施工时的地质编录情况）、施工形象及资源投入情况 ")],-1)),l("td",P,[l("div",j,[l("span",null,n(s.field5),1)])])]),l("tr",null,[t[5]||(t[5]=l("th",null,[l("div",{class:"cell"},"承包人质量检验和安全作业情况")],-1)),l("td",L,[l("div",I,[l("span",null,n(s.field6),1)])])]),l("tr",null,[t[6]||(t[6]=l("th",null,[l("div",{class:"cell"},"监理机构的检查、巡视、检验情况")],-1)),l("td",O,[l("div",V,[l("span",null,n(s.field7),1)])])]),l("tr",null,[t[7]||(t[7]=l("th",null,[l("div",{class:"cell"}," 施工作业存在的问题，现场监理人员提出的处理意见以及承包人对处理意见的落实情况 ")],-1)),l("td",D,[l("div",J,[l("span",null,n(s.field8),1)])])]),l("tr",null,[t[8]||(t[8]=l("th",null,[l("div",{class:"cell"},"汇报事项和监理机构指示")],-1)),l("td",N,[l("div",Q,[l("span",null,n(s.field9),1)])])]),l("tr",null,[t[9]||(t[9]=l("th",null,[l("div",{class:"cell"},"其他事项")],-1)),l("td",R,[l("div",U,[l("span",null,n(s.field10),1)])])]),t[10]||(t[10]=l("tr",null,[l("td",{colspan:"8"},[l("div",{class:"cell"},[l("div",{style:{height:"30px"}}),l("div",{class:"part-div",style:{"margin-left":"100px"}},[l("div",{class:"part-sign"},[l("div",null,[l("span",null,"监理人员：")]),l("div",null,[l("span",null,"日期："),l("span",{style:{"padding-left":"2em"}},"年"),l("span",{style:{"padding-left":"2em"}},"月"),l("span",{style:{"padding-left":"2em"}},"日")])])])])])],-1))])])]),footer:r(({formData:s,formTable:m,baseObj:c,uploadAccept:u,taskStart:f,taskComment2:v,taskComment3:b,taskComment4:h})=>t[12]||(t[12]=[l("div",{class:"footer-input"},[l("div",null,"说明：本表由现场监理人员填写，按月装订成册。")],-1)])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const el=k(T,[["render",X]]);export{el as default};
