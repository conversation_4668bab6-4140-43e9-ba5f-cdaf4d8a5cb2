import{F as C,D as P}from"./index-a831f9da.js";import{_ as x}from"./index-4829f8e2.js";import{Q as r,R as y,X as L,V as p,k as u,U as e,Y as n}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const B={name:"JL02",components:{FormTemplate:C,DocumentPart:P},emits:[],props:{},setup(i,{attrs:t,slots:f,emit:a}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:i,detailParamList:t}){},onBeforeSubmit({formData:i,detailParamList:t},f){return new Promise((a,o)=>{try{a()}catch(l){o(l)}})}}},F={class:"one-line"},A={class:"form-info"},D={class:"form-info"},O={class:"form-info"},S={class:"form-info"},U={class:"form-info"},g={class:"form-info"},I={class:"form-info"},W={class:"comment-wp"},j={class:"textarea-wp"},z={class:"footer-input"},J={class:"form-info"},Q={class:"form-info"},R={class:"form-info"},X={class:"form-info"};function Y(i,t,f,a,o,l){const _=r("van-field"),b=r("DocumentPart"),v=r("FormTemplate");return y(),L(v,{ref:"FormTemplate",nature:"合开工","on-after-init":l.onAfterInit,"on-before-submit":l.onBeforeSubmit,"detail-table":o.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:o.attachmentDesc},{default:p(({formData:s,formTable:V,baseObj:m,uploadAccept:c,taskStart:d,taskComment2:N,taskComment3:k,taskComment4:T})=>[u(b,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:m.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!d},{default:p(()=>[e("div",F,[t[0]||(t[0]=e("span",{style:{"padding-left":"2em"}},"贵方",-1)),e("span",A,n(s.field1),1),t[1]||(t[1]=e("span",null,"年",-1)),e("span",D,n(s.field2),1),t[2]||(t[2]=e("span",null,"月",-1)),e("span",O,n(s.field3),1),t[3]||(t[3]=e("span",null,"日报送的",-1)),e("span",S,n(s.projectName),1),t[4]||(t[4]=e("span",null,"工程合同工程开工申请",-1)),t[5]||(t[5]=e("span",null,"（ ",-1)),e("span",U,n(s.constructionName),1),t[6]||(t[6]=e("span",null," [ ",-1)),e("span",g,n(s.field4),1),t[7]||(t[7]=e("span",null," ] 合开工",-1)),e("span",I,n(s.field5),1),t[8]||(t[8]=e("span",null,"号）",-1)),t[9]||(t[9]=e("span",null,"已经通过审核，",-1)),t[10]||(t[10]=e("span",null,"同意贵方按施工进度计划组织施工。",-1))]),e("div",W,[t[11]||(t[11]=e("div",null,"批复意见：",-1)),e("div",j,[u(_,{modelValue:s.field6,"onUpdate:modelValue":w=>s.field6=w,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"（可附页）",readonly:!d},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),u(b,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:s.epcDeptName,deptOptions:m.epcDeptName,personLabel:"项目负责人：",labelWidth:"10em",disabled:!0},{default:p(()=>t[12]||(t[12]=[e("div",{class:"comment-wp"},[e("div",null,"今已收到合同工程的开工批复。")],-1)])),_:2,__:[12]},1032,["deptValue","deptOptions"])]),footer:p(({formData:s,formTable:V,baseObj:m,uploadAccept:c,taskStart:d,taskComment2:N,taskComment3:k,taskComment4:T})=>[e("div",z,[t[13]||(t[13]=e("span",null,"说明：本表一式",-1)),e("span",J,n(s.num1),1),t[14]||(t[14]=e("span",null,"份，由监理机构填写。承包人签收后，承包人",-1)),e("span",Q,n(s.num2),1),t[15]||(t[15]=e("span",null,"份，监理机构",-1)),e("span",R,n(s.num3),1),t[16]||(t[16]=e("span",null,"份，发包人",-1)),e("span",X,n(s.num4),1),t[17]||(t[17]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const pt=x(B,[["render",Y]]);export{pt as default};
