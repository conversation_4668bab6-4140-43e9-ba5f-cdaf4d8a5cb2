import{F as P,D as S}from"./index-1be3ad72.js";import{_ as F}from"./index-4829f8e2.js";import{Q as N,R as f,X as L,V as m,k as b,U as e,Y as n,S as w,W as x,F as B,Z as O}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const A={name:"CB04",components:{FormTemplate:P,DocumentPart:S},emits:[],props:{},setup(p,{attrs:t,slots:_,emit:r}){},data(){return{detailTable:[{},{},{},{},{},{}],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:p,detailParamList:t}){},onBeforeSubmit({formData:p,detailParamList:t,taskComment3:_},r){return new Promise((d,l)=>{try{d()}catch(i){l(i)}})},getSummaries(p=[],t){return p.reduce((r,d)=>{const l=Number(d[t]);return isNaN(l)?Number(Number(r).toFixed(2)):Number(Number(r+l).toFixed(2))},0)}}},W={class:"one-line"},U={class:"form-info"},E={class:"form-info"},I={class:"form-table"},T={key:0,rowspan:"6"},z={class:"comment-wp"},Q={class:"textarea-wp"},R={class:"footer-input"},X={class:"form-info"},Y={class:"form-info"},Z={class:"form-info"},j={class:"form-info"};function q(p,t,_,r,d,l){const i=N("DocumentPart"),k=N("van-field"),C=N("FormTemplate");return f(),L(C,{ref:"FormTemplate",nature:"资金","on-after-init":l.onAfterInit,"on-before-submit":l.onBeforeSubmit,"detail-table":d.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:d.attachmentDesc},{default:m(({formData:s,formTable:a,baseObj:u,uploadAccept:h,taskStart:c,taskComment2:v,taskComment3:D,taskComment4:g,taskComment5:y})=>[b(i,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:s.constructionDeptName,deptOptions:u.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!c},{default:m(()=>[e("div",W,[t[0]||(t[0]=e("span",{style:{"padding-left":"2em"}},"我方今提交",-1)),e("span",U,n(s.projectName),1),t[1]||(t[1]=e("span",null,"工程的",-1)),e("span",E,n(s.field1),1),t[2]||(t[2]=e("span",null,"请贵方审核。",-1))]),e("div",I,[e("table",null,[t[4]||(t[4]=e("thead",null,[e("tr",null,[e("th",{colspan:"1",rowspan:"1"},"年"),e("th",{colspan:"1",rowspan:"1"},"月"),e("th",{colspan:"1",rowspan:"1"},"工程预付款和工程材料预付款（元）"),e("th",{colspan:"1",rowspan:"1"},"完成工作量付款（元）"),e("th",{colspan:"1",rowspan:"1"},"质量保证金扣留（元）"),e("th",{colspan:"1",rowspan:"1"},"预付款扣还（元）"),e("th",{colspan:"1",rowspan:"1"},"其他（元）"),e("th",{colspan:"1",rowspan:"1"},"应得付款（元）")])],-1)),e("tbody",null,[(f(!0),w(B,null,x(a||[],(o,V)=>(f(),w("tr",{key:V},[V==0?(f(),w("td",T,n(o.field1),1)):O("",!0),e("td",null,n(o.field2),1),e("td",null,n(o.field3),1),e("td",null,n(o.field4),1),e("td",null,n(o.field5),1),e("td",null,n(o.field6),1),e("td",null,n(o.field7),1),e("td",null,n(o.field8),1)]))),128))]),e("tfoot",null,[e("tr",null,[t[3]||(t[3]=e("th",{colspan:"2"},"合计",-1)),e("td",null,n(l.getSummaries(a,"field3")),1),e("td",null,n(l.getSummaries(a,"field4")),1),e("td",null,n(l.getSummaries(a,"field5")),1),e("td",null,n(l.getSummaries(a,"field6")),1),e("td",null,n(l.getSummaries(a,"field7")),1),e("td",null,n(l.getSummaries(a,"field8")),1)])])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),b(i,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:s.epcDeptName,deptOptions:u.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!c},{default:m(()=>[e("div",z,[t[5]||(t[5]=e("div",null,"EPC总承包项目部意见：",-1)),e("div",Q,[b(k,{modelValue:s.comment2,"onUpdate:modelValue":o=>s.comment2=o,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),b(i,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:u.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!c},{default:m(()=>t[6]||(t[6]=[e("div",{class:"comment-wp"},[e("div",null,"监理机构将另行签发审批意见。")],-1)])),_:2,__:[6]},1032,["deptValue","deptOptions","disabled"])]),footer:m(({formData:s,formTable:a,baseObj:u,uploadAccept:h,taskStart:c,taskComment2:v,taskComment3:D,taskComment4:g,taskComment5:y})=>[e("div",R,[t[7]||(t[7]=e("span",null,"说明：本表一式",-1)),e("span",X,n(s.num1),1),t[8]||(t[8]=e("span",null,"份，由承包人填写，监理机构签收后，发包人",-1)),e("span",Y,n(s.num2),1),t[9]||(t[9]=e("span",null,"份，监理机构",-1)),e("span",Z,n(s.num3),1),t[10]||(t[10]=e("span",null,"份，承包人",-1)),e("span",j,n(s.num4),1),t[11]||(t[11]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const re=F(A,[["render",q]]);export{re as default};
