import{F as P,D as k}from"./index-1be3ad72.js";import{_ as T}from"./index-4829f8e2.js";import{Q as _,R as L,X as F,V as a,k as m,U as e,Y as n}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const h={name:"CB35",components:{FormTemplate:P,DocumentPart:k},emits:[],props:{},setup(i,{attrs:t,slots:f,emit:d}){},data(){return{detailTable:[],attachmentDesc:"1、前期验收遗留问题处理情况。\n2、未处理遗留问题的处理措施计划。\n3、验收报告、资料。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:i,detailParamList:t}){},onBeforeSubmit({formData:i,detailParamList:t,taskComment3:f},d){return new Promise((l,o)=>{try{l()}catch(r){o(r)}})},getSummaries(i=[],t){return i.reduce((d,l)=>{const o=Number(l[t]);return isNaN(o)?Number(Number(d).toFixed(2)):Number(Number(d+o).toFixed(2))},0)}}},B={class:"one-line"},O={class:"form-info",style:{"padding-left":"2em"}},U={class:"form-info"},g={class:"form-info"},A={class:"form-info"},D={style:{margin:"20px 0"}},W={class:"cb-table"},z={rowspan:"2"},I={class:"cell"},S={class:"one-line"},E={class:"form-info"},Q={class:"one-line"},R={class:"form-info"},X={class:"one-line"},Y={class:"form-info"},j={class:"cell"},q={class:"form-info"},G={class:"cell"},H={class:"form-info"},J={class:"attachment-desc"},K={class:"comment-wp"},M={class:"textarea-wp"},Z={class:"footer-input"},$={class:"form-info"},ee={class:"form-info"},te={class:"form-info"},se={class:"form-info"};function ne(i,t,f,d,l,o){const r=_("van-field"),c=_("DocumentPart"),N=_("FormTemplate");return L(),F(N,{ref:"FormTemplate",nature:"验报","on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":l.detailTable,"is-show-confirm1":!1,nums:[6,1,1,4],attachmentDesc:l.attachmentDesc},{default:a(({formData:s,formTable:V,baseObj:u,uploadAccept:y,taskStart:p,taskComment2:v,taskComment3:C,taskComment4:w,taskComment5:x})=>[m(c,{deptLabel:"施工项目部：",deptProp:"constructionDeptName",deptValue:s.constructionDeptName,deptOptions:u.constructionDeptName,personLabel:"施工负责人：",labelWidth:"10em",disabled:!p},{default:a(()=>[e("div",B,[e("span",O,n(s.projectName),1),t[0]||(t[0]=e("span",null,"工程项目已于",-1)),e("span",U,n(s.field1),1),t[1]||(t[1]=e("span",null,"年",-1)),e("span",g,n(s.field2),1),t[2]||(t[2]=e("span",null,"月",-1)),e("span",A,n(s.field3),1),t[3]||(t[3]=e("span",null,"日完工，",-1)),t[4]||(t[4]=e("span",null,"未处理的遗留问题不影响本次验收评定",-1)),t[5]||(t[5]=e("span",null,"并编制了处理措施计划，",-1)),t[6]||(t[6]=e("span",null,"验收报告，资料已准备就绪，",-1)),t[7]||(t[7]=e("span",null," 现申请验收。",-1))]),e("div",D,[e("table",W,[e("tbody",null,[e("tr",null,[e("td",z,[e("div",I,[e("div",S,[e("span",E,n(s.field4),1),t[8]||(t[8]=e("span",null,"完工验收",-1))]),e("div",Q,[e("span",R,n(s.field5),1),t[9]||(t[9]=e("span",null,"验收",-1))]),e("div",X,[e("span",Y,n(s.field6),1),t[10]||(t[10]=e("span",null,"验收",-1))])])]),t[11]||(t[11]=e("th",null,[e("div",{class:"cell"},"验收工程名称、编码")],-1)),t[12]||(t[12]=e("th",null,[e("div",{class:"cell"},"申请验收时间")],-1))]),e("tr",null,[e("td",null,[e("div",j,[e("span",q,n(s.field7),1)])]),e("td",null,[e("div",G,[e("span",H,n(s.field8),1)])])])])])]),e("div",J,[t[13]||(t[13]=e("div",null,"附件：",-1)),m(r,{modelValue:s.attachmentDesc,"onUpdate:modelValue":b=>s.attachmentDesc=b,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!p},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),m(c,{deptLabel:"总承包项目部：",deptProp:"epcDeptName",deptValue:s.epcDeptName,deptOptions:u.epcDeptName,personLabel:"负责人：",labelWidth:"10em",disabled:!p},{default:a(()=>[e("div",K,[t[14]||(t[14]=e("div",null,"EPC总承包项目部意见：",-1)),e("div",M,[m(r,{modelValue:s.comment2,"onUpdate:modelValue":b=>s.comment2=b,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),m(c,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:u.supervisionDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!p},{default:a(()=>t[15]||(t[15]=[e("div",{class:"comment-wp"},[e("div",null,"监理机构将另行签发审核意见。")],-1)])),_:2,__:[15]},1032,["deptValue","deptOptions","disabled"])]),footer:a(({formData:s,formTable:V,baseObj:u,uploadAccept:y,taskStart:p,taskComment2:v,taskComment3:C,taskComment4:w,taskComment5:x})=>[e("div",Z,[t[16]||(t[16]=e("span",null,"说明：本表一式",-1)),e("span",$,n(s.num1),1),t[17]||(t[17]=e("span",null,"份，由承包人填写，监理机构签收后，发包人",-1)),e("span",ee,n(s.num2),1),t[18]||(t[18]=e("span",null,"份，监理机构",-1)),e("span",te,n(s.num3),1),t[19]||(t[19]=e("span",null,"份，承包人",-1)),e("span",se,n(s.num4),1),t[20]||(t[20]=e("span",null,"份。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const Ve=T(h,[["render",ne]]);export{Ve as default};
