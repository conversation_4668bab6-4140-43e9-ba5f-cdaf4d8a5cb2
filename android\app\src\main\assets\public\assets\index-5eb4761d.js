import{F as _,D as k}from"./index-a831f9da.js";import{_ as w}from"./index-4829f8e2.js";import{Q as y,R as T,X as g,V as m,U as t,Y as e}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const C={name:"JL31",components:{FormTemplate:_,DocumentPart:k},emits:[],props:{},setup(i,{attrs:l,slots:a,emit:r}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:i,detailParamList:l}){},onBeforeSubmit({formData:i,detailParamList:l,taskStart:a},r){return new Promise((o,n)=>{try{o()}catch(d){n(d)}})}}},x={class:"jl-table"},S={class:"cell"},B={colspan:"3"},F={class:"cell"},A={class:"cell"},P={class:"cell"},j={class:"cell"},L={colspan:"5"},I={class:"cell"},O={colspan:"5"},V={class:"cell"},D={colspan:"5"},J={class:"cell"},N={colspan:"5"},Q={class:"cell"},R={colspan:"5"},U={class:"cell"};function X(i,l,a,r,o,n){const d=y("FormTemplate");return T(),g(d,{ref:"FormTemplate",nature:"安检","on-after-init":n.onAfterInit,"on-before-submit":n.onBeforeSubmit,"detail-table":o.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:o.attachmentDesc},{default:m(({formData:s,formTable:c,baseObj:p,uploadAccept:u,taskStart:f,taskComment2:v,taskComment3:b,taskComment4:h})=>[t("table",x,[l[11]||(l[11]=t("colgroup",null,[t("col",{width:"50"}),t("col",{"min-width":"40"}),t("col",{width:"55"}),t("col",{"min-width":"40"}),t("col",{width:"55"}),t("col",{"min-width":"40"})],-1)),t("tbody",null,[t("tr",null,[l[0]||(l[0]=t("th",null,[t("div",{class:"cell"},"日期")],-1)),t("td",null,[t("div",S,e(s.field1),1)]),l[1]||(l[1]=t("th",null,[t("div",{class:"cell"},"检查人")],-1)),t("td",B,[t("div",F,e(s.field2),1)])]),t("tr",null,[l[2]||(l[2]=t("th",null,[t("div",{class:"cell"},"时间")],-1)),t("td",null,[t("div",A,e(s.field3),1)]),l[3]||(l[3]=t("th",null,[t("div",{class:"cell"},"天气")],-1)),t("td",null,[t("div",P,e(s.field4),1)]),l[4]||(l[4]=t("th",null,[t("div",{class:"cell"},"温度")],-1)),t("td",null,[t("div",j,e(s.field5),1)])]),t("tr",null,[l[5]||(l[5]=t("th",null,[t("div",{class:"cell"},"检查部位")],-1)),t("td",L,[t("div",I,e(s.field6),1)])]),t("tr",null,[l[6]||(l[6]=t("th",null,[t("div",{class:"cell"},"人员、设备、施工作业及环境和条件等")],-1)),t("td",O,[t("div",V,e(s.field7),1)])]),t("tr",null,[l[7]||(l[7]=t("th",null,[t("div",{class:"cell"},"危险品及危险源安全情况")],-1)),t("td",D,[t("div",J,e(s.field8),1)])]),t("tr",null,[l[8]||(l[8]=t("th",null,[t("div",{class:"cell"},"发现的安全隐患及消除隐患的监理指示")],-1)),t("td",N,[t("div",Q,e(s.field9),1)])]),t("tr",null,[l[9]||(l[9]=t("th",null,[t("div",{class:"cell"}," 承包人的安全措施及隐患消除情况（安全隐患未消除的，检查人必须上报） ")],-1)),t("td",R,[t("div",U,e(s.field10),1)])]),l[10]||(l[10]=t("tr",null,[t("td",{colspan:"6"},[t("div",{class:"cell"},[t("div",{style:{height:"50px"}}),t("div",{class:"part-div"},[t("div",{class:"part-sign"},[t("div",null,[t("span",null,"检查人："),t("span",null,"（签名）")]),t("div",null,[t("span",null,"日期："),t("span",{style:{"padding-left":"4em"}},"年"),t("span",{style:{"padding-left":"2em"}},"月"),t("span",{style:{"padding-left":"2em"}},"日")])])])])])],-1))])])]),footer:m(({formData:s,formTable:c,baseObj:p,uploadAccept:u,taskStart:f,taskComment2:v,taskComment3:b,taskComment4:h})=>l[12]||(l[12]=[t("div",{class:"footer-input"},[t("div",null,"说明：1、本表可用于监理人员安全检查的记录")],-1),t("div",{class:"footer-input"},[t("div",{style:{"text-indent":"3em"}},"2、本表单独汇编成册")],-1)])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ot=w(C,[["render",X]]);export{ot as default};
