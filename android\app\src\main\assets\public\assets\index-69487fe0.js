import{F as x,D as y}from"./index-a831f9da.js";import{_ as C}from"./index-4829f8e2.js";import{Q as d,R as P,X as L,V as p,k as r,U as e,Y as n}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const B={name:"JL05",components:{FormTemplate:x,DocumentPart:y},emits:[],props:{},setup(i,{attrs:t,slots:u,emit:a}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:i,detailParamList:t}){},onBeforeSubmit({formData:i,detailParamList:t},u){return new Promise((a,o)=>{try{a()}catch(l){o(l)}})}}},F={class:"one-line"},S={class:"form-info"},A={class:"form-info"},D={class:"form-info"},O={class:"form-info"},U={class:"form-info"},g={class:"comment-wp"},I={class:"textarea-wp"},W={class:"comment-wp"},z={class:"one-line"},J={class:"footer-input"},Q={class:"form-info"},R={class:"form-info"},X={class:"form-info"},Y={class:"form-info"};function j(i,t,u,a,o,l){const _=d("van-field"),f=d("DocumentPart"),v=d("FormTemplate");return P(),L(v,{ref:"FormTemplate",nature:"批复","on-after-init":l.onAfterInit,"on-before-submit":l.onBeforeSubmit,"detail-table":o.detailTable,"is-show-confirm1":!0,nums:[6,1,1,4],attachmentDesc:o.attachmentDesc},{default:p(({formData:s,formTable:V,baseObj:m,uploadAccept:k,taskStart:b,taskComment2:c,taskComment3:N,taskComment4:T})=>[r(f,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:s.supervisionDeptName,deptOptions:m.supervisionDeptName,personLabel:"总监理工程师/监理工程师：",labelWidth:"10em",disabled:!b},{default:p(()=>[e("div",F,[t[0]||(t[0]=e("span",{style:{"padding-left":"2em"}},"贵方于",-1)),e("span",S,n(s.field1),1),t[1]||(t[1]=e("span",null,"年",-1)),e("span",A,n(s.field2),1),t[2]||(t[2]=e("span",null,"月",-1)),e("span",D,n(s.field3),1),t[3]||(t[3]=e("span",null,"日报送的",-1)),e("span",O,n(s.field4),1),t[4]||(t[4]=e("span",null,"（文号",-1)),e("span",U,n(s.field5),1),t[5]||(t[5]=e("span",null,"），",-1)),t[6]||(t[6]=e("span",null,"经监理机构审核，",-1)),t[7]||(t[7]=e("span",null,"批复意见如下：",-1))]),e("div",g,[e("div",I,[r(_,{modelValue:s.comment2,"onUpdate:modelValue":w=>s.comment2=w,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!c},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),r(f,{deptLabel:"承包人：",deptProp:"epcDeptName",deptValue:s.epcDeptName,deptOptions:m.epcDeptName,personLabel:"签收人：",labelWidth:"10em",disabled:!0},{default:p(()=>[e("div",W,[e("div",z,[e("span",null,"今已收到"+n(s.supervisionSimpleName||"")+" [ "+n(s.annualDate)+" ] 批复 ",1),e("span",null,n(s.titleCode),1),t[8]||(t[8]=e("span",null,"号。",-1))])])]),_:2},1032,["deptValue","deptOptions"])]),footer:p(({formData:s,formTable:V,baseObj:m,uploadAccept:k,taskStart:b,taskComment2:c,taskComment3:N,taskComment4:T})=>[e("div",J,[t[9]||(t[9]=e("span",null,"说明：1、本表一式",-1)),e("span",Q,n(s.num1),1),t[10]||(t[10]=e("span",null,"份，由监理机构填写，承包人签收后，承包人",-1)),e("span",R,n(s.num2),1),t[11]||(t[11]=e("span",null,"份，监理机构",-1)),e("span",X,n(s.num3),1),t[12]||(t[12]=e("span",null,"份，发包人",-1)),e("span",Y,n(s.num4),1),t[13]||(t[13]=e("span",null,"份。",-1))]),t[14]||(t[14]=e("div",{class:"footer-input"},[e("span",{style:{"text-indent":"3em"}}," 2、一般批复由监理工程师签发，重要批复由总监理工程师签发。 ")],-1))]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const pt=C(B,[["render",j]]);export{pt as default};
