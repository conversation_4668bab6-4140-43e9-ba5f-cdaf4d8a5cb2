import{F,D as P}from"./index-a831f9da.js";import{_ as x}from"./index-4829f8e2.js";import{Q as p,R as d,X as B,V as u,k as h,U as t,Y as o,S as v,W as L,F as N}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const g={name:"JL23",components:{FormTemplate:F,DocumentPart:P},emits:[],props:{},setup(r,{attrs:e,slots:c,emit:m}){},data(){return{detailTable:[{},{},{},{},{},{},{},{},{},{},{},{}],attachmentDesc:"施工图纸核查意见（应由核查监理人员签字）。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:r,detailParamList:e}){},onBeforeSubmit({formData:r,detailParamList:e},c){return new Promise((m,a)=>{try{m()}catch(l){a(l)}})}}},A={class:"one-line"},S={class:"form-info"},O={class:"form-table"},U={class:"center"},I={class:"attachment-desc"},W={class:"footer-input"},j={class:"form-info"};function z(r,e,c,m,a,l){const D=p("van-field"),k=p("DocumentPart"),y=p("FormTemplate");return d(),B(y,{ref:"FormTemplate",nature:"图核","on-after-init":l.onAfterInit,"on-before-submit":l.onBeforeSubmit,"detail-table":a.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:a.attachmentDesc},{default:u(({formData:n,formTable:f,baseObj:b,uploadAccept:w,taskStart:i,taskComment2:V,taskComment3:C,taskComment4:T})=>[h(k,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:n.supervisionDeptName,deptOptions:b.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!i},{default:u(()=>[t("div",A,[e[0]||(e[0]=t("span",{style:{"padding-left":"2em"}},"经对以下图纸（共",-1)),t("span",S,o(n.field1),1),e[1]||(e[1]=t("span",null,"张）核查意见如下：",-1))]),t("div",O,[t("table",null,[e[2]||(e[2]=t("thead",null,[t("tr",null,[t("th",{colspan:"1",rowspan:"1"},"序号"),t("th",{colspan:"1",rowspan:"1"},"施工设计图纸名称"),t("th",{colspan:"1",rowspan:"1"},"图号"),t("th",{colspan:"1",rowspan:"1"},"核查人员"),t("th",{colspan:"1",rowspan:"1"},"备注")])],-1)),t("tbody",null,[(d(!0),v(N,null,L(f||[],(s,_)=>(d(),v("tr",{key:_},[t("td",U,o(_+1),1),t("td",null,o(s.field1),1),t("td",null,o(s.field2),1),t("td",null,o(s.field3),1),t("td",null,o(s.field4),1)]))),128))])])]),t("div",I,[e[3]||(e[3]=t("div",null,"附件：",-1)),h(D,{modelValue:n.attachmentDesc,"onUpdate:modelValue":s=>n.attachmentDesc=s,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:u(({formData:n,formTable:f,baseObj:b,uploadAccept:w,taskStart:i,taskComment2:V,taskComment3:C,taskComment4:T})=>[t("div",W,[e[4]||(e[4]=t("span",null,"说明：1、本表一式",-1)),t("span",j,o(n.num1),1),e[5]||(e[5]=t("span",null,"份，由监理机构填写并存档。",-1))]),e[6]||(e[6]=t("div",{class:"footer-input"},[t("div",{style:{"text-indent":"3em"}},[t("span",null,"2、各图号可以是单张号、连续号或区间号。")])],-1))]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ot=x(g,[["render",z]]);export{ot as default};
