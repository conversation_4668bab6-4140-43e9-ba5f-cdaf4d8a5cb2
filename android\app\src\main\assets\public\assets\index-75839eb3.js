import{F as V,D as B}from"./index-a831f9da.js";import{_ as D}from"./index-4829f8e2.js";import{Q as _,R as n,X as S,V as h,U as t,Y as a,S as m,W as k,F as y,k as A}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const L={name:"JL35",components:{FormTemplate:V,DocumentPart:B},emits:[],props:{},setup(i,{attrs:l,slots:p,emit:c}){},data(){return{detailTable:[{},{},{},{},{},{},{}],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:i,detailParamList:l}){},onBeforeSubmit({formData:i,detailParamList:l,taskStart:p},c){return new Promise((s,o)=>{try{s()}catch(r){o(r)}})}}},P={class:"jl-table"},j={colspan:"3"},U={class:"cell"},$={colspan:"3"},I={class:"cell"},N={colspan:"3"},O={class:"cell"},z={class:"cell"},E={colspan:"4"},J={class:"cell"};function Q(i,l,p,c,s,o){const r=_("van-field"),g=_("FormTemplate");return n(),S(g,{ref:"FormTemplate",nature:"内签","on-after-init":o.onAfterInit,"on-before-submit":o.onBeforeSubmit,"detail-table":s.detailTable,"is-show-confirm1":!1,"show-target":!1,attachmentDesc:s.attachmentDesc},{default:h(({formData:e,formTable:u,baseObj:w,uploadAccept:C,taskStart:f,taskComment2:T,taskComment3:x,taskComment4:F})=>[t("table",P,[l[6]||(l[6]=t("colgroup",null,[t("col",{width:"50"}),t("col",{"min-width":"50"}),t("col",{width:"50"}),t("col",{width:"50"})],-1)),t("tbody",null,[t("tr",null,[l[0]||(l[0]=t("th",null,[t("div",{class:"cell"},"事由")],-1)),t("td",j,[t("div",U,[t("span",null,a(e.field1),1)])])]),t("tr",null,[l[1]||(l[1]=t("th",null,[t("div",{class:"cell"},"会签内容")],-1)),t("td",$,[t("div",I,[t("span",null,a(e.field2),1)])])]),t("tr",null,[l[2]||(l[2]=t("th",null,[t("div",{class:"cell"},"依据、参考文件")],-1)),t("td",N,[t("div",O,[t("span",null,a(e.field3),1)])])]),l[5]||(l[5]=t("tr",null,[t("th",null,[t("div",{class:"cell"},"会签部门")]),t("th",null,[t("div",{class:"cell"},"部门意见")]),t("th",null,[t("div",{class:"cell"},"负责人签名")]),t("th",null,[t("div",{class:"cell"},"日期")])],-1)),(n(!0),m(y,null,k(u,(d,v)=>(n(),m("tr",{key:v},[(n(),m(y,null,k(4,b=>t("td",{key:"".concat(v,"_").concat(b)},[t("div",z,[t("span",null,a(d["field".concat(b)]),1)])])),64))]))),128)),t("tr",null,[t("td",E,[t("div",J,[l[3]||(l[3]=t("div",{style:{"padding-bottom":"10px"}},"会签意见：",-1)),A(r,{modelValue:e.field4,"onUpdate:modelValue":d=>e.field4=d,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!f},null,8,["modelValue","onUpdate:modelValue","readonly"]),l[4]||(l[4]=t("div",{class:"part-div"},[t("div",{class:"part-sign",style:{"padding-left":"100px"}},[t("div",null,[t("span",null,"总监理工程师："),t("span",null,"（签名）")]),t("div",null,[t("span",null,"日期："),t("span",{style:{"padding-left":"2em"}},"年"),t("span",{style:{"padding-left":"2em"}},"月"),t("span",{style:{"padding-left":"2em"}},"日")])])],-1))])])])])])]),footer:h(({formData:e,formTable:u,baseObj:w,uploadAccept:C,taskStart:f,taskComment2:T,taskComment3:x,taskComment4:F})=>l[7]||(l[7]=[t("div",{class:"footer-input"},[t("div",null,"说明：在监理机构作出决定之前需内部会签时，可用此表。")],-1)])),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const nt=D(L,[["render",Q]]);export{nt as default};
