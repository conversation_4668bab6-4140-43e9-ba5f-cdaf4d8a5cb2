import{F as C,a as k}from"./index-8d635ba7.js";import{F as w}from"./FormItemPicker-d3f69283.js";import{F as q}from"./FormItemPerson-bd0e3e57.js";import{U as P}from"./index-fc22947f.js";import{_ as V}from"./index-4829f8e2.js";import{Q as n,R as f,X as p,V as l,k as m,Z as d,B as v}from"./verder-361ae6c7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";const _={name:"MeetSummaryFlow",components:{FlowForm:C,FormItemPicker:w,FormItemPerson:q,UploadFiles:P,FormItemMultiplePerson:k},props:{},emits:[],data(){var o,r;return{type:((o=this.$route.query)==null?void 0:o.type)||"",taskKey:((r=this.$route.query)==null?void 0:r.taskKey)||"",entityName:"TechnologyMeetingSummary",modelKey:"meet_summary_flow",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-technology/form/query",submit:"/cybereng-technology/form/commit"},formData:{formKey:"MeetSummaryFlow",fkId:"",portalId:"",subProjectName:"",subProjectId:null,projectDeptCode:"",projectDeptName:"",moduleType:"",sendingName:"",sendingType:"",sendingCode:"",sendingTypeCode:"",createBy:"",userFullname:"",userTelephoneNum:"",prjDepName:"",prjDepCode:"",fillingDate:this.$dayjs().format("YYYY-MM-DD"),isConfirm1:!1,approverUsername1:"",approverFullname1:"",isConfirm2:!1,approverUsername2:"",approverFullname2:"",isConfirm3:!1,approverUsername3:"",approverFullname3:"",notifyMethod:[],attachment:null,attachmentFiles:1,summaryAttachment:null,summaryAttachmentNum:1}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},canEdit0(){return this.type==="add"||this.taskKey==="UserTask_0"},yesOrNoList(){return[{text:"是",value:!0},{text:"否",value:!1}]}},mounted(){this.initForm()},methods:{async initForm(){if(this.type==="add"){this.formData={...this.formData,...this.$route.query};const{userName:o="",userFullname:r="",orgList:i=[],phone:h=""}=this.user||{},e=i.find(y=>{var u;return y.portalId==((u=this.portal)==null?void 0:u.id)})||i[0],a=(e==null?void 0:e.name)||"",s=(e==null?void 0:e.orgNo)||"";this.formData.createBy=o,this.formData.userFullname=r,this.formData.prjDepName=a,this.formData.prjDepCode=s,this.formData.userTelephoneNum=h}else this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,o=>{const{detailParamList:r=[],entityObject:i}=o;this.detailParamList=r||[],this.formData={...this.formData,...i},this.formData.notifyMethod=i.notifyMethod?i.notifyMethod.split(","):[],console.log("formData",this.formData,this.detailParamList)})})},async onDraft(){try{const o={...this.formData};await this.$refs.FlowForm.onSaveDraft(this.service.submit,o)}catch(o){console.log(o)}},async onSubmit(){try{await this.$refs.form.validate();const o={...this.formData,notifyMethod:this.formData.notifyMethod.join(",")};await this.$refs.FlowForm.onSubmit(this.service.submit,o)}catch(o){console.log(o)}},getSubProjectNode(o){this.formData.portalId=o&&o.portalId||""},clearUserChoose(o){this.formData["approverUsername".concat(o)]="",this.formData["approverFullname".concat(o)]=""},async updateFiles(){return new Promise(async(o,r)=>{try{this.$refs.notifyAttachmentFiles&&await this.$refs.notifyAttachmentFiles.update(),this.$refs.summaryAttachmentNum&&await this.$refs.summaryAttachmentNum.update(),this.$refs.attachmentFiles&&await this.$refs.attachmentFiles.update(),o()}catch(i){r()}})},afterSubmit(o,r){this.updateFiles()}}};function I(o,r,i,h,e,a){const s=n("van-field"),y=n("UploadFiles"),u=n("van-cell-group"),D=n("FormItemPicker"),F=n("FormItemMultiplePerson"),b=n("FormItemPerson"),c=n("van-checkbox"),g=n("van-checkbox-group"),N=n("van-form"),U=n("FlowForm");return f(),p(U,{ref:"FlowForm","model-key":e.modelKey,"form-key":o.formKey,"entity-name":e.entityName,"detail-param-list":e.detailParamList,"detail-entity-name-list":e.detailEntityNameList,onDraftClick:a.onDraft,onSubmitClick:a.onSubmit,onAfterSubmit:a.afterSubmit},{default:l(()=>[m(N,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:l(()=>[m(u,{border:!1},{default:l(()=>[m(s,{modelValue:e.formData.subProjectName,"onUpdate:modelValue":r[0]||(r[0]=t=>e.formData.subProjectName=t),label:"子工程",placeholder:"请选择",readonly:"",required:""},null,8,["modelValue"]),m(s,{modelValue:e.formData.projectDeptName,"onUpdate:modelValue":r[1]||(r[1]=t=>e.formData.projectDeptName=t),label:"项目部",placeholder:"请选择",readonly:"",required:""},null,8,["modelValue"]),m(s,{modelValue:e.formData.sendingType,"onUpdate:modelValue":r[2]||(r[2]=t=>e.formData.sendingType=t),label:"会议类型",placeholder:"请选择",readonly:"",required:""},null,8,["modelValue"]),m(s,{modelValue:e.formData.sendingName,"onUpdate:modelValue":r[3]||(r[3]=t=>e.formData.sendingName=t),label:"会议名称",placeholder:"请输入",required:"",readonly:""},null,8,["modelValue"]),m(s,{label:"会议纪要","label-align":"top","input-align":"left",required:"",rules:[{required:!0,message:"请上传"}],modelValue:e.formData.summaryAttachmentNum,"onUpdate:modelValue":r[6]||(r[6]=t=>e.formData.summaryAttachmentNum=t)},{input:l(()=>[m(y,{ref:"summaryAttachmentNum",g9s:e.formData.summaryAttachment,"onUpdate:g9s":r[4]||(r[4]=t=>e.formData.summaryAttachment=t),files:e.formData.summaryAttachmentNum,"onUpdate:files":r[5]||(r[5]=t=>e.formData.summaryAttachmentNum=t),accept:"*",multiple:!0,readonly:e.type==="view"||!a.canEdit0},null,8,["g9s","files","readonly"])]),_:1},8,["modelValue"])]),_:1}),m(u,{border:!1},{default:l(()=>[m(D,{label:"会议纪要是否核稿","label-width":"10em",value:e.formData.isConfirm1,"onUpdate:value":r[7]||(r[7]=t=>e.formData.isConfirm1=t),columns:[...a.yesOrNoList],required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!a.canEdit0,onChange:r[8]||(r[8]=t=>a.clearUserChoose("1"))},null,8,["value","columns","readonly"]),e.formData.isConfirm1?(f(),p(F,{key:0,label:"核稿人",userName:e.formData.approverUsername1,"onUpdate:userName":r[9]||(r[9]=t=>e.formData.approverUsername1=t),userFullname:e.formData.approverFullname1,"onUpdate:userFullname":r[10]||(r[10]=t=>e.formData.approverFullname1=t),title:"请选择核稿人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!a.canEdit0},null,8,["userName","userFullname","readonly"])):d("",!0)]),_:1}),m(u,{border:!1},{default:l(()=>[m(D,{label:"会议纪要是否签发","label-width":"10em",value:e.formData.isConfirm2,"onUpdate:value":r[11]||(r[11]=t=>e.formData.isConfirm2=t),columns:[...a.yesOrNoList],required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!a.canEdit0,onChange:r[12]||(r[12]=t=>a.clearUserChoose("2"))},null,8,["value","columns","readonly"]),e.formData.isConfirm2?(f(),p(b,{key:0,label:"签发人",userName:e.formData.approverUsername2,"onUpdate:userName":r[13]||(r[13]=t=>e.formData.approverUsername2=t),userFullname:e.formData.approverFullname2,"onUpdate:userFullname":r[14]||(r[14]=t=>e.formData.approverFullname2=t),title:"请选择签发人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!a.canEdit0},null,8,["userName","userFullname","readonly"])):d("",!0)]),_:1}),m(u,{border:!1},{default:l(()=>[m(D,{label:"会议纪要是否传阅","label-width":"10em",value:e.formData.isConfirm3,"onUpdate:value":r[15]||(r[15]=t=>e.formData.isConfirm3=t),columns:[...a.yesOrNoList],required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!a.canEdit0,onChange:r[16]||(r[16]=t=>a.clearUserChoose("3"))},null,8,["value","columns","readonly"]),e.formData.isConfirm3?(f(),p(F,{key:0,label:"传阅人",userName:e.formData.approverUsername3,"onUpdate:userName":r[17]||(r[17]=t=>e.formData.approverUsername3=t),userFullname:e.formData.approverFullname3,"onUpdate:userFullname":r[18]||(r[18]=t=>e.formData.approverFullname3=t),title:"请选择传阅人",required:"",rules:[{required:!0,message:"请选择"}],readonly:e.type==="view"||!a.canEdit0},null,8,["userName","userFullname","readonly"])):d("",!0)]),_:1}),m(u,{border:!1},{default:l(()=>[a.canEdit0?(f(),p(s,{key:0,name:"checkboxGroup",label:"通知方式",class:"check-box-color",required:"",rules:[{required:!0,message:"请选择"}]},{input:l(()=>[m(g,{modelValue:e.formData.notifyMethod,"onUpdate:modelValue":r[19]||(r[19]=t=>e.formData.notifyMethod=t),direction:"horizontal",shape:"square",disabled:e.type==="view"||!a.canEdit0},{default:l(()=>[m(c,{name:"系统通知"},{default:l(()=>r[22]||(r[22]=[v("系统通知")])),_:1,__:[22]}),m(c,{name:"短信通知"},{default:l(()=>r[23]||(r[23]=[v("短信通知")])),_:1,__:[23]})]),_:1},8,["modelValue","disabled"])]),_:1})):d("",!0),e.formData.attachment&&e.formData.attachmentFiles||a.canEdit0?(f(),p(s,{key:1,label:"上传附件","label-align":"top","input-align":"left"},{input:l(()=>[m(y,{ref:"attachmentFiles",g9s:e.formData.attachment,"onUpdate:g9s":r[20]||(r[20]=t=>e.formData.attachment=t),files:e.formData.attachmentFiles,"onUpdate:files":r[21]||(r[21]=t=>e.formData.attachmentFiles=t),accept:"*",multiple:!0,readonly:e.type==="view"||!a.canEdit0},null,8,["g9s","files","readonly"])]),_:1})):d("",!0)]),_:1})]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit"])}const G=V(_,[["render",I],["__scopeId","data-v-302a56f6"]]);export{G as default};
