import{F as T,D as N}from"./index-a831f9da.js";import{_ as P}from"./index-4829f8e2.js";import{Q as c,R as C,X as L,V as p,k as n,U as t,Y as f}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const U={name:"JL07",components:{FormTemplate:T,DocumentPart:N},emits:[],props:{},setup(d,{attrs:e,slots:b,emit:i}){},data(){return{detailTable:[],attachmentDesc:""}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:d,detailParamList:e}){},onBeforeSubmit({formData:d,detailParamList:e},b){return new Promise((i,s)=>{try{i()}catch(m){s(m)}})}}},k={class:"comment-wp"},B={class:"textarea-wp"},D={class:"comment-wp"},F={class:"textarea-wp"},A={class:"comment-wp"},O={class:"textarea-wp"},z={class:"footer-input"},g={class:"form-info"},I={class:"form-info"},W={class:"form-info"};function J(d,e,b,i,s,m){const r=c("van-field"),_=c("DocumentPart"),v=c("FormTemplate");return C(),L(v,{ref:"FormTemplate",nature:"报告","employer-target":!0,"on-after-init":m.onAfterInit,"on-before-submit":m.onBeforeSubmit,"detail-table":s.detailTable,"is-show-confirm1":!1,attachmentDesc:s.attachmentDesc},{default:p(({formData:o,formTable:y,baseObj:u,uploadAccept:w,taskStart:l,taskComment2:x,taskComment3:h,taskComment4:V})=>[n(_,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:o.supervisionDeptName,deptOptions:u.supervisionDeptName,personLabel:"总监理工程师/监理工程师：",labelWidth:"10em",disabled:!l},{default:p(()=>[t("div",k,[e[0]||(e[0]=t("div",null,"事由：",-1)),t("div",B,[n(r,{modelValue:o.field1,"onUpdate:modelValue":a=>o.field1=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),t("div",D,[e[1]||(e[1]=t("div",null,"报告内容：",-1)),t("div",F,[n(r,{modelValue:o.field2,"onUpdate:modelValue":a=>o.field2=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!l},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"]),n(_,{deptLabel:"发包人：",deptProp:"employerName",deptValue:o.employerName,deptOptions:u.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!l},{default:p(()=>[t("div",A,[e[2]||(e[2]=t("div",null,"就贵方报告事宜答复如下：",-1)),t("div",O,[n(r,{modelValue:o.comment4,"onUpdate:modelValue":a=>o.comment4=a,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!V},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:p(({formData:o,formTable:y,baseObj:u,uploadAccept:w,taskStart:l,taskComment2:x,taskComment3:h,taskComment4:V})=>[t("div",z,[e[3]||(e[3]=t("span",null,"说明：1、本通知一式",-1)),t("span",g,f(o.num1),1),e[4]||(e[4]=t("span",null,"份，由监理机构填写，发包人批复后留",-1)),t("span",I,f(o.num2),1),e[5]||(e[5]=t("span",null,"份，退回监理机构",-1)),t("span",W,f(o.num3),1),e[6]||(e[6]=t("span",null,"份。",-1))]),e[7]||(e[7]=t("div",{class:"footer-input"},[t("span",{style:{"text-indent":"3em"}},"2、本表可用于监理机构认为需报请发包人批示的各项事宜。")],-1))]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const te=P(U,[["render",J]]);export{te as default};
