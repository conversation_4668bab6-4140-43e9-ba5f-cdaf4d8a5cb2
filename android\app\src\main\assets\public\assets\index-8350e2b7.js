import{h,_ as k}from"./index-4829f8e2.js";import{F as U}from"./index-8d635ba7.js";import{F as c}from"./FormItemPicker-d3f69283.js";import{F as E}from"./FormItemDate-ba00d9d5.js";import{F as R}from"./FormItemCalendar-905fde75.js";import{F as S}from"./FormItemPerson-bd0e3e57.js";import{F as I}from"./FormItemCoord-9e82e1bf.js";import{U as w}from"./index-fc22947f.js";import{Q as m,R as p,X as D,V as u,k as a,Z as y}from"./verder-361ae6c7.js";import"./vant-91101745.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./formKeys-f34a583f.js";import"./validate-2249584f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";function q(n){return h({url:"/cybereng-safety/safety/hiddenDanger/delayTask",method:"get",params:n})}function x(n){return h({url:"/cybereng-safety/safety/hiddenDanger/sendMessage",method:"get",params:n})}function P(n){return h({url:"/cybereng-safety/yxst/safety/hiddenDanger",method:"put",params:n})}const V={name:"SafetyHiddenDangerSupervisor",components:{FlowForm:U,FormItemPicker:c,FormItemDate:E,FormItemCalendar:R,FormItemPerson:S,FormItemCoord:I,UploadFiles:w},props:{},emits:[],setup(n,{attrs:r,slots:d,emit:s}){},data(){var n,r;return{type:((n=this.$route.query)==null?void 0:n.type)||"",taskKey:((r=this.$route.query)==null?void 0:r.taskKey)||"",entityName:"SafetyHiddenDanger",formKey:"SafetyHiddenDangerSupervisor",modelKey:"safety_hidden_danger_flow_jl",detailParamList:[],detailEntityNameList:[],service:{query:"/cybereng-safety/form/query",submit:"/cybereng-safety/form/commit"},formData:{type:"2",id:void 0,portalId:void 0,subProjectId:"",subProjectName:"",unitEngineeringId:"",unitEngineeringName:"",divisionEngineeringId:"",divisionEngineeringName:"",longitude:"",latitude:"",hiddenDangerCode:void 0,hiddenDangerLevel:"",hiddenDangerCategory:"",rectifyDate:"",overdueState:"",hiddenDangerContent:"",beforeFileToken:"",hiddenDangerReportor:"",hiddenDangerReportorFullname:"",hiddenDangerReportorDeptName:"",hiddenDangerReportorDeptCode:"",hiddenDangerChecker:"",hiddenDangerCheckerFullname:"",hiddenDangerCheckerDeptName:"",hiddenDangerCheckerDeptCode:"",hiddenDangerRectifier:"",hiddenDangerRectifierFullname:"",hiddenDangerRectifierDeptName:"",hiddenDangerRectifierDeptCode:"",hiddenDangerSupervisor:"",hiddenDangerSupervisorFullname:"",hiddenDangerSupervisorDeptName:"",hiddenDangerSupervisorDeptCode:"",hiddenDangerRectifyApprover:"",hiddenDangerRectifyApproverFullname:"",hiddenDangerRectifyApproverDeptName:"",hiddenDangerRectifyApproverDeptCode:"",rectifyState:"待整改",rectifyMeasures:"",rectifySituation:"",afterFileToken:"",finishDate:""}}},computed:{portal(){return this.$store.PORTAL},portalId(){return this.$store.PORTAL_ID},user(){return this.$store.USER_INFO},subProjectList(){return this.$store.NODES_TREE},unitEngineeringList(){if(this.portal.type==1){if(!this.formData.subProjectId)return[];const n=this.subProjectList.find(r=>r.id==this.formData.subProjectId);return n?n.children||[]:[]}else return this.subProjectList[0]?this.subProjectList[0].children||[]:[]},divisionEngineeringList(){if(!this.formData.unitEngineeringId)return[];const n=this.unitEngineeringList.find(r=>r.id==this.formData.unitEngineeringId);return n?n.children||[]:[]},canEdit0(){return this.type==="add"||this.taskKey==="UserTask_0"},canEdit1(){return this.type==="execute"&&this.taskKey==="UserTask_1"},canEdit2(){return this.type==="execute"&&this.taskKey==="UserTask_2"}},watch:{},created(){},mounted(){this.initForm()},methods:{copyCallBack(n){let r=["hiddenDangerContent","latitude","longitude","beforeFileToken"];this.formData={...n},r.forEach(d=>{this.formData[d]=""})},async initForm(){if(this.type==="add"){if(this.formData.portalId=this.portalId,this.portal.type!=1){const o=this.subProjectList.find(l=>l.portalId==this.portal.id);this.formData.subProjectId=o==null?void 0:o.id,this.formData.subProjectName=o==null?void 0:o.nodeName}const{userName:n="",userFullname:r="",orgList:d=[]}=this.user||{},s=d.find(o=>{var l;return o.portalId==((l=this.portal)==null?void 0:l.id)})||d[0],e=(s==null?void 0:s.name)||"",i=(s==null?void 0:s.orgNo)||"";this.formData.hiddenDangerReportor=n,this.formData.hiddenDangerReportorFullname=r,this.formData.hiddenDangerReportorDeptName=e,this.formData.hiddenDangerReportorDeptCode=i,this.formData.hiddenDangerRectifyApprover=n,this.formData.hiddenDangerRectifyApproverFullname=r,this.formData.hiddenDangerRectifyApproverDeptName=e,this.formData.hiddenDangerRectifyApproverDeptCode=i}else this.$nextTick(()=>{this.$refs.FlowForm.onInit(this.service.query,n=>{const{detailParamList:r=[],entityObject:d}=n;this.detailParamList=r,this.formData={...this.formData,...d}})})},async onDraft(){try{const n={...this.formData,rectifyState:"暂存"};this.$refs.FlowForm.onSaveDraft(this.service.submit,n)}catch(n){console.log(n)}},async onSubmit(){try{await this.$refs.form.validate();let n="待整改";this.taskKey==="UserTask_0"||this.taskKey==="UserTask_1"?n="待整改":this.taskKey==="UserTask_2"||this.taskKey==="UserTask_3"?n="待审核":this.taskKey==="UserTask_4"&&(n="整改完成",this.formData.finishDate=this.$dayjs().format("YYYY-MM-DD HH:mm:ss"));const r={...this.formData,rectifyState:n};this.$refs.FlowForm.onSubmit(this.service.submit,r)}catch(n){console.log(n)}},async updateFiles(){return new Promise(async(n,r)=>{try{this.$refs.beforeFiles&&await this.$refs.beforeFiles.update(),this.$refs.afterFiles&&await this.$refs.afterFiles.update(),n()}catch(d){r()}})},afterSubmit(n,r){this.updateFiles(),n==="submit"&&(this.taskKey=="UserTask_1"&&q({id:this.formData.id}),(this.taskKey=="UserTask_1"||this.taskKey=="UserTask_2")&&x({id:this.formData.id}),this.formData.id&&P({id:this.formData.id}))},handleSubProjectChange(n={}){this.formData.unitEngineeringId="",this.formData.unitEngineeringName="",this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""},handleUnitEngineeringChange(n={}){this.formData.divisionEngineeringId="",this.formData.divisionEngineeringName=""}}};function T(n,r,d,s,e,i){const o=m("van-field"),l=m("FormItemPicker"),F=m("FormItemCoord"),C=m("FormItemCalendar"),g=m("van-cell-group"),v=m("UploadFiles"),f=m("FormItemPerson"),N=m("van-form"),b=m("FlowForm");return p(),D(b,{ref:"FlowForm","model-key":e.modelKey,"form-key":e.formKey,"entity-name":e.entityName,"detail-param-list":e.detailParamList,"detail-entity-name-list":e.detailEntityNameList,onDraftClick:i.onDraft,onSubmitClick:i.onSubmit,onAfterSubmit:i.afterSubmit,onCopyCallBack:i.copyCallBack},{default:u(()=>[a(N,{ref:"form","label-width":"7em","input-align":"right","error-message-align":"right"},{default:u(()=>[a(g,{border:!1},{default:u(()=>[e.formData.hiddenDangerCode?(p(),D(o,{key:0,modelValue:e.formData.hiddenDangerCode,"onUpdate:modelValue":r[0]||(r[0]=t=>e.formData.hiddenDangerCode=t),label:"隐患整改单号",placeholder:"审批完成后自动生成",readonly:""},null,8,["modelValue"])):y("",!0),a(l,{label:"子工程",value:e.formData.subProjectId,"onUpdate:value":r[1]||(r[1]=t=>e.formData.subProjectId=t),text:e.formData.subProjectName,"onUpdate:text":r[2]||(r[2]=t=>e.formData.subProjectName=t),columns:[...i.subProjectList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择子工程",required:"",rules:[{required:!0,message:"请选择子工程"}],readonly:i.portal.type!=1||e.type==="view"||!i.canEdit0,onChange:i.handleSubProjectChange},null,8,["value","text","columns","readonly","onChange"]),a(l,{label:"单位工程",value:e.formData.unitEngineeringId,"onUpdate:value":r[3]||(r[3]=t=>e.formData.unitEngineeringId=t),text:e.formData.unitEngineeringName,"onUpdate:text":r[4]||(r[4]=t=>e.formData.unitEngineeringName=t),columns:[...i.unitEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择单位工程",required:"",rules:[{required:!0,message:"请选择单位工程"}],readonly:e.type==="view"||!i.canEdit0,onChange:i.handleUnitEngineeringChange},null,8,["value","text","columns","readonly","onChange"]),a(l,{label:"分部工程",value:e.formData.divisionEngineeringId,"onUpdate:value":r[5]||(r[5]=t=>e.formData.divisionEngineeringId=t),text:e.formData.divisionEngineeringName,"onUpdate:text":r[6]||(r[6]=t=>e.formData.divisionEngineeringName=t),columns:[...i.divisionEngineeringList],"columns-field-names":{text:"nodeName",value:"id",children:"none"},title:"选择分部工程",required:"",rules:[{required:!0,message:"请选择分部工程"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","text","columns","readonly"]),a(F,{label:"定位",longitude:e.formData.longitude,"onUpdate:longitude":r[7]||(r[7]=t=>e.formData.longitude=t),latitude:e.formData.latitude,"onUpdate:latitude":r[8]||(r[8]=t=>e.formData.latitude=t),title:"选择定位",readonly:e.type==="view"||!i.canEdit0},null,8,["longitude","latitude","readonly"]),a(l,{label:"隐患级别",value:e.formData.hiddenDangerLevel,"onUpdate:value":r[9]||(r[9]=t=>e.formData.hiddenDangerLevel=t),"dict-name":"hidden_danger_level",title:"选择隐患级别",required:"",rules:[{required:!0,message:"请选择隐患级别"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),a(l,{label:"隐患分类",value:e.formData.hiddenDangerCategory,"onUpdate:value":r[10]||(r[10]=t=>e.formData.hiddenDangerCategory=t),"dict-name":"hidden_danger_category",title:"选择隐患分类",required:"",rules:[{required:!0,message:"请选择隐患分类"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),a(C,{label:"整改期限",value:e.formData.rectifyDate,"onUpdate:value":r[11]||(r[11]=t=>e.formData.rectifyDate=t),title:"选择整改期限",required:"",rules:[{required:!0,message:"请选择整改期限"}],readonly:e.type==="view"||!i.canEdit0},null,8,["value","readonly"]),e.type!=="add"?(p(),D(o,{key:1,modelValue:e.formData.rectifyState,"onUpdate:modelValue":r[12]||(r[12]=t=>e.formData.rectifyState=t),label:"整改状态",placeholder:"",readonly:""},null,8,["modelValue"])):y("",!0),e.type!=="add"?(p(),D(o,{key:2,modelValue:e.formData.overdueState,"onUpdate:modelValue":r[13]||(r[13]=t=>e.formData.overdueState=t),label:"逾期情况",placeholder:"",readonly:""},null,8,["modelValue"])):y("",!0)]),_:1}),a(g,{border:!1},{default:u(()=>[a(o,{label:"隐患内容",modelValue:e.formData.hiddenDangerContent,"onUpdate:modelValue":r[14]||(r[14]=t=>e.formData.hiddenDangerContent=t),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请输入隐患内容",required:"",rules:[{required:!0,message:"请输入隐患内容"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit0},null,8,["modelValue","readonly"]),a(o,{label:"附件照片","label-align":"top","input-align":"left"},{input:u(()=>[a(v,{ref:"beforeFiles",g9s:e.formData.beforeFileToken,"onUpdate:g9s":r[15]||(r[15]=t=>e.formData.beforeFileToken=t),accept:"image/*",multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:e.type==="view"||!i.canEdit0},null,8,["g9s","readonly"])]),_:1})]),_:1}),a(g,{border:!1},{default:u(()=>[a(f,{label:"监理发起人",userName:e.formData.hiddenDangerReportor,"onUpdate:userName":r[16]||(r[16]=t=>e.formData.hiddenDangerReportor=t),userFullname:e.formData.hiddenDangerReportorFullname,"onUpdate:userFullname":r[17]||(r[17]=t=>e.formData.hiddenDangerReportorFullname=t),deptName:e.formData.hiddenDangerReportorDeptName,"onUpdate:deptName":r[18]||(r[18]=t=>e.formData.hiddenDangerReportorDeptName=t),deptCode:e.formData.hiddenDangerReportorDeptCode,"onUpdate:deptCode":r[19]||(r[19]=t=>e.formData.hiddenDangerReportorDeptCode=t),title:"选择监理发起人",required:"",rules:[{required:!0,message:"请选择监理发起人"}],readonly:!0},null,8,["userName","userFullname","deptName","deptCode"]),a(o,{modelValue:e.formData.hiddenDangerReportorDeptName,"onUpdate:modelValue":r[20]||(r[20]=t=>e.formData.hiddenDangerReportorDeptName=t),label:"上报人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),a(f,{label:"总包确认人",userName:e.formData.hiddenDangerChecker,"onUpdate:userName":r[21]||(r[21]=t=>e.formData.hiddenDangerChecker=t),userFullname:e.formData.hiddenDangerCheckerFullname,"onUpdate:userFullname":r[22]||(r[22]=t=>e.formData.hiddenDangerCheckerFullname=t),deptName:e.formData.hiddenDangerCheckerDeptName,"onUpdate:deptName":r[23]||(r[23]=t=>e.formData.hiddenDangerCheckerDeptName=t),deptCode:e.formData.hiddenDangerCheckerDeptCode,"onUpdate:deptCode":r[24]||(r[24]=t=>e.formData.hiddenDangerCheckerDeptCode=t),title:"选择总包确认人",required:"",rules:[{required:!0,message:"请选择总包确认人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),a(o,{modelValue:e.formData.hiddenDangerCheckerDeptName,"onUpdate:modelValue":r[25]||(r[25]=t=>e.formData.hiddenDangerCheckerDeptName=t),label:"确认人所在部门",placeholder:"",readonly:""},null,8,["modelValue"]),a(f,{"label-width":"8em",label:"施工单位整改人",userName:e.formData.hiddenDangerRectifier,"onUpdate:userName":r[26]||(r[26]=t=>e.formData.hiddenDangerRectifier=t),userFullname:e.formData.hiddenDangerRectifierFullname,"onUpdate:userFullname":r[27]||(r[27]=t=>e.formData.hiddenDangerRectifierFullname=t),deptName:e.formData.hiddenDangerRectifierDeptName,"onUpdate:deptName":r[28]||(r[28]=t=>e.formData.hiddenDangerRectifierDeptName=t),deptCode:e.formData.hiddenDangerRectifierDeptCode,"onUpdate:deptCode":r[29]||(r[29]=t=>e.formData.hiddenDangerRectifierDeptCode=t),title:"选择施工单位整改人",required:!i.canEdit0,rules:[{required:!i.canEdit0,message:"请选择施工单位整改人"}],readonly:e.type==="view"||!i.canEdit0&&!i.canEdit1},null,8,["userName","userFullname","deptName","deptCode","required","rules","readonly"]),a(f,{label:"总包审核人",userName:e.formData.hiddenDangerSupervisor,"onUpdate:userName":r[30]||(r[30]=t=>e.formData.hiddenDangerSupervisor=t),userFullname:e.formData.hiddenDangerSupervisorFullname,"onUpdate:userFullname":r[31]||(r[31]=t=>e.formData.hiddenDangerSupervisorFullname=t),deptName:e.formData.hiddenDangerSupervisorDeptName,"onUpdate:deptName":r[32]||(r[32]=t=>e.formData.hiddenDangerSupervisorDeptName=t),deptCode:e.formData.hiddenDangerSupervisorDeptCode,"onUpdate:deptCode":r[33]||(r[33]=t=>e.formData.hiddenDangerSupervisorDeptCode=t),title:"选择总包审核人",required:"",rules:[{required:!0,message:"请选择总包审核人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"]),a(f,{label:"监理审核人",userName:e.formData.hiddenDangerRectifyApprover,"onUpdate:userName":r[34]||(r[34]=t=>e.formData.hiddenDangerRectifyApprover=t),userFullname:e.formData.hiddenDangerRectifyApproverFullname,"onUpdate:userFullname":r[35]||(r[35]=t=>e.formData.hiddenDangerRectifyApproverFullname=t),deptName:e.formData.hiddenDangerRectifyApproverDeptName,"onUpdate:deptName":r[36]||(r[36]=t=>e.formData.hiddenDangerRectifyApproverDeptName=t),deptCode:e.formData.hiddenDangerRectifyApproverDeptCode,"onUpdate:deptCode":r[37]||(r[37]=t=>e.formData.hiddenDangerRectifyApproverDeptCode=t),title:"选择监理审核人",required:"",rules:[{required:!0,message:"请选择监理审核人"}],readonly:e.type==="view"||!i.canEdit0},null,8,["userName","userFullname","deptName","deptCode","readonly"])]),_:1}),e.type==="view"||e.taskKey&&e.taskKey!=="UserTask_0"&&e.taskKey!=="UserTask_1"?(p(),D(g,{key:0,border:!1},{default:u(()=>[a(o,{label:"整改措施",modelValue:e.formData.rectifyMeasures,"onUpdate:modelValue":r[38]||(r[38]=t=>e.formData.rectifyMeasures=t),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改措施",required:"",rules:[{required:!0,message:"请填写整改措施"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),a(o,{label:"整改情况",modelValue:e.formData.rectifySituation,"onUpdate:modelValue":r[39]||(r[39]=t=>e.formData.rectifySituation=t),rows:"4",autosize:"",type:"textarea","label-align":"top",placeholder:"请填写整改情况",required:"",rules:[{required:!0,message:"请填写整改情况"}],"input-align":"left",readonly:e.type==="view"||!i.canEdit2},null,8,["modelValue","readonly"]),a(o,{label:"附件照片","label-align":"top","input-align":"left"},{input:u(()=>[a(v,{ref:"afterFiles",g9s:e.formData.afterFileToken,"onUpdate:g9s":r[40]||(r[40]=t=>e.formData.afterFileToken=t),accept:"image/*",multiple:!0,"max-count":9,"max-size":1*1024*1024*50,readonly:e.type==="view"||!i.canEdit2},null,8,["g9s","readonly"])]),_:1})]),_:1})):y("",!0)]),_:1},512)]),_:1},8,["model-key","form-key","entity-name","detail-param-list","detail-entity-name-list","onDraftClick","onSubmitClick","onAfterSubmit","onCopyCallBack"])}const ee=k(V,[["render",T]]);export{ee as default};
