import{F as P,D as C}from"./index-a831f9da.js";import{_ as L}from"./index-4829f8e2.js";import{Q as f,R as _,X as B,V as a,k as m,U as t,Y as s}from"./verder-361ae6c7.js";import"./index-8d635ba7.js";import"./form-a8596e72.js";import"./api-77f046f1.js";import"./index-fc22947f.js";import"./file-842bc27d.js";import"./file-2bef16be.js";import"./delegate-b245d146.js";import"./index-889f88bd.js";import"./vant-91101745.js";import"./formKeys-f34a583f.js";import"./FormItemPerson-bd0e3e57.js";import"./FormItemPicker-d3f69283.js";const F={name:"JL19",components:{FormTemplate:P,DocumentPart:C},emits:[],props:{},setup(d,{attrs:e,slots:b,emit:y}){},data(){return{detailTable:[],attachmentDesc:"1、工程进度付款审核汇总表。\n2、其他。"}},computed:{},watch:{},created(){},mounted(){},methods:{onAfterInit({formData:d,detailParamList:e}){},onBeforeSubmit({formData:d,detailParamList:e,taskStart:b},y){return new Promise((o,l)=>{try{o()}catch(p){l(p)}})}}},U={class:"one-line"},g={class:"form-info"},A={class:"form-info"},O={class:"form-info"},S={class:"form-info"},z={class:"form-info"},D={class:"one-line"},I={class:"form-info"},W={class:"attachment-desc"},J={class:"comment-wp"},Q={class:"textarea-wp"},R={class:"footer-input"},X={class:"form-info"},Y={class:"form-info"},j={class:"form-info"},q={class:"form-info"};function E(d,e,b,y,o,l){const p=f("van-field"),V=f("DocumentPart"),w=f("FormTemplate");return _(),B(w,{ref:"FormTemplate",nature:"进度付","employer-target":!0,"on-after-init":l.onAfterInit,"on-before-submit":l.onBeforeSubmit,"detail-table":o.detailTable,"is-show-confirm1":!1,attachmentDesc:o.attachmentDesc},{default:a(({formData:n,formTable:N,baseObj:r,uploadAccept:T,taskStart:i,taskComment2:k,taskComment3:x,taskComment4:v})=>[m(V,{deptLabel:"监理机构：",deptProp:"supervisionDeptName",deptValue:n.supervisionDeptName,deptOptions:r.supervisionDeptName,personLabel:"总监理工程师：",labelWidth:"10em",disabled:!i},{default:a(()=>[t("div",U,[e[0]||(e[0]=t("span",{style:{"padding-left":"2em"}},"经审核承包人的工程进度付款申请单",-1)),e[1]||(e[1]=t("span",null,"（ ",-1)),t("span",g,s(n.constructionName),1),e[2]||(e[2]=t("span",null," [ ",-1)),t("span",A,s(n.field1),1),e[3]||(e[3]=t("span",null," ] 进度付",-1)),t("span",O,s(n.field2),1),e[4]||(e[4]=t("span",null,"号），",-1)),e[5]||(e[5]=t("span",null,"本月应支付给承包人的",-1)),e[6]||(e[6]=t("span",null,"工程价款金额共计",-1)),e[7]||(e[7]=t("span",null,"（大写）",-1)),t("span",S,s(n.field3),1),e[8]||(e[8]=t("span",null,"元",-1)),e[9]||(e[9]=t("span",null,"（小写",-1)),t("span",z,s(n.field4),1),e[10]||(e[10]=t("span",null,"元）。",-1))]),t("div",D,[e[11]||(e[11]=t("span",{style:{"padding-left":"2em"}},"根据施工合同约定，请贵方在收到此证书后的",-1)),t("span",I,s(n.field5),1),e[12]||(e[12]=t("span",null,"天之内完成审批，",-1)),e[13]||(e[13]=t("span",null,"将上述工程价款支付给承包人。",-1))]),t("div",W,[e[14]||(e[14]=t("div",null,"附件：",-1)),m(p,{modelValue:n.attachmentDesc,"onUpdate:modelValue":u=>n.attachmentDesc=u,rows:"2",autosize:"",label:"",type:"textarea","label-width":"3em",placeholder:"",readonly:!i},null,8,["modelValue","onUpdate:modelValue","readonly"])])]),_:2},1032,["deptValue","deptOptions","disabled"]),m(V,{deptLabel:"发包人：",deptProp:"employerName",deptValue:n.employerName,deptOptions:r.employerName,personLabel:"负责人：",labelWidth:"10em",disabled:!i},{default:a(()=>[t("div",J,[e[15]||(e[15]=t("div",null,"发包人审批意见：",-1)),t("div",Q,[m(p,{modelValue:n.comment4,"onUpdate:modelValue":u=>n.comment4=u,rows:"3",autosize:"",label:"",type:"textarea",placeholder:"",readonly:!v},null,8,["modelValue","onUpdate:modelValue","readonly"])])])]),_:2},1032,["deptValue","deptOptions","disabled"])]),footer:a(({formData:n,formTable:N,baseObj:r,uploadAccept:T,taskStart:i,taskComment2:k,taskComment3:x,taskComment4:v})=>[t("div",R,[e[16]||(e[16]=t("span",null,"说明：本证书一式",-1)),t("span",X,s(n.num1),1),e[17]||(e[17]=t("span",null,"份，由监理机构填写，发包人审批后，发包人",-1)),t("span",Y,s(n.num2),1),e[18]||(e[18]=t("span",null,"份，监理机构",-1)),t("span",j,s(n.num3),1),e[19]||(e[19]=t("span",null,"份，承包人",-1)),t("span",q,s(n.num4),1),e[20]||(e[20]=t("span",null,"份，",-1)),e[21]||(e[21]=t("span",null,"办理结算时使用。",-1))])]),_:1},8,["on-after-init","on-before-submit","detail-table","attachmentDesc"])}const ie=L(F,[["render",E]]);export{ie as default};
