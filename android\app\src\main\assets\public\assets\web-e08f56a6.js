import{W as n}from"./index-4829f8e2.js";import"./verder-361ae6c7.js";import"./vant-91101745.js";class a extends n{async getCurrentPosition(e){return new Promise((i,t)=>{navigator.geolocation.getCurrentPosition(o=>{i(o)},o=>{t(o)},Object.assign({enableHighAccuracy:!1,timeout:1e4,maximumAge:0},e))})}async watchPosition(e,i){const t=navigator.geolocation.watchPosition(o=>{i(o)},o=>{i(null,o)},Object.assign({enableHighAccuracy:!1,timeout:1e4,maximumAge:0},e));return"".concat(t)}async clearWatch(e){window.navigator.geolocation.clearWatch(parseInt(e.id,10))}async checkPermissions(){if(typeof navigator>"u"||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");const e=await window.navigator.permissions.query({name:"geolocation"});return{location:e.state,coarseLocation:e.state}}async requestPermissions(){throw this.unimplemented("Not implemented on web.")}}const l=new a;export{l as Geolocation,a as GeolocationWeb};
