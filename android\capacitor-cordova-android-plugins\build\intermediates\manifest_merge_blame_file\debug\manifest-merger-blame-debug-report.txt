1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:amazon="http://schemas.amazon.com/apk/res/android"
3    xmlns:android="http://schemas.android.com/apk/res/android"
4    package="capacitor.android.plugins" >
5
6    <uses-sdk
7        android:minSdkVersion="22"
7-->E:\project\yjp\mobile-front\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
8        android:targetSdkVersion="33" />
8-->E:\project\yjp\mobile-front\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
9
10    <application android:usesCleartextTraffic="true" >
10-->E:\project\yjp\mobile-front\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:5:1-7:15
10-->E:\project\yjp\mobile-front\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:5:15-50
11    </application>
12
13</manifest>
