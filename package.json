{"name": "project-yjp-app", "private": true, "version": "0.0.27", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build", "build:dev": "vite build --mode development", "build:localtest": "vite build --mode localtest", "build:stage": "vite build --mode stage", "preview": "vite preview", "docker": "vite build && docker buildx build --platform linux/amd64 -t yjp/project-app:0.0.26 . && docker push reg.hdec.com/yjp/mobile-front:0.0.26"}, "dependencies": {"@capacitor-community/barcode-scanner": "^3.0.3", "@capacitor-community/file-opener": "1.0.4", "@capacitor/android": "^4.7.3", "@capacitor/app": "^4.1.1", "@capacitor/browser": "^4.1.1", "@capacitor/camera": "^4.1.4", "@capacitor/core": "^4.7.3", "@capacitor/device": "^4.1.0", "@capacitor/filesystem": "^4.1.5", "@capacitor/geolocation": "^4.1.0", "@capacitor/ios": "^4.7.3", "@capacitor/local-notifications": "^4.1.5", "@capacitor/preferences": "^4.0.2", "@capacitor/splash-screen": "^4.2.0", "@handsontable/vue3": "^15.3.0", "@turf/turf": "^6.5.0", "@vant/use": "^1.5.1", "@vue-office/docx": "^1.3.0", "@vue-office/excel": "^1.4.5", "@vue-office/pdf": "^1.5.3", "@vueuse/core": "^10.1.2", "add": "^2.0.6", "axios": "^1.3.6", "base64-js": "^1.5.1", "bpmn-js": "^13.2.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "echarts": "^5.4.3", "ezuikit-js": "^8.1.9-beta.3", "handsontable": "^15.3.0", "lodash": "^4.17.21", "mapbox-gl": "^2.15.0", "moment": "^2.30.1", "mqtt": "^4.3.7", "pinia": "^2.0.35", "pinia-plugin-persistedstate": "^3.1.0", "qs": "^5.2.1", "sharp": "^0.30.7", "sm-crypto": "^0.3.12", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "vant": "^4.7.1", "vue": "^3.3.4", "vue-echarts": "^6.6.0", "vue-pdf-embed": "^1.2.1", "vue-router": "^4.1.6", "xgplayer": "^3.0.5", "xgplayer-hls": "^3.0.5", "yarn": "^1.22.19"}, "devDependencies": {"@capacitor/assets": "^2.0.4", "@capacitor/cli": "^4.7.3", "@vitejs/plugin-legacy": "^4.1.0", "@vitejs/plugin-vue": "^4.1.0", "@yuo/postcss-px2vw": "^1.0.5", "sass": "^1.62.0", "terser": "^5.19.1", "unocss": "^0.57.2", "unplugin-vue-components": "^0.24.1", "vconsole": "^3.15.0", "vite": "^4.4.9"}}