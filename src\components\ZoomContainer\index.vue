<template>
  <div class="zoom-container-wrapper">
    <div class="zoom-controls" v-if="showControls">
      <button @click="zoomIn" class="zoom-btn">+</button>
      <span class="zoom-level">{{ Math.round(scale * 100) }}%</span>
      <button @click="zoomOut" class="zoom-btn">-</button>
      <button @click="resetZoom" class="zoom-btn reset-btn">重置</button>
    </div>

    <div
      class="simple-zoom-container"
      ref="container"
      @wheel="handleWheel"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      :style="containerStyle"
    >
      <div
        class="zoom-content"
        :class="{ 'center-mode': scale <= 1 }"
        :style="zoomStyle"
      >
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleZoomContainer',
  props: {
    minScale: {
      type: Number,
      default: 0.5
    },
    maxScale: {
      type: Number,
      default: 3
    },
    showControls: {
      type: Boolean,
      default: false
    },
    wheelSensitivity: {
      type: Number,
      default: 0.1
    }
  },
  data() {
    return {
      scale: 1,
      isZooming: false,
      lastTouchDistance: 0,
      lastTouchCenter: { x: 0, y: 0 },
      transformOriginX: 50, // 百分比
      transformOriginY: 0   // 百分比
    };
  },
  computed: {
    containerStyle() {
      // 放大时调整容器高度以支持滚动
      if (this.scale > 1) {
        return {
          height: `${this.scale * 100}%`,
          minHeight: '100%'
        };
      }
      return {};
    },

    zoomStyle() {
      if (this.scale <= 1) {
        // 缩小时：居中靠上
        return {
          transform: `scale(${this.scale})`,
          transformOrigin: 'center top',
          width: '100%',
          height: '100%',
          transition: this.isZooming ? 'none' : 'transform 0.3s ease'
        };
      } else {
        // 放大时：使用动态计算的中心点，调整尺寸支持滚动
        return {
          transform: `scale(${this.scale})`,
          transformOrigin: `${this.transformOriginX}% ${this.transformOriginY}%`,
          width: '100%',
          height: '100%',
          transition: this.isZooming ? 'none' : 'transform 0.3s ease'
        };
      }
    }
  },
  mounted() {
    // 初始化时适配宽度
    this.$nextTick(() => {
      setTimeout(() => {
        this.fitToWidth();
      }, 300);
    });
  },
  methods: {
    // 适配宽度
    fitToWidth() {
      if (!this.$refs.container) return;
      
      const container = this.$refs.container;
      const pdfCanvas = container.querySelector('canvas');
      
      if (pdfCanvas) {
        const containerWidth = container.clientWidth;
        const canvasWidth = pdfCanvas.clientWidth;
        
        if (canvasWidth > 0) {
          // 计算适配比例，留出一些边距
          const fitScale = (containerWidth - 40) / canvasWidth;
          this.scale = Math.max(this.minScale, Math.min(this.maxScale, fitScale));
          
          console.log('适配宽度:', {
            containerWidth,
            canvasWidth,
            fitScale: this.scale
          });
        }
      }
    },
    
    // 滚轮缩放 - 以鼠标位置为中心
    handleWheel(e) {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();

        // 计算鼠标在容器中的位置百分比
        const container = this.$refs.container;
        const rect = container.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // 更新缩放中心点（百分比）
        this.transformOriginX = (mouseX / rect.width) * 100;
        this.transformOriginY = (mouseY / rect.height) * 100;

        const delta = e.deltaY > 0 ? -this.wheelSensitivity : this.wheelSensitivity;
        const newScale = Math.max(this.minScale, Math.min(this.maxScale, this.scale + delta));

        this.scale = newScale;
      }
    },
    
    // 双指缩放 - 以双指中心为缩放点
    handleTouchStart(e) {
      if (e.touches.length === 2) {
        e.preventDefault();
        e.stopPropagation();
        this.isZooming = true;
        this.lastTouchDistance = this.getTouchDistance(e.touches);
        this.lastTouchCenter = this.getTouchCenter(e.touches);

        // 计算双指中心在容器中的位置百分比
        const container = this.$refs.container;
        const rect = container.getBoundingClientRect();
        this.transformOriginX = ((this.lastTouchCenter.x - rect.left) / rect.width) * 100;
        this.transformOriginY = ((this.lastTouchCenter.y - rect.top) / rect.height) * 100;
      }
    },

    handleTouchMove(e) {
      if (e.touches.length === 2 && this.isZooming) {
        e.preventDefault();
        e.stopPropagation();

        const currentDistance = this.getTouchDistance(e.touches);
        const currentCenter = this.getTouchCenter(e.touches);
        const scaleChange = currentDistance / this.lastTouchDistance;

        // 更新缩放中心点
        const container = this.$refs.container;
        const rect = container.getBoundingClientRect();
        this.transformOriginX = ((currentCenter.x - rect.left) / rect.width) * 100;
        this.transformOriginY = ((currentCenter.y - rect.top) / rect.height) * 100;

        // 添加阻尼，让缩放更平滑
        const dampedScaleChange = 1 + (scaleChange - 1) * 0.8;
        const newScale = Math.max(this.minScale, Math.min(this.maxScale, this.scale * dampedScaleChange));

        this.scale = newScale;
        this.lastTouchDistance = currentDistance;
        this.lastTouchCenter = currentCenter;
      }
    },
    
    handleTouchEnd() {
      this.isZooming = false;
    },
    
    // 工具方法
    getTouchDistance(touches) {
      const dx = touches[0].clientX - touches[1].clientX;
      const dy = touches[0].clientY - touches[1].clientY;
      return Math.sqrt(dx * dx + dy * dy);
    },

    getTouchCenter(touches) {
      const centerX = (touches[0].clientX + touches[1].clientX) / 2;
      const centerY = (touches[0].clientY + touches[1].clientY) / 2;
      return { x: centerX, y: centerY };
    },
    
    // 控制按钮
    zoomIn() {
      const newScale = Math.min(this.maxScale, this.scale + 0.2);
      this.scale = newScale;
    },
    
    zoomOut() {
      const newScale = Math.max(this.minScale, this.scale - 0.2);
      this.scale = newScale;
    },
    
    resetZoom() {
      this.scale = 1;
    },
    
    // 外部调用
    setScale(scale) {
      this.scale = Math.max(this.minScale, Math.min(this.maxScale, scale));
    },
    
    getScale() {
      return this.scale;
    }
  }
};
</script>

<style scoped lang="scss">
.zoom-container-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.simple-zoom-container {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: auto;
  background: transparent;

  /* iOS滚动优化 */
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  -webkit-overscroll-behavior: contain;

  /* 触摸事件优化 - 允许滚动但阻止页面缩放 */
  touch-action: pan-x pan-y pinch-zoom;

  /* iOS Safari兼容性 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.zoom-content {
  width: 100%;
  height: 100%;
  padding: 8px;

  /* 添加硬件加速 */
  will-change: transform;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  /* 缩小时居中 */
  &.center-mode {
    margin: 0 auto;
  }

  :deep(.pdf-viewer) {
    background: transparent;
    width: 100%;
    height: 100%;

    canvas {
      background: white !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
      border-radius: 4px !important;
      margin-bottom: 6px !important;

      &:last-child {
        margin-bottom: 0 !important;
      }
    }
  }
}

.zoom-controls {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: #f5f5f5;
  border-radius: 16px;
  padding: 4px 8px;
  margin: 4px auto;
  width: fit-content;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  height: 30px;
  flex-shrink: 0;

  .zoom-btn {
    background: white;
    border: 1px solid #d0d0d0;
    color: #333;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.15s ease;

    &:hover {
      background: #f0f0f0;
      border-color: #bbb;
    }

    &:active {
      background: #e8e8e8;
      transform: scale(0.98);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    /* 重置按钮特殊样式 */
    &.reset-btn {
      border-radius: 6px;
      width: auto;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 32px;
      padding: 0 6px;
      font-size: 12px;
      white-space: nowrap;
    }
  }

  .zoom-level {
    color: #333;
    font-size: 11px;
    font-weight: 600;
    min-width: 32px;
    text-align: center;
    padding: 0 2px;
  }
}
</style>
