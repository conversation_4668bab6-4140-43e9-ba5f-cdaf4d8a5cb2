<template>
  <div class="zoom-container-wrapper">
    <div class="zoom-controls" v-if="showControls">
      <button @click="zoomIn" class="zoom-btn">+</button>
      <span class="zoom-level">{{ Math.round(scale * 100) }}%</span>
      <button @click="zoomOut" class="zoom-btn">-</button>
      <button @click="resetZoom" class="zoom-btn reset-btn">重置</button>
    </div>

    <div
      class="simple-zoom-container"
      ref="container"
      @wheel="handleWheel"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @touchcancel="handleTouchCancel"
      @transitionend="handleTransitionEnd"
    >
      <div
        class="zoom-content-wrapper"
        :style="{
          transform: `translate(${translateX}px, ${translateY}px)`,
          transition: transitionState
        }"
      >
        <div
          class="zoom-content"
          :class="{ 'center-mode': scale <= 1 }"
          :style="zoomStyle"
        >
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleZoomContainer',
  props: {
    minScale: {
      type: Number,
      default: 0.5
    },
    maxScale: {
      type: Number,
      default: 3
    },
    showControls: {
      type: Boolean,
      default: false
    },
    wheelSensitivity: {
      type: Number,
      default: 0.1
    }
  },
  data() {
    return {
      scale: 1,
      isZooming: false,
      translateX: 0,
      translateY: 0,
      centerX: 0,
      centerY: 0,
      lastTouchDistance: 0,
      lastTouchCenter: { x: 0, y: 0 },
      touchStart: { x: 0, y: 0 },
      transitionState: 'none'
    };
  },
  computed: {
    zoomStyle() {
      if (this.scale <= 1) {
        // 缩小时：居中靠上
        return {
          transform: `scale(${this.scale})`,
          transformOrigin: 'center top',
          width: '100%',
          height: '100%',
          transition: this.isZooming ? 'none' : 'transform 0.3s ease'
        };
      } else {
        // 放大时：以双指中心点缩放
        return {
          transform: `scale(${this.scale})`,
          transformOrigin: `${this.centerX}px ${this.centerY}px`,
          width: '100%',
          height: '100%',
          transition: this.isZooming ? 'none' : 'transform 0.3s ease'
        };
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.updateCenterPoint();
      this.fitToWidth();
      
      // 添加鼠标移动事件监听
      const container = this.$refs.container;
      if (container) {
        container.addEventListener('mousemove', this.handleMouseMove);
      }
    });
  },
  beforeDestroy() {
    const container = this.$refs.container;
    if (container) {
      container.removeEventListener('mousemove', this.handleMouseMove);
    }
  },
  methods: {
    handleMouseMove(e) {
      const container = this.$refs.container;
      if (container) {
        const rect = container.getBoundingClientRect();
        this.centerX = e.clientX - rect.left;
        this.centerY = e.clientY - rect.top;
      }
    },
    // 更新缩放中心点
    updateCenterPoint() {
      const container = this.$refs.container;
      const contentWrapper = this.$refs.contentWrapper;
      
      if (container && contentWrapper) {
        this.centerX = container.clientWidth / 2;
        this.centerY = container.clientHeight / 2;
      }
    },
    // 适配宽度
    fitToWidth() {
      if (!this.$refs.container) return;
      
      const container = this.$refs.container;
      const pdfCanvas = container.querySelector('canvas');
      
      if (pdfCanvas) {
        const containerWidth = container.clientWidth;
        const canvasWidth = pdfCanvas.clientWidth;
        
        if (canvasWidth > 0) {
          const fitScale = (containerWidth - 40) / canvasWidth;
          this.scale = Math.max(this.minScale, Math.min(this.maxScale, fitScale));
          
          // 确保容器大小正确
          container.style.width = `${containerWidth}px`;
          container.style.height = 'auto';
          
          // 重新计算边界限制
          this.handleTouchEnd();
        }
      }
    },
    // 滚轮缩放
    handleWheel(e) {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
        
        const container = this.$refs.container;
        const rect = container.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        // 保存缩放前的中心点
        const oldScale = this.scale;
        const oldCenterX = this.centerX;
        const oldCenterY = this.centerY;
        
        // 更新缩放中心点
        this.centerX = mouseX;
        this.centerY = mouseY;
        
        // 计算缩放变化
        const delta = e.deltaY > 0 ? -this.wheelSensitivity : this.wheelSensitivity;
        const newScale = Math.max(this.minScale, Math.min(this.maxScale, this.scale + delta));
        
        // 计算缩放比例
        const scaleRatio = newScale / oldScale;
        
        // 调整translate值以保持缩放中心点不变
        this.translateX = (this.translateX + oldCenterX) * scaleRatio - this.centerX;
        this.translateY = (this.translateY + oldCenterY) * scaleRatio - this.centerY;
        
        this.scale = newScale;
        
        // 应用边界限制
        const { minX, maxX, minY, maxY } = this.getBoundaryLimits();
        this.translateX = Math.min(Math.max(this.translateX, minX), maxX);
        this.translateY = Math.min(Math.max(this.translateY, minY), maxY);
      }
    },
    // 双指缩放
    handleTouchStart(e) {
      if (e.touches.length === 2) {
        e.preventDefault();
        e.stopPropagation();
        this.isZooming = true;
        this.lastTouchDistance = this.getTouchDistance(e.touches);
        this.lastTouchCenter = this.getTouchCenter(e.touches);
        this.transitionState = 'none';
      }
    },
    handleTouchMove(e) {
      if (e.touches.length === 2 && this.isZooming) {
        e.preventDefault();
        e.stopPropagation();

        const currentDistance = this.getTouchDistance(e.touches);
        const currentCenter = this.getTouchCenter(e.touches);
        const scaleChange = currentDistance / this.lastTouchDistance;

        // 更新缩放中心点
        this.centerX = currentCenter.x;
        this.centerY = currentCenter.y;

        // 添加阻尼，让缩放更平滑
        const dampedScaleChange = 1 + (scaleChange - 1) * 0.8;
        const newScale = Math.max(this.minScale, Math.min(this.maxScale, this.scale * dampedScaleChange));

        this.scale = newScale;
        this.lastTouchDistance = currentDistance;
        this.lastTouchCenter = currentCenter;
      } else if (e.touches.length === 1 && this.scale > 1) {
        // 处理拖动
        const touch = e.touches[0];
        if (this.touchStart.x === 0) {
          this.touchStart.x = touch.clientX;
          this.touchStart.y = touch.clientY;
        }
        
        // 计算新的translate值
        const newTranslateX = this.translateX + (touch.clientX - this.touchStart.x);
        const newTranslateY = this.translateY + (touch.clientY - this.touchStart.y);
        
        // 只在缩小时才应用严格的边界限制
        if (this.scale <= 1) {
          const { minX, maxX, minY, maxY } = this.getBoundaryLimits();
          this.translateX = Math.min(Math.max(newTranslateX, minX), maxX);
          this.translateY = Math.min(Math.max(newTranslateY, minY), maxY);
        } else {
          this.translateX = newTranslateX;
          this.translateY = newTranslateY;
        }
        
        this.touchStart.x = touch.clientX;
        this.touchStart.y = touch.clientY;
        
        e.preventDefault();
      }
    },
    // 在handleTouchEnd中使用边界限制
    handleTouchEnd() {
      this.isZooming = false;
      this.touchStart = { x: 0, y: 0 };
      
      // 获取边界限制
      const { minX, maxX, minY, maxY } = this.getBoundaryLimits();
      
      // 只在缩小时才应用严格的边界限制
      if (this.scale <= 1) {
        this.translateX = Math.min(Math.max(this.translateX, minX), maxX);
        this.translateY = Math.min(Math.max(this.translateY, minY), maxY);
      }
      
      // 触发平滑过渡
      this.transitionState = 'transform 0.3s ease';
    },
    handleTouchCancel() {
      this.handleTouchEnd();
    },
    handleTransitionEnd() {
      if (!this.isZooming) {
        this.transitionState = 'none';
      }
    },
    // 获取触摸中心点
    getTouchCenter(touches) {
      const centerX = (touches[0].clientX + touches[1].clientX) / 2;
      const centerY = (touches[0].clientY + touches[1].clientY) / 2;
      return { x: centerX, y: centerY };
    },
    // 获取触摸距离
    getTouchDistance(touches) {
      const dx = touches[0].clientX - touches[1].clientX;
      const dy = touches[0].clientY - touches[1].clientY;
      return Math.sqrt(dx * dx + dy * dy);
    },
    // 控制按钮
    zoomIn() {
      const newScale = Math.min(this.maxScale, this.scale + 0.2);
      this.scale = newScale;
    },
    zoomOut() {
      const newScale = Math.max(this.minScale, this.scale - 0.2);
      this.scale = newScale;
    },
    // 添加边界限制计算
    getBoundaryLimits() {
      const container = this.$refs.container;
      const contentWrapper = this.$refs.contentWrapper;
      
      if (!container || !contentWrapper) return { minX: 0, maxX: 0, minY: 0, maxY: 0 };
      
      const containerRect = container.getBoundingClientRect();
      const contentRect = contentWrapper.getBoundingClientRect();
      
      // 计算缩放后的内容尺寸
      const scaledWidth = contentRect.width * this.scale;
      const scaledHeight = contentRect.height * this.scale;
      
      // 计算边界限制
      // 放大时，内容应该可以滚动查看完整区域
      const minX = Math.min(0, containerRect.width - scaledWidth);
      const maxX = Math.max(0, scaledWidth - containerRect.width);
      const minY = Math.min(0, containerRect.height - scaledHeight);
      const maxY = Math.max(0, scaledHeight - containerRect.height);
      
      return { minX, maxX, minY, maxY };
    },
    // 在resetZoom中添加归位逻辑
    resetZoom() {
      this.scale = 1;
      this.translateX = 0;
      this.translateY = 0;
      this.updateCenterPoint();
      this.transitionState = 'transform 0.3s ease';
      
      // 确保内容回到顶部和中线
      const container = this.$refs.container;
      if (container) {
        const containerRect = container.getBoundingClientRect();
        const contentWrapper = this.$refs.contentWrapper;
        if (contentWrapper) {
          const contentRect = contentWrapper.getBoundingClientRect();
          
          // 计算顶部和中线位置
          this.translateX = (containerRect.width - contentRect.width) / 2;
          this.translateY = 0;
        }
      }
    },
    // 外部调用
    setScale(scale) {
      this.scale = Math.max(this.minScale, Math.min(this.maxScale, scale));
    },
    getScale() {
      return this.scale;
    }
  }
};
</script>

<style scoped lang="scss">
.zoom-container-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.simple-zoom-container {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  background: transparent;

  /* 防止iOS Safari的橡皮筋效果 */
  overscroll-behavior: contain;
  -webkit-overscroll-behavior: contain;

  /* 确保触摸事件正常工作 */
  touch-action: pan-x pan-y;
}

.zoom-content {
  width: 100%;
  height: 100%;
  padding: 8px;

  /* 添加硬件加速 */
  will-change: transform;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  /* 缩小时居中 */
  &.center-mode {
    margin: 0 auto;
  }

  :deep(.pdf-viewer) {
    background: transparent;
    width: 100%;
    height: 100%;

    canvas {
      background: white !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
      border-radius: 4px !important;
      margin-bottom: 6px !important;

      &:last-child {
        margin-bottom: 0 !important;
      }
    }
  }
}

.zoom-controls {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: #f5f5f5;
  border-radius: 16px;
  padding: 4px 8px;
  margin: 4px auto;
  width: fit-content;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  height: 30px;
  flex-shrink: 0;

  .zoom-btn {
    background: white;
    border: 1px solid #d0d0d0;
    color: #333;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.15s ease;

    &:hover {
      background: #f0f0f0;
      border-color: #bbb;
    }

    &:active {
      background: #e8e8e8;
      transform: scale(0.98);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    /* 重置按钮特殊样式 */
    &.reset-btn {
      border-radius: 6px;
      width: auto;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 32px;
      padding: 0 6px;
      font-size: 12px;
      white-space: nowrap;
    }
  }

  .zoom-level {
    color: #333;
    font-size: 11px;
    font-weight: 600;
    min-width: 32px;
    text-align: center;
    padding: 0 2px;
  }
}
</style>
