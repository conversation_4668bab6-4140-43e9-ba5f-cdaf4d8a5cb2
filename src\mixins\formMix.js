/**
 * 工作流表单混入
 * 提供表单数据获取、处理和提交的核心逻辑
 */
import { getFormData } from '@/api/form.js'
import { isJSON } from '@/utils/validate.js'

export default {
  data() {
    return {
      // 流程人员数据统一管理
      bpmPersonObj: {
        approverConfigList: [],  // 审批人配置列表
        ccUserConfigList: [],    // 抄送人配置列表
        approverParamList: [],   // 审批人参数列表
        ccParamList: [],         // 抄送人参数列表
        initiatorInfo: null,     // 发起人信息
		    comments: [],
      },
      // 表单详情数据列表
      detailParamList: [],
      // 包含的属性列表（在审批配置中显示）
      includeProps: [],
      // 排除的属性列表（不在审批配置中显示）
      excludeProps: [],
    }
  },

  props: {
    // 是否自定义初始化
    isCustomeInit: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    // 从路由或父组件参数中获取业务ID
    bizId() {
      return this.$route.query?.bizId || this.$route.params?.bizId || '';
    },

    // 从路由或父组件参数中获取任务ID
    taskId() {
      return this.$route.query?.taskId || this.$route.params?.taskId || '';
    },

    // 从路由或父组件参数中获取流程实例ID
    processInstanceId() {
      return this.$route.query?.processInstanceId || this.$route.params?.processInstanceId || '';
    },

    // 从路由或父组件参数中获取任务类型
    type() {
      return this.$route.query?.type || this.$route.params?.type || 'add';
    },

    // 从路由或父组件参数中获取任务节点标识
    taskKey() {
      return this.$route.query?.taskKey || this.$route.params?.taskKey || '';
    },

    // 用于直接访问 bpmPersonObj 中的属性
    approverConfigList() {
      return this.bpmPersonObj.approverConfigList || [];
    },

    ccUserConfigList() {
      return this.bpmPersonObj.ccUserConfigList || [];
    },

    approverParamList() {
      return this.bpmPersonObj.approverParamList || [];
    },

    ccParamList() {
      return this.bpmPersonObj.ccParamList || [];
    },

    initiatorInfo() {
      return this.bpmPersonObj.initiatorInfo;
    },
    // 计算表单字段控制状态
    flowConfig() {
      let temp = {}
      // 查看或审批状态下，所有字段默认只读
      if (this.type === 'view' || this.type === 'execute') {
        Object.keys(this.formData || {}).forEach(key => {
          // 在处理任务时，默认通知方式可以编辑
          if (this.type === 'execute' && key === 'notifyMethod') {
            temp[key] = ''
          } else {
            temp[key] = 'readonly'
          }
        })
      }

      // 处理发起节点
      const currentTaskKey = this.taskKey || 'UserTask_0'
      if (this.type === 'execute' && (currentTaskKey === 'UserTask_0' || currentTaskKey === 'fawkes_custom_flow_start')) {
        Object.keys(this.formData || {}).forEach(key => {
          temp[key] = ''
        })
      }

      // 调用自定义配置方法(如果存在)
      this.customConfig && this.customConfig(temp)
      return temp
    }
  },

  created() {
    // 初始化表单数据
    !this.isCustomeInit && this.getFormDataInit()
  },

  methods: {
    /**
     * 控制字段在指定节点是否可见
     * @param {String} taskKey - 目标任务节点标识
     * @returns {Boolean} - 是否可见
     */
    isVisibleTaskKey(taskKey) {
      const allKeys = this.allTaskKeys || []
      // 筛选包含该节点的所有路径
      const relevantPaths = allKeys.filter(path => path.includes(taskKey))

      for (let i = 0; i < relevantPaths.length; i++) {
        const path = relevantPaths[i]
        const taskIndex = path.findIndex(k => k === taskKey)
        const currentIndex = path.findIndex(k => k === (this.formData?.taskKey || this.taskKey))

        // 查看模式下，当前节点需要在目标节点之后才可见
        if (this.type === 'view') {
          return currentIndex > taskIndex
        } else {
          // 其他模式下，当前节点需要大于等于目标节点
          return currentIndex >= taskIndex
        }
      }
      return false
    },

    /**
     * 控制字段在指定节点是否可编辑
     * @param {String} taskKey - 目标任务节点标识
     * @returns {Boolean} - 是否可编辑
     */
    isEnableTaskKey(taskKey) {
      // 逻辑与isVisibleTaskKey相似
      return this.isVisibleTaskKey(taskKey)
    },

    /**
     * 获取表单数据
     * @param {Boolean} showLoading - 是否显示加载提示
     */
    async getFormDataInit() {
      try {
        this.$showToast({
          type: 'loading',
          loadingType: 'spinner',
          message: '加载中...',
          forbidClick: true,
        });

        // 前置处理钩子
        this.beforeGetFormData && await this.beforeGetFormData?.();

        // 有业务ID时获取详细表单数据
        if (this.bizId && this.bizId !== '0') {
          await this._getFormDataWithBizId();
        } else {
          // 新发起流程时只获取审批人配置
          await this._getFormDataForNew();
        }

        // 后置处理钩子
        this.afterGetFormData && await this.afterGetFormData?.()
      } catch (e) {
      } finally {
        this.$closeToast();
      }
    },

    /**
     * 获取已有业务的表单数据（内部方法）
     * @private
     */
    async _getFormDataWithBizId() {
      const params = {
        entityName: this.entityName,
        taskKey: this.taskKey,
        id: this.bizId,
        type: this.type,
        detailEntityNameList: this.detailEntityNameList || []
      };

      const data = await getFormData(this.service.query, params);

      if (data) {
        let formData = data?.entityObject || {}

        // 默认使用站内信通知
        if (!formData.notifyMethod) {
          formData.notifyMethod = [0]
        } else if (isJSON(formData.notifyMethod)) {
          formData.notifyMethod = JSON.parse(formData.notifyMethod)
        }
        data.entityObject = formData

        // 调用beforeInit钩子(如果存在)
        const result = this.beforeInit ? await this.beforeInit(data) : null;

        // 处理流程人员信息
        let bpmPersonInfo = {
          approverConfigList: data?.approverConfigList || [],
          ccUserConfigList: data?.ccUserConfigList || [],
          approverParamList: data?.approverParamList || [],
          ccParamList: this.type === 'view' ? data?.ccParamList || [] : [],
          initiatorInfo: data?.initiatorInfo
        };
        // 只展示指定的属性
        if(this.includeProps && this.includeProps.length > 0){
          bpmPersonInfo.approverConfigList = bpmPersonInfo.approverConfigList.filter(
            item => this.includeProps.indexOf(item.prop) > -1
          );
        }

        // 排除指定的属性
        if(this.excludeProps && this.excludeProps.length > 0) {
          bpmPersonInfo.approverConfigList = bpmPersonInfo.approverConfigList.filter(
            item => this.excludeProps.indexOf(item.prop) < 0
          );
        }

        // 暂存状态处理
        if (result && result.processState === 3) {
          bpmPersonInfo.approverConfigList = bpmPersonInfo.approverConfigList.map((v) => {
            let item = bpmPersonInfo.approverParamList.find(x => x.prop === v.prop)
            return { ...v, ...item }
          });
        }

        // 调用beforeSetBpmPerson钩子(如果存在)
        bpmPersonInfo = this.beforeSetBpmPerson
          ? await this.beforeSetBpmPerson(bpmPersonInfo, result || formData, data)
          : bpmPersonInfo;

        // 更新组件状态
        this.bpmPersonObj = bpmPersonInfo;

        // 更新父组件的表单数据
        if (result) {
          // 使用结果数据
          this.formData = result
        } else {
          // 使用API返回的数据
          this.formData = formData
          this.detailParamList = data?.detailParamList || [];
        }
      }
    },

    /**
     * 获取新建流程的人员配置（内部方法）
     * @private
     */
    async _getFormDataForNew() {
      const params = {
        entityName: this.entityName,
        detailEntityNameList: this.detailEntityNameList || [],
        taskKey: this.taskKey,
        type: this.type,
      };

      const data = await getFormData(this.service.query, params);
      if (data) {
        let bpmPersonInfo = {
          approverConfigList: data?.approverConfigList || [],
          ccUserConfigList: data?.ccUserConfigList || []
        };

        // 只展示指定的属性
        if(this.includeProps && this.includeProps.length > 0){
          bpmPersonInfo.approverConfigList = bpmPersonInfo.approverConfigList.filter(
            item => this.includeProps.indexOf(item.prop) > -1
          );
        }

        // 排除指定的属性
        if(this.excludeProps && this.excludeProps.length > 0) {
          bpmPersonInfo.approverConfigList = bpmPersonInfo.approverConfigList.filter(
            item => this.excludeProps.indexOf(item.prop) < 0
          );
        }
        // 调用beforeSetBpmPerson钩子(如果存在)
        bpmPersonInfo = this.beforeSetBpmPerson
          ? await this.beforeSetBpmPerson(bpmPersonInfo, {}, data)
          : bpmPersonInfo;

        // 更新组件状态
        this.bpmPersonObj = bpmPersonInfo;
      }
    },

    /**
     * 设置人员信息（供子组件调用）
     * @param {Object} personData - 人员数据
     */
    setBpmPerson(personData) {
      if (!personData) return;

      // 合并数据而不是替换
      Object.keys(personData).forEach(key => {
        this.bpmPersonObj[key] = personData[key];
      });
    },
    getDictLabel(enums, value){
      if(!Array.isArray(enums) || !value){
        return value
      }

      for (let i = 0; i < enums.length; i++) {
        if (enums[i].code === value) {
          return enums[i]['zh-CN'];
        }

      }
    },
  },

  beforeDestroy() {
    // 清理资源
    this.detailParamList = [];
    // 清理人员相关数据
    this.bpmPersonObj = {
      approverConfigList: [],
      ccUserConfigList: [],
      approverParamList: [],
      ccParamList: [],
	    comments: [],
      initiatorInfo: null
    };
  }
}
