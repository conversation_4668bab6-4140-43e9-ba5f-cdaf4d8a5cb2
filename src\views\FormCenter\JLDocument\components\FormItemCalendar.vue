<template>
  <van-field
    class="showValue"
    :name="name"
    :model-value="showValue"
    :label="label"
    :required="required"
    :rules="rules"
    :input-align="inputAlign"
    :error-message-align="errorMessageAlign"
    :label-width="labelWidth"
    readonly
    :is-link="!readonly"
    placeholder="请选择"
    @click="onShowPicker()"
  />
  <van-calendar
    teleport="#app"
    v-model:show="showPicker"
    :show-confirm='showConfirm'
    :readonly="readonly"
    :title="title"
    :min-date="minDate"
    :max-date="maxDate"
    :default-date="currentDate"
    @confirm="onSelectConfirm"
    @cancel="onClosePicker"
  />
</template>

<script>
export default {
  name: 'FormItemCalendar', // 弹窗日历选择器组件封装
  components: {},
  props: {
    value: [String, Number], // 选项选中的value
    text: String, // 选项选中value对应的text
    name: String, // 表单标识
    label: {
      type: String,
      default: '日期:',
    },
    title: String,
    inputAlign: {
      type: String,
      default: 'right', // left, right
    },
    errorMessageAlign: {
      type: String,
      default: undefined, // left, right
    },
    required: Boolean, // 必填*
    readonly: Boolean, // 只读时不能选择
    labelWidth: {
      type: String,
      default: undefined,
    },
    rules: {
      type: Array,
      default: () => {
        return [];
      },
    },
    type: {
      type: String,
      default: () => {
        return 'single'; // single,multiple, range
      },
    },
    minDate: {
      type: Date,
      default: () => {
        // const currentYear = new Date().getFullYear();
        return new Date(new Date().getFullYear() - 20, 0, 1);
      },
    },
    maxDate: {
      type: Date,
      default: () => {
        return new Date(new Date().getFullYear() + 20, 0, 1);
      },
    },
    showConfirm: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { attrs, slots, emit }) {},
  data() {
    return {
      showPicker: false,
      currentDate: undefined,
      showValue: '',
    };
  },
  computed: {
  },
  watch: {
    // 设置默认值
    value: {
      immediate: true,
      handler(v) {
        // 只在初始化时设置默认值
        if (this.currentDate == undefined) {
          let date = new Date(v);
          // 判断当前日期是否 Invalid Date
          if (!(date instanceof Date && !isNaN(date.getTime()))) {
            date = new Date();
          }
          this.currentDate = date;
          this.showValue = this.$dayjs(date).format('YYYY-MM-DD');
          this.$emit('update:value', this.showValue);
        } else {
          if(v){
            this.showValue = this.$dayjs(v).format('YYYY-MM-DD');
          } else {
            this.showValue = '';
          }
        }
      },
    },
  },
  created() {},
  mounted() {},
  methods: {
    onShowPicker() {
      if (!this.readonly) {
        this.showPicker = true;
      }
    },
    onClosePicker() {
      this.showPicker = false;
    },
    onSelectConfirm(date) {
      const value = this.$dayjs(date).format('YYYY-MM-DD'); // 这里必须要时分秒,不让后端报错
      this.showValue = value;
      this.$emit('update:value', value);
      this.$emit('change', value);
      this.onClosePicker();
    },
  },
};
</script>

<style scoped lang="scss">
:deep(.showValue){
  padding: 0!important;
}
:deep(.van-field__label){
  text-align: right!important;
}
:deep(.van-field__value){
  border: solid 1px #000!important;
  border-radius: 2px!important;
  padding-left: 6px!important;
}
:deep(.van-cell__right-icon){
  display: none!important;
}

:deep(.van-field__value .van-field__control){
  text-align: left;
}
</style>
