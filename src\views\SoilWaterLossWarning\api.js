import request from '@/utils/request';
const prefix = import.meta.env.VITE_APP_WATER_API_SERVICENAME

/**
 * 工程区水土流失危害预警-根据测项获取预警规则
 * @param params
 * @return {*}
 */
export function getWarningRule(params) {
  return request({
    url: `${prefix}/water_warn/erosion-rule/enable-rule`,
    method: 'get',
    params,
  });
}

/**
 * 工程区水土流失危害预警-预警消息推送查询分页
 * @param data
 * @return {*}
 */
export function getWarningList(data) {
  return request({
    url: `${prefix}/water-erosion/message/page`,
    method: 'post',
    data,
  });
}

/**
 * 工程区水土流失危害预警-雨量、土壤流失量列表
 * @param params
 * @return {*}
 */
export function getList(params) {
  return request({
    url: `${prefix}/water_warn/board/erosion/list`,
    method: 'get',
    params,
  });
}
