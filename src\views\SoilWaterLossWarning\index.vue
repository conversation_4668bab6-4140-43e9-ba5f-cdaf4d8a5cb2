<template>
  <Navbar back :title="route.meta.title" />
  <div class="view-height">
    <div class="bg-white px-2">
      <div class="flex items-center justify-between py-3">
        <div class="flex items-center">
          <van-image :src="square" class="w-5 h-5 mr-2" />
          <span class="font-bold">{{ formatDate(warningDate, 'YYYY年M月') }}预警通知</span>
        </div>
        <span class="text-gray" @click="picker = 'warning'">
          {{ formatDate(warningDate, 'YYYY年M月') }}
          <van-icon name="arrow-down" class="mx-1" />
        </span>
        <van-popup
          :show="!!picker"
          position="bottom"
          teleport="#app"
        >
          <template v-if="picker === 'warning'">
            <van-date-picker
              v-model="warningDate"
              :columns-type="['year', 'month']"
              :max-date="maxDate"
              title="选择年月"
              @confirm="pickerConfirm"
              @cancel="picker = null"
            />
          </template>
          <template v-else-if="picker === 'realtimeStart'">
            <van-date-picker
              v-model="realtimeStartDate"
              :max-date="new Date(realtimeEndDate)"
              title="选择日期"
              @confirm="pickerConfirm"
              @cancel="picker = null"
            />
          </template>
          <template v-else-if="picker === 'realtimeEnd'">
            <van-date-picker
              v-model="realtimeEndDate"
              :min-date="new Date(realtimeStartDate)"
              :max-date="maxDate"
              title="选择日期"
              @confirm="pickerConfirm"
              @cancel="picker = null"
            />
          </template>
          <template v-else-if="picker === 'soil'">
            <van-date-picker
              v-model="soilDate"
              :columns-type="['year']"
              :min-date="minDate"
              :max-date="maxDate"
              title="选择年份"
              @confirm="pickerConfirm"
              @cancel="picker = null"
            />
          </template>
        </van-popup>
      </div>
      <van-divider class="!my-0" />
      <div class="py-2">
        <template v-if="warningList.length > 0">
          <div
            v-for="(item, i) in warningList"
            :key="item.id"
            :class="{ 'mt-2': i > 0 }"
          >
            <div class="text-gray mb-1">{{ formatDate(item.createDate, 'YYYY年MM月DD日') }}</div>
            <div>{{ item.notifyContent || '-' }}</div>
          </div>
        </template>
        <van-empty v-else description="暂无数据" class="!py-0" />
      </div>
    </div>
    <div class="bg-white px-2 mt-2">
      <div class="flex items-center justify-between py-3">
        <div class="flex items-center">
          <van-image :src="square" class="w-5 h-5 mr-2" />
          <span class="font-bold">雨量实时数据（mm）</span>
        </div>
      </div>
      <van-divider class="!my-0" />
      <div class="py-2">
        <div class="flex items-center justify-around mb-3">
          <div @click="picker = 'realtimeStart'">
            {{ formatDate(realtimeStartDate) }}
            <van-icon name="arrow-down" class="mx-1" />
          </div>
          <span class="text-gray">至</span>
          <div @click="picker = 'realtimeEnd'">
            {{ formatDate(realtimeEndDate) }}
            <van-icon name="arrow-down" class="mx-1" />
          </div>
        </div>
        <div id="realtimeChart" class="w-full h-[200px]" />
      </div>
    </div>
    <div class="bg-white px-2 mt-2">
      <div class="flex items-center justify-between py-3">
        <div class="flex items-center">
          <van-image :src="square" class="w-5 h-5 mr-2" />
          <span class="font-bold">土壤流失量列表</span>
        </div>
        <span class="text-gray" @click="picker = 'soil'">
          {{ formatDate(soilDate, 'YYYY年') }}
          <van-icon name="arrow-down" class="mx-1" />
        </span>
      </div>
      <van-divider class="!my-0" />
      <div class="py-2">
        <table class="table">
          <thead>
            <tr>
              <th style="min-width: 2em">#</th>
              <th style="min-width: 8.5em">时间</th>
              <th style="min-width: 3em">雨量（mm）</th>
              <th style="min-width: 6em">土壤流失量（t）</th>
            </tr>
          </thead>
          <tbody>
            <template v-if="soilList.length > 0">
              <tr v-for="(item, i) in soilList" :key="`${item.year}-${item.quarter}`">
                <td>{{ i + 1 }}</td>
                <td>{{ item.year }}年{{ quarterMap[item.quarter] }}</td>
                <td>{{ formatNum(item.rainfall) }}</td>
                <td :style="{ color: item.lossAmountWarn ? 'red' : 'inherit' }">{{
                  formatNum(item.lossAmount)
                }}</td>
              </tr>
            </template>
            <tr v-else>
              <td colspan="4">
                <van-empty description="暂无数据" class="!py-0" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, getCurrentInstance, nextTick, onBeforeMount, onBeforeUnmount, ref } from 'vue';
import { useRoute } from 'vue-router';
import * as echarts from 'echarts';
import useAppStore from '@/store/app';
import square from '@/assets/icons/icon-svg-square-on-square.svg';
import { getList, getWarningRule, getWarningList } from './api';

// 季报开始时间
const minDate = new Date(2024, 6, 1);
const maxDate = new Date();
const quarterMap = {
  1: '第一季度',
  2: '第二季度',
  3: '第三季度',
  4: '第四季度',
};

const instance = getCurrentInstance();
const { $dayjs } = instance.appContext.config.globalProperties;
const route = useRoute();

const appStore = useAppStore();
const portalId = appStore.PORTAL?.id;
const userName = appStore.USER_INFO?.userName;

const currentDate = $dayjs();
const year = currentDate.year();
const month = currentDate.month();
const date = currentDate.date();
const startDate = currentDate.subtract(7, 'day');

const warningDate = ref([year, month + 1]);
const warningEndDate = computed(() =>
  $dayjs(warningDate.value).endOf('month').format('YYYY-MM-DD HH:mm:ss'));
const warningList = ref([]);

const realtimeStartDate = ref([
  startDate.year(),
  startDate.month() + 1,
  startDate.date(),
]);
const realtimeEndDate = ref([year, month + 1, date]);
const realtimeList = ref([]);
let realtimeChart = null;
// 雨量预警上下限
let realtimeLimits = [null, null];
const realtimeOption = computed(() => {
  const data = realtimeList.value.map(item => item.rainfall);
  const dataMax = Math.max(...data);
  const realtimeMax = Math.max(...realtimeLimits);
  return {
    color: ['#FFB24C'],
    tooltip: {
      trigger: 'axis',
        confine: true,
        backgroundColor: 'rgba(0,0,0,0.75)',
        textStyle: {
        color: '#fff'
      },
      axisPointer: {
        lineStyle: {
          color: 'rgba(0,0,0,0.65)',
            type: 'solid',
        }
      },
      valueFormatter: (value) => `${formatNum(value)}mm`,
    },
    grid: {
      top: 10,
        left: 16,
        right: 26,
        bottom: 10,
        containLabel: true,
    },
    xAxis: {
      axisTick: {
        lineStyle: {
          color: 'rgba(0,0,0,0.25)'
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(85,85,85,0.5)'
        }
      },
      axisLabel: {
        textStyle: {
          color: 'rgba(0,0,0,0.65)'
        },
        rotate: realtimeList.value.length > 2 ? 45 : 0,
      },
      data: realtimeList.value
        .map((item) => item.rainfallDate
          ? formatDate(item.rainfallDate, 'YYYY-MM-DD [08:00]')
          : '未知日期'
        ),
    },
    yAxis: {
      max: dataMax > realtimeMax ? undefined : realtimeMax,
      axisTick: {
        lineStyle: {
          color: '#e7e7e7',
        },
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
          showMinLine: false,
          lineStyle: {
          type: 'dashed',
            color: 'rgba(85,85,85,0.5)',
        },
      },
      axisLabel: {
        textStyle: {
          color: 'rgba(0,0,0,0.65)',
        },
      },
      type: 'value',
    },
    visualMap: {
      show: false,
      pieces: [
        {
          lt: realtimeLimits[0] ?? -Number.MAX_SAFE_INTEGER,
          color: '#FFB24C'
        },
        {
          gte: realtimeLimits[0] ?? -Number.MAX_SAFE_INTEGER,
          lt: realtimeLimits[1] ?? Number.MAX_SAFE_INTEGER,
          color: 'red'
        },
        {
          gte: realtimeLimits[1] ?? Number.MAX_SAFE_INTEGER,
          color: '#FFB24C',
        },
      ],
    },
    dataZoom: {
      type: 'inside',
      start: 0,
    },
    series: [
      {
        name: '雨量',
        data,
        markLine: {
          symbol: ['none', 'none'],
          lineStyle: {
            color: 'red',
            type: 'solid',
          },
          data: [
            realtimeLimits[0] !== null ? { name: '预警下限', yAxis: realtimeLimits[0] } : null,
            realtimeLimits[1] !== null ? { name: '预警上限', yAxis: realtimeLimits[1] } : null,
          ].filter((obj) => !!obj),
        },
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        showSymbol: false,
        emphasis: {
          focus: 'series',
        },
      },
    ],
  };
});

const soilDate = ref([year]);
const soilList = ref([]);

const picker = ref(null);

onBeforeMount(() => {
  getWarning();
  getRealtime();
  getSoil();
});

onBeforeUnmount(() => {
  realtimeChart?.dispose();
});

function getWarning() {
  getWarningList({
    portalId,
    pageSize: 999,
    createStartTime: formatDate(warningDate.value, 'YYYY-MM-[01 00:00:00]'),
    createEndTime: warningEndDate.value,
    recipientBy: userName,
  }).then(res => {
    warningList.value = Array.isArray(res?.records) ? res.records : [];
  });
}

async function getRealtime() {
  const res = await getWarningRule({
    portalId,
    observationField: 'rainfall',
  });
  realtimeLimits = [
    res?.monitorFloor ?? null,
    res?.monitorCeiling ?? null,
  ]

  getList({
    portalId,
    statisticsType: 'day',
    startDate: formatDate(realtimeStartDate.value),
    endDate: formatDate(realtimeEndDate.value),
  }).then(res => {
    realtimeList.value = Array.isArray(res) ? res : [];
    nextTick(() => {
      if (!realtimeChart) {
        realtimeChart = echarts.init(document.getElementById('realtimeChart'));
      }
      realtimeChart.setOption(realtimeOption.value);
    });
  });
}

function getSoil() {
  getList({
    portalId,
    statisticsType: 'quarter',
    warnFlag: true,
    year: soilDate.value[0],
  }).then(res => {
    soilList.value = Array.isArray(res) ? res : [];
  });
}

function formatDate(date, formatter = 'YYYY-MM-DD') {
  return date ? $dayjs(date).format(formatter) : '-';
}

function formatNum(value, digit = 2) {
  return isNaN(parseFloat(value)) ? value : value.toFixed(digit);
}

function pickerConfirm() {
  if (picker.value === 'warning') {
    getWarning();
  } else if (picker.value === 'soil') {
    getSoil();
  } else {
    getRealtime();
  }
  picker.value = null;
}
</script>

<style scoped lang="scss">
.view-height {
  height: calc(100vh - var(--van-nav-bar-height) - var(--sat));
  padding: 0 10px;
  overflow-x: hidden;
  overflow-y: scroll;
}

.table {
  width: 100%;
  border-collapse: collapse;
  text-align: center;

  th,
  td {
    padding: 8px 2px;
    border: 1px solid #EEEEEE;
  }

  thead > tr {
    background-color: #ECF5FF;
  }

  tbody > tr {
    &:nth-of-type(even) {
      background-color: #F9FCFF;
    }
  }
}
</style>
