<template>
  <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
    <van-list
      v-model:loading="loading"
      :finished="finished"
      :finished-text="list && list.length ? '没有更多了' : ''"
      :immediate-check="false"
      @load="onLoadList"
    >
      <template v-if="list && list.length">
        <template v-for="item in list || []" :key="item.id">
          <ListItem :item="item" />
        </template>
      </template>
      <template v-else>
        <div v-if="!loading" class="p-[10px]">
          <van-empty description="暂无数据" />
        </div>
      </template>
    </van-list>
  </van-pull-refresh>
</template>

<script>
import { getPage } from '../api';
import ListItem from './ListItem.vue';

export default {
  name: 'List',
  components: {
    ListItem,
  },
  props: {
    search: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  emits: [],
  setup(props, { attrs, slots, emit }) {},
  data() {
    return {
      loading: false,
      finished: false,
      list: [],
      refreshing: false,
      searchParams: {
        page: 1,
        size: 20,
      },
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.onLoadList();
  },
  methods: {
    onRefresh() {
      this.refreshing = false;
      this.finished = false;
      this.searchParams.page = 1;
      this.onLoadList();
    },
    async onLoadList() {
      try {
        this.loading = true;
        this.finished = false;
        const params = {
          ...this.searchParams,
          ...this.search,
        };
        if (params.page === 1) {
          this.$showToast({
            type: 'loading',
            loadingType: 'spinner',
            message: '加载中...',
            forbidClick: true,
          });
        }
        const data = await getPage(params);
        const list = this.searchParams.page <= 1 ? [] : this.list || []; // 第一页的时候设为空
        if (this.list.length >= data.total) {
          this.finished = true;
        }
        this.list = [...list, ...data.records];
        this.searchParams.page++;
      } catch (e) {
        this.finished = true;
      } finally {
        setTimeout(() => {
          this.loading = false;
          this.$closeToast();
        }, 100);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page {
  height: 100%;
}
</style>
