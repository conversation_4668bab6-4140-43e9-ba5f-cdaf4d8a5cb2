<template>
  <div class="list-item" @click.stop.prevent="toDetail">
    <div class="body">
      <div class="item-info">
        <span class="key">检查编号</span>
        <span class="value">{{ item.inspectNo || '-' }}</span>
      </div>
      <div class="item-info">
        <span class="key">检查类型</span>
        <span class="value">{{ $formatLabel(item.inspectType, $DICT_CODE.earthwork_inspection_type) }}</span>
      </div>
      <div class="item-info">
        <span class="key">巡检人</span>
        <span class="value">{{ item.createFullName || '-' }}</span>
      </div>
      <div class="item-info" style="line-height: 1.125em">
        <span class="key">巡检人所在部门</span>
        <span class="value">{{ item.createOrg || '-' }}</span>
      </div>
      <div class="item-info" >
        <span class="key">所属标段</span>
        <span class="value">{{ $formatLabel(item.sectionId, $DICT_CODE.earthwork_inspection_section) }}</span>
      </div>
      <div class="item-info">
        <span class="key">弃渣场名称</span>
        <span class="value text-truncate">{{ item.earthworkName || '-' }}</span>
      </div>
      <div class="item-info">
        <span class="key">检查日期</span>
        <span class="value">{{ $dayjs(item.inspectTime).format('YYYY-MM-DD') }}</span>
      </div>
      <div class="right">
        <van-tag class="tag" :color="tagColor" plain size="medium">{{ taskStateText(item) }}</van-tag>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ListItem',
  components: {},
  props: {
    item: {
      type: Object,
      default: () => {
        return {};
      },
    }
  },
  emits: [],
  setup(props, { attrs, slots, emit }) {
  },
  data() {
    return {};
  },
  watch: {},
  created() {
  },
  computed: {
    tagColor() {
      const state = String(this.item?.inspectResult);
      switch (state) {
        case '0': {
          return '#07c160';
        }
        case '1':
        default: {
          return '#ff4d4f';
        }
      }
    },
    // 当前用户
    user() {
      return this.$store.USER_INFO;
    },
  },
  methods: {
    taskStateText(item) {
      return this.$formatLabel(item.inspectResult, this.$DICT_CODE.earthwork_inspection_result)
    },
    toDetail() {
      this.$router.push({
        path: '/WasteDumpInspectionDetail',
        query: {
          id: this.item.id,
          type: this.user.userName === this.item.createBy ? 'update' : 'detail',
          title: this.user.userName === this.item.createBy ? '编辑弃渣场巡检' : '弃渣场巡检详情'
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.list-item {
  background-color: #fff;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 10px;

  .body {
    padding: 5px;
    padding-top: 10px;
    position: relative;

    .item-info {
      display: flex;
      font-size: 14px;
      color: #9a9a9a;
      line-height: 1;
      padding: 5px 0;

      > .key {
        min-width: 8em;
      }

      > .value {
        flex: 1;
        color: #333;
      }
    }

    .right {
      position: absolute;
      top: 5px;
      right: 4px;

      .tag {
        padding: 6px 12px;
        text-align: center;
      }
    }
  }
}
</style>
