<template>
  <Navbar back :title="navbarTitle"/>
  <template v-if="loading">
  </template>
  <template v-else-if="error">
    <van-empty image="error" :description="error" />
  </template>
  <template v-else>
    <div class="view-height" :class="{ 'btn-bom': isAdd || isUpdate }">
      <van-form
        ref="formRef"
        :readonly="isDetail"
        label-width="8em"
        input-align="right"
        error-message-align="right"
        class="form-bottom"
      >
        <van-cell-group :border="false">
          <van-field
            v-model="form.inspectNo"
            name="inspectNo"
            label="检查编号"
            readonly
            placeholder="自动生成"
          />
          <FormItemPicker
            v-model:value="form.inspectType"
            :readonly="isDetail"
            :dict-name="$DICT_CODE.earthwork_inspection_type"
            :rules="[{ required: true, message: '请选择检查类型' }]"
            label="检查类型"
            name="inspectionUnit"
            input-align="right"
            title="检查类型"
            required
          />
          <van-field
            v-model="form.createFullName"
            name="createFullName"
            label="巡检人"
            placeholder="自动生成"
            readonly
          />
          <van-field
            v-model="form.createOrg"
            name="createOrg"
            label="巡检人所在部门"
            placeholder="自动生成"
            type="textarea"
            autosize
            readonly
          />
          <FormItemPicker
            v-model:value="form.sectionId"
            :readonly="isDetail"
            :dict-name="$DICT_CODE.earthwork_inspection_section"
            :rules="[{ required: true, message: '请选择所属标段' }]"
            label="所属标段"
            name="sectionId"
            input-align="right"
            title="所属标段"
            required
            @change="onSectionChange"
          />
          <FormItemPicker
            v-model:value="form.earthworkName"
            :readonly="isDetail"
            :columns="earthworkNameList"
            :rules="[{ required: true, message: '请选择弃渣场名称' }]"
            label="弃渣场名称"
            name="earthworkName"
            input-align="right"
            title="弃渣场名称"
            required
          />
          <FormItemDate
            v-model:value="form.inspectTime"
            :readonly="isDetail"
            :rules="[{ required: true, message: '请选择检查日期' }]"
            label="检查日期"
            placeholder="请选择"
            submitTimeFormat="00:00:00"
            required
          />
          <van-field
            :rules="[{ required: true, message: '请选择检查结果' }]"
            name="inspectResult"
            label="检查结果"
            placeholder="检查结果"
            required
          >
            <template #input>
              <span v-if="isDetail">{{ $formatLabel(form.inspectResult, $DICT_CODE.earthwork_inspection_result) }}</span>
              <van-radio-group
                v-else
                v-model="form.inspectResult"
                direction="horizontal"
              >
                <van-radio
                  v-for="item in ENUM_DICT[DICT_CODE.earthwork_inspection_result]"
                  :key="item.code"
                  :name="item.code"
                >{{ item['zh-CN'] }}</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            label="问题上报"
            name="problemContent"
            v-model="form.problemContent"
            :readonly="isDetail"
            :required="hasProblem"
            :rules="[{ required: hasProblem, message: '请上报问题' }]"
            :placeholder="isDetail ? '' : '请输入'"
            :show-word-limit="!isDetail"
            rows="1"
            autosize
            type="textarea"
            label-align="top"
            maxlength="200"
            input-align="left"
          />
          <van-field
            :rules="[{ required: true, message: '请上传照片' }]"
            label="照片上传"
            label-align="top"
            input-align="left"
            required
          >
            <template #label>
              <span>照片上传</span>
              <span class="ml-2 text-gray text-[13px]">支持扩展名：JPG,PNG；5张上限；</span>
            </template>
            <template #input>
              <UploadFiles
                ref="uploadRef"
                v-model:g9s="g9s"
                :readonly="isDetail"
                :multiple="true"
                :max-count="5"
              />
            </template>
          </van-field>
          <van-field
            label="原因分析"
            name="reasonContent"
            v-model="form.reasonContent"
            :readonly="isDetail"
            :required="hasProblem"
            :rules="[{ required: hasProblem, message: '请输入原因分析' }]"
            :placeholder="isDetail ? '' : '请输入'"
            :show-word-limit="!isDetail"
            rows="1"
            autosize
            type="textarea"
            label-align="top"
            maxlength="200"
            input-align="left"
          />
        </van-cell-group>
      </van-form>
      <div class="btn-group" v-if="isAdd || isUpdate">
        <van-button round type="danger" plain @click.stop.prevent="cancel">取消</van-button>
        <van-button round type="primary" plain @click.stop.prevent="save">保存</van-button>
      </div>
    </div>
  </template>
</template>
<script setup>
import { onBeforeMount, computed, getCurrentInstance, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import useAppStore from '@/store/app';
import { getDetail, add, update, getEarthworkList } from './api.js';
import FormItemPicker from '@/components/FormItem/FormItemPicker.vue';
import FormItemDate from '@/components/FormItem/FormItemDate.vue';
import UploadFiles from '@/components/UploadFiles/index.vue';
import { DICT_CODE } from '@/utils/rafire.js';

const { query } = useRoute();
const router = useRouter();

const instance = getCurrentInstance();
const { $showToast, $closeToast } =  instance.appContext.config.globalProperties;

const { ENUM_DICT, PORTAL, USER_INFO } = useAppStore();
const portalId = PORTAL?.id;

const loading = ref(false);
const error = ref('');
const earthworkNameList = ref([])
const form = ref({
  createBy: USER_INFO?.userName,
  createFullName: USER_INFO?.userFullname,
  createOrg: USER_INFO?.orgList?.map((item) => item.name).join(';'),
  portalId,
  inspectResult: '0',
  annexes: [],
})
const formRef = ref();
const g9s = ref('');
const uploadRef = ref();

const navbarTitle = computed(() => query.title || '弃渣场检查详情');
const isAdd = computed(() => query.type === 'add');
const isUpdate = computed(() => query.type === 'update');
const isDetail = computed(() => query.type === 'detail');
const hasProblem = computed(() => form.value.inspectResult !== '0');

onBeforeMount(async () => {
  await getEarthworkList({ portalId }).then((res) => {
    earthworkNameList.value = Array.isArray(res)
      ? res.map(item => ({ text: item.slagYardName, value: item.slagYardName }))
      : []
  })
  getData()
});

function getData() {
  if (isAdd.value) return;
  if (!query.id) {
    error.value = '出错了!';
    return;
  }
  loading.value = true;
  $showToast({
    type: 'loading',
    loadingType: 'spinner',
    forbidClick: true,
    message: '加载中...',
    duration: 0,
  });
  getDetail(query.id)
    .then(res => {
      form.value = {
        ...res,
        annexes: res.annexes ?? [],
      }
      g9s.value = res.annexes?.[0]?.groupToken ?? '';
      error.value = '';
    })
    .catch((e) => {
      error.value = e.message;
    })
    .finally(() => {
      $closeToast();
      loading.value = false;
    })
}

function onSectionChange(val) {
  form.value.sectionName = val?.['zh-CN'];
}

async function save() {
  try {
    await formRef.value.validate();

    const { fileList, deleteList, update: updateFileList } = uploadRef.value;
    const annexes = fileList.filter((file) => !deleteList.includes(file.fileToken));
    if (!annexes.length) {
      this.$showToast({ message: '请上传照片' });
      return false;
    }

    $showToast({
      type: 'loading',
      loadingType: 'spinner',
      message: '加载中...',
      forbidClick: true,
    });

    // 手动调用删除照片
    await updateFileList()

    const data = {
      ...form.value,
      annexes: annexes.map((item) => ({
        fileToken: item.fileToken,
        groupToken: g9s.value,
      })),
    }
    if (isAdd.value) {
      await add(data)
        .then(() => {
          cancel();
        })
        .finally(() => {
          $closeToast();
        })

    } else if (isUpdate.value) {
      await update(data)
        .then(() => {
          cancel();
        })
        .finally(() => {
          $closeToast();
        })
    }
  } catch (e) {
    console.log(e);
  }
}

function cancel() {
  router.go(-1)
}
</script>

<style scoped lang="scss">
.view-height {
  height: calc(100vh - var(--van-nav-bar-height) - var(--sat));
  padding: 0 10px;
  overflow-x: hidden;
  overflow-y: scroll;
  &.btn-bom {
    padding: 0 10px 50px 10px !important;
  }
}

.btn-group {
  width: calc(100% - 20px);
  display: flex;
  justify-content: space-between;
  gap: 0 15px;
  position: absolute;
  bottom: 10px;
  left: 10px;
  > button {
    flex: 1;
  }
}
</style>
