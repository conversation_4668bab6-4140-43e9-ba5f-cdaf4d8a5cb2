import request from '@/utils/request';
const prefix = import.meta.env.VITE_APP_WATER_API_SERVICENAME

export function getPage(data) {
  return request({
    url: `${prefix}/water-inspect/earthwork/page`,
    method: 'post',
    data,
  });
}

export function getDetail(id) {
  return request({
    url: `${prefix}/water-inspect/earthwork/${id}`,
    method: 'get',
  });
}

export function add(data) {
  return request({
    url: `${prefix}/water-inspect/earthwork/add`,
    method: 'post',
    data,
  });
}

export function update(data) {
  return request({
    url: `${prefix}/water-inspect/earthwork/edit`,
    method: 'put',
    data,
  });
}

/**
 * 获取季报弃渣场列表
 * @return {*}
 */
export function getEarthworkList(params) {
  return request({
    url: `${prefix}/course-monitor/quarterly/earthwork-list`,
    method: 'get',
    params,
  });
}
