<template>
  <Navbar back>
    <template #right>
      <van-icon :name="Sift" size="2em" @click.stop.prevent="showTop = !showTop"/>
    </template>
  </Navbar>
  <van-popup
    v-model:show="showTop"
    position="top"
  >
    <FormItemPicker
      v-model:value="search.earthworkName"
      :columns="earthworkNameList"
      placeholder="选择弃渣场"
    />
    <FormItemPicker
      v-model:value="search.inspectType"
      :dict-name="$DICT_CODE.earthwork_inspection_type"
      placeholder="选择检查类型"
    />
    <FormItemPicker
      v-model:value="search.inspectResult"
      :dict-name="$DICT_CODE.earthwork_inspection_result"
      placeholder="选择检查结果"
    />
    <div class="btn-group">
      <van-button round type="primary" plain @click.stop.prevent="handleQuery">查询</van-button>
      <van-button round plain @click.stop.prevent="handleReset">重置</van-button>
    </div>
  </van-popup>
  <div class="view-height" :class="{ 'no-tabbar': envFeishu }">
    <List ref="listRef" :search="search" />
  </div>
  <van-button type="primary" size="normal" style="width: 100%" @click="handleAdd">新增巡检</van-button>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router';
import useAppStore from '@/store/app';
import Sift from '@/assets/sift.svg'
import FormItemPicker from '@/components/FormItem/FormItemPicker.vue';
import List from './Components/List.vue';
import { getEarthworkList } from '@/views/WasteDumpInspection/api.js';

const envFeishu = !!window.ENV_FEISHU

const router = useRouter()
const appStore = useAppStore()
const portalId = appStore.PORTAL?.id

const showTop = ref(false)
const earthworkNameList = ref([])

const search = ref({
  portalId,
})
const listRef = ref()

onMounted(() => {
  getEarthworkList({ portalId }).then((res) => {
    earthworkNameList.value = Array.isArray(res)
      ? res.map(item => ({ text: item.slagYardName, value: item.slagYardName }))
      : []
  })
})

function handleQuery() {
  showTop.value = false
  nextTick(() => {
    listRef.value.onRefresh(search.value);
  })
}

function handleReset() {
  search.value = {
    portalId,
  }
  handleQuery()
}

function handleAdd() {
  router.push('/WasteDumpInspectionDetail?type=add&title=新增弃渣场巡检')
}
</script>

<style scoped lang="scss">
.view-height {
  height: calc(
    100vh - var(--van-nav-bar-height) - var(--sat) - var(--van-tabbar-height) - var(--sab)
  );
  padding-top: 10px;
  overflow-x: hidden;
  overflow-y: scroll;

  &.no-tabbar {
    height: calc(100vh - var(--van-nav-bar-height) - var(--sat) - var(--sab) - 45px);
  }
}

.btn-group {
  width: 100%;
  padding: 0 15px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  gap: 0 15px;
  margin: 10px 0;
  > button {
    flex: 1;
  }
}
</style>
